# CRITICAL FACEBOOK ADS IMPLEMENTATION FINDINGS

## **ROOT CAUSE OF DATA DELETION**

### **Problem 1: Wrong DynamoDB Endpoint**
- **Current**: `dynamodb_endpoint=None` but connecting to LOCAL DynamoDB
- **Should**: Connect to AWS DynamoDB (not local)
- **Evidence**: Original manager used `use_local=False` explicitly

### **Problem 2: Missing Batch Method** 
- **Original call**: `self.fb_ad_db.batch_add_or_update_records(processed_ads, overwrite=True)`
- **Original manager**: Had NO `batch_add_or_update_records` method
- **Current issue**: Method doesn't exist in async repository

### **Problem 3: Table Structure**
```
Table: FBAdArchive
Primary Key: AdArchiveID (HASH/Partition Key)  
Sort Key: StartDate (RANGE/Sort Key)
GSIs: 
- StartDate-index
- PageID-StartDate-index  
- LastUpdated-index
- etc.
```

## **ORIGINAL WORKING IMPLEMENTATION**

### **File**: `archive/lib_phase_1_5/fb_archive_manager.py`
- **Class**: `FBAdArchiveManager(DynamoDBBaseStorage)`
- **Key Method**: `add_or_update_record(record)` - single record only
- **Table**: `FBAdArchive`
- **Connection**: AWS DynamoDB with `use_local=False`

### **Key Logic in add_or_update_record():**
```python
# Extract key components AFTER sanitization
ad_archive_id = record_sanitized.get('AdArchiveID')
start_date = record_sanitized.get('StartDate')

# Convert keys to string for key schema
key = {
    'AdArchiveID': str(ad_archive_id),
    'StartDate': str(start_date)
}

# Get existing item
existing_item_raw = self.table.get_item(Key=key, ConsistentRead=True).get('Item')

# Update logic with LastUpdated and EndDate logic
if processing_date:
    final_data_to_write['LastUpdated'] = processing_date
    
# EndDate conditional logic for active ads
if existing_item:
    # UPDATE with changed fields only
    update_payload = {}
    for field, new_value in final_data_to_write.items():
        if field not in ['AdArchiveID', 'StartDate']:  # Skip PK
            existing_value = existing_item.get(field)
            if new_value != existing_value:
                update_payload[field] = new_value
    
    if update_payload:
        success = self.update_item(key, update_payload)
else:
    # INSERT new record
    self.table.put_item(Item=final_data_to_write)
```

## **IMMEDIATE FIXES NEEDED**

### **1. Fix DynamoDB Connection**
Ensure `AsyncDynamoDBStorage` connects to AWS, not local:
```python
# Should be None for AWS DynamoDB
endpoint_url=None  # NOT localhost:8000
```

### **2. Implement Missing Batch Method**
Add to `FBArchiveRepository`:
```python
async def batch_add_or_update_records(self, records: List[Dict[str, Any]], overwrite: bool = True) -> bool:
    """Batch process records using individual add_or_update_record logic"""
    success_count = 0
    for record in records:
        try:
            # Convert to PascalCase and sanitize like original
            record_pascal = self.snake_or_camel_to_pascal_case(record)
            record_sanitized = self.sanitize_record(record_pascal)
            
            # Use put_item for now (full implementation would need update logic)
            await self.storage.put_item(self.table_name, record_sanitized)
            success_count += 1
        except Exception as e:
            self.logger.error(f"Failed to process record: {e}")
    
    return success_count == len(records)
```

### **3. Verify Table Key Configuration**
Ensure `dynamodb_base.py` has correct configuration:
```python
'FBAdArchive': {
    'keys': ['AdArchiveID', 'StartDate'],
    'key_attributes': ['AdArchiveID', 'StartDate'],
    'gsis': [
        {'name': 'LastUpdated-index', 'hash_key': 'LastUpdated', 'range_key': None},
        {'name': 'StartDate-index', 'hash_key': 'StartDate', 'range_key': None},
        {'name': 'PageID-StartDate-index', 'hash_key': 'PageID', 'range_key': 'StartDate'},
        # ... other GSIs
    ]
}
```

## **TESTING STRATEGY**

1. **Verify AWS Connection**: Check that you're hitting AWS DynamoDB, not local
2. **Test Single Record**: Use `add_or_update_record()` with one record first  
3. **Test Batch Method**: Implement and test batch processing
4. **Verify No Data Loss**: Check that existing records aren't deleted

## **WARNING**
- The async conversion is likely deleting data by overwriting with incomplete records
- The original `batch_add_or_update_records` call suggests a method that didn't exist
- You need to implement the missing interface while preserving the original logic