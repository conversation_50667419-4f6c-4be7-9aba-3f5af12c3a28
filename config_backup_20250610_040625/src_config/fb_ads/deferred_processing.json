{"deferred_processing": {"enabled": true, "description": "Enable deferred image text extraction for faster scraping", "image_queue_dir": "./data/image_queue", "cleanup_days": 7, "auto_process": false, "auto_process_time": "02:00"}, "vision_models": {"default_model": "llama32-vision-ocr", "ollama_base_url": "http://localhost:11434", "models": {"llama32-vision-ocr": {"description": "Optimized model specifically for OCR tasks", "recommended_for": "Production OCR workloads, best balance of speed and accuracy", "timeout": 3600, "temperature": 0.1, "num_gpu_layers": -1, "min_vram_gb": 8, "throughput_estimate": "15-40 seconds per image", "realistic_performance": "20-30 seconds average on M4 Mac"}, "llama3.2-vision:11b": {"description": "Full model - Best accuracy for OCR and text extraction", "recommended_for": "High accuracy requirements, complex layouts", "timeout": 3600, "temperature": 0.1, "num_gpu_layers": -1, "min_vram_gb": 16, "throughput_estimate": "30-90 seconds per image", "realistic_performance": "45-60 seconds average"}, "llama3.2-vision:11b-instruct-q4_K_M": {"description": "Quantized model - Faster than full model with minimal accuracy loss", "recommended_for": "Production use when llama32-vision-ocr not available", "timeout": 3600, "temperature": 0.1, "num_gpu_layers": -1, "min_vram_gb": 8, "throughput_estimate": "20-60 seconds per image", "realistic_performance": "30-40 seconds average on M4 Mac"}, "llava:7b": {"description": "Legacy LLaVA model - Smaller but less accurate", "recommended_for": "Low-resource environments, simple text", "timeout": 1800, "temperature": 0.3, "num_gpu_layers": -1, "min_vram_gb": 6, "throughput_estimate": "10-30 seconds per image", "realistic_performance": "15-20 seconds average"}, "llava:7b-v1.6-mistral-q4_0": {"description": "Efficient LLaVA variant - Fastest but least accurate", "recommended_for": "High-volume processing where speed is critical", "timeout": 1800, "temperature": 0.3, "num_gpu_layers": -1, "min_vram_gb": 4, "throughput_estimate": "5-20 seconds per image", "realistic_performance": "8-12 seconds average"}}}, "processing_performance": {"default_concurrency": 1, "default_batch_size": 10, "db_write_concurrency": 16, "retry_max_attempts": 3, "retry_delay_seconds": 60, "gpu_profiles": {"m4_mac_128gb": {"recommended_model": "llama32-vision-ocr", "ollama_num_parallel": 4, "client_concurrency": 1, "batch_size": 10, "expected_throughput": "20-30 seconds per image", "daily_capacity": "2000-3000 images", "notes": "Unified memory architecture, excellent for large models"}, "8gb_vram": {"recommended_model": "llama3.2-vision:11b-instruct-q4_K_M", "ollama_num_parallel": 1, "client_concurrency": 1, "batch_size": 10, "expected_throughput": "30-60 seconds per image", "daily_capacity": "1000-2000 images"}, "16gb_vram": {"recommended_model": "llama32-vision-ocr", "ollama_num_parallel": 2, "client_concurrency": 1, "batch_size": 15, "expected_throughput": "20-40 seconds per image", "daily_capacity": "2000-3000 images"}, "24gb_vram": {"recommended_model": "llama3.2-vision:11b", "ollama_num_parallel": 2, "client_concurrency": 1, "batch_size": 20, "expected_throughput": "30-45 seconds per image", "daily_capacity": "1500-2500 images"}, "48gb_vram": {"recommended_model": "llama3.2-vision:11b", "ollama_num_parallel": 3, "client_concurrency": 1, "batch_size": 25, "expected_throughput": "25-35 seconds per image", "daily_capacity": "2500-3500 images"}}}, "queue_management": {"status_check_interval": 300, "failed_items_alert_threshold": 100, "pending_items_alert_threshold": 10000, "processing_timeout_minutes": 30, "export_format": "json", "export_compression": true}, "summary_updates": {"enabled": true, "update_criteria": {"skip_classifications": ["NA", "SKIPPED", "ERROR", "FAILED"], "min_confidence_threshold": 0.7, "max_confidence_threshold": 0.95, "skip_if_contains": ["[Image Text]", "image text:", "Image content:"]}, "batch_size": 50, "regeneration_model": "gpt-4", "fallback_model": "deepseek"}, "ollama_server": {"startup_commands": ["export OLLAMA_NUM_PARALLEL=4", "export OLLAMA_KEEP_ALIVE=-1", "export OLLAMA_GPU_LAYERS=-1"], "health_check_endpoint": "http://localhost:11434/api/tags", "model_load_endpoint": "http://localhost:11434/api/generate", "required_models": ["llama32-vision-ocr", "llama3.2-vision:11b-instruct-q4_K_M"]}, "optimization_tips": {"performance": {"use_optimized_model": "Run 'ollama create llama32-vision-ocr -f Modelfile.llama32-vision-optimized'", "reduce_context": "Lower num_ctx to 2048 for OCR tasks", "limit_output": "Set num_predict to 512 or lower", "batch_processing": "Process during off-hours for best performance", "monitor_memory": "Watch for memory pressure with Activity Monitor"}, "quality": {"preprocess_images": "Resize large images to max 1024x1024 before processing", "enhance_contrast": "Apply contrast enhancement for low-quality images", "temperature": "Keep at 0.1 for consistent OCR results", "prompt_engineering": "Use focused OCR prompts, not general descriptions"}, "reliability": {"timeouts": "Set generous timeouts (3600s+) for vision models", "retries": "Enable automatic retries with exponential backoff", "monitoring": "Track processing times to identify bottlenecks", "fallback": "Have faster models ready as fallback options"}}, "monitoring": {"metrics_enabled": true, "metrics_interval_seconds": 60, "tracked_metrics": ["queue_size", "processing_rate", "success_rate", "average_processing_time", "gpu_utilization", "memory_usage"], "alert_channels": ["console", "log"]}, "error_handling": {"s3_access_denied": {"action": "mark_skipped", "retry": false, "log_level": "warning"}, "vision_model_timeout": {"action": "retry", "retry": true, "max_retries": 2, "log_level": "error"}, "vision_model_error": {"action": "mark_failed", "retry": true, "max_retries": 3, "log_level": "error"}, "out_of_memory": {"action": "reduce_concurrency", "retry": true, "concurrency_reduction": 0.5, "log_level": "critical"}}}