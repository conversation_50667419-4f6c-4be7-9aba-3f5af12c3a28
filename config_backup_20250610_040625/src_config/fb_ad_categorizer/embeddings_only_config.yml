# Embeddings-Only Classification Configuration  
# Usage: python src/scripts/hybrid_classifier.py --config src/config/fb_ad_categorizer/embeddings_only_config.yml

# Data Source
input:
  source: dynamodb
  dynamodb_table_name: "FBAdArchive"
  local_dynamodb: true
  dynamodb_endpoint_url: "http://localhost:8000"
  start_date: null
  limit: null
  csv_file: null

# Campaign Configuration
campaigns:
  config_file: "src/config/fb_ad_categorizer/campaign_config.json"
  skip_terms_file: "src/config/fb_ad_categorizer/campaign_skip_terms.json"

# Processing Options
processing:
  batch_size: 256
  stages:
    rules_only: false
    embeddings_only: true             # Run rules + embeddings only
    full_pipeline: false
  text_fields:
    - "Title"
    - "Body" 
    - "Summary"
    - "LinkDescription"
    - "PageName"
  deduplication_fields:
    - "Title"
    - "Body"
    - "Summary"

# AI Models
models:
  embedder:
    model_name: "all-roberta-large-v1"
    cache_file: "embedding_roberta.pkl"
  
  ner:
    model_name: "en_core_web_trf"
    cache_file: "ner_cache_new.pkl"
  
  llm:
    enabled: false                    # Disable LLM for embeddings-only
    backend: "ollama"
    model: "deepseek-r1:8b-0528-qwen3-q8_0"
    succinct_names: true
    enhanced_llm: false
    timeout: 120
    max_retries: 3
    retry_delay: 5
    cache_file: "llm_response_cache.pkl"

# Output
output:
  csv_file: "classified_ads_embeddings_only.csv"
  deduplicated_csv_file: "classified_ads_embeddings_dedup.csv"
  
# Rule Improvement
rules:
  improve_rules: true
  output_suggestions: "rule_suggestions_embeddings_only.json"

# Logging
logging:
  level: "INFO"

# AWS Settings
aws:
  region: "us-west-2"