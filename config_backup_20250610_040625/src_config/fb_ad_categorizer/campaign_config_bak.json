[{"Category": "Sexual Abuse", "Company": null, "Product": null, "LitigationName": "Juvenile Detention Abuse", "triggers": ["juvenile detention", "youth facility", "juvenile detention center", "juvenile detention centers", "detention center", "detention centers", "detained youth", "youth abused", "detained youth facility", "detained youth facilities"], "include": ["sexual assault", "sexual abuse", "abuse", "inappropriate touching"]}, {"Category": "Products Liability", "Company": "Various", "LitigationName": "Ultra-processed foods Type-2 Diabetes Products Liability", "MdlNum": "9002", "triggers": ["type 2 diabetes", "type-2 diabetes", "type 2 diabetes", "ultra processed foods", "ultra-processed foods", "ultra processed food", "ultra-processed food", "ultra processed food"], "include": ["type 2 diabetes", "type-2 diabetes", "type 2 diabetes", "ultra processed foods", "ultra-processed foods"], "exclude": ["glp-1", "stomach paralysis", "intestinal blockage", "eye stroke", "vomitting", "water contamination", "military", "firefighter", "aqueous-film-forming-foam", "afff", "(afff)", "ozempic", "wegovy"]}, {"Category": "Products Liability", "Company": "Monsanto", "Product": "RoundUp Herbicide", "Injuries": ["Cancer"], "Litigation_Type": "Products Liability", "LitigationName": "RoundUp Products Liability", "MdlNum": "2741", "triggers": ["roundup", "glyphosate", "bayer roundup", "roundup weed killer", "exposed to roundup", "bayer/monsanto", "non-hodgkin", "non-hodgkins", "lymphoma", "non hodgkins"], "exclude": ["oxb<PERSON>ta", "afff", "firefighting", "fire fighting", "firefighting", "gard<PERSON>l", "suboxone", "merick", "indivior", "biozorb", "pfizer-biontech", "igora royal", "false labeling", "playboy", "cigna", "columbus bd", "jessup elite spice", "north haven", "river rouge", "baxter healthcare", "virginia beach", "sterilization", "river rouge", "cancer contamination", "zantac", "johnson & johnson", "firefighting foam", "firefighter", "coventry", "boston scientific", "talcum powder", "medtronic", "xomed", "camp lejeune", "girl scout", "girl scouts", "groveland", "sterigenics", "cosmed", "ethylene oxide", "toxic air", "lakewood", "hair dye", "pfas", "<PERSON><PERSON><PERSON>'s disease", "parkinsons", "ethicon", "hair-dye"]}, {"Category": "Products Liability", "Company": "<PERSON><PERSON>", "Product": "Paragard IUD", "Injuries": ["Surgery", "Infertility"], "Litigation_Type": "Products Liability", "LitigationName": "Paragard IUD Products Liability", "MdlNum": "2974", "triggers": ["para<PERSON>", "<PERSON><PERSON> ud", "<PERSON><PERSON> iud", "IUD"]}, {"Category": "Products Liability", "Company": "Syngenta", "Product": "Paraquat Herbicide", "Injuries": ["Parkinson's Disease"], "Litigation_Type": "Products Liability", "LitigationName": "Paraquat Products Liability", "MdlNum": "3004", "triggers": ["paraquat", "herbicide", "<PERSON><PERSON><PERSON>'s disease", "\"paraquat\""], "exclude": ["pregnant", "asd", "adhd", "add/adhd", "birth defect", "dcpa", "dact<PERSON>"]}, {"Category": "Products Liability", "Company": "Pfizer", "Product": "Depo-Provera", "Injuries": ["Health Risks"], "Litigation_Type": "Products Liability", "LitigationName": "Depo-Provera Products Liability", "triggers": ["depo-provera", "depoprovera", "birth control", "injectable birth control", "brain tumor lawsuit", "meningioma", "depo shot", "depo-shot", "depo provera", "brain tumor", "brain tumors", "brain-tumor"], "exclude": ["filshie®", "oxb<PERSON>ta", "afff", "hpv", "gardasil vaccine", "firefighting", "fire fighting", "firefighting", "gardisal", "suboxone", "merick", "indivior", "biozorb", "pfizer-biontech", "pfas", "pfas chemicals", "filshie", "filshie clip", "biozorb marker", "biozorb® implant", "biozorb® implants", "biozorb implant", "biozorb implants", "merck", "filshie clips"]}, {"Category": "Consumer Fraud", "Company": "<PERSON><PERSON><PERSON>", "Product": "In-App Game Purchases", "Injuries": ["Economic Loss", "Consumer Fraud"], "Litigation_Type": "Consumer Fraud", "LitigationName": "In-App Game Purchases Consumer Fraud", "triggers": ["in-game purchases", "in game purchases", "in-game purchase", "in game purchase", "dark patterns", "dark pattern", "in-game purchases", "in game purchases", "in-app purchase", "in app purchase", "in-app-purchase", "in-app-purchases", "in-app-purchase", "in-app-purchases", "in-app-purchase", "dark patterns", "dark pattern", "dark-patterns", "dark-pattern"], "exclude": ["taking over your child’s life", "your child's well being", "your child's well-being", "child's gaming habit", "video game addiction", "gaming addiction", "video games addiction", "video gaming addiction"]}, {"Category": "Environmental", "Company": "Chiquita Canyon/Waste Connections", "Product": "Landfill Operations", "Injuries": ["Chemical Exposure", "Toxic Emissions", "Property Damage", "Health Issues", "Air Quality Impact"], "Litigation_Type": "Environmental", "LitigationName": "Chiquita Canyon Landfill Environmental Contamination", "description": "Cases involving Chiquita Canyon Landfill, Sunshine Canyon Landfill, toxic exposure, chemical fires, gas leaks, or environmental contamination. If the case mentions specific locations like Val Verde, Castaic, Santa Clarita, or other nearby communities but involves the Chiquita Canyon or Sunshine Canyon landfills, classify under this campaign.", "triggers": ["chiquita canyon", "sunshine canyon", "landfill", "toxic exposure", "chemical fires", "val verde", "castaic", "santa clarita"], "include": ["landfil", "canyon"], "exclude": ["banana", "bananas"]}, {"Category": "Products Liability", "Company": "Pfizer", "Product": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Injuries": ["Sickle Cell Disease Complications"], "Litigation_Type": "Products Liability", "LitigationName": "Oxbryta Products Liability", "triggers": ["oxb<PERSON>ta", "sickle cell", "global blood therapeutics", "voxelotor"]}, {"Category": "Products Liability", "Company": "Abbott Laboratories", "Product": "Simila<PERSON>", "Injuries": ["Necrotizing Enterocolitis (NEC)"], "Litigation_Type": "Products Liability", "LitigationName": "Abbott Laboratories Preterm Infant Nutrition Products Liability", "MdlNum": "3026", "triggers": ["necrotizing enterocolitis", "fed enfamil", "necrotizing", "nec", "cow's milk", "cow's milk-based", "similac", "enfamil", "nec baby formula", "enfamil/similac"]}, {"Category": "Products Liability", "Company": "Johnson & Johnson", "Product": "<PERSON><PERSON><PERSON>", "Injuries": ["Cancer"], "Litigation_Type": "Products Liability", "LitigationName": "Johnson & Johnson Talcum Powder Litigation", "MdlNum": "2738", "triggers": ["talcum powder", "baby powder", "johnson & johnson", "<PERSON><PERSON><PERSON>", "johnson", "fallopian", "peritoneal", "ovarian cancer"], "include": ["talcum", "talcum powder", "baby powder", "talc"]}, {"Category": "Products Liability", "Company": "Bard/AngioDynamics", "Product": "PowerPort Implants", "Injuries": ["Complications"], "Litigation_Type": "Products Liability", "LitigationName": "Bard/AngioDynamics PowerPort Products Liability", "MdlNum": "3081/3125", "triggers": ["port catheter", "bard powerport lawsuit", "angio dynamics", "angiodynamics", "catheter", "powerport", "angiodynamics", "angiodynamics'", "smartport", "xcela", "ventralex", "smart-port", "chemo port", "chemo ports", "chemo port", "port-a-cath", "port-a-caths", "Bard® PowerPorts®", "powerports®", "powerports", "bard powerport", "bard powerport", "smart port ct", "BardPort®", "bard access systems"], "exclude": ["atrium health", "her2", "breast cancer", "abiomed", "hernia mesh"]}, {"Category": "<PERSON><PERSON><PERSON>", "Company": "Sterigenics", "Product": "Ethylene Oxide", "Injuries": ["Cancer"], "Litigation_Type": "<PERSON><PERSON><PERSON>", "LitigationName": "Ethylene Oxide Toxic Tort", "triggers": ["columbus bd", "jessup elite spice", "north haven", "river rouge", "baxter healthcare", "virginia beach", "sterilization", "river rouge", "cancer contamination", "cvr", "xomed", "lifenet", "midwest sterilization", "lemco ardmore", "groveland", "trigen laboratories", "trigen", "jorgenson labs", "erie medical center", "ethylene oxide", "san engelo", "sterigenics", "medtronic", "toxic air", "sterilization", "arthrex"], "include": ["chemicals", "industrial chemicals", "air pollution", "toxic air", "cancer", "cancers", "toxic air"]}, {"Category": "Products Liability", "Company": "Indivior", "Product": "Suboxone Film", "Injuries": ["Dental Damage"], "Litigation_Type": "Products Liability", "LitigationName": "Suboxone Products Liability", "MdlNum": "3092", "triggers": ["suboxone", "indivior", "suboxone film", "Suboxone®"]}, {"Category": "Sexual Assault", "Company": "Uber Technologies", "Product": null, "Injuries": ["Sexual Assault"], "Litigation_Type": "Sexual Assault", "LitigationName": "Uber Technologies Sexual Assault", "MdlNum": "3084", "triggers": ["uber", "lyft", "uber/lyft", "ride-sharing", "ridesharing", "rideshare"], "include": ["dating app", "dating", "harm", "assault", "sexual assault", "inappropriate", "forced kissing", "genital exposure", "sexually assaulted", "sexual abuse"]}, {"Category": "Products Liability", "Company": "Various", "Product": "Hair Relaxers", "Injuries": ["Cancer"], "Litigation_Type": "Products Liability", "LitigationName": "Hair Relaxer Products Liability", "MdlNum": "3060", "triggers": ["ovarian cancer", "uterine cancer", "endometrial cancer"], "include": ["hair relaxers", "hair relaxer", "hair straightener", "hair straighteners", "chemical hair relaxers", "chemical hair straightener"], "exclude": ["pfas", "AFFF", "firefighting", "fire fighting foam", "firefighting foam", "firefighters", "firefighting gear", "oxb<PERSON>ta", "afff", "firefighting", "fire fighting", "gard<PERSON>l", "suboxone", "merick", "indivior", "biozorb", "pfizer-biontech", "pfas"]}, {"Category": "Products Liability", "Company": "Various", "Product": "Weight Loss/Diabetes Medications", "Injuries": ["Side Effects", "Health Complications"], "Litigation_Type": "Products Liability", "LitigationName": "GLP-1 Products Liability", "description": "Cases involving weight loss injections, injectable weight loss medications, weight loss drugs, diabetes injections, or any weight loss and diabetes medications including but not limited to: Ozempic, Wegovy, Rybelsus, Trulicity, semaglutide, GLP-1. If ANY content mentions 'weight loss injection', 'injectable weight loss', 'diabetes injection', 'weight loss medication', 'weight loss drug', 'weight loss and diabetes' or similar variations, classify as this campaign.", "MdlNum": "3094", "triggers": ["ozempic", "wegovy", "ry<PERSON><PERSON>", "trulicity", "semaglutide", "mou<PERSON><PERSON>", "glp-1", "naion", "weight loss injection", "diabetes injection", "injectable weight loss", "non-arteritic anterior ischemic optic neuropathy"], "exclude": ["metformin"]}, {"Category": "Products Liability", "Company": "3M", "Product": "Firefighting Foam", "Injuries": ["Cancer"], "Litigation_Type": "Products Liability", "LitigationName": "AFFF Products Liability", "MdlNum": "2873", "triggers": ["afff", "firefighting foam", "afff foam", "afff exposure", "firefighters", "firefighting gear"], "exclude": ["los angeles", "insurance payout", "total loss", "wildfire survivors", "wildfire damage", "fire lawsuit", "explosion", "recent wildfires", "eaton fire", "wildfire", "wildfires", "fire", "fires", "propane explostions", "propane explosion", "toxic water contamination", "toxic water", "forever chemicals", "wildfire", "hair relaxer", "dog bites", "water-resistant fabrics", "cookware"]}, {"Category": "Products Liability", "Company": "3M", "Product": "<PERSON><PERSON> Warming Devices", "Injuries": ["Infections"], "Litigation_Type": "Products Liability", "LitigationName": "Bair Hugger Products Liability", "MdlNum": "2666", "triggers": ["bair hugger"], "include": ["warming device", "infections", "warming blanket"]}, {"Category": "Products Liability", "Company": "Me<PERSON><PERSON>", "Product": "<PERSON><PERSON><PERSON><PERSON>", "Injuries": ["Health Issues"], "Litigation_Type": "Products Liability", "LitigationName": "Gardasil Products Liability", "MdlNum": "3036", "triggers": ["gard<PERSON>l", "gard<PERSON>l", "merck"], "include": ["hpv vaccine", "hpv"]}, {"Category": "Products Liability", "Company": "Various", "Product": "Baby Food", "Injuries": ["Heavy Metal Contamination", "Health Issues"], "Litigation_Type": "Products Liability", "LitigationName": "Baby Food Products Liability", "MdlNum": "3101", "triggers": ["toxic baby food", "heavy metal contamination", "gerber", "best organic", "beech nut", "beech-nut", "happy baby", "plum organic", "earth's best organic baby food"], "exclude": ["data breach"]}, {"Category": "Products Liability", "Company": "Exactech", "Product": "Joint Replacements", "Injuries": ["Complications"], "Litigation_Type": "Products Liability", "LitigationName": "Exactech Products Liability", "MdlNum": "3044", "triggers": ["exactech"], "include": ["recall", "knee", "ankle", "hip", "joint replacement"]}, {"Category": "Class Action", "Company": "Various", "Product": "Online Gambling", "Injuries": ["Economic Loss"], "Litigation_Type": "Consumer Protection", "LitigationName": "Online Gambling Products Liability", "triggers": ["online gambling", "online casino", "casino slots", "unauthorized gambling"]}, {"Category": "Products Liability", "Company": "Various", "Product": "Video Games", "Injuries": ["Addiction", "Related Harm"], "Litigation_Type": "Products Liability", "LitigationName": "Video Game Addiction Products Liability", "triggers": ["justice for gaming", "is gaming taking over your child’s life", "video game addiction", "gaming disorder", "video gaming addiction", "gaming addiction", "fortnite", "call of duty", "roblo<PERSON>", "gaming", "gaming habits", "child stealing", "epic games", "minecraft", "grand theft auto", "gta", "overwatch", "world of warcraft", "rainbox six", "battlefield", "apex legends", "nba 2k", "candy crush"], "include": ["addicting", "addiction", "hooked", "gaming", "mental", "physical", "health", "sleep", "disturbances", "emotional", "distress", "exhaustion", "treatment", "recovery", "oppositional defiant disorder", "ADHD", "suicide", "harm", "habit", "habits", "addicted", "issues", "highly addictive"]}, {"Category": "Products Liability", "Company": "Fisher-Price", "Product": "<PERSON><PERSON><PERSON> Infant Swing", "Injuries": ["Injury", "Death"], "Litigation_Type": "Products Liability", "LitigationName": "Fisher-Price Snuga Infant Swing Products Liability", "triggers": ["fisher-price"], "include": ["swing", "recall", "snuga swing", "snuga swing"]}, {"Category": "Products Liability", "Company": "Various", "Product": "Social Media Platforms", "Injuries": ["Addiction", "Mental Health Issues", "Child Harm"], "Litigation_Type": "Products Liability", "LitigationName": "Social Media Addiction Products Liability", "MdlNum": "3047", "triggers": ["social media addiction", "social media harm", "instagram", "tiktok", "youtube", "facebook", "instagram", "snapchat", "meta", "social media use,", "excessive social media use", "addictive social media", "social media usage."], "include": ["social media addiction", "child harm", "mental health"], "exclude": ["video games", "te<PERSON><PERSON>", "<PERSON><PERSON>", "c-8", "oxy", "asbestos", "oxycontin", "oxy kingpins", "mass arbitration", "privacy violations", "arbitration", "extortion"]}, {"Category": "Products Liability", "Company": "Hologic", "Product": "Biomarker Implant", "Injuries": ["Surgery", "Death"], "Litigation_Type": "Products Liability", "LitigationName": "Hologic Biomarker Implant Products Liability", "triggers": ["hologic", "biozorb", "biozorb® implant lawsuit", "biozorb implant", "biozorb implants", "breast cancer implant lawsuit", "biozorb® implant", "biozorb® implants", "biozorb marker", "BioZorb® Marker"]}, {"Category": "Products Liability", "Company": "<PERSON>immer <PERSON>", "Product": "Hip Implants", "Injuries": ["Complications"], "Litigation_Type": "Products Liability", "LitigationName": "Zimmer Biomet Products Liability", "triggers": ["zimmer biomet"], "include": ["hip implant", "complications", "hip"]}, {"Category": "Products Liability", "Company": "<PERSON><PERSON><PERSON>", "Product": "<PERSON><PERSON><PERSON><PERSON>", "Injuries": ["Liver Damage"], "Litigation_Type": "Products Liability", "LitigationName": "Veozah Products Liability", "triggers": ["<PERSON><PERSON><PERSON><PERSON>"], "include": ["liver"]}, {"Category": "Privacy Violations", "Company": "Etsy", "Product": "Website", "Injuries": ["Privacy Violation"], "Litigation_Type": "Privacy Violations", "LitigationName": "Etsy Privacy Litigation", "triggers": ["etsy", "etsy's", "etsy.com"]}, {"Category": "Consumer Protection", "Company": "Event Ticket Center", "Product": "Tickets", "Injuries": ["Deceptive Fees", "Junk Fees"], "Litigation_Type": "Consumer Protection", "LitigationName": "Event Ticket Center Junk Fee Investigation", "triggers": ["event ticket center", "event ticket centers"]}, {"Category": "Consumer Protection", "Company": "Expedia", "Product": "Tickets", "Injuries": ["Deceptive Fees", "Junk Fees"], "Litigation_Type": "Consumer Protection", "LitigationName": "Expedia Deceptive Fees Investigation", "triggers": ["expedia"], "include": ["new york"]}, {"Category": "Consumer Protection", "Company": "Experian", "Product": "Credit Service", "Injuries": ["illegal contract"], "Litigation_Type": "Consumer Protection", "LitigationName": "Experian Consumer Protection", "triggers": ["experian", "identityworks", "creditworks"], "include": ["illegally restrict", "illegally restricting", "illegal cause"]}, {"Category": "Products Liability", "Company": "AMVAC Chemical Corporation", "Product": "Dacthal Pesticide", "Injuries": ["Child Injury", "Disability"], "Litigation_Type": "Products Liability", "LitigationName": "Dacthal Pesticide Products Liability", "triggers": ["dact<PERSON>", "dcpa", "pesticide"], "include": ["fetal", "birth defects", "child", "birth defect"], "exclude": ["paraqut"]}, {"Category": "Products Liability", "Company": "Various", "Product": "Asbestos", "Injuries": ["Lung Cancer", "Mesothelioma"], "Litigation_Type": "Products Liability", "LitigationName": "Asbestos Products Liability", "triggers": ["asbestos", "mesothelioma"], "exclude": ["pfas", "talcum", "talc", "toxic chemicals"]}, {"Category": "Environmental", "Company": "Various", "Product": null, "Injuries": ["Contaminated Water", "Health Issues"], "Litigation_Type": "Environmental", "LitigationName": "Mohawk & Shaw PFAS Carpet & Rug Contamination", "triggers": ["mohawk and shaw", "mohawk", "shaw"], "include": ["groundwater", "contamination", "fort", "exposed"], "exclude": ["water", "firefighting", "fire fighting", "firefighting", "firefighting foam", "fire fighting foam", "firefighters", "firefighting gear", "afff", "(afff)", "afff firefighting foam"]}, {"Category": "Environmental", "Company": "Various", "Product": null, "Injuries": ["Contaminated Water", "Health Issues"], "Litigation_Type": "Environmental", "LitigationName": "PFAS Drinking Water Contamination", "triggers": ["pfas", "toxic water contamination", "'forever chemicals", "‘Forever Chemicals’"], "include": ["groundwater", "contamination", "fort", "exposed"], "exclude": ["camp lejeune", "fertilizer", "firefighting", "fire fighting", "firefighting", "firefighting foam", "fire fighting foam", "firefighters", "firefighting gear", "afff", "(afff)", "afff firefighting foam", "mohawk", "shaw", "stain resistant"]}, {"Category": "Environmental", "Company": "Various", "Litigation_Type": "Environmental", "LitigationName": "PFAS Fertilizer Contamination", "triggers": ["pfas", "toxic"], "include": ["fertilizer"]}, {"Category": "Products Liability", "Company": "Various", "Product": "Pressure Cookers", "Injuries": ["Injuries"], "Litigation_Type": "Products Liability", "LitigationName": "Pressure Cooker Products Liability", "triggers": ["pressure cooker", "pressure cookers", "instapot", "insta-pot"]}, {"Category": "Sexual Abuse", "Company": "<PERSON>", "Product": null, "Injuries": ["Sexual Abuse"], "Litigation_Type": "Sexual Abuse", "LitigationName": "<PERSON><PERSON> Sex Abuse Litigation", "triggers": ["sean combs", "p. diddy", "diddy", "puff daddy"]}, {"Category": "Sexual Assault", "Company": "Playboy", "Product": null, "Injuries": ["Sexual Assault"], "Litigation_Type": "Sexual Assault", "LitigationName": "Playboy Sexual Assault Litigation", "triggers": ["playboy", "hugh hefner"], "include": ["sexual assault"], "exclude": []}, {"Category": "Products Liability", "Company": "Bard/Covidien", "Product": "<PERSON><PERSON>", "Injuries": ["Complications"], "Litigation_Type": "Products Liability", "LitigationName": "Bard/Covidien Hernia Mesh Products Liability", "MdlNum": "2846/3029", "triggers": ["hernia mesh", "hernia surgery", "hernia surgeries"]}, {"Category": "Horizon Therapeutics", "Company": "Te<PERSON>zza", "Product": "Hearing Loss", "Injuries": ["Hearing Loss", "hearing damage"], "Litigation_Type": "Products Liability", "LitigationName": "Tepezza Products Liability", "MdlNum": "3079", "triggers": ["te<PERSON><PERSON>"], "include": ["hearing loss", "hearing", "tinnitus"]}, {"Category": "Products Liability", "Company": "<PERSON><PERSON><PERSON><PERSON>", "Product": "<PERSON><PERSON><PERSON><PERSON>", "Injuries": ["Complications"], "Litigation_Type": "Products Liability", "LitigationName": "Filshie Clips Product Liability", "triggers": ["filshie"]}, {"Category": "Products Liability", "Company": "Cooper Surgical", "Product": "IVF Culture Media", "Injuries": ["Defective IVF Culture Medium"], "Litigation_Type": "Products Liability", "LitigationName": "Cooper Surgical IVF Culture Media", "triggers": ["cooper surgical", "coopersurgical"], "include": ["ivf", "culture medium"], "exclude": ["pgt-a", "ptg-a", "genetic", "aneuploidy"]}, {"Category": "Products Liability", "Company": "Cooper Surgical", "Product": "IVF Culture Media", "Injuries": ["Defective Aneuploidy Testing"], "Litigation_Type": "Products Liability", "LitigationName": "Cooper Surgical Defective Aneuploidy Testing", "triggers": ["ptg-a", "pgt-a", "genetic", "aneuploidy", "testing"], "include": ["cooper surgical", "coopersurgical"], "exclude": ["culture medium"]}, {"Category": "<PERSON><PERSON>med", "Company": "<PERSON><PERSON>med", "Product": "Impella Heart Pump", "Injuries": ["Complications"], "Litigation_Type": "Products Liability", "LitigationName": "Abiomed Impella Heart Pump Products Liability", "triggers": ["abiomed heart pump"]}, {"Category": "Unpaid Wage Claims", "Company": null, "Product": null, "Injuries": ["Unpaid Wages", "Overtime", "Worker Misclassification"], "Litigation_Type": "Labor", "LitigationName": "Unpaid Wage Claims", "triggers": ["unpaid wages", "overtime", "worker classification"]}, {"Category": "TCPA", "Company": null, "Product": null, "Injuries": ["Unsolicited Calls", "Unsolicited Texts", "spam text", "Spam Texts", "Call Violations"], "Litigation_Type": "Privacy", "LitigationName": "TCPA Violations", "triggers": ["tcpa", "spam texts"]}, {"Category": "Products Liability", "Company": "<PERSON><PERSON><PERSON>", "Product": "Zantac", "Injuries": ["Cancer"], "Litigation_Type": "Products Liability", "LitigationName": "Zantac Products Liability Litigation", "MdlNum": "2924", "triggers": ["zantac", "ranitidine"]}, {"Category": "Products Liability", "Company": "Various", "Product": "Hair Dye/Color Exposure", "Injuries": ["Cancer"], "Litigation_Type": "Products Liability", "LitigationName": "Hair Dye/Color Health Risk Investigation", "triggers": ["hair color", "hair dye", "color exposure", "bladder cancer"], "include": ["hair"], "exclude": ["hair relaxer", "hair relaxers", "advance magazine", "allure's", "pfas", "AFFF", "firefighting", "fire fighting foam", "firefighting foam", "firefighters", "firefighting gear", "oxb<PERSON>ta", "afff", "firefighting", "fire fighting", "gard<PERSON>l", "suboxone", "merck", "false labeling", "indivior", "biozorb", "pfizer-biontech", "pfas", "playboy", "sexual assault"]}, {"Category": "Environmental", "Company": "Southern ConEd", "Product": "NA", "Injuries": ["Property Damage, Deaths. Injuries"], "Litigation_Type": "Wildfire Litigation", "LitigationName": "LA County Wildfire Litigation", "triggers": ["eaton", "california", "los angeles", "eaton fire", "LA", "California", "<PERSON><PERSON>", "Palisades", "CA", "L.A.", "L.A. County", "Palisades", "lidia", "m<PERSON><PERSON><PERSON>", "mountain fire", "palisade", "altadena", "southern california edison", "sunset", "wildfire", "cal fair", "california", "california's", "california"], "include": ["wildfire", "fire", "fires", "wildfires"], "exclude": ["maui", "moss landing", "mountain", "hawaii", "biolab", "conyers fire lawsuit", "biolab explosition", "airport", "biolab", "bio-lab"]}, {"Category": "Environmental", "Company": "Airport", "Product": "NA", "Injuries": ["Property Damage, Deaths. Injuries"], "Litigation_Type": "Wildfire Litigation", "LitigationName": "Airport County Wildfire Litigation", "triggers": ["airport"], "include": ["wildfire"], "exclude": ["biolab", "bio-lab"]}, {"Category": "Products Liability", "Company": "Johnson & Johnson/Janssen Healthcare", "Product": "Risperdal/Invega", "Injuries": ["Cancer"], "Litigation_Type": "Products Liability", "LitigationName": "Risperdal (Invega) Products Liability", "triggers": ["risperdal", "invega"]}, {"Category": "Consumer Protection", "Company": "Honey/PayPal", "Product": "<PERSON> Browser Extension", "Injuries": ["Financial Loss", "Affiliate Commissions"], "Litigation_Type": "Consumer Protection", "LitigationName": "Honey (PayPal) Commission Theft Investigation", "triggers": ["honey", "paypal"], "include": ["commission", "commissions", "commission theft", "browser extension"]}, {"Category": "Sexual Abuse", "Company": "The Church of Jesus Christ of Latter-day Saints", "Product": null, "Injuries": ["sexual abuse", "abuse"], "Litigation_Type": "Sex Abuse", "LitigationName": "The Church of Jesus Christ of Latter-day Saints Sex Abuse Investigation", "triggers": ["mormon church", "mormon", "lds", "latter-day", "church of jesus christ"], "include": ["sexual abuse", "church", "sexual abuse", "abuse", "clergy abuse", "bishop", "stake president"]}, {"Category": "Chemical Explosion", "Company": "Biolab", "Product": null, "Injuries": ["health issues", "chemical exporure"], "Litigation_Type": "Negligence Liability", "LitigationName": "Bio-Lab. Chemical Plan Explosion Liability", "triggers": ["biolab", "bio-lab"]}, {"Category": "Negligence", "Company": "Chicago IVF", "Product": ["IVF"], "Injuries": ["lost embryos"], "Litigation_Type": "Negligence", "LitigationName": "Chicago IVF Lost Embryos Litigation", "triggers": ["chicago IVF", "chicago-ivf"]}, {"Category": "Negligence", "Company": "CrowdStrike", "Product": "Data Services", "Injuries": ["Data Outage, Financial Losses"], "Litigation_Type": "Products Liability", "LitigationName": "Crowdstrike Outage", "triggers": ["crowdstrike", "crowd strike"], "include": ["software glitch", "outage"]}, {"Category": "Products Liability", "Company": "<PERSON><PERSON>'s Head", "Product": "Deli Meats and Poultry Products", "Injuries": ["Listeria Infection", "Food Poisoning", "Death"], "Litigation_Type": "Products Liability", "LitigationName": "Listeria Products Liability", "description": "Cases involving Listeria", "triggers": ["boar's head", "boars head", "listeria"], "include": ["outbreak", "sick", "ill", "hospitalized", "diagnosed", "recall", "contamination", "monocytogenes"]}, {"Category": "Sexual Abuse", "Company": "Dr. <PERSON>", "Product": null, "Injuries": ["Sexual Abuse", "Sexual Assault"], "Litigation_Type": "Sexual Abuse", "LitigationName": "Dr. <PERSON> Sex Abuse Litigation", "description": "Cases involving sexual abuse allegations against Dr. <PERSON>", "triggers": ["dr. barry brock", "barry brock"], "include": ["abuse", "assault", "victim", "survivor"]}, {"Category": "Products Liability", "Company": "Various", "Product": "Nexium/Prilosec", "Injuries": ["St<PERSON>ch Cancer", "Gastric Cancer", "Esophageal Cancer"], "Litigation_Type": "Products Liability", "LitigationName": "Nexium/Prilosec Cancer Products Liability", "description": "Cases involving prolonged use of heartburn medications Nexium or Prilosec linked to development of stomach, gastric, or esophageal cancers.", "triggers": ["nexium", "prilosec"], "include": ["cancer", "gastric", "stomach", "esophageal"]}, {"Category": "Products Liability", "Company": "Atrium Health Wake Forest Baptist", "Product": "HER2+ Breast Cancer Diagnosis", "Injuries": ["Misdiagnosis", "Unnecessary Medical Treatment", "Emotional Trauma"], "Litigation_Type": "Products Liability", "LitigationName": "Atrium Health Wake Forest HER2+ Breast Cancer Diagnosis Products Liability", "description": "Cases involving patients at Atrium Health Wake Forest Baptist who were misdiagnosed with HER2 positive breast cancer and underwent unnecessary treatments. This includes patients who received incorrect diagnoses and underwent treatments such as chemotherapy, radiation, or surgery before being informed of the diagnostic error.", "triggers": ["atrium health wake forest", "her2", "her2+", "her2 positive"], "include": ["breast cancer", "misdiagnosis", "wrong type", "diagnostic error", "incorrectly diagnosed", "unnecessary treatment"]}, {"Category": "Consumer Protection", "Company": "Barnes & Noble", "Product": "Digital Textbooks", "Injuries": ["Hidden Fees", "Deceptive Pricing", "Financial Loss"], "Litigation_Type": "Mass Arbitration", "LitigationName": "Barnes & Noble Digital Textbook Fees Mass Arbitration", "description": "Cases involving Barnes & Noble campus bookstores' alleged practice of adding undisclosed digital delivery fees for textbooks during checkout. Particularly focuses on university bookstores including Howard and Georgetown, where students may be owed up to $1,500 for potentially illegal fee practices.", "triggers": ["barnes & noble", "digital textbook", "digital delivery fee"], "include": ["howard", "georgetown", "AU's", "American University", "mass arbitration", "bookstore", "textbooks", "digital delivery", "checkout process"], "exclude": ["store closing", "job opening", "book signing", "new release"]}, {"LitigationName": "Data Breach", "triggers": ["data breach", "security breach", "hacked", "personal information exposed", "cyberattack", "unauthorized access", "data leak", "information compromised", "stolen data"]}, {"LitigationName": "Privacy Violation", "triggers": ["online registration", "watch videos on", "schedule appointment online", "schedule consultation online", "data illegally shared", "appointment online", "schedule a consultation online", "privacy investigation", "privacy violation", "illegal tracking", "vppa", "biometrics", "uploaded photo id to verify", "video privacy protection act", "biometric information", "bipa", "illinois biometric information privacy act", "facial recognition", "cipa", "cookie tracking", "california invasion of privacy act", "wiretapping", "illegal recording", "unlawful surveillance", "session replay", "pixel tracking", "data scraping", "unauthorized data collection", "sharing users' information", "unauthorized use of data", "violated consumers' privacy", "sharing users' personal information", "data sharing", "data collection", "data privacy"]}, {"LitigationName": "<PERSON><PERSON>", "triggers": ["usury", "predatory lending", "high interest loan", "excessive interest", "payday loan trap", "illegal loan terms", "loan shark"], "exclude": ["etsy"]}, {"LitigationName": "False Advertising", "triggers": ["false advertising", "misleading claims", "deceptive marketing", "bait and switch", "false promises", "product misrepresentation"]}, {"LitigationName": "Overdraft & NSF Fees", "triggers": ["overdraft fee", "nsf fee", "non-sufficient funds fee", "excessive bank fees", "unlawful bank charges", "surprise bank fee", "bounced check", "overdraft", "non-sufficient funds", "bank charge"]}, {"LitigationName": "Wildfire/Fire Claims", "triggers": ["la wildfire", "wildfire damage", "fire damage", "burn injury", "property loss fire", "utility caused fire", "smoke inhalation", "airport fire", "eaton fire", "palisades fire", "maui wildfire"], "exclude": ["uomeod", "mini steamers"]}, {"LitigationName": "Antitrust Litigation", "triggers": ["price fixing", "antitrust", "collusion", "anti-competitive practices", "price gouging", "monopoly pricing", "bid rigging"]}, {"Company": "RealPage", "LitigationName": "RealPage Antitrust Investigation", "triggers": ["realpage", "real page"]}, {"Company": "Multiplan", "LitigationName": "Multiplan Antitrust Investigation", "triggers": ["insurance payment reimbursements", "out-of-network", "multiplan", "data isight", "viant"], "include": ["price-fixing", "price fixing"]}, {"Company": "Syngenta", "LitigationName": "Syngenta Antitrust Investigation", "triggers": ["syngenta", "corte<PERSON>", "basf"], "include": ["price-fixing", "price fixing", "antitrust"]}, {"Company": "PBMs", "LitigationName": "Insulin Pricing Litigation", "triggers": ["self-insured health plans", "eli lilly"], "include": ["insulin", "glp-1 medications"]}, {"Company": "PBM", "LitigationName": "Independent Pharamcy Antitrust Investigation", "triggers": ["independent pharmacy owners"], "include": ["price-fixing", "price fixing"]}, {"LitigationName": "Online Gambling Addiction Investigation", "triggers": ["underog fantasy", "draftkings", "fanduel", "sports bettting apps", "online sports gambling", "online sports betting"], "include": ["gambling addiction", "gambling disorder", "gambling harm", "gambling addiction investigation"], "exclude": ["video game addiction"]}, {"LitigationName": "Accolade Pacemaker Products Liability", "triggers": ["acolade pacemaker"]}, {"LitigationName": "Leqembi/Kisunla Products Liability", "triggers": ["leq<PERSON><PERSON>", "kisunla"], "include": ["brain", "brain injury"]}, {"LitigationName": "Kratom Products Liability", "triggers": ["kratom"]}, {"LitigationName": "Tabletop Fire Pits Products Liability", "triggers": ["table top fire pit", "tabletop fire pit", "tabletop fire pits", "tabletop fire pit recall"]}, {"LitigationName": "Tepezza Products Liability", "triggers": ["te<PERSON><PERSON>"]}, {"LitigationName": "Low Interest Rate Cash Sweep", "triggers": ["low interest rates", "cash sweep"]}, {"LitigationName": "Boy Scouts of America Sexual Abuse Litigation", "triggers": ["boy scouts of america", "bsa", "boy scouts"], "include": ["sexual abuse", "sexual assault", "abuse", "assault"], "exclude": ["catholic church", "catholic clergy", "catholic priests", "catholic priest"]}, {"LitigationName": "Catholic Church Sexual Abuse Litigation", "triggers": ["catholic church", "catholic church sexual abuse survivors", "catholic clergy", "catholic priests", "catholic priest", "church leaders", "clergy"], "include": ["sexual abuse", "sexual assault", "abuse", "assault"]}, {"LitigationName": "Real Page Pricing Fixing Lawsuit", "triggers": ["realpage", "rent"], "exclude": ["hertz", "runway", "job", "knetbooks.com", "knetbooks", "capital one", "credit card"]}, {"LitigationName": "Antitrust Lawsuit", "triggers": ["antitrust"]}, {"Category": "Products Liability", "Company": "3M", "Product": "3M Earplugs", "Injuries": ["Hearing Loss", "<PERSON><PERSON><PERSON>"], "Litigation_Type": "Products Liability", "LitigationName": "3M Earplugs Products Liability", "triggers": ["earplugs", "ear plugs", "3m earplugs", "3m ear plugs", "3m earplug", "3m ear plug", "hearing loss", "tinnitus"], "exclude": ["oxb<PERSON>ta", "te<PERSON><PERSON>", "va disability attorneys", "va disability attorney"]}]