# Prompt for Extracting Plaintiff Names Only

**Objective:** Identify and extract the full names of all plaintiffs mentioned in the provided legal document text.

**Input:** Legal document text (`filing`).

**Output Requirements:**

*   Return a JSON object containing a single key: `plaintiffs`.
*   The value associated with the `plaintiffs` key must be a JSON list of strings.
*   Each string in the list should be the full name of one identified plaintiff.
*   Extract names as accurately as possible, preserving titles, suffixes (Jr., Sr., II, etc.), and middle names/initials if present.
*   If no plaintiff names can be confidently identified, return an empty list: `{"plaintiffs": []}`.
*   Handle variations like "JANE DOE individually and on behalf of..." - extract "JANE DOE".
*   Handle lists like "Plaintiffs are <PERSON>, <PERSON>, and..." - extract ["<PERSON>", "<PERSON>"].
*   Only return unique names case insensitively.

**Example Output:**

```json
{
  "plaintiffs": ["<PERSON>", "<PERSON>", "ROBERT B."]
}
```
