# YAML configuration for weekly report generation
step_name: "generate_weekly_report"
input_source: "data/" # Example, adjust if needed
output_location: "data/" # Example, adjust if needed

# Date Configuration
date: '05/30/25' # MM/DD/YY format, matches main.py params
# For weekly reports, we don't need to specify start_date - it will be calculated automatically as date - 7 days
start_date: null # MM/DD/YY format or null 
end_date: null   # MM/DD/YY format or null

# LLM Configuration
llm_provider: 'deepseek'

# Upload Configuration
upload: True
upload_types:
  - 's3'
  - 'dynamodb'
force_upload: True

# Ensure force_s3_upload is also set for data transformer compatibility
force_s3_upload: True

# Number of workers
num_workers: 16

# Report Generator Configuration (from main.py params)
report_generator: True
skip_ads: True
skip_invalidate: False
weekly: True  # This flag enables weekly report generation (7-day span)