#!/usr/bin/env python3
"""
Script to recover ZIP files that may be stuck in temporary directories.
This searches for ZIP files in various temp locations and copies them to the proper dockets directory.
"""

import os
import shutil
import sys
from pathlib import Path
import subprocess
import json
from datetime import datetime

def find_zip_files_in_temp_areas():
    """Search for ZIP files in various temporary directories."""
    search_paths = [
        "/tmp",
        "/var/tmp", 
        "/Users/<USER>/Library/Caches",
        "/Users/<USER>/PycharmProjects/lexgenius/data/20250612",
        str(Path.home() / "Downloads")
    ]
    
    found_files = []
    
    for search_path in search_paths:
        if not os.path.exists(search_path):
            continue
            
        print(f"Searching in: {search_path}")
        try:
            # Use find command to search for ZIP files
            result = subprocess.run([
                "find", search_path, "-name", "*.zip", "-type", "f", "-newermt", "2025-06-12"
            ], capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                for line in result.stdout.strip().split('\n'):
                    if line and 'cand_25_' in line:
                        found_files.append(line)
                        print(f"Found potential stuck file: {line}")
                        
        except subprocess.TimeoutExpired:
            print(f"Search timed out for: {search_path}")
        except Exception as e:
            print(f"Error searching {search_path}: {e}")
    
    return found_files

def find_missing_files():
    """Find JSON files that don't have corresponding ZIP files."""
    dockets_dir = Path("/Users/<USER>/PycharmProjects/lexgenius/data/20250612/dockets")
    missing = []
    
    for json_file in dockets_dir.glob("cand_25_*.json"):
        base_name = json_file.stem
        zip_file = dockets_dir / f"{base_name}.zip"
        
        if not zip_file.exists():
            # Read JSON to check if it should have a ZIP
            try:
                with open(json_file) as f:
                    data = json.load(f)
                
                # Skip if it has processing notes, review reasons, or S3 links
                skip_reasons = [
                    data.get('_reason_review'),
                    data.get('_processing_notes'), 
                    data.get('_processing_error'),
                    data.get('s3_link')
                ]
                
                if not any(skip_reasons):
                    missing.append((str(json_file), base_name))
                    print(f"Missing ZIP for: {base_name}")
                    
            except Exception as e:
                print(f"Error reading {json_file}: {e}")
    
    return missing

def recover_files(found_files, missing_files):
    """Try to match found ZIP files to missing files and copy them."""
    dockets_dir = Path("/Users/<USER>/PycharmProjects/lexgenius/data/20250612/dockets")
    recovery_dir = Path("/Users/<USER>/PycharmProjects/lexgenius/data/20250612/dockets/recovered")
    recovery_dir.mkdir(exist_ok=True)
    
    recovered_count = 0
    
    for zip_path in found_files:
        zip_file = Path(zip_path)
        file_name = zip_file.name
        
        # Check if this matches any missing file
        for json_path, base_name in missing_files:
            if base_name in file_name or file_name.startswith(base_name):
                print(f"Recovering: {file_name} -> {base_name}.zip")
                
                # Copy to recovery directory first
                recovery_path = recovery_dir / f"{base_name}.zip"
                shutil.copy2(zip_path, recovery_path)
                
                # Then copy to final location
                final_path = dockets_dir / f"{base_name}.zip"
                shutil.copy2(zip_path, final_path)
                
                print(f"Recovered to: {final_path}")
                recovered_count += 1
                break
    
    # Also copy any found files to recovery directory for manual inspection
    for zip_path in found_files:
        zip_file = Path(zip_path)
        recovery_path = recovery_dir / f"found_{zip_file.name}"
        if not recovery_path.exists():
            shutil.copy2(zip_path, recovery_path)
            print(f"Copied for inspection: {recovery_path}")
    
    return recovered_count

def main():
    print("=== PACER File Recovery Script ===")
    print(f"Started at: {datetime.now()}")
    
    print("\n1. Finding missing ZIP files...")
    missing_files = find_missing_files()
    
    print(f"\nFound {len(missing_files)} missing ZIP files")
    
    print("\n2. Searching for ZIP files in temp areas...")
    found_files = find_zip_files_in_temp_areas()
    
    print(f"\nFound {len(found_files)} potential files in temp areas")
    
    if found_files:
        print("\n3. Attempting recovery...")
        recovered = recover_files(found_files, missing_files)
        print(f"\nRecovered {recovered} files")
    else:
        print("\nNo files found in temp areas to recover")
    
    print(f"\nCompleted at: {datetime.now()}")

if __name__ == "__main__":
    main()