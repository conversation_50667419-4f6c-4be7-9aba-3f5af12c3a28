#!/usr/bin/env python3
"""
Script to process MDL 2738 docket numbers and fix length issues
"""
import asyncio
import re
from typing import List, Dict, Any
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.prompt import Confirm
from rich.progress import Progress, SpinnerColumn, TextColumn

from src.infrastructure.storage.dynamodb_async import AsyncDynamoDBStorage
from src.repositories.pacer_repository import PacerRepository

console = Console()

async def main():
    """Main processing function"""
    
    # Initialize storage and repository
    console.print(Panel("🔍 Initializing PACER Repository", style="blue"))
    storage = AsyncDynamoDBStorage({})
    
    async with storage:
        repo = PacerRepository(storage)
        
        # Query MDL 2738 data
        with Progress(SpinnerColumn(), TextColumn("[progress.description]{task.description}")) as progress:
            task = progress.add_task("Querying MDL 2738 data for date range 20250605-20250612...", total=None)
            
            records = await repo.query_by_mdl_and_date_range("2738", "20250605", "20250612")
            progress.update(task, completed=True)
        
        console.print(f"✅ Found {len(records)} records for MDL 2738")
        
        if not records:
            console.print("[yellow]No records found. Exiting.[/yellow]")
            return
        
        # Analyze docket numbers
        long_docket_records = []
        pattern = re.compile(r'^(.*?\d{5}).*$')  # Match everything up to and including 5 consecutive digits
        
        console.print("\n📊 Analyzing docket numbers...")
        
        for record in records:
            docket_num = record.get('DocketNum', '')
            if len(docket_num) > 13:
                long_docket_records.append(record)
        
        console.print(f"🔢 Found {len(long_docket_records)} records with docket_num length > 13 characters")
        
        # Display table of long docket numbers
        if long_docket_records:
            table = Table(title="Docket Numbers > 13 Characters", show_header=True, header_style="bold magenta")
            table.add_column("Filing Date", style="cyan")
            table.add_column("Current Docket Number", style="yellow")
            table.add_column("Length", style="red")
            table.add_column("Proposed Fix", style="green")
            
            for record in long_docket_records:
                current_docket = record.get('DocketNum', '')
                filing_date = record.get('FilingDate', '')
                
                # Extract the part up to and including 5 consecutive digits
                match = pattern.match(current_docket)
                proposed_fix = match.group(1) if match else current_docket
                
                table.add_row(
                    filing_date,
                    current_docket,
                    str(len(current_docket)),
                    proposed_fix
                )
            
            console.print(table)
            
            # Wait for user approval
            console.print(f"\n🚨 Ready to update {len(long_docket_records)} records")
            console.print("This will:")
            console.print("• Truncate docket numbers after 5 consecutive digits")
            console.print("• Update records in DynamoDB using PK:FilingDate SK:DocketNum")
            
            if Confirm.ask("Do you want to proceed with the updates?"):
                await update_records(repo, long_docket_records, pattern)
            else:
                console.print("[yellow]Operation cancelled by user.[/yellow]")
        else:
            console.print("[green]No records found with docket numbers > 13 characters.[/green]")

async def update_records(repo: PacerRepository, records: List[Dict[str, Any]], pattern: re.Pattern):
    """Update the records with truncated docket numbers"""
    
    with Progress() as progress:
        task = progress.add_task("Updating records...", total=len(records))
        
        success_count = 0
        error_count = 0
        
        for record in records:
            try:
                current_docket = record.get('DocketNum', '')
                filing_date = record.get('FilingDate', '')
                
                # Extract the part up to and including 5 consecutive digits
                match = pattern.match(current_docket)
                new_docket = match.group(1) if match else current_docket
                
                if new_docket != current_docket:
                    # Since DocketNum is part of the primary key, we need to:
                    # 1. Create a new record with the corrected docket number  
                    # 2. Delete the old record
                    
                    # Create updated record
                    updated_record = record.copy()
                    updated_record['DocketNum'] = new_docket
                    
                    # Add the new record first
                    success = await repo.add_or_update_record(updated_record)
                    if success:
                        # Now delete the old record
                        old_key = {'FilingDate': filing_date, 'DocketNum': current_docket}
                        try:
                            await repo.storage.delete_item(repo.table_name, old_key)
                            success_count += 1
                            console.print(f"✅ Updated: {current_docket} → {new_docket}")
                        except Exception as delete_error:
                            console.print(f"⚠️  Created new record but failed to delete old: {current_docket} - {delete_error}")
                            success_count += 1  # Still count as success since new record was created
                    else:
                        error_count += 1
                        console.print(f"❌ Failed to create new record: {current_docket}")
                else:
                    console.print(f"⏭️  No change needed: {current_docket}")
                
            except Exception as e:
                error_count += 1
                console.print(f"❌ Error updating {record.get('DocketNum', 'unknown')}: {e}")
            
            progress.advance(task)
    
    # Summary
    console.print(Panel(
        f"✅ Successfully updated: {success_count}\n"
        f"❌ Errors: {error_count}\n"
        f"📊 Total processed: {len(records)}\n"
        f"🗑️  Old records automatically deleted during update process",
        title="Update Summary",
        style="green"
    ))

if __name__ == "__main__":
    asyncio.run(main())