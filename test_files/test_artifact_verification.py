#!/usr/bin/env python3
"""
Test file for artifact verification functionality.
This tests the critical path validation fixes for failed download reprocessing.

CRITICAL WARNING: DO NOT MODIFY THE PATH VALIDATION LOGIC IN:
- src/pacer/file_manager.py (check_if_artifact_exists_* methods)
- src/pacer/docket_processor.py (verify_case method)
- src/pacer/pacer_document_downloader.py (context_download_path validation)

These paths are essential for proper download file handling and reprocessing failed downloads.
"""

import asyncio
import tempfile
import json
from datetime import date, timedelta
from pathlib import Path
import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from pacer.file_manager import PacerFileManager

async def test_artifact_verification():
    """Test that artifact verification correctly distinguishes between PDF/ZIP files and JSON-only cases."""
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        file_manager = PacerFileManager(str(temp_path))
        
        # Set up test date
        test_date = date.today()
        iso_date = test_date.strftime('%Y%m%d')
        
        # Create test directory structure
        dockets_dir = temp_path / iso_date / 'dockets'
        dockets_dir.mkdir(parents=True, exist_ok=True)
        
        # Test case 1: JSON file only (should return False for artifact check)
        json_only_base = "test_25_12345_case_json_only"
        json_only_path = dockets_dir / f"{json_only_base}.json"
        
        with open(json_only_path, 'w') as f:
            json.dump({
                "court_id": "test",
                "docket_num": "1:25-cv-12345",
                "_processing_notes": "Download failed."
            }, f)
        
        # Test case 2: JSON + ZIP file (should return True for artifact check)
        json_zip_base = "test_25_67890_case_with_zip"
        json_zip_json_path = dockets_dir / f"{json_zip_base}.json"
        json_zip_zip_path = dockets_dir / f"{json_zip_base}.zip"
        
        with open(json_zip_json_path, 'w') as f:
            json.dump({
                "court_id": "test", 
                "docket_num": "1:25-cv-67890",
                "is_downloaded": True
            }, f)
        
        with open(json_zip_zip_path, 'wb') as f:
            f.write(b"fake zip content")
        
        # Test case 3: JSON + PDF file (should return True for artifact check)
        json_pdf_base = "test_25_11111_case_with_pdf"
        json_pdf_json_path = dockets_dir / f"{json_pdf_base}.json"
        json_pdf_pdf_path = dockets_dir / f"{json_pdf_base}.pdf"
        
        with open(json_pdf_json_path, 'w') as f:
            json.dump({
                "court_id": "test",
                "docket_num": "1:25-cv-11111", 
                "is_downloaded": True
            }, f)
        
        with open(json_pdf_pdf_path, 'wb') as f:
            f.write(b"fake pdf content")
        
        print("Testing artifact verification methods...")
        
        # Test artifact-only checks (should ignore JSON content)
        result1 = await file_manager.check_if_artifact_exists_by_pattern(iso_date, "test", "25_12345")
        assert result1 == False, f"Expected False for JSON-only case, got {result1}"
        print("✓ JSON-only case correctly returns False for artifact check")
        
        result2 = await file_manager.check_if_artifact_exists_by_pattern(iso_date, "test", "25_67890")
        assert result2 == True, f"Expected True for JSON+ZIP case, got {result2}"
        print("✓ JSON+ZIP case correctly returns True for artifact check")
        
        result3 = await file_manager.check_if_artifact_exists_by_pattern(iso_date, "test", "25_11111")
        assert result3 == True, f"Expected True for JSON+PDF case, got {result3}"
        print("✓ JSON+PDF case correctly returns True for artifact check")
        
        # Test 7-day artifact check
        result4 = await file_manager.check_if_artifact_exists_last_7_days_by_pattern(test_date, "test", "25_12345")
        assert result4 == False, f"Expected False for JSON-only case in 7-day check, got {result4}"
        print("✓ 7-day artifact check correctly returns False for JSON-only case")
        
        result5 = await file_manager.check_if_artifact_exists_last_7_days_by_pattern(test_date, "test", "25_67890")
        assert result5 == True, f"Expected True for JSON+ZIP case in 7-day check, got {result5}"
        print("✓ 7-day artifact check correctly returns True for JSON+ZIP case")
        
        # Compare with regular download check (which does check JSON content)
        result6 = await file_manager.check_if_downloaded_by_pattern(iso_date, "test", "25_12345")
        assert result6 == True, f"Expected True for JSON-only case with skip reasons, got {result6}"
        print("✓ Regular download check correctly finds skip reasons in JSON-only case")
        
        print("\n" + "="*60)
        print("CRITICAL PATH VALIDATION TESTS PASSED")
        print("="*60)
        print("The artifact verification logic correctly:")
        print("- Ignores JSON content for explicitly requested cases")
        print("- Only checks for actual PDF/ZIP artifacts")
        print("- Allows failed downloads to be reprocessed")
        print("- Maintains existing behavior for regular cases")
        print("="*60)

if __name__ == "__main__":
    asyncio.run(test_artifact_verification())