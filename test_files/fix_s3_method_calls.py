#!/usr/bin/env python3
"""Fix S3 method calls that were incorrectly updated."""
import re
from pathlib import Path
from typing import List

# Files that need fixing based on the warnings
FILES_TO_FIX = [
    "src/lib/s3_manager.py",
    "src/data_transformer/file_handler.py", 
    "src/fb_ads/image_handler.py",
]

def fix_file(file_path: Path) -> bool:
    """Fix S3 method calls in a file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
    except Exception as e:
        print(f"Error reading {file_path}: {e}")
        return False
    
    original_content = content
    
    # Fix incorrect upload_content calls that should be upload_file
    # Pattern: .upload_content(local_path, s3_key) where local_path is a file path
    content = re.sub(
        r'\.upload_content\s*\(\s*([a-zA-Z_][a-zA-Z0-9_]*(?:_path|_file)?)\s*,\s*([^)]+)\)',
        r'.upload_file(\1, \2)',
        content
    )
    
    # Fix incorrect download_content calls that should be download_file  
    # Pattern: .download_content(key, local_path)
    content = re.sub(
        r'\.download_content\s*\(\s*([^,]+),\s*([a-zA-Z_][a-zA-Z0-9_]*(?:_path|_file)?)\s*\)',
        r'.download_file(\1, \2)',
        content
    )
    
    # Fix boto3 client calls that were incorrectly changed
    content = re.sub(
        r's3_client\.download_content\s*\(',
        r's3_client.download_file(',
        content
    )
    
    content = re.sub(
        r's3_client\.upload_content\s*\(',
        r's3_client.upload_file(',
        content
    )
    
    # Only write if content changed
    if content != original_content:
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✓ Fixed: {file_path}")
            return True
        except Exception as e:
            print(f"Error writing {file_path}: {e}")
            return False
    else:
        print(f"No changes needed: {file_path}")
        return False


def main():
    """Main function to fix S3 method calls."""
    root_dir = Path(__file__).parent
    
    print("Fixing S3 method calls...")
    print("=" * 60)
    
    fixed_count = 0
    
    for file_path in FILES_TO_FIX:
        full_path = root_dir / file_path
        if full_path.exists():
            if fix_file(full_path):
                fixed_count += 1
        else:
            print(f"⚠️  File not found: {file_path}")
    
    print("\n" + "=" * 60)
    print(f"Fixed {fixed_count} files")


if __name__ == "__main__":
    main()