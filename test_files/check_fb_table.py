"""Quick script to check FB Ad Archive table structure and data"""
import boto3
from boto3.dynamodb.conditions import Key

# Create DynamoDB client
dynamodb = boto3.resource('dynamodb', region_name='us-west-2')
table = dynamodb.Table('FBAdArchive')

print(f"Table name: {table.table_name}")
print(f"Table status: {table.table_status}")
print(f"Item count: {table.item_count}")

# Check indexes
print("\nGlobal Secondary Indexes:")
if hasattr(table, 'global_secondary_indexes') and table.global_secondary_indexes:
    for idx in table.global_secondary_indexes:
        print(f"  - {idx['IndexName']}: {idx['KeySchema']}")
else:
    print("  No GSIs found")

# Try a simple scan to see if there's data
print("\nTrying a scan (limit 5)...")
response = table.scan(Limit=5)
items = response.get('Items', [])
print(f"Found {len(items)} items")

if items:
    print("\nSample item keys:")
    for key in sorted(items[0].keys())[:10]:
        print(f"  - {key}: {type(items[0][key]).__name__}")
    
    # Check StartDate values
    print("\nStartDate values in sample:")
    for item in items:
        if 'StartDate' in item:
            print(f"  - {item.get('StartDate')}")

# Try querying by a specific date if we found one
if items and 'StartDate' in items[0]:
    test_date = items[0]['StartDate']
    print(f"\nTrying to query StartDate-index for date: {test_date}")
    try:
        response = table.query(
            IndexName='StartDate-index',
            KeyConditionExpression=Key('StartDate').eq(test_date)
        )
        print(f"Query returned {len(response.get('Items', []))} items")
    except Exception as e:
        print(f"Query failed: {e}")