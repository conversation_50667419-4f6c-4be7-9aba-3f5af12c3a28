#!/usr/bin/env python3
"""
Test script to verify docket number normalization is working correctly.
Tests the examples provided by the user to ensure consistent 13-character formatting.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.core.html.html_case_parser import HTMLCaseParser
from src.pacer.docket_processor import DocketProcessor

def test_docket_normalization():
    """Test docket number normalization with user-provided examples."""
    
    # Test cases from user's examples
    test_cases = [
        # (input_docket, expected_output, description)
        ("1:25-cv-08592", "1:25-cv-08592", "Already correct 13-character format"),
        ("1:25-cv-08592-RMB-SAK", "1:25-cv-08592", "Should drop judge initials"),
        ("3:24-cv-123", "3:24-cv-00123", "Should zero-pad to 5 digits"),
        ("2:23-sf-1", "2:23-sf-00001", "Should zero-pad single digit"),
        ("1:25-cv-8592", "1:25-cv-08592", "Should zero-pad 4 digits to 5"),
        ("2:21-cv-12345-ABC-XYZ", "2:21-cv-12345", "Should drop multiple suffixes"),
    ]
    
    print("Testing docket number normalization...")
    print("="*60)
    
    # Test HTML parser normalization
    print("\n1. Testing HTMLCaseParser._normalize_docket_to_13_chars:")
    for input_docket, expected, description in test_cases:
        result = HTMLCaseParser._normalize_docket_to_13_chars(input_docket)
        status = "✓ PASS" if result == expected else "✗ FAIL"
        print(f"{status} | Input: '{input_docket}' → Output: '{result}' | Expected: '{expected}'")
        if result != expected:
            print(f"      Description: {description}")
    
    # Test DocketProcessor normalization (simulated since it's a method of a class that requires initialization)
    print("\n2. Testing DocketProcessor._extract_db_docket_number pattern:")
    print("(Note: This tests the same logic pattern as implemented in the processor)")
    
    def simulate_extract_db_docket_number(docket_num: str) -> str:
        """Simulate the _extract_db_docket_number function logic."""
        import re
        if ':' not in docket_num:
            return docket_num
            
        try:
            prefix = docket_num.split(':')[0]  # N
            suffix_parts = docket_num.split(':')[1].split('-')
            
            if len(suffix_parts) >= 3:
                year = suffix_parts[0]  # YY
                case_type = suffix_parts[1]  # XX (cv, sf, etc.)
                docket_part = suffix_parts[2]  # NNNNN-...
                
                # Extract digits and zero-pad to exactly 5 digits
                num_match = re.search(r'(\d{1,5})', docket_part)
                if num_match:
                    digits = num_match.group(1)
                    # Zero-pad to exactly 5 digits
                    five_digits = digits.zfill(5)
                    return f"{prefix}:{year}-{case_type}-{five_digits}"
                else:
                    # Fallback: use original if no digit match
                    return docket_num
            else:
                # Fallback for unexpected formats
                return docket_num
        except Exception:
            # Fallback for any parsing errors
            return docket_num
    
    for input_docket, expected, description in test_cases:
        result = simulate_extract_db_docket_number(input_docket)
        status = "✓ PASS" if result == expected else "✗ FAIL"
        print(f"{status} | Input: '{input_docket}' → Output: '{result}' | Expected: '{expected}'")
        if result != expected:
            print(f"      Description: {description}")
    
    print("\n" + "="*60)
    print("Test completed!")
    print("\nKey findings:")
    print("- All docket numbers should be normalized to exactly 13 characters")
    print("- Format: N:NN-XX-NNNNN (where N=digit, XX=2 letters, NNNNN=5 zero-padded digits)")
    print("- Judge initials and additional suffixes are dropped")
    print("- Numbers less than 5 digits are zero-padded on the left")

if __name__ == "__main__":
    test_docket_normalization()