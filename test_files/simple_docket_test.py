#!/usr/bin/env python3
"""
Simple test of docket number normalization logic without complex imports.
"""

import re

def normalize_docket_to_13_chars(docket_num: str) -> str:
    """Normalize docket number to exactly 13 characters: N:NN-XX-NNNNN"""
    if not docket_num or ':' not in docket_num:
        return docket_num
        
    try:
        prefix = docket_num.split(':')[0]  # N
        suffix_parts = docket_num.split(':')[1].split('-')
        
        if len(suffix_parts) >= 3:
            year = suffix_parts[0]  # YY
            case_type = suffix_parts[1]  # XX (cv, sf, etc.)
            docket_part = suffix_parts[2]  # NNNNN-...
            
            # Extract and zero-pad to exactly 5 digits
            num_match = re.search(r'(\d{1,5})', docket_part)
            if num_match:
                digits = num_match.group(1)
                # Zero-pad to 5 digits
                five_digits = digits.zfill(5)
                return f"{prefix}:{year}-{case_type}-{five_digits}"
        
        return docket_num  # Fallback for unexpected formats
    except Exception:
        return docket_num  # Fallback for any parsing errors

def test_docket_normalization():
    """Test docket number normalization with user-provided examples."""
    
    # Test cases from user's examples
    test_cases = [
        # (input_docket, expected_output, description)
        ("1:25-cv-08592", "1:25-cv-08592", "Already correct 13-character format"),
        ("1:25-cv-08592-RMB-SAK", "1:25-cv-08592", "Should drop judge initials"),
        ("3:24-cv-123", "3:24-cv-00123", "Should zero-pad to 5 digits"),
        ("2:23-sf-1", "2:23-sf-00001", "Should zero-pad single digit"),
        ("1:25-cv-8592", "1:25-cv-08592", "Should zero-pad 4 digits to 5"),
        ("2:21-cv-12345-ABC-XYZ", "2:21-cv-12345", "Should drop multiple suffixes"),
    ]
    
    print("Testing docket number normalization...")
    print("="*80)
    
    all_passed = True
    
    for input_docket, expected, description in test_cases:
        result = normalize_docket_to_13_chars(input_docket)
        status = "✓ PASS" if result == expected else "✗ FAIL"
        if result != expected:
            all_passed = False
        print(f"{status} | Input: '{input_docket:20}' → Output: '{result:15}' | Expected: '{expected}'")
        print(f"      Description: {description}")
        print()
    
    print("="*80)
    if all_passed:
        print("🎉 ALL TESTS PASSED!")
    else:
        print("❌ SOME TESTS FAILED!")
        
    print("\nKey findings:")
    print("- All docket numbers are normalized to exactly 13 characters")
    print("- Format: N:NN-XX-NNNNN (where N=digit, XX=2 letters, NNNNN=5 zero-padded digits)")
    print("- Judge initials and additional suffixes are dropped")
    print("- Numbers less than 5 digits are zero-padded on the left")
    
    return all_passed

if __name__ == "__main__":
    test_docket_normalization()