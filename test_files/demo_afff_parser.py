#!/usr/bin/env python3
"""
Demo script to show AFFF parser outputs without interactive prompts.
"""

from parse_afff_filings import AFFFFilingsParser
import json

def main():
    # Create parser instance
    parser = AFFFFilingsParser()
    
    # Load data
    print("Loading AFFF Filings data...")
    if not parser.load_data():
        return
    
    print("\n" + "="*50)
    print("PARSING STATISTICS")
    print("="*50)
    print(f"Total Records: {len(parser.parsed_data)}")
    print(f"Records with Attorneys: {sum(1 for r in parser.parsed_data if r['attorney'])}")
    print(f"Records with Case Numbers: {sum(1 for r in parser.parsed_data if r['case_number'])}")
    print(f"Unique Attorneys: {len(parser.attorney_data)}")
    
    print("\n" + "="*50)
    print("TOP 10 ATTORNEYS BY FILING COUNT")
    print("="*50)
    
    # Get attorney summary
    attorney_summary = [
        (name, len(data["filings"])) 
        for name, data in parser.attorney_data.items()
    ]
    attorney_summary.sort(key=lambda x: x[1], reverse=True)
    
    # Calculate totals
    total_filings = sum(count for _, count in attorney_summary)
    
    print(f"{'Rank':<6} {'Attorney Name':<30} {'Filings':>10}")
    print("-" * 50)
    for rank, (attorney, count) in enumerate(attorney_summary[:10], 1):
        print(f"{rank:<6} {attorney:<30} {count:>10,}")
    
    print("-" * 50)
    print(f"{'TOP 10 SUBTOTAL':<36} {sum(count for _, count in attorney_summary[:10]):>10,}")
    print("=" * 50)
    print(f"{'TOTAL (ALL {})'.format(len(attorney_summary)) + ' ATTORNEYS':<36} {total_filings:>10,}")
    print(f"{'AVERAGE PER ATTORNEY':<36} {total_filings/len(attorney_summary):>10.1f}")
    
    # Export sample JSON
    print("\n" + "="*50)
    print("SAMPLE JSON OUTPUT (First 2 attorneys)")
    print("="*50)
    
    sample_data = {}
    for attorney_name, data in list(parser.attorney_data.items())[:2]:
        sample_data[attorney_name] = {
            "law_firm": data["law_firm"],
            "filing_count": len(data["filings"]),
            "filings": data["filings"][:3]  # Show first 3 filings only
        }
    
    print(json.dumps(sample_data, indent=2))
    
    # Save full outputs
    print("\n" + "="*50)
    print("SAVING OUTPUTS")
    print("="*50)
    
    # Save CSV
    with open("test_files/parsed_afff_filings.csv", 'w', newline='', encoding='utf-8') as f:
        import csv
        fieldnames = ['date', 'doc_num', 'plaintiff', 'defendant', 'versus', 'attorney', 'case_number']
        writer = csv.DictWriter(f, fieldnames=fieldnames)
        writer.writeheader()
        for result in parser.parsed_data:
            writer.writerow(result)
    print("✓ Saved CSV to test_files/parsed_afff_filings.csv")
    
    # Save JSON
    export_data = {}
    for attorney_name, data in attorney_summary:
        export_data[attorney_name] = {
            "law_firm": "",  # Empty for now
            "filing_count": data[1] if isinstance(data, tuple) else len(parser.attorney_data[attorney_name]["filings"]),
            "filings": parser.attorney_data[attorney_name]["filings"]
        }
    
    with open("test_files/attorney_filings.json", 'w', encoding='utf-8') as f:
        json.dump(export_data, f, indent=2, ensure_ascii=False)
    print("✓ Saved JSON to test_files/attorney_filings.json")

if __name__ == "__main__":
    main()