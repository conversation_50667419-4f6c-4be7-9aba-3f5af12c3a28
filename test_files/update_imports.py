#!/usr/bin/env python3
"""Update imports for refactored components."""
import os
import re
from pathlib import Path
from typing import List, Tuple

# Define import mappings
IMPORT_MAPPINGS = [
    # S3AsyncStorage(r'from src\.lib\.s3_manager import S3Manager',
     'from src.infrastructure.storage.s3_async import S3AsyncStorage'),
    (r'from src\.lib\.s3_manager import S3ManagerAsync',
     'from src.infrastructure.storage.s3_async import S3AsyncStorage'),
    
    # AI Services
    (r'from src\.lib\.ai_integrator import AIIntegrator',
     'from src.services.ai.ai_orchestrator import AIOrchestrator'),
    (r'from src\.lib\.gpt4_interface import GPT4Interface',
     'from src.infrastructure.external.openai_client import OpenAIClient'),
    (r'from src\.lib\.deepseek_interface import DeepSeekInterface',
     'from src.infrastructure.external.deepseek_client import DeepSeekClient'),
    (r'from src\.lib\.llava_vision import LLaVAVision',
     'from src.infrastructure.external.llava_client import LLaVAClient'),
    
    # CloudFront
    (r'from src\.lib\.cloudfront_invalidator import CloudFrontInvalidator',
     'from src.infrastructure.messaging.cloudfront import CloudFrontService'),
    (r'from src\.lib\.invalidate_cloudfront import CloudFrontInvalidator',
     'from src.infrastructure.messaging.cloudfront import CloudFrontService'),
]

# Class name mappings
CLASS_MAPPINGS = [
    ('S3Manager', 'S3AsyncStorage'),
    ('S3ManagerAsync', 'S3AsyncStorage'),
    ('AIIntegrator', 'AIOrchestrator'),
    ('GPT4Interface', 'OpenAIClient'),
    ('DeepSeekInterface', 'DeepSeekClient'),
    ('LLaVAVision', 'LLaVAClient'),
    ('CloudFrontInvalidator', 'CloudFrontService'),
]


def update_file(file_path: Path) -> bool:
    """Update imports in a single file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
    except Exception as e:
        print(f"Error reading {file_path}: {e}")
        return False
    
    original_content = content
    
    # Update imports
    for old_import, new_import in IMPORT_MAPPINGS:
        content = re.sub(old_import, new_import, content)
    
    # Update class names in the code
    for old_class, new_class in CLASS_MAPPINGS:
        # Update class instantiation
        content = re.sub(
            rf'\b{old_class}\s*\(',
            f'{new_class}(',
            content
        )
        # Update type hints
        content = re.sub(
            rf':\s*{old_class}\b',
            f': {new_class}',
            content
        )
        # Update isinstance checks
        content = re.sub(
            rf'isinstance\s*\(([^,]+),\s*{old_class}\s*\)',
            rf'isinstance(\1, {new_class})',
            content
        )
    
    # Only write if content changed
    if content != original_content:
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            return True
        except Exception as e:
            print(f"Error writing {file_path}: {e}")
            return False
    
    return False


def find_python_files(root_dir: Path, exclude_dirs: List[str]) -> List[Path]:
    """Find all Python files in the project."""
    python_files = []
    
    for file_path in root_dir.rglob('*.py'):
        # Skip excluded directories
        if any(excluded in file_path.parts for excluded in exclude_dirs):
            continue
        python_files.append(file_path)
    
    return python_files


def main():
    """Main function to update all imports."""
    root_dir = Path(__file__).parent
    exclude_dirs = ['.venv', 'venv', '__pycache__', '.git', 'build', 'dist', '.pytest_cache']
    
    print("Finding Python files...")
    python_files = find_python_files(root_dir, exclude_dirs)
    print(f"Found {len(python_files)} Python files")
    
    updated_files = []
    
    print("\nUpdating imports...")
    for file_path in python_files:
        if update_file(file_path):
            updated_files.append(file_path)
            print(f"✓ Updated: {file_path.relative_to(root_dir)}")
    
    print(f"\nSummary:")
    print(f"Total files scanned: {len(python_files)}")
    print(f"Files updated: {len(updated_files)}")
    
    if updated_files:
        print("\nUpdated files:")
        for file_path in updated_files:
            print(f"  - {file_path.relative_to(root_dir)}")


if __name__ == "__main__":
    main()