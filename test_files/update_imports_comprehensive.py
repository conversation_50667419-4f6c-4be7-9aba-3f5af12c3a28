#!/usr/bin/env python3
"""Comprehensive import and method update for refactored components."""
import os
import re
from pathlib import Path
from typing import List, Tuple, Dict

# Define import mappings
IMPORT_MAPPINGS = [
    # S3AsyncStorage(r'from src\.lib\.s3_manager import S3Manager',
     'from src.infrastructure.storage.s3_async import S3AsyncStorage'),
    (r'from src\.lib\.s3_manager import S3ManagerAsync',
     'from src.infrastructure.storage.s3_async import S3AsyncStorage'),
    
    # AI Services
    (r'from src\.lib\.ai_integrator import AIIntegrator',
     'from src.services.ai.ai_orchestrator import AIOrchestrator'),
    (r'from src\.lib\.gpt4_interface import GPT4Interface',
     'from src.infrastructure.external.openai_client import OpenAIClient'),
    (r'from src\.lib\.deepseek_interface import DeepSeekInterface',
     'from src.infrastructure.external.deepseek_client import DeepSeekClient'),
    (r'from src\.lib\.llava_vision import LLaVAVision',
     'from src.infrastructure.external.llava_client import LLaVAClient'),
    
    # CloudFront
    (r'from src\.lib\.cloudfront_invalidator import CloudFrontInvalidator',
     'from src.infrastructure.messaging.cloudfront import CloudFrontService'),
    (r'from src\.lib\.invalidate_cloudfront import CloudFrontInvalidator',
     'from src.infrastructure.messaging.cloudfront import CloudFrontService'),
]

# Class name mappings
CLASS_MAPPINGS = [
    ('S3Manager', 'S3AsyncStorage'),
    ('S3ManagerAsync', 'S3AsyncStorage'),
    ('AIIntegrator', 'AIOrchestrator'),
    ('GPT4Interface', 'OpenAIClient'),
    ('DeepSeekInterface', 'DeepSeekClient'),
    ('LLaVAVision', 'LLaVAClient'),
    ('CloudFrontInvalidator', 'CloudFrontService'),
]

# Method name mappings (old -> new)
METHOD_MAPPINGS = {
    # S3 method mappings
    'upload_file': 'upload_content',
    'download_file': 'download_content',
    'list_files': 'list_objects',
    'delete_file': 'delete_object',
    
    # AI method mappings
    'generate': 'process_text',
    'analyze_image': 'analyze_image',  # Same name
    'extract_text_from_image': 'extract_text_from_image',  # Same name
    
    # CloudFront method mappings
    'invalidate': 'invalidate_paths',
}

# Method signature changes
METHOD_SIGNATURE_CHANGES = {
    # S3Manager.upload_content(file_path, s3_key) -> S3AsyncStorage.upload_content(content, object_key)
    'upload_file': {
        'note': 'Now expects content bytes instead of file path',
        'example': 'await s3.upload_content(content=file_content, object_key=s3_key)'
    },
    'download_file': {
        'note': 'Returns bytes directly instead of saving to file',
        'example': 'content = await s3.download_content(object_key)'
    }
}


def update_file(file_path: Path, report_changes: bool = True) -> Dict[str, List[str]]:
    """Update imports and method calls in a single file."""
    changes = {
        'imports': [],
        'classes': [],
        'methods': [],
        'warnings': []
    }
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
    except Exception as e:
        changes['warnings'].append(f"Error reading file: {e}")
        return changes
    
    original_content = content
    
    # Update imports
    for old_import, new_import in IMPORT_MAPPINGS:
        if re.search(old_import, content):
            content = re.sub(old_import, new_import, content)
            changes['imports'].append(f"{old_import} -> {new_import}")
    
    # Update class names
    for old_class, new_class in CLASS_MAPPINGS:
        # Update class instantiation
        pattern = rf'\b{old_class}\s*\('
        if re.search(pattern, content):
            content = re.sub(pattern, f'{new_class}(', content)
            changes['classes'].append(f"{old_class} -> {new_class}")
        
        # Update type hints
        pattern = rf':\s*{old_class}\b'
        if re.search(pattern, content):
            content = re.sub(pattern, f': {new_class}', content)
            if f"{old_class} -> {new_class}" not in changes['classes']:
                changes['classes'].append(f"{old_class} -> {new_class}")
    
    # Update method calls
    for old_method, new_method in METHOD_MAPPINGS.items():
        pattern = rf'\.{old_method}\s*\('
        if re.search(pattern, content):
            content = re.sub(pattern, f'.{new_method}(', content)
            changes['methods'].append(f"{old_method} -> {new_method}")
            
            # Add warning if signature changed
            if old_method in METHOD_SIGNATURE_CHANGES:
                warning = f"Method {old_method} signature changed: {METHOD_SIGNATURE_CHANGES[old_method]['note']}"
                changes['warnings'].append(warning)
    
    # Only write if content changed
    if content != original_content:
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
        except Exception as e:
            changes['warnings'].append(f"Error writing file: {e}")
    
    return changes


def find_python_files(root_dir: Path, exclude_dirs: List[str]) -> List[Path]:
    """Find all Python files in the project."""
    python_files = []
    
    for file_path in root_dir.rglob('*.py'):
        # Skip excluded directories
        if any(excluded in file_path.parts for excluded in exclude_dirs):
            continue
        python_files.append(file_path)
    
    return python_files


def main():
    """Main function to update all imports and methods."""
    root_dir = Path(__file__).parent
    exclude_dirs = ['.venv', 'venv', '__pycache__', '.git', 'build', 'dist', '.pytest_cache', 'deprecated']
    
    print("Finding Python files...")
    python_files = find_python_files(root_dir, exclude_dirs)
    print(f"Found {len(python_files)} Python files")
    
    updated_files = []
    all_warnings = []
    
    print("\nUpdating imports and methods...")
    for file_path in python_files:
        changes = update_file(file_path)
        
        if any(changes[key] for key in ['imports', 'classes', 'methods']):
            updated_files.append((file_path, changes))
            print(f"\n✓ Updated: {file_path.relative_to(root_dir)}")
            
            if changes['imports']:
                print("  Imports:")
                for imp in changes['imports']:
                    print(f"    - {imp}")
            
            if changes['classes']:
                print("  Classes:")
                for cls in changes['classes']:
                    print(f"    - {cls}")
            
            if changes['methods']:
                print("  Methods:")
                for method in changes['methods']:
                    print(f"    - {method}")
            
            if changes['warnings']:
                print("  ⚠️  Warnings:")
                for warning in changes['warnings']:
                    print(f"    - {warning}")
                    all_warnings.append((file_path, warning))
    
    print(f"\n{'='*60}")
    print(f"SUMMARY")
    print(f"{'='*60}")
    print(f"Total files scanned: {len(python_files)}")
    print(f"Files updated: {len(updated_files)}")
    
    if all_warnings:
        print(f"\n⚠️  IMPORTANT WARNINGS ({len(all_warnings)}):")
        print("These files may need manual review due to method signature changes:")
        for file_path, warning in all_warnings:
            print(f"\n  {file_path.relative_to(root_dir)}:")
            print(f"    {warning}")
        
        print("\n📝 MANUAL REVIEW NEEDED:")
        print("The following method signatures have changed:")
        for method, info in METHOD_SIGNATURE_CHANGES.items():
            print(f"\n  {method}():")
            print(f"    {info['note']}")
            print(f"    Example: {info['example']}")


if __name__ == "__main__":
    main()