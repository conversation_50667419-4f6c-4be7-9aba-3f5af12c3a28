#!/usr/bin/env python3
"""
Merge attorney law firm lookup with filing details to create comprehensive JSON.
"""

import json
from pathlib import Path

def merge_attorney_data():
    """Merge law firm lookup with attorney filings data."""
    
    # Load the law firm lookup
    with open('test_files/attorney_law_firm_lookup.json', 'r') as f:
        law_firm_data = json.load(f)
    
    # Load the attorney filings
    with open('test_files/attorney_filings.json', 'r') as f:
        filings_data = json.load(f)
    
    # Create new comprehensive data structure
    comprehensive_data = {
        "attorneys": {},
        "metadata": {
            "created_date": "2025-01-06",
            "total_attorneys": 77,
            "total_filings": 0,
            "description": "Comprehensive attorney data with law firms and all filing details"
        }
    }
    
    # Process each attorney
    for attorney_name, filing_info in filings_data.items():
        # Get law firm from lookup
        law_firm = "Unknown"
        if attorney_name in law_firm_data["attorneys"]:
            law_firm = law_firm_data["attorneys"][attorney_name]["law_firm"]
        
        # Structure the attorney data
        comprehensive_data["attorneys"][attorney_name] = {
            "law_firm": law_firm,
            "filing_count": filing_info["filing_count"],
            "filings": []
        }
        
        # Add all filing details
        for filing in filing_info["filings"]:
            # Clean up versus field
            versus = filing["versus"]
            # Remove Charleston - MDL 2873 Division
            versus = versus.replace("Charleston - MDL 2873 Division", "").strip()
            # Convert to title case
            versus = versus.title()
            
            comprehensive_data["attorneys"][attorney_name]["filings"].append({
                "date": filing["date"],
                "doc_num": filing["doc_num"],
                "docket_num": filing["docket_num"],
                "versus": versus
            })
        
        # Update total filings count
        comprehensive_data["metadata"]["total_filings"] += filing_info["filing_count"]
    
    # Save the comprehensive data
    output_file = Path("test_files/attorney_comprehensive_data.json")
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(comprehensive_data, f, indent=2, ensure_ascii=False)
    
    print(f"Created comprehensive attorney data file: {output_file}")
    print(f"Total attorneys: {len(comprehensive_data['attorneys'])}")
    print(f"Total filings: {comprehensive_data['metadata']['total_filings']}")
    
    # Show sample output
    print("\nSample entry (first attorney with law firm):")
    for name, data in comprehensive_data["attorneys"].items():
        if data["law_firm"] != "Unknown" and data["law_firm"] != "":
            print(f"\n{name}:")
            print(f"  Law Firm: {data['law_firm']}")
            print(f"  Filing Count: {data['filing_count']}")
            print(f"  First 3 filings:")
            for i, filing in enumerate(data['filings'][:3]):
                print(f"    {i+1}. {filing['date']} - Doc #{filing['doc_num']} - {filing['docket_num']}")
                print(f"       {filing['versus']}")
            break

if __name__ == "__main__":
    merge_attorney_data()