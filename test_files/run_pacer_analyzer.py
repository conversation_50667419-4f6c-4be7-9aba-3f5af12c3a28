#!/usr/bin/env python3
"""Simple script to run pacer_local_analyzer with option 1 (Analyze Pacer Table)"""

import sys
import os

# Add project root to path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

from scripts.run.processing_helpers.pacer_local_analyzer import PacerLocalAnalyzer
from rich.console import Console

if __name__ == "__main__":
    console = Console()
    
    console.print("[bold green]Initializing Pacer Local Analyzer...[/bold green]")
    analyzer = PacerLocalAnalyzer(max_workers=os.cpu_count() or 4)
    
    # Run option 1 directly without interactive loop
    console.print("\n[bold cyan]Running option 1: Analyze Pacer Table (Local DynamoDB)[/bold cyan]\n")
    analyzer.load_pacer_data()