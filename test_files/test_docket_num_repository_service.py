#!/usr/bin/env python3
"""
Comprehensive tests for repository and service layer methods that use docket_num.
These tests focus on database operations, queries, and service layer interactions.
"""

import unittest
import asyncio
import sys
from pathlib import Path
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from typing import Dict, List, Any, Optional

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Suppress import warnings for testing
import warnings
warnings.filterwarnings("ignore")


class TestPacerRepositoryDocketMethods(unittest.TestCase):
    """Test PacerRepository methods that handle docket_num operations."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.mock_storage = AsyncMock()
        self.test_records = [
            {
                'court_id': 'nysd',
                'docket_num': '1:25-cv-08592',
                'filing_date': '20250101',
                'versus': '<PERSON> v<PERSON>',
                'is_removal': True,
                'law_firm': 'Test Law Firm',
                'mdl_num': '12345',
            },
            {
                'court_id': 'cand',
                'docket_num': '3:24-sf-12345-JUDGE',
                'filing_date': '20240601',
                'versus': 'ABC Corp v. XYZ Inc',
                'is_removal': False,
            }
        ]
    
    def test_map_fields_to_dynamodb_docket_num(self):
        """Test that docket_num is correctly mapped to DynamoDB format."""
        try:
            from src.repositories.pacer_repository import PacerRepository
            
            repo = PacerRepository(self.mock_storage)
            
            for record in self.test_records:
                mapped = repo._map_fields_to_dynamodb(record)
                
                # Verify docket_num mapping
                self.assertIn('DocketNum', mapped, "docket_num should be mapped to DocketNum")
                self.assertEqual(mapped['DocketNum'], record['docket_num'], 
                    "Mapped DocketNum should match original docket_num")
                self.assertNotIn('docket_num', mapped, 
                    "Original docket_num field should not exist after mapping")
                
                # Verify other critical mappings
                self.assertIn('CourtId', mapped)
                self.assertIn('FilingDate', mapped)
                self.assertEqual(mapped['CourtId'], record['court_id'])
                self.assertEqual(mapped['FilingDate'], record['filing_date'])
                
        except ImportError as e:
            self.skipTest(f"Could not import PacerRepository: {e}")
    
    def test_map_fields_boolean_conversion(self):
        """Test that boolean fields are properly converted."""
        try:
            from src.repositories.pacer_repository import PacerRepository
            
            repo = PacerRepository(self.mock_storage)
            
            test_record = {
                'docket_num': '1:25-cv-08592',
                'is_removal': 'true',  # String that should become boolean
                'is_downloaded': 1,     # Number that should become boolean
                'html_only': False,     # Already boolean
            }
            
            mapped = repo._map_fields_to_dynamodb(test_record)
            
            # Check boolean conversions
            self.assertIsInstance(mapped['IsRemoval'], bool)
            self.assertTrue(mapped['IsRemoval'])
            
            self.assertIsInstance(mapped['IsDownloaded'], bool) 
            self.assertTrue(mapped['IsDownloaded'])
            
            self.assertIsInstance(mapped['HtmlOnly'], bool)
            self.assertFalse(mapped['HtmlOnly'])
            
        except ImportError as e:
            self.skipTest(f"Could not import PacerRepository: {e}")
    
    async def async_test_add_or_update_record(self):
        """Test adding/updating records with docket_num."""
        try:
            from src.repositories.pacer_repository import PacerRepository
            
            repo = PacerRepository(self.mock_storage)
            self.mock_storage.put_item.return_value = None
            
            test_record = {
                'docket_num': '1:25-cv-08592',
                'filing_date': '20250101',
                'court_id': 'nysd',
                'versus': 'Smith v. Jones',
            }
            
            # Test successful add
            result = await repo.add_or_update_record(test_record)
            self.assertTrue(result, "add_or_update_record should return True on success")
            
            # Verify put_item was called with mapped fields
            self.mock_storage.put_item.assert_called_once()
            call_args = self.mock_storage.put_item.call_args
            table_name, record_data = call_args[0]
            
            self.assertEqual(table_name, 'Pacer')
            self.assertIn('DocketNum', record_data)
            self.assertIn('FilingDate', record_data)
            self.assertEqual(record_data['DocketNum'], '1:25-cv-08592')
            
            # Test missing required fields
            self.mock_storage.reset_mock()
            incomplete_record = {'docket_num': '1:25-cv-08592'}  # Missing filing_date
            
            result = await repo.add_or_update_record(incomplete_record)
            self.assertFalse(result, "Should return False for incomplete record")
            
        except ImportError as e:
            self.skipTest(f"Could not import PacerRepository: {e}")
    
    async def async_test_query_by_court_and_docket(self):
        """Test querying by court_id and docket_num."""
        try:
            from src.repositories.pacer_repository import PacerRepository
            
            repo = PacerRepository(self.mock_storage)
            
            # Mock successful query response
            mock_response = [
                {'CourtId': 'nysd', 'DocketNum': '1:25-cv-08592', 'FilingDate': '20250101'}
            ]
            self.mock_storage.query.return_value = mock_response
            
            result = await repo.query_by_court_and_docket('nysd', '1:25-cv-08592')
            
            self.assertEqual(len(result), 1)
            self.assertEqual(result[0]['DocketNum'], '1:25-cv-08592')
            self.assertEqual(result[0]['CourtId'], 'nysd')
            
            # Verify query was called with correct parameters
            self.mock_storage.query.assert_called_once()
            call_args = self.mock_storage.query.call_args
            self.assertEqual(call_args[1]['index_name'], 'CourtId-DocketNum-index')
            
        except ImportError as e:
            self.skipTest(f"Could not import PacerRepository: {e}")
    
    async def async_test_check_docket_exists(self):
        """Test checking if docket exists."""
        try:
            from src.repositories.pacer_repository import PacerRepository
            
            repo = PacerRepository(self.mock_storage)
            
            # Mock query response for existing docket
            self.mock_storage.query.return_value = [
                {'CourtId': 'nysd', 'DocketNum': '1:25-cv-08592'}
            ]
            
            exists = await repo.check_docket_exists('nysd', '1:25-cv-08592')
            self.assertTrue(exists, "Should return True for existing docket")
            
            # Mock empty response for non-existing docket
            self.mock_storage.query.return_value = []
            
            exists = await repo.check_docket_exists('nysd', '9:99-cv-99999')
            self.assertFalse(exists, "Should return False for non-existing docket")
            
        except ImportError as e:
            self.skipTest(f"Could not import PacerRepository: {e}")
    
    async def async_test_get_by_filing_date_and_docket(self):
        """Test getting record by filing date and docket number."""
        try:
            from src.repositories.pacer_repository import PacerRepository
            
            repo = PacerRepository(self.mock_storage)
            
            # Mock get_item response
            mock_item = {
                'FilingDate': '20250101',
                'DocketNum': '1:25-cv-08592',
                'CourtId': 'nysd',
                'Versus': 'Smith v. Jones'
            }
            self.mock_storage.get_item.return_value = mock_item
            
            result = await repo.get_by_filing_date_and_docket('20250101', '1:25-cv-08592')
            
            self.assertIsNotNone(result)
            self.assertEqual(result['DocketNum'], '1:25-cv-08592')
            self.assertEqual(result['FilingDate'], '20250101')
            
            # Verify get_item was called with correct key
            self.mock_storage.get_item.assert_called_once_with(
                'Pacer',
                {'FilingDate': '20250101', 'DocketNum': '1:25-cv-08592'}
            )
            
        except ImportError as e:
            self.skipTest(f"Could not import PacerRepository: {e}")
    
    async def async_test_update_transfer_info(self):
        """Test updating transfer information with docket_num."""
        try:
            from src.repositories.pacer_repository import PacerRepository
            
            repo = PacerRepository(self.mock_storage)
            self.mock_storage.update_item.return_value = None
            
            transfer_info = {
                'TransferorCourtId': 'cacd',
                'TransferorDocketNum': '2:24-cv-12345',
                'TransferredIn': True,
            }
            
            result = await repo.update_transfer_info(
                '20250101', 
                '1:25-cv-08592', 
                transfer_info
            )
            
            self.assertTrue(result, "Should return True on successful update")
            
            # Verify update_item was called correctly
            self.mock_storage.update_item.assert_called_once()
            call_args = self.mock_storage.update_item.call_args[0]
            
            table_name, key, update_expr, expr_values = call_args
            self.assertEqual(table_name, 'Pacer')
            self.assertEqual(key['FilingDate'], '20250101')
            self.assertEqual(key['DocketNum'], '1:25-cv-08592')
            
        except ImportError as e:
            self.skipTest(f"Could not import PacerRepository: {e}")
    
    async def async_test_query_transfer_info_async(self):
        """Test querying transfer information."""
        try:
            from src.repositories.pacer_repository import PacerRepository
            
            repo = PacerRepository(self.mock_storage)
            
            # Mock response with S3 link
            mock_records = [
                {
                    'CourtId': 'nysd',
                    'DocketNum': '1:25-cv-08592',
                    'S3Link': 'https://example.com/document.pdf',
                    'FilingDate': '20250101'
                },
                {
                    'CourtId': 'nysd', 
                    'DocketNum': '1:25-cv-08592',
                    'FilingDate': '20250102'
                    # No S3Link
                }
            ]
            self.mock_storage.query.return_value = mock_records
            
            result = await repo.query_transfer_info_async('nysd', '1:25-cv-08592')
            
            self.assertIsNotNone(result)
            self.assertEqual(result['DocketNum'], '1:25-cv-08592')
            # Should return the record with S3Link
            self.assertIn('S3Link', result)
            self.assertTrue(result['S3Link'].endswith('.pdf'))
            
        except ImportError as e:
            self.skipTest(f"Could not import PacerRepository: {e}")
    
    def test_repository_methods(self):
        """Run all async repository tests."""
        async def run_all():
            await self.async_test_add_or_update_record()
            await self.async_test_query_by_court_and_docket() 
            await self.async_test_check_docket_exists()
            await self.async_test_get_by_filing_date_and_docket()
            await self.async_test_update_transfer_info()
            await self.async_test_query_transfer_info_async()
        
        try:
            asyncio.run(run_all())
        except Exception as e:
            self.skipTest(f"Async test execution failed: {e}")


class TestPacerQueryServiceDocketMethods(unittest.TestCase):
    """Test PacerQueryService methods that use docket_num."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.mock_repo = AsyncMock()
    
    async def async_test_query_service_methods(self):
        """Test service layer docket operations."""
        try:
            from src.services.pacer.query_service import PacerQueryService
            
            service = PacerQueryService(self.mock_repo)
            
            # Mock repository responses
            mock_transfer_info = {
                'CourtId': 'nysd',
                'DocketNum': '1:25-cv-08592',
                'S3Link': 'https://example.com/doc.pdf',
                'TransferorCourtId': 'cacd',
                'TransferorDocketNum': '2:24-cv-12345'
            }
            self.mock_repo.query_transfer_info_async.return_value = mock_transfer_info
            
            # Test query_transfer_info
            result = await service.query_transfer_info('nysd', '1:25-cv-08592')
            
            self.assertIsNotNone(result)
            self.assertEqual(result['DocketNum'], '1:25-cv-08592')
            self.assertEqual(result['CourtId'], 'nysd')
            
            # Verify repository method was called with correct parameters
            self.mock_repo.query_transfer_info_async.assert_called_once_with(
                'nysd', '1:25-cv-08592'
            )
            
            # Test with non-existent docket
            self.mock_repo.query_transfer_info_async.return_value = None
            result = await service.query_transfer_info('test', 'nonexistent')
            self.assertIsNone(result)
            
        except ImportError as e:
            self.skipTest(f"Could not import PacerQueryService: {e}")
    
    async def async_test_check_docket_status(self):
        """Test checking docket status through service layer."""
        try:
            from src.services.pacer.query_service import PacerQueryService
            
            service = PacerQueryService(self.mock_repo)
            
            # Mock docket exists
            self.mock_repo.check_docket_exists.return_value = True
            self.mock_repo.get_by_filing_date_and_docket.return_value = {
                'DocketNum': '1:25-cv-08592',
                'FilingDate': '20250101',
                'CourtId': 'nysd'
            }
            
            # Test check_docket_status if method exists
            if hasattr(service, 'check_docket_status'):
                result = await service.check_docket_status('nysd', '1:25-cv-08592', '20250101')
                # Result format depends on implementation
                self.assertIsNotNone(result)
            
        except ImportError as e:
            self.skipTest(f"Could not import PacerQueryService: {e}")
        except AttributeError:
            self.skipTest("check_docket_status method not found")
    
    def test_service_methods(self):
        """Run all async service tests."""
        async def run_all():
            await self.async_test_query_service_methods()
            await self.async_test_check_docket_status()
        
        try:
            asyncio.run(run_all())
        except Exception as e:
            self.skipTest(f"Service test execution failed: {e}")


class TestDocketNumDataValidation(unittest.TestCase):
    """Test data validation for docket_num fields."""
    
    def test_required_fields_validation(self):
        """Test validation of required fields including docket_num."""
        try:
            from src.data_transformer.docket_validator import DocketValidator
            
            # Test if validator exists and has validation methods
            if hasattr(DocketValidator, 'validate_required_fields'):
                validator = DocketValidator()
                
                # Valid record
                valid_record = {
                    'court_id': 'nysd',
                    'docket_num': '1:25-cv-08592',
                    'filing_date': '20250101'
                }
                
                result = validator.validate_required_fields(valid_record)
                # Exact format depends on implementation
                self.assertIsNotNone(result)
                
                # Invalid record - missing docket_num
                invalid_record = {
                    'court_id': 'nysd',
                    'filing_date': '20250101'
                }
                
                result = validator.validate_required_fields(invalid_record)
                # Should indicate validation failure
                self.assertIsNotNone(result)
                
        except ImportError:
            self.skipTest("Could not import DocketValidator")
        except AttributeError:
            self.skipTest("DocketValidator validation methods not found")
    
    def test_docket_format_validation(self):
        """Test docket number format validation."""
        # Generic format validation
        def validate_basic_docket_format(docket_num: str) -> bool:
            """Basic docket format validation."""
            if not docket_num or not isinstance(docket_num, str):
                return False
            
            # Must contain a colon
            if ':' not in docket_num:
                return False
            
            # Basic structure check
            parts = docket_num.split(':')
            if len(parts) != 2:
                return False
            
            # Check prefix is numeric
            if not parts[0].isdigit():
                return False
            
            # Check suffix has dash separators
            if '-' not in parts[1]:
                return False
            
            return True
        
        valid_dockets = [
            "1:25-cv-08592",
            "3:24-sf-12345",
            "9:99-zz-99999"
        ]
        
        invalid_dockets = [
            "",
            "invalid",
            "1-25-cv-08592",  # No colon
            "a:25-cv-08592",  # Non-numeric prefix
            "1:25cv08592",    # No dashes
        ]
        
        for docket in valid_dockets:
            self.assertTrue(validate_basic_docket_format(docket),
                f"Valid docket {docket} should pass basic validation")
        
        for docket in invalid_dockets:
            self.assertFalse(validate_basic_docket_format(docket),
                f"Invalid docket {docket} should fail basic validation")


def run_repository_service_tests():
    """Run all repository and service tests."""
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add all test classes
    test_classes = [
        TestPacerRepositoryDocketMethods,
        TestPacerQueryServiceDocketMethods,
        TestDocketNumDataValidation,
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Print summary
    print("\n" + "="*80)
    print(f"DOCKET_NUM REPOSITORY & SERVICE TEST SUMMARY")
    print("="*80)
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    print(f"Skipped: {len(getattr(result, 'skipped', []))}")
    
    if result.testsRun > 0:
        success_rate = ((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100)
        print(f"Success rate: {success_rate:.1f}%")
    
    if result.failures:
        print(f"\nFAILURES ({len(result.failures)}):")
        for test, traceback in result.failures:
            print(f"- {test}")
    
    if result.errors:
        print(f"\nERRORS ({len(result.errors)}):")
        for test, traceback in result.errors:
            print(f"- {test}")
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_repository_service_tests()
    sys.exit(0 if success else 1)