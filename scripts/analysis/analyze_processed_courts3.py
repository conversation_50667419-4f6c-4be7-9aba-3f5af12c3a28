#!/usr/bin/env python3
"""
Script to summarize statistics from PACER log files.
For each pacer_*.log file, extracts the last occurrence of statistics and displays a summary.
"""

import re
import json
from pathlib import Path
from collections import defaultdict
from typing import Dict, List, Optional, Tuple
from rich.console import Console
from rich.table import Table, Column
from rich import box

# Initialize console for rich output
console = Console()

def extract_statistics(log_content: str) -> Optional[Dict[str, int]]:
    """Extract statistics from log content."""
    # Look for the final statistics block in the log
    final_stats_match = re.search(
        r'SingleCourtReportTask: Task completed successfully\. Final counts: (\{.*?\})',
        log_content,
        re.DOTALL
    )
    
    if not final_stats_match:
        return None
    
    try:
        # Clean up the matched string to make it valid JSON
        stats_str = final_stats_match.group(1)
        # Replace single quotes with double quotes for JSON parsing
        stats_str = stats_str.replace("'", '"')
        # Remove any trailing commas before closing braces/brackets
        stats_str = re.sub(r',\s*}', '}', stats_str)
        stats_str = re.sub(r',\s*]', ']', stats_str)
        
        # Parse the JSON string
        return json.loads(stats_str)
    except json.JSONDecodeError as e:
        console.print(f"[red]Error parsing statistics: {e}")
        return None

def process_log_file(file_path: Path) -> Optional[Dict[str, int]]:
    """Process a single log file and return its statistics."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            return extract_statistics(content)
    except Exception as e:
        console.print(f"[red]Error processing {file_path}: {e}[/red]")
        return None

def format_number(value: int) -> str:
    """Format number with thousands separator."""
    return f"{value:,}"

def main():
    # Define paths
    logs_dir = Path("data/20250530/logs")
    
    # Find all pacer_*.log files
    log_files = sorted(logs_dir.glob("pacer_*.log"))
    
    if not log_files:
        console.print("[yellow]No pacer_*.log files found.[/yellow]")
        return
    
    # Initialize totals
    totals = defaultdict(int)
    all_stats = []
    
    # Define the columns we want to display
    columns = [
        'downloaded', 'successful', 'failed',
        'failed_extract_elements', 'failed_get_docket_num',
        'skipped_date', 'skipped_exists_db_gsi', 'skipped_exists_local_date_range',
        'skipped_historical_irrelevant', 'skipped_irrelevant_local_json_today',
        'skipped_downloaded_local_json_today', 'skipped_md_case',
        'skipped_nos_defendant_check', 'skipped_other_relevance',
        'skipped_report_data_irrelevant'
    ]
    
    # Process each log file
    for log_file in log_files:
        stats = process_log_file(log_file)
        if stats:
            all_stats.append((log_file.stem.replace('pacer_', ''), stats))
            for key, value in stats.items():
                totals[key] += value
    
    # Create and configure the table
    table = Table(
        title="PACER Log Statistics Summary",
        box=box.ROUNDED,
        show_header=True,
        header_style="bold magenta",
        title_justify="left"
    )
    
    # Add columns
    table.add_column("District", style="cyan", width=8)
    for col in columns:
        # Make the column headers more readable
        display_name = col.replace('_', ' ').title().replace('Db', 'DB').replace('Nos', 'NOS').replace('Mdc', 'MDC')
        table.add_column(display_name, justify="right", width=len(display_name) + 2)
    
    # Add rows for each district
    for district, stats in all_stats:
        row = [district]
        for col in columns:
            value = stats.get(col, 0)
            row.append(format_number(value) if value > 0 else "-")
        table.add_row(*row)
    
    # Add totals row
    if totals:
        totals_row = ["[bold]TOTALS[/bold]"]
        for col in columns:
            value = totals.get(col, 0)
            totals_row.append(f"[bold]{format_number(value)}[/bold]" if value > 0 else "[bold]-[/bold]")
        table.add_row(*totals_row, style="bold")
    
    # Print the table
    console.print(table)
    
    # Calculate summary metrics
    total_skipped = sum(v for k, v in totals.items() if k.startswith('skipped_'))
    total_processed = sum(totals.get(k, 0) for k in ['successful', 'failed'])
    success_rate = (totals.get('successful', 0) / total_processed * 100) if total_processed > 0 else 0
    
    # Print a summary of key metrics
    console.print("\n[bold]Summary:[/bold]")
    console.print(f"Total districts processed: {len(all_stats)}")
    console.print(f"Total cases processed: {format_number(total_processed)}")
    console.print(f"Total downloaded: {format_number(totals.get('downloaded', 0))}")
    console.print(f"Total successful: {format_number(totals.get('successful', 0))} ({success_rate:.1f}%)")
    console.print(f"Total failed: {format_number(totals.get('failed', 0))}")
    console.print(f"Total skipped (all reasons): {format_number(total_skipped)}")

if __name__ == "__main__":
    main()
