#!/usr/bin/env python3
import asyncio
import collections # For isinstance check in debug
from collections import Counter
import os
import sys
import time
from functools import partial
from typing import List, Dict, Optional, Tuple, Set, Any

import pandas as pd
from aiohttp import ClientError

# Add the project root to the path to make imports work
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "../..")))
from src.repositories.fb_archive_repository import FBArchiveRepository
from src.lib.config_adapter import load_config
from rich.console import Console
from rich.prompt import Prompt, Confirm  # Added Confirm
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TimeElapsedColumn  # Added TaskID
from datetime import datetime

console = Console()


class FBDuplicateScanner:
    def __init__(self, use_local: bool = True, max_workers: int = 10):
        config = load_config('01/01/70')
        self.repository = FBArchiveRepository()
        self.df = pd.DataFrame()
        self.image_hashes_df = pd.DataFrame()
        self.duplicate_fields: List[str] = []
        self.core_content_fields_for_consolidation: List[str] = []
        self.max_workers = max_workers
        self.duplicate_stats = {}
        self.modified_for_pageid_resolution_indices: Set[int] = set()

        self.image_hash_table_name = "FBImageHash"
        self.image_hash_pk = "PHash"
        self.image_hash_sk = "AdArchiveID"

        self.last_multi_archive_id_groups: List[Dict] = []
        self.last_core_content_fields_for_analysis: List[str] = []
        self.last_analysis_advertiser_name: Optional[str] = None
        self.last_analysis_base_df_indices: Optional[pd.Index] = None

        # Comprehensive list of columns to ALWAYS disregard in selection and display
        self.globally_disregarded_columns: Set[str] = {
            # User-specified application/data columns to ignore
            'Compny', 'Category', 'ImageText', 'Injuries', 'IsForbidden403',
            'Llm', 'PublisherPlatform', 'S3ExistsInitial', 'S3ImageKey',
            'AdCreativeidClean', 'AdArchiveidClean', 'Company', # Company is alias for Compny
            'LitigationName', 'Summary', 'Product', 'LitigationType',
            'ResizedImageUrl', 'OriginalImageUrl', 'VideoHdUrl', 'VideoSdUrl',
            'Resized Image Url', 'Original Image Url', # Handle spaces if they appear
            # Internal/helper columns from various analyses
            '_duplicate_key',
            'StartDate_std_analysis', 'EndDate_std_analysis', # From consolidation analysis
            'LastUpdated_std', 'StartDate_std', # From general date standardization
        }

    def _set_core_content_fields(self) -> bool:
        """
        Prompts user to set 'core content fields' for consolidation analysis,
        restricted to a predefined list and excluding globally disregarded columns.
        """
        if self.df.empty:
            console.print("[red]No data loaded. Please load data first (Option 1).")
            return False

        user_specified_attributes_for_analysis_scope = [  # These are the *potential* fields for this type of analysis
            'LinkDescription', 'PageName', 'CtaText', 'StartDate', 'Body', 'Title',
            'AdCreativeId', 'EndDateCaption', 'LawFirm', 'LinkUrl', 'AdArchiveID', 'PHash'
        ]

        # Fields actually available in DF, from the scope list, AND not globally disregarded
        selectable_fields_for_core_content = sorted([
            col for col in user_specified_attributes_for_analysis_scope
            if col in self.df.columns and col not in self.globally_disregarded_columns
        ])

        if not selectable_fields_for_core_content:
            console.print(
                "[red]No fields available for core content selection from the specified scope after excluding globally disregarded ones.[/red]")
            return False

        console.print("\n[bold]Define Core Content Fields for Consolidation Analysis[/bold]")
        console.print(
            "Core content fields define the ad's essential message. Variations in `AdArchiveID`, `StartDate`, `EndDate` are expected.")
        console.print(
            "[yellow]Note: Field selection is restricted to a predefined list relevant to this analysis, excluding globally disregarded columns.[/yellow]")

        console.print("\n[bold]Available Fields for Core Content (restricted list, excluding disregarded):[/bold]")
        if len(selectable_fields_for_core_content) > 15:
            cols_per_line = 4
            lines = []
            for i in range(0, len(selectable_fields_for_core_content), cols_per_line):
                lines.append("  |  ".join(selectable_fields_for_core_content[i:i + cols_per_line]))
            console.print("\n".join(lines))
        else:
            console.print("  " + ", ".join(selectable_fields_for_core_content))

        # Fields typically excluded from being "core content" BY DEFAULT (user can still add them if in selectable_fields_for_core_content)
        # This is for suggesting defaults, not for final validation (that's what selectable_fields_for_core_content is for)
        excluded_from_core_content_defaults = {
            'AdArchiveID', 'StartDate', 'EndDate', 'EndDateCaption', 'AdCreativeId',
            # These are already in self.globally_disregarded_columns, but good to be explicit if context changes
            'id', 'archive_id', 'LastUpdated', 'Date', 'DateStd', 'CreationDate',
            'Impressions', 'Spend', 'Currency', 'SK', 'PK', 'GSI1PK', 'GSI1SK',
        }

        # Suggested defaults are from selectable_fields_for_core_content, excluding the non-content ones
        suggested_defaults = [
            field for field in selectable_fields_for_core_content
            if field not in excluded_from_core_content_defaults
        ]
        common_content_order = ['Title', 'Body', 'PHash', 'LinkUrl', 'LinkDescription', 'CtaText', 'PageName',
                                'LawFirm']
        final_suggested_defaults = [
            ccf for ccf in common_content_order if ccf in suggested_defaults
            # Only if they made it to suggested_defaults
        ]
        for sgf in suggested_defaults:
            if sgf not in final_suggested_defaults:
                final_suggested_defaults.append(sgf)

        current_fields_str = ', '.join(
            self.core_content_fields_for_consolidation) if self.core_content_fields_for_consolidation else 'None'

        default_prompt_value = ",".join(self.core_content_fields_for_consolidation)
        if not self.core_content_fields_for_consolidation and final_suggested_defaults:
            default_prompt_value = ",".join(final_suggested_defaults)
        elif not self.core_content_fields_for_consolidation and not final_suggested_defaults:  # No current, no suggested
            default_prompt_value = ""

        console.print(
            f"\n[dim]Suggested defaults if current is empty: {', '.join(final_suggested_defaults) if final_suggested_defaults else 'None'}[/dim]")
        selections_str = Prompt.ask(
            f"Enter core content field NAMES (comma separated).\n"
            f"Current fields: [yellow]{current_fields_str}[/yellow]\n"
            f"Prefix with '-' to remove. Press Enter for current/suggested.",
            default=default_prompt_value,
            show_default=True
        ).strip()

        if not selections_str:
            if self.core_content_fields_for_consolidation:
                console.print(
                    f"[yellow]No changes entered. Core content fields remain: {', '.join(self.core_content_fields_for_consolidation)}[/yellow]")
            elif final_suggested_defaults:  # No current, used suggested defaults by hitting enter
                self.core_content_fields_for_consolidation = sorted(list(set(final_suggested_defaults)))
                console.print(
                    f"[green]Using suggested default core content fields: {', '.join(self.core_content_fields_for_consolidation)}[/green]")
            else:  # No current, no defaults, no input
                console.print("[yellow]No fields selected. Core content fields remain empty.[/yellow]")
                self.core_content_fields_for_consolidation = []
            return True

        candidate_fields_list = list(self.core_content_fields_for_consolidation)
        invalid_inputs = []
        warnings = []
        processed_an_operation = False
        raw_parts = selections_str.split(",")
        validation_set = set(selectable_fields_for_core_content)  # User can only pick from these

        for part in raw_parts:
            stripped_part = part.strip()
            if not stripped_part: continue

            processed_an_operation = True
            is_removal = stripped_part.startswith('-')
            field_name_from_input = stripped_part[1:].strip() if is_removal else stripped_part

            if not field_name_from_input:
                invalid_inputs.append(f"Malformed instruction: '{stripped_part}'")
                continue

            if is_removal:
                if field_name_from_input in candidate_fields_list:
                    candidate_fields_list = [f for f in candidate_fields_list if f != field_name_from_input]
                    console.print(f"[dim]Field '{field_name_from_input}' marked for removal.[/dim]")
                elif field_name_from_input in validation_set:
                    warnings.append(
                        f"Field '{field_name_from_input}' (for removal) is valid but was not in current selection.")
                else:
                    invalid_inputs.append(
                        f"Field '{field_name_from_input}' (for removal) is not an allowed field name.")
            else:
                if field_name_from_input in validation_set:
                    if field_name_from_input not in candidate_fields_list:
                        candidate_fields_list.append(field_name_from_input)
                        console.print(f"[dim]Field '{field_name_from_input}' marked for addition.[/dim]")
                    else:
                        warnings.append(f"Field '{field_name_from_input}' (for addition) is already in selection.")
                else:
                    invalid_inputs.append(
                        f"Field '{field_name_from_input}' (for addition) is not an allowed field name.")

        if not processed_an_operation and selections_str:
            console.print("[yellow]Input did not result in any valid field operations. Fields unchanged.[/yellow]")
            return True

        if invalid_inputs:
            console.print("[bold red]Error: Invalid field specifications found:[/bold red]")
            for invalid in invalid_inputs: console.print(f"  - {invalid}")
            console.print("[yellow]Core content fields were NOT updated.[/yellow]")
            return False

        if warnings:
            console.print("\n[bold yellow]Warnings during field selection:[/bold yellow]")
            for warn in warnings: console.print(f"  - {warn}")

        final_candidate_fields_list_ordered = list(dict.fromkeys(candidate_fields_list))

        if set(self.core_content_fields_for_consolidation) == set(final_candidate_fields_list_ordered) and \
                len(self.core_content_fields_for_consolidation) == len(final_candidate_fields_list_ordered) and \
                all(orig == new for orig, new in
                    zip(self.core_content_fields_for_consolidation, final_candidate_fields_list_ordered)):
            console.print(
                f"[dim]No effective change. Core content fields: {', '.join(final_candidate_fields_list_ordered) if final_candidate_fields_list_ordered else 'None'}[/dim]")
        else:
            self.core_content_fields_for_consolidation = sorted(final_candidate_fields_list_ordered)
            if self.core_content_fields_for_consolidation:
                console.print(
                    f"[green]Core content fields set to: {', '.join(self.core_content_fields_for_consolidation)}[/green]")
            else:
                console.print("[green]Core content fields list is now empty.[/green]")

        return True

    def set_duplicate_fields(self) -> None:
        """Prompt user to select fields for duplicate detection, allowing modification of existing selection."""
        if self.df.empty:
            console.print("[red]No data loaded yet. Please load data first.")
            return

        # Filter available columns by excluding globally disregarded ones
        selectable_columns = sorted([
            col for col in self.df.columns if col not in self.globally_disregarded_columns
        ])
        if not selectable_columns:
            console.print("[red]No fields available for selection after excluding globally disregarded columns.[/red]")
            return

        console.print("[bold]Available Fields for Duplicate Detection (excluding globally disregarded):[/bold]")
        if len(selectable_columns) > 15:
            cols_per_line = 4
            lines = []
            for i in range(0, len(selectable_columns), cols_per_line):
                lines.append("  |  ".join(selectable_columns[i:i + cols_per_line]))
            console.print("\n".join(lines))
        else:
            console.print("  " + ", ".join(selectable_columns))

        current_fields_str = ', '.join(self.duplicate_fields) if self.duplicate_fields else 'None'

        default_prompt_value = ",".join(self.duplicate_fields)
        if not default_prompt_value and len(selectable_columns) > 0:
            default_prompt_value = ",".join(selectable_columns[:min(3, len(selectable_columns))])

        selections_str = Prompt.ask(
            f"\nEnter field NAMES to check for duplicates (comma separated).\n"
            f"Current fields: [yellow]{current_fields_str}[/yellow]\n"
            f"Prefix with '-' to remove a field (e.g., -OldField,NewField).",
            default=default_prompt_value
        ).strip()

        if not selections_str:
            if self.duplicate_fields:
                console.print(
                    f"[yellow]No changes entered. Duplicate fields remain: {', '.join(self.duplicate_fields)}")
            else:
                console.print("[yellow]No fields entered and no prior fields set. Duplicate fields remain empty.")
            return

        candidate_fields_list = list(self.duplicate_fields)
        invalid_inputs = []
        warnings = []
        processed_an_operation = False
        raw_parts = selections_str.split(",")
        available_columns_set = set(selectable_columns)  # Use the filtered list for validation

        for part in raw_parts:
            stripped_part = part.strip()
            if not stripped_part: continue

            processed_an_operation = True
            is_removal = stripped_part.startswith('-')
            field_name = stripped_part[1:].strip() if is_removal else stripped_part

            if not field_name:
                invalid_inputs.append(f"Malformed instruction: '{stripped_part}'")
                continue

            if is_removal:
                if field_name in candidate_fields_list:
                    candidate_fields_list = [f for f in candidate_fields_list if f != field_name]
                    console.print(f"[dim]Field '{field_name}' removed from selection.[/dim]")
                elif field_name in available_columns_set:  # Check against selectable columns
                    warnings.append(
                        f"Field '{field_name}' (for removal) is valid but was not in the current selection.")
                else:  # Not in current and not even selectable
                    invalid_inputs.append(f"Field '{field_name}' (for removal) is not an available/allowed field name.")
            else:  # Field to add
                if field_name in available_columns_set:  # Check against selectable columns
                    if field_name not in candidate_fields_list:
                        candidate_fields_list.append(field_name)
                        console.print(f"[dim]Field '{field_name}' added to selection.[/dim]")
                    else:
                        warnings.append(f"Field '{field_name}' (for addition) is already in the selection.")
                else:
                    invalid_inputs.append(
                        f"Field '{field_name}' (for addition) is not an available/allowed field name.")

        if not processed_an_operation and selections_str:
            console.print(
                "[yellow]Input did not result in any valid field operations. Duplicate fields remain unchanged.")
            return

        if invalid_inputs:
            console.print("[bold red]Error: Invalid field specifications found:[/bold red]")
            for invalid in invalid_inputs: console.print(f"  - {invalid}")
            console.print("[yellow]Duplicate fields were NOT updated due to these errors.[/yellow]")
        else:
            if warnings:
                console.print("\n[bold yellow]Warnings during field selection:[/bold yellow]")
                for warn in warnings: console.print(f"  - {warn}")

            final_candidate_fields_list_ordered = list(dict.fromkeys(candidate_fields_list))
            if set(self.duplicate_fields) == set(final_candidate_fields_list_ordered) and \
                    len(self.duplicate_fields) == len(final_candidate_fields_list_ordered) and \
                    all(orig == new for orig, new in zip(self.duplicate_fields, final_candidate_fields_list_ordered)):
                console.print(
                    f"[dim]No effective change to duplicate fields. Still: {', '.join(final_candidate_fields_list_ordered) if final_candidate_fields_list_ordered else 'None'}[/dim]")
            else:
                self.duplicate_fields = final_candidate_fields_list_ordered  # Preserve user's order for additions
                if self.duplicate_fields:
                    console.print(f"[green]Duplicate fields set to: {', '.join(self.duplicate_fields)}[/green]")
                else:
                    console.print("[green]Duplicate fields list is now empty.[/green]")

    def _perform_consolidation_analysis_on_subset(self, df_subset: pd.DataFrame, content_fields: List[str],
                                                  advertiser_name: Optional[str] = None) -> None:
        """
        Performs the content consolidation analysis on a given DataFrame subset.
        This is the core logic extracted from analyze_content_duplicates_for_consolidation.
        It now populates self.last_multi_archive_id_groups and related attributes.
        """
        # Clear previous results before populating new ones for this specific call context
        self.last_multi_archive_id_groups = []
        self.last_core_content_fields_for_analysis = list(content_fields)  # Store a copy
        self.last_analysis_advertiser_name = advertiser_name
        self.last_analysis_base_df_indices = df_subset.index.copy()

        if df_subset.empty:
            if advertiser_name:
                console.print(f"[yellow]No data provided for advertiser '{advertiser_name}' to analyze.[/yellow]")
            else:
                console.print("[yellow]No data provided for analysis subset.[/yellow]")
            return

        if 'AdArchiveID' not in df_subset.columns:
            console.print(
                f"[red]Error: 'AdArchiveID' column missing in the provided data subset. Cannot perform analysis.[/red]")
            return

        analysis_title_prefix = f"Advertiser: {advertiser_name} - " if advertiser_name else ""
        console.rule(
            f"[bold]{analysis_title_prefix}Consolidation Analysis based on: {', '.join(content_fields)}[/bold]")

        analysis_df = df_subset.copy()
        for field in content_fields:
            if field not in analysis_df.columns:
                console.print(
                    f"[red]Error: Core content field '{field}' not found in DataFrame subset for {advertiser_name or 'global analysis'}. Aborting this part.[/red]")
                return
            analysis_df[field] = analysis_df[field].fillna("_NaN_").astype(str)

        if 'StartDate' in analysis_df.columns:
            analysis_df['StartDate_std_analysis'] = analysis_df['StartDate'].apply(
                self._standardize_date_for_comparison)
        if 'EndDate' in analysis_df.columns:
            analysis_df['EndDate_std_analysis'] = analysis_df['EndDate'].apply(self._standardize_date_for_comparison)

        console.print(
            f"Grouping {len(analysis_df)} records for {advertiser_name or 'global analysis'} by core content fields...")
        try:
            # Ensure groupby keys are handled as tuples even for a single column
            grouped_by_content = analysis_df.groupby(content_fields if len(content_fields) > 1 else content_fields[0],
                                                     dropna=False)
        except KeyError as e:
            console.print(
                f"[red]Error during grouping for {advertiser_name or 'global analysis'}: A field in {content_fields} caused a KeyError: {e}.[/red]")
            return

        num_core_content_groups = len(grouped_by_content)
        console.print(
            f"Found {num_core_content_groups} unique core content combinations for {advertiser_name or 'global analysis'}.")

        if num_core_content_groups == 0:
            console.print(
                f"[yellow]No content groups found for {advertiser_name or 'global analysis'}. Analysis cannot proceed for this subset.[/yellow]")
            return

        temp_multi_archive_id_groups = []  # Use a temporary list before assigning to self
        archive_id_counts_distribution = Counter()

        with Progress(
                SpinnerColumn(), TextColumn("[progress.description]{task.description}"), BarColumn(),
                TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
                TextColumn("({task.completed} of {task.total})"), TimeElapsedColumn(),
                console=console, transient=False
        ) as progress:
            task_description = f"Processing groups for {advertiser_name}" if advertiser_name else "Processing global groups"
            analysis_task = progress.add_task(task_description, total=num_core_content_groups)
            for content_key_tuple_raw, group_df in grouped_by_content:
                # Ensure content_key_tuple is always a tuple, even for single group_by field
                content_key_tuple = content_key_tuple_raw if isinstance(content_key_tuple_raw, tuple) else (
                content_key_tuple_raw,)

                unique_archive_ids = group_df['AdArchiveID'].nunique()
                archive_id_counts_distribution[unique_archive_ids] += 1

                if unique_archive_ids > 1:
                    min_start_date = group_df[
                        'StartDate_std_analysis'].min() if 'StartDate_std_analysis' in group_df and group_df[
                        'StartDate_std_analysis'].notna().any() else "N/A"
                    max_end_date = group_df['EndDate_std_analysis'].max() if 'EndDate_std_analysis' in group_df and \
                                                                             group_df[
                                                                                 'EndDate_std_analysis'].notna().any() else "N/A"

                    # content_key_tuple is already a tuple of actual values
                    content_key_display = " | ".join(
                        [str(val)[:30] + ('...' if len(str(val)) > 30 else '') for val in content_key_tuple]
                    )

                    temp_multi_archive_id_groups.append({
                        'content_key_tuple': content_key_tuple,  # Store the actual tuple
                        'content_key_display': content_key_display,
                        'num_unique_archive_ids': unique_archive_ids,
                        'total_records_in_group': len(group_df),
                        'min_start_date': min_start_date if pd.notna(min_start_date) else "N/A",
                        'max_end_date': max_end_date if pd.notna(max_end_date) else "N/A",
                        'sample_archive_ids': list(group_df['AdArchiveID'].unique()[:3])
                    })
                progress.update(analysis_task, advance=1)

        # Assign to self attribute after processing is complete
        self.last_multi_archive_id_groups = temp_multi_archive_id_groups

        console.print(f"\n[bold cyan]Summary for {analysis_title_prefix}Content Consolidation[/bold cyan]")
        console.print(f"Total FB Ad records analyzed in this subset: {len(analysis_df)}")
        console.print(f"Core content fields used: {', '.join(content_fields)}")
        console.print(f"Number of unique core content groups found: {num_core_content_groups}")

        groups_with_multiple_ids_count = sum(
            count for num_ids, count in archive_id_counts_distribution.items() if num_ids > 1)
        percentage_multiple = (
                    groups_with_multiple_ids_count / num_core_content_groups * 100) if num_core_content_groups > 0 else 0
        console.print(
            f"Core content groups associated with >1 AdArchiveID: [yellow]{groups_with_multiple_ids_count}[/yellow] ({percentage_multiple:.2f}%)")

        if groups_with_multiple_ids_count > 0:
            console.print("\n[bold]Distribution of AdArchiveID Counts per Core Content Group:[/bold]")
            dist_table = Table(show_header=True)
            dist_table.add_column("# AdArchiveIDs", style="cyan")
            dist_table.add_column("# Core Content Groups with this many IDs", style="green")
            for num_ids, count_of_groups in sorted(archive_id_counts_distribution.items()):
                if num_ids > 0:
                    dist_table.add_row(str(num_ids), str(count_of_groups))
            console.print(dist_table)

            console.print(
                f"\n[bold]Sample of Core Content Groups with Multiple AdArchiveIDs (Top 10 by #AdArchiveIDs):[/bold]")
            sorted_multi_groups = sorted(self.last_multi_archive_id_groups,
                                         key=lambda x: (x['num_unique_archive_ids'], x['total_records_in_group']),
                                         reverse=True)

            sample_table = Table(title=f"{analysis_title_prefix}Multi-AdArchiveID Content Groups")
            sample_table.add_column("Core Content (Sample)", style="magenta", overflow="fold", max_width=60)
            sample_table.add_column("#Unique IDs", style="cyan", justify="right")
            sample_table.add_column("Total Runs", style="green", justify="right")
            sample_table.add_column("Min Start", style="blue")
            sample_table.add_column("Max End", style="blue")
            sample_table.add_column("Sample AdArchiveIDs", style="yellow", overflow="fold")

            for item in sorted_multi_groups[:10]:
                sample_table.add_row(
                    item['content_key_display'],
                    str(item['num_unique_archive_ids']),
                    str(item['total_records_in_group']),
                    item['min_start_date'],
                    item['max_end_date'],
                    ', '.join(map(str, item['sample_archive_ids']))
                )
            console.print(sample_table)
            if len(sorted_multi_groups) > 10:
                console.print(
                    f"[dim]... and {len(sorted_multi_groups) - 10} more groups with multiple AdArchiveIDs for {advertiser_name or 'this analysis'}.[/dim]")
        else:
            console.print(
                f"[green]No core content groups found with multiple AdArchiveIDs for {advertiser_name or 'this analysis'} based on the selected fields.[/green]")

    def analyze_content_duplicates_for_consolidation(self) -> None:
        """
        Globally analyzes the DataFrame for ads with identical core content.
        Stores results for later detailed comparison.
        """
        if self.df.empty:
            console.print("[red]No data loaded. Please load data first (Option 1).")
            return

        console.print("[bold cyan]Global Content Consolidation Analysis[/bold cyan]")
        # Clear previous results before setting up for a new analysis run
        self.last_multi_archive_id_groups = []
        self.last_analysis_advertiser_name = None
        self.last_analysis_base_df_indices = None

        if not self.core_content_fields_for_consolidation:
            console.print("[yellow]Core content fields for consolidation are not set.[/yellow]")
            if not self._set_core_content_fields():
                console.print("[red]Failed to set core content fields. Aborting analysis.[/red]")
                return
            if not self.core_content_fields_for_consolidation:
                console.print("[red]Core content fields are required for this analysis. Aborting.[/red]")
                return
        else:
            if not Confirm.ask(
                    f"Use current core content fields for global analysis? Fields: [cyan]{', '.join(self.core_content_fields_for_consolidation)}[/cyan]",
                    default=True):
                if not self._set_core_content_fields():
                    console.print("[red]Failed to set core content fields. Aborting analysis.[/red]")
                    return
                if not self.core_content_fields_for_consolidation:
                    console.print("[red]Core content fields are required for this analysis. Aborting.[/red]")
                    return

        # Call the helper which will populate self.last_... attributes
        self._perform_consolidation_analysis_on_subset(self.df, self.core_content_fields_for_consolidation,
                                                       advertiser_name=None)
        console.rule()

    def analyze_content_duplicates_per_advertiser(self) -> None:
        """
        Analyzes content duplicates for consolidation, separately for each advertiser
        identified by PageName == LawFirm. Stores results of the *last processed advertiser*
        for detailed comparison.
        """
        if self.df.empty:
            console.print("[red]No data loaded. Please load data first (Option 1).")
            return

        if 'PageName' not in self.df.columns or 'LawFirm' not in self.df.columns:
            console.print(
                "[red]Error: 'PageName' and/or 'LawFirm' columns not found in DataFrame. Cannot perform per-advertiser analysis.[/red]")
            return

        console.rule("[bold cyan]Per-Advertiser Content Consolidation Analysis (PageName = LawFirm)[/bold cyan]")
        # Clear previous results before starting a new multi-advertiser run
        self.last_multi_archive_id_groups = []
        self.last_analysis_advertiser_name = None
        self.last_analysis_base_df_indices = None

        df_pn_str = self.df['PageName'].astype(str).str.strip()
        df_lf_str = self.df['LawFirm'].astype(str).str.strip()
        advertiser_mask = (df_pn_str == df_lf_str) & (df_pn_str != '') & self.df['PageName'].notna()
        df_filtered_advertisers = self.df.loc[advertiser_mask].copy()

        if df_filtered_advertisers.empty:
            console.print("[yellow]No advertisers found where PageName equals LawFirm (and are non-empty).[/yellow]")
            console.rule()
            return

        unique_advertiser_names = sorted(df_filtered_advertisers['PageName'].astype(str).str.strip().unique())
        console.print(f"Found {len(unique_advertiser_names)} unique advertisers where PageName = LawFirm.")

        # Set core content fields once for all advertisers in this run
        current_core_fields = list(self.core_content_fields_for_consolidation)  # Make a copy
        if not current_core_fields:
            console.print("\n[yellow]Core content fields for consolidation are not set.[/yellow]")
            if not self._set_core_content_fields():  # This updates self.core_content_fields_for_consolidation
                console.print("[red]Failed to set core content fields. Aborting per-advertiser analysis.[/red]")
                return
            current_core_fields = list(self.core_content_fields_for_consolidation)
            if not current_core_fields:
                console.print("[red]Core content fields are required. Aborting per-advertiser analysis.[/red]")
                return
        else:
            if not Confirm.ask(
                    f"Use current core content fields for per-advertiser analysis? Fields: [cyan]{', '.join(current_core_fields)}[/cyan]",
                    default=True):
                if not self._set_core_content_fields():
                    console.print("[red]Failed to set core content fields. Aborting per-advertiser analysis.[/red]")
                    return
                current_core_fields = list(self.core_content_fields_for_consolidation)
                if not current_core_fields:
                    console.print("[red]Core content fields are required. Aborting per-advertiser analysis.[/red]")
                    return

        content_fields_for_this_analysis = [
            f for f in current_core_fields if f not in ['PageName', 'LawFirm']
        ]
        if len(content_fields_for_this_analysis) != len(current_core_fields):
            console.print(
                f"[dim]Note: 'PageName' and 'LawFirm' were excluded from core content fields for this specific per-advertiser analysis, using: {', '.join(content_fields_for_this_analysis)}[/dim]")

        if not content_fields_for_this_analysis:
            console.print(
                "[red]After excluding 'PageName' and 'LawFirm', no core content fields remain. Per-advertiser analysis cannot proceed meaningfully.[/red]")
            console.rule()
            return

        num_advertisers_to_process = len(unique_advertiser_names)
        processed_count = 0
        for advertiser_name_str in unique_advertiser_names:
            processed_count += 1
            console.print(
                f"\n[bold underline]Processing Advertiser ({processed_count}/{num_advertisers_to_process}): {advertiser_name_str}[/bold underline]")

            current_advertiser_df_subset = df_filtered_advertisers[
                df_filtered_advertisers['PageName'].astype(str).str.strip() == advertiser_name_str
                ].copy()

            if current_advertiser_df_subset.empty:
                console.print(
                    f"[yellow]No data for advertiser '{advertiser_name_str}' after final filtering. Skipping.[/yellow]")
                continue

            # _perform_consolidation_analysis_on_subset will update self.last_... for this advertiser
            self._perform_consolidation_analysis_on_subset(
                current_advertiser_df_subset,
                content_fields_for_this_analysis,
                advertiser_name=advertiser_name_str
            )
            # After each advertiser, self.last_... will hold their results.
            # The user can then use the "display details" option if they want to drill down
            # for the *just processed* advertiser.

            if processed_count < num_advertisers_to_process:
                if not Confirm.ask(f"Continue to the next advertiser?", default=True):
                    console.print("[yellow]Per-advertiser analysis stopped by user.[/yellow]")
                    break

        console.rule("[bold cyan]Per-Advertiser Content Consolidation Analysis Complete[/bold cyan]")

    def _execute_parallel_scan(self, worker_fn, description: str, total_segments_override: Optional[int] = None) -> \
    List[Dict]:
        """
        Helper function to execute a scan operation in parallel using a provided worker.
        The worker_fn must accept (segment: int, total_segments: int) as arguments.
        """
        start_time = time.time()
        total_segments = total_segments_override if total_segments_override else min(self.max_workers, 25)
        all_results = []

        with Progress(
                SpinnerColumn(),
                TextColumn(f"[bold green]{description}..."),
                BarColumn(),
                TextColumn("[bold]{task.completed}/{task.total} segments"),
                TimeElapsedColumn(),
                console=console
        ) as progress_bar:  # Renamed from progress to avoid conflict if passed as arg
            task = progress_bar.add_task(description.split("...")[0],
                                         total=total_segments)  # Use a shorter description for task if needed

            with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                futures = [executor.submit(worker_fn, i, total_segments) for i in range(total_segments)]
                for future in concurrent.futures.as_completed(futures):
                    try:
                        segment_results = future.result()
                        if segment_results:  # Ensure segment_results is not None
                            all_results.extend(segment_results)
                        progress_bar.update(task, advance=1)
                    except Exception as e:
                        console.print(f"[red]Error during segment processing for {description}: {e}")

        elapsed_time = time.time() - start_time
        console.print(
            f"[dim]Scan for {description} completed in {elapsed_time:.2f} seconds, found {len(all_results)} items.[/dim]")
        return all_results

    def _fetch_segment_from_dynamodb_worker(self, segment: int, total_segments: int,
                                            table_name_str: str,
                                            proj_expr: Optional[str] = None,
                                            expr_attr_names: Optional[Dict] = None) -> List[Dict]:
        """
        Worker function to fetch data from a specific segment of any DynamoDB table.
        Handles pagination within the segment scan.
        """
        if not hasattr(self.manager, 'dynamodb') or not self.manager.dynamodb:
            console.print(f"[red]DynamoDB resource not available on manager. Cannot scan table '{table_name_str}'.")
            return []

        try:
            table = self.manager.dynamodb.Table(table_name_str)
            scan_kwargs = {'Segment': segment, 'TotalSegments': total_segments}
            if proj_expr:
                scan_kwargs['ProjectionExpression'] = proj_expr
            if expr_attr_names:
                scan_kwargs['ExpressionAttributeNames'] = expr_attr_names

            items: List[Dict] = []
            response = table.scan(**scan_kwargs)
            items.extend(response.get('Items', []))

            while 'LastEvaluatedKey' in response:
                scan_kwargs['ExclusiveStartKey'] = response['LastEvaluatedKey']
                response = table.scan(**scan_kwargs)
                items.extend(response.get('Items', []))
            return items
        except Exception as e:
            console.print(f"[red]Error scanning segment {segment} for table {table_name_str}: {e}")
            return []

    async def _process_main_ad_segment(self, segment: int, total_segments: int) -> List[Dict]:
        """
        Process a segment of the main DynamoDB ad table.
        This was the original _process_segment method.
        """
        # Use async repository to scan table
        try:
            async with self.repository:
                all_items = await self.repository.scan_all()
                # Simulate segmentation by dividing items
                items_per_segment = len(all_items) // total_segments + 1
                start_idx = segment * items_per_segment
                end_idx = min((segment + 1) * items_per_segment, len(all_items))
                return all_items[start_idx:end_idx]
        except Exception as e:
            console.print(f"[red]Error in _process_main_ad_segment (segment {segment}/{total_segments}): {e}")
            return []

    @staticmethod
    def _standardize_date_for_comparison(date_val: Any) -> Optional[str]:
        """
        Attempts to standardize a date value to 'YYYYMMDD' string format.
        Handles common string formats, datetime objects, and basic numeric/Decimal epochs.
        Returns None if parsing fails or input is null or a string representation of NA.
        """
        if pd.isna(date_val):  # Handles np.nan, None, pd.NaT
            return None

        if isinstance(date_val, (datetime, pd.Timestamp)):
            return date_val.strftime('%Y%m%d')

        if isinstance(date_val, str):
            s_date = date_val.strip()
            # Explicitly check for common string representations of NA after stripping
            # Also handle empty string as missing.
            if s_date.lower() in ["nan", "none", "null", "na", "<na>", "nat", ""]:
                return None  # Treat these as missing, return None silently

            # Try YYYYMMDD first as it's the expected valid format
            try:
                return datetime.strptime(s_date, '%Y%m%d').strftime('%Y%m%d')
            except ValueError:
                pass
            # Try YYYY-MM-DD (potentially with time)
            try:
                return datetime.strptime(s_date.split('T')[0].split(' ')[0], '%Y-%m-%d').strftime('%Y%m%d')
            except ValueError:
                pass
            # Try MM/DD/YYYY
            try:
                return datetime.strptime(s_date, '%m/%d/%Y').strftime('%Y%m%d')
            except ValueError:
                pass
            # Try MM/DD/YY
            try:
                return datetime.strptime(s_date, '%m/%d/%y').strftime('%Y%m%d')
            except ValueError:
                pass

            # If none of the above string formats matched, then print the warning
            console.print(f"[dim]Could not parse date string: '{s_date}' (original was: '{date_val}')[/dim]", end=" ")
            return None

        if isinstance(date_val, (int, float, pd.Int64Dtype, pd.Float64Dtype)) or \
                (hasattr(pd, 'DecimalDtype') and isinstance(date_val, pd.DecimalDtype)):
            if 1000000000 <= abs(date_val) < 2000000000:
                try:
                    return datetime.fromtimestamp(int(date_val)).strftime('%Y%m%d')
                except (ValueError, TypeError, OSError):
                    pass
            if isinstance(date_val, (int, pd.Int64Dtype)) and 19700101 <= date_val <= 21001231:
                try:
                    return datetime.strptime(str(int(date_val)), '%Y%m%d').strftime('%Y%m%d')
                except ValueError:
                    pass

        if 'decimal' in str(type(date_val)).lower():
            try:
                num_val = int(date_val)
                if 1000000000 <= abs(num_val) < 2000000000:
                    return datetime.fromtimestamp(num_val).strftime('%Y%m%d')
                if 19700101 <= num_val <= 21001231:
                    return datetime.strptime(str(num_val), '%Y%m%d').strftime('%Y%m%d')
            except (ValueError, TypeError, OSError):
                pass

        # This print will now only be reached for types not handled above (e.g., complex objects, etc.)
        # or numeric values that didn't fit epoch/YYYYMMDD patterns.
        console.print(f"[dim]Unparseable date format for value '{str(date_val)[:50]}' (type: {type(date_val)})[/dim]",
                      end=" ")
        return None

    def analyze_missing_pageid_lawfirm_matches(self) -> None:
        """
        Analyzes items with no PageID/PageId against LawFirms data.
        Calculates % where LawFirm/PageName attributes match a Name in LawFirms,
        includes min/max StartDate and LastUpdated (excluding 19700101 for LastUpdated min/max),
        and counts items with LastUpdated = 19700101.
        """
        if self.df.empty:
            console.print("[red]FB Ad data not loaded. Please load data first (Option 1).")
            return

        pageid_col_present = 'PageId' in self.df.columns
        pageID_col_present = 'PageID' in self.df.columns
        start_date_col_present = 'StartDate' in self.df.columns
        last_updated_col_present = 'LastUpdated' in self.df.columns

        missing_fb_cols = []
        if not pageid_col_present: missing_fb_cols.append("'PageId'")
        if not pageID_col_present: missing_fb_cols.append("'PageID'")
        if not start_date_col_present: missing_fb_cols.append("'StartDate'")
        if not last_updated_col_present: missing_fb_cols.append("'LastUpdated'")

        if missing_fb_cols:
            console.print(f"[red]Error: Required column(s) {', '.join(missing_fb_cols)} not found in FB Ad data.")
            return

        df_fb_missing_page_ids = self.df[
            self.df['PageId'].isna() & self.df['PageID'].isna()
            ].copy()

        if df_fb_missing_page_ids.empty:
            console.print("[yellow]No items found in FB Ad data with both PageId and PageID missing.")
            return

        console.print(f"Found {len(df_fb_missing_page_ids)} FB Ad items with missing PageId and PageID.")

        console.print("Standardizing 'StartDate' and 'LastUpdated' values for analysis...")
        df_fb_missing_page_ids['StartDate_std'] = df_fb_missing_page_ids['StartDate'].apply(
            self._standardize_date_for_comparison
        )
        df_fb_missing_page_ids['LastUpdated_std'] = df_fb_missing_page_ids['LastUpdated'].apply(
            self._standardize_date_for_comparison
        )
        console.print()

        console.print("Fetching LawFirms data for analysis...")
        try:
            if _using_new_architecture:
                law_firms_manager = create_manager_replacement('LawFirmsManager', self.manager.config)
            else:
                law_firms_manager = LawFirmsManager(self.manager.config)
            law_firms_records = law_firms_manager.get_all_records(
                projection_expression="#nm",
                expression_attribute_names={'#nm': 'Name'}
            )
            if not law_firms_records:
                console.print("[red]Failed to load LawFirms data or no records found in LawFirms table.")
                return
            law_firms_df = pd.DataFrame(law_firms_records)
            console.print(f"Loaded {len(law_firms_df)} records from LawFirms table.")
        except Exception as e:
            console.print(f"[red]Error loading LawFirms data: {e}")
            return

        if 'Name' not in law_firms_df.columns:
            console.print("[red]Error: 'Name' column not found in LawFirms data after fetching. Check projection.")
            return

        unique_law_firm_names = set(
            law_firms_df['Name'].astype(str).str.lower().str.strip().unique()
        )
        unique_law_firm_names.discard('')
        if not unique_law_firm_names:
            console.print("[yellow]No valid names found in LawFirms data to match against.")
            return

        # Initialize counts and date lists
        lawfirm_match_overall_count = 0
        pagename_match_overall_count = 0
        both_lf_pn_match_count = 0
        neither_lf_pn_match_count = 0
        last_updated_is_1970_count = 0  # New counter

        dates_sd_lawfirm_match = []
        dates_sd_pagename_match = []
        dates_sd_both_match = []
        dates_sd_neither_match = []

        dates_lu_lawfirm_match = []
        dates_lu_pagename_match = []
        dates_lu_both_match = []
        dates_lu_neither_match = []

        has_fb_lawfirm_col = 'LawFirm' in df_fb_missing_page_ids.columns
        has_fb_pagename_col = 'PageName' in df_fb_missing_page_ids.columns

        for _, row in df_fb_missing_page_ids.iterrows():
            fb_lawfirm_value = ""
            if has_fb_lawfirm_col and pd.notna(row['LawFirm']):
                fb_lawfirm_value = str(row['LawFirm']).lower().strip()

            fb_pagename_value = ""
            if has_fb_pagename_col and pd.notna(row['PageName']):
                fb_pagename_value = str(row['PageName']).lower().strip()

            current_start_date_std = row['StartDate_std']
            current_last_updated_std = row['LastUpdated_std']

            # Count items where LastUpdated is '19700101'
            if current_last_updated_std == '19700101':
                last_updated_is_1970_count += 1

            # For Min/Max LU calculation, use the date only if it's valid and NOT '19700101'
            valid_lu_for_min_max = current_last_updated_std if current_last_updated_std and current_last_updated_std != '19700101' else None

            lawfirm_matches_this_item = fb_lawfirm_value != '' and fb_lawfirm_value in unique_law_firm_names
            pagename_matches_this_item = fb_pagename_value != '' and fb_pagename_value in unique_law_firm_names

            if lawfirm_matches_this_item:
                lawfirm_match_overall_count += 1
                if current_start_date_std: dates_sd_lawfirm_match.append(current_start_date_std)
                if valid_lu_for_min_max: dates_lu_lawfirm_match.append(valid_lu_for_min_max)

            if pagename_matches_this_item:
                pagename_match_overall_count += 1
                if current_start_date_std: dates_sd_pagename_match.append(current_start_date_std)
                if valid_lu_for_min_max: dates_lu_pagename_match.append(valid_lu_for_min_max)

            if lawfirm_matches_this_item and pagename_matches_this_item:
                both_lf_pn_match_count += 1
                if current_start_date_std: dates_sd_both_match.append(current_start_date_std)
                if valid_lu_for_min_max: dates_lu_both_match.append(valid_lu_for_min_max)

            if not lawfirm_matches_this_item and not pagename_matches_this_item:
                neither_lf_pn_match_count += 1
                if current_start_date_std: dates_sd_neither_match.append(current_start_date_std)
                if valid_lu_for_min_max: dates_lu_neither_match.append(valid_lu_for_min_max)

        total_to_analyze = len(df_fb_missing_page_ids)
        console.rule("[bold]Analysis: FB Ads with Missing PageId/PageID vs. LawFirms Table 'Name' Attribute")
        console.print(f"Total FB Ad items with missing PageId/PageID analyzed: {total_to_analyze}")
        console.print(
            f"FB Ad items (among analyzed) with LastUpdated = '19700101': [yellow]{last_updated_is_1970_count}[/yellow]")

        results_table = Table(title="Match Statistics with LawFirms 'Name'")
        results_table.add_column("Category", style="cyan", overflow="fold")
        results_table.add_column("Count", style="magenta", justify="right")
        results_table.add_column("Percentage", style="green", justify="right")
        results_table.add_column("Min StartDate", style="blue", justify="center")
        results_table.add_column("Max StartDate", style="blue", justify="center")
        results_table.add_column("Min LastUpdated", style="purple", justify="center")
        results_table.add_column("Max LastUpdated", style="purple", justify="center")

        categories_data = [
            ("FB Ad 'LawFirm' attr matches a LawFirms 'Name'", lawfirm_match_overall_count, dates_sd_lawfirm_match,
             dates_lu_lawfirm_match),
            ("FB Ad 'PageName' attr matches a LawFirms 'Name'", pagename_match_overall_count, dates_sd_pagename_match,
             dates_lu_pagename_match),
            ("FB Ad 'LawFirm' AND 'PageName' BOTH match a LawFirms 'Name'", both_lf_pn_match_count, dates_sd_both_match,
             dates_lu_both_match),
            ("NEITHER FB Ad 'LawFirm' NOR 'PageName' match any LawFirms 'Name'", neither_lf_pn_match_count,
             dates_sd_neither_match, dates_lu_neither_match),
        ]

        for desc, count, start_date_list, last_updated_list_filtered in categories_data:
            percentage = (count / total_to_analyze) * 100 if total_to_analyze > 0 else 0.0
            min_sd_str = min(start_date_list) if start_date_list else "N/A"
            max_sd_str = max(start_date_list) if start_date_list else "N/A"
            min_lu_str = min(last_updated_list_filtered) if last_updated_list_filtered else "N/A"
            max_lu_str = max(last_updated_list_filtered) if last_updated_list_filtered else "N/A"
            results_table.add_row(desc, str(count), f"{percentage:.2f}%", min_sd_str, max_sd_str, min_lu_str,
                                  max_lu_str)

        console.print(results_table)
        console.print(f"[dim]Note: Matching is case-insensitive and ignores leading/trailing whitespace.")
        console.print(
            f"[dim]Note: Min/Max StartDate & LastUpdated based on successfully parsed/standardized dates ('YYYYMMDD' format).")
        console.print(f"[dim]Note: Min/Max LastUpdated calculation excludes values of '19700101'.")
        console.print(
            f"[dim]Note: LawFirmsManager used its default configuration (likely querying AWS 'LawFirms' table).")
        console.rule()

    def _select_and_display_content_group_details(self) -> None:
        """
        Allows the user to select a content group from the last consolidation analysis
        and displays detailed comparison of its records.
        """
        console.rule("[bold]Detailed Comparison of Ads in a Content Group[/bold]")
        if not self.last_multi_archive_id_groups:
            console.print(
                "[yellow]No content consolidation analysis has been run yet, or the last run found no groups with multiple AdArchiveIDs.[/yellow]")
            console.print(
                "[dim]Please run 'Analyze Content Duplicates (Global)' (Option 9) or '(Per Advertiser)' (Option 10) first.[/dim]")
            return

        console.print(
            f"Select a content group from the last analysis run for: [cyan]{self.last_analysis_advertiser_name or 'Global Data'}[/cyan]")
        console.print(
            f"Core fields used for that analysis: [cyan]{', '.join(self.last_core_content_fields_for_analysis)}[/cyan]")

        selection_table = Table(title="Select a Content Group")
        selection_table.add_column("#", style="magenta")
        selection_table.add_column("Core Content (Sample)", overflow="fold", max_width=70)
        selection_table.add_column("#Unique IDs", style="cyan")
        selection_table.add_column("Total Runs", style="green")

        for i, group_info in enumerate(self.last_multi_archive_id_groups):
            selection_table.add_row(
                str(i + 1),
                group_info['content_key_display'],
                str(group_info['num_unique_archive_ids']),
                str(group_info['total_records_in_group'])
            )
        console.print(selection_table)

        if not self.last_multi_archive_id_groups:  # Should be caught earlier, but defense
            console.print("[yellow]No groups available for selection.[/yellow]")
            return

        try:
            choice_num_str = Prompt.ask("Enter number of the group to inspect",
                                        choices=[str(i + 1) for i in range(len(self.last_multi_archive_id_groups))])
            selected_index = int(choice_num_str) - 1
            if not (0 <= selected_index < len(self.last_multi_archive_id_groups)):
                raise ValueError("Index out of bounds.")
        except ValueError:
            console.print("[red]Invalid selection.[/red]")
            return

        selected_group_info = self.last_multi_archive_id_groups[selected_index]
        content_key_tuple_to_match = selected_group_info['content_key_tuple']

        # Determine the base DataFrame for filtering
        if self.last_analysis_base_df_indices is not None:
            base_df_for_filtering = self.df.loc[self.last_analysis_base_df_indices].copy()
        else:  # Fallback to full self.df if indices weren't stored for some reason (should not happen)
            console.print(
                "[yellow]Warning: Base DataFrame indices from last analysis not found. Using full DataFrame. Results might be broader than expected.[/yellow]")
            base_df_for_filtering = self.df.copy()

        # Re-filter the base_df_for_filtering to get the exact records for this content group
        # Need to handle NaNs consistently for filtering
        filter_mask = pd.Series([True] * len(base_df_for_filtering), index=base_df_for_filtering.index)

        for i, field_name in enumerate(self.last_core_content_fields_for_analysis):
            value_to_match = content_key_tuple_to_match[i]

            # Apply fillna with "_NaN_" before comparison, just like in grouping
            # Ensure series is string type for comparison if value_to_match is "_NaN_" or string
            series_to_filter = base_df_for_filtering[field_name].fillna("_NaN_").astype(str)
            filter_mask &= (series_to_filter == str(value_to_match))

        records_to_compare = base_df_for_filtering[filter_mask]

        if records_to_compare.empty:
            console.print(
                "[red]Could not retrieve records for the selected content group. This might indicate an issue with re-filtering.[/red]")
            console.print(
                f"[dim]Attempted to match key: {content_key_tuple_to_match} on fields: {self.last_core_content_fields_for_analysis}[/dim]")
            return

        title_prefix = f"Advertiser: {self.last_analysis_advertiser_name} - " if self.last_analysis_advertiser_name else "Global - "
        self._display_record_comparison_table(records_to_compare, title_prefix=title_prefix)
        console.rule()

    def _display_record_comparison_table(self, records_df: pd.DataFrame, title_prefix: str = "") -> None:
        """Displays a focused comparison table for a set of records,
        showing only the AdArchiveID and columns with differing values."""
        if records_df.empty:
            console.print("[yellow]No records to compare.[/yellow]")
            return

        # Ensure DataFrame index is unique for reliable .iloc access
        if not records_df.index.is_unique:
            records_df = records_df.reset_index(drop=True)

        all_df_columns = records_df.columns.tolist()

        # Determine the primary key for display
        key_col = 'AdArchiveID'
        if key_col not in all_df_columns:
            # Fallback if AdArchiveID is somehow missing
            temp_records_df = records_df.reset_index()
            key_col = 'index'  # Use original index if AdArchiveID is not present
            # records_df needs to be the one with 'index' if we use it as key_col
            # However, this case should be rare if data loading is consistent
            # For safety, we ensure key_col is in the dataframe we iterate over.
            # If 'AdArchiveID' is missing, we use the df with 'index'.
            # This part is a bit complex, let's assume 'AdArchiveID' is usually present.
            # A simpler approach if AdArchiveID might be missing is to always reset_index and use it.
            # For now, let's prioritize AdArchiveID.
            if 'AdArchiveID' not in temp_records_df.columns:  # If it's truly not there
                records_df = temp_records_df.rename(columns={'index': 'OriginalRowIndex'})
                key_col = 'OriginalRowIndex'

        # Identify columns that have differing values, EXCLUDING disregarded ones and the key_col
        differing_display_cols = []
        if len(records_df) > 1:
            for col in all_df_columns:
                if col == key_col or col in self.globally_disregarded_columns:
                    continue
                # Use .astype(str).nunique() for robustness with mixed types / NaNs
                if records_df[col].astype(str).nunique(dropna=False) > 1:
                    differing_display_cols.append(col)

        # Sort for consistent column order
        differing_display_cols = sorted(differing_display_cols)

        table_title = f"{title_prefix}Detailed Ad Comparison ({len(records_df)} Records)"
        if differing_display_cols:
            table_title += " - Showing Only Differing Attributes"
        else:
            table_title += " - No Differing Attributes Found (besides AdArchiveID)"

        table = Table(title=table_title)
        table.add_column(key_col, style="bold cyan", overflow="fold")

        if differing_display_cols:
            console.print(
                f"[dim]The following attributes differ across these ads: {', '.join(differing_display_cols)}[/dim]")
            for col in differing_display_cols:
                table.add_column(col, overflow="fold", header_style="bold yellow")
        elif len(records_df) > 1:
            console.print(
                f"[green]All other comparable attributes are identical across these {len(records_df)} ads (based on selected core content).[/green]")
        elif len(records_df) == 1:
            console.print("[dim]Only one ad in this group. No comparison needed for other attributes.[/dim]")
            # Optionally, list its other attributes if desired, but the request is about differences.
            # For now, we'll just show the key_col if no differing_display_cols.

        for idx_df, row_series in records_df.iterrows():
            row_values_for_table = [str(row_series.get(key_col, ""))]
            for col in differing_display_cols:  # Only iterate over columns that are known to differ
                row_values_for_table.append(str(row_series.get(col, "")))
            table.add_row(*row_values_for_table)

        if not differing_display_cols and len(records_df) == 1:
            # If only one record and no differing columns (obviously), the table would be just AdArchiveID.
            # We can add a note or skip table print if it's not useful.
            # For now, it will print a table with one column (AdArchiveID) and one row.
            pass

        console.print(table)

    def _process_segment(self, segment: int, total_segments: int) -> List[Dict]:
        """Process a segment of the DynamoDB table in parallel"""
        return list(self.manager.scan_table(Segment=segment, TotalSegments=total_segments))

    def load_data(self) -> None:
        """Load data from FB Ad Archive and FBImageHash, then merge them."""
        console.rule("[bold cyan]Loading Data[/bold cyan]")

        # --- Load main FB Ad Archive data ---
        console.print("[bold]Step 1: Loading FB Ad Archive data...[/bold]")
        main_ad_data_rows = self._execute_parallel_scan(
            self._process_main_ad_segment,
            description="Scanning FB Ad Archive"
        )
        if main_ad_data_rows:
            self.df = pd.DataFrame(main_ad_data_rows)
            console.print(f"[green]Successfully loaded {len(self.df)} items from FB Ad Archive.[/green]")
        else:
            self.df = pd.DataFrame()
            console.print("[yellow]No items found or loaded from FB Ad Archive.[/yellow]")

        # --- Load FBImageHash data ---
        console.print(f"\n[bold]Step 2: Loading {self.image_hash_table_name} data...[/bold]")

        projection_expression = f"#{self.image_hash_pk.replace('#', '')}, #{self.image_hash_sk.replace('#', '')}"
        expression_attribute_names = {
            f"#{self.image_hash_pk.replace('#', '')}": self.image_hash_pk,
            f"#{self.image_hash_sk.replace('#', '')}": self.image_hash_sk
        }

        image_hash_segment_worker = partial(
            self._fetch_segment_from_dynamodb_worker,
            table_name_str=self.image_hash_table_name,
            proj_expr=projection_expression,
            expr_attr_names=expression_attribute_names
        )

        image_hash_rows = self._execute_parallel_scan(
            image_hash_segment_worker,
            description=f"Scanning {self.image_hash_table_name}"
        )

        if image_hash_rows:
            self.image_hashes_df = pd.DataFrame(image_hash_rows)
            # Ensure projected columns are present
            if self.image_hash_pk not in self.image_hashes_df.columns or \
                    self.image_hash_sk not in self.image_hashes_df.columns:
                console.print(
                    f"[red]Error: Projected columns '{self.image_hash_pk}' or '{self.image_hash_sk}' not found in {self.image_hash_table_name} data. Found: {self.image_hashes_df.columns.tolist()}")
                self.image_hashes_df = pd.DataFrame()  # Reset if data is not as expected
            else:
                console.print(
                    f"[green]Successfully loaded {len(self.image_hashes_df)} items from {self.image_hash_table_name}.[/green]")
        else:
            self.image_hashes_df = pd.DataFrame()
            console.print(f"[yellow]No items found or loaded from {self.image_hash_table_name}.[/yellow]")

        # --- Merge data ---
        if not self.df.empty and not self.image_hashes_df.empty:
            console.print(
                f"\n[bold]Step 3: Merging FB Ad Archive data with {self.image_hash_table_name} data...[/bold]")

            if 'AdArchiveID' not in self.df.columns:
                console.print(
                    f"[red]Error: 'AdArchiveID' column not found in the main ad data (self.df). Cannot merge with image hashes.")
            elif self.image_hash_sk not in self.image_hashes_df.columns:  # self.image_hash_sk is 'AdArchiveID'
                console.print(
                    f"[red]Error: Column '{self.image_hash_sk}' not found in {self.image_hash_table_name} data (self.image_hashes_df). Check projection and table structure.")
            else:
                original_df_cols = set(self.df.columns)
                # Merge on 'AdArchiveID'. self.image_hashes_df has self.image_hash_pk ('PHash') and self.image_hash_sk ('AdArchiveID').
                self.df = pd.merge(
                    self.df,
                    self.image_hashes_df[[self.image_hash_pk, self.image_hash_sk]],  # Select only relevant cols
                    left_on='AdArchiveID',  # Column in self.df
                    right_on=self.image_hash_sk,  # Column in self.image_hashes_df ('AdArchiveID')
                    how='left',
                    suffixes=('', '_imagehash')  # Suffix for any other potential (unexpected) overlapping columns
                )

                if self.image_hash_pk in self.df.columns:
                    console.print(
                        f"[green]Merge successful. Column '{self.image_hash_pk}' (from {self.image_hash_table_name}) is now available in the main DataFrame.[/green]")
                    num_merged_with_phash = self.df[self.image_hash_pk].notna().sum()
                    console.print(
                        f"[dim]{num_merged_with_phash} out of {len(self.df)} ad items were matched with a '{self.image_hash_pk}'.[/dim]")
                else:
                    console.print(
                        f"[yellow]Warning: Expected column '{self.image_hash_pk}' not found in DataFrame after merge. Check merge logic and column names.")
                    new_cols = set(self.df.columns) - original_df_cols
                    if new_cols:
                        console.print(f"[dim]Columns added by merge: {new_cols}[/dim]")
                    else:
                        console.print(f"[dim]No new columns seem to have been added by the merge.[/dim]")
        elif self.df.empty and not self.image_hashes_df.empty:
            console.print(
                f"\n[yellow]Main ad data (self.df) is empty. No merge performed. Data from {self.image_hash_table_name} is in `self.image_hashes_df`.[/yellow]")
        elif not self.df.empty and self.image_hashes_df.empty:
            console.print(
                f"\n[yellow]Data from {self.image_hash_table_name} (self.image_hashes_df) is empty. No merge performed.[/yellow]")
        else:  # both empty
            console.print("\n[yellow]Both main ad data and image hash data are empty. No merge performed.")

        console.rule()

    def set_duplicate_fields(self) -> None:
        """Prompt user to select fields for duplicate detection, allowing modification of existing selection."""
        if self.df.empty:
            console.print("[red]No data loaded yet. Please load data first.")
            return

        console.print("[bold]Available Fields for Duplicate Detection:[/bold]")
        if len(self.df.columns) > 15:
            cols_per_line = 4
            lines = []
            for i in range(0, len(self.df.columns), cols_per_line):
                lines.append("  |  ".join(self.df.columns[i:i + cols_per_line]))
            console.print("\n".join(lines))
        else:
            console.print("  " + ", ".join(self.df.columns))

        current_fields_str = ', '.join(self.duplicate_fields) if self.duplicate_fields else 'None'

        # Determine default prompt value
        default_prompt_value = ",".join(self.duplicate_fields)  # Default to current selection
        if not default_prompt_value and len(self.df.columns) > 0:  # If current is empty, suggest first few
            default_prompt_value = ",".join(self.df.columns[:min(3, len(self.df.columns))])

        selections_str = Prompt.ask(
            f"\nEnter field NAMES to check for duplicates (comma separated).\n"
            f"Current fields: [yellow]{current_fields_str}[/yellow]\n"
            f"Prefix with '-' to remove a field (e.g., -OldField,NewField).",
            default=default_prompt_value
        ).strip()

        if not selections_str:
            if self.duplicate_fields:
                console.print(
                    f"[yellow]No changes entered. Duplicate fields remain: {', '.join(self.duplicate_fields)}")
            else:
                console.print("[yellow]No fields entered and no prior fields set. Duplicate fields remain empty.")
            return

        # Start with the current fields and modify this list
        candidate_fields_list = list(self.duplicate_fields)

        invalid_inputs = []
        warnings = []
        processed_an_operation = False

        raw_parts = selections_str.split(",")
        available_columns_set = set(self.df.columns)

        for part in raw_parts:
            stripped_part = part.strip()
            if not stripped_part:  # Skip empty parts like ",," or " ,"
                continue

            processed_an_operation = True
            is_removal = stripped_part.startswith('-')
            field_name = stripped_part[1:].strip() if is_removal else stripped_part

            if not field_name:  # Handles input like "-" or "-," or just "," if it wasn't skipped
                invalid_inputs.append(f"Malformed instruction: '{stripped_part}'")
                continue

            if is_removal:
                if field_name in candidate_fields_list:
                    candidate_fields_list = [f for f in candidate_fields_list if f != field_name]
                    console.print(f"[dim]Field '{field_name}' removed from selection.[/dim]")
                elif field_name in available_columns_set:
                    warnings.append(
                        f"Field '{field_name}' (for removal) is valid but was not in the current selection.")
                else:
                    invalid_inputs.append(f"Field '{field_name}' (for removal) is not an available field name.")
            else:  # Field to add
                if field_name in available_columns_set:
                    if field_name not in candidate_fields_list:
                        candidate_fields_list.append(field_name)
                        console.print(f"[dim]Field '{field_name}' added to selection.[/dim]")
                    else:
                        warnings.append(f"Field '{field_name}' (for addition) is already in the selection.")
                else:
                    invalid_inputs.append(f"Field '{field_name}' (for addition) is not an available field name.")

        if not processed_an_operation and selections_str:  # e.g. user entered only commas or spaces
            console.print(
                "[yellow]Input did not result in any valid field operations. Duplicate fields remain unchanged.")
            return

        if invalid_inputs:
            console.print("[bold red]Error: Invalid field specifications found:[/bold red]")
            for invalid in invalid_inputs:
                console.print(f"  - {invalid}")
            console.print("[yellow]Duplicate fields were NOT updated due to these errors.[/yellow]")
        else:
            if warnings:
                console.print("\n[bold yellow]Warnings during field selection:[/bold yellow]")
                for warn in warnings:
                    console.print(f"  - {warn}")

            # Check if there's an effective change
            if set(self.duplicate_fields) == set(candidate_fields_list) and \
                    len(self.duplicate_fields) == len(candidate_fields_list) and \
                    all(orig == new for orig, new in zip(self.duplicate_fields, candidate_fields_list)):  # Order check
                console.print(
                    f"[dim]No effective change to duplicate fields. Still: {', '.join(candidate_fields_list) if candidate_fields_list else 'None'}[/dim]")
            else:
                self.duplicate_fields = candidate_fields_list
                if self.duplicate_fields:
                    console.print(f"[green]Duplicate fields set to: {', '.join(self.duplicate_fields)}[/green]")
                else:
                    console.print("[green]Duplicate fields list is now empty.[/green]")

    def find_duplicates(self) -> pd.DataFrame:
        """Find duplicates based on current fields"""
        if not self.duplicate_fields:
            console.print("[red]No duplicate fields set. Please set fields first.")
            return pd.DataFrame()

        # Find duplicates
        duplicates_df = self.df[self.df.duplicated(subset=self.duplicate_fields, keep=False)]
        console.print(f"[yellow]Found {len(duplicates_df)} potential duplicates")

        # Calculate duplicate statistics
        # Use .copy() to avoid SettingWithCopyWarning if _calculate_duplicate_stats modifies it
        self._calculate_duplicate_stats(duplicates_df.copy())

        return duplicates_df

    def _calculate_duplicate_stats(self, duplicates: pd.DataFrame) -> None:
        """Calculate detailed statistics about duplicates"""
        if duplicates.empty:
            self.duplicate_stats = {}
            return

        if not self.duplicate_fields:
            console.print("[red]_calculate_duplicate_stats: No duplicate fields set. Cannot calculate stats.[/red]")
            self.duplicate_stats = {
                'total_duplicate_records': len(duplicates),
                'unique_duplicate_combinations': 0,
                'max_duplicates_of_single_item': 0,
                'avg_duplicates_per_item': 0,
                'duplicate_frequency_distribution': {},
                'most_common_duplicates': []
            }
            return

        try:
            # Create a temporary column for the composite key from duplicate fields
            duplicates['_duplicate_key'] = duplicates.apply(
                lambda row: tuple(str(row[field]) for field in self.duplicate_fields),
                axis=1
            )

            duplicate_combo_counts_series = duplicates['_duplicate_key'].value_counts()

            if duplicate_combo_counts_series.empty:
                self.duplicate_stats = {
                    'total_duplicate_records': len(duplicates),
                    'unique_duplicate_combinations': 0,
                    'max_duplicates_of_single_item': 0,
                    'avg_duplicates_per_item': 0,
                    'duplicate_frequency_distribution': {},
                    'most_common_duplicates': []
                }
                return

            duplicate_combo_counts_dict = duplicate_combo_counts_series.to_dict()

            # Ensure Counter is from collections (handled by import change)
            # and that duplicate_combo_counts_dict.values() is an iterable of hashable items (counts, typically ints)
            counts_of_the_counts = Counter(duplicate_combo_counts_dict.values())

            # --- Start of added diagnostic prints ---
            console.print(f"\n[bold blue]Debugging _calculate_duplicate_stats:[/bold blue]")
            console.print(f"  - Type of `counts_of_the_counts`: [cyan]{type(counts_of_the_counts)}[/cyan]")
            if 'collections' in sys.modules:  # Check if collections was successfully imported
                console.print(
                    f"  - Is `counts_of_the_counts` an instance of `collections.Counter`? [cyan]{isinstance(counts_of_the_counts, collections.Counter)}[/cyan]")
            else:
                console.print(
                    f"  - [yellow]Warning: `collections` module not found in sys.modules for isinstance check.[/yellow]")
            console.print(
                f"  - Value of `counts_of_the_counts` (first 5 items if many): [cyan]{str(list(counts_of_the_counts.items())[:5]) if counts_of_the_counts else '{}'}[/cyan]")
            console.print(f"  - Type of `dict` being used: [cyan]{type(dict)}[/cyan]")
            console.print(
                f"  - Is `dict` the built-in dict? [cyan]{dict == __builtins__.dict if hasattr(__builtins__, 'dict') else 'N/A'}[/cyan]")
            # --- End of added diagnostic prints ---

            most_common_dupes_list = sorted(
                duplicate_combo_counts_dict.items(),
                key=lambda item_tuple: item_tuple[1],
                reverse=True
            )[:10]

            self.duplicate_stats = {
                'total_duplicate_records': len(duplicates),
                'unique_duplicate_combinations': len(duplicate_combo_counts_dict),
                'max_duplicates_of_single_item': duplicate_combo_counts_series.max() if not duplicate_combo_counts_series.empty else 0,
                'avg_duplicates_per_item': duplicate_combo_counts_series.mean() if not duplicate_combo_counts_series.empty else 0,
                'duplicate_frequency_distribution': dict(counts_of_the_counts),  # Problematic line
                'most_common_duplicates': most_common_dupes_list
            }
        except Exception as e:
            console.print(f"[bold red]CRITICAL ERROR in _calculate_duplicate_stats: {e}[/bold red]")
            import traceback
            console.print(traceback.format_exc())
            self.duplicate_stats = {
                'total_duplicate_records': len(duplicates) if 'duplicates' in locals() and not duplicates.empty else 0,
                'unique_duplicate_combinations': 0,
                'max_duplicates_of_single_item': 0,
                'avg_duplicates_per_item': 0,
                'duplicate_frequency_distribution': {},
                'most_common_duplicates': []
            }

    def show_duplicates(self, duplicates: pd.DataFrame) -> None:
        """Display duplicates in a rich table and show statistics"""
        if duplicates.empty:
            console.print("[green]No duplicates found!")
            return

        # First show summary statistics
        self._show_duplicate_stats()

        # Then show sample of duplicate records
        table = Table(title=f"Sample Duplicate Items (showing up to 10 of {len(duplicates)} found)")

        # Add columns based on duplicate fields first, then other columns
        for field in self.duplicate_fields:
            table.add_column(field, style="bold magenta")

        # Add a few more columns that might be useful
        remaining_cols = [col for col in duplicates.columns if col not in self.duplicate_fields
                          and col != '_duplicate_key']
        for col in remaining_cols[:5]:  # Limit to 5 additional columns to avoid table being too wide
            table.add_column(col)

        # Add rows
        for _, row in duplicates.head(10).iterrows():
            row_values = [str(row[field]) for field in self.duplicate_fields]
            row_values.extend([str(row[col]) for col in remaining_cols[:5]])
            table.add_row(*row_values)

        console.print(table)

        if len(duplicates) > 10:
            console.print(f"[dim]Showing 10 of {len(duplicates)} duplicates")

    def _show_duplicate_stats(self) -> None:
        """Display statistics about duplicates"""
        if not self.duplicate_stats:
            return

        console.rule("[bold]Duplicate Statistics")

        stats = self.duplicate_stats
        console.print(f"[bold]Total Records with Duplicates:[/bold] {stats['total_duplicate_records']}")
        console.print(f"[bold]Unique Duplicate Combinations:[/bold] {stats['unique_duplicate_combinations']}")
        console.print(f"[bold]Maximum Duplicates of Single Item:[/bold] {stats['max_duplicates_of_single_item']}")
        console.print(f"[bold]Average Duplicates per Item:[/bold] {stats['avg_duplicates_per_item']:.2f}")

        # Show distribution of duplicate counts
        console.print("\n[bold]Distribution of Duplicate Counts:[/bold]")
        dist_table = Table(show_header=True)
        dist_table.add_column("Number of Duplicates", style="cyan")
        dist_table.add_column("Frequency", style="green")

        for count, freq in sorted(stats['duplicate_frequency_distribution'].items()):
            dist_table.add_row(str(count), str(freq))

        console.print(dist_table)

        # Show most common duplicates if available
        if stats['most_common_duplicates']:
            console.print("\n[bold]Most Frequently Duplicated Items:[/bold]")
            common_table = Table(show_header=True)
            common_table.add_column("Values", style="magenta")
            common_table.add_column("Count", style="green")

            for values, count in stats['most_common_duplicates']:
                # Format the values tuple for display
                formatted_values = " | ".join([str(v) for v in values])
                common_table.add_row(formatted_values, str(count))

            console.print(common_table)

        console.rule()

    def _display_sample_items_for_analysis(self, df_subset: pd.DataFrame, title: str, col1_name: str, col2_name: str,
                                           max_samples: int = 5) -> None:
        """Helper to display a sample of items for PageId/PageID analysis."""
        if df_subset.empty:
            return

        console.print(f"\n[bold yellow]Sample for '{title}' (up to {max_samples} of {len(df_subset)} items):")

        table = Table(title=f"Sample: {title}")
        table.add_column("Index", style="dim", overflow="fold")
        table.add_column(col1_name, style="cyan", overflow="fold")
        table.add_column(col2_name, style="magenta", overflow="fold")

        id_col_to_show = None
        potential_id_cols = ['id', 'archive_id', 'ad_archive_id']
        for c in potential_id_cols:
            if c in self.df.columns:
                id_col_to_show = c
                table.add_column(id_col_to_show, style="blue", overflow="fold")
                break

        for idx, row_data_from_subset in df_subset.head(max_samples).iterrows():
            # idx is the original index from self.df
            display_values = [
                str(idx),
                str(row_data_from_subset[col1_name]),
                str(row_data_from_subset[col2_name])
            ]
            if id_col_to_show:
                display_values.append(str(row_data_from_subset.get(id_col_to_show, "")))

            table.add_row(*display_values)
        console.print(table)

    def analyze_pageid_consistency(self) -> None:
        """Analyzes consistency between 'PageId' and 'PageID' fields based on detailed categories."""
        if self.df.empty:
            console.print("[red]No data loaded yet. Please load data first.")
            return

        col1 = 'PageId'
        col2 = 'PageID'

        missing_cols = []
        if col1 not in self.df.columns:
            missing_cols.append(f"'{col1}'")
        if col2 not in self.df.columns:
            missing_cols.append(f"'{col2}'")

        if missing_cols:
            console.print(f"[red]Required columns {', '.join(missing_cols)} not found in the dataset.")
            return

        console.rule(f"[bold]PageId vs PageID Consistency Analysis (Total items: {len(self.df)})")

        # Masks for presence/absence
        c1_notna = self.df[col1].notna()
        c2_notna = self.df[col2].notna()
        c1_isna = self.df[col1].isna()
        c2_isna = self.df[col2].isna()

        # Category S1: Both present and match
        # Category S2: Both present and mismatch
        both_present_mask = c1_notna & c2_notna
        df_both_present = self.df[both_present_mask]

        s1_rows = pd.DataFrame(columns=self.df.columns)
        s2_rows = pd.DataFrame(columns=self.df.columns)

        if not df_both_present.empty:
            # Perform string comparison only on the subset where both are present
            val1_str = df_both_present[col1].astype(str)
            val2_str = df_both_present[col2].astype(str)
            matches_on_both_present = val1_str == val2_str

            s1_rows = df_both_present[matches_on_both_present]
            s2_rows = df_both_present[~matches_on_both_present]

        s1_count = len(s1_rows)
        s2_count = len(s2_rows)

        # Category S3: col1 present, col2 is null
        s3_mask = c1_notna & c2_isna
        s3_rows = self.df[s3_mask]
        s3_count = len(s3_rows)

        # Category S4: col1 is null, col2 present
        s4_mask = c1_isna & c2_notna
        s4_rows = self.df[s4_mask]
        s4_count = len(s4_rows)

        # Category S5: Both null
        s5_mask = c1_isna & c2_isna
        # s5_rows = self.df[s5_mask] # Only count needed for reporting
        s5_count = s5_mask.sum()

        # --- Reporting based on user's categories ---

        # User Category 1: PageID and PageId match (S1)
        console.print(f"\n1. Items where '{col1}' and '{col2}' match:")
        console.print(f"   Count: [green]{s1_count}[/green]")

        # User phrase: "PageID do not match PageId match and the non-matching item is null."
        # This corresponds to S3 + S4 cases.
        console.print(f"\n2. Items where '{col1}' and '{col2}' differ due to one being null/NA:")
        console.print(f"   Total for this scenario (one valued, one null): [yellow]{s3_count + s4_count}[/yellow]")

        # User Category (breakdown of above): PageID exist and PageId is na, null (S4)
        console.print(f"   2a. Items where '{col2}' exists and '{col1}' is null/NA:")
        console.print(f"      Count: [cyan]{s4_count}[/cyan]")
        if s4_count > 0:
            self._display_sample_items_for_analysis(s4_rows, f"{col2} exists, {col1} is null", col1, col2)

        # User Category (breakdown of above): PageID is null, and PageId exists (S3)
        console.print(f"   2b. Items where '{col1}' exists and '{col2}' is null/NA:")
        console.print(f"      Count: [magenta]{s3_count}[/magenta]")
        if s3_count > 0:
            self._display_sample_items_for_analysis(s3_rows, f"{col1} exists, {col2} is null", col1, col2)

        # Additional useful categories for completeness:

        # Mismatch where both are present but values differ (S2)
        console.print(f"\n3. Items where '{col1}' and '{col2}' are both present but do not match:")
        console.print(f"   Count: [yellow]{s2_count}[/yellow]")
        if s2_count > 0:
            self._display_sample_items_for_analysis(s2_rows, f"Both present, values differ", col1, col2)

        # Both are null/NA (S5)
        console.print(f"\n4. Items where both '{col1}' and '{col2}' are null/NA:")
        console.print(f"   Count: [blue]{s5_count}[/blue]")

        total_reported = s1_count + s2_count + s3_count + s4_count + s5_count
        if total_reported != len(self.df):
            console.print(
                f"[bold red]Warning: Sum of categories ({total_reported}) does not match total items ({len(self.df)}). Check logic.[/bold red]")

        console.rule()

    @staticmethod
    def _convert_series_to_dynamodb_item(series: pd.Series) -> Dict:
        """Converts a pandas Series to a dictionary suitable for DynamoDB item.
        NaN/NaT values are converted to None."""
        item_dict = series.to_dict()
        # Convert NaN/NaT to None, handle other type conversions if necessary
        # Floats that are whole numbers (e.g., 1.0) might be better as int for DynamoDB if appropriate.
        # However, direct conversion to None for NaNs is the primary goal here.
        return {k: (None if pd.isna(v) else v) for k, v in item_dict.items()}

    def _update_item_worker(self, key_dict: Dict, changes_dict: Dict) -> Tuple[bool, Optional[str]]:
        """Worker function to update specific fields of an item in DynamoDB."""
        try:
            # Directly use the update_item method from the manager (inherited from base)
            # This method is designed for partial updates.
            success = self.manager.update_item(key_dict, changes_dict)
            if success:
                return True, None
            else:
                # update_item in base manager logs errors, so we provide a summary here.
                return False, f"Update failed for key {str(key_dict)}. Check manager logs."
        except Exception as e:
            # Catch any other exceptions during the update process
            return False, f"Exception updating item with key {str(key_dict)}: {str(e)}"

    def _direct_update_worker(self, key_for_dynamodb: Dict, pageID_to_set_str: str) -> Tuple[bool, Dict, Optional[str]]:
        """
        Worker for DIRECTLY updating PageID for a single item.
        Returns (success_flag, key_used, error_message_or_none)
        """
        update_expression = "SET PageID = :pid_val"
        expression_attribute_values = {":pid_val": pageID_to_set_str}

        try:
            # Using self.manager.table directly
            response = self.manager.table.update_item(
                Key=key_for_dynamodb,
                UpdateExpression=update_expression,
                ExpressionAttributeValues=expression_attribute_values,
                ReturnValues="UPDATED_NEW"
            )
            # Check if DynamoDB actually reported changed attributes
            if response.get('Attributes') and response['Attributes'].get('PageID') == pageID_to_set_str:
                return True, key_for_dynamodb, None  # Success
            else:
                # This could mean the value was already set, or some other subtle issue
                # For now, consider it a soft failure if attributes don't confirm the change.
                error_msg = f"DynamoDB response did not confirm PageID update. Response: {response}"
                # console.print(f"[dim]Warning for key {key_for_dynamodb}: {error_msg}[/dim]") # Optional: log this during run
                return False, key_for_dynamodb, error_msg
        except ClientError as ce:
            error_msg = f"ClientError: {ce.response.get('Error', {}).get('Message', str(ce))}"
            return False, key_for_dynamodb, error_msg
        except Exception as e:
            error_msg = f"Exception: {str(e)}"
            return False, key_for_dynamodb, error_msg

    def resolve_pageid_null_discrepancies(self) -> None:
        """
        ULTRA-SIMPLIFIED: If PageId has a value and PageID is NA, set self.df['PageID'] = self.df['PageId'].
        With added logging for counts.
        """
        if self.df.empty:
            console.print("[red]DataFrame is empty. Load data first.[/red]")
            return

        console.rule("[bold red]SIMPLIFIED PageId/PageID Null Discrepancy Resolution (DataFrame)[/bold red]")
        self.modified_for_pageid_resolution_indices.clear()

        col_pageid = 'PageId'
        col_pageBIGID = 'PageID'

        if not (col_pageid in self.df.columns and col_pageBIGID in self.df.columns):
            console.print(f"[red]Missing one or both columns: '{col_pageid}', '{col_pageBIGID}'. Aborting.[/red]")
            return

        # Initial mask: PageId has a value (is not NA), PageID is NA
        mask_to_update = self.df[col_pageid].notna() & self.df[col_pageBIGID].isna()
        indices_to_evaluate = self.df[mask_to_update].index  # Indices where PageId is notna and PageID isna

        if indices_to_evaluate.empty:
            console.print(
                "[yellow]RESOLVE: No rows found where PageId has a value and PageID is null to begin with.[/yellow]")
            console.rule()
            return

        console.print(
            f"[dim]RESOLVE: Found {len(indices_to_evaluate)} candidate rows (PageId notna, PageID isna).[/dim]")

        num_actually_updated_in_df = 0
        num_skipped_due_to_problematic_source = 0
        problematic_na_strings = ["nan", "none", "null", "na", "<na>", "nat", ""]
        DEBUG_TARGET_DF_INDEX_RESOLVE = 6  # For specific item logging

        for idx in indices_to_evaluate:
            source_pageid_val = self.df.loc[idx, col_pageid]
            str_source_pageid_val = str(source_pageid_val).strip()

            if str_source_pageid_val and str_source_pageid_val.lower() not in problematic_na_strings:
                self.df.loc[idx, col_pageBIGID] = str_source_pageid_val
                self.modified_for_pageid_resolution_indices.add(idx)
                num_actually_updated_in_df += 1
                # if idx == DEBUG_TARGET_DF_INDEX_RESOLVE:
                #     console.print(f"[SIMPLIFIED RESOLVE DEBUG] Index {idx}: PageID set to '{str_source_pageid_val}'")
            else:
                num_skipped_due_to_problematic_source += 1
                # if idx == DEBUG_TARGET_DF_INDEX_RESOLVE:
                #     console.print(f"[SIMPLIFIED RESOLVE DEBUG] Index {idx}: Source PageId '{source_pageid_val}' was problematic. PageID not set.")

        console.print(
            f"[green]RESOLVE: Updated {num_actually_updated_in_df} rows in DataFrame. '{col_pageBIGID}' set from '{col_pageid}'.[/green]")
        if num_skipped_due_to_problematic_source > 0:
            console.print(
                f"[yellow]RESOLVE: Skipped {num_skipped_due_to_problematic_source} rows because source PageId was empty or 'nan'-like after stripping.[/yellow]")

        console.print(
            f"[bold cyan]RESOLVE: Total items added to modified_for_pageid_resolution_indices: {len(self.modified_for_pageid_resolution_indices)}[/bold cyan]")

        if DEBUG_TARGET_DF_INDEX_RESOLVE in self.modified_for_pageid_resolution_indices:
            console.print(
                f"\n[bold magenta]>>> DEBUG POST-RESOLVE (Simplified) FOR DF INDEX {DEBUG_TARGET_DF_INDEX_RESOLVE} <<<[/bold magenta]")
            pk_name_debug = self.manager.pk_name or "PK_NAME_ERROR"
            sk_name_debug = self.manager.sk_name or "SK_NAME_ERROR"
            console.print(
                f"  DF[{DEBUG_TARGET_DF_INDEX_RESOLVE}, '{pk_name_debug}']: [cyan]{self.df.loc[DEBUG_TARGET_DF_INDEX_RESOLVE].get(pk_name_debug)}[/cyan] (Type: {type(self.df.loc[DEBUG_TARGET_DF_INDEX_RESOLVE].get(pk_name_debug)).__name__})")
            if sk_name_debug != "SK_NAME_ERROR" and sk_name_debug in self.df.columns:
                console.print(
                    f"  DF[{DEBUG_TARGET_DF_INDEX_RESOLVE}, '{sk_name_debug}']: [cyan]{self.df.loc[DEBUG_TARGET_DF_INDEX_RESOLVE].get(sk_name_debug)}[/cyan] (Type: {type(self.df.loc[DEBUG_TARGET_DF_INDEX_RESOLVE].get(sk_name_debug)).__name__})")
            console.print(
                f"  DF[{DEBUG_TARGET_DF_INDEX_RESOLVE}, 'PageId']: [cyan]{self.df.loc[DEBUG_TARGET_DF_INDEX_RESOLVE].get('PageId')}[/cyan] (Type: {type(self.df.loc[DEBUG_TARGET_DF_INDEX_RESOLVE].get('PageId')).__name__})")
            console.print(
                f"  DF[{DEBUG_TARGET_DF_INDEX_RESOLVE}, 'PageID']: [green]{self.df.loc[DEBUG_TARGET_DF_INDEX_RESOLVE].get('PageID')}[/green] (Type: {type(self.df.loc[DEBUG_TARGET_DF_INDEX_RESOLVE].get('PageID')).__name__})\n")

        console.rule()

    def commit_pageid_resolutions_to_dynamodb(self) -> None:
        """
        ULTRA-SIMPLIFIED & DIRECT DEBUG: Attempts to update items based on self.modified_for_pageid_resolution_indices.
        Uses direct self.manager.table.update_item call via _direct_update_worker.
        With added logging for counts.
        """
        if not self.modified_for_pageid_resolution_indices:
            console.print("[yellow]COMMIT: No changes staged (modified_indices is empty).[/yellow]")
            return

        if not self.manager.pk_name or not self.manager.sk_name:
            console.print(
                f"[red]COMMIT Error: Manager pk_name ('{self.manager.pk_name}') or sk_name ('{self.manager.sk_name}') not set. Cannot commit.[/red]")
            return

        console.rule("[bold green]Commit PageId/PageID (Direct Update Strategy)[/bold green]")

        indices_to_commit_from_resolve = list(self.modified_for_pageid_resolution_indices)
        console.print(
            f"[dim]COMMIT: Received {len(indices_to_commit_from_resolve)} indices from 'resolve' step to process for commit.[/dim]")

        items_for_direct_update: List[Tuple[Dict, str]] = []  # List of (key_dict, pageID_string_to_set)

        pk_name = self.manager.pk_name
        sk_name = self.manager.sk_name
        problematic_na_strings = ["nan", "none", "null", "na", "<na>", "nat", ""]

        skipped_due_key_issue = 0
        skipped_due_pageid_issue = 0
        DEBUG_TARGET_DF_INDEX_COMMIT = 6

        for df_idx in indices_to_commit_from_resolve:
            # Check if index is still valid in the current DataFrame
            if df_idx not in self.df.index:
                console.print(
                    f"[yellow]COMMIT WARNING: DF Index {df_idx} from modified_indices not found in current self.df.index. Skipping.[/yellow]")
                skipped_due_key_issue += 1
                continue

            row_series = self.df.loc[df_idx]
            key_dict = {}
            try:
                pk_val_from_df = row_series[pk_name]
                sk_val_from_df = row_series[sk_name]
                if pd.isna(pk_val_from_df) or pd.isna(sk_val_from_df):
                    skipped_due_key_issue += 1
                    if df_idx == DEBUG_TARGET_DF_INDEX_COMMIT: console.print(
                        f"[SIMPLIFIED COMMIT DEBUG] Index {df_idx}: PK or SK is NA. Skipping.")
                    continue

                key_dict = {
                    pk_name: str(pk_val_from_df).strip(),
                    sk_name: str(sk_val_from_df).strip()
                }
                if not key_dict[pk_name] or not key_dict[sk_name]:
                    skipped_due_key_issue += 1
                    if df_idx == DEBUG_TARGET_DF_INDEX_COMMIT: console.print(
                        f"[SIMPLIFIED COMMIT DEBUG] Index {df_idx}: PK or SK became empty. Skipping.")
                    continue
            except KeyError as e:
                skipped_due_key_issue += 1
                if df_idx == DEBUG_TARGET_DF_INDEX_COMMIT: console.print(
                    f"[SIMPLIFIED COMMIT DEBUG] Index {df_idx}: KeyError forming key '{e}'. Skipping.")
                console.print(
                    f"[red]KeyError forming key for DF index {df_idx}: {e}. Skipping.")  # Less verbose for general run
                continue
            except Exception as e_key:
                skipped_due_key_issue += 1
                if df_idx == DEBUG_TARGET_DF_INDEX_COMMIT: console.print(
                    f"[SIMPLIFIED COMMIT DEBUG] Index {df_idx}: Exception forming key: {e_key}. Skipping.")
                console.print(
                    f"[red]Unexpected error forming key for DF index {df_idx}: {e_key}. Skipping.")  # Less verbose
                continue

            pageID_value_from_df = row_series.get('PageID')
            pageID_to_set_str = None
            if pd.notna(pageID_value_from_df):
                temp_str = str(pageID_value_from_df).strip()
                if temp_str and temp_str.lower() not in problematic_na_strings:
                    pageID_to_set_str = temp_str

            if not pageID_to_set_str:
                skipped_due_pageid_issue += 1
                if df_idx == DEBUG_TARGET_DF_INDEX_COMMIT:
                    console.print(
                        f"[SIMPLIFIED COMMIT DEBUG] Index {df_idx}: No valid 'PageID' in DataFrame row to commit. Value was '{pageID_value_from_df}'. Skipping.")
                continue

            items_for_direct_update.append((key_dict, pageID_to_set_str))

            if df_idx == DEBUG_TARGET_DF_INDEX_COMMIT:  # Log the specific item if it makes it this far
                console.print(
                    f"\n[bold magenta]>>> PRE-SUBMIT DEBUG FOR DF INDEX {DEBUG_TARGET_DF_INDEX_COMMIT} (Commit Stage) <<<[/bold magenta]")
                console.print(f"  DataFrame self.df.loc[{df_idx}, '{pk_name}']: [cyan]{row_series.get(pk_name)}[/cyan]")
                console.print(f"  DataFrame self.df.loc[{df_idx}, '{sk_name}']: [cyan]{row_series.get(sk_name)}[/cyan]")
                console.print(
                    f"  DataFrame self.df.loc[{df_idx}, 'PageId'] (original source): [cyan]{row_series.get('PageId')}[/cyan]")
                console.print(
                    f"  DataFrame self.df.loc[{df_idx}, 'PageID'] (resolved value from DF): [green]{row_series.get('PageID')}[/green]")
                console.print(f"  Key to be sent to DDB: [yellow]{key_dict}[/yellow]")
                console.print(f"  PageID value to be set in DDB: [yellow]'{pageID_to_set_str}'[/yellow]\n")

        if skipped_due_key_issue > 0:
            console.print(
                f"[yellow]COMMIT: Skipped {skipped_due_key_issue} items due to key preparation issues.[/yellow]")
        if skipped_due_pageid_issue > 0:
            console.print(
                f"[yellow]COMMIT: Skipped {skipped_due_pageid_issue} items due to invalid/missing PageID value in DataFrame after resolve.[/yellow]")

        num_records_to_attempt = len(items_for_direct_update)
        if num_records_to_attempt == 0:
            console.print(
                "[yellow]COMMIT: No records prepared for DynamoDB update after all filtering and checks.[/yellow]")
            console.rule()
            return

        console.print(f"COMMIT: Attempting direct PageID update for {num_records_to_attempt} records in DynamoDB "
                      f"({'local' if self.manager.is_local else 'REMOTE'})...")

        successful_updates = 0
        failed_updates = 0
        failed_details_log: List[str] = []

        with Progress(
                SpinnerColumn(), TextColumn("[progress.description]{task.description}"), BarColumn(),
                TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
                TextColumn("({task.completed} of {task.total})"), TimeElapsedColumn(),
                console=console, transient=False
        ) as progress:
            task_id = progress.add_task("Updating DynamoDB (Direct)", total=num_records_to_attempt)

            with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                future_to_details = {
                    executor.submit(self._direct_update_worker, key_data, pageid_val_str_to_set): (
                        key_data, pageid_val_str_to_set)
                    for key_data, pageid_val_str_to_set in items_for_direct_update
                }

                for future in concurrent.futures.as_completed(future_to_details):
                    original_key, original_pageid_val = future_to_details[future]
                    try:
                        success, _, error_message = future.result()
                        if success:
                            successful_updates += 1
                        else:
                            failed_updates += 1
                            failed_details_log.append(
                                f"Key: {original_key}, Tried PageID: '{original_pageid_val}', Error: {error_message or 'Unknown'}")
                    except Exception as exc:
                        failed_updates += 1
                        failed_details_log.append(
                            f"Key: {original_key}, Tried PageID: '{original_pageid_val}', Exception: {exc}")
                    progress.update(task_id, advance=1)

        console.print(f"[green]COMMIT: Successfully updated PageID for {successful_updates} items in DynamoDB.")
        if failed_updates > 0:
            console.print(f"[red]COMMIT: Failed to update PageID for {failed_updates} items in DynamoDB.")
            if failed_details_log:
                console.print("[bold red]Error details for failed direct updates (first few examples):[/bold red]")
                for i, detail in enumerate(failed_details_log[:min(10, len(failed_details_log))]):
                    console.print(f"  - {detail}")
                if len(failed_details_log) > 10:
                    console.print(f"  ... and {len(failed_details_log) - 10} more errors.")

        if successful_updates > 0 and failed_updates == 0:
            self.modified_for_pageid_resolution_indices.clear()
            console.print("[dim]Modification tracking set cleared as all targeted direct updates succeeded.")
        elif successful_updates > 0 and failed_updates > 0:
            console.print("[yellow]Some direct updates succeeded, but others failed. Tracking set NOT cleared.")
        elif successful_updates == 0 and failed_updates > 0:
            console.print("[red]All attempted direct updates failed. Tracking set NOT cleared.")
        elif successful_updates == 0 and failed_updates == 0 and num_records_to_attempt > 0:
            # This case should ideally be caught by the earlier check if num_records_to_attempt == 0
            console.print(
                "[yellow]No updates were attempted, though records were prepared. Check logs for filtering issues.")

        console.rule()

    @staticmethod
    def update_1970_last_updated_with_end_date(self) -> None:
        """
        Identifies items where LastUpdated is '19700101'.
        Optionally exports these items to CSV.
        Then, updates LastUpdated to the item's EndDate in local DynamoDB and DataFrame
        if EndDate is valid and not '19700101'.
        Counts NaN EndDates.
        """
        if self.df.empty:
            console.print("[red]FB Ad data not loaded. Please load data first (Option 1).")
            return

        required_cols_base = ['LastUpdated', 'EndDate', self.manager.pk_name]
        if self.manager.sk_name:
            required_cols_base.append(self.manager.sk_name)

        missing_cols = [col for col in required_cols_base if col not in self.df.columns]
        if missing_cols:
            columns_formatted = ', '.join(f'"{c}"' for c in missing_cols)
            console.print(
                f"[red]Error: Required column(s) {columns_formatted} not found in FB Ad data."
            )
            return

        console.print("Analyzing DataFrame for items with LastUpdated = '19700101'...")

        df_with_std_dates = self.df.copy()
        df_with_std_dates['LastUpdated_std'] = df_with_std_dates['LastUpdated'].apply(
            self._standardize_date_for_comparison)
        df_with_std_dates['EndDate_std'] = df_with_std_dates['EndDate'].apply(self._standardize_date_for_comparison)
        console.print()

        target_df_with_std = df_with_std_dates[df_with_std_dates['LastUpdated_std'] == '19700101'].copy()

        if target_df_with_std.empty:
            console.print("[yellow]No items found with LastUpdated = '19700101'.")
            return

        num_targeted_items = len(target_df_with_std)
        console.print(f"Found {num_targeted_items} items with LastUpdated = '19700101'.")

        nan_end_date_count = target_df_with_std['EndDate_std'].isna().sum()
        console.print(
            f"Among these {num_targeted_items} items, [yellow]{nan_end_date_count}[/yellow] have an EndDate that is NaN or unparseable.")

        # Export option MOVED HERE - before update confirmation
        if Confirm.ask(
                f"\nDo you want to export these {num_targeted_items} items (where LastUpdated='19700101') to a CSV file?",
                default=True):  # Default to True for export
            original_rows_to_export = self.df.loc[target_df_with_std.index]
            try:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"fb_ads_last_updated_19700101_{timestamp}.csv"
                original_rows_to_export.to_csv(filename, index=False)
                console.print(f"[green]Exported {len(original_rows_to_export)} items to '{filename}'[/green]")
            except Exception as e:
                console.print(f"[red]Error exporting to CSV: {e}[/red]")

        items_to_update_dynamodb: List[Tuple[Dict, Dict]] = []
        df_indices_to_update: Dict[int, str] = {}
        skipped_count_invalid_enddate = 0

        pk_name = self.manager.pk_name
        sk_name = self.manager.sk_name

        for index, row in target_df_with_std.iterrows():
            new_last_updated_value = row['EndDate_std']

            if not new_last_updated_value or new_last_updated_value == '19700101':
                skipped_count_invalid_enddate += 1
                continue

            key_dict = {pk_name: row[pk_name]}
            if sk_name:
                key_dict[sk_name] = row[sk_name]

            changes_dict = {'LastUpdated': new_last_updated_value}
            items_to_update_dynamodb.append((key_dict, changes_dict))
            df_indices_to_update[index] = new_last_updated_value

        num_valid_to_update = len(items_to_update_dynamodb)
        if num_valid_to_update == 0:
            console.print(
                f"\n[yellow]No items eligible for update. All {num_targeted_items} items with LastUpdated='19700101' either had invalid/unparseable EndDates, EndDate was NaN, or EndDate was also '19700101'.")
            console.rule()
            return

        console.print(f"\nIdentified {num_valid_to_update} items eligible for update in local DynamoDB.")
        if skipped_count_invalid_enddate > 0:
            console.print(
                f"[yellow]Skipped {skipped_count_invalid_enddate} items from update due to invalid/placeholder/NaN EndDate.")

        if not Confirm.ask(
                f"Proceed with updating these {num_valid_to_update} items in local DynamoDB and in-memory DataFrame?",
                default=True):
            console.print("[yellow]Update operation cancelled by user.")
            console.rule()
            return

        successful_db_updates = 0
        failed_db_updates = 0

        console.rule("[bold]Updating Local DynamoDB")
        with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                BarColumn(),
                TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
                TextColumn("({task.completed} of {task.total})"),
                TimeElapsedColumn(),
                console=console,
                transient=False
        ) as progress:
            task_id = progress.add_task("Updating DynamoDB", total=num_valid_to_update)

            with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                future_to_key_and_index = {
                    executor.submit(self._update_item_worker, key_data, changes_data):
                        (str(key_data), df_idx)
                    for (key_data, changes_data), df_idx in zip(items_to_update_dynamodb, df_indices_to_update.keys())
                }

                successfully_updated_df_indices: List[Tuple[int, str]] = []

                for future in concurrent.futures.as_completed(future_to_key_and_index):
                    key_str, df_idx = future_to_key_and_index[future]
                    original_new_lu_value = df_indices_to_update[df_idx]
                    try:
                        success, error_message = future.result()
                        if success:
                            successful_db_updates += 1
                            successfully_updated_df_indices.append((df_idx, original_new_lu_value))
                        else:
                            failed_db_updates += 1
                            console.print(f"[red]Failed to update item {key_str} in DynamoDB: {error_message}")
                    except Exception as exc:
                        failed_db_updates += 1
                        console.print(f"[red]Exception updating item {key_str} in DynamoDB: {exc}")
                    progress.update(task_id, advance=1)

        console.print(f"[green]DynamoDB: Successfully updated {successful_db_updates} items.")
        if failed_db_updates > 0:
            console.print(f"[red]DynamoDB: Failed to update {failed_db_updates} items.")

        if successfully_updated_df_indices:
            console.print("Updating in-memory DataFrame...")
            updated_df_count = 0
            for df_idx, new_val in successfully_updated_df_indices:
                self.df.loc[df_idx, 'LastUpdated'] = new_val
                updated_df_count += 1
            console.print(f"[green]In-memory DataFrame: Updated {updated_df_count} rows.")

        console.rule()

    def interactive_loop(self) -> None:
        """Main interactive loop"""
        while True:
            console.rule("FB Duplicate Scanner")
            choices = {
                "1": "Load data (parallel scan)",
                "2": "Set duplicate fields (for exact duplicates)",
                "3": "Find exact duplicates and show statistics",
                "4": "Analyze PageId/PageID consistency",
                "5": "Resolve PageId/PageID null discrepancies (in DataFrame)",
                "6": "Commit PageId/PageID resolutions to Local DynamoDB",
                "7": "Analyze missing PageId/PageID items against LawFirms",
                "8": "Update '19700101' LastUpdated with EndDate (Local DB & DataFrame)",
                "9": "Analyze Content Duplicates for Consolidation (Global)",
                "10": "Analyze Content Duplicates per Advertiser (PageName=LawFirm)",
                "11": "Compare Ads within a Content Group (from last analysis)", # New option
                "12": "Exit"
            }

            for key, desc in choices.items():
                console.print(f"[cyan]{key}[/cyan]. {desc}")

            choice = Prompt.ask("Select an option", choices=list(choices.keys()), default="1")

            if choice == "1":
                self.load_data()
            elif choice == "2":
                self.set_duplicate_fields()
            elif choice == "3":
                duplicates = self.find_duplicates()
                if isinstance(duplicates, pd.DataFrame):
                    self.show_duplicates(duplicates)
                else:
                    console.print("[red]Error: find_duplicates did not return a DataFrame.")
                    self.show_duplicates(pd.DataFrame())
            elif choice == "4":
                self.analyze_pageid_consistency()
            elif choice == "5":
                self.resolve_pageid_null_discrepancies()
            elif choice == "6":
                self.commit_pageid_resolutions_to_dynamodb()
            elif choice == "7":
                self.analyze_missing_pageid_lawfirm_matches()
            elif choice == "8":
                self.update_1970_last_updated_with_end_date()
            elif choice == "9":
                self.analyze_content_duplicates_for_consolidation()
            elif choice == "10":
                self.analyze_content_duplicates_per_advertiser()
            elif choice == "11": # New handler
                self._select_and_display_content_group_details()
            elif choice == "12":
                break


if __name__ == "__main__":
    scanner = FBDuplicateScanner(use_local=True, max_workers=10)
    scanner.interactive_loop()

# Make the file executable
# This line should ideally be outside the Python script or run once after saving.
# os.chmod(__file__, 0o755)
# For the purpose of this response, I'll comment it out as it might cause issues
# if the script is not saved to a file system where chmod is applicable (e.g. certain online environments)
# If this script is run directly, __file__ will be defined.
if __name__ == "__main__" and os.path.exists(__file__):
    try:
        os.chmod(__file__, 0o755)
    except OSError as e:
        # This might fail if the user doesn't have permissions, or on some filesystems.
        # It's not critical for the script's main functionality.
        # console.print(f"[dim]Could not make script executable: {e}[/dim]")
        pass
