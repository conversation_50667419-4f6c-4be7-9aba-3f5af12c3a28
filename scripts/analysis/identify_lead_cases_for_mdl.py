#!/usr/bin/env python3
"""
Script to identify lead cases for MDL (Multidistrict Litigation) from docket data.

This script:
1. Iterates through each folder in the data directory containing dockets
2. For each folder, processes JSON files to extract MDL information
3. Aggregates data by MDL number, lead case, and counts the number of cases
4. Outputs a formatted summary
"""

import os
import json
import glob
from collections import defaultdict
from rich.console import Console
from rich.table import Table
from datetime import datetime
import re

def process_docket_files():
    """
    Process docket files to extract MDL information.

    Returns:
        dict: Aggregated data by MDL number and lead case
    """
    # Base directory for docket data
    base_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 'data')

    # Dictionary to store aggregated data
    mdl_data = defaultdict(lambda: defaultdict(int))

    # Find all date-based folders in the data directory
    date_folders = glob.glob(os.path.join(base_dir, '*'))

    for date_folder in date_folders:
        if not os.path.isdir(date_folder):
            continue

        # Find dockets folder within the date folder
        dockets_folder = os.path.join(date_folder, 'dockets')
        if not os.path.isdir(dockets_folder):
            continue

        # Process all JSON files in the dockets folder
        json_files = glob.glob(os.path.join(dockets_folder, '*.json'))

        for json_file in json_files:
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    # Fix common JSON errors
                    content = re.sub(r',\s*}', '}', content)
                    content = re.sub(r',\s*]', ']', content)
                    try:
                        data = json.loads(content)
                    except json.JSONDecodeError:
                        # Skip files with invalid JSON
                        print(f"Skipping invalid JSON file: {json_file}")
                        continue

                # Skip if no MDL number
                if 'mdl_num' not in data:
                    continue

                mdl_num = str(data['mdl_num'])

                # Skip if MDL number is "NA" or None
                if mdl_num == "NA" or mdl_num is None or mdl_num.lower() == "none":
                    continue

                # Check if there's a lead_case field
                if 'lead_case' in data:
                    lead_case = str(data['lead_case'])
                    if lead_case and lead_case.lower() != "none" and lead_case != "NA":
                        # Increment count for this MDL number and lead case
                        mdl_data[mdl_num][lead_case] += 1
                        continue

                # If no lead_case, check flags for potential lead case info
                if 'flags' in data:
                    flags = data['flags']
                    # Convert string to list if needed
                    if isinstance(flags, str):
                        flags = [flags]

                    # Look for lead case indicator in flags
                    for flag in flags:
                        if 'LEAD' in flag:
                            lead_case = data.get('docket_num', 'Unknown')
                            mdl_data[mdl_num][lead_case] += 1
                            break
                    else:
                        # No lead case found in flags, use 'Unknown'
                        mdl_data[mdl_num]['Unknown'] += 1
                else:
                    # No flags field, use 'Unknown'
                    mdl_data[mdl_num]['Unknown'] += 1

            except (json.JSONDecodeError, IOError) as e:
                print(f"Error processing {json_file}: {e}")

    return mdl_data

def display_results(mdl_data):
    """
    Display the aggregated MDL data in a formatted table.

    Args:
        mdl_data (dict): Aggregated data by MDL number and lead case
    """
    console = Console()

    # Create main table for MDL summary
    mdl_summary_table = Table(title="MDL Summary by Number")

    mdl_summary_table.add_column("MDL Number", style="cyan")
    mdl_summary_table.add_column("Total Cases", style="magenta", justify="right")
    mdl_summary_table.add_column("Lead Cases Found", style="green", justify="right")
    mdl_summary_table.add_column("Unknown Lead Cases", style="yellow", justify="right")

    # Create detailed table for lead cases
    lead_case_table = Table(title="MDL Lead Case Details")

    lead_case_table.add_column("MDL Number", style="cyan")
    lead_case_table.add_column("Lead Case", style="green")
    lead_case_table.add_column("Number of Cases", style="magenta", justify="right")

    # Process and sort the data
    mdl_summary = {}

    # Sort by MDL number numerically when possible
    def mdl_sort_key(mdl_num):
        try:
            return (0, int(mdl_num))
        except (ValueError, TypeError):
            return (1, str(mdl_num))

    for mdl_num in sorted(mdl_data.keys(), key=mdl_sort_key):
        lead_cases = mdl_data[mdl_num]

        # Calculate total cases for this MDL
        total_cases = sum(lead_cases.values())

        # Count known vs unknown lead cases
        unknown_cases = lead_cases.get('Unknown', 0)
        known_cases = total_cases - unknown_cases

        # Add to summary
        mdl_summary[mdl_num] = {
            'total': total_cases,
            'known_lead_cases': known_cases,
            'unknown_lead_cases': unknown_cases
        }

        # Add rows to the detailed table
        # Sort lead cases with known cases first, then by count (descending)
        sorted_lead_cases = sorted(
            lead_cases.items(),
            key=lambda x: (x[0] == 'Unknown', -x[1])
        )

        for lead_case, count in sorted_lead_cases:
            lead_case_table.add_row(mdl_num, lead_case, str(count))

    # Add rows to the summary table
    for mdl_num, summary in sorted(mdl_summary.items(), key=lambda x: mdl_sort_key(x[0])):
        mdl_summary_table.add_row(
            mdl_num,
            str(summary['total']),
            str(summary['known_lead_cases']),
            str(summary['unknown_lead_cases'])
        )

    # Print the tables
    console.print(mdl_summary_table)
    console.print("\n")
    console.print(lead_case_table)

def main():
    """Main function to run the script."""
    print(f"Processing docket files to identify MDL lead cases...")
    mdl_data = process_docket_files()

    if not mdl_data:
        print("No MDL data found in the docket files.")
        return

    display_results(mdl_data)

if __name__ == "__main__":
    main()
