import os
import json
import re
import argparse
from typing import List, <PERSON><PERSON>
from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich import print as rich_print


# Import the project root handling function
try:
    from scripts.utils import get_project_root
except ImportError:
    # Fall back to direct import if utils.py is not available
    try:
        from src.lib.config_adapter import PROJECT_ROOT
    except ImportError:
        # If can't import, try to load from environment or use a fallback
        try:
            from dotenv import load_dotenv
            load_dotenv()
            PROJECT_ROOT = os.getenv('PROJECT_ROOT', os.path.expanduser('/'))
        except ImportError:
            PROJECT_ROOT = os.path.expanduser('/')
else:
    PROJECT_ROOT = get_project_root()


class PlaintiffSearcher:
    def __init__(self, base_path: str = os.path.join(PROJECT_ROOT, "data")):
        self.base_path = base_path
        self.console = Console()

    @staticmethod
    def _clean_text(text: str) -> str:
        """Remove punctuation and convert to lowercase"""
        # Remove all punctuation and convert to lowercase
        return re.sub(r'[^\w\s]', '', text).lower().strip()

    def _get_date_directories(self) -> List[str]:
        """Get all YYYYMMDD directories"""
        return [d for d in os.listdir(self.base_path)
                if os.path.isdir(os.path.join(self.base_path, d))
                and d.isdigit() and len(d) == 8]

    def search_plaintiff(self, search_name: str) -> List[Tuple[str, str, str]]:
        """
        Search for a plaintiff name across all docket files.
        Returns: List of tuples (date_dir, filename, matched_name)
        """
        results = []
        date_dirs = self._get_date_directories()
        cleaned_search = self._clean_text(search_name)

        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=self.console
        ) as progress:
            task = progress.add_task("[cyan]Searching dockets...", total=len(date_dirs))

            for date_dir in sorted(date_dirs):
                progress.update(task, description=f"[cyan]Searching in {date_dir}...")
                docket_path = os.path.join(self.base_path, date_dir, "dockets")

                if not os.path.exists(docket_path):
                    continue

                for file in os.listdir(docket_path):
                    if not file.endswith('.json'):
                        continue

                    json_path = os.path.join(docket_path, file)
                    try:
                        with open(json_path, 'r', encoding='utf-8') as f:
                            data = json.load(f)

                        if 'plaintiff' in data and isinstance(data['plaintiff'], list):
                            for plaintiff in data['plaintiff']:
                                cleaned_plaintiff = self._clean_text(plaintiff)
                                if cleaned_search in cleaned_plaintiff:
                                    results.append((date_dir, file, plaintiff))

                    except Exception as e:
                        self.console.print(f"[red]Error processing {file}: {str(e)}[/red]")

                progress.advance(task)

        return results

    def display_results(self, results: List[Tuple[str, str, str]]) -> None:
        """Display search results with rich formatting"""
        if not results:
            self.console.print("\n[yellow]No matches found.[/yellow]")
            return

        self.console.print("\n[green]Found matches:[/green]")
        for date_dir, filename, matched_name in results:
            rich_print(f"""
[bold blue]{date_dir}[/bold blue]
  └─ [cyan]{filename}[/cyan]
     └─ [green]Matched:[/green] {matched_name}""")

        self.console.print(f"\n[bold green]Total matches found: {len(results)}[/bold green]")

def main():
    parser = argparse.ArgumentParser(description="Search for plaintiff names in docket files")
    parser.add_argument("--name", required=True, help="Name of plaintiff to search for")
    args = parser.parse_args()
    
    searcher = PlaintiffSearcher()
    rich_print(f"\n[bold]Searching for: {args.name}[/bold]")
    results = searcher.search_plaintiff(args.name)
    searcher.display_results(results)

if __name__ == "__main__":
    main()