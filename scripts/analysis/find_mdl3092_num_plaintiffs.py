#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to find MDL 3092 cases with multiple plaintiffs.

This script:
1. Iterates through date folders starting from '20250314'
2. Finds JSON files starting with 'ohnd' in the dockets subfolder
3. Checks if mdl_num is '3092'
4. Counts unique plaintiffs (case-insensitive)
5. Reports cases with more than one unique plaintiff
"""

import os
import json
import datetime
from rich.console import Console
from rich.table import Table
from pathlib import Path

# Initialize rich console
console = Console()

def get_date_folders(base_path: str, start_date: str) -> list:
    """Get sorted list of date folders >= start_date."""
    start_date_obj = datetime.datetime.strptime(start_date, '%Y%m%d')

    # Get all folders in the base path
    all_folders = [f for f in os.listdir(base_path) if os.path.isdir(os.path.join(base_path, f))]

    # Filter folders that match the date pattern and are >= start_date
    date_folders = []
    for folder in all_folders:
        # Check if the folder name matches the YYYYMMDD pattern
        if len(folder) >= 8 and folder[:8].isdigit():
            folder_date = folder[:8]  # Extract the date part
            try:
                folder_date_obj = datetime.datetime.strptime(folder_date, '%Y%m%d')
                if folder_date_obj >= start_date_obj:
                    date_folders.append(folder)
            except ValueError:
                # Skip folders that don't have a valid date format
                continue

    # Sort folders by date
    date_folders.sort()
    return date_folders

def analyze_docket_file(file_path):
    """Analyze a docket JSON file and return plaintiff info if it matches criteria."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)

        # Check if this is an MDL 3092 case
        if data.get('mdl_num') == '3092':
            # Get plaintiffs
            plaintiffs = data.get('plaintiff', []) # Changed from 'defendant' to 'plaintiff'

            # Create a set of unique plaintiffs (case-insensitive)
            unique_plaintiffs = set(p.lower() for p in plaintiffs) # Changed variable names
            num_plaintiffs = len(unique_plaintiffs) # Changed variable name

            # If there's more than one unique plaintiff, return the info
            if num_plaintiffs > 1: # Changed condition
                return {
                    'base_filename': os.path.basename(file_path).replace('.json', ''),
                    'num_plaintiffs': num_plaintiffs, # Changed key name
                    'plaintiffs': plaintiffs # Changed key name
                }
    except Exception as e:
        console.print(f"[bold red]Error processing {file_path}:[/bold red] {str(e)}")

    return None

def main():
    # Base path for data
    base_path = '/Users/<USER>/PycharmProjects/lexgenius/data'

    # Start date
    start_date = '20250314'

    # Create a table for results
    table = Table(title=f"MDL 3092 Cases with Multiple Plaintiffs (Starting from {start_date})") # Updated title
    table.add_column("Date", style="cyan")
    table.add_column("Filename", style="green")
    table.add_column("Plaintiffs", style="yellow", justify="right") # Updated column header

    # Get date folders
    date_folders = get_date_folders(base_path, start_date)

    if not date_folders:
        console.print(f"[bold yellow]No date folders found starting from {start_date}[/bold yellow]")
        return

    # Track total cases found
    total_cases = 0

    # Process each date folder
    for date_folder in date_folders:
        date_path = os.path.join(base_path, date_folder)
        dockets_path = os.path.join(date_path, 'dockets')

        # Skip if dockets folder doesn't exist
        if not os.path.exists(dockets_path):
            continue

        # Find all JSON files starting with 'ohnd'
        ohnd_files = [f for f in os.listdir(dockets_path)
                     if f.startswith('ohnd') and f.endswith('.json')]

        # Process each file
        for file_name in ohnd_files:
            file_path = os.path.join(dockets_path, file_name)
            result = analyze_docket_file(file_path)

            if result:
                total_cases += 1
                table.add_row(
                    date_folder[:8],  # Date
                    result['base_filename'],  # Filename
                    str(result['num_plaintiffs'])  # Number of plaintiffs - Updated key access
                )

    # Display results
    console.print("\n")
    console.print(f"[bold blue]Total cases found: {total_cases}[/bold blue]")
    console.print(table)

if __name__ == "__main__":
    main()