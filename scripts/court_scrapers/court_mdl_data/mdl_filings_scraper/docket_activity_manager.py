from typing import Dict, Any, List

from boto3.dynamodb.conditions import Key
from botocore.exceptions import ClientError

# Use relative import
try:
    from dynamodb_base_manager import DynamoDbBaseManager
except ImportError:
    from src.lib.dynamodb_base_manager import DynamoDbBaseManager


class DocketActivityManager(DynamoDbBaseManager):
    # No need to redefine key_config here if it's correctly in the base manager
    def __init__(self, config, use_local=False, local_port=8000):  # Added use_local/port
        super().__init__(config, 'DocketActivity', use_local=use_local, local_port=local_port)  # Pass flags
        # Ensure GSI info is present in base manager's key_config for local table creation
        if 'DocketActivity' not in self.key_config or 'gsis' not in self.key_config['DocketActivity']:
            self.logger.warning(
                "GSI configuration for DocketActivity might be missing in base manager. Local table creation may lack indexes.")

    def add_or_update_docket_activity(self, record, overwrite=False):
        """Add or update a single docket activity record using the batch method with batch_size=1."""
        # Ensure the record structure matches expected (e.g., snake_case if coming from external source)
        # Base manager's batch_add_or_update handles case conversion and sanitization now
        self.batch_insert_items([record], batch_size=1)

    def batch_add_or_update_docket_activities(self, records, batch_size=25, overwrite=False,
                                              sleep_time=1):  # Increased default batch size
        """Batch add or update docket activity records using the inherited method."""
        self.batch_insert_items(records, batch_size=batch_size, sleep_time=sleep_time)

    def get_records_by_mdlnum_and_docnum(self, mdl_num: str, doc_num: str) -> List[Dict[str, Any]]:  # Added type hints
        """Retrieve records by MdlNum and DocNum using the GSI."""
        gsi_name = 'MdlNum-DocNum-index'  # Defined in base key_config
        try:
            # Convert to string just in case, DynamoDB keys are often strings
            mdl_num_str = str(mdl_num)
            doc_num_str = str(doc_num)

            response = self.table.query(
                IndexName=gsi_name,
                KeyConditionExpression=Key('MdlNum').eq(mdl_num_str) & Key('DocNum').eq(doc_num_str)
                # Add other parameters like ProjectionExpression if needed
            )
            items = response.get('Items', [])
            # Add pagination handling if many items can match a single MdlNum/DocNum pair (unlikely but possible)
            while 'LastEvaluatedKey' in response:
                self.logger.debug(f"Paginating GSI query for {mdl_num_str}/{doc_num_str}")
                response = self.table.query(
                    IndexName=gsi_name,
                    KeyConditionExpression=Key('MdlNum').eq(mdl_num_str) & Key('DocNum').eq(doc_num_str),
                    ExclusiveStartKey=response['LastEvaluatedKey']
                )
                items.extend(response.get('Items', []))

            # Process records to convert Decimals etc. if needed by caller
            processed_items = self.process_records(items)

            self.logger.info(
                f"Found {len(processed_items)} items via GSI {gsi_name} for MdlNum {mdl_num_str} and DocNum {doc_num_str}")
            return processed_items
        except ClientError as e:
            if e.response['Error']['Code'] == 'ResourceNotFoundException':
                self.logger.error(f"GSI '{gsi_name}' not found on table '{self.table.name}'. Cannot query.")
            else:
                self.logger.error(
                    f"Failed to query GSI {gsi_name} for MdlNum {mdl_num_str} / DocNum {doc_num_str}: {e.response['Error']['Message']}",
                    exc_info=True)
            return []
        except Exception as e:
            self.logger.error(f"An unexpected error occurred during GSI query: {str(e)}", exc_info=True)
            return []
