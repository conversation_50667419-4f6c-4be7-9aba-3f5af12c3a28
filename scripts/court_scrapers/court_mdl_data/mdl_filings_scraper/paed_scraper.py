import re
import unicodedata
from pprint import pformat

import requests
from bs4 import <PERSON><PERSON>oup

from color_logging import LoggerSetup
from config import load_config
from pdf_extractor import PDFExtractor


class PAEDScraper:
    def __init__(self, config, url, mdl_info):
        self.url = url
        self.mdl_num = mdl_info
        log_setup = LoggerSetup(config, 'PAEDScraperLogger', 'PAEDScraper.log')
        self.logger = log_setup.get_logger()
        self.pdf_extractor = PDFExtractor(config, '')  # Instantiate PDFExtractor

    def fetch_content(self, url):
        try:
            response = requests.get(url)
            response.raise_for_status()
            return BeautifulSoup(response.content, 'html.parser')
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Error fetching the URL: {e}")
            return None

    def parse_orders(self, soup):
        tbody = soup.find('tbody')
        if not tbody:
            self.logger.info("No <tbody> found in the HTML content.")
            return []

        results = []
        rows = tbody.find_all('tr')
        for row in rows:
            result = {
                "mdl_num": self.mdl_num
            }

            # Extract the filing date
            date_td = row.find('td', text=re.compile(r'\d{1,2}/\d{1,2}/\d{2,4}'))
            if date_td:
                filing_date = date_td.get_text(strip=True)
                result['filing_date'] = filing_date
                self.logger.info(f"Extracted Filing Date: {filing_date}")

            # Extract the URL and document title
            a_tag = row.find('a', href=True)
            if a_tag:
                href = a_tag['href'].strip()
                url = href
                doc_title = a_tag.get_text(strip=True)

                result['url'] = url
                result['doc_title'] = doc_title
                self.logger.info(f"Extracted URL: {url} and Document Title: {doc_title}")

                # Extract the document number (doc_num) from the PDF
                result = self.extract_doc_nums_and_dates(result)

            results.append(result)

        return results

    def extract_doc_nums_and_dates(self, result):
        if 'pdf' in result['url']:
            pdf_text = self.pdf_extractor.extract_text_from_pdf_pymupdf(result['url'])
            normalized_text = unicodedata.normalize('NFKD', pdf_text)
            cleaned_text = ''.join(c for c in normalized_text if not unicodedata.category(c).startswith('C'))

            # Extract the document number using the specified regex
            if not result.get('doc_num') or result.get('doc_num') == 'NA':
                doc_num_match = re.search(r'\s+[Dd]ocument\s+(\d+)\s+', cleaned_text)
                if doc_num_match:
                    doc_num = doc_num_match.group(1)
                    result['doc_num'] = doc_num.replace('()', '')
                    self.logger.info(f"Extracted Doc Num: {doc_num} from PDF at URL: {result['url']}")

            # Extract the filing date
            if not result.get('filing_date') or result['filing_date'] == 'NA':
                filing_date = self.pdf_extractor.extract_filing_date(cleaned_text)
                if filing_date:
                    result['filing_date'] = filing_date
                    self.logger.info(f"Extracted Filing Date: {filing_date} from PDF at URL: {result['url']}")

        return result

    def run(self):
        all_results = []

        # Fetch and parse the content from the orders page
        soup = self.fetch_content(self.url)
        if soup:
            results = self.parse_orders(soup)
            all_results.extend(results)

        self.logger.info(pformat(all_results))
        return all_results


if __name__ == "__main__":
    config = load_config('01/01/1970')
    mdl_info = [
        ('paed', 'https://www.paed.uscourts.gov/mdl/mdl3094/orders', '3094')
    ]

    for mdl in mdl_info:
        court_id, url, mdl_info = mdl
    scraper = PAEDScraper(config, url, mdl_info)
    results = scraper.run()
