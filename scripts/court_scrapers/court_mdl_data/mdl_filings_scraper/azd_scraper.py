import requests
from bs4 import BeautifulSoup
import re
import json
from color_logging import <PERSON><PERSON><PERSON>etup
from config import load_config
from pprint import pformat


class AZDScraper:
    def __init__(self, config, url, mdl_id):
        self.url = url
        self.mdl_num = mdl_id
        log_setup = LoggerSetup(config, 'ILNDScraperLogger', 'ILNDScraper.log')
        self.logger = log_setup.get_logger()

    @staticmethod
    def extract_doc_num(url):
        # Check for cases with _{num}.pdf
        match_with_num = re.search(r'-(\d+)_\d+\.pdf', url)
        if match_with_num:
            return match_with_num.group(1)

        # Check for cases ending with _0.pdf
        match_with_0 = re.search(r'-(\d+)_0\.pdf', url)
        if match_with_0:
            return match_with_0.group(1)

        # Default case: number before .pdf
        match_default = re.search(r'-(\d+)\.pdf', url)
        return match_default.group(1) if match_default else None

    def run(self):
        results = []

        current_url = self.url

        while True:
            # Fetch the webpage content
            response = requests.get(current_url)
            data = response.text

            # Parse the HTML content
            soup = BeautifulSoup(data, 'html.parser')

            # Find the cases of interest section by filtering rows within the specific table
            table = soup.find('table', class_='views-table')
            if not table:
                break

            rows = table.find_all('tr')[1:]  # Skip header row

            for row in rows:
                filing_date = row.find('span', {'class': 'date-display-single'}).text.strip()
                doc_title = row.find('a').text.strip()
                url_link = row.find_all('a')[-1]  # Get the last <a> tag in the row, which links to the PDF

                # Extract the direct URL of the case file
                url = url_link['href']
                doc_num = self.extract_doc_num(url)

                case_info = {
                    "mdl_num": self.mdl_num,
                    "filing_date": filing_date,
                    "doc_title": doc_title,
                    "url": url,
                    "doc_num": doc_num
                }
                self.logger.info(case_info)
                results.append(case_info)

            # Find the 'next' page link
            next_page_link = soup.find('a', title="Go to next page")
            if next_page_link:
                current_url = self.url + next_page_link['href']
            else:
                break

        return results


if __name__ == '__main__':
    config = load_config('01/01/1970')


    mdl_info = [
        ("azd", "https://www.azd.uscourts.gov/re-bard-implanted-port-catheter-products-liability-litigation", "3081"),
        # ("/re-bard-ivc-filters-products-liability-litigation", "2641"),
    ]

    for mdl in mdl_info:
        scraper = AZDScraper(config, mdl[1], mdl[2])
        cases = scraper.run()
    # returns list of dicts