import requests
from bs4 import <PERSON>Soup
from urllib.parse import urljoin, quote, unquote, urlparse, urlunparse
from color_logging import LoggerSetup
from config import load_config
from pprint import pformat


class ILNDScraper:
    def __init__(self, config, url, mdl_id):
        self.url = url
        self.mdl_num = mdl_id
        log_setup = LoggerSetup(config, 'ILNDScraperLogger', 'ILNDScraper.log')
        self.logger = log_setup.get_logger()

    def fetch_content(self, url):
        try:
            response = requests.get(url)
            response.raise_for_status()
            return BeautifulSoup(response.content, 'html.parser')
        except requests.exceptions.RequestException as e:
            self.logger.error(pformat(f"Error fetching the URL: {e}"))
            return None

    def fix_url(self, href):
        parsed_url = urlparse(href)
        if parsed_url.scheme and parsed_url.netloc:
            # If it's a full URL, decode any encoded parts and then re-encode them properly
            decoded_path = unquote(parsed_url.path)
            reencoded_path = quote(decoded_path)
            fixed_url = urlunparse((parsed_url.scheme, parsed_url.netloc, reencoded_path, '', '', ''))
        else:
            # If the URL does not start with 'http', prepend the base URL
            if not href.startswith('http'):
                href = urljoin('https://www.ilnd.uscourts.gov', href)
            fixed_url = href
        return fixed_url

    def get_case_management_orders(self, soup, mdl_num):
        results = []

        table = soup.find('table', id='casemanagementorders')
        if not table:
            self.logger.info(pformat("No Case Management Orders table found."))
            return []

        tbody = table.find('tbody')
        for row in tbody.find_all('tr'):
            cols = row.find_all('td')
            if len(cols) >= 4:
                filing_date = cols[1].get_text(strip=True)
                doc_num = cols[2].get_text(strip=True)

                # Extract the URL and document title
                a_tag = cols[3].find('a')
                if a_tag and 'href' in a_tag.attrs:
                    href = a_tag['href'].strip()
                    url = self.fix_url(href)
                    doc_title = a_tag.get_text(strip=True)

                    results.append({
                        "mdl_num": mdl_num,
                        "url": url,
                        "doc_title": doc_title,
                        "filing_date": filing_date,
                        "doc_num": doc_num
                    })

        return results

    def get_forms(self, soup, mdl_num):
        forms = []

        form_section = soup.find('div', {'id': f'collapse_{mdl_num}_1'})
        if form_section:
            form_body = form_section.find('div', class_='panel-body')
            if form_body:
                for p in form_body.find_all('p'):
                    a_tag = p.find('a')
                    if a_tag and 'href' in a_tag.attrs:
                        href = a_tag['href'].strip()
                        url = self.fix_url(href)
                        doc_title = a_tag.get_text(strip=True)

                        forms.append({
                            'mdl_num': mdl_num,
                            'url': url,
                            'doc_title': doc_title
                        })
        return forms

    def run(self):
        all_results = []

        soup = self.fetch_content(self.url)
        if soup:
            case_management_orders = self.get_case_management_orders(soup, self.mdl_num)
            forms = self.get_forms(soup, self.mdl_num)
            all_results.extend(case_management_orders)
            all_results.extend(forms)

            # self.logger.info(pformat(f"Scraped data for MDL {self.mdl_num}: {case_management_orders}"))
            # self.logger.info(pformat(f"Scraped forms for MDL {self.mdl_num}: {forms}"))
        self.logger.info(pformat(all_results))
        return all_results


if __name__ == "__main__":
    config = load_config('01/01/1970')
    mdl_info = [
        ('ilnd', 'https://www.ilnd.uscourts.gov/mdl-details.aspx?91eSFtoI+ycFmA6482wQKA==', '3060'),
        ('ilnd', 'https://www.ilnd.uscourts.gov/mdl-details.aspx?fnu/YDnD0O7Y8SjpUhHmZA==', '3079'),
        ('ilnd', 'https://www.ilnd.uscourts.gov/mdl-details.aspx?Lbz1nwUsE4JWF/IQJN6GpA==', '3026'),
        ('ilnd', 'https://www.ilnd.uscourts.gov/mdl-details.aspx?Y/EMwl0+f9LcVNqgbUFRDw==', '3037')
    ]

    for mdl in mdl_info:
        court_id, url, mdl_num = mdl
        scraper = ILNDScraper(config, url, mdl_num)
        results = scraper.run()
