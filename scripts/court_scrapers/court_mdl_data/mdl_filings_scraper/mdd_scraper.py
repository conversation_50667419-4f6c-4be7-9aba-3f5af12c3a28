import requests
from bs4 import BeautifulSoup
import re
import urllib.parse
import unicodedata

from pdf_extractor import PDFExtractor
from color_logging import LoggerSetup
from config import load_config
from pprint import pprint, pformat

class MDDScraper:
    def __init__(self, config, url, mdl_info):
        self.config = config
        self.url = url
        self.mdl_num = mdl_info
        self.pdf_extractor = PDFExtractor(config, '')  # Initialize PDFExtractor correctly
        # Assuming LoggerSetup is configured in your config module
        log_setup = LoggerSetup(config, 'MDDScraperLogger', 'MDDScraper.log')
        self.logger = log_setup.get_logger()

    @staticmethod
    def extract_filing_date(title):
        # Regex to match the date formats
        date_match = re.search(
            r'\b(?:Jan(?:uary)?|Feb(?:ruary)?|Mar(?:ch)?|Apr(?:il)?|May|Jun(?:e)?|Jul(?:y)?|Aug(?:ust)?|Sep(?:tember)?|Oct(?:ober)?|Nov(?:ember)?|Dec(?:ember)?)\.?\s+\d{1,2},\s+\d{4}\b',
            title
        )
        if date_match:
            # Map of month abbreviations to their corresponding numeric values
            months = {
                'Jan': '01', 'Feb': '02', 'Mar': '03', 'Apr': '04', 'May': '05', 'Jun': '06',
                'Jul': '07', 'Aug': '08', 'Sep': '09', 'Oct': '10', 'Nov': '11', 'Dec': '12'
            }

            # Extract and format the date
            month_str, day, year = re.match(r'([A-Za-z]+)\.? (\d{1,2}), (\d{4})', date_match.group(0)).groups()
            month = months[month_str[:3]]
            day = day.zfill(2)  # Zero-pad the day if necessary
            date_str = f'{month}/{day}/{year}'

            # Remove any surrounding parentheses
            date_str = date_str.strip("()")

            return date_str

        return None

    @staticmethod
    def extract_doc_num(url):
        # Regex to match document numbers with a number followed by a hyphen and more numbers
        match = re.search(r'(\d+)-(\d+)', url)
        if match:
            return match.group(0)  # Return the entire match (e.g., "123-456")

        # Fallback to the original extraction logic
        match = re.search(r'-(\d+)\.pdf', url)
        return match.group(1) if match else None

    @staticmethod
    def fix_url(url):
        # Replace spaces with %20 to make the URL valid
        return urllib.parse.quote(url, safe=':/')

    def extract_doc_nums_and_dates(self, result):
        if 'pdf' in result['url']:
            pdf_text = self.pdf_extractor.extract_text_from_pdf_pymupdf(result['url'])
            normalized_text = unicodedata.normalize('NFKD', pdf_text)
            cleaned_text = ''.join(c for c in normalized_text if not unicodedata.category(c).startswith('C'))

            # Extract the document number using the specified regex
            if not result.get('doc_num') or result.get('doc_num') == 'NA':
                doc_num_match = re.search(r'\s+[Ee]ntry\s+[Nn]umber\s+(\d+)\s+', cleaned_text)
                if doc_num_match:
                    doc_num = doc_num_match.group(1)
                    result['doc_num'] = doc_num.replace('()', '')
                    self.logger.info(f"Extracted Doc Num: {doc_num} from PDF at URL: {result['url']}")
                else:
                    doc_num = self.pdf_extractor.extract_doc_num_with_tesseract(result['url'])
                    if doc_num is not None:
                        result['doc_num'] = doc_num
                    self.logger.info(f"Extracted Doc Num: {doc_num} from PDF at URL: {result['url']}")

            # Extract the filing date
            if not result.get('filing_date') or result['filing_date'] == 'NA':
                filing_date = self.pdf_extractor.extract_filing_date_with_tesseract(result['url'])
                if filing_date:
                    result['filing_date'] = filing_date
                    self.logger.info(f"Extracted Filing Date: {filing_date} from PDF at URL: {result['url']}")

        return result  # Always return the result dictionary, even if no changes were made

    def run(self):
        results = []
        response = requests.get(self.url)
        soup = BeautifulSoup(response.text, 'html.parser')
        nodes = soup.find_all('div', class_='field__item')

        for node in nodes:
            anchor_tag = node.find('a')
            if anchor_tag:
                title = anchor_tag.text.strip()
                url = urllib.parse.unquote(anchor_tag['href'])
                doc_num = self.extract_doc_num(url)
                filing_date = self.extract_filing_date(title)

                # Remove the date from the title after extracting it
                if filing_date:
                    title = re.sub(
                        r'\b(?:Jan(?:uary)?|Feb(?:ruary)?|Mar(?:ch)?|Apr(?:il)?|May|Jun(?:e)?|Jul(?:y)?|Aug(?:ust)?|Sep(?:tember)?|Oct(?:ober)?|Nov(?:ember)?|Dec(?:ember)?)\.?\s+\d{1,2},\s+\d{4}\b',
                        '', title).strip()

                case_info = {
                    "mdl_num": self.mdl_num,
                    "url": self.fix_url(url),
                    "doc_title": title,
                    "doc_num": doc_num,
                    "filing_date": filing_date
                }

                # Backup extraction if initial extraction fails
                if not doc_num or not filing_date:
                    case_info = self.extract_doc_nums_and_dates(case_info)

                if case_info['doc_title'] == 'Case Management Order No. 2':
                    case_info['filing_date'] = '08/23/2017'
                pprint(case_info)
                results.append(case_info)

        return results


if __name__ == '__main__':
    config = load_config('01/01/1970')
    mdl_info = "2775"
    scraper = MDDScraper(config, "https://www.mdd.uscourts.gov/mdl_documents", mdl_info)
    cases = scraper.run()
    for case in cases:
        print(case)
