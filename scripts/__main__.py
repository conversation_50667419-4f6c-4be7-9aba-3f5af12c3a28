"""
Enable running scripts as modules.

This allows scripts to be run with:
    python -m src.scripts.script_name

For example:
    python -m src.scripts.regenerate_missing_ads --date 20250528
"""

import importlib
import sys
from pathlib import Path


def main():
    """Main entry point for running scripts as modules."""
    if len(sys.argv) < 2:
        print("Usage: python -m src.scripts.SCRIPT_NAME [arguments]")
        print("\nAvailable scripts:")

        # List available scripts
        scripts_dir = Path(__file__).parent
        for script in scripts_dir.glob("*.py"):
            if script.name not in ["__init__.py", "__main__.py"] and not script.name.startswith("_"):
                print(f"  - {script.stem}")

        sys.exit(1)

    # Get the script name from command line
    script_name = sys.argv[1]

    # Remove the script name from argv so the script sees normal arguments
    sys.argv = sys.argv[1:]

    try:
        # Import and run the script
        module = importlib.import_module(f".{script_name}", package="src.scripts")

        # Check if the module has a main function or __main__ block
        if hasattr(module, 'main'):
            import asyncio
            if asyncio.iscoroutinefunction(module.main):
                asyncio.run(module.main())
            else:
                module.main()

    except ImportError as e:
        print(f"Error: Could not import script '{script_name}'")
        print(f"Details: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"Error running script '{script_name}': {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
