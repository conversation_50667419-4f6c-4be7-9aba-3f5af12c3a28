# import os
# import shutil
# from pathlib import Path
# from rich.console import Console
# from typing import List
#
#
# # Import the project root handling function
# try:
#     from scripts.utils import get_project_root
# except ImportError:
#     # Fall back to direct import if utils.py is not available
#     try:
#         from src.lib.config_adapter import PROJECT_ROOT
#     except ImportError:
#         # If can't import, try to load from environment or use a fallback
#         try:
#             from dotenv import load_dotenv
#             load_dotenv()
#             PROJECT_ROOT = os.getenv('PROJECT_ROOT', os.path.expanduser('/'))
#         except ImportError:
#             PROJECT_ROOT = os.path.expanduser('/')
# else:
#     PROJECT_ROOT = get_project_root()
#
#
# class DocketCopier:
#     def __init__(self):
#         self.console = Console()
#         self.source_dir = os.path.join(PROJECT_ROOT, "data", 20250311_bak/dockets)
#         self.target_dir = os.path.join(PROJECT_ROOT, "data", 20250311/dockets)
#
#     @staticmethod
#     def get_files_to_copy() -> List[str]:
#         return [
#             "ilnd_25_02517_Colbert_v_Godrej_SON_Holdings_Inc_et_al.json",
#             "ksd_25_02117_Crisp_v_Sunflower_Medical_Group_PA.json",
#             "mnd_25_00898_Ahearn_v_3M_Company_et_al.json",
#             "mnd_25_00897_Ackerman_v_3M_Company_et_al.json",
#             "mnd_25_00900_Kuspa_v_3M_Company_et_al.json",
#             "njd_25_01783_STAINS_v_DEWALT_INDUSTRIAL_TOOL_CO_et_al.json",
#             "cand_25_02434_Thomas_v_Meta_Platforms_Inc_et_al.json",
#             "flnd_25_00256_CARRUTHERS_v_PFIZER_INC_et_al.json",
#             "cand_25_02446_B_v_Uber_Technologies_Inc_et_al.json",
#         ]
#
#     def copy_files(self):
#         # Create target directory if it doesn't exist
#         Path(self.target_dir).mkdir(parents=True, exist_ok=True)
#
#         files_to_copy = self.get_files_to_copy()
#         copied_count = 0
#         errors = []
#
#         for filename in files_to_copy:
#             try:
#                 base_name = filename.replace('.json', '')
#
#                 # Define all possible file variants
#                 variants = {
#                     'json': f"{base_name}.json",
#                     'zip': f"{base_name}.zip",
#                     'pdf': f"{base_name}.pdf"
#                 }
#
#                 # Copy each variant if it exists
#                 for ext, variant_filename in variants.items():
#                     source = os.path.join(self.source_dir, variant_filename)
#                     target = os.path.join(self.target_dir, variant_filename)
#
#                     if os.path.exists(source):
#                         shutil.copy2(source, target)
#                         self.console.print(f"[green]Copied {variant_filename}")
#                         copied_count += 1
#                     else:
#                         if ext == 'json':  # Only report missing JSON as error
#                             errors.append(f"JSON file not found: {variant_filename}")
#                         else:
#                             self.console.print(f"[yellow]Warning: {ext.upper()} file not found: {variant_filename}")
#
#             except Exception as e:
#                 errors.append(f"Error copying {filename}: {str(e)}")
#
#         # Print summary
#         self.console.print(f"\n[bold]Summary:[/bold]")
#         self.console.print(f"Total files copied: {copied_count}")
#
#         if errors:
#             self.console.print("\n[bold red]Errors:[/bold red]")
#             for error in errors:
#                 self.console.print(f"[red]{error}[/red]")
#
# def main():
#     copier = DocketCopier()
#     copier.copy_files()
#
# if __name__ == "__main__":
#     main()