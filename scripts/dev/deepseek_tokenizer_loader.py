# --- START OF FILE deepseek_tokenizer_loader.py ---
import transformers
import logging
import os

logger = logging.getLogger(__name__)

def load_deepseek_tokenizer(tokenizer_dir="./"):
    """Loads the DeepSeek tokenizer from a specified directory."""
    if not os.path.isdir(tokenizer_dir):
        logger.error(f"Tokenizer directory not found: {tokenizer_dir}")
        raise FileNotFoundError(f"Tokenizer directory not found: {tokenizer_dir}")

    expected_files = ["tokenizer_config.json"] # Add others if needed like .model or tokenizer.json
    if not all(os.path.exists(os.path.join(tokenizer_dir, f)) for f in expected_files):
         logger.warning(f"Expected tokenizer configuration files might be missing in {tokenizer_dir}")

    try:
        logger.info(f"Loading tokenizer from: {tokenizer_dir}")
        # Ensure trust_remote_code=True if the tokenizer requires custom code
        # Adjust based on the actual DeepSeek tokenizer implementation
        tokenizer = transformers.AutoTokenizer.from_pretrained(
            tokenizer_dir,
            trust_remote_code=True
        )
        logger.info("Tokenizer loaded successfully.")
        return tokenizer
    except Exception as e:
        logger.error(f"Failed to load tokenizer from {tokenizer_dir}: {e}", exc_info=True)
        raise

if __name__ == '__main__':
    logging.basicConfig(level=logging.INFO)
    try:
        # Example Usage: Assumes tokenizer files are in the current directory
        tokenizer = load_deepseek_tokenizer("../../src/")
        test_text = "Hello world! This is a test."
        encoded = tokenizer.encode(test_text)
        logger.info(f"Test text: '{test_text}'")
        logger.info(f"Encoded token IDs: {encoded}")
        logger.info(f"Number of tokens: {len(encoded)}")
        decoded = tokenizer.decode(encoded)
        logger.info(f"Decoded text: '{decoded}'")
    except Exception as e:
        logger.error(f"Error during tokenizer test: {e}")
# --- END OF FILE deepseek_tokenizer_loader.py ---