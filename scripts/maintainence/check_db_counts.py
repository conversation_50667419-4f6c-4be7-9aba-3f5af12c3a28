from src.lib.config_adapter import load_config, PROJECT_ROOT
from src.repositories.pacer_repository import PacerRepository
from src.repositories.fb_archive_repository import FBArchiveRepository
import asyncio
import logging
import os

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def check_db_counts(storage_dir: str) -> dict:
    """Check record counts for both tables in a specific storage location"""
    config = load_config('01/01/70')
    
    # Override storage directory and ensure we're using local only
    os.environ['DYNAMODB_LOCAL_DIR'] = storage_dir
    
    counts = {}
    
    # Check Pacer table
    pacer_repository = PacerRepository()
    async with pacer_repository:
        counts['Pacer'] = await pacer_repository.count_items()
    
    # Check FBAdArchive table
    fb_repository = FBArchiveRepository()
    async with fb_repository:
        counts['FBAdArchive'] = await fb_repository.count_items()
    
    return counts

async def main():
    # Define both storage locations
    root_storage = os.path.join(PROJECT_ROOT, '.dynamodb')
    src_storage = os.path.join(PROJECT_ROOT, 'src', '.dynamodb')
    
    logger.info("Checking record counts in local DynamoDB instances only...")
    
    # Check root storage
    logger.info(f"\nChecking root storage at: {root_storage}")
    root_counts = await check_db_counts(root_storage)
    logger.info("Root storage counts:")
    logger.info(f"  Pacer: {root_counts['Pacer']:,} records")
    logger.info(f"  FBAdArchive: {root_counts['FBAdArchive']:,} records")
    
    # Check src storage
    logger.info(f"\nChecking src storage at: {src_storage}")
    src_counts = await check_db_counts(src_storage)
    logger.info("Src storage counts:")
    logger.info(f"  Pacer: {src_counts['Pacer']:,} records")
    logger.info(f"  FBAdArchive: {src_counts['FBAdArchive']:,} records")
    
    # Compare and recommend
    logger.info("\nComparison:")
    for table in ['Pacer', 'FBAdArchive']:
        diff = root_counts[table] - src_counts[table]
        if diff > 0:
            logger.info(f"{table}: Root has {diff:,} more records")
        elif diff < 0:
            logger.info(f"{table}: Src has {abs(diff):,} more records")
        else:
            logger.info(f"{table}: Both locations have same number of records")

if __name__ == "__main__":
    asyncio.run(main())