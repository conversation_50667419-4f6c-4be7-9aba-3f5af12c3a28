#!/bin/bash

# Function to print usage and exit
usage() {
  echo "Usage: $0 -f FIELD -v VALUE [-s]"
  echo "  -f FIELD:  The JSON field to search."
  echo "  -v VALUE:  The value to search for."
  echo "  -s:       Enable case-insensitive substring search."
  exit 1
}

# Initialize variables
FIELD=""
VALUE=""
SUBSTRING_FLAG=false
args=("$@")  # Store all arguments in an array

# Manually parse arguments
i=0
while [ $i -lt ${#args[@]} ]; do
  arg="${args[$i]}"
  case "$arg" in
    -f)
      i=$((i + 1))  # Move to next argument (the field value)
      if [ $i -ge ${#args[@]} ]; then
        echo "Missing argument for -f" >&2
        usage
      fi
      FIELD="${args[$i]}"
      i=$((i + 1))  # Increment i AFTER processing the value.
      ;;
    -v)
      i=$((i + 1))  # Move to next argument (the value)
      if [ $i -ge ${#args[@]} ]; then
        echo "Missing argument for -v" >&2
        usage
      fi
      VALUE="${args[$i]}"
      i=$((i + 1))  # Increment i AFTER processing the value.
      ;;
    -s)
      SUBSTRING_FLAG=true
      i=$((i + 1))  # Increment i AFTER processing the flag
      ;;
    *)
      echo "Invalid option: $arg" >&2
      usage
      exit 1  # Add an exit here to prevent further processing with bad options.
      ;;
  esac
  # Remove this line:  i=$((i + 1)) # Increment i AFTER processing the flag
done

# Check if required arguments are provided *AFTER* parsing!
if [[ -z "$FIELD" ]]; then
    echo "Missing required argument: -f FIELD" >&2
    usage
fi

if [[ -z "$VALUE" ]]; then
    echo "Missing required argument: -v VALUE" >&2
    usage
fi

# BASE_DIR and the rest of the script remain the same from the previous versions
BASE_DIR="/Users/<USER>/PycharmProjects/mt_competitive_analysis/data"

# Find all JSON files under directories matching pattern: {BASE_DIR}/{YYYYMMDD}/dockets/...
find "$BASE_DIR" -type f -name "*.json" | while read -r file; do
    # Use jq to check if the field contains the specified value
    if [ "$SUBSTRING_FLAG" = true ]; then
        # Case-insensitive substring search
        # Use 'tr' for lowercase conversion (more portable)
        lowercase_value=$(echo "$VALUE" | tr '[:upper:]' '[:lower:]')
        jq_expression=".\"$FIELD\" | ascii_downcase | contains(\"${lowercase_value}\")"
    else
        # Exact match search
        jq_expression=".\"$FIELD\" == \"${VALUE}\""
    fi

    if jq -e "$jq_expression" "$file" >/dev/null 2>&1; then
        # Print path relative to the current working directory
        rel_path="${file/#$PWD\//}"
        echo "$rel_path"
    fi
done
