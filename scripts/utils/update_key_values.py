import logging
import os
# Configure logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

# Import the project root handling function
try:
    from scripts.utils import get_project_root
except ImportError:
    # Fall back to direct import if utils.py is not available
    try:
        from src.lib.config_adapter import PROJECT_ROOT
    except ImportError:
        # If can't import, try to load from environment or use a fallback
        try:
            from dotenv import load_dotenv
            load_dotenv()
            PROJECT_ROOT = os.getenv('PROJECT_ROOT', os.path.expanduser('/'))
        except ImportError:
            PROJECT_ROOT = os.path.expanduser('/')
else:
    PROJECT_ROOT = get_project_root()

#!/usr/bin/env python3
"""
Script to update key values in JSON files.

This script searches for a specific key with a specific value in JSON files
and updates it with a new value. It can search recursively through nested
dictionaries and lists.

Usage:
    python update_key_values.py --date YYYYMMDD --key "key_name" --old-value "old_value" --new-value "new_value"
"""

import argparse
import json
import os
import sys
from typing import Dict, List, Any, Tuple, Set

# Rich library for better terminal output
try:
    from rich.console import Console
    from rich.panel import Panel
    from rich.prompt import Confirm
    from rich.table import Table
    from rich.progress import Progress, TextColumn, BarColumn, TimeElapsedColumn
    RICH_AVAILABLE = True
except ImportError:
    RICH_AVAILABLE = False
    print("Rich library not available. Install with: pip install rich")
    print("Falling back to standard output.")

    # Fallbacks for rich functions
    class Console:
        def print(self, *args, **kwargs):
            print(*args)

    class Panel:
        @staticmethod
        def fit(text, **kwargs):
            return text

    class Confirm:
        @staticmethod
        def ask(question, default=True):
            response = input(f"{question} (Y/n): ").strip().lower()
            if not response:
                return default
            return response.startswith('y')

    class Table:
        def __init__(self, *args, **kwargs):
            pass

        def add_column(self, *args, **kwargs):
            pass

        def add_row(self, *args, **kwargs):
            pass

    class Progress:
        def __init__(self, *args, **kwargs):
            pass

        def __enter__(self):
            return self

        def __exit__(self, *args, **kwargs):
            pass

        def add_task(self, *args, **kwargs):
            return 0

        def update(self, *args, **kwargs):
            pass

# Initialize console
console = Console()


def parse_arguments() -> argparse.Namespace:
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser(
        description="Update key values in JSON files."
    )
    parser.add_argument(
        "--date",
        required=True,
        help="Date in YYYYMMDD format"
    )
    parser.add_argument(
        "--key",
        required=True,
        help="JSON key to search for (can be a nested key using dot notation, e.g., 'parent.child')"
    )
    parser.add_argument(
        "--old-value",
        required=True,
        help="Old value to replace"
    )
    parser.add_argument(
        "--new-value",
        required=True,
        help="New value to set"
    )
    parser.add_argument(
        "--no-dry-run",
        action="store_true",
        help="Apply changes immediately without confirmation"
    )
    parser.add_argument(
        "--case-insensitive",
        action="store_true",
        help="Perform case-insensitive matching"
    )
    parser.add_argument(
        "--substring",
        action="store_true",
        help="Perform substring replacement"
    )

    args = parser.parse_args()
    # Set dry_run to True by default (opposite of no-dry-run)
    args.dry_run = not args.no_dry_run
    return args


def find_keys_with_value(
    data: Any,
    target_key: str,
    target_value: Any,
    path: str = "",
    results: List[Tuple[str, Any]] = None,
    case_insensitive: bool = False,
    substring_match: bool = False
) -> List[Tuple[str, Any]]:
    """
    Recursively search for keys with specific value.
    
    Args:
        data: The data structure to search in
        target_key: The key to search for
        target_value: The value to match
        path: Current path in the data structure
        results: List to store results
        case_insensitive: If True, perform case-insensitive matching
        substring_match: If True, match if target_value is a substring of the field value
        
    Returns:
        List of tuples with (path, value)
    """
    if results is None:
        results = []

    if isinstance(data, dict):
        for key, value in data.items():
            new_path = f"{path}.{key}" if path else key

            # Check if this key matches our target
            if key == target_key:
                if isinstance(value, str) and isinstance(target_value, str):
                    if substring_match:
                        if case_insensitive:
                            if target_value.lower() in value.lower():
                                results.append((new_path, value))
                        else:
                            if target_value in value:
                                results.append((new_path, value))
                    else:
                        if case_insensitive:
                            if value.lower() == target_value.lower():
                                results.append((new_path, value))
                        else:
                            if value == target_value:
                                results.append((new_path, value))

            # Recursively search in nested structures
            if isinstance(value, (dict, list)):
                find_keys_with_value(value, target_key, target_value, new_path, results, case_insensitive,
                                     substring_match)

    elif isinstance(data, list):
        for i, item in enumerate(data):
            new_path = f"{path}[{i}]"

            # Recursively search in nested structures
            if isinstance(item, (dict, list)):
                find_keys_with_value(item, target_key, target_value, new_path, results, case_insensitive,
                                     substring_match)

            # If the list contains dictionaries, check each one
            if isinstance(item, dict) and target_key in item:
                value = item[target_key]
                if isinstance(value, str) and isinstance(target_value, str):
                    if substring_match:
                        if case_insensitive:
                            if target_value.lower() in value.lower():
                                results.append((f"{new_path}.{target_key}", value))
                        else:
                            if target_value in value:
                                results.append((f"{new_path}.{target_key}", value))
                    else:
                        if case_insensitive:
                            if value.lower() == target_value.lower():
                                results.append((f"{new_path}.{target_key}", value))
                        else:
                            if value == target_value:
                                results.append((f"{new_path}.{target_key}", value))

    return results


def update_keys_with_value(
    data: Any,
    target_key: str,
    old_value: Any,
    new_value: Any,
    path: str = "",
    changes: List[Tuple[str, Any, Any]] = None,
    case_insensitive: bool = False,
    substring_match: bool = False
) -> List[Tuple[str, Any, Any]]:
    """
    Recursively update keys with new value.
    
    Args:
        data: The data structure to update
        target_key: The key to update
        old_value: The value to replace
        new_value: The new value to set
        path: Current path in the data structure
        changes: List to store changes made
        case_insensitive: If True, perform case-insensitive matching
        substring_match: If True, match if old_value is a substring of the field value
        
    Returns:
        List of tuples with (path, old_value, new_value)
    """
    if changes is None:
        changes = []

    if isinstance(data, dict):
        for key, value in data.items():
            new_path = f"{path}.{key}" if path else key

            if key == target_key:
                if isinstance(value, str) and isinstance(old_value, str):
                    if substring_match:
                        if case_insensitive:
                            if old_value.lower() in value.lower():
                                # Perform the substring replacement
                                new_val = value.replace(old_value, new_value)
                                if new_val != value:  # Only record if change occurred
                                    data[key] = new_val
                                    changes.append((new_path, value, new_val))
                        else:
                            if old_value in value:
                                # Perform the substring replacement
                                new_val = value.replace(old_value, new_value)
                                if new_val != value:  # Only record if change occurred
                                    data[key] = new_val
                                    changes.append((new_path, value, new_val))
                    else:
                        # Exact match logic remains the same
                        match_found = False
                        if case_insensitive:
                            match_found = value.lower() == old_value.lower()
                        else:
                            match_found = value == old_value

                        if match_found:
                            data[key] = new_value
                            changes.append((new_path, value, new_value))

            if isinstance(value, (dict, list)):
                update_keys_with_value(value, target_key, old_value, new_value, new_path, changes, case_insensitive,
                                       substring_match)

    elif isinstance(data, list):
        for i, item in enumerate(data):
            new_path = f"{path}[{i}]"

            if isinstance(item, (dict, list)):
                update_keys_with_value(item, target_key, old_value, new_value, new_path, changes, case_insensitive,
                                       substring_match)

            if isinstance(item, dict) and target_key in item:
                value = item[target_key]
                if isinstance(value, str) and isinstance(old_value, str):
                    if substring_match:
                        if case_insensitive:
                            if old_value.lower() in value.lower():
                                # Perform the substring replacement
                                new_val = value.replace(old_value, new_value)
                                if new_val != value:
                                    item[target_key] = new_val
                                    changes.append((f"{new_path}.{target_key}", value, new_val))
                        else:
                            if old_value in value:
                                # Perform the substring replacement
                                new_val = value.replace(old_value, new_value)
                                if new_val != value:
                                    item[target_key] = new_val
                                    changes.append((f"{new_path}.{target_key}", value, new_val))
                    else:
                        # Exact match logic remains the same
                        match_found = False
                        if case_insensitive:
                            match_found = value.lower() == old_value.lower()
                        else:
                            match_found = value == old_value

                        if match_found:
                            old_val = item[target_key]
                            item[target_key] = new_value
                            changes.append((f"{new_path}.{target_key}", old_val, new_value))

    return changes


def process_json_files(
        date_str: str,
        target_key: str,
        old_value: Any,
        new_value: Any,
        dry_run: bool = True,
        case_insensitive: bool = False,
        substring_match: bool = False
) -> Tuple[Dict[str, List[Tuple[str, Any, Any]]], Set[str]]:
    """
    Process all JSON files in the specified directory.

    Args:
        date_str: Date string in YYYYMMDD format
        target_key: The key to update
        old_value: The value to replace
        new_value: The new value to set
        dry_run: If True, don't make changes
        case_insensitive: If True, perform case-insensitive matching
        substring_match: If True, match if old_value is a substring of the field value

    Returns:
        Dictionary mapping filenames to lists of changes and set of files with matches
    """
    base_path = os.path.join(PROJECT_ROOT, "data", date_str, "dockets")

    if not os.path.exists(base_path):
        console.print(f"[bold red]Error:[/bold red] Directory not found: {base_path}")
        sys.exit(1)

    json_files = [f for f in os.listdir(base_path) if f.endswith('.json')]

    if not json_files:
        console.print(f"[bold yellow]Warning:[/bold yellow] No JSON files found in {base_path}")
        sys.exit(0)

    console.print(f"[bold green]Found {len(json_files)} JSON files in {base_path}[/bold green]")

    # Dictionary to store changes by filename
    all_changes = {}
    # Set to track files with matches
    files_with_matches = set()

    # Process each JSON file
    with Progress(
            TextColumn("[bold blue]{task.description}[/bold blue]"),
            BarColumn(),
            TextColumn("[bold green]{task.completed}/{task.total}[/bold green]"),
            TimeElapsedColumn()
    ) as progress:
        task = progress.add_task("Processing JSON files", total=len(json_files))

        for filename in json_files:
            file_path = os.path.join(base_path, filename)
            changes_in_file = []

            try:
                with open(file_path, 'r') as f:
                    data = json.load(f)

                # Direct check for law_firm field
                if target_key == "law_firm":
                    # Check top-level law_firm field
                    if "law_firm" in data:
                        current_value = data["law_firm"]
                        match_found = False

                        if substring_match:
                            if case_insensitive:
                                match_found = str(old_value).lower() in str(current_value).lower()
                            else:
                                match_found = str(old_value) in str(current_value)
                        else:
                            if case_insensitive:
                                match_found = str(current_value).lower() == str(old_value).lower()
                            else:
                                match_found = current_value == old_value

                        if match_found:
                            files_with_matches.add(filename)
                            changes_in_file.append(("law_firm", current_value, new_value))

                            if not dry_run:
                                data["law_firm"] = new_value

                    # Check law_firms array
                    if "law_firms" in data and isinstance(data["law_firms"], list):
                        for i, firm in enumerate(data["law_firms"]):
                            match_found = False

                            if substring_match:
                                if case_insensitive:
                                    match_found = str(old_value).lower() in str(firm).lower()
                                else:
                                    match_found = str(old_value) in str(firm)
                            else:
                                if case_insensitive:
                                    match_found = str(firm).lower() == str(old_value).lower()
                                else:
                                    match_found = firm == old_value

                            if match_found:
                                files_with_matches.add(filename)
                                changes_in_file.append((f"law_firms[{i}]", firm, new_value))

                                if not dry_run:
                                    data["law_firms"][i] = new_value

                    # Check attorneys_gpt array
                    if "attorneys_gpt" in data and isinstance(data["attorneys_gpt"], list):
                        for i, attorney in enumerate(data["attorneys_gpt"]):
                            if isinstance(attorney, dict) and "law_firm" in attorney:
                                current_value = attorney["law_firm"]
                                match_found = False

                                if substring_match:
                                    if case_insensitive:
                                        match_found = str(old_value).lower() in str(current_value).lower()
                                    else:
                                        match_found = str(old_value) in str(current_value)
                                else:
                                    if case_insensitive:
                                        match_found = str(current_value).lower() == str(old_value).lower()
                                    else:
                                        match_found = current_value == old_value

                                if match_found:
                                    files_with_matches.add(filename)
                                    changes_in_file.append((f"attorneys_gpt[{i}].law_firm", current_value, new_value))

                                    if not dry_run:
                                        attorney["law_firm"] = new_value

                    # Check attorney array
                    if "attorney" in data and isinstance(data["attorney"], list):
                        for i, attorney in enumerate(data["attorney"]):
                            if isinstance(attorney, dict) and "law_firm" in attorney:
                                current_value = attorney["law_firm"]
                                match_found = False

                                if substring_match:
                                    if case_insensitive:
                                        match_found = str(old_value).lower() in str(current_value).lower()
                                    else:
                                        match_found = str(old_value) in str(current_value)
                                else:
                                    if case_insensitive:
                                        match_found = str(current_value).lower() == str(old_value).lower()
                                    else:
                                        match_found = current_value == old_value

                                if match_found:
                                    files_with_matches.add(filename)
                                    changes_in_file.append((f"attorney[{i}].law_firm", current_value, new_value))

                                    if not dry_run:
                                        attorney["law_firm"] = new_value
                else:
                    # Use the recursive search for other keys (like "title")
                    matches = find_keys_with_value(
                        data,
                        target_key,
                        old_value,
                        case_insensitive=case_insensitive,
                        substring_match=substring_match  # This was missing before
                    )

                    if matches:
                        files_with_matches.add(filename)

                        if not dry_run:
                            # Update all occurrences
                            changes = update_keys_with_value(
                                data,
                                target_key,
                                old_value,
                                new_value,
                                case_insensitive=case_insensitive,
                                substring_match=substring_match  # This was missing before
                            )

                            if changes:
                                changes_in_file.extend(changes)
                        else:
                            # In dry run mode, just record what would be changed
                            changes_in_file.extend([(path, val, new_value) for path, val in matches])

                # If we made changes and not in dry run mode, write back to file
                if changes_in_file and not dry_run:
                    with open(file_path, 'w') as f:
                        json.dump(data, f, indent=4)

                # Record changes for reporting
                if changes_in_file:
                    all_changes[filename] = changes_in_file

            except json.JSONDecodeError:
                console.print(f"[bold red]Error:[/bold red] Invalid JSON in file: {filename}")
            except Exception as e:
                console.print(f"[bold red]Error:[/bold red] Failed to process {filename}: {str(e)}")

            progress.update(task, advance=1)

    return all_changes, files_with_matches


def display_changes(
    changes: Dict[str, List[Tuple[str, Any, Any]]],
    files_with_matches: Set[str],
    target_key: str,
    old_value: Any,
    new_value: Any,
    dry_run: bool = False
):
    """
    Display changes in a formatted table.
    
    Args:
        changes: Dictionary mapping filenames to lists of changes
        files_with_matches: Set of filenames with matches
        target_key: The key that was updated
        old_value: The old value that was replaced
        new_value: The new value that was set
        dry_run: If True, show what would be changed
    """
    if not files_with_matches:
        console.print(f"[bold yellow]No files found with key '{target_key}' having value '{old_value}'[/bold yellow]")
        return
        
    # Create a table to display the changes
    table = Table(title=f"{'Potential ' if dry_run else ''}Changes Made")
    table.add_column("File", style="cyan")
    table.add_column("Path", style="green")
    table.add_column("Old Value", style="yellow")
    table.add_column("New Value", style="blue")
    
    total_changes = 0
    
    for filename in sorted(files_with_matches):
        if filename in changes:
            for path, old, new in changes[filename]:
                table.add_row(filename, path, str(old), str(new))
                total_changes += 1
    
    # Display summary panel
    action = "would be updated" if dry_run else "updated"
    console.print(Panel.fit(
        f"[bold]Total files with matches: {len(files_with_matches)}[/bold]\n"
        f"[bold]Total occurrences {action}: {total_changes}[/bold]",
        title="Summary",
        border_style="green"
    ))
    
    # Display the table with changes
    console.print(table)


def main():
    """Main entry point for the script."""
    args = parse_arguments()
    
    # Convert string values to appropriate types if needed
    try:
        # Try to convert to int or float if it looks like a number
        if args.old_value.isdigit():
            old_value = int(args.old_value)
        elif args.old_value.replace('.', '', 1).isdigit() and args.old_value.count('.') <= 1:
            old_value = float(args.old_value)
        else:
            old_value = args.old_value
            
        if args.new_value.isdigit():
            new_value = int(args.new_value)
        elif args.new_value.replace('.', '', 1).isdigit() and args.new_value.count('.') <= 1:
            new_value = float(args.new_value)
        else:
            new_value = args.new_value
    except (ValueError, AttributeError):
        # Fall back to string values if conversion fails
        old_value = args.old_value
        new_value = args.new_value
    
    # Get substring flag with default value
    substring_match = getattr(args, 'substring', False)
    
    # Display script parameters
    console.print(Panel.fit(
        f"[bold]Date:[/bold] {args.date}\n"
        f"[bold]Key:[/bold] {args.key}\n"
        f"[bold]Old Value:[/bold] {old_value} ({type(old_value).__name__})\n"
        f"[bold]New Value:[/bold] {new_value} ({type(new_value).__name__})\n"
        f"[bold]Dry Run:[/bold] {args.dry_run}\n"
        f"[bold]Case Insensitive:[/bold] {args.case_insensitive}\n"
        f"[bold]Substring Match:[/bold] {substring_match}",
        title="Script Parameters",
        border_style="blue"
    ))
    
    # Process JSON files - ALWAYS do a dry run first
    changes, files_with_matches = process_json_files(
        args.date,
        args.key,
        old_value,
        new_value,
        True,  # Always dry run first
        args.case_insensitive,
        substring_match
    )
    
    # Display changes
    display_changes(
        changes,
        files_with_matches,
        args.key,
        old_value,
        new_value,
        True  # Always show as dry run
    )
    
    # Always ask for confirmation
    if files_with_matches:
        if Confirm.ask("\nDo you want to apply these changes?"):
            # Re-run without dry run
            console.print("[bold green]Applying changes...[/bold green]")
            changes, files_with_matches = process_json_files(
                args.date,
                args.key,
                old_value,
                new_value,
                False,  # Now apply changes
                args.case_insensitive,
                substring_match
            )
            
            # Display the actual changes made
            console.print("[bold green]Changes applied successfully![/bold green]")
            display_changes(
                changes,
                files_with_matches,
                args.key,
                old_value,
                new_value,
                False  # Show as applied
            )
        else:
            console.print("[bold yellow]Operation cancelled. No changes were made.[/bold yellow]")
    else:
        console.print("[bold yellow]No matching files found. No changes to apply.[/bold yellow]")


if __name__ == "__main__":
    main()