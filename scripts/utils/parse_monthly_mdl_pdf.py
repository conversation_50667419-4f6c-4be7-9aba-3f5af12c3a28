import logging
import os
import re

import PyPDF2
import pandas as pd

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')


def extract_text_pypdf2(pdf_path):
    text = ""
    with open(pdf_path, 'rb') as file:
        reader = PyPDF2.PdfReader(file)
        for page_num in range(len(reader.pages)):
            page = reader.pages[page_num]
            text += page.extract_text()
    return text


def parse_pdf_text_to_df(text):
    lines = text.split('\n')
    data = []
    current_district = None
    current_judge = None

    pattern = re.compile(r'^([A-Z]{2,4})\s+(.*?)\s+MDL\s*-(\d+)\s+(.*?)\s+(\d+(?:,\d+)?)\s+(\d+(?:,\d+)?)$')

    for i, line in enumerate(lines[9:], start=9):
        logging.info(f"Processing line {i}: {line}")

        if 'Report Totals:' in line:
            break

        match = pattern.match(line)
        if match:
            district, judge, mdl_num, litigation, pending_actions, historical_actions = match.groups()
            current_district = district
            current_judge = judge
        elif current_district and current_judge:
            parts = line.split('MDL  -')
            if len(parts) == 2:
                judge = current_judge
                mdl_num, rest = parts[1].split(' ', 1)
                litigation, pending_actions, historical_actions = rest.rsplit(' ', 2)
            else:
                logging.warning(f"Skipping line {i} (Invalid format): {line}")
                continue
        else:
            logging.warning(f"Skipping line {i} (No current district or judge): {line}")
            continue

        # Remove commas from numbers
        pending_actions = pending_actions.replace(',', '')
        historical_actions = historical_actions.replace(',', '')

        data.append([current_district, judge, mdl_num, litigation, pending_actions, historical_actions])
        logging.info(f"Processed: {data[-1]}")

    df = pd.DataFrame(data, columns=["district", "judge", "mdl_num", "litigation", "actions_now_pending",
                                     "historical_actions"])
    return df


if __name__ == "__main__":
    pdf_path = 'data/mdls/mdls_by_district/Pending_MDL_Dockets_By_District-September-3-2024.pdf'
    full_pdf_path = os.path.join(os.getcwd(), '..', pdf_path)
    csv_path = '../../src/data/mdls/current_mdl_pending_by_actions_by_court.csv'
    full_csv_path = os.path.join(os.getcwd(), '..', csv_path)

    text_pypdf2 = extract_text_pypdf2(full_pdf_path)
    df = parse_pdf_text_to_df(text_pypdf2)

    new_values = {
        "litigation": "IN RE: E. I. du Pont de Nemours and Company C-8 Personal Injury Litigation",
        "actions_now_pending": "42",
        "historical_actions": "3,739"
    }
    for column, new_value in new_values.items():
        df.loc[df['mdl_num'] == '2433', column] = new_value

    new_values = {
        "litigation": "IN RE: Chiquita Brands International, Inc., Alien Tort Statute and Shareholders Derivative Litigation",
        "actions_now_pending": " 19",
        "historical_actions": "31"
    }
    for column, new_value in new_values.items():
        df.loc[df['mdl_num'] == '1916', column] = new_value

    new_values = {
        "litigation": "IN RE: Fluoroquinolone Products Liability Litigation",
        "actions_now_pending": "4",
        "historical_actions": "1,270"
    }
    for column, new_value in new_values.items():
        df.loc[df['mdl_num'] == '2642', column] = new_value

    df['court_id'] = df['district'].str.lower() + 'd'
    df = df.drop(['district'], axis=1)
    print(df.head())

    df.to_csv(full_csv_path, index=False)
    logging.info(f"CSV file saved to {full_csv_path}")
