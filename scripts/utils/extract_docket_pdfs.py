#!/usr/bin/env python3
"""
Extract and rename PDF files from zip archives in docket directories.

This script processes zip files in data/YYYYMMDD/dockets/ directories,
extracting PDFs and renaming them according to the docket naming convention.
"""

import argparse
import glob
import logging
import os
import sys
from datetime import datetime
from typing import List

# Add parent directory to path to import from src
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from data_transformer.file_handler import FileHandler
from src.utils import extract_from_zip


def validate_date(date_str: str) -> str:
    """Validate date format YYYYMMDD."""
    try:
        datetime.strptime(date_str, '%Y%m%d')
        return date_str
    except ValueError:
        raise argparse.ArgumentTypeError(f"Invalid date format: {date_str}. Use YYYYMMDD format.")


def extract_pdfs_from_zips(docket_dir: str, logger: logging.Logger) -> List[str]:
    """
    Extract PDF files from all zip archives in the docket directory.
    
    Args:
        docket_dir: Directory containing zip files
        logger: Logger instance
        
    Returns:
        List of successfully extracted PDF paths
    """
    extracted_pdfs = []
    
    # Find all zip files in the directory
    zip_files = glob.glob(os.path.join(docket_dir, '*.zip'))
    
    if not zip_files:
        logger.info(f"No zip files found in {docket_dir}")
        return extracted_pdfs
    
    logger.info(f"Found {len(zip_files)} zip files to process")
    
    for zip_path in zip_files:
        base_name = os.path.splitext(os.path.basename(zip_path))[0]
        pdf_path = os.path.join(docket_dir, f"{base_name}.pdf")
        json_path = os.path.join(docket_dir, f"{base_name}.json")
        
        # Check if corresponding JSON exists
        if not os.path.exists(json_path):
            logger.warning(f"Skipping {os.path.basename(zip_path)}: no corresponding JSON file")
            continue
            
        # Check if PDF already exists
        if os.path.exists(pdf_path):
            logger.debug(f"PDF already exists for {os.path.basename(zip_path)}")
            extracted_pdfs.append(pdf_path)
            continue
        
        # Extract PDF from zip
        logger.info(f"Extracting PDF from {os.path.basename(zip_path)}")
        if extract_from_zip(zip_path, pdf_path, logger):
            logger.info(f"Successfully extracted to {os.path.basename(pdf_path)}")
            extracted_pdfs.append(pdf_path)
            
            # Delete the zip file after successful extraction
            try:
                os.remove(zip_path)
                logger.info(f"Deleted zip file: {os.path.basename(zip_path)}")
            except Exception as e:
                logger.warning(f"Could not delete zip file {os.path.basename(zip_path)}: {e}")
        else:
            logger.error(f"Failed to extract PDF from {os.path.basename(zip_path)}")
    
    return extracted_pdfs


def main():
    parser = argparse.ArgumentParser(
        description='Extract and rename PDF files from zip archives in docket directories.'
    )
    parser.add_argument(
        '--date',
        type=validate_date,
        required=True,
        help='Date in YYYYMMDD format'
    )
    parser.add_argument(
        '--log-level',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        default='INFO',
        help='Set the logging level (default: INFO)'
    )
    
    args = parser.parse_args()
    
    # Setup basic logging
    logging.basicConfig(
        level=getattr(logging, args.log_level),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    logger = logging.getLogger(__name__)
    
    # Determine project root and data directory
    project_root = os.environ.get('LEXGENIUS_DATA_DIR')
    if not project_root:
        # Default to relative path from script location
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    
    # Build dockets directory path
    docket_dir = os.path.join(project_root, 'data', args.date, 'dockets')
    
    logger.info(f"Processing zip files for date: {args.date}")
    logger.info(f"Dockets directory: {docket_dir}")
    
    # Check if directory exists
    if not os.path.exists(docket_dir):
        logger.error(f"Dockets directory does not exist: {docket_dir}")
        sys.exit(1)
    
    try:
        # Extract PDFs from zip files
        extracted_pdfs = extract_pdfs_from_zips(docket_dir, logger)
        
        if extracted_pdfs:
            logger.info(f"Successfully extracted {len(extracted_pdfs)} PDF files:")
            for pdf_path in extracted_pdfs:
                logger.info(f"  - {os.path.basename(pdf_path)}")
        else:
            logger.info("No PDFs were extracted.")
            
        # Create configuration for FileHandler cleanup
        config = {
            'iso_date': args.date,
            'directories': {
                'base_dir': project_root
            },
            'project_root': project_root
        }
        
        # Initialize FileHandler for cleanup
        file_handler = FileHandler(config)
        file_handler.cleanup()
        logger.info("Cleanup completed.")
        
    except Exception as e:
        logger.error(f"Error processing zip files: {e}", exc_info=True)
        sys.exit(1)
    
    logger.info("Script completed successfully.")


if __name__ == "__main__":
    main()