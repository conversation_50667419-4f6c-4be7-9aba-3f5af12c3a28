#!/bin/bash

# Check if date parameter is provided
if [ $# -ne 2 ]; then
    echo "Usage: $0 --date YYYYMMDD"
    echo "Example: $0 --date 20230607"
    exit 1
fi

# Parse arguments
if [ "$1" != "--date" ]; then
    echo "Error: Expected --date flag"
    echo "Usage: $0 --date YYYYMMDD"
    exit 1
fi

DATE="$2"
BASE_DIR="data/${DATE}/dockets"

# Verify the directory exists
if [ ! -d "$BASE_DIR" ]; then
    echo "Error: Directory $BASE_DIR does not exist"
    exit 1
fi

# Find all zip files in the directory and its subdirectories
find "$BASE_DIR" -name '*.zip' -type f | while read -r zip_file; do
    # Get the directory of the zip file
    zip_dir=$(dirname "$zip_file")
    # Get the base name (without .zip)
    base_name=$(basename "$zip_file" .zip)
    
    echo "Processing: $zip_file"

    # Create a temporary directory for extraction
    temp_dir=$(mktemp -d)

    # Extract the .zip into the temp directory (flattening paths)
    if unzip -q -j "$zip_file" "*.pdf" -d "$temp_dir" 2>/dev/null; then
        # Find the first PDF in the temp directory
        extracted_pdf=$(find "$temp_dir" -type f -name "*.pdf" | head -n 1)

        if [[ -n "$extracted_pdf" ]]; then
            # Rename and move the PDF to the same directory as the zip
            mv "$extracted_pdf" "${zip_dir}/${base_name}.pdf"
            echo "  → Extracted and renamed to: ${zip_dir}/${base_name}.pdf"
            # Remove the zip file after successful extraction
            rm "$zip_file"
            echo "  → Removed: $zip_file"
        else
            echo "  → No PDF found inside '$zip_file'"
        fi
    else
        echo "  → Error extracting '$zip_file'"
    fi

    # Clean up the temp directory
    rm -rf "$temp_dir"
done

echo "Processing complete!"