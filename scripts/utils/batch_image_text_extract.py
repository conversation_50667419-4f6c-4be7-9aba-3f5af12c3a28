#!/usr/bin/env python3
import asyncio
import logging
import os
from typing import Optional, Dict, List, Tuple, Union, Any
import argparse

import pandas as pd
from colorama import Fore, init
# Rich and Colorama for UI
# REMOVED: from rich import Console  <--- This was incorrect
from rich.console import Console # <--- This is the correct import
from rich.progress import Progress, SpinnerColumn, BarColumn, TimeRemainingColumn

# Library Imports (Assuming these are correct and working)
try:
    from src.lib.config_adapter import load_config
except ImportError as e:
    print(f"Error importing config_adapter: {e}")
    exit(1)

from src.repositories.fb_archive_repository import FBArchiveRepository
from src.lib.llava_vision import LlavaImageExtractor
from src.infrastructure.storage.s3_async import S3AsyncStorage, S3_ACCESS_DENIED_MARKER
except ImportError as e:
    print(f"Error importing library: {e}. Please ensure paths are correct.")
    exit(1)
except FileNotFoundError as e:
    print(f"Error: Required script not found: {e}")
    exit(1)


FORBIDDEN_403_MARKER = "FORBIDDEN_403" # Defined elsewhere

init(autoreset=True) # Assuming initialization happens

# This function was previously imported from src.scripts.add_fb_summary_to_missing
def prepare_dynamodb_item(row_dict: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """
    Prepares a dictionary for insertion into DynamoDB FBAdArchive table.
    
    Args:
        row_dict: Dictionary containing Facebook Ad Archive data, typically from a DataFrame row
        
    Returns:
        Dictionary formatted for DynamoDB insertion, or None if required fields are missing
    """
    module_logger = logging.getLogger(__name__ + ".prepare_dynamodb_item")
    
    try:
        # Required fields for DynamoDB key
        if 'AdArchiveID' not in row_dict or 'StartDate' not in row_dict:
            module_logger.warning(f"Missing required key fields for DynamoDB item: AdArchiveID or StartDate")
            return None
            
        # Create a new dict with only the fields we want to store
        item = {}
        
        # Copy key fields (required)
        item['AdArchiveID'] = str(row_dict['AdArchiveID'])
        item['StartDate'] = str(row_dict['StartDate'])
        
        # Copy other important fields if they exist
        for field in ['AdCreativeId', 'ImageText', 'IsForbidden403', 'EndDate', 'LastUpdated']:
            if field in row_dict and row_dict[field] is not None:
                item[field] = str(row_dict[field]) if field != 'IsForbidden403' else bool(row_dict[field])
        
        # Handle special fields
        if 'AdCreativeId_Clean' in row_dict and row_dict['AdCreativeId_Clean'] is not None:
            item['AdCreativeId'] = str(row_dict['AdCreativeId_Clean'])
            
        if 'AdArchiveID_Clean' in row_dict and row_dict['AdArchiveID_Clean'] is not None:
            item['AdArchiveID'] = str(row_dict['AdArchiveID_Clean'])
            
        # Ensure IsForbidden403 is a boolean
        if 'IsForbidden403' in item:
            item['IsForbidden403'] = bool(item['IsForbidden403'])
        else:
            item['IsForbidden403'] = False
            
        return item
    except Exception as e:
        module_logger.error(f"Error preparing DynamoDB item: {e}", exc_info=True)
        return None

# Initialize UI components
init(autoreset=True)
console = Console()

# Setup logging
logging.basicConfig(level=logging.DEBUG,  # Keep DEBUG for detailed output during testing
                    format='%(asctime)s [%(levelname)s] %(name)s: %(message)s',  # Added logger name
                    datefmt='%H:%M:%S')
logger = logging.getLogger(__name__)  # Get logger for this specific module

# Configuration
# Load Ollama config if available
ollama_config = {}
try:
    from src.lib.llava_vision import LlavaImageExtractor
    ollama_config = LlavaImageExtractor.load_ollama_config() or {}
except:
    pass

# Get performance settings from config or use defaults
perf_config = ollama_config.get('performance', {})
OLLAMA_CONCURRENCY = 6  # Default matching process_image_queue.py
METAL_FLUSH_ALL = os.getenv('METAL_FLUSH_ALL', '1')  # Potentially relevant for Metal performance
OLLAMA_NUM_GPU_LAYERS = -1  # Match DEFAULT_NUM_GPU_LAYERS from llava_vision.py (auto/cpu)
DB_WRITE_CONCURRENCY = perf_config.get('db_write_concurrency', 16)
BATCH_UPDATE_SIZE = 100  # Default matching process_image_queue.py
OLLAMA_KEEP_ALIVE = -1  # Keep model loaded indefinitely - Set via Ollama server env var

# Log configuration
logger.info(f"Performance config: OLLAMA_CONCURRENCY={OLLAMA_CONCURRENCY}, "
           f"DB_WRITE_CONCURRENCY={DB_WRITE_CONCURRENCY}, BATCH_SIZE={BATCH_UPDATE_SIZE}")


# --- REMOVED MetalOptimizer and dynamic_concurrency_controller ---
# Direct manipulation of semaphore._value causes errors.
# Using fixed concurrency is simpler and more stable for now.


# Enhanced Image Text Extraction (Updated Signature)
async def extract_image_text_with_semaphore(
        semaphore: asyncio.Semaphore,
        llava_extractor: LlavaImageExtractor,
        s3_manager: S3AsyncStorage,
        archive_id: str,
        creative_id: str,
        progress: Progress,
        task_id,
        pair_to_indices: Dict[Tuple[str, str], List[int]],
        is_initial_403: bool, # <<<--- Pass info if known 403 from previous runs
        process_403_flag: bool # <<<--- Pass the flag value
) -> Optional[Union[str, str]]: # Return string (text), FORBIDDEN_403_MARKER, or None
    """
    Fetch image from S3 or handle known 403 status, extract text using LLaVA.
    Treats S3 'Not Found' errors the same as 'Access Denied (403)' for skipping purposes.

    Returns:
        - Extracted text (str) on success.
        - FORBIDDEN_403_MARKER (str) if S3 access denied (403) OR if the S3 key is not found.
        - None on other errors (LLaVA fail, unexpected S3 issues, etc.).
    """
    module_logger = logging.getLogger(__name__ + ".extract_image_text")
    async with semaphore:
        start_time = asyncio.get_running_loop().time()
        result_to_return = None # Final result (text, marker, or None)
        log_prefix = f"ArchiveID {archive_id} (CreativeID {creative_id})"
        processing_pair = (archive_id, creative_id)
        num_records_for_pair = len(pair_to_indices.get(processing_pair, []))
        s3_key = f"adarchive/fb/{archive_id}/{creative_id}.jpg"

        # --- Handle known 403s based on flag ---
        if is_initial_403 and not process_403_flag:
            module_logger.info(f"{Fore.MAGENTA}Skipping known 403 pair {log_prefix} as --process-403 is not set.{Fore.RESET}")
            result_to_return = FORBIDDEN_403_MARKER # Still mark as 403 for consistency if needed later
        else:
            # --- S3 Fetch and LLaVA Processing ---
            image_data: Optional[Union[bytes, str]] = None # Can be bytes or S3_ACCESS_DENIED_MARKER
            extracted_text_raw: Optional[str] = None

            try:
                # 1. Fetch image data from S3
                if is_initial_403 and process_403_flag:
                     module_logger.info(f"{Fore.CYAN}Processing known 403 pair {log_prefix} due to --process-403 flag.{Fore.RESET}")
                module_logger.debug(f"Attempting to fetch image from S3: {s3_manager.bucket_name}/{s3_key}")
                loop = asyncio.get_running_loop()
                # Ensure s3_manager.get_content_by_key returns bytes, S3_ACCESS_DENIED_MARKER, or None
                image_data = await loop.run_in_executor(
                    None, s3_manager.get_content_by_key, s3_key
                )

                # 2. Handle S3 Fetch Result
                if image_data == S3_ACCESS_DENIED_MARKER:
                    module_logger.warning(f"{Fore.MAGENTA}S3 Access Denied (403) detected for {log_prefix} (Key: {s3_key}). Marking as forbidden.{Fore.RESET}")
                    result_to_return = FORBIDDEN_403_MARKER # Mark as 403

                # --- CHANGE IS HERE ---
                elif image_data is None:
                    module_logger.warning(f"{Fore.YELLOW}S3 Key not found or fetch failed for {log_prefix} (Key: {s3_key}). Treating as 403/forbidden.{Fore.RESET}")
                    result_to_return = FORBIDDEN_403_MARKER # <<< Treat 'Not Found' as 403
                # --- END OF CHANGE ---

                elif isinstance(image_data, bytes):
                    # 3. Process valid bytes with LLaVA
                    module_logger.debug(f"Successfully fetched {len(image_data)} bytes from S3 for {log_prefix}. Calling LLaVA.")
                    try:
                        # Call the correct method name in LlavaImageExtractor
                        extracted_text_raw = await llava_extractor.extract_text_from_bytes(image_data)
                        module_logger.debug(f"Raw result from llava_extractor (S3 bytes) for {log_prefix}: {extracted_text_raw!r}")

                        # Process LLaVA result
                        if extracted_text_raw is None:
                             module_logger.warning(f"{Fore.YELLOW}LLaVA processing failed or returned None for {log_prefix} (S3 Key: {s3_key}).")
                             result_to_return = None
                        elif isinstance(extracted_text_raw, str):
                            extracted_text = extracted_text_raw.strip()
                            if extracted_text:
                                log_content_snippet = extracted_text[:200] + ('...' if len(extracted_text) > 200 else '')
                                module_logger.info(f"{Fore.GREEN}Success LLaVA {log_prefix} (from S3). Result: {log_content_snippet}")
                                result_to_return = extracted_text # Success! Return the text
                            else:
                                module_logger.warning(f"{Fore.YELLOW}LLaVA query succeeded but returned empty string for {log_prefix} (S3 Key: {s3_key}).")
                                result_to_return = "" # Return empty string if LLaVA returns empty
                        else:
                            module_logger.error(f"Unexpected type from llava_extractor for {log_prefix}: {type(extracted_text_raw)}")
                            result_to_return = None

                    except AttributeError as ae:
                         if 'extract_text_from_bytes' in str(ae):
                             module_logger.critical(f"{Fore.RED}FATAL: LlavaImageExtractor missing 'extract_text_from_bytes'. Cannot process S3 images.", exc_info=True)
                             raise ae # Re-raise critical error
                         else:
                             module_logger.error(f"{Fore.RED}AttributeError during LLaVA processing for {log_prefix}: {ae}", exc_info=True)
                             result_to_return = None
                    except Exception as llava_err:
                        error_type = type(llava_err).__name__
                        module_logger.error(f"{Fore.RED}LLaVA error processing S3 bytes for {log_prefix} ({error_type}): {llava_err}", exc_info=True)
                        result_to_return = None # Mark as standard failure/skip
                else:
                    # Should not happen if S3 manager returns bytes, None, or marker
                    module_logger.error(f"Unexpected data type received from S3 manager for {log_prefix}: {type(image_data)}")
                    result_to_return = None

            except Exception as e:
                # Catch errors during S3 fetch/outer block (excluding LLaVA Attribute Error)
                if isinstance(e, AttributeError) and 'extract_text_from_bytes' in str(e):
                    raise e # Let critical error propagate
                error_type = type(e).__name__
                log_location = "S3 fetch/outer block"
                module_logger.error(f"{Fore.RED}Error during {log_location} for {log_prefix} (S3 Key: {s3_key}) ({error_type}): {e}", exc_info=True)
                # General S3/outer errors still result in None (potential retry later)
                result_to_return = None

        # --- Finalization ---
            finally:
                latency = asyncio.get_running_loop().time() - start_time
                module_logger.debug(f"Total processing latency for {log_prefix}: {latency:.2f}s")
                # Advance progress regardless of outcome for this pair's records
                if num_records_for_pair > 0:
                    progress.update(task_id, advance=num_records_for_pair)
                else:
                     module_logger.warning(f"No records found for pair {processing_pair} in index map, progress not advanced.")
                # This return value (text, FORBIDDEN_403_MARKER, or None) is now used by main
                return result_to_return


# DynamoDB Update Function (Using Individual Updates Instead of Batch Write)
async def write_batch_to_dynamo(
        semaphore: asyncio.Semaphore,
        fb_repository: FBArchiveRepository,
        batch: list[dict],
        batch_number: int
) -> Tuple[int, int]:
    """
    Updates DynamoDB records in batches, using individual update_item calls instead of BatchWriteItem.
    
    This approach provides several advantages:
    1. No 25-item limit that BatchWriteItem has
    2. Better error handling for individual records
    3. Each record update uses conditional expressions to only update changed values
    4. Avoids batch throttling issues that can occur with DynamoDB BatchWriteItem
    
    Args:
        semaphore: Limits concurrent DB operations
        fb_repository: FBArchiveRepository instance for DB operations
        batch: List of record dictionaries to update
        batch_number: Batch identifier for logging
        
    Returns:
        Tuple of (attempted_count, error_count)
    """
    # UPDATED: Use individual record updates instead of batch_insert_items
    module_logger = logging.getLogger(__name__ + ".write_batch")
    attempted_count = len(batch) # Count items we are trying to write
    errors_in_batch = 0
    successful_updates = 0
    
    async with semaphore:
        if not batch:
            module_logger.info(f"DB Write Batch {batch_number} is empty, skipping write.")
            return 0, 0 # Return (0 attempted, 0 errors)

        try:
            module_logger.info(f"Starting individual updates for batch {batch_number} ({attempted_count} items)...")
            loop = asyncio.get_running_loop()
            
            # Process each record individually using the update_item method
            for i, item in enumerate(batch):
                try:
                    # Extract the key components (AdArchiveID and StartDate are the keys for FBAdArchive)
                    if 'AdArchiveID' not in item or 'StartDate' not in item:
                        module_logger.error(f"Record {i} in batch {batch_number} missing key fields: {item}")
                        errors_in_batch += 1
                        continue
                    
                    # Create the primary key for the record
                    key = {
                        'AdArchiveID': item['AdArchiveID'],
                        'StartDate': item['StartDate']
                    }
                    
                    # Create the update data (all fields except primary key)
                    update_data = {k: v for k, v in item.items() if k not in ['AdArchiveID', 'StartDate']}
                    
                    if not update_data:
                        module_logger.warning(f"Record {i} in batch {batch_number} has no data to update")
                        continue
                    
                    # Use async repository method directly
                    success = await fb_repository.update_item(key, update_data)
                    
                    if success:
                        successful_updates += 1
                    else:
                        module_logger.warning(f"Failed to update record {i} in batch {batch_number}: {key}")
                        errors_in_batch += 1
                        
                except Exception as item_err:
                    module_logger.error(f"Error updating record {i} in batch {batch_number}: {item_err}", exc_info=True)
                    errors_in_batch += 1
            
            module_logger.info(f"Completed individual updates for batch {batch_number}. "
                             f"Successful: {successful_updates}, Failed: {errors_in_batch}")
            
        except Exception as batch_err:
            module_logger.error(f"{Fore.RED}--- Critical error processing batch {batch_number} ---{Fore.RESET}", exc_info=True)
            console.print(f"[bold red]Critical error processing batch {batch_number}: {batch_err}[/bold red]")
            # Only count records not yet processed as errors
            errors_in_batch = attempted_count - successful_updates
            
        return attempted_count, errors_in_batch


# Main Function (Updated Logic)
async def main(args: argparse.Namespace): # <<<--- Accept parsed args
    console.print("[bold cyan]M4-Optimized Batch Image Processing[/bold cyan]")
    
    # Handle command line overrides
    global OLLAMA_CONCURRENCY, BATCH_UPDATE_SIZE
    if args.concurrency:
        OLLAMA_CONCURRENCY = args.concurrency
        logger.info(f"Using command line concurrency: {OLLAMA_CONCURRENCY}")
    if hasattr(args, 'batch_size') and args.batch_size:
        BATCH_UPDATE_SIZE = args.batch_size
        logger.info(f"Using command line batch size: {BATCH_UPDATE_SIZE}")
        
    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)
        logger.info("Debug logging enabled.")
    if args.process_403:
        console.print("[bold yellow]--process-403 flag set: Will attempt to process pairs previously marked as 403 Forbidden.[/bold yellow]")
    if args.reprocess_all:
        console.print("[bold yellow]--reprocess-all flag set: Will reprocess ALL images regardless of existing ImageText.[/bold yellow]")

    config = load_config('01/01/1970')

    # Initialize LLaVA Extractor with same params as process_image_queue.py
    # Allow model selection via command line or config
    llava_model = args.model or config.get('llava_model_name', 'llama3.2-vision:11b')
        
    llava_extractor = LlavaImageExtractor(
        ollama_base_url=config.get('ollama_base_url', "http://localhost:11434"),
        model_name=llava_model,
        request_timeout=config.get('llava_timeout', 1200),
        default_temperature=config.get('llava_temperature', 0.1),  # Match process_image_queue.py
        num_gpu_layers=OLLAMA_NUM_GPU_LAYERS,
        use_client_semaphore=True,
        client_semaphore_count=args.concurrency  # Use concurrency setting
    )
    console.print(f"[cyan]Initialized LLaVA with model: {llava_model}[/cyan]")

    # Initialize S3 Manager
    try:
        s3_bucket_name = config.get('s3_image_bucket', 'lexgenius-dockets')
        s3_manager = S3AsyncStorage(config, bucket_name=s3_bucket_name)
        console.print(f"[cyan]Initialized S3 Manager for bucket: {s3_bucket_name}[/cyan]")
    except KeyError as ke:
         console.print(f"[bold red]Failed to initialize S3 Manager: Missing AWS key in config ({ke})[/bold red]")
         await llava_extractor.close_session()
         return
    except Exception as s3_init_err:
        console.print(f"[bold red]Failed to initialize S3 Manager: {s3_init_err}[/bold red]")
        await llava_extractor.close_session()
        return

    ollama_semaphore = asyncio.BoundedSemaphore(OLLAMA_CONCURRENCY)
    db_write_semaphore = asyncio.Semaphore(DB_WRITE_CONCURRENCY)

    # --- Database Scanning and Preprocessing ---
    try:
        fb_repository = FBArchiveRepository()
        console.print("[cyan]Scanning local DynamoDB table...[/cyan]")
        
        async with fb_repository:
            items = await fb_repository.scan_all()
        df = pd.DataFrame(items)
        if df.empty:
            console.print("[yellow]No items found in the local DynamoDB table. Exiting.[/yellow]")
            await llava_extractor.close_session()
            return
        initial_count = len(df)
        console.print(f"[green]Scan complete. Found {initial_count} total records.[/green]")

        console.print("[cyan]Preprocessing data...[/cyan]")
        required_cols = ['AdArchiveID', 'StartDate', 'AdCreativeId']
        missing_req_cols = [col for col in required_cols if col not in df.columns]
        if missing_req_cols:
            console.print(f"[bold red]Fatal Error: Missing required columns: {', '.join(missing_req_cols)}.[/bold red]")
            await llava_extractor.close_session()
            return

        # --- Ensure IsForbidden403 column exists and is boolean ---
        if 'IsForbidden403' not in df.columns:
            df['IsForbidden403'] = False
        else:
            # Convert various potential inputs (None, NaN, strings) to boolean False, keep existing True as True
            df['IsForbidden403'] = df['IsForbidden403'].apply(lambda x: isinstance(x, bool) and x).fillna(False).astype(bool)
        # ---

        # Clean ImageText column
        if 'ImageText' not in df.columns:
            df['ImageText'] = None
        else:
            df['ImageText'] = df['ImageText'].apply(lambda x: str(x) if pd.notna(x) else None)
            values_indicating_reprocessing = ['', 'nan', 'none', 'null', 'na', 'n/a', '<unknown>', '<coroutine object']
            df['ImageText'] = df['ImageText'].replace(values_indicating_reprocessing, None, regex=False)

        # Clean ID columns
        def clean_id(val):
            if pd.isna(val): return None
            s_val = str(val)
            if isinstance(val, float) and val.is_integer(): s_val = str(int(val))
            if s_val == '0' or not s_val.strip(): return None
            return s_val
        df['AdCreativeId_Clean'] = df['AdCreativeId'].apply(clean_id)
        df['AdArchiveID_Clean'] = df['AdArchiveID'].apply(clean_id)

        # --- FILTERING AND TASK IDENTIFICATION ---
        console.print("[cyan]Identifying records potentially needing processing...[/cyan]")
        
        # First, filter based on target phrases
        target_prefixes = ["The image", "This image", "This appears", "This advertisement"]
        
        # Create mask for records that start with target prefixes
        starts_with_target = df['ImageText'].str.startswith(tuple(target_prefixes), na=False)
        
        if args.exclude_target_phrases:
            # Exclude records that start with target phrases
            console.print(f"[cyan]--exclude-target-phrases flag set: Excluding {starts_with_target.sum()} records that start with target phrases.[/cyan]")
            df_filtered = df[~starts_with_target].copy()  # Use NOT operator to exclude
            
            if df_filtered.empty:
                console.print("[bold yellow]No records remaining after excluding target phrases. Exiting.[/bold yellow]")
                await llava_extractor.close_session()
                return
        else:
            # Default behavior: only include records that start with target phrases
            console.print(f"[cyan]Found {starts_with_target.sum()} records with ImageText starting with target phrases.[/cyan]")
            df_filtered = df[starts_with_target].copy()
            
            if df_filtered.empty:
                console.print("[bold yellow]No records found with ImageText starting with target phrases. Exiting.[/bold yellow]")
                await llava_extractor.close_session()
                return
        
        if args.reprocess_all:
            # Force reprocessing of all filtered records
            console.print("[bold yellow]--reprocess-all flag set: Will reprocess ALL filtered images regardless of existing ImageText.[/bold yellow]")
            potential_process_mask = pd.Series([True] * len(df_filtered), index=df_filtered.index)
        else:
            # Initial mask: needs ImageText OR is a known 403 (if --process-403 is set)
            needs_text_mask = df_filtered['ImageText'].isna() | df_filtered['ImageText'].str.contains('<coroutine object', na=False)
            potential_process_mask = needs_text_mask.copy()
            if args.process_403:
                 # If flag is set, also consider rows currently marked as 403
                potential_process_mask |= df_filtered['IsForbidden403']

        df_potential_process = df_filtered[potential_process_mask].copy()

        processed_initially = initial_count - len(df_potential_process)
        if not args.reprocess_all:
            console.print(
                f"[cyan]Filtered out {processed_initially} records with valid text and not marked as 403 (or --process-403 not set).[/cyan]")
        console.print(f"[cyan]Potentially processing {len(df_potential_process)} records.[/cyan]")


        # Ensure both cleaned IDs are present for processing
        original_potential_count = len(df_potential_process)
        df_potential_process.dropna(subset=['AdArchiveID_Clean', 'AdCreativeId_Clean'], inplace=True)
        num_invalid_ids = original_potential_count - len(df_potential_process)
        if num_invalid_ids > 0:
            console.print(
                f"[yellow]Note: {num_invalid_ids} potential records had invalid ArchiveID or CreativeID and were skipped.[/yellow]")

        if df_potential_process.empty:
            console.print("[bold yellow]No records need LLaVA processing after filtering. Exiting.[/bold yellow]")
            await llava_extractor.close_session()
            return

        console.print("[cyan]Identifying unique ArchiveID/CreativeID pairs needing processing...[/cyan]")
        # Group by PAIR on the potentially processable subset
        grouped_by_pair = df_potential_process.groupby(['AdArchiveID_Clean', 'AdCreativeId_Clean'])
        initial_unique_pairs = list(grouped_by_pair.groups.keys()) # Get unique pairs identified so far

        # Create a map from (archive_id, creative_id) tuple to list of original DataFrame indices FOR THE POTENTIAL SUBSET
        pair_to_indices: Dict[Tuple[str, str], List[int]] = {
            pair: group.index.tolist()
            for pair, group in grouped_by_pair
        }

        # --- FILTER TASKS BASED ON --process-403 FLAG ---
        final_tasks_to_run: List[Tuple[str, str]] = []
        skipped_403_pairs = 0
        known_403_map: Dict[Tuple[str, str], bool] = {} # Track if a pair is initially 403

        if not args.process_403:
            console.print("[cyan]Filtering out pairs currently marked as IsForbidden403 (default behaviour)...[/cyan]")
            for pair in initial_unique_pairs:
                indices = pair_to_indices.get(pair, [])
                # Check the original DataFrame 'df' for the 403 status using the indices
                is_forbidden = False
                if indices:
                    # Check if ANY record for this pair in the main df is marked True
                    is_forbidden = df.loc[indices, 'IsForbidden403'].any()

                known_403_map[pair] = is_forbidden # Store initial 403 status

                if is_forbidden:
                    skipped_403_pairs += 1
                    # logger.debug(f"Skipping pair {pair} due to existing IsForbidden403=True")
                else:
                    final_tasks_to_run.append(pair)
            if skipped_403_pairs > 0:
                 console.print(f"[yellow]Skipped {skipped_403_pairs} unique pairs due to existing IsForbidden403=True. Use --process-403 to include them.[/yellow]")
        else:
            # If flag is set, process all initially identified unique pairs
            final_tasks_to_run = initial_unique_pairs
            # Still populate known_403_map
            for pair in initial_unique_pairs:
                 indices = pair_to_indices.get(pair, [])
                 is_forbidden = False
                 if indices:
                    is_forbidden = df.loc[indices, 'IsForbidden403'].any()
                 known_403_map[pair] = is_forbidden
            console.print(f"[cyan]Processing all {len(final_tasks_to_run)} unique pairs, including those marked IsForbidden403.[/cyan]")
        # --- END OF 403 FILTERING ---

        num_unique_pairs_to_process = len(final_tasks_to_run)
        if num_unique_pairs_to_process == 0:
             console.print("[bold yellow]No pairs remaining to process after filtering. Exiting.[/bold yellow]")
             await llava_extractor.close_session()
             return

        # Calculate total records for the progress bar based *only* on pairs being processed
        total_records_for_progress = sum(len(pair_to_indices[pair]) for pair in final_tasks_to_run)

        console.print(
            f"[green]Will process {num_unique_pairs_to_process} unique image pairs, affecting {total_records_for_progress} records.[/green]")

        # --- Process LLaVA and Write to DB in Batches ---
        total_update_errors = 0
        total_successful_extractions = 0
        total_records_updated_in_df = 0
        total_marked_as_403 = 0
        processed_despite_403 = 0

        console.print(
            f"[cyan]Starting LLaVA extraction and DB writes "
            f"(LLaVA Concurrency: {OLLAMA_CONCURRENCY}, DB Concurrency: {DB_WRITE_CONCURRENCY}, Batch Size: {BATCH_UPDATE_SIZE})...[/cyan]"
        )

        with Progress(SpinnerColumn(), "{task.description}", BarColumn(), "{task.percentage:>3.0f}%",
                      TimeRemainingColumn(), "{task.completed}/{task.total} records",
                      console=console) as progress:
            llava_progress_task_id = progress.add_task(
                "[yellow]Processing Records (S3/LLaVA + DB)",
                total=total_records_for_progress # Use filtered count for progress
            )

            db_write_tasks_list = []

            # Iterate through the FINAL list of pairs to process
            for i in range(0, num_unique_pairs_to_process, BATCH_UPDATE_SIZE):
                chunk_pairs = final_tasks_to_run[i: i + BATCH_UPDATE_SIZE]
                logger.info(f"Processing LLaVA chunk {i // BATCH_UPDATE_SIZE + 1} ({len(chunk_pairs)} unique pairs)")

                llava_chunk_tasks = []
                for archive_id, creative_id in chunk_pairs:
                    pair_key = (archive_id, creative_id)
                    is_initial_403 = known_403_map.get(pair_key, False) # Check if it was a known 403
                    task = asyncio.create_task(extract_image_text_with_semaphore(
                        ollama_semaphore,
                        llava_extractor,
                        s3_manager,
                        archive_id,
                        creative_id,
                        progress,
                        llava_progress_task_id,
                        pair_to_indices, # Pass map (needed for progress update inside)
                        is_initial_403,   # Pass known 403 status
                        args.process_403  # Pass flag value
                    ))
                    llava_chunk_tasks.append((pair_key, task)) # Store with pair key

                # Wait for LLaVA tasks *in this chunk* to complete
                await asyncio.gather(*(task for _, task in llava_chunk_tasks))

                # --- Process results and schedule DB write for the completed chunk ---
                items_for_db_batch = []
                indices_updated_in_chunk = set() # Track indices modified in this chunk (text or 403 status)
                chunk_successful_extractions = 0
                chunk_marked_as_403 = 0

                for processing_pair, task in llava_chunk_tasks:
                    try:
                        # Result can be: extracted text (str), FORBIDDEN_403_MARKER, or None
                        result = task.result()
                        indices = pair_to_indices.get(processing_pair, [])
                        is_initial_403_for_pair = known_403_map.get(processing_pair, False)

                        if not indices:
                            logger.error(f"Pair {processing_pair} not found in index map during result processing!")
                            continue # Skip if no indices found

                        if result == FORBIDDEN_403_MARKER:
                            chunk_marked_as_403 += 1
                            # Update DF: Set IsForbidden403 to True for these indices
                            # Only update if the status changed (was False before)
                            needs_update_indices = df.loc[indices][~df.loc[indices, 'IsForbidden403']].index
                            if not needs_update_indices.empty:
                                df.loc[needs_update_indices, 'IsForbidden403'] = True
                                logger.info(f"{Fore.MAGENTA}Marked {len(needs_update_indices)} records for pair {processing_pair} as IsForbidden403=True.{Fore.RESET}")
                                indices_updated_in_chunk.update(needs_update_indices)
                            else:
                                logger.debug(f"Pair {processing_pair} already marked as IsForbidden403=True.")

                        elif isinstance(result, str) and result: # Successful extraction
                            chunk_successful_extractions += 1
                            if is_initial_403_for_pair and args.process_403:
                                processed_despite_403 += 1 # Count pairs processed despite initial 403 status

                            # Update DF: Set ImageText and ensure IsForbidden403 is False
                            current_values = df.loc[indices, ['ImageText', 'IsForbidden403']]
                            needs_update_mask = (
                                (current_values['ImageText'] != result) | # Text is different
                                (current_values['IsForbidden403'])        # Or was previously 403
                            )
                            needs_update_indices = current_values[needs_update_mask].index

                            if not needs_update_indices.empty:
                                df.loc[needs_update_indices, 'ImageText'] = result
                                df.loc[needs_update_indices, 'IsForbidden403'] = False # Mark as not forbidden
                                logger.debug(f"Updated ImageText/IsForbidden403 for {len(needs_update_indices)} records for pair {processing_pair}")
                                indices_updated_in_chunk.update(needs_update_indices)
                            else:
                                 logger.debug(f"Pair {processing_pair}: Extracted text matched existing or no update needed.")


                        else: # Result is None (S3 fail, LLaVA fail, etc.)
                            # Update DF: Ensure IsForbidden403 is False (unless it was already True and we didn't process it)
                            # Only mark as False if it wasn't a 403 we skipped processing
                            if not (is_initial_403_for_pair and not args.process_403):
                                needs_update_indices = df.loc[indices][df.loc[indices, 'IsForbidden403']].index
                                if not needs_update_indices.empty:
                                    df.loc[needs_update_indices, 'IsForbidden403'] = False
                                    logger.debug(f"Marked {len(needs_update_indices)} records for pair {processing_pair} as IsForbidden403=False due to non-403 failure.")
                                    indices_updated_in_chunk.update(needs_update_indices)

                    except Exception as result_err:
                         logger.error(f"Error processing result for pair {processing_pair}: {result_err}", exc_info=True)

                total_successful_extractions += chunk_successful_extractions
                total_marked_as_403 += chunk_marked_as_403
                total_records_updated_in_df += len(indices_updated_in_chunk) # Count records whose row changed

                # --- Prepare and schedule DB write ---
                if indices_updated_in_chunk:
                    logger.debug(
                        f"Preparing DB items for {len(indices_updated_in_chunk)} updated indices in chunk {i // BATCH_UPDATE_SIZE + 1}")
                    for index in indices_updated_in_chunk:
                        if index in df.index:
                            try:
                                row_dict = df.loc[index].to_dict()
                                item = prepare_dynamodb_item(row_dict) # Assumes this handles IsForbidden403
                                if item:
                                    items_for_db_batch.append(item)
                                else:
                                     logger.warning(f"prepare_dynamodb_item returned None for index {index}. Skipping DB add.")
                            except Exception as prep_err:
                                archive_id_err = df.loc[index].get('AdArchiveID_Clean', 'N/A')
                                creative_id_err = df.loc[index].get('AdCreativeId_Clean', 'N/A')
                                logger.error(
                                    f"Error preparing item for index {index} (Pair: {archive_id_err}/{creative_id_err}): {prep_err}",
                                    exc_info=True)
                        else:
                            logger.warning(f"Index {index} no longer found in DataFrame during DB prep.")

                    if items_for_db_batch:
                        logger.info(
                            f"Scheduling DB write for chunk {i // BATCH_UPDATE_SIZE + 1} ({len(items_for_db_batch)} items)")
                        db_task = asyncio.create_task(write_batch_to_dynamo(
                            db_write_semaphore, fb_repository, items_for_db_batch,
                            i // BATCH_UPDATE_SIZE + 1
                        ))
                        db_write_tasks_list.append(db_task)
                    else:
                        logger.info(f"No valid DB items prepared for chunk {i // BATCH_UPDATE_SIZE + 1} after filtering.")
                else:
                    logger.info(f"No records needed update in DataFrame for chunk {i // BATCH_UPDATE_SIZE + 1}")


            # --- Wait for all background DB writes to complete ---
            console.print("[cyan]Waiting for all pending DynamoDB writes to complete...[/cyan]")
            total_records_attempted_write = 0
            total_update_errors = 0
            if db_write_tasks_list:
                db_results: List[Tuple[int, int]] = await asyncio.gather(*db_write_tasks_list)
                for attempted, errors in db_results:
                    total_records_attempted_write += attempted
                    total_update_errors += errors
            total_records_successfully_written = total_records_attempted_write - total_update_errors

        # --- Final Summary ---
        console.print("\n" + "=" * 30 + " Summary " + "=" * 30)
        console.print(f"Total Records Scanned Initially: {initial_count}")
        console.print(f"Records Potentially Needing Processing: {len(df_potential_process)}")
        console.print(f"Unique Image Pairs Initially Identified: {len(initial_unique_pairs)}")
        if not args.process_403:
             console.print(f"Unique Pairs Skipped Due To IsForbidden403: {skipped_403_pairs}")
        console.print(f"Unique Image Pairs Attempted Processing: {num_unique_pairs_to_process}")
        console.print(f"  - Pairs Resulting in S3 Access Denied (403): {total_marked_as_403}")
        if args.process_403:
            console.print(f"  - Pairs Processed Despite Initial 403: {processed_despite_403}")
        console.print(f"  - Pairs with Successful LLaVA Extractions: {total_successful_extractions}")
        console.print(f"Total Records Updated in DataFrame (Text or 403 Status): {total_records_updated_in_df}")
        console.print(f"Total Records Attempted Write to DB: {total_records_attempted_write}")
        console.print(f"Total Records Successfully Written to DB (Estimated): {total_records_successfully_written}")
        if total_update_errors > 0:
            console.print(
                f"[bold red]Errors During DB Write: {total_update_errors} records failed (estimated).[/bold red]")
        else:
            console.print(f"[green]DB Write Errors: 0.[/green]")
        console.print("=" * 70)

    except Exception as e:
        logger.critical("Unhandled exception in main:", exc_info=True)
        console.print(f"\n[bold red]Critical error in main: {e}[/bold red]")
        console.print_exception(show_locals=False, word_wrap=True)
    finally:
        if 'llava_extractor' in locals() and llava_extractor:
            await llava_extractor.close_session()
            logger.info("LLaVA Extractor session closed.")
        console.print("[bold green]--- Script Finished ---[/bold green]")


# --- Argument Parser and Runner ---
if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Batch process FB Ad Archive images using LLaVA, fetching from S3 and handling 403 errors.")
    parser.add_argument(
        '--process-403',
        action='store_true',
        help="Include records previously marked with IsForbidden403=True in the processing run."
    )
    parser.add_argument(
        '--reprocess-all',
        action='store_true',
        help="Force reprocessing of all images, including those with existing ImageText"
    )
    parser.add_argument(
        '--debug',
        action='store_true',
        help="Enable debug logging."
    )
    parser.add_argument(
        '--model',
        type=str,
        choices=['llama32-vision-ocr', 'llama3.2-vision:11b', 'llama3.2-vision:11b-instruct-q4_K_M', 'llava:7b', 'llava:7b-v1.6-mistral-q4_0'],
        help="Vision model to use for image text extraction. Default: llama3.2-vision:11b (best accuracy)"
    )
    parser.add_argument(
        '--concurrency',
        type=int,
        default=6,
        help="Number of concurrent vision model requests (default: 6)"
    )
    parser.add_argument(
        '--batch-size',
        type=int,
        default=100,
        help="Number of image pairs to process per batch (default: 100)"
    )
    parser.add_argument(
        '--exclude-target-phrases',
        action='store_true',
        help="Process all items EXCEPT those with ImageText starting with 'The image', 'This image', 'This appears', or 'This advertisement'"
    )
    parsed_args = parser.parse_args()

    try:
        # Pass parsed arguments to main
        asyncio.run(main(parsed_args))
    except KeyboardInterrupt:
        logger.warning("Script interrupted by user.")
        console.print("\n[bold yellow]Script interrupted by user.[/bold yellow]")
    except AttributeError as fatal_ae:
         # Catch the specific LLaVA method error if it bubbles up
         if 'extract_text_from_bytes' in str(fatal_ae):
              logger.critical(f"Script stopped due to missing method in LlavaImageExtractor: {fatal_ae}", exc_info=True)
              console.print(f"\n[bold red]FATAL ERROR: LlavaImageExtractor class needs the method 'extract_text_from_bytes'.[/bold red]")
         else:
              logger.critical("Top-level script error (AttributeError):", exc_info=True)
              console.print(f"\n[bold red]Critical error: {fatal_ae}[/bold red]")
    except Exception as e:
        logger.critical("Top-level script error:", exc_info=True)
        console.print(f"\n[bold red]Critical error: {e}[/bold red]")
        console.print_exception(show_locals=False, word_wrap=True)