#!/bin/bash

# Set the specific search path
search_path="/Users/<USER>/PycharmProjects/mt_competitive_analysis/src/data"

# Check if a file pattern is provided
if [ $# -eq 0 ]; then
    echo "Usage: $0 <file_pattern>"
    echo "Example: $0 '*_Small_v_Monsanto_Company.pdf'"
    exit 1
fi

# File pattern to search for (provided as first argument)
file_pattern="$1"

# Array to hold all found file paths
found_files=()

# Function to check if a directory name is in YYYYMMDD format
is_date_format() {
    [[ $1 =~ ^[0-9]{8}$ ]]
}

# Use find command to search for files matching the pattern in the specified path
while IFS= read -r -d '' dir; do
    if is_date_format "$(basename "$dir")"; then
        echo "Searching in: $dir"
        while IFS= read -r -d '' file; do
            echo "File found: $file"
            found_files+=("$file")
        done < <(find "$dir" -type f -name "$file_pattern" -print0)
    fi
done < <(find "$search_path" -type d -print0)

# Check if any files were found and display the results
if [ ${#found_files[@]} -eq 0 ]; then
    echo "No files matching '$file_pattern' found in any matching subdirectory of $search_path."
else
    echo "All matching files found:"
    for file in "${found_files[@]}"; do
        echo "$file"
    done
fi
