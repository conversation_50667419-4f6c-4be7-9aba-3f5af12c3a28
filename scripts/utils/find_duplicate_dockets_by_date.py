#!/usr/bin/env python3
"""
Find duplicate docket files across different dates.

This script scans the data directory for docket JSON files matching the pattern
{court_id}_YY_NNNNN*.json and identifies duplicates across different dates.
"""

import argparse
import json
from collections import defaultdict
from datetime import datetime, timed<PERSON><PERSON>
from pathlib import Path
from typing import Dict, List, Set, Tuple

from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.table import Table
from rich.prompt import Confirm

console = Console()


def parse_date(date_str: str) -> datetime:
    """Parse date string in YYYYMMDD format."""
    return datetime.strptime(date_str, "%Y%m%d")


def format_date(date: datetime) -> str:
    """Format date to YYYYMMDD string."""
    return date.strftime("%Y%m%d")


def extract_docket_key(filename: str) -> str:
    """
    Extract the base docket key from a filename.
    
    Examples:
        'njd_24_12345.json' -> 'njd_24_12345'
        'njd_24_12345_complaint.json' -> 'njd_24_12345'
    """
    # Remove .json extension
    base = filename.replace('.json', '')
    
    # Split by underscore and take first 3 parts (court_id, year, case_number)
    parts = base.split('_')
    if len(parts) >= 3:
        return '_'.join(parts[:3])
    return base


def scan_date_directory(date_path: Path) -> Dict[str, List[str]]:
    """
    Scan a date directory for docket files.
    
    Returns:
        Dict mapping docket keys to list of actual filenames
    """
    dockets = defaultdict(list)
    dockets_dir = date_path / "dockets"
    
    if not dockets_dir.exists():
        return dockets
    
    # Find all JSON files matching the pattern
    for json_file in dockets_dir.glob("*_??_????*.json"):
        docket_key = extract_docket_key(json_file.name)
        dockets[docket_key].append(json_file.name)
    
    return dict(dockets)


def find_duplicates(
    start_date: datetime, 
    end_date: datetime, 
    data_dir: Path
) -> Dict[str, Dict[str, List[str]]]:
    """
    Find duplicate dockets across date range.
    
    Returns:
        Dict mapping docket keys to dict of dates and filenames
    """
    all_dockets = defaultdict(lambda: defaultdict(list))
    
    # Scan each date in range
    current_date = start_date
    dates_to_scan = []
    
    while current_date <= end_date:
        dates_to_scan.append(current_date)
        current_date += timedelta(days=1)
    
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console,
    ) as progress:
        task = progress.add_task(
            f"Scanning {len(dates_to_scan)} dates...", 
            total=len(dates_to_scan)
        )
        
        for date in dates_to_scan:
            date_str = format_date(date)
            date_path = data_dir / date_str
            
            if date_path.exists():
                dockets = scan_date_directory(date_path)
                for docket_key, filenames in dockets.items():
                    all_dockets[docket_key][date_str].extend(filenames)
            
            progress.update(task, advance=1)
    
    # Filter to only duplicates (appearing on multiple dates)
    duplicates = {
        key: dates_dict 
        for key, dates_dict in all_dockets.items() 
        if len(dates_dict) > 1
    }
    
    return duplicates


def delete_duplicate_files(duplicates: Dict[str, Dict[str, List[str]]], target_date: str, data_dir: Path):
    """Delete duplicate files from the target date that appeared on previous dates."""
    console.print("\n[bold yellow]Preparing to delete duplicate files from 2025-06-10...[/bold yellow]\n")
    
    files_to_delete = []
    
    for docket_key, dates_dict in duplicates.items():
        sorted_dates = sorted(dates_dict.keys())
        first_date = sorted_dates[0]
        
        # Check if this docket appears on target date and first appeared before target date
        if target_date in dates_dict and first_date < target_date:
            # Get all files for this docket on the target date
            target_files = dates_dict[target_date]
            for filename in target_files:
                # Check for both .json and .zip files
                json_path = data_dir / target_date / "dockets" / filename
                zip_filename = filename.replace('.json', '.zip')
                zip_path = data_dir / target_date / "dockets" / zip_filename
                
                if json_path.exists():
                    files_to_delete.append(json_path)
                if zip_path.exists():
                    files_to_delete.append(zip_path)
    
    if not files_to_delete:
        console.print("[green]No duplicate files to delete.[/green]")
        return
    
    # Show files that will be deleted
    console.print(f"[bold red]Found {len(files_to_delete)} files to delete:[/bold red]\n")
    
    delete_table = Table(show_header=True, header_style="bold magenta")
    delete_table.add_column("File Type", style="cyan")
    delete_table.add_column("File Path", style="white")
    
    for file_path in sorted(files_to_delete)[:20]:  # Show first 20
        file_type = "ZIP" if file_path.suffix == ".zip" else "JSON"
        relative_path = file_path.relative_to(data_dir)
        delete_table.add_row(file_type, str(relative_path))
    
    console.print(delete_table)
    
    if len(files_to_delete) > 20:
        console.print(f"\n[dim]... and {len(files_to_delete) - 20} more files[/dim]")
    
    # Confirm deletion
    if not Confirm.ask("\n[bold red]Do you want to delete these files?[/bold red]"):
        console.print("[yellow]Deletion cancelled.[/yellow]")
        return
    
    # Delete files
    deleted_count = 0
    errors = []
    
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console,
    ) as progress:
        task = progress.add_task("Deleting files...", total=len(files_to_delete))
        
        for file_path in files_to_delete:
            try:
                file_path.unlink()
                deleted_count += 1
            except Exception as e:
                errors.append((file_path, str(e)))
            
            progress.update(task, advance=1)
    
    # Report results
    console.print(f"\n[green]Successfully deleted {deleted_count} files.[/green]")
    
    if errors:
        console.print(f"\n[red]Failed to delete {len(errors)} files:[/red]")
        for file_path, error in errors[:10]:
            console.print(f"  - {file_path}: {error}")
        if len(errors) > 10:
            console.print(f"[dim]  ... and {len(errors) - 10} more errors[/dim]")


def display_results(duplicates: Dict[str, Dict[str, List[str]]], all_dates: List[str], data_dir: Path, delete_duplicates: bool = False):
    """Display duplicate findings in a matrix format and detailed views."""
    if not duplicates:
        console.print("[green]No duplicate dockets found across different dates.[/green]")
        return
    
    # Filter out 7th and 8th from display
    dates_to_exclude = ["20250607", "20250608"]
    filtered_dates = [date for date in all_dates if date not in dates_to_exclude]
    
    # Calculate statistics by date
    date_stats = {}
    for date in all_dates:
        date_stats[date] = {
            'unique': 0,
            'duplicates': 0,
            'total': 0
        }
    
    # Count unique vs duplicate appearances
    for docket_key, dates_dict in duplicates.items():
        sorted_dates = sorted(dates_dict.keys())
        first_date = sorted_dates[0]
        
        for date in sorted_dates:
            if date == first_date:
                date_stats[date]['unique'] += 1
            else:
                date_stats[date]['duplicates'] += 1
            date_stats[date]['total'] += 1
    
    # Calculate total statistics
    total_duplicate_instances = 0
    for docket_key, dates_dict in duplicates.items():
        total_duplicate_instances += len(dates_dict) - 1  # Count duplicates only
    
    console.print(f"[bold]Total unique dockets with duplicates: {len(duplicates)}[/bold]")
    console.print(f"[bold]Total duplicate instances: {total_duplicate_instances}[/bold]\n")
    
    # Display statistics table
    console.print("[bold underline]Statistics by Date:[/bold underline]\n")
    stats_table = Table(show_header=True, header_style="bold magenta")
    stats_table.add_column("Date", style="cyan")
    stats_table.add_column("Unique", justify="right", style="green")
    stats_table.add_column("Duplicates", justify="right", style="red")
    stats_table.add_column("Total", justify="right", style="yellow")
    
    for date in filtered_dates:
        stats = date_stats.get(date, {'unique': 0, 'duplicates': 0, 'total': 0})
        formatted_date = f"{date[:4]}-{date[4:6]}-{date[6:8]}"
        stats_table.add_row(
            formatted_date,
            str(stats['unique']),
            str(stats['duplicates']),
            str(stats['total'])
        )
    
    console.print(stats_table)
    
    # Create matrix view
    console.print("\n[bold underline]Matrix View (o = initial, x = duplicate):[/bold underline]\n")
    
    # Create matrix table
    matrix_table = Table(
        show_header=True,
        header_style="bold magenta"
    )
    
    # Add columns
    matrix_table.add_column("Docket Key", style="cyan", min_width=20)
    for date in filtered_dates:
        # Format date for display (YYYY-MM-DD)
        formatted_date = f"{date[:4]}-{date[4:6]}-{date[6:8]}"
        matrix_table.add_column(formatted_date, justify="center", min_width=12)
    
    # Sort dockets by first appearance date and then by key
    docket_items = []
    for docket_key, dates_dict in duplicates.items():
        sorted_dates = sorted(dates_dict.keys())
        first_date = sorted_dates[0]
        docket_items.append((first_date, docket_key, dates_dict))
    
    docket_items.sort(key=lambda x: (x[0], x[1]))
    
    # Add rows to matrix
    for first_date, docket_key, dates_dict in docket_items:
        row = [docket_key]
        sorted_dates = sorted(dates_dict.keys())
        first_date = sorted_dates[0]
        
        for date in filtered_dates:
            if date in dates_dict:
                if date == first_date:
                    row.append("[green]o[/green]")  # Initial appearance
                else:
                    row.append("[red]x[/red]")      # Duplicate
            else:
                row.append("")  # Not present on this date
        
        matrix_table.add_row(*row)
    
    console.print(matrix_table)
    
    # Group duplicates by first appearance date for summary
    by_first_date = defaultdict(list)
    
    for docket_key, dates_dict in duplicates.items():
        sorted_dates = sorted(dates_dict.keys())
        first_date = sorted_dates[0]
        subsequent_dates = sorted_dates[1:]
        
        by_first_date[first_date].append({
            'docket_key': docket_key,
            'first_date': first_date,
            'subsequent_dates': subsequent_dates,
            'all_dates': sorted_dates,
            'dates_dict': dates_dict
        })
    
    # Display summary by first appearance date
    console.print("\n[bold underline]Summary by First Appearance Date:[/bold underline]")
    
    for first_date in sorted(by_first_date.keys()):
        dockets_on_date = by_first_date[first_date]
        console.print(f"\n[yellow]{first_date}:[/yellow] {len(dockets_on_date)} items first appeared and were duplicated later")
    
    # Calculate duplicates on 6/10 from previous days by court
    target_date = "20250610"
    court_duplicates = defaultdict(int)
    
    for docket_key, dates_dict in duplicates.items():
        sorted_dates = sorted(dates_dict.keys())
        first_date = sorted_dates[0]
        
        # Check if this docket appears on 6/10 and first appeared before 6/10
        if target_date in dates_dict and first_date < target_date:
            # Extract court_id from docket_key (e.g., "njd_24_12345" -> "njd")
            court_id = docket_key.split('_')[0] if '_' in docket_key else docket_key
            court_duplicates[court_id] += 1
    
    # Display court summary
    if court_duplicates:
        console.print("\n[bold underline]Duplicates on 2025-06-10 from Previous Days (by Court):[/bold underline]\n")
        
        court_table = Table(show_header=True, header_style="bold magenta")
        court_table.add_column("Court ID", style="cyan")
        court_table.add_column("Duplicate Count", justify="right", style="red")
        
        # Sort by duplicate count (descending) then by court_id
        sorted_courts = sorted(court_duplicates.items(), key=lambda x: (-x[1], x[0]))
        
        total_court_duplicates = 0
        for court_id, count in sorted_courts:
            court_table.add_row(court_id, str(count))
            total_court_duplicates += count
        
        console.print(court_table)
        console.print(f"\n[bold]Total duplicates on {target_date} from previous days: {total_court_duplicates}[/bold]")
    
    # Handle deletion if requested
    if delete_duplicates:
        delete_duplicate_files(duplicates, target_date, data_dir)


def main():
    parser = argparse.ArgumentParser(
        description="Find duplicate docket files across different dates"
    )
    parser.add_argument(
        "--start-date",
        required=True,
        help="Start date in YYYYMMDD format"
    )
    parser.add_argument(
        "--end-date",
        required=True,
        help="End date in YYYYMMDD format"
    )
    parser.add_argument(
        "--data-dir",
        type=Path,
        default=Path("data"),
        help="Path to data directory (default: data)"
    )
    parser.add_argument(
        "--delete-duplicates",
        action="store_true",
        help="Delete duplicate files from 2025-06-10 that appeared on previous dates"
    )
    
    args = parser.parse_args()
    
    try:
        start_date = parse_date(args.start_date)
        end_date = parse_date(args.end_date)
        
        if start_date > end_date:
            console.print("[red]Error: Start date must be before or equal to end date[/red]")
            return
        
        if not args.data_dir.exists():
            console.print(f"[red]Error: Data directory '{args.data_dir}' does not exist[/red]")
            return
        
        console.print(f"[bold]Scanning for duplicate dockets from {args.start_date} to {args.end_date}[/bold]\n")
        
        duplicates = find_duplicates(start_date, end_date, args.data_dir)
        
        # Generate list of all dates in range
        all_dates = []
        current_date = start_date
        while current_date <= end_date:
            all_dates.append(format_date(current_date))
            current_date += timedelta(days=1)
        
        display_results(duplicates, all_dates, args.data_dir, args.delete_duplicates)
        
    except ValueError as e:
        console.print(f"[red]Error parsing date: {e}[/red]")
        return


if __name__ == "__main__":
    main()