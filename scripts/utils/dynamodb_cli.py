#!/usr/bin/env python3
# !/usr/bin/env python3
# Run from project root directory.
"""
dynamodb_cli.py - Manages synchronization between AWS and local DynamoDB.
                  Provides Full, Incremental, and Missing sync options with parallel processing.
                  
                  Features AWS data caching optimization: When multiple sync operations share
                  the same AWS source table (e.g., option 3: Pacer + PacerDockets), the AWS data
                  is scanned only once and cached for reuse, significantly improving performance.
"""
import asyncio
import contextlib
import logging
import os
import sys
import time
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any, Tuple
from concurrent.futures import ThreadPoolExecutor, as_completed

from tqdm import tqdm

# Botocore exceptions for specific handling

# Rich imports
try:
    from rich.console import Console
    from rich.logging import RichHandler
    from rich.progress import Progress, BarColumn, TextColumn, TimeElapsedColumn, SpinnerColumn
    from rich.table import Table  # Import Table for displaying counts

    rich_available = True
except ImportError:
    rich_available = False


    # Define dummy classes/functions if rich is not available
    class Console:
        def print(self, *args, **kwargs): print(*args)

        def input(self, *args, **kwargs): return input(*args)


    class Progress:
        def __init__(self, *args, **kwargs): pass

        def __enter__(self): return self

        def __exit__(self, exc_type, exc_val, exc_tb): pass

        def add_task(self, *args, **kwargs): return 0

        def update(self, *args, **kwargs): pass

        def stop(self): pass


    class RichHandler(logging.StreamHandler):
        pass  # Basic fallback


    class Table:
        def __init__(self, *args, **kwargs): self._rows = []; self._title = kwargs.get('title', '')

        def add_column(self, *args, **kwargs): pass

        def add_row(self, *args, **kwargs): self._rows.append(args)

        def __str__(self):  # Basic string representation for fallback
            header = f"--- {self._title} ---"
            lines = [header] + [" | ".join(map(str, row)) for row in self._rows]
            return "\n".join(lines)


    # Columns are not strictly needed for fallback
    class BarColumn:
        pass


    class TextColumn:
        pass


    class TimeElapsedColumn:
        pass


    class SpinnerColumn:
        pass

# Configure Logging (using RichHandler if available)
log_handlers: List[logging.Handler]
if rich_available:
    log_handlers = [RichHandler(rich_tracebacks=True, markup=True, console=Console())]  # Enable markup for styling
else:
    log_handlers = [logging.StreamHandler(sys.stdout)]  # Basic fallback

logging.basicConfig(
    level=logging.INFO,  # Default level for CLI output
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s" if not rich_available else "%(message)s",
    datefmt="[%X]",
    handlers=log_handlers
)
cli_logger = logging.getLogger("dynamodb_cli")
console = Console()  # For rich printing (will be dummy if rich not installed)

# --- Relative Imports ---
try:
    # Assuming PROJECT_ROOT is in config
    from src.lib.config_adapter import load_config, PROJECT_ROOT
    # Import base manager and serializer from new infrastructure
    from src.infrastructure.storage.dynamodb_legacy_adapter import DynamoDbBaseManager, json_decimal_serializer
    # Import specific managers if needed for type hinting or specific methods outside base
    
    # Use new async repositories directly
    from src.repositories.pacer_repository import PacerRepository
    from src.repositories.fb_archive_repository import FBArchiveRepository
    _using_new_architecture = True
except ImportError:
    cli_logger.error("Failed relative imports. Ensure script is run correctly relative to module structure.",
                     exc_info=True)
    # Attempt absolute imports as fallback (adjust paths if necessary)
    try:
        # Adjust these based on your actual structure if running directly
        script_dir = os.path.dirname(os.path.abspath(__file__))
        project_root_guess = os.path.dirname(os.path.dirname(script_dir))  # Go up two levels from src/scripts
        sys.path.insert(0, project_root_guess)
        from src.lib.config_adapter import load_config, PROJECT_ROOT
        from src.infrastructure.storage.dynamodb_legacy_adapter import DynamoDbBaseManager, json_decimal_serializer
        from src.repositories.pacer_repository import PacerRepository
        from src.repositories.fb_archive_repository import FBArchiveRepository
        # Create compatibility aliases for old manager names
        PacerManager = PacerRepository
        PacerDocketsManager = PacerRepository  # Both use same repository
        FBAdArchiveManager = FBArchiveRepository
    except ImportError:
        cli_logger.critical("Could not resolve imports. Exiting.")
        sys.exit(1)



async def get_local_table_counts(config: Dict[str, Any], display_name: str, manager_class: type) -> Dict[str, Any]:
    """Helper function to get item count for a single local table."""
    try:
        if hasattr(manager_class, 'count_table_items'):
            # Legacy sync manager
            manager = manager_class(config=config, use_local=True)
            count = manager.count_table_items()
        else:
            # New async repository - use boto3 client directly for counting (simpler than aioboto3)
            import boto3
            
            cli_logger.debug(f"Creating boto3 DynamoDB client for local endpoint")
            
            # Use boto3 client for simpler connection
            dynamodb_client = boto3.client(
                'dynamodb',
                region_name='us-west-2',
                endpoint_url='http://localhost:8000',
                aws_access_key_id='dummy',
                aws_secret_access_key='dummy'
            )
            
            cli_logger.debug(f"Attempting to scan table '{display_name}' on local DynamoDB")
            
            # Count all items by scanning the entire table
            count = 0
            last_evaluated_key = None
            
            while True:
                scan_params = {
                    'TableName': display_name,
                    'Select': 'COUNT'
                }
                if last_evaluated_key:
                    scan_params['ExclusiveStartKey'] = last_evaluated_key
                
                try:
                    response = dynamodb_client.scan(**scan_params)
                    count += response.get('Count', 0)
                    
                    last_evaluated_key = response.get('LastEvaluatedKey')
                    if not last_evaluated_key:
                        break
                except Exception as scan_error:
                    cli_logger.error(f"Failed to scan table '{display_name}': {scan_error}")
                    raise
            
            cli_logger.debug(f"Successfully scanned table '{display_name}', found {count} items")
        return {"table": display_name, "local": count if count != -1 else "[red]Error[/red]"}
    except Exception as e:
        cli_logger.error(f"Error counting items in local table '{display_name}': {e}", exc_info=True)
        return {"table": display_name, "local": "[red]Error[/red]"}


def display_counts(counts: List[Dict[str, Any]]) -> None:
    """Display the item counts for all tables in a formatted table.
    
    Args:
        counts: List of dictionaries containing table names and their item counts
    """
    if not counts:
        console.print("[yellow]No table counts to display.[/yellow]")
        return
        
    # Create a table with columns
    table = Table(show_header=True, header_style="bold magenta")
    table.add_column("Table", style="cyan", no_wrap=True)
    table.add_column("Item Count", justify="right")
    
    # Add rows for each table
    for count_info in counts:
        table_name = count_info["table"]
        count = count_info["local"]
        
        # Format the count with thousands separators if it's a number
        if isinstance(count, int):
            count_str = f"{count:,}"
        else:
            count_str = str(count)
            
        table.add_row(table_name, count_str)
    
    # Print the table
    console.print("\n[bold]Current Item Counts:[/bold]")
    console.print(table)

def get_config(end_date: Optional[str] = None) -> Dict[str, Any]:
    if not end_date:
        end_date = datetime.now().strftime("%m/%d/%Y")
        cli_logger.debug(f"End date default: {end_date}")
    try:
        # Use the new config system
        from src.config_models.loader import load_storage_config
        storage_config = load_storage_config()
        
        # Get date from old config system for compatibility
        old_config = load_config(end_date=end_date)
        if not isinstance(old_config, dict):
            raise TypeError(f"load_config did not return a dict. Type: {type(old_config)}")

        # Create combined config with new AWS credentials format
        config = {
            'aws_access_key': storage_config.aws_access_key_id,
            'aws_secret_key': storage_config.aws_secret_access_key,
            'region_name': storage_config.aws_region,
            'date': old_config.get('date', 'unknown'),
            'iso_date': old_config.get('iso_date', old_config.get('date')),
        }

        cli_logger.info(f"Config loaded for date: {config.get('date', 'unknown')} (Effective End Date: {end_date})")
        if not config.get('aws_access_key') or not config.get('aws_secret_key'):
            raise ValueError("AWS credentials not found in config.")
        if not config.get('region_name'):
            config['region_name'] = 'us-west-2'
            cli_logger.warning("AWS region defaulted to us-west-2.")
        return config
    except FileNotFoundError:
        cli_logger.error("[bold red]Config file (.env) not found.[/bold red]")
        sys.exit(1)
    except ValueError as ve:
        cli_logger.error(f"[bold red]{ve}[/bold red]")
        sys.exit(1)
    except TypeError as te:
        cli_logger.error(f"[bold red]Config loading type error: {te}[/bold red]", exc_info=True)
        sys.exit(1)
    except Exception as e:
        cli_logger.error(f"[bold red]Error loading config: {e}[/bold red]", exc_info=True)
        sys.exit(1)


class DynamoDBIncrementalSync:
    """
    Manages synchronization between AWS and local DynamoDB tables.
    Handles Full, Incremental, and Missing sync modes.
    Can sync between different table types/managers for AWS source and Local target.
    """

    def __init__(self, config: Dict[str, Any],
                 aws_source_table_name: str,
                 AwsSourceManagerClass: type,
                 local_target_table_name: str,
                 LocalTargetManagerClass: type,
                 aws_data_cache: Optional[Dict[str, Any]] = None):
        self.config = config
        self.aws_table_name = aws_source_table_name  # AWS source table
        self.AwsManagerClass = AwsSourceManagerClass  # Manager for AWS source
        self.local_table_name = local_target_table_name  # Local target table
        self.LocalManagerClass = LocalTargetManagerClass  # Manager for Local target
        self.aws_data_cache = aws_data_cache or {}  # Cache for AWS data

        self.logger = logging.getLogger(
            f"DynamoDBSync[{self.aws_table_name}(AWS)->{self.local_table_name}(Local)]")

        self.aws_manager: Optional[Any] = None
        self.local_manager: Optional[Any] = None

        self._initialize_local_manager()  # Initialize local first
        self.logger.debug("AWS Manager initialization deferred.")

        # Sync fields are determined by the AWS SOURCE table and its manager
        self.timestamp_field, self.timestamp_index = self._get_sync_fields(
            self.AwsManagerClass, self.aws_table_name
        )
        self.max_workers = min(os.cpu_count() * 4, 32)
        self.aws_read_batch_size = 100
        self.local_write_batch_size = 25
        self.key_names: List[str] = []  # This will be based on AWS source for "missing" sync

    def _initialize_local_manager(self):
        """Initializes the Local manager for the target local table."""
        self.logger.debug(
            f"Initializing Local manager using {self.LocalManagerClass.__name__} for local table '{self.local_table_name}'.")
        try:
            # Check if this is a new repository (takes storage) or legacy manager (takes config)
            if hasattr(self.LocalManagerClass, 'count_table_items'):
                # Legacy sync manager
                self.local_manager = self.LocalManagerClass(config=self.config, use_local=True)
            else:
                # New async repository - create storage instance
                from src.infrastructure.storage.dynamodb_async import AsyncDynamoDBStorage
                from src.config_models.loader import load_storage_config
                
                storage_config = load_storage_config()
                storage_config_dict = {
                    'aws_access_key_id': 'dummy',  # Required for local DynamoDB
                    'aws_secret_access_key': 'dummy',  # Required for local DynamoDB
                    'aws_region': storage_config.aws_region,
                    'dynamodb_endpoint': 'http://localhost:8000',  # Always use local for sync
                    'dynamodb_max_retries': 15,
                    'dynamodb_base_delay': 1.0,
                    'dynamodb_max_delay': 300.0
                }
                
                # Create a simple config object with attributes
                class SimpleConfig:
                    def __init__(self, config_dict):
                        for key, value in config_dict.items():
                            setattr(self, key, value)
                
                config_obj = SimpleConfig(storage_config_dict)
                
                # Create storage and repository
                storage = AsyncDynamoDBStorage(config_obj)
                self.local_manager = self.LocalManagerClass(storage=storage)
                self.local_storage = storage  # Keep reference for cleanup

            # For repositories, we don't check table names since they're hardcoded in the repository
            self.logger.info(f"Local manager for target table '{self.local_table_name}' initialized.")
        except Exception as e:
            self.logger.critical(f"Failed to initialize Local manager for target table '{self.local_table_name}': {e}",
                                 exc_info=True)
            raise

    def _ensure_aws_manager(self):
        """Initializes the AWS manager for the source AWS table."""
        if self.aws_manager is None:
            self.logger.debug(
                f"Initializing AWS manager using {self.AwsManagerClass.__name__} for source AWS table '{self.aws_table_name}'.")
            try:
                # Check if this is a new repository (takes storage) or legacy manager (takes config)
                if hasattr(self.AwsManagerClass, 'count_table_items'):
                    # Legacy sync manager
                    self.aws_manager = self.AwsManagerClass(config=self.config, use_local=False)
                else:
                    # New async repository - create storage instance
                    from src.infrastructure.storage.dynamodb_async import AsyncDynamoDBStorage
                    from src.config_models.loader import load_storage_config
                    
                    storage_config = load_storage_config()
                    storage_config_dict = {
                        'aws_access_key_id': storage_config.aws_access_key_id,
                        'aws_secret_access_key': storage_config.aws_secret_access_key,
                        'aws_region': storage_config.aws_region,
                        'dynamodb_endpoint': None,  # Use AWS for source
                        'dynamodb_max_retries': 15,
                        'dynamodb_base_delay': 1.0,
                        'dynamodb_max_delay': 300.0
                    }
                    
                    # Create a simple config object with attributes
                    class SimpleConfig:
                        def __init__(self, config_dict):
                            for key, value in config_dict.items():
                                setattr(self, key, value)
                    
                    config_obj = SimpleConfig(storage_config_dict)
                    
                    # Create storage and repository
                    storage = AsyncDynamoDBStorage(config_obj)
                    self.aws_manager = self.AwsManagerClass(storage=storage)
                    self.aws_storage = storage  # Keep reference for cleanup

                self.logger.info(f"AWS manager for source table '{self.aws_table_name}' initialized.")
            except Exception as e:
                self.logger.critical(f"Failed to initialize AWS manager for source table '{self.aws_table_name}': {e}",
                                     exc_info=True)
                raise
        else:
            self.logger.debug(f"AWS manager for source table '{self.aws_table_name}' already initialized.")

    def _get_sync_fields(self, SourceManagerClass: type, source_table_name: str) -> Tuple[str, Optional[str]]:
        """Determines timestamp field and index FOR THE AWS SOURCE TABLE for incremental sync."""
        manager_class_name = SourceManagerClass.__name__ if SourceManagerClass else "None"
        self.logger.debug(f"Getting sync fields for AWS source: {manager_class_name} / Table: {source_table_name}")

        if manager_class_name in ["PacerManager", "PacerRepository"]:
            return "AddedOn", "AddedOn-index"
        elif manager_class_name in ["PacerDocketsManager", "PacerDocketsRepository"]:
            # This case would apply if AWS PacerDockets was the source
            return "AddedOn", "AddedOn-index"
        elif manager_class_name in ["FBAdArchiveManager", "FBArchiveRepository"]:
            return "LastUpdated", "LastUpdated-index"
        elif source_table_name == "DocketActivity":
            return "FilingDate", None
        else:
            # Fallback logic (can be expanded)
            if hasattr(SourceManagerClass, 'DEFAULT_TIMESTAMP_FIELD') and hasattr(SourceManagerClass,
                                                                                  'DEFAULT_TIMESTAMP_INDEX'):
                self.logger.warning(
                    f"Using default sync fields from {SourceManagerClass.__name__} for AWS source table '{source_table_name}'")
                return SourceManagerClass.DEFAULT_TIMESTAMP_FIELD, SourceManagerClass.DEFAULT_TIMESTAMP_INDEX  # type: ignore

            # More robust fallback for Pacer-like tables if specifically used as a generic source
            if source_table_name.startswith("Pacer"):  # General Pacer source
                # Check for 'AddedOn' as a common pattern for Pacer tables
                base_attr_config = getattr(DynamoDbBaseManager, 'attribute_config', {})
                table_attrs = base_attr_config.get(source_table_name, {}).get('include', [])
                if 'AddedOn' in table_attrs:
                    key_conf = getattr(DynamoDbBaseManager, 'key_config', {}).get(source_table_name, {})
                    gsi_index_name = next(
                        (gsi.get('name') for gsi in key_conf.get('gsis', []) if gsi.get('hash_key') == 'AddedOn'), None)
                    if gsi_index_name:
                        self.logger.info(
                            f"Guessed 'AddedOn' and GSI '{gsi_index_name}' for source '{source_table_name}'.")
                        return "AddedOn", gsi_index_name
                    else:
                        self.logger.warning(
                            f"Guessed 'AddedOn' for source '{source_table_name}', but no matching GSI found.")
                        return "AddedOn", None

            raise ValueError(
                f"Incremental sync configuration (timestamp_field, timestamp_index) missing or ambiguous for AWS source table '{source_table_name}' / Manager '{manager_class_name}'. Define explicitly or update _get_sync_fields.")

    async def _perform_full_sync(self) -> None:
        """Performs a full sync by copying all items from AWS to local."""
        # Check cache first
        cache_key = f"full_{self.aws_table_name}"
        if cache_key in self.aws_data_cache:
            aws_items = self.aws_data_cache[cache_key]
            self.logger.info(f"Using cached AWS data for {self.aws_table_name} ({len(aws_items)} items)")
        else:
            self._ensure_aws_manager()
            if not self.aws_manager:
                raise RuntimeError("AWS Manager not initialized")
                
            self.logger.info("Starting FULL sync (all items from AWS to local)")
            
            # Get all items from AWS
            try:
                if hasattr(self.aws_manager, 'scan_table'):
                    # Legacy sync manager
                    scan_response = self.aws_manager.scan_table()
                    aws_items = list(scan_response) if scan_response else []
                else:
                    # New async repository - need to use storage context
                    async with self.aws_storage:
                        aws_items = await self.aws_manager.scan_all()
                        
                self.logger.info(f"Retrieved {len(aws_items)} items from AWS")
                
                # Cache the data for reuse
                self.aws_data_cache[cache_key] = aws_items
                
            except Exception as e:
                self.logger.error(f"Error scanning AWS table: {str(e)}", exc_info=True)
                raise
        
        if not self.local_manager:
            raise RuntimeError("Local Manager not initialized")
            
        if not aws_items:
            self.logger.warning("No items found in AWS table")
            return
            
        # Write all items to local
        total_items = len(aws_items)
        self.logger.info(f"Starting to write {total_items} items to local table {self.local_table_name}")
        
        try:
            if hasattr(self.local_manager, 'batch_insert_items'):
                # Legacy sync manager
                success_count, failed_count = self.local_manager.batch_insert_items(
                    records=aws_items,
                    batch_size=self.local_write_batch_size,
                    disable_progress=False
                )
                self.logger.info(f"Full sync completed. Success: {success_count}, Failed: {failed_count}")
            else:
                # New async repository - use storage context and individual puts for now
                async with self.local_storage:
                    success_count = 0
                    failed_count = 0
                    for item in aws_items:
                        try:
                            await self.local_manager.add_or_update_record(item)
                            success_count += 1
                        except Exception as e:
                            self.logger.error(f"Failed to write item: {e}")
                            failed_count += 1
                    self.logger.info(f"Full sync completed. Success: {success_count}, Failed: {failed_count}")
            
        except Exception as e:
            self.logger.error(f"Error during full sync: {str(e)}", exc_info=True)
            raise

    async def _perform_incremental_sync(self) -> None:
        """Performs an incremental sync by copying only new/updated items."""
        if not self.local_manager:
            raise RuntimeError("Local Manager not initialized")

        self.logger.info(
            f"Starting INCREMENTAL sync using field: '{self.timestamp_field}' on index '{self.timestamp_index}'")

        try:
            # Determine the latest local timestamp by iterating backwards
            latest_local_timestamp_value = None
            self.logger.info(
                f"Attempting to find latest local timestamp for '{self.timestamp_field}' by iterating backwards from today.")

            # Iterate backwards from today for up to 366 days (today + 365 prior days)
            for i in range(366):
                date_to_check = datetime.now() - timedelta(days=i)
                date_to_check_str = date_to_check.strftime('%Y%m%d')

                # Query local manager for items on this specific date_to_check_str
                # using the available repository methods
                # We only need to know if *any* item exists for this date.
                items_on_this_day = []
                try:
                    # For FBArchiveRepository, use LastUpdated field with query_by_last_updated_range
                    if hasattr(self.local_manager, 'query_by_last_updated_range') and self.timestamp_field == 'LastUpdated':
                        async with self.local_storage:
                            items_on_this_day = await self.local_manager.query_by_last_updated_range(date_to_check_str)
                            items_on_this_day = list(items_on_this_day)[:1]
                    elif hasattr(self.local_manager, 'query_by_added_on_range'):
                        # Use storage context for the query
                        async with self.local_storage:
                            # Use the new repository method
                            items_on_this_day = await self.local_manager.query_by_added_on_range(
                                start_date=date_to_check_str,
                                end_date=date_to_check_str
                            )
                            # Convert to list and limit to 1 item
                            items_on_this_day = list(items_on_this_day)[:1]
                    elif hasattr(self.local_manager, 'query_by_date'):
                        # Legacy sync manager method
                        items_on_this_day = list(self.local_manager.query_by_date(
                            start_date_str=date_to_check_str,
                            end_date_str=date_to_check_str,  # Query for a single day
                            date_field=self.timestamp_field,
                            index_name=self.timestamp_index,  # Pass the GSI name
                            limit=1  # We only need one item to confirm data exists for this day
                        ))
                    else:
                        # Fallback: use storage context to query directly
                        async with self.local_storage:
                            # Try to find any record with the specific AddedOn date
                            all_items = await self.local_manager.scan_all()
                            items_on_this_day = [item for item in all_items 
                                               if item.get(self.timestamp_field) == date_to_check_str][:1]
                except Exception as query_error:
                    self.logger.warning(f"Failed to query date {date_to_check_str}: {query_error}")
                    items_on_this_day = []

                if items_on_this_day:
                    # Ensure the item's timestamp field matches the day we queried for
                    # This is a sanity check; it should match if query_by_date works correctly.
                    item_timestamp = items_on_this_day[0].get(self.timestamp_field)
                    if item_timestamp == date_to_check_str:
                        latest_local_timestamp_value = date_to_check_str
                        self.logger.info(
                            f"Found latest local timestamp by iterating backwards: {latest_local_timestamp_value}")
                        break  # Exit loop once the most recent day with data is found
                    else:
                        # This indicates a potential mismatch or issue in data/query logic
                        self.logger.warning(
                            f"Item found on {date_to_check_str} but its '{self.timestamp_field}' field is '{item_timestamp}'. "
                            f"This might indicate an issue. Continuing search for a direct match.")

            if not latest_local_timestamp_value:
                self.logger.info(
                    f"No local items with timestamp field '{self.timestamp_field}' found after checking 366 days.")

            self.logger.info(f"Latest local {self.timestamp_field} determined as: {latest_local_timestamp_value}")

            if latest_local_timestamp_value is None:
                self.logger.warning(
                    f"No local data with a valid timestamp for '{self.timestamp_field}' found, performing full sync instead.")
                await self._perform_full_sync()
                return

            # Query AWS for items from latest_local_timestamp_value up to now.
            # Start querying AWS from the day of the latest_local_timestamp_value to catch any updates on that day.
            
            # Check cache first
            cache_key = f"incremental_{self.aws_table_name}_{latest_local_timestamp_value}"
            if cache_key in self.aws_data_cache:
                aws_items_to_sync = self.aws_data_cache[cache_key]
                self.logger.info(f"Using cached AWS incremental data for {self.aws_table_name} ({len(aws_items_to_sync)} items)")
            else:
                self._ensure_aws_manager()
                if not self.aws_manager:
                    raise RuntimeError("AWS Manager not initialized")
                    
                aws_items_to_sync = []
                current_aws_sync_date = datetime.strptime(latest_local_timestamp_value, '%Y%m%d')
                end_aws_sync_date = datetime.now()

                self.logger.info(
                    f"Querying AWS for items from {current_aws_sync_date.strftime('%Y%m%d')} to {end_aws_sync_date.strftime('%Y%m%d')}.")

                date_iterator = current_aws_sync_date
                while date_iterator.date() <= end_aws_sync_date.date():
                    date_str_for_aws_query = date_iterator.strftime('%Y%m%d')
                    self.logger.debug(
                        f"Querying AWS for {self.timestamp_field} = {date_str_for_aws_query} using index '{self.timestamp_index}'")

                    daily_aws_items = []
                    try:
                        # For FBArchiveRepository, use LastUpdated field with query_by_last_updated_range
                        if hasattr(self.aws_manager, 'query_by_last_updated_range') and self.timestamp_field == 'LastUpdated':
                            async with self.aws_storage:
                                daily_aws_items = await self.aws_manager.query_by_last_updated_range(date_str_for_aws_query)
                                daily_aws_items = list(daily_aws_items)
                        elif hasattr(self.aws_manager, 'query_by_added_on_range'):
                            # Use the new repository method for AWS side
                            async with self.aws_storage:
                                daily_aws_items = await self.aws_manager.query_by_added_on_range(
                                    start_date=date_str_for_aws_query,
                                    end_date=date_str_for_aws_query
                                )
                                daily_aws_items = list(daily_aws_items)
                        elif hasattr(self.aws_manager, 'query_items') and self.timestamp_index:  
                            # Legacy sync manager with GSI
                            aws_query_key_conditions = {self.timestamp_field: date_str_for_aws_query}
                            daily_aws_items = list(self.aws_manager.query_items(
                                key_conditions=aws_query_key_conditions,
                                index_name=self.timestamp_index
                            ))
                        else:  # Fallback to scan with filter if no GSI (less efficient)
                            self.logger.warning(
                                f"No efficient query method available for '{self.timestamp_field}'. Falling back to scan for AWS data on {date_str_for_aws_query}. This can be slow.")
                            if hasattr(self.aws_manager, 'scan_table'):
                                scan_filter_aws = {
                                    self.timestamp_field: {'AttributeValueList': [date_str_for_aws_query],
                                                           'ComparisonOperator': 'EQ'}
                                }
                                daily_aws_items = list(self.aws_manager.scan_table(filter_expression=scan_filter_aws))
                            else:
                                # Use storage context to scan directly
                                async with self.aws_storage:
                                    all_items = await self.aws_manager.scan_all()
                                    daily_aws_items = [item for item in all_items 
                                                     if item.get(self.timestamp_field) == date_str_for_aws_query]
                    except Exception as aws_query_error:
                        self.logger.warning(f"Failed to query AWS for date {date_str_for_aws_query}: {aws_query_error}")
                        daily_aws_items = []

                    if daily_aws_items:
                        self.logger.debug(
                            f"Found {len(daily_aws_items)} items in AWS for {self.timestamp_field}={date_str_for_aws_query}.")
                        aws_items_to_sync.extend(daily_aws_items)

                    date_iterator += timedelta(days=1)
                
                # Cache the data for reuse
                self.aws_data_cache[cache_key] = aws_items_to_sync

            self.logger.info(f"Found {len(aws_items_to_sync)} new/updated items in AWS to sync.")

            if not aws_items_to_sync:
                self.logger.info("No new or updated items found in AWS based on the determined local timestamp.")
                return

            # Write new/updated items to local 
            if hasattr(self.local_manager, 'batch_insert_items'):
                # Legacy sync manager
                success_count, failed_count = self.local_manager.batch_insert_items(
                    records=aws_items_to_sync,  # Use the correctly fetched items
                    batch_size=self.local_write_batch_size,
                    disable_progress=False
                )
            else:
                # New async repository - use storage context and individual puts
                async with self.local_storage:
                    success_count = 0
                    failed_count = 0
                    for item in aws_items_to_sync:
                        try:
                            await self.local_manager.add_or_update_record(item)
                            success_count += 1
                        except Exception as e:
                            self.logger.error(f"Failed to write item: {e}")
                            failed_count += 1

            self.logger.info(f"Incremental sync completed. Success: {success_count}, Failed: {failed_count}")

        except Exception as e:
            self.logger.error(f"Error during incremental sync: {str(e)}", exc_info=True)
            raise

    async def _perform_missing_sync(self) -> None:
        """Performs a sync of only items that exist in AWS but not in local."""
        if not self.local_manager:
            raise RuntimeError("Local Manager not initialized")
            
        self.logger.info("Starting MISSING ITEMS sync")
        
        try:
            # Check cache first for full data (we'll extract keys from it)
            cache_key = f"full_{self.aws_table_name}"
            if cache_key in self.aws_data_cache:
                aws_items = self.aws_data_cache[cache_key]
                self.logger.info(f"Using cached AWS data for missing sync ({len(aws_items)} items)")
                
                # Get key attribute names
                if aws_items:
                    # Determine key attributes based on table name
                    if self.aws_table_name in ['Pacer', 'PacerDockets']:
                        key_attr_names = ['FilingDate', 'DocketNum']
                    elif self.aws_table_name == 'FBAdArchive':
                        key_attr_names = ['AdArchiveID', 'StartDate']
                    else:
                        # Fallback: try to infer from first item
                        if aws_items:
                            key_attr_names = list(aws_items[0].keys())[:2]  # Assume first 2 are keys
                        else:
                            self.logger.error(f"Cannot determine key attributes for table {self.aws_table_name}")
                            return
                else:
                    self.logger.warning("No items in cache to determine keys")
                    return
            else:
                self._ensure_aws_manager()
                if not self.aws_manager:
                    raise RuntimeError("AWS Manager not initialized")
                    
                # Determine key attributes based on table name
                if self.aws_table_name in ['Pacer', 'PacerDockets']:
                    key_attr_names = ['FilingDate', 'DocketNum']
                elif self.aws_table_name == 'FBAdArchive':
                    key_attr_names = ['AdArchiveID', 'StartDate']
                else:
                    self.logger.error(f"Unknown table {self.aws_table_name} - cannot determine key attributes")
                    return
                
                # Get all AWS items (we need full data anyway for the missing items)
                if hasattr(self.aws_manager, 'scan_table'):
                    # Legacy sync manager
                    aws_items = list(self.aws_manager.scan_table())
                else:
                    # New async repository
                    async with self.aws_storage:
                        aws_items = await self.aws_manager.scan_all()
            
            # Create a set of key tuples for AWS items
            aws_keys = set(
                tuple(item[attr] for attr in key_attr_names)
                for item in aws_items
            )
            
            self.logger.info(f"Found {len(aws_keys)} items in AWS")
            
            # Get local item keys in the same way
            if hasattr(self.local_manager, 'scan_table'):
                # Legacy sync manager
                local_items = self.local_manager.scan_table(
                    projection_expression=", ".join(key_attr_names)
                )
            else:
                # New async repository - need to implement scan with projection
                async with self.local_storage:
                    local_items = await self.local_manager.scan_all()
            
            # Create a set of key tuples for local items
            local_keys = set(
                tuple(item[attr] for attr in key_attr_names)
                for item in local_items
            )
            
            self.logger.info(f"Found {len(local_keys)} items in local")
            
            # Find keys in AWS but not in local
            missing_key_tuples = aws_keys - local_keys
            
            if not missing_key_tuples:
                self.logger.info("No missing items found in local")
                return
                
            self.logger.info(f"Found {len(missing_key_tuples)} missing items in local")
            
            # Convert key tuples back to dictionaries for batch_get
            missing_keys = [
                {attr: key_tuple[i] for i, attr in enumerate(key_attr_names)}
                for key_tuple in missing_key_tuples
            ]
            
            # Batch get the missing items from AWS
            missing_items = []
            
            # If we have full data cached, extract missing items from there
            if cache_key in self.aws_data_cache and cache_key == f"full_{self.aws_table_name}":
                # We have the full data, so extract missing items directly
                full_aws_items = self.aws_data_cache[cache_key]
                for item in full_aws_items:
                    item_key_tuple = tuple(item[attr] for attr in key_attr_names)
                    if item_key_tuple in missing_key_tuples:
                        missing_items.append(item)
                self.logger.info(f"Extracted {len(missing_items)} missing items from cached data")
            else:
                # Need to fetch from AWS - get all items and filter
                if hasattr(self.aws_manager, 'scan_table'):
                    # Legacy sync manager
                    full_aws_items = list(self.aws_manager.scan_table())
                else:
                    # New async repository
                    async with self.aws_storage:
                        full_aws_items = await self.aws_manager.scan_all()
                
                # Extract missing items from full data
                for item in full_aws_items:
                    item_key_tuple = tuple(item[attr] for attr in key_attr_names)
                    if item_key_tuple in missing_key_tuples:
                        missing_items.append(item)
                self.logger.info(f"Extracted {len(missing_items)} missing items from AWS scan")
            
            if not missing_items:
                self.logger.warning("No items found for the missing keys")
                return
                
            # Write missing items to local
            if hasattr(self.local_manager, 'batch_insert_items'):
                # Legacy sync manager
                success_count, failed_count = self.local_manager.batch_insert_items(
                    records=missing_items,
                    batch_size=self.local_write_batch_size,
                    disable_progress=False
                )
            else:
                # New async repository - use storage context and individual puts
                async with self.local_storage:
                    success_count = 0
                    failed_count = 0
                    for item in missing_items:
                        try:
                            await self.local_manager.add_or_update_record(item)
                            success_count += 1
                        except Exception as e:
                            self.logger.error(f"Failed to write item: {e}")
                            failed_count += 1
                
            self.logger.info(f"Missing items sync completed. Success: {success_count}, Failed: {failed_count}")
            
        except Exception as e:
            self.logger.error(f"Error during missing items sync: {str(e)}", exc_info=True)
            raise


async def main():
    console.print("\n[bold blue]DynamoDB AWS <> Local Synchronization Tool[/bold blue]")
    console.print("========================================")

    try:
        # --- Configuration ---
        end_date_override = sys.argv[1] if len(sys.argv) > 1 else None
        if end_date_override:
            try:
                datetime.strptime(end_date_override, "%m/%d/%Y")
                console.print(f"Using command-line end date: {end_date_override}")
            except ValueError:
                console.print(f"[yellow]Invalid date format '{end_date_override}'. Using default (today).[/yellow]")
                end_date_override = None
        config = get_config(end_date_override)
        manager_config = {
            'aws_access_key': config.get('aws_access_key'),
            'aws_secret_key': config.get('aws_secret_key'),
            'iso_date': config.get('iso_date', config.get('date')),
            'region_name': config.get('region_name', 'us-west-2'),
            'date': config.get('date'),
        }
        manager_config = {k: v for k, v in manager_config.items() if v is not None}
        
        # Cache for AWS data to avoid duplicate scans
        aws_data_cache = {}

        # --- Define Tables and Managers ---
        # Format: display_name, DefaultLocalTargetManagerClass, DefaultAwsSourceTableName, DefaultAwsSourceManagerClass
        table_map = {
            "1": ("Pacer", PacerRepository, "Pacer", PacerRepository),
            "2": ("PacerDockets", PacerRepository, "PacerDockets", PacerRepository),
            # Assumes AWS PacerDockets for standalone sync
            "3": ("Pacer + PacerDockets (AWS Pacer -> Local Pacer & Local PacerDockets)", None, None, None),
            # Special combined option
            "4": ("FBAdArchive", FBArchiveRepository, "FBAdArchive", FBArchiveRepository)
        }

        tables_for_count_display = [
            ("Pacer", PacerRepository),
            ("PacerDockets", PacerRepository),
            ("FBAdArchive", FBArchiveRepository)
        ]

        console.print("\n[bold]Fetching initial local table counts...[/bold]")
        all_counts = []
        
        # Use async execution for table counts with proper timeout handling
        for display_name, m_class in tables_for_count_display:
            try:
                # Temporarily enable DEBUG logging for connection diagnostics
                original_level = cli_logger.level
                cli_logger.setLevel(logging.DEBUG)
                
                result = await asyncio.wait_for(
                    get_local_table_counts(manager_config, display_name, m_class),
                    timeout=10.0  # 10 second timeout per table
                )
                all_counts.append(result)
                
                # Restore original logging level
                cli_logger.setLevel(original_level)
            except asyncio.TimeoutError:
                cli_logger.setLevel(original_level)
                cli_logger.warning(f"Timeout getting local counts for '{display_name}' (likely DynamoDB Local not running)")
                all_counts.append({"table": display_name, "local": "[yellow]Timeout[/yellow]"})
            except Exception as e:
                cli_logger.setLevel(original_level)
                cli_logger.error(f"Error getting local counts for '{display_name}': {e}")
                all_counts.append({"table": display_name, "local": "[red]Error[/red]"})
        
        all_counts.sort(key=lambda x: x["table"])
        display_counts(all_counts)

        console.print("\n[bold]Available Tables for Sync:[/bold]")
        for key, (name, _, _, _) in table_map.items():
            console.print(f"  {key}. {name}")
        console.print("  q. Quit")

        # Stores tuples of: (LocalTargetTableName, LocalTargetManagerClass, AwsSourceTableName, AwsSourceManagerClass)
        selected_sync_configurations: List[Tuple[str, type, str, type]] = []

        table_choice_input = console.input("\nSelect table number to sync (or 'q' to quit): ").strip()
        if table_choice_input == "3":
            # Config for AWS Pacer -> Local Pacer
            selected_sync_configurations.append(("Pacer", PacerRepository, "Pacer", PacerRepository))
            # Config for AWS Pacer -> Local PacerDockets (both use same repository)
            selected_sync_configurations.append(("PacerDockets", PacerRepository, "Pacer", PacerRepository))
            console.print(
                f"Selected: Combined sync. Target 1: Local [cyan]Pacer[/cyan] from AWS [cyan]Pacer[/cyan]. Target 2: Local [cyan]PacerDockets[/cyan] from AWS [cyan]Pacer[/cyan].")
        elif table_choice_input.lower() == 'q':
            console.print("\n[yellow]Exiting DynamoDB sync tool.[/yellow]")
            return
        elif table_choice_input in table_map:
            _, local_mgr_cls, aws_tbl_name, aws_mgr_cls = table_map[table_choice_input]
            # For single table syncs, the local target table name is the same as the AWS source table name by default.
            # The display name is used as the local target table name.
            local_target_table_name_for_single_sync = table_map[table_choice_input][0]

            if local_mgr_cls is None or aws_mgr_cls is None:  # Should not happen for non-combined valid options
                console.print("[red]Invalid table configuration in table_map. Exiting.[/red]")
                sys.exit(1)
            selected_sync_configurations.append(
                (local_target_table_name_for_single_sync, local_mgr_cls, aws_tbl_name, aws_mgr_cls))
            console.print(
                f"Selected: Local table [cyan]{local_target_table_name_for_single_sync}[/cyan] from AWS table [cyan]{aws_tbl_name}[/cyan].")
        else:
            console.print("[red]Invalid table selection. Exiting.[/red]")
            sys.exit(1)

        console.print("\n[bold]Sync Options:[/bold]")
        sync_map = {
            "1": ("full", "Full Download (Recreates Local Table)"),
            "2": ("incremental", "Incremental Update (Fetches New/Updated)"),
            "3": ("missing", "Missing Sync (Compares Keys, Fetches Missing)")
        }
        for key, (_, desc) in sync_map.items():
            console.print(f"  {key}. {desc}")
        console.print("  q. Quit")
        while True:
            sync_choice_input = console.input("\nSelect sync type number (or 'q' to quit): ").strip()
            if sync_choice_input.lower() == 'q':
                console.print("\n[yellow]Exiting DynamoDB sync tool.[/yellow]")
                return
            elif sync_choice_input in sync_map:
                sync_type_selected, sync_desc_selected = sync_map[sync_choice_input]
                break
            console.print("[red]Invalid selection. Please enter the number or 'q' to quit.[/red]")

        overall_sync_start_time = time.time()
        any_sync_failed = False
        
        # Check if multiple sync configs share the same AWS source
        aws_source_counts = {}
        for _, _, aws_src, _ in selected_sync_configurations:
            aws_source_counts[aws_src] = aws_source_counts.get(aws_src, 0) + 1
        
        # Display cache optimization notice if applicable
        for aws_src, count in aws_source_counts.items():
            if count > 1:
                console.print(f"\n[green]✓ Cache optimization enabled:[/green] AWS table [cyan]{aws_src}[/cyan] will be scanned once and reused for {count} syncs")

        for config_index, (
        local_target_tbl_name, local_target_mgr_cls, aws_source_tbl_name, aws_source_mgr_cls) in enumerate(
                selected_sync_configurations):
            console.rule(
                f"[bold white]Sync Task {config_index + 1}/{len(selected_sync_configurations)}: Local Target='{local_target_tbl_name}', AWS Source='{aws_source_tbl_name}'[/bold white]",
                style="blue")
            console.print(
                f"\nInitializing sync for local table [cyan]'{local_target_tbl_name}'[/cyan] (Manager: {local_target_mgr_cls.__name__}) "
                f"from AWS table [cyan]'{aws_source_tbl_name}'[/cyan] (Manager: {aws_source_mgr_cls.__name__}) "
                f"using [magenta]{sync_desc_selected}[/magenta]...")

            updater_instance: Optional[DynamoDBIncrementalSync] = None
            try:
                updater_instance = DynamoDBIncrementalSync(
                    config=manager_config,
                    aws_source_table_name=aws_source_tbl_name,
                    AwsSourceManagerClass=aws_source_mgr_cls,
                    local_target_table_name=local_target_tbl_name,
                    LocalTargetManagerClass=local_target_mgr_cls,
                    aws_data_cache=aws_data_cache  # Pass the shared cache
                )
                console.print("Sync manager initialized.")
            except Exception as init_e_loop:
                console.print(f"[bold red]Initialization failed for this sync task: {init_e_loop}[/bold red]")
                cli_logger.error(f"Initialization failed for sync task: {init_e_loop}", exc_info=True)
                any_sync_failed = True
                continue

            aws_display_name_loop = getattr(getattr(updater_instance, 'aws_manager', None), 'table_name',
                                            aws_source_tbl_name)
            local_display_name_loop = getattr(getattr(updater_instance, 'local_manager', None), 'table_name',
                                              local_target_tbl_name)

            console.print(
                f"\n[bold yellow]Action:[/bold yellow] Perform [magenta]{sync_type_selected}[/magenta] sync from AWS [cyan]'{aws_display_name_loop}'[/cyan] to Local [cyan]'{local_display_name_loop}'[/cyan].")

            if sync_type_selected == 'full':
                console.print(
                    f"[bold orange1]WARNING:[/bold orange1] Full sync will affect the local table "
                    f"'{local_display_name_loop}'. Depending on manager, it might be deleted and recreated or cleared.")

            proceed_sync = 'y'
            if config_index == 0:  # Only ask for confirmation for the first task or if only one task
                proceed_sync = console.input(
                    f"Proceed with [magenta]{sync_type_selected}[/magenta] sync for the selected task(s)? (y/N): ").strip().lower()
                if proceed_sync != 'y':
                    console.print("Operation cancelled by user. No sync tasks will run.")
                    any_sync_failed = True
                    break  # Exit the loop over sync tasks
            elif len(selected_sync_configurations) > 1:  # For subsequent tasks in a multi-task run
                console.print(
                    f"Proceeding with sync for Local: [cyan]{local_target_tbl_name}[/cyan] from AWS: [cyan]{aws_source_tbl_name}[/cyan]...")

            if proceed_sync == 'y':
                console.print(
                    f"\nStarting [magenta]{sync_type_selected}[/magenta] synchronization for Local: '{local_target_tbl_name}' from AWS: '{aws_source_tbl_name}'...")
                single_task_start_time = time.time()
                sync_success_current_task = False
                try:
                    if updater_instance:
                        if sync_type_selected == 'full':
                            await updater_instance._perform_full_sync()
                        elif sync_type_selected == 'incremental':
                            await updater_instance._perform_incremental_sync()
                        elif sync_type_selected == 'missing':
                            await updater_instance._perform_missing_sync()
                        sync_success_current_task = True
                    else:
                        cli_logger.error(f"Updater object was None for task. This should not happen if init succeeded.")
                        sync_success_current_task = False
                except Exception as sync_err_loop:
                    cli_logger.critical(
                        f"Sync operation failed for Local: '{local_target_tbl_name}' from AWS: '{aws_source_tbl_name}': {sync_err_loop}",
                        exc_info=True)
                    console.print(f"[bold red]Sync Error for this task:[/bold red] Check logs.")
                    sync_success_current_task = False

                if not sync_success_current_task:
                    any_sync_failed = True

                duration_current_task = time.time() - single_task_start_time
                result_color_current = "green" if sync_success_current_task else "red"
                console.print(
                    f"\n[{result_color_current}]Task (Local: {local_target_tbl_name} from AWS: {aws_source_tbl_name}) finished in {duration_current_task:.2f} seconds.[/{result_color_current}]")
            else:  # User cancelled for this specific task (should only happen if asked individually, not here with current logic)
                console.print(
                    f"Sync for Local: '{local_target_tbl_name}' from AWS: '{aws_source_tbl_name}' cancelled by user.")
                any_sync_failed = True

        overall_duration = time.time() - overall_sync_start_time
        final_result_color = "green" if not any_sync_failed else "red"
        console.print(
            f"\n[{final_result_color}]All selected sync operations finished in {overall_duration:.2f} seconds.[/{final_result_color}]")


    except KeyboardInterrupt:
        console.print("\n[yellow]Operation cancelled by user (KeyboardInterrupt).[/yellow]")
    except Exception as e:
        cli_logger.critical(
            f"\n[bold red]CLI Error: An unexpected error occurred: {e}[/bold red]",
            exc_info=True)
        sys.exit(1)
    finally:
        console.print("\nDynamoDB sync tool finished.")


if __name__ == "__main__":
    # Configure logging levels for dependencies
    logging.getLogger("botocore").setLevel(logging.WARNING)
    logging.getLogger("boto3").setLevel(logging.WARNING)
    logging.getLogger("urllib3").setLevel(logging.WARNING)

    # Set level for repositories
    logging.getLogger("src.repositories").setLevel(logging.INFO)

    # Allow setting CLI log level via environment variable, defaulting to INFO
    cli_log_level = os.environ.get("CLI_LOG_LEVEL", "INFO").upper()
    logging.getLogger("dynamodb_cli").setLevel(cli_log_level)
    # Also set the sync class logger level
    logging.getLogger("DynamoDBSync").setLevel(cli_log_level)

    asyncio.run(main())
