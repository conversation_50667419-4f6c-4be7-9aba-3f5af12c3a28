
import os
from typing import Dict, <PERSON>, <PERSON><PERSON>, NamedTuple
import json
from collections import defaultdict
from rich.console import Console
from rich.table import Table
from rich import print as rich_print
import argparse
from datetime import datetime


# Import the project root handling function
try:
    from scripts.utils import get_project_root
except ImportError:
    # Fall back to direct import if utils.py is not available
    try:
        from src.lib.config_adapter import PROJECT_ROOT
    except ImportError:
        # If can't import, try to load from environment or use a fallback
        try:
            from dotenv import load_dotenv
            load_dotenv()
            PROJECT_ROOT = os.getenv('PROJECT_ROOT', os.path.expanduser('/'))
        except ImportError:
            PROJECT_ROOT = os.path.expanduser('/')
else:
    PROJECT_ROOT = get_project_root()


class DocketFile(NamedTuple):
    json_path: str
    pdf_exists: bool

class DocketDuplicateAnalyzer:
    def __init__(self, base_path: str = os.path.join(PROJECT_ROOT, "data")):
        self.base_path = base_path
        self.console = Console()
        self.pdf_filter = None  # Add this line
        
    def _get_date_directories(self) -> List[str]:
        """Get all YYYYMMDD directories"""
        paths = []
        for item in os.listdir(self.base_path):
            if item.isdigit() and len(item) == 8:  # YYYYMMDD format
                docket_path = os.path.join(self.base_path, item, 'dockets')
                if os.path.isdir(docket_path):
                    paths.append(item)
        return sorted(paths)

    def _get_dockets_for_date(self, date_str: str) -> Dict[Tuple[str, str], DocketFile]:
        """Get all dockets for a specific date"""
        dockets = {}
        docket_path = os.path.join(self.base_path, date_str, 'dockets')
        
        if not os.path.exists(docket_path):
            self.console.print(f"[red]No dockets found for date {date_str}[/red]")
            return dockets
            
        for file in os.listdir(docket_path):
            if not file.endswith('.json'):
                continue
                
            json_path = os.path.join(docket_path, file)
            pdf_path = json_path.replace('.json', '.pdf')
            
            try:
                with open(json_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    
                court_id = data.get('court_id')
                docket_num = data.get('docket_num')
                
                if court_id and docket_num:
                    dockets[(court_id, docket_num)] = DocketFile(
                        json_path=json_path,
                        pdf_exists=os.path.exists(pdf_path)
                    )
                    
            except Exception as e:
                self.console.print(f"[red]Error processing {file}: {str(e)}[/red]")
                
        return dockets

    def analyze_specific_date(self, target_date: str, check_pdfs: bool = False) -> Dict[Tuple[str, str], Dict[str, DocketFile]]:
        """
        Find duplicates for dockets from a specific date across all other dates
        """
        duplicates = defaultdict(dict)
        target_dockets = self._get_dockets_for_date(target_date)
        
        if not target_dockets:
            return {}
            
        self.console.print(f"[yellow]Found {len(target_dockets)} dockets for {target_date}[/yellow]")
        
        with self.console.status("[bold green]Analyzing other dates for duplicates...") as status:
            for date_dir in self._get_date_directories():
                if date_dir == target_date:
                    continue
                    
                docket_path = os.path.join(self.base_path, date_dir, 'dockets')
                if not os.path.exists(docket_path):
                    continue
                    
                for file in os.listdir(docket_path):
                    if not file.endswith('.json'):
                        continue
                        
                    json_path = os.path.join(docket_path, file)
                    pdf_path = json_path.replace('.json', '.pdf')
                    
                    try:
                        with open(json_path, 'r', encoding='utf-8') as f:
                            data = json.load(f)
                            
                        court_id = data.get('court_id')
                        docket_num = data.get('docket_num')
                        
                        if court_id and docket_num:
                            key = (court_id, docket_num)
                            if key in target_dockets:
                                # Store both target and current docket info
                                if not duplicates[key]:
                                    duplicates[key][target_date] = target_dockets[key]
                                duplicates[key][date_dir] = DocketFile(
                                    json_path=json_path,
                                    pdf_exists=os.path.exists(pdf_path)
                                )
                                
                    except Exception as e:
                        self.console.print(f"[red]Error processing {file}: {str(e)}[/red]")

        return {k: v for k, v in duplicates.items() if len(v) > 1}

    def analyze_duplicates(self, check_pdfs: bool = False, pdf_filter: str = None) -> Dict[Tuple[str, str], Dict[str, DocketFile]]:
        """
        Analyze duplicates across all date directories
        Args:
            check_pdfs: Whether to check for PDF existence
            pdf_filter: 'matching' or 'non_matching' to filter results based on PDF existence
        Returns:
            Dictionary mapping (court_id, docket_num) to dict of dates and their DocketFiles
        """
        duplicates = defaultdict(dict)
        
        # Always check PDFs if filtering is requested
        if pdf_filter:
            check_pdfs = True
        
        date_dirs = self._get_date_directories()
        total_files = 0
        
        with self.console.status("[bold green]Analyzing files...") as status:
            for date_dir in date_dirs:
                docket_path = os.path.join(self.base_path, date_dir, 'dockets')
                
                if not os.path.exists(docket_path):
                    continue
                    
                for file in os.listdir(docket_path):
                    if not file.endswith('.json'):
                        continue
                        
                    total_files += 1
                    json_path = os.path.join(docket_path, file)
                    pdf_path = json_path.replace('.json', '.pdf')
                    pdf_exists = os.path.exists(pdf_path)
                    
                    try:
                        with open(json_path, 'r', encoding='utf-8') as f:
                            data = json.load(f)
                            
                        court_id = data.get('court_id')
                        docket_num = data.get('docket_num')
                        
                        if court_id and docket_num:
                            key = (court_id, docket_num)
                            duplicates[key][date_dir] = DocketFile(
                                json_path=json_path,
                                pdf_exists=pdf_exists
                            )
                            
                    except Exception as e:
                        self.console.print(f"[red]Error processing {file}: {str(e)}[/red]")

        # Filter for duplicates first
        duplicates = {k: v for k, v in duplicates.items() if len(v) > 1}

        # Apply PDF status filtering if requested
        if pdf_filter:
            filtered = {}
            for key, date_files in duplicates.items():
                pdf_statuses = [docket.pdf_exists for docket in date_files.values()]
                all_have_pdfs = all(pdf_statuses)
                none_have_pdfs = not any(pdf_statuses)
                
                if pdf_filter == 'matching' and (all_have_pdfs or none_have_pdfs):
                    filtered[key] = date_files
                elif pdf_filter == 'non_matching' and not (all_have_pdfs or none_have_pdfs):
                    filtered[key] = date_files
            
            duplicates = filtered

        return duplicates

    def display_results(self, duplicates: Dict[Tuple[str, str], Dict[str, DocketFile]], check_pdfs: bool = False):
        """Display results in a formatted table"""
        table = Table(show_header=True, header_style="bold")
        table.add_column("Court ID", style="cyan")
        table.add_column("Docket Number", style="magenta")
        table.add_column("Filing Dates", style="green")
        if check_pdfs:
            table.add_column("PDF Status", style="yellow")

        filtered_entries = []
        
        for (court_id, docket_num), date_files in sorted(duplicates.items()):
            pdf_statuses = [docket.pdf_exists for docket in date_files.values()]
            
            # For 'matching', ALL duplicates must have their PDFs
            if self.pdf_filter == 'matching' and not all(pdf_statuses):
                continue
            # For 'non_matching', show entries where at least one PDF is missing
            if self.pdf_filter == 'non_matching' and all(pdf_statuses):
                continue
                
            dates_info = []
            pdf_status = []
            
            for date, docket_file in sorted(date_files.items()):
                dates_info.append(date)
                if check_pdfs:
                    status = "✓" if docket_file.pdf_exists else "✗"
                    pdf_status.append(f"{date}: {status}")
            
            filtered_entries.append((
                court_id,
                docket_num,
                ", ".join(dates_info),
                "\n".join(pdf_status) if check_pdfs else None
            ))

        for entry in filtered_entries:
            table.add_row(*[col for col in entry if col is not None])

        self.console.print(table)
        self.console.print(f"\n[bold]Summary:[/bold]")
        self.console.print(f"Found [red]{len(filtered_entries)}[/red] duplicate court_id/docket_num combinations")

def parse_date(date_str: str) -> str:
    """Convert various date formats to YYYYMMDD"""
    try:
        # Try MM/DD/YY
        date = datetime.strptime(date_str, '%m/%d/%y')
        return date.strftime('%Y%m%d')
    except ValueError:
        try:
            # Try YYYYMMDD
            datetime.strptime(date_str, '%Y%m%d')
            return date_str
        except ValueError:
            raise ValueError("Date must be in MM/DD/YY or YYYYMMDD format")

def main():
    parser = argparse.ArgumentParser(description='Analyze duplicate dockets across dates')
    parser.add_argument('--pdf-filter', 
                       choices=['matching', 'non_matching'],
                       help='Filter results to show only matching or non-matching PDF cases')
    parser.add_argument('--date', 
                       help='Specific date to analyze (YYYYMMDD format)')
    parser.add_argument('--check-pdfs', 
                       action='store_true',
                       help='Check for PDF existence')
    args = parser.parse_args()

    analyzer = DocketDuplicateAnalyzer()
    analyzer.pdf_filter = args.pdf_filter  # Add this line
    rich_print("[bold]Starting duplicate docket analysis...[/bold]")
    
    if args.date:
        duplicates = analyzer.analyze_specific_date(args.date, check_pdfs=args.check_pdfs)
    else:
        duplicates = analyzer.analyze_duplicates(check_pdfs=args.check_pdfs)
    
    analyzer.display_results(duplicates, check_pdfs=True if args.pdf_filter else args.check_pdfs)

if __name__ == "__main__":
    main()