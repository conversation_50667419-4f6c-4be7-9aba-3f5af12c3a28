import json

def read_json_file(file_path):
    """
    Reads a JSON object from a file and returns it as a dictionary.

    Args:
    file_path (str): The path to the JSON file.

    Returns:
    dict: The JSON object as a dictionary.
    """
    try:
        with open(file_path, 'r') as file:
            json_data = json.load(file)
        return json_data
    except FileNotFoundError:
        print(f"File '{file_path}' not found.")
        return None
    except json.JSONDecodeError as e:
        print(f"Error decoding JSON from '{file_path}': {e}")
        return None

def save_json_file(data, file_path):
    """
    Saves data as JSON to a file.

    Args:
    data (dict): The data to be saved as JSON.
    file_path (str): The path to the JSON file.
    """
    try:
        with open(file_path, 'w') as file:
            json.dump(data, file, indent=4)
        print(f"Data saved to '{file_path}' successfully.")
    except Exception as e:
        print(f"Error saving data to '{file_path}': {e}")

# Example usage:
# Assuming you have a file named district_courts.json in the same directory as this script
json_data = read_json_file('../../src/config/courts/district_courts.json')
new_dict = []
if json_data:
    for court in json_data.get('data', []):  # Use .get() to safely retrieve 'data' key
        court_title = court.get('title')  # Use .get() to safely retrieve 'title' key
        court_type = court.get('type')  # Use .get() to safely retrieve 'type' key
        if court_type == 'District' and court_title != 'Pacer Case Locator':
            try:
                new_dict.append(
                    {
                        'court_id': court['court_id'].lower(),
                        'login_url': court['login_url'],
                        'court_name': court['title']
                    }
                )
            except Exception as e:
                print(json.dumps(court, indent=4))  # Print court details with indent=4 on exception
                print(f"Error occurred: {e}")

# Save the data as JSON to 'court_info.json'
save_json_file(new_dict, '../../src/config/courts/district_courts.json')
