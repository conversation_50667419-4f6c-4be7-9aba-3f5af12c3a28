import fitz
import argparse
from pathlib import Path


def find_signature_images(pdf_path):
    """Find potential signature images in a PDF document with improved position detection."""
    doc = fitz.open(pdf_path)
    signature_candidates = []
    signature_keywords = ["signature", "signed by", "authorized", "approved by", "sign here",
                          "executed by", "witnessed by", "certified by"]

    for page_num, page in enumerate(doc, start=1):
        try:
            # Get all images on the page with more detailed information
            images = page.get_images(full=True)

            # Get all text blocks and shapes for better context
            text_blocks = page.get_text("blocks")
            drawings = page.get_drawings()

            for img_index, img in enumerate(images):
                try:
                    xref = img[0]
                    base_image = doc.extract_image(xref)

                    # Image characteristics check
                    width, height = base_image["width"], base_image["height"]
                    aspect_ratio = width / height

                    # Skip images that don't match signature characteristics
                    if not (50 <= width <= 500 and 1.5 <= aspect_ratio <= 4):
                        continue

                    # Try multiple methods to find image position
                    img_bbox = None

                    # Method 1: Check text blocks referencing images
                    for block in text_blocks:
                        if f"Image{img[0]}" in block[4]:
                            img_bbox = fitz.Rect(block[:4])
                            break

                    # Method 2: Check drawings that might contain the image
                    if img_bbox is None:
                        for draw in drawings:
                            if hasattr(draw, 'image') and draw.image and draw.rect.width > 0:
                                img_bbox = draw.rect
                                break

                    # Method 3: Fallback to page image areas (less precise)
                    if img_bbox is None:
                        img_areas = page.get_image_info()
                        for area in img_areas:
                            if area.get('xref') == xref:  # Safely handle missing 'xref' key
                                try:
                                    img_bbox = fitz.Rect(area['bbox'])
                                    break
                                except (KeyError, ValueError):
                                    continue

                    position_status = "Position found" if img_bbox else "No position data"

                    # Signature relevance checks
                    relevance_factors = []

                    # Check for nearby signature-related text
                    if img_bbox:
                        for block in text_blocks:
                            block_text = block[4].lower()
                            if any(keyword in block_text for keyword in signature_keywords):
                                block_rect = fitz.Rect(block[:4])
                                distance = img_bbox.distance_to(block_rect)
                                if distance < 100:
                                    relevance_factors.append(f"Near '{block_text[:30]}...'")

                    # Check page position
                    if img_bbox:
                        page_rect = page.rect
                        # Bottom-right quadrant
                        bottom_right = fitz.Rect(
                            page_rect.width * 0.6,
                            page_rect.height * 0.7,
                            page_rect.width,
                            page_rect.height
                        )
                        # End-of-document area (last 15% vertically)
                        end_of_doc = fitz.Rect(
                            0,
                            page_rect.height * 0.85,
                            page_rect.width,
                            page_rect.height
                        )

                        if img_bbox.intersects(bottom_right):
                            relevance_factors.append("Bottom-right position")
                        if img_bbox.intersects(end_of_doc):
                            relevance_factors.append("End-of-document position")

                    # Check if image appears after signature lines
                    if img_bbox and len(relevance_factors) == 0:
                        for block in text_blocks:
                            if "_____" in block[4] or "....." in block[4]:  # Common signature lines
                                block_rect = fitz.Rect(block[:4])
                                if img_bbox.y0 > block_rect.y0:  # Image appears below line
                                    relevance_factors.append("Below signature line")

                    if relevance_factors or position_status == "No position data":
                        signature_candidates.append((
                            page_num,
                            xref,
                            position_status,
                            "; ".join(relevance_factors) if relevance_factors else "Size matches signature"
                        ))

                except Exception as img_error:
                    print(f"Error processing image {img_index} on page {page_num}: {str(img_error)}")
                    continue

        except Exception as page_error:
            print(f"Error processing page {page_num}: {str(page_error)}")
            continue

    return signature_candidates

def extract_specific_image(pdf_path, xref, output_path="signature.jpg"):
    doc = fitz.open(pdf_path)
    try:
        img = doc.extract_image(xref)
        with open(output_path, "wb") as f:
            f.write(img["image"])
        print(f"Successfully extracted image to {output_path}")
    except Exception as e:
        print(f"Error extracting image: {e}")
    finally:
        doc.close()

# For your specific case:
extract_specific_image("your_document.pdf", 60, "page11_signature.jpg")

def main():
    parser = argparse.ArgumentParser(description="Find signature images in PDF documents")
    parser.add_argument("--path", required=True, help="Path to the PDF file to analyze")
    args = parser.parse_args()

    pdf_path = Path(args.path)
    if not pdf_path.exists():
        print(f"Error: File not found at {pdf_path}")
        return

    print(f"Analyzing PDF: {pdf_path}")
    try:
        signatures = find_signature_images(pdf_path)

        if not signatures:
            print("No potential signature images found.")
            return

        print("\nPotential signature images found:")
        print("Page | XREF | Position Status | Evidence")
        print("----------------------------------------")
        for page_num, xref, position_status, evidence in signatures:
            print(f"{page_num:4} | {xref:4} | {position_status:16} | {evidence}")

    except Exception as e:
        print(f"Failed to analyze PDF: {str(e)}")


if __name__ == "__main__":
    main()