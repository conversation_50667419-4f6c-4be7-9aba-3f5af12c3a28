import requests
import os
import logging # Use logging from your existing setup

# Configure logging if running standalone
# logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# --- Extract this URL from the src attribute ---
video_url = "https://video-mia3-1.xx.fbcdn.net/v/t42.1790-2/485933400_654425003900859_4391056523070642348_n.mp4?_nc_cat=100&ccb=1-7&_nc_sid=c53f8f&_nc_ohc=8fFgddo4VCgQ7kNvwFftfUt&_nc_oc=Adkh4_8u0Uz6zpyHVw0GRBuRjexKVr3M7tXd3NvOJzv5ySn1lo92cAFA94n22V2UGJ1GyAGE0UNfSts55WcKF3Hx&_nc_zt=28&_nc_ht=video-mia3-1.xx&_nc_gid=C0BNfD53iHJsqXzeGiPobQ&oh=00_AYGaWmJjFyOZGeds6cXxnP3g-2YB9H0QQT0waeFSKlhJ8A&oe=67F66D47"
# --- ---

# --- Define where to save the video ---
# Ideally, use ad_archive_id or creative_id if you have them
# For this example, we'll derive a name from the URL path
try:
    url_path = requests.utils.urlparse(video_url).path
    base_filename = os.path.basename(url_path) if url_path else "downloaded_video.mp4"
    if not base_filename.lower().endswith('.mp4'): # Ensure .mp4 extension
         base_filename += ".mp4"
except Exception:
    base_filename = "downloaded_video.mp4"

# Make sure the download directory exists (adjust path as needed)
download_dir = './video_downloads'
os.makedirs(download_dir, exist_ok=True)
local_filename = os.path.join(download_dir, base_filename)
# --- ---

logger = logging.getLogger(__name__) # Get logger instance

logger.info(f"Attempting to download video from: {video_url}")
logger.info(f"Saving to: {local_filename}")

try:
    # Use stream=True to download in chunks (good for large files)
    # IMPORTANT: Pass necessary session/headers if needed (see point 3 below)
    response = requests.get(video_url, stream=True, timeout=60) # Increased timeout

    # Check if the request was successful
    response.raise_for_status()

    # Get total size for progress (optional)
    total_size = int(response.headers.get('content-length', 0))
    block_size = 8192 # 8KB chunks
    downloaded_size = 0

    print(f"Downloading {base_filename} ({total_size / (1024*1024):.2f} MB)...")

    # Open the local file in binary write mode
    with open(local_filename, 'wb') as f:
        for chunk in response.iter_content(chunk_size=block_size):
            if chunk:  # Filter out keep-alive new chunks
                f.write(chunk)
                downloaded_size += len(chunk)
                # Optional: Print progress
                progress = int(50 * downloaded_size / total_size) if total_size > 0 else 0
                print(f"\r[{'#' * progress}{'.' * (50 - progress)}] {downloaded_size / (1024*1024):.2f} MB", end='')

    # print() # New line after progress bar
    logger.info(f"Video downloaded successfully: {local_filename}")

except requests.exceptions.Timeout:
    logger.error(f"Download timed out for {video_url}")
except requests.exceptions.RequestException as e:
    logger.error(f"Error downloading video: {e}")
    if e.response is not None:
        logger.error(f"Status Code: {e.response.status_code}")
        logger.error(f"Response Text (first 500 chars): {e.response.text[:500]}")
except IOError as e:
    logger.error(f"Error writing video file {local_filename}: {e}")
except Exception as e:
     logger.error(f"An unexpected error occurred: {e}", exc_info=True)