import os


# Import the project root handling function
try:
    from scripts.utils import get_project_root
except ImportError:
    # Fall back to direct import if utils.py is not available
    try:
        from src.lib.config_adapter import PROJECT_ROOT
    except ImportError:
        # If can't import, try to load from environment or use a fallback
        try:
            from dotenv import load_dotenv
            load_dotenv()
            PROJECT_ROOT = os.getenv('PROJECT_ROOT', os.path.expanduser('/'))
        except ImportError:
            PROJECT_ROOT = os.path.expanduser('/')
else:
    PROJECT_ROOT = get_project_root()

#!/usr/bin/env python3
"""
Script to check for matching files across different formats in the dockets directory.
"""

import argparse
import os
from pathlib import Path
from rich.console import Console
from rich.table import Table
from rich import print as rprint

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Match files across different formats.")
    parser.add_argument("--date", required=True, help="Date in YYYYMMDD format")
    return parser.parse_args()

def find_matching_files(date_str):
    """Find matching files across different formats."""
    base_dir = Path(os.path.join(PROJECT_ROOT, "data", date_str, "dockets"))

    if not base_dir.exists():
        rprint(f"[bold red]Error:[/bold red] Directory {base_dir} does not exist.")
        return []

    results = []

    # Get all JSON files
    json_files = list(base_dir.glob("*.json"))

    for json_file in json_files:
        base_filename = json_file.stem
        old_filename = json_file.name

        # Check for matching PDF, MD, and ZIP files
        pdf_file = base_dir / f"{base_filename}.pdf"
        md_file = base_dir / f"{base_filename}.md"
        zip_file = base_dir / f"{base_filename}.zip"

        has_json = True  # We know this exists since we're iterating over JSON files
        has_pdf = pdf_file.exists()
        has_md = md_file.exists()
        has_zip = zip_file.exists()

        results.append({
            "base_filename": base_filename,
            "old_filename": old_filename,
            "json": has_json,
            "pdf": has_pdf,
            "md": has_md,
            "zip": has_zip
        })

    return results

def display_results(results):
    """Display results in a formatted table."""
    console = Console()

    table = Table(show_header=True, header_style="bold magenta")
    table.add_column("Base Filename")
    table.add_column("Original Filename")
    table.add_column("JSON", justify="center")
    table.add_column("PDF", justify="center")
    table.add_column("MD", justify="center")
    table.add_column("ZIP", justify="center")

    for result in results:
        table.add_row(
            result["base_filename"],
            result["old_filename"],
            "✅" if result["json"] else "❌",
            "✅" if result["pdf"] else "❌",
            "✅" if result["md"] else "❌",
            "✅" if result["zip"] else "❌"
        )

    console.print(table)

    # Print summary
    total = len(results)
    with_pdf = sum(1 for r in results if r["pdf"])
    with_md = sum(1 for r in results if r["md"])
    with_zip = sum(1 for r in results if r["zip"])
    with_all = sum(1 for r in results if r["pdf"] and r["md"] and r["zip"])

    console.print(f"\n[bold]Summary:[/bold]")
    console.print(f"Total JSON files: {total}")
    console.print(f"Files with matching PDF: {with_pdf} ({with_pdf/total*100:.1f}%)")
    console.print(f"Files with matching MD: {with_md} ({with_md/total*100:.1f}%)")
    console.print(f"Files with matching ZIP: {with_zip} ({with_zip/total*100:.1f}%)")
    console.print(f"Files with all formats: {with_all} ({with_all/total*100:.1f}%)")

def main():
    args = parse_args()
    date_str = args.date

    rprint(f"[bold]Checking files for date:[/bold] {date_str}")
    results = find_matching_files(date_str)

    if results:
        display_results(results)
    else:
        rprint("[yellow]No JSON files found.[/yellow]")

if __name__ == "__main__":
    main()