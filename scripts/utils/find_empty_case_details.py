#!/usr/bin/env python3
"""
Script to find JSON files with empty mdl_num, title, and allegations fields.

Usage:
    python find_empty_case_details.py --date YYYYMMDD

This script iterates through each JSON file in data/YYYYMMDD/dockets and identifies
files where mdl_num == "NA" and title == "" and allegations == "".
"""

import argparse
import json
import os
import sys
from pathlib import Path
from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TextColumn

console = Console()

def find_empty_case_details(date_str: str) -> None:
    """Find JSON files with empty case details."""
    
    # Construct the dockets path
    dockets_dir = Path(f"data/{date_str}/dockets")
    
    if not dockets_dir.exists():
        console.print(f"[red]Error: Directory {dockets_dir} does not exist[/red]")
        sys.exit(1)
    
    # Find all JSON files
    json_files = list(dockets_dir.glob("*.json"))
    
    if not json_files:
        console.print(f"[yellow]No JSON files found in {dockets_dir}[/yellow]")
        return
    
    matching_files = []
    
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console
    ) as progress:
        task = progress.add_task(f"Scanning {len(json_files)} JSON files...", total=len(json_files))
        
        for json_file in json_files:
            progress.advance(task)
            
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # Check if all three conditions are met
                mdl_num = data.get('mdl_num', '')
                title = data.get('title', '')
                allegations = data.get('allegations', '')
                
                # Check for empty values: "", null, or "NA"
                mdl_empty = mdl_num in ["NA", "", None]
                title_empty = title in ["", None]
                allegations_empty = allegations in ["", None]
                
                if mdl_empty and title_empty and allegations_empty:
                    matching_files.append(str(json_file.absolute()))
                    
            except (json.JSONDecodeError, UnicodeDecodeError) as e:
                console.print(f"[red]Error reading {json_file}: {e}[/red]")
                continue
            except Exception as e:
                console.print(f"[red]Unexpected error processing {json_file}: {e}[/red]")
                continue
    
    # Display results
    console.print(f"\n[bold green]Found {len(matching_files)} files with empty case details:[/bold green]")
    
    if matching_files:
        console.print("\n[bold]Copy-paste ready list:[/bold]")
        file_list = '[' + ',\n'.join(f'    "{file_path}"' for file_path in sorted(matching_files)) + '\n]'
        console.print(file_list)
    else:
        console.print("[yellow]No files found matching the criteria.[/yellow]")

def main():
    parser = argparse.ArgumentParser(
        description="Find JSON files with empty case details (mdl_num='NA', title='', allegations='')"
    )
    parser.add_argument(
        '--date',
        required=True,
        help='Date in YYYYMMDD format'
    )
    
    args = parser.parse_args()
    
    # Validate date format
    if len(args.date) != 8 or not args.date.isdigit():
        console.print("[red]Error: Date must be in YYYYMMDD format[/red]")
        sys.exit(1)
    
    find_empty_case_details(args.date)

if __name__ == "__main__":
    main()