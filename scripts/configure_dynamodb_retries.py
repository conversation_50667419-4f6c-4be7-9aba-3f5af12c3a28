#!/usr/bin/env python3
"""
Configuration helper for DynamoDB retry settings.

This script helps configure optimal DynamoDB retry settings based on your use case.
"""

def get_retry_config(environment='production', use_case='bulk_upload'):
    """
    Get recommended DynamoDB retry configuration based on environment and use case.
    
    Args:
        environment: 'development', 'staging', or 'production'
        use_case: 'bulk_upload', 'real_time', 'batch_processing', or 'mixed'
    
    Returns:
        Dict with retry configuration parameters
    """
    
    configs = {
        'development': {
            'bulk_upload': {
                'dynamodb_max_retries': 10,
                'dynamodb_base_delay': 0.5,
                'dynamodb_max_delay': 60.0,
                'description': 'Fast failure for development testing'
            },
            'real_time': {
                'dynamodb_max_retries': 5,
                'dynamodb_base_delay': 0.3,
                'dynamodb_max_delay': 30.0,
                'description': 'Quick retry for interactive development'
            }
        },
        'staging': {
            'bulk_upload': {
                'dynamodb_max_retries': 15,
                'dynamodb_base_delay': 0.8,
                'dynamodb_max_delay': 120.0,
                'description': 'Moderate retry for staging tests'
            },
            'real_time': {
                'dynamodb_max_retries': 8,
                'dynamodb_base_delay': 0.5,
                'dynamodb_max_delay': 60.0,
                'description': 'Balanced retry for staging'
            }
        },
        'production': {
            'bulk_upload': {
                'dynamodb_max_retries': 25,
                'dynamodb_base_delay': 1.0,
                'dynamodb_max_delay': 600.0,
                'description': 'Aggressive retry for production bulk uploads'
            },
            'real_time': {
                'dynamodb_max_retries': 12,
                'dynamodb_base_delay': 0.7,
                'dynamodb_max_delay': 120.0,
                'description': 'Resilient retry for production real-time'
            },
            'batch_processing': {
                'dynamodb_max_retries': 30,
                'dynamodb_base_delay': 1.5,
                'dynamodb_max_delay': 900.0,
                'description': 'Maximum resilience for large batch jobs'
            },
            'mixed': {
                'dynamodb_max_retries': 20,
                'dynamodb_base_delay': 1.0,
                'dynamodb_max_delay': 300.0,
                'description': 'Balanced config for mixed workloads'
            }
        }
    }
    
    return configs.get(environment, {}).get(use_case, configs['production']['mixed'])

def print_config_code(config):
    """Print Python code to configure the retry settings."""
    print("# Add this to your configuration setup:")
    print(f"config.dynamodb_max_retries = {config['dynamodb_max_retries']}")
    print(f"config.dynamodb_base_delay = {config['dynamodb_base_delay']}")
    print(f"config.dynamodb_max_delay = {config['dynamodb_max_delay']}")
    print(f"# {config['description']}")

def calculate_max_retry_time(config):
    """Calculate the maximum total retry time."""
    max_retries = config['dynamodb_max_retries']
    base_delay = config['dynamodb_base_delay']
    max_delay = config['dynamodb_max_delay']
    
    total_time = 0
    for attempt in range(max_retries):
        delay = min(max_delay, base_delay * (2 ** attempt))
        total_time += delay
    
    return total_time

def main():
    """Interactive configuration helper."""
    print("🔧 DynamoDB Retry Configuration Helper")
    print("=" * 50)
    
    print("\nEnvironments:")
    print("1. Development")
    print("2. Staging") 
    print("3. Production")
    
    env_choice = input("\nSelect environment (1-3): ").strip()
    env_map = {'1': 'development', '2': 'staging', '3': 'production'}
    environment = env_map.get(env_choice, 'production')
    
    print(f"\nSelected: {environment}")
    
    if environment == 'production':
        print("\nUse cases:")
        print("1. Bulk upload (high volume, can tolerate longer delays)")
        print("2. Real-time (user-facing, need faster response)")
        print("3. Batch processing (large jobs, maximum resilience)")
        print("4. Mixed workload (balanced approach)")
        
        use_choice = input("\nSelect use case (1-4): ").strip()
        use_map = {'1': 'bulk_upload', '2': 'real_time', '3': 'batch_processing', '4': 'mixed'}
        use_case = use_map.get(use_choice, 'mixed')
    else:
        use_case = 'bulk_upload'
    
    print(f"Use case: {use_case}")
    
    # Get recommended config
    config = get_retry_config(environment, use_case)
    
    print("\n" + "=" * 50)
    print("📋 RECOMMENDED CONFIGURATION")
    print("=" * 50)
    
    print_config_code(config)
    
    max_time = calculate_max_retry_time(config)
    print(f"\n📊 Maximum total retry time: {max_time:.1f} seconds ({max_time/60:.1f} minutes)")
    
    print("\n💡 Additional Recommendations:")
    if use_case == 'bulk_upload':
        print("- Consider using batch_write_item() for multiple items")
        print("- Monitor CloudWatch metrics for throttling patterns")
        print("- Enable DynamoDB auto-scaling if not already enabled")
    elif use_case == 'real_time':
        print("- Monitor average response times under load")
        print("- Consider eventual consistency reads where possible")
        print("- Use DynamoDB Accelerator (DAX) for hot data")
    elif use_case == 'batch_processing':
        print("- Use batch operations whenever possible")
        print("- Consider parallel processing with rate limiting")
        print("- Monitor and adjust provisioned capacity based on patterns")
    
    print("\n✅ Configuration ready! Copy the code above to your config setup.")

if __name__ == "__main__":
    main()