import time
import pandas as pd
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

class FdaSafetyScraper:
    def __init__(self, driver_path):
        self.driver_path = driver_path
        self.driver = None

    def start_driver(self):
        try:
            service = Service(self.driver_path)
            options = webdriver.ChromeOptions()
            self.driver = webdriver.Chrome(service=service, options=options)
            self.driver.get("https://www.fda.gov/safety/medwatch-fda-safety-information-and-adverse-event-reporting-program")
        except Exception as e:
            print(f"Error starting the driver: {e}")
            raise

    def select_all_option(self):
        try:
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.NAME, "DataTables_Table_0_length"))
            )
            select_element = self.driver.find_element(By.NAME, "DataTables_Table_0_length")
            self.driver.execute_script("arguments[0].value = '-1';", select_element)
            select_element.click()
            self.driver.execute_script("arguments[0].dispatchEvent(new Event('change'));", select_element)
        except Exception as e:
            print(f"Error selecting the 'All' option: {e}")
            raise

    def extract_table_data(self):
        try:
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.ID, "DataTables_Table_0"))
            )
            time.sleep(5)  # Wait for the table to update with all entries

            table = self.driver.find_element(By.ID, "DataTables_Table_0")
            rows = table.find_elements(By.TAG_NAME, "tr")

            data = []
            for row in rows[1:]:  # Skip header row
                cells = row.find_elements(By.TAG_NAME, "td")
                if len(cells) >= 3:
                    date = cells[0].text
                    url_element = cells[1].find_element(By.TAG_NAME, "a")
                    url = url_element.get_attribute("href")
                    safety_alert = url_element.text
                    product_type = cells[2].text
                    data.append({"date": date, "url": url, "safety_alert": safety_alert, "product_type": product_type})

            return data
        except Exception as e:
            print(f"Error extracting table data: {e}")
            raise

    def split_product_type(self, df):
        product_types_split = df['product_type'].str.split(',', expand=True)
        product_types_split.columns = [f'pt_{i+1}' for i in range(product_types_split.shape[1])]
        df = pd.concat([df, product_types_split], axis=1)
        return df

    def split_url(self, df):
        def extract_categories(url):
            parts = url.split('/fda.gov/')[-1].split('/')[3:-1]
            return pd.Series(parts)

        url_categories = df['url'].apply(extract_categories)
        url_categories.columns = [f'cat_{i+1}' for i in range(url_categories.shape[1])]
        df = pd.concat([df, url_categories], axis=1)
        return df

    def save_to_dataframe(self, data):
        df = pd.DataFrame(data)
        df.to_csv("fda_safety_data.csv", index=False)
        return df

    def scrape(self):
        try:
            self.start_driver()
            self.select_all_option()
            data = self.extract_table_data()
            df = self.save_to_dataframe(data)
            df = self.split_product_type(df)
            df = self.split_url(df)
            df.to_csv("fda_safety_data_split.csv", index=False)
            return df
        finally:
            if self.driver:
                self.driver.quit()

if __name__ == "__main__":
    # driver_path = "/Users/<USER>/.wdm/drivers/chromedriver/mac64/126.0.6478.182/chromedriver-mac-arm64/chromedriver"  # Ensure this path is correct
    # scraper = FdaSafetyScraper(driver_path)
    # df = scraper.scrape()
    df = pd.read_csv('fda_safety_data_split.csv')
    print(df)
