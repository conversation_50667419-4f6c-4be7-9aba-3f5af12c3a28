#!/usr/bin/env python

import os
# Set up warning filters BEFORE any imports that might trigger warnings
import warnings

# Suppress PyTorch FutureWarning about weights_only parameter
warnings.filterwarnings("ignore", category=FutureWarning, module="thinc.shims.pytorch")
warnings.filterwarnings("ignore", message=".*You are using `torch.load` with `weights_only=False`.*")
# Suppress repetitive spaCy warnings
warnings.filterwarnings("ignore", message=".*rule-based lemmatizer did not find POS annotation.*")
warnings.filterwarnings("ignore", message=".*The rule-based lemmatizer did not find POS annotation.*")
warnings.filterwarnings("ignore", category=UserWarning, module="spacy")
os.environ["PYTHONWARNINGS"] = "ignore::UserWarning:spacy"
os.environ["TOKENIZERS_PARALLELISM"] = "false"  # Prevent tokenizer fork warnings

# Now do the rest of the imports
import argparse
import asyncio
import dataclasses
import hashlib
import json
import logging
import pickle
import re  # Moved to top-level imports
import sys
import time
# Standard library imports
from collections import Counter, defaultdict  # Counter added here
from functools import lru_cache
from pathlib import Path
from typing import Any, Dict, List, Optional, Pattern as RePattern, Tuple, \
    Union  # Pattern renamed to RePattern to avoid conflict

# Third-party libraries
import pandas as pd
import torch  # Keep for SentenceTransformers & MPS check
import yaml
from rich import box
# Rich integration
from rich.console import Console
from rich.logging import RichHandler
from rich.panel import Panel
from rich.progress import (BarColumn, Progress, TextColumn,
                           TimeRemainingColumn, track)
from rich.prompt import Prompt, Confirm
from rich.table import Table

from fb_ads import EmbeddingDiskCache

try:
    import psutil
except ImportError:
    psutil = None

# Warning filters have been moved to the top of the file

# Add src directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
try:
    # Try new migration helper first
    from src.migration import create_manager_replacement
    _using_new_architecture = True
except ImportError:
    _using_new_architecture = False
    from src.lib.law_firms_manager import LawFirmsManager

# NLP/ML libraries (SentenceTransformers and SpaCy are primary)
try:
    from sentence_transformers import SentenceTransformer

    SENTENCE_TRANSFORMERS_AVAILABLE = True
except ImportError:
    SENTENCE_TRANSFORMERS_AVAILABLE = False
    SentenceTransformer = None

try:
    import spacy

    SPACY_AVAILABLE = True
except ImportError:
    SPACY_AVAILABLE = False
    spacy = None

# FLAIR related code (FLAIR_AVAILABLE, imports, monkey patches) has been removed.

# Optional LLM libraries
try:
    from transformers import pipeline as hf_pipeline  # Renamed to avoid conflict

    TRANSFORMERS_AVAILABLE = True
except ImportError:
    TRANSFORMERS_AVAILABLE = False
    hf_pipeline = None

try:
    import mlx
    import mlx.core as mx
    import mlx.nn as mnn
    from mlx_lm import load, generate

    MLX_AVAILABLE = True
except ImportError:
    MLX_AVAILABLE = False

try:
    from llama_cpp import Llama

    LLAMA_CPP_AVAILABLE = True
except ImportError:
    LLAMA_CPP_AVAILABLE = False

_ollama_import_logged = False
try:
    import ollama

    OLLAMA_AVAILABLE = True
    if not _ollama_import_logged and os.getenv("SPACY_WORKER_ID") is None:
        print(f"DEBUG: Ollama import successful, version: {getattr(ollama, '__version__', 'unknown')}")
        _ollama_import_logged = True
except ImportError as e:
    OLLAMA_AVAILABLE = False
    print(f"DEBUG: Ollama import failed: {e}")
except Exception as e:
    OLLAMA_AVAILABLE = False
    print(f"DEBUG: Ollama import error: {e}")

# --- Logger setup with Rich ---
logger = logging.getLogger("HybridClassifier")
if not logger.handlers:
    log_handler = RichHandler(rich_tracebacks=True, show_time=True, show_level=True, show_path=False,
                              console=Console(stderr=True))
    log_formatter = logging.Formatter('%(name)s - %(message)s')
    log_handler.setFormatter(log_formatter)
    logger.addHandler(log_handler)
    logger.setLevel(logging.INFO)
    logger.propagate = False

# Fuzzy matching library
FUZZYWUZZY_AVAILABLE = False
FUZZY_BACKEND = None
try:
    from rapidfuzz import fuzz, process
    from rapidfuzz.utils import default_process as full_process

    FUZZYWUZZY_AVAILABLE = True
    FUZZY_BACKEND = "rapidfuzz"
    logger.debug("Using rapidfuzz for fuzzy matching (faster)")
except ImportError:
    try:
        from fuzzywuzzy import fuzz, process
        from fuzzywuzzy.utils import full_process

        FUZZYWUZZY_AVAILABLE = True
        FUZZY_BACKEND = "fuzzywuzzy"
        logger.debug("Using fuzzywuzzy for fuzzy matching (slower fallback)")
    except ImportError:
        logger.warning("No fuzzy matching library available. Install rapidfuzz or fuzzywuzzy.")


# --- Helper Functions ---
def _normalize_text_for_matching(text: str) -> str:
    if not isinstance(text, str):
        return ""
    text = text.lower()
    text = re.sub(r'[^\w\s]', ' ', text)
    text = re.sub(r'\s+', ' ', text).strip()
    return text


def _compile_term_pattern(terms: List[str], term_type_description: str) -> Optional[RePattern]:
    if not terms:
        return None
    try:
        valid_terms = [term for term in terms if term]
        if not valid_terms:
            return None
        pattern_str = r'\b(?:' + '|'.join(re.escape(term) for term in valid_terms) + r')\b'
        pattern = re.compile(pattern_str, re.IGNORECASE)
        logger.debug(f"Compiled regex pattern for {len(valid_terms)} {term_type_description}.")
        return pattern
    except Exception as e:
        logger.error(f"Failed to compile regex for {term_type_description}: {e}", exc_info=True)
        return None


# --- Dataclasses ---
@dataclasses.dataclass
class ClassificationResult:
    """Result of classification with confidence scores"""
    ad_id: str
    campaign: str
    company: Optional[str]
    original_name: Optional[str]  # Original company name before fuzzy matching
    confidence: float
    method: str  # 'rule', 'embedding', 'llm', 'llm_enhanced', 'llm_suggestion', 'llm_failed', 'hybrid', 'none', 'skip_terms', 'error', 'worker_error', 'worker_init_error'
    needs_review: bool
    rule_source: Optional[str]  # 'manual_config', 'auto_generated'
    rule_confidence: float = 0.0
    embedding_confidence: float = 0.0
    llm_confidence: float = 0.0
    dbscan_cluster: Optional[int] = None  # DBSCAN cluster ID for 'Other' campaigns
    title: Optional[str] = None  # Ad title
    body: Optional[str] = None  # Ad body
    ner_results: Optional[Dict[str, List[str]]] = None  # NER extraction results
    details: Dict[str, Any] = dataclasses.field(default_factory=dict)


# --- Core Components ---


class AdCampaignMatcher:
    def __init__(self, known_campaigns: List[Dict[str, Any]], logger_instance: logging.Logger):
        self.known_campaigns = known_campaigns
        self.logger = logger_instance
        self.data_breach_rules = [c for c in self.known_campaigns if c['LitigationName'] == "Data Breach"]
        self.etsy_privacy_rules = [c for c in self.known_campaigns if c['LitigationName'] == "Etsy Privacy Litigation"]
        self.general_privacy_rules = [c for c in self.known_campaigns if c['LitigationName'] == "Privacy Violation"]
        other_rule_litigation_names = ["Data Breach", "Etsy Privacy Litigation", "Privacy Violation"]
        self.remaining_rules_for_dynamic_check = sorted(
            [c for c in self.known_campaigns if c['LitigationName'] not in other_rule_litigation_names],
            key=lambda x: (
                len(x.get('trigger_pattern').pattern if x.get('trigger_pattern') else ''),
                len(x.get('include_or_pattern').pattern if x.get('include_or_pattern') else ''),
                len(x.get('exclude_pattern').pattern if x.get('exclude_pattern') else '')
            ),
            reverse=True
        )
        self.prioritized_rule_sets_for_match = [
            ("Data Breach Rules", self.data_breach_rules),
            ("Etsy Privacy Rules", self.etsy_privacy_rules),
            ("General Privacy Rules", self.general_privacy_rules),
            ("Other Campaign Rules (Dynamic Selection)", self.remaining_rules_for_dynamic_check)
        ]
        self.logger.info(f"AdCampaignMatcher initialized with {len(self.known_campaigns)} campaigns, "
                         f"{len(self.remaining_rules_for_dynamic_check)} in dynamic set.")

    def _matches_rules_for_campaign(self, campaign_config_entry: Dict[str, Any], text_to_match: str) -> bool:
        trigger_pattern: Optional[RePattern] = campaign_config_entry.get('trigger_pattern')
        include_or_pattern: Optional[RePattern] = campaign_config_entry.get('include_or_pattern')
        exclude_pattern: Optional[RePattern] = campaign_config_entry.get('exclude_pattern')
        if not trigger_pattern or not trigger_pattern.search(text_to_match):
            return False
        if include_or_pattern and not include_or_pattern.search(text_to_match):
            return False
        if exclude_pattern and exclude_pattern.search(text_to_match):
            return False
        return True

    def _calculate_specificity_score(self, campaign_config_entry: Dict[str, Any], text_to_match: str) -> int:
        trigger_pattern: Optional[RePattern] = campaign_config_entry.get('trigger_pattern')
        include_or_pattern: Optional[RePattern] = campaign_config_entry.get('include_or_pattern')
        matched_trigger_terms_list = trigger_pattern.findall(text_to_match) if trigger_pattern else []
        matched_include_terms_list = include_or_pattern.findall(text_to_match) if include_or_pattern else []
        specificity_score = 0
        if include_or_pattern and matched_include_terms_list:
            specificity_score += 1000
        specificity_score += len(set(matched_trigger_terms_list))
        specificity_score += len(set(matched_include_terms_list))
        return specificity_score

    def match_ad_text(self, text_to_match: str, ad_id_for_log: str = "N/A") -> Tuple[Optional[str], float]:
        matched_campaign_name: Optional[str] = None
        best_score_for_match = 0.0
        if not text_to_match.strip():
            logger.debug(f"AD_ID: {ad_id_for_log} - AdCampaignMatcher: Empty text_to_match.")
            return None, 0.0
        for pass_name, current_rule_set in self.prioritized_rule_sets_for_match:
            if matched_campaign_name and pass_name != "Other Campaign Rules (Dynamic Selection)":
                break
            logger.debug(f"AD_ID: {ad_id_for_log} - AdCampaignMatcher attempting pass: {pass_name}")
            if pass_name == "Other Campaign Rules (Dynamic Selection)":
                potential_matches: List[Tuple[int, str]] = []
                for campaign_config_entry in current_rule_set:
                    campaign_name_from_config = campaign_config_entry['LitigationName']
                    if self._matches_rules_for_campaign(campaign_config_entry, text_to_match):
                        score = self._calculate_specificity_score(campaign_config_entry, text_to_match)
                        potential_matches.append((score, campaign_name_from_config))
                if potential_matches:
                    potential_matches.sort(key=lambda x: x[0], reverse=True)
                    if not matched_campaign_name or potential_matches[0][0] > best_score_for_match:
                        matched_campaign_name = potential_matches[0][1]
                        best_score_for_match = potential_matches[0][0]
            else:
                for campaign_config_entry in current_rule_set:
                    campaign_name_from_config = campaign_config_entry['LitigationName']
                    if self._matches_rules_for_campaign(campaign_config_entry, text_to_match):
                        matched_campaign_name = campaign_name_from_config
                        best_score_for_match = self._calculate_specificity_score(campaign_config_entry, text_to_match)
                        break
            if matched_campaign_name and pass_name != "Other Campaign Rules (Dynamic Selection)":
                break
        final_confidence = 0.98 if matched_campaign_name else 0.0
        if not matched_campaign_name:
            logger.debug(f"AD_ID: {ad_id_for_log} - AdCampaignMatcher: No campaign rule matched.")
        return matched_campaign_name, final_confidence


# NLP/ML libraries (SentenceTransformers and SpaCy are primary)
try:
    # We only check for availability here. The actual import is deferred.
    import sentence_transformers

    SENTENCE_TRANSFORMERS_AVAILABLE = True
except ImportError:
    SENTENCE_TRANSFORMERS_AVAILABLE = False
    SentenceTransformer = None

try:
    # We only check for availability here. The actual import is deferred.
    import spacy

    SPACY_AVAILABLE = True
except ImportError:
    SPACY_AVAILABLE = False
    spacy = None


class LocalLLMClassifier:
    def __init__(self, backend: str = 'ollama', model_path: Optional[str] = None,
                 device: Optional[str] = None, cache_file: Optional[str] = None):
        self.backend = backend
        self.model_path = model_path
        self.model: Any = None
        self.tokenizer: Any = None
        self.device = device
        self.cache_file = cache_file or "cache/llm_response_cache.pkl"
        self._response_cache = {}
        self._cache_hits = 0
        self._cache_misses = 0
        self._load_cache()
        self._initialize_model()

    def _initialize_model(self):
        if self.backend == 'ollama' and OLLAMA_AVAILABLE:
            logger.info("Initializing Ollama client for default host http://127.0.0.1:11434.")
            if not self.model_path:
                logger.error("Ollama model name/tag must be provided.")
                self.model = None
                self.model_path = None
                return
            try:
                self.model = ollama.Client(host='http://127.0.0.1:11434')
                models_info = self.model.list()
                actual_model_names = []
                if isinstance(models_info, dict) and 'models' in models_info:  # ollama < 0.1.30
                    actual_model_names = [m.get('name') for m in models_info.get('models', []) if m.get('name')]
                elif hasattr(models_info, 'models') and isinstance(models_info.models, list):  # ollama >= 0.1.30
                    actual_model_names = [m.model for m in models_info.models if hasattr(m, 'model')]

                model_exists = any(
                    self.model_path == name or self.model_path.split(':')[0] == name.split(':')[0] for name in
                    actual_model_names)
                if not model_exists:
                    logger.error(f"Ollama model '{self.model_path}' NOT FOUND. Available: {actual_model_names}")
                    self.model = None
                    self.model_path = None
                    return
                logger.info(f"✅ Ollama connection successful and model '{self.model_path}' IS AVAILABLE.")
            except Exception as e:
                logger.error(f"❌ Ollama initialization failed: {type(e).__name__} - {e}", exc_info=True)
                self.model = None
                self.model_path = None
        elif self.backend == 'mlx' and MLX_AVAILABLE:
            logger.info("Initializing MLX model...")
            if not self.model_path: self.model_path = "mlx-community/Nous-Hermes-2-Mistral-7B-DPO-4bit-MLX"
            try:
                self.model, self.tokenizer = load(self.model_path)
                logger.info(f"MLX model {self.model_path} loaded.")
            except Exception as e:
                logger.error(f"Failed to load MLX model {self.model_path}: {e}", exc_info=True)
                self.model = None
                self.tokenizer = None
        elif self.backend == 'llama_cpp' and LLAMA_CPP_AVAILABLE:
            logger.info("Initializing llama.cpp model...")
            if not self.model_path: logger.error("Model path required for llama.cpp."); self.model = None; return
            try:
                self.model = Llama(model_path=self.model_path, n_ctx=2048, n_gpu_layers=99, verbose=False)
                logger.info(f"llama.cpp model {self.model_path} loaded.")
            except Exception as e:
                logger.error(f"Failed to load llama.cpp model {self.model_path}: {e}", exc_info=True)
                self.model = None
        elif self.backend == 'transformers' and TRANSFORMERS_AVAILABLE and hf_pipeline is not None:
            logger.info("Initializing Hugging Face Transformers model...")
            if not self.model_path: self.model_path = "facebook/bart-large-mnli"
            resolved_device_idx = -1
            if self.device:
                if self.device == "mps" and torch.backends.mps.is_available():
                    resolved_device_idx = "mps"  # hf_pipeline takes "mps" string
                elif "cuda" in self.device and torch.cuda.is_available():
                    try:
                        resolved_device_idx = int(self.device.split(':')[-1]) if ':' in self.device else 0
                    except ValueError:
                        resolved_device_idx = 0 if torch.cuda.is_available() else -1
                else:  # Fallback logic
                    if torch.cuda.is_available():
                        resolved_device_idx = 0
                    elif torch.backends.mps.is_available() and sys.platform == "darwin":
                        resolved_device_idx = "mps"
            else:  # Auto-detect
                if torch.cuda.is_available():
                    resolved_device_idx = 0
                elif torch.backends.mps.is_available() and sys.platform == "darwin":
                    resolved_device_idx = "mps"
            try:
                self.model = hf_pipeline("zero-shot-classification", model=self.model_path, device=resolved_device_idx)
                logger.info(f"Transformers pipeline {self.model_path} loaded on device: {self.model.device}.")
            except Exception as e:
                logger.error(f"Failed to load Transformers model {self.model_path}: {e}", exc_info=True)
                self.model = None
        else:
            logger.warning(
                f"LLM Backend '{self.backend}' not recognized or library not available. LLM features disabled.")
            self.model = None

    def _load_cache(self):
        if self.cache_file and Path(self.cache_file).exists():
            try:
                with open(self.cache_file, 'rb') as f:
                    self._response_cache = pickle.load(f)
                logger.info(f"Loaded {len(self._response_cache)} cached LLM responses from {self.cache_file}")
            except Exception as e:
                logger.warning(f"Failed to load LLM cache: {e}")
        else:
            logger.info("LLM response cache not found or not specified. New cache will be created.")

    def _save_cache(self):
        if not self.cache_file: return
        try:
            Path(self.cache_file).parent.mkdir(parents=True, exist_ok=True)
            with open(self.cache_file, 'wb') as f:
                pickle.dump(self._response_cache, f)
        except Exception as e:
            logger.error(f"Failed to save LLM cache: {e}")

    def _get_cache_key(self, text: str, candidate_labels: List[str], context: Optional[str] = None,
                       enhanced: bool = False) -> str:
        key_data = {'text': text[:1500], 'labels': sorted(candidate_labels), 'context': context, 'enhanced': enhanced,
                    'model': self.model_path, 'backend': self.backend, 'type': 'classification'}
        return hashlib.md5(json.dumps(key_data, sort_keys=True).encode()).hexdigest()

    def _get_generic_llm_cache_key(self, messages: List[Dict[str, str]], call_options: Optional[Dict] = None) -> str:
        sorted_options_str = json.dumps(call_options, sort_keys=True) if call_options else None
        key_data = {'messages': json.dumps(messages, sort_keys=True), 'options': sorted_options_str,
                    'model': self.model_path, 'backend': self.backend, 'type': 'generic_chat'}
        return hashlib.md5(json.dumps(key_data, sort_keys=True).encode()).hexdigest()

    def classify(self, text: str, candidate_labels: List[str], context: Optional[str] = None) -> Tuple[
        Optional[str], float]:
        if not self.model or (self.backend == 'ollama' and not self.model_path):
            logger.debug(f"{self.backend} LLM not ready for classification.")
            return None, 0.0
        if not candidate_labels: logger.warning("LLM classify called with no candidate_labels."); return None, 0.0
        text_to_classify = text[:1000]
        cache_key = self._get_cache_key(text_to_classify, candidate_labels, context, enhanced=False)
        if cache_key in self._response_cache: self._cache_hits += 1; return self._response_cache[cache_key]
        self._cache_misses += 1
        result_tuple = (None, 0.0)
        try:
            if self.backend == 'ollama':
                system_prompt = f"Classify into one of: {', '.join(candidate_labels)}. Respond ONLY with category name."
                user_prompt = f"Ad text: \"{text_to_classify}\"\nCategory:"
                response = self.model.chat(model=self.model_path,
                                           messages=[{'role': 'system', 'content': system_prompt},
                                                     {'role': 'user', 'content': user_prompt}],
                                           options={'temperature': 0.0})
                response_text = response['message']['content'].strip()
                best_match_label = next((label for label in candidate_labels if label.lower() == response_text.lower()),
                                        None)
                if not best_match_label: best_match_label = next(
                    (label for label in candidate_labels if label.lower() in response_text.lower()), None)
                if best_match_label: result_tuple = (best_match_label, 0.70)
            elif self.backend == 'mlx' and self.tokenizer:
                prompt = f"Classify: \"{text_to_classify}\" Categories: {', '.join(candidate_labels)}.\nCategory:"
                response_text = generate(self.model, self.tokenizer, prompt=prompt, max_tokens=25, temp=0.0).strip()
                result_tuple = next(
                    ((label, 0.75) for label in candidate_labels if label.lower() in response_text.lower()),
                    (None, 0.0))
            elif self.backend == 'llama_cpp':
                prompt = f"Classify: \"{text_to_classify}\" Categories: {', '.join(candidate_labels)}.\nCategory:"
                response = self.model(prompt, max_tokens=25, temperature=0.0, stop=["\n", ".", ","])
                response_text = response['choices'][0]['text'].strip()
                result_tuple = next(
                    ((label, 0.75) for label in candidate_labels if label.lower() in response_text.lower()),
                    (None, 0.0))
            elif self.backend == 'transformers' and self.model:  # self.model is HF pipeline here
                hf_result = self.model(text_to_classify, candidate_labels, hypothesis_template="This ad is about {}.",
                                       multi_label=False)
                if hf_result and 'labels' in hf_result and hf_result['labels']:
                    result_tuple = (hf_result['labels'][0], float(hf_result['scores'][0]))
        except Exception as e:
            logger.error(f"LLM classification error ({self.backend}): {e}", exc_info=False)
        self._response_cache[cache_key] = result_tuple
        if self._cache_misses % 100 == 0: self._save_cache()
        return result_tuple

    def classify_with_enhanced_prompt(self, text: str, candidate_labels: List[str], context: str = "general") -> Tuple[
        Optional[str], float]:
        if not self.model or (self.backend == 'ollama' and not self.model_path):
            logger.debug(f"{self.backend} LLM not ready for enhanced classification.")
            return None, 0.0
        if not candidate_labels: logger.warning(
            "Enhanced LLM classify called with no candidate_labels."); return None, 0.0
        text_to_classify = text[:1200]
        cache_key = self._get_cache_key(text_to_classify, candidate_labels, context, enhanced=True)
        if cache_key in self._response_cache: self._cache_hits += 1; return self._response_cache[cache_key]
        self._cache_misses += 1
        result_tuple = (None, 0.0)
        try:
            if self.backend == 'ollama':
                system_prompt = f"Expert legal ad classifier. Categories: {', '.join(candidate_labels)}. Context: Legal ad for mass tort/class action/PI. Instructions: Identify indicators. Choose most prominent. Respond ONLY with exact category. If uncertain/not litigation, respond \"Other\". Examples: \"Roundup cancer...\" → Roundup."
                user_prompt = f"Analyze ad:\n\n\"{text_to_classify}\"\n\nLitigation category:"
                response = self.model.chat(model=self.model_path,
                                           messages=[{'role': 'system', 'content': system_prompt},
                                                     {'role': 'user', 'content': user_prompt}],
                                           options={'temperature': 0.0})
                response_text = response['message']['content'].strip()
                best_match_label = next((label for label in candidate_labels if label.lower() == response_text.lower()),
                                        None)
                confidence = 0.85 if best_match_label else 0.0
                if not best_match_label:
                    best_match_label = next(
                        (label for label in candidate_labels if label.lower() in response_text.lower()), None)
                    if best_match_label: confidence = 0.75 if response_text.lower().startswith(
                        best_match_label.lower()) else 0.65
                if best_match_label: result_tuple = (best_match_label, confidence)
            else:
                return self.classify(text, candidate_labels, context)  # Fallback
        except Exception as e:
            logger.error(f"Enhanced LLM classification error ({self.backend}): {e}", exc_info=False)
        self._response_cache[cache_key] = result_tuple
        if self._cache_misses % 100 == 0: self._save_cache()
        return result_tuple

    def execute_cached_llm_call(self, messages: List[Dict[str, str]], call_options: Optional[Dict] = None) -> Optional[
        Dict]:
        if not self.model or (self.backend == 'ollama' and not self.model_path):
            logger.debug(f"{self.backend} LLM not ready for generic call.")
            return None
        cache_key = self._get_generic_llm_cache_key(messages, call_options or {})
        if cache_key in self._response_cache: self._cache_hits += 1; return self._response_cache[cache_key]
        self._cache_misses += 1
        llm_response_data: Optional[Dict] = None
        try:
            if self.backend == 'ollama':
                llm_response_data = self.model.chat(model=self.model_path, messages=messages,
                                                    options=call_options or {})
            else:
                logger.warning(f"Generic LLM call not implemented for backend '{self.backend}'.")
                return None
        except Exception as e:
            logger.error(f"Generic LLM call error ({self.backend}): {e}", exc_info=False)
            return None
        if llm_response_data is not None: self._response_cache[cache_key] = llm_response_data
        if self._cache_misses % 100 == 0: self._save_cache()
        return llm_response_data


class CompanyNameNormalizer:
    def __init__(self, similarity_threshold: float = 85):
        self.similarity_threshold = similarity_threshold
        self.known_companies: Dict[str, str] = {}
        self.company_aliases: Dict[str, List[str]] = defaultdict(list)
        self._fuzzy_match_cache: Dict[str, Tuple[Optional[str], Optional[str]]] = {}

    def _normalize_company_name(self, name: str) -> str:
        if not name: return ""
        invalid_values = {'na', 'n/a', 'none', 'null', 'unknown', 'skipped', 'skip', 'summary generation failed',
                          'failed', 'error', 'undefined', 'not available', 'n.a.', 'tbd', 'pending', 'various',
                          'multiple'}
        name_lower = name.lower().strip()
        if name_lower in invalid_values or any(term in name_lower for term in ['failed', 'error', 'skipped']): return ""
        normalized = name_lower
        suffixes_to_remove = [', inc.', ', inc', ' inc.', ' inc', ', llc', ' llc', ', corp.', ' corp.', ', corporation',
                              ' corporation', ', ltd.', ' ltd', ', co.', ' co', '?<br', '?', '<br>']
        for suffix in suffixes_to_remove:
            if normalized.endswith(suffix): normalized = normalized[:-len(suffix)].strip()
        return normalized

    def add_or_match_company(self, company_name: str) -> Tuple[Optional[str], Optional[str]]:
        if not company_name or not company_name.strip(): return None, None
        original_name = company_name.strip()
        normalized = self._normalize_company_name(original_name)
        if not normalized: return None, None
        if normalized in self._fuzzy_match_cache: return self._fuzzy_match_cache[normalized]

        if not FUZZYWUZZY_AVAILABLE:
            canonical = self.known_companies.get(normalized, original_name)
            if normalized not in self.known_companies: self.known_companies[normalized] = canonical  # Add if new
            if original_name not in self.company_aliases[canonical]: self.company_aliases[canonical].append(
                original_name)
            result = (canonical, original_name)
            self._fuzzy_match_cache[normalized] = result
            return result

        if normalized in self.known_companies:
            canonical = self.known_companies[normalized]
            if original_name not in self.company_aliases[canonical]: self.company_aliases[canonical].append(
                original_name)
            result = (canonical, original_name)
            self._fuzzy_match_cache[normalized] = result
            return result

        if self.known_companies:
            try:
                scorer = fuzz.WRatio if FUZZY_BACKEND == "rapidfuzz" else fuzz.ratio
                match_tuple = process.extractOne(normalized, self.known_companies.keys(), scorer=scorer,
                                                 score_cutoff=self.similarity_threshold)
                if match_tuple:
                    match_norm_name, score = match_tuple
                    canonical = self.known_companies[match_norm_name]
                    if original_name not in self.company_aliases[canonical]: self.company_aliases[canonical].append(
                        original_name)
                    self.known_companies[normalized] = canonical  # Map current normalized to existing canonical
                    result = (canonical, original_name)
                    self._fuzzy_match_cache[normalized] = result
                    return result
            except Exception as e:
                logger.debug(f"Fuzzy matching error for '{company_name}': {e}")

        # No match, add as new canonical
        self.known_companies[normalized] = original_name  # New canonical is the original_name
        self.company_aliases[original_name].append(original_name)  # Add itself as an alias
        result = (original_name, original_name)
        self._fuzzy_match_cache[normalized] = result
        return result

    def get_company_stats(self) -> Dict[str, Any]:
        total_aliases = sum(len(aliases) for aliases in self.company_aliases.values())
        num_unique_companies = len(self.company_aliases)
        return {'unique_companies': num_unique_companies, 'total_name_variants': total_aliases,
                'avg_variants_per_company': total_aliases / max(1, num_unique_companies)}


class M4OptimizedEmbedder:
    def __init__(self, model_name: str = 'all-roberta-large-v1', cache_file: Optional[str] = "cache/embedding_cache.pkl",
                 use_disk_cache: bool = True, max_memory_mb: int = 500, max_cache_entries: int = 50000,
                 batch_size: int = 32):
        if not SENTENCE_TRANSFORMERS_AVAILABLE:
            raise ImportError("SentenceTransformers library is required for M4OptimizedEmbedder.")

        from sentence_transformers import SentenceTransformer

        if torch.backends.mps.is_available() and torch.backends.mps.is_built():
            self.device = torch.device("mps")
            logger.info("✅ MPS available. Embedder will use Apple Metal GPU.")
        else:
            self.device = torch.device("cpu")
            logger.info("MPS not available. Embedder will use CPU.")

        self.model_name = model_name
        self.model = SentenceTransformer(model_name, device=self.device)
        logger.info(f"M4OptimizedEmbedder: Initialized model '{model_name}' on device '{self.device}'")
        self.use_disk_cache = use_disk_cache
        self.cache_file = Path(cache_file) if cache_file else None
        self._new_embeddings_count = 0
        self._save_interval = 200
        self._cache_hits_count = 0
        self._cache_misses_count = 0
        self.max_cache_entries = max_cache_entries
        self.batch_size = batch_size
        self._cache = None
        self._cache_loaded = True  # Set to True since we load immediately below
        self._cache_file_exists = False

        from collections import OrderedDict
        if not self.use_disk_cache:
            self._cache = OrderedDict()
            if self.cache_file and self.cache_file.exists():
                self._cache_file_exists = True
                try:
                    with open(self.cache_file, 'rb') as f:
                        loaded_data = pickle.load(f)
                    self._cache = OrderedDict(loaded_data) if isinstance(loaded_data, dict) else loaded_data
                    logger.info(
                        f"M4Embedder (Memory Cache): Loaded {len(self._cache)} embeddings from {self.cache_file}")
                    logger.info(f"Cache hit rate will be tracked. Current cache size: {len(self._cache)}")
                except Exception as e:
                    logger.warning(f"M4Embedder (Memory Cache): Could not load cache: {e}. Creating new.")
                    self._cache = OrderedDict()
            else:
                logger.info("M4Embedder (Memory Cache): No cache file found. Creating new.")
        else:
            try:
                self._cache = EmbeddingDiskCache(self.cache_file, max_memory_mb=max_memory_mb)
                logger.info(f"Using disk-based cache at {self.cache_file} with {max_memory_mb}MB memory limit")
            except ImportError:
                logger.error("DiskCache library not found. Please install it to use disk-based caching.")
                raise

    def _get_hash(self, text: str) -> str:
        return hashlib.md5(text.encode('utf-8')).hexdigest()

    def encode(self, texts: Union[str, List[str]], batch_size: Optional[int] = None, show_progress: bool = False,
               **kwargs) -> np.ndarray:
        if isinstance(texts, str):
            # This path is for single texts, used by _classify_single_ad_item
            # It needs to handle caching internally for efficiency.
            if self.use_disk_cache:
                cached = self._cache.get_embedding(texts)
                if cached is not None: return cached
            else:  # In-memory
                text_hash = self._get_hash(texts)
                if text_hash in self._cache:
                    return self._cache[text_hash]

            # If not in cache, encode it
            embedding = self.model.encode(texts, convert_to_numpy=True, device=self.device)

            # Store in cache
            if self.use_disk_cache:
                self._cache.set_embedding(texts, embedding)
            else:
                text_hash = self._get_hash(texts)
                if self.max_cache_entries and len(self._cache) >= self.max_cache_entries:
                    self._cache.popitem(last=False)
                self._cache[text_hash] = embedding
                self._cache.move_to_end(text_hash)
            return embedding

        # This path is for batch pre-warming where caching is handled externally.
        batch_size = batch_size or self.batch_size
        if show_progress:
            logger.info(f"M4Embedder: Encoding {len(texts)} texts on device '{self.device}'...")

        return self.model.encode(
            texts,
            batch_size=batch_size,
            show_progress_bar=show_progress,
            convert_to_numpy=True,
            device=self.device
        )

    def _migrate_pickle_to_disk(self):
        pickle_path = self.cache_file.with_suffix('.pkl')  # Assuming disk cache is .db
        if pickle_path.exists() and isinstance(getattr(self, '_cache', None), EmbeddingDiskCache):
            # Ensure .db doesn't exist or is older for migration to happen.
            # This logic might need refinement based on actual DiskCache behavior.
            db_path = self.cache_file  # Assuming self.cache_file is the .db path for DiskCache
            if db_path.exists() and db_path.stat().st_mtime > pickle_path.stat().st_mtime:
                logger.info(f"Disk cache {db_path} is newer than pickle {pickle_path}. Skipping migration.")
                return
            try:
                logger.info(f"Migrating pickle cache {pickle_path} to disk cache {db_path}...")
                with open(pickle_path, 'rb') as f:
                    old_cache = pickle.load(f)
                migrated_count = 0
                for text_or_hash, embedding in old_cache.items():
                    # DiskCache typically keys by text for `set_embedding`
                    # If old_cache is hash-keyed, this needs adjustment or re-encoding.
                    # Assuming old_cache was text-keyed for simplicity here.
                    # If it was hash-keyed, direct migration isn't possible without texts.
                    # For now, let's assume it was text-keyed for set_embedding.
                    # If it was hash-keyed, this part would fail or store hashes as texts.
                    # A more robust migration might re-encode texts if only hashes were stored.
                    # For this refactor, we'll assume text keys in pickle for direct migration.
                    if isinstance(text_or_hash, str):  # Check if key is text
                        self._cache.set_embedding(text_or_hash, embedding)  # text_or_hash is text
                        migrated_count += 1
                logger.info(f"Successfully migrated {migrated_count} embeddings to disk cache.")
                # os.remove(pickle_path) # Optionally remove old pickle cache
            except Exception as e:
                logger.warning(f"Failed to migrate pickle cache: {e}")

    def _load_cache_memory(self):  # Renamed for clarity
        self._cache_loaded = False  # For lazy loading memory cache
        if self.cache_file and self.cache_file.exists():
            self._cache_file_exists = True
            logger.info(f"M4Embedder (Memory Cache): Cache file found at {self.cache_file}, will load on demand")
        else:
            self._cache_file_exists = False
            logger.info(f"M4Embedder (Memory Cache): No existing cache at {self.cache_file}, will create new.")
            self._cache = {}
            self._cache_loaded = True  # Empty cache is "loaded"

    def _ensure_cache_loaded_memory(self):  # Renamed for clarity
        if not self.use_disk_cache and not self._cache_loaded and self._cache_file_exists:
            try:
                with open(self.cache_file, 'rb') as f:
                    self._cache = pickle.load(f)
                logger.info(f"M4Embedder (Memory Cache): Loaded {len(self._cache)} embeddings from {self.cache_file}")
            except Exception as e:
                logger.warning(f"M4Embedder (Memory Cache): Could not load from {self.cache_file}: {e}")
                self._cache = {}
            self._cache_loaded = True

    @lru_cache(maxsize=10240)  # LRU cache for single encodes benefits both disk and memory caches
    def _encode_single_text_with_lru(self, text: str) -> np.ndarray:
        if self.use_disk_cache:
            cached_embedding = self._cache.get_embedding(text)
            if cached_embedding is not None:
                self._cache_hits_count += 1
                return cached_embedding
        else:  # Memory cache
            self._ensure_cache_loaded_memory()
            text_hash = self._get_hash(text)
            if text_hash in self._cache:
                self._cache_hits_count += 1
                return self._cache[text_hash]

        self._cache_misses_count += 1
        # device=self.device.type already set in model init
        embedding = self.model.encode([text], convert_to_numpy=True, show_progress_bar=False)[0]

        if self.use_disk_cache:
            self._cache.set_embedding(text, embedding)
        else:  # Memory cache
            self._cache[text_hash] = embedding
            self._new_embeddings_count += 1
            if self._new_embeddings_count >= self._save_interval:
                self.save_cache()
        return embedding

    def save_cache(self, force=False):
        if self.use_disk_cache and hasattr(self._cache, 'sync'):
            self._cache.sync()
            return

        if not self.use_disk_cache:
            should_save = force or (
                    self._new_embeddings_count > 0 and self._new_embeddings_count >= self._save_interval)
            if self.cache_file and should_save and self._cache:
                try:
                    self.cache_file.parent.mkdir(parents=True, exist_ok=True)
                    with open(self.cache_file.with_suffix('.tmp'), 'wb') as f:
                        pickle.dump(self._cache, f)
                    self.cache_file.with_suffix('.tmp').rename(self.cache_file)
                    self._new_embeddings_count = 0
                except Exception as e:
                    logger.error(f"Could not save memory cache: {e}", exc_info=True)

    def close(self):
        if hasattr(self._cache, 'close'):
            self._cache.close()
        elif not self.use_disk_cache:
            self.save_cache(force=True)

    def __del__(self):
        try:
            self.close()
        except Exception:
            pass


class HybridClassifier:
    def __init__(self,
                 campaign_config_path: str,
                 text_processing_fields: List[str],
                 embedder_model_name: str = 'all-roberta-large-v1',
                 embedder_cache_file: Optional[str] = "cache/embedding_roberta-large-v1.pkl",
                 company_ner_model: str = "en_core_web_trf",
                 use_llm: bool = False,
                 llm_backend: str = 'ollama',
                 llm_model_path: Optional[str] = None,
                 llm_cache_file: Optional[str] = "cache/llm_response_cache.pkl",
                 enhanced_llm: bool = True,
                 ner_cache_file: str = "cache/ner_results_cache.pkl",
                 log_level: str = "INFO",
                 company_similarity_threshold: float = 85,
                 skip_terms_file: Optional[str] = None,
                 improve_rules_active: bool = False,
                 max_workers: Optional[int] = None,
                 embedding_similarity_threshold: float = 0.65,
                 embeddings_only: bool = False,
                 ner_only: bool = False,
                 batch_size: int = 32,
                 **kwargs):

        if SPACY_AVAILABLE:
            import spacy
        else:
            spacy = None

        logger.setLevel(log_level.upper())
        self.console = Console()
        self.text_processing_fields = text_processing_fields
        self.batch_size = batch_size
        self.embedding_similarity_threshold = embedding_similarity_threshold
        self.embeddings_only = embeddings_only
        self.ner_only = ner_only
        self.max_workers = max_workers
        self.company_similarity_threshold = company_similarity_threshold
        self.improve_rules_active = improve_rules_active
        self.use_llm = use_llm
        self.enhanced_llm = enhanced_llm
        self.skip_terms_file = skip_terms_file
        self.use_disk_cache = kwargs.get('use_disk_cache', True)  # Default to True if not specified
        self._ner_cache_hits = 0
        self._ner_cache_misses = 0
        self.kwargs = kwargs

        if embeddings_only and ner_only:
            raise ValueError("Cannot use both --embeddings-only and --ner-only flags simultaneously.")

        # --- Config and Rule Loading ---
        _invalid_summary_strings_input = ['NA', 'SKIPPED', '', 'None', 'null', "Summary generation failed",
                                          "no summary available"]
        self.invalid_summary_strings = {s.lower() for s in _invalid_summary_strings_input if isinstance(s, str)}

        self.skip_terms = []
        if skip_terms_file and os.path.exists(skip_terms_file):
            try:
                with open(skip_terms_file, 'r', encoding='utf-8') as f:
                    self.skip_terms = json.load(f)
                logger.info(f"📋 Loaded {len(self.skip_terms)} skip terms from {skip_terms_file}")
            except Exception as e:
                logger.warning(f"Failed to load skip terms: {e}")

        self.campaign_config_path = Path(campaign_config_path)
        self.known_campaigns_processed: List[Dict[str, Any]] = []
        self._campaign_config_map: Dict[str, Any] = {}
        self._load_and_process_campaign_config_data()
        self.ad_campaign_matcher = AdCampaignMatcher(self.known_campaigns_processed, logger)

        # --- Embedder Initialization ---
        self.embedder = None
        self.campaign_prototypes: Dict[str, np.ndarray] = {}
        if not self.ner_only and SENTENCE_TRANSFORMERS_AVAILABLE:
            logger.info(f"Initializing embedder with model: {embedder_model_name}")
            # Filter out kwargs not intended for M4OptimizedEmbedder
            embedder_kwargs = {k: v for k, v in kwargs.items() if
                               k in ['use_disk_cache', 'max_memory_mb', 'max_cache_entries']}
            self.embedder = M4OptimizedEmbedder(
                model_name=embedder_model_name, cache_file=embedder_cache_file, batch_size=self.batch_size,
                **embedder_kwargs
            )
            self._generate_campaign_prototypes()

        # --- NER Initialization with GPU Preference ---
        self.ner_model = None
        if not self.embeddings_only and SPACY_AVAILABLE and spacy:
            logger.info(f"Initializing SpaCy for NER with model '{company_ner_model}'...")
            try:
                if spacy.prefer_gpu():
                    logger.info("✅ SpaCy GPU (MPS) has been successfully enabled.")
                else:
                    logger.warning("⚠️ SpaCy GPU (MPS) not available. Falling back to CPU for NER.")
                self.ner_model = spacy.load(company_ner_model, disable=["parser", "tagger"])
            except Exception as e:
                logger.error(f"Fatal error loading SpaCy model '{company_ner_model}': {e}", exc_info=True)
                raise

        self.ner_cache_file = Path(ner_cache_file) if ner_cache_file else None
        self._ner_results_cache: Dict[str, Dict[str, List[str]]] = {}
        self._ner_cache_loaded = False
        self._ensure_ner_cache_loaded()

        # --- LLM and Helper Class Initialization ---
        self.llm_classifier: Optional[LocalLLMClassifier] = None
        if use_llm:
            self.llm_classifier = LocalLLMClassifier(backend=llm_backend, model_path=llm_model_path,
                                                     cache_file=llm_cache_file)

        self.company_normalizer = CompanyNameNormalizer(similarity_threshold=self.company_similarity_threshold)
        if FUZZYWUZZY_AVAILABLE:
            logger.info(
                f"🔍 Fuzzy matching enabled using {FUZZY_BACKEND} (threshold: {self.company_similarity_threshold}%)")

        self.law_firms_manager = None
        self.known_law_firms = set()
        try:
            if _using_new_architecture:
                law_firms_manager = create_manager_replacement('LawFirmsManager', {'dynamo_table': 'LawFirms', 'use_local': False})
            else:
                law_firms_manager = LawFirmsManager({'dynamo_table': 'LawFirms', 'use_local': False})
            all_law_firms = law_firms_manager.get_all_records()
            for firm in all_law_firms:
                if name := firm.get('Name'): self.known_law_firms.add(name.lower())
                if alias := firm.get('PageAlias'): self.known_law_firms.add(alias.lower())
            logger.info(f"Pre-loaded {len(self.known_law_firms)} law firm names/aliases.")
        except Exception as e:
            logger.warning(f"Failed to initialize LawFirmsManager: {e}")

        # Initialize sklearn components for DBSCAN clustering
        self.sklearn_available = False
        self.StandardScaler = None
        self.DBSCAN = None
        self.NearestNeighbors = None
        
        try:
            from sklearn.preprocessing import StandardScaler
            from sklearn.cluster import DBSCAN
            from sklearn.neighbors import NearestNeighbors
            
            self.StandardScaler = StandardScaler
            self.DBSCAN = DBSCAN
            self.NearestNeighbors = NearestNeighbors
            self.sklearn_available = True
            logger.info("scikit-learn found. DBSCAN clustering for 'Other' category is available.")
        except ImportError:
            logger.warning("scikit-learn not found. DBSCAN clustering for 'Other' category will be unavailable.")

    def _load_and_process_campaign_config_data(self):
        """Load campaign configuration from JSON file and process it."""
        try:
            if not self.campaign_config_path.exists():
                logger.error(f"Campaign config file not found: {self.campaign_config_path}")
                return

            with open(self.campaign_config_path, 'r', encoding='utf-8') as f:
                campaign_data = json.load(f)

            if not isinstance(campaign_data, list):
                logger.error(f"Campaign config must be a list, got: {type(campaign_data)}")
                return

            # Process campaign rules
            for campaign in campaign_data:
                if not isinstance(campaign, dict):
                    continue

                litigation_name = campaign.get('LitigationName')
                if not litigation_name:
                    continue

                # Create pattern objects for rule matching
                triggers = campaign.get('triggers', [])
                includes = campaign.get('include', [])  # Note: JSON uses 'include' not 'includes'
                excludes = campaign.get('exclude', [])  # Note: JSON uses 'exclude' not 'excludes'

                # Create regex patterns
                trigger_pattern = None
                include_pattern = None
                exclude_pattern = None

                if triggers:
                    # Use word boundaries for more accurate matching
                    trigger_pattern = re.compile('|'.join(r'\b' + re.escape(t) + r'\b' for t in triggers), re.IGNORECASE)
                if includes:
                    # Use word boundaries for more accurate matching
                    include_pattern = re.compile('|'.join(r'\b' + re.escape(i) + r'\b' for i in includes), re.IGNORECASE)
                if excludes:
                    # Use word boundaries for more accurate matching
                    exclude_pattern = re.compile('|'.join(r'\b' + re.escape(e) + r'\b' for e in excludes), re.IGNORECASE)

                processed_campaign = {
                    'LitigationName': litigation_name,
                    'Company': campaign.get('Company'),
                    'triggers': triggers,
                    'includes': includes,
                    'excludes': excludes,
                    'trigger_pattern': trigger_pattern,
                    'include_or_pattern': include_pattern,
                    'exclude_pattern': exclude_pattern
                }

                self.known_campaigns_processed.append(processed_campaign)
                self._campaign_config_map[litigation_name] = processed_campaign

            logger.info(f"Loaded {len(self.known_campaigns_processed)} campaign configurations")

        except json.JSONDecodeError as e:
            logger.error(f"Error decoding campaign config JSON: {e}")
        except Exception as e:
            logger.error(f"Error loading campaign config: {e}")

    def _generate_campaign_prototypes(self):
        if not self.embedder or not self.known_campaigns_processed:
            return

        logger.info("Generating campaign prototypes from rules...")

        all_sample_texts = []
        campaign_text_indices = {}
        unique_texts = set()

        for campaign in self.known_campaigns_processed:
            litigation_name = campaign['LitigationName']
            start_index = len(all_sample_texts)

            sample_texts = []
            triggers = campaign.get('triggers', [])
            for trigger in triggers:
                sample_texts.extend([trigger, f"lawsuit about {trigger}", f"{trigger} litigation"])

            includes = campaign.get('includes', [])
            sample_texts.extend(includes)

            for text in sample_texts:
                if text not in unique_texts:
                    unique_texts.add(text)
                    all_sample_texts.append(text)

            end_index = len(all_sample_texts)
            campaign_text_indices[litigation_name] = (start_index, end_index)

        if not all_sample_texts:
            return

        logger.info(f"Encoding {len(all_sample_texts)} unique sample texts for prototypes on GPU...")
        # Encode each text individually to leverage caching
        all_embeddings = []
        for text in all_sample_texts:
            embedding = self.embedder.encode(text, show_progress=False)
            all_embeddings.append(embedding)
        all_embeddings = np.array(all_embeddings)

        for litigation_name, (start, end) in campaign_text_indices.items():
            prototype_embeddings = all_embeddings[start:end]
            if prototype_embeddings.shape[0] > 0:
                self.campaign_prototypes[litigation_name] = np.mean(prototype_embeddings, axis=0)

        logger.info(f"Generated {len(self.campaign_prototypes)} campaign prototypes.")

    def _estimate_dbscan_eps(self, embeddings_scaled: np.ndarray, min_samples: int, percentile: float = 80.0) -> Optional[float]:
        """
        Estimate eps parameter for DBSCAN clustering based on k-nearest neighbors distances.
        
        Args:
            embeddings_scaled: Scaled embedding matrix
            min_samples: Minimum samples for DBSCAN
            percentile: Percentile of k-distances to use for eps estimation
            
        Returns:
            Estimated eps value or None if estimation fails
        """
        if not self.NearestNeighbors or len(embeddings_scaled) < min_samples:
            logger.warning(
                f"DBSCAN: NearestNeighbors not available or insufficient samples ({len(embeddings_scaled)} < {min_samples}) for eps estimation.")
            return None
        
        try:
            k_for_eps_calc = min_samples
            
            nn = self.NearestNeighbors(n_neighbors=k_for_eps_calc, metric='euclidean')
            nn.fit(embeddings_scaled)
            distances, _ = nn.kneighbors(embeddings_scaled)
            
            if distances.shape[1] < k_for_eps_calc:
                logger.warning(
                    f"DBSCAN eps: Requested {k_for_eps_calc} neighbors, but data only supports {distances.shape[1]}. Using {distances.shape[1]}.")
                k_for_eps_calc = distances.shape[1]
                if k_for_eps_calc == 0:
                    return None
            
            kth_distances = np.sort(distances[:, k_for_eps_calc - 1])
            eps_value = np.percentile(kth_distances, percentile)
            logger.info(
                f"Estimated DBSCAN eps: {eps_value:.4f} (using {k_for_eps_calc}-th neighbor distances at {percentile}th percentile)")
            
            if eps_value <= 1e-6:
                logger.warning(
                    f"Estimated eps ({eps_value:.4f}) is very small or zero. DBSCAN might not be effective.")
                non_zero_kth_distances = kth_distances[kth_distances > 1e-6]
                if len(non_zero_kth_distances) > 0:
                    num_to_avg = max(1, int(len(non_zero_kth_distances) * 0.15))
                    eps_value = np.mean(np.sort(non_zero_kth_distances)[:num_to_avg]) * 1.5
                    logger.info(f"Adjusted eps based on smallest non-zero k-th distances to: {eps_value:.4f}")
                    if eps_value <= 1e-6:
                        logger.warning(f"Adjusted eps ({eps_value:.4f}) is still too small. DBSCAN may fail.")
                        return None
                else:
                    logger.warning(
                        "All k-th distances are zero or near-zero. Cannot determine a suitable eps for DBSCAN.")
                    return None
            return eps_value
        except Exception as e:
            logger.error(f"Error during DBSCAN eps estimation: {e}", exc_info=True)
            return None

    def _get_or_compute_ner_entities(self, text_to_analyze: str, ad_data_context: Dict[str, Any]) -> Dict[
        str, List[str]]:
        """
        Retrieves NER entities from cache or computes them using SpaCy.
        """
        # NER backend is fixed to SpaCy
        if not self.ner_model:
            logger.warning(
                f"AD_ID {ad_data_context.get('AdArchiveID', 'Unknown')} - SpaCy model not initialized, returning empty entities")
            return {}
        if not text_to_analyze or not text_to_analyze.strip():  # Handle empty input
            return {}
        # logger.debug(f"AD_ID {ad_data_context.get('AdArchiveID', 'Unknown')} - Using SpaCy NER for processing text of length {len(text_to_analyze)}")

        cache_key = text_to_analyze  # Use raw text as cache key
        self._ensure_ner_cache_loaded()  # Lazy load if not already

        if self.ner_cache_file and cache_key in self._ner_results_cache:
            self._ner_cache_hits += 1
            return self._ner_results_cache[cache_key]

        self._ner_cache_misses += 1
        entities_by_type = defaultdict(list)
        try:
            # Process with SpaCy (Flair path removed)
            max_spacy_len = getattr(self.ner_model, 'max_length', 1000000)  # Get max length from model if available
            doc = self.ner_model(text_to_analyze[:max_spacy_len])
            entities_by_type = self._extract_entities_from_doc(doc, ad_data_context)  # Pass context
        except Exception as e:
            ad_id_log = ad_data_context.get('AdArchiveID', 'Unknown_in_NER_compute')
            logger.error(f"AD_ID: {ad_id_log} - SpaCy NER processing error: {e}",
                         exc_info=False)  # exc_info=False for brevity

        result_entities = dict(entities_by_type)  # Convert defaultdict to dict for consistent return type
        if self.ner_cache_file:  # Only try to cache if a file is specified
            self._ner_results_cache[cache_key] = result_entities
        return result_entities

    def _classify_single_ad_item(self, ad_data_item: Dict[str, Any]) -> ClassificationResult:
        ad_id = str(ad_data_item.get('AdArchiveID', ad_data_item.get('ad_id', f'unknown_single_{id(ad_data_item)}')))
        raw_text, normalized_text = HybridClassifier._prepare_ad_text_for_processing(ad_data_item, self.text_processing_fields,
                                                                         self.invalid_summary_strings)

        current_details = {
            "raw_text_snippet": raw_text[:200] if raw_text else "EMPTY_RAW_TEXT_FROM_PREP",
            "normalized_text_snippet": normalized_text[:200] if normalized_text else "EMPTY_NORM_TEXT_FROM_PREP",
            "extracted_entities": {}
        }

        all_extracted_entities: Dict[str, List[str]] = {}
        if not self.embeddings_only:
            # NER model is now just self.ner_model (SpaCy)
            if self.ner_model and raw_text and raw_text.strip():  # Check raw_text for NER
                try:
                    all_extracted_entities = self._get_or_compute_ner_entities(raw_text, ad_data_item)  # Pass raw_text
                    current_details['extracted_entities'] = all_extracted_entities
                except Exception as ner_error:
                    logger.error(f"AD_ID: {ad_id} - Error in _get_or_compute_ner_entities: {ner_error}", exc_info=False)
            elif not self.ner_model:  # Log if NER model wasn't available
                logger.debug(f"AD_ID: {ad_id} - SpaCy NER model not available, skipping NER.")
        else:
            logger.debug(f"AD_ID: {ad_id} - Skipping NER processing (embeddings-only mode)")

        final_campaign: str = "Other"
        final_confidence: float = 0.0
        final_method: str = "none"
        rule_source_val: Optional[str] = None
        rule_confidence_val: float = 0.0  # Specificity score from rules
        embedding_confidence_val: float = 0.0  # Similarity score from embeddings
        llm_confidence_val: float = 0.0  # Confidence from LLM
        campaign_config_entry = None  # To store config of matched campaign

        if normalized_text and self.ad_campaign_matcher:
            campaign_rule, conf_rule_match = self.ad_campaign_matcher.match_ad_text(normalized_text,
                                                                                    ad_id_for_log=ad_id)
            rule_confidence_val = conf_rule_match  # This is specificity score from matcher
            if campaign_rule and campaign_rule != "Other":
                final_campaign = campaign_rule
                final_confidence = conf_rule_match  # Using specificity score as confidence
                final_method = "rule"
                rule_source_val = "manual_config"
                campaign_config_entry = self._get_campaign_config_by_name(campaign_rule)
                if campaign_config_entry:
                    logger.debug(
                        f"AD_ID {ad_id} - Rule match campaign config for '{campaign_rule}': Company='{campaign_config_entry.get('Company', 'NOT FOUND')}'")

        # Skip term matching - check for consecutive occurrence ignoring punctuation
        if final_method != "rule" and self.skip_terms and raw_text:  # Ensure raw_text is not empty
            # Remove punctuation for skip term matching
            import string
            text_no_punct = raw_text.translate(str.maketrans('', '', string.punctuation))
            text_no_punct_lower = text_no_punct.lower()
            
            skip_term_found = False
            for skip_term in self.skip_terms:
                # Remove punctuation from skip term too
                skip_term_no_punct = skip_term.translate(str.maketrans('', '', string.punctuation)).lower()
                # Check if skip term appears as consecutive words
                if skip_term_no_punct in text_no_punct_lower:
                    skip_term_found = True
                    break
            
            if skip_term_found:
                companies_s, original_s = self.extract_company_info(ad_data_item, "General", all_extracted_entities,
                                                                    None)
                company_s = companies_s[0] if companies_s else None
                original_name_s = original_s[0] if original_s else None
                current_details.update(
                    {"final_extracted_companies": companies_s, "final_original_company_names": original_s,
                     "final_extracted_company": company_s, "final_original_company_name": original_name_s})
                # Extract title and body for skip_terms result
                title = ad_data_item.get('Title', '')
                body = ad_data_item.get('Body', '')
                
                return ClassificationResult(ad_id=ad_id, campaign="General", company=company_s,
                                            original_name=original_name_s,
                                            confidence=1.0, method="skip_terms", needs_review=False,
                                            # Skip terms are definitive
                                            rule_source=rule_source_val, rule_confidence=rule_confidence_val,
                                            embedding_confidence=embedding_confidence_val,
                                            llm_confidence=llm_confidence_val,
                                            dbscan_cluster=None,  # No clustering for skip_terms
                                            title=title,
                                            body=body,
                                            ner_results=all_extracted_entities,
                                            details=current_details)

        proceed_to_embedding = (final_method != "rule") or \
                               (final_method == "rule" and final_campaign == "Other")

        if self.embedder and self.campaign_prototypes and not self.ner_only:
            if raw_text and raw_text.strip():  # Check raw_text for embedding
                # M4OptimizedEmbedder.encode handles single string or list; Flair specific logic removed
                ad_embedding = self.embedder.encode(raw_text, show_progress=False)  # Pass raw_text
                if ad_embedding is not None and ad_embedding.ndim > 0 and np.any(
                        ad_embedding):  # Check if embedding is valid
                    best_sim = -1.0
                    campaign_embed_match = None
                    valid_proto_data = {name: emb for name, emb in self.campaign_prototypes.items() if
                                        emb is not None and emb.ndim > 0 and np.any(emb)}
                    if valid_proto_data:
                        proto_names_list = list(valid_proto_data.keys())
                        proto_embs_array = np.array(list(valid_proto_data.values()))
                        if proto_embs_array.ndim == 2 and ad_embedding.ndim == 1 and proto_embs_array.shape[1] == \
                                ad_embedding.shape[0]:
                            ad_embedding_norm = np.linalg.norm(ad_embedding)
                            if ad_embedding_norm > 1e-8:  # Avoid division by zero
                                proto_embs_norms = np.linalg.norm(proto_embs_array, axis=1)
                                valid_indices_mask = proto_embs_norms > 1e-8
                                if np.any(valid_indices_mask):
                                    active_proto_embs = proto_embs_array[valid_indices_mask]
                                    active_proto_norms = proto_embs_norms[valid_indices_mask]
                                    active_proto_names = [proto_names_list[i] for i, keep in
                                                          enumerate(valid_indices_mask) if keep]
                                    if active_proto_embs.size > 0:
                                        sims = np.dot(active_proto_embs, ad_embedding) / (
                                                active_proto_norms * ad_embedding_norm)
                                        if len(sims) > 0:
                                            idx_max_sim = np.argmax(sims)
                                            best_sim = sims[idx_max_sim]
                                            if idx_max_sim < len(active_proto_names): campaign_embed_match = \
                                                active_proto_names[idx_max_sim]
                    embedding_confidence_val = float(best_sim) if best_sim > -1.0 else 0.0
                    if campaign_embed_match: logger.debug(
                        f"AD_ID: {ad_id} - Embedding match: '{campaign_embed_match}' (sim: {embedding_confidence_val:.3f})")

                    if proceed_to_embedding and campaign_embed_match and campaign_embed_match != "Other" and embedding_confidence_val > self.embedding_similarity_threshold:
                        exclude_match = False
                        temp_campaign_config_entry = self._get_campaign_config_by_name(
                            campaign_embed_match)  # Use a temp var
                        if temp_campaign_config_entry:
                            exclude_pattern = temp_campaign_config_entry.get('exclude_pattern')
                            if exclude_pattern and normalized_text and exclude_pattern.search(normalized_text):
                                logger.debug(
                                    f"AD_ID: {ad_id} - Embedding match '{campaign_embed_match}' rejected by exclude pattern.")
                                exclude_match = True
                        if not exclude_match and (
                                embedding_confidence_val > final_confidence or final_campaign == "Other"):
                            final_campaign = campaign_embed_match
                            final_confidence = embedding_confidence_val
                            final_method = "embedding"
                            rule_source_val = None
                            campaign_config_entry = temp_campaign_config_entry  # Assign to main var if match is accepted
            elif not raw_text or not raw_text.strip():
                logger.debug(f"AD_ID: {ad_id} - No raw text for embedding.")
        elif self.ner_only:
            logger.debug(f"AD_ID: {ad_id} - Skipping embeddings (NER-only mode).")

        proceed_to_llm = (final_method == "none") or \
                         (final_method == "embedding" and final_confidence < 0.65) or \
                         (
                                 final_method == "rule" and rule_confidence_val < 0.65 and final_campaign == "Other")  # Using rule_confidence_val here

        if self.use_llm and self.llm_classifier and proceed_to_llm:
            candidate_campaigns_for_llm = list(self.campaign_prototypes.keys())  # Use all known campaigns
            if raw_text and raw_text.strip() and candidate_campaigns_for_llm:  # Check raw_text and candidates
                campaign_llm_match, conf_llm_match = self.llm_classifier.classify(raw_text,
                                                                                  candidate_campaigns_for_llm)  # Pass raw_text
                llm_confidence_val = conf_llm_match
                if campaign_llm_match and campaign_llm_match != "Other" and llm_confidence_val > 0.50:  # Threshold for LLM basic classification
                    if llm_confidence_val > final_confidence or final_campaign == "Other":
                        final_campaign = campaign_llm_match
                        final_confidence = llm_confidence_val
                        final_method = "llm"
                        rule_source_val = None
                        campaign_config_entry = self._get_campaign_config_by_name(final_campaign)
                elif final_method == "none":  # LLM also failed or its confidence was too low
                    final_method = "llm_failed"
                    # Try individual suggestion if still "none" and LLM is available
                    if self.use_llm and self.llm_classifier and raw_text and raw_text.strip():  # Re-check LLM availability
                        suggested_campaign, suggested_confidence = self._suggest_individual_campaign(raw_text,
                                                                                                     all_extracted_entities)  # Pass raw_text
                        if suggested_campaign and suggested_campaign != "Other" and suggested_confidence > 0.6:  # Threshold for suggestion
                            final_campaign = suggested_campaign
                            final_confidence = suggested_confidence
                            final_method = "llm_suggestion"
                            # Try to get config for suggested campaign, might be new
                            campaign_config_entry = self._get_campaign_config_by_name(final_campaign)
                            if not campaign_config_entry: logger.info(
                                f"AD_ID {ad_id} - LLM suggested new campaign '{final_campaign}'. No existing config.")

        # Retrieve campaign config entry if it changed and wasn't set (e.g. by embedding or LLM)
        if final_method in ["embedding", "llm",
                            "llm_suggestion"] and campaign_config_entry is None and final_campaign not in ["Other",
                                                                                                           "General",
                                                                                                           "Unknown"]:
            campaign_config_entry = self._get_campaign_config_by_name(final_campaign)

        companies, original_names = self.extract_company_info(ad_data_item, final_campaign, all_extracted_entities,
                                                              campaign_config_entry)
        company = companies[0] if companies else None
        original_name = original_names[0] if original_names else None
        current_details.update({"final_extracted_companies": companies, "final_original_company_names": original_names,
                                "final_extracted_company": company, "final_original_company_name": original_name})

        needs_review = (final_method != "rule") and \
                       (final_confidence < 0.7 or (final_method == "none" and final_campaign == "Other"))
        if final_method == "skip_terms": needs_review = False
        if final_method == "rule" and final_confidence >= 0.85: needs_review = False  # Using actual confidence (specificity)
        if final_campaign == "Other" and final_method != "skip_terms": needs_review = True

        # Extract title and body from ad data
        title = ad_data_item.get('Title', '')
        body = ad_data_item.get('Body', '')
        
        # Only set dbscan_cluster for non-rule methods (will be populated later)
        dbscan_cluster = None if final_method == "rule" else None
        
        return ClassificationResult(
            ad_id=ad_id, campaign=final_campaign, company=company, original_name=original_name,
            confidence=final_confidence, method=final_method, needs_review=needs_review,
            rule_source=rule_source_val, rule_confidence=rule_confidence_val,
            embedding_confidence=embedding_confidence_val, llm_confidence=llm_confidence_val,
            dbscan_cluster=dbscan_cluster,
            title=title,
            body=body,
            ner_results=all_extracted_entities,
            details=current_details
        )

    def classify_batch(self, ads_data_list: List[Dict[str, Any]], batch_size: int = 256,
                       output_csv: Optional[str] = None, output_deduplicated_csv: Optional[str] = None,
                       deduplication_fields: Optional[List[str]] = None) -> List[ClassificationResult]:
        """
        Classify a batch of ads and optionally save results to CSV files.
        This method is optimized to perform NER and embedding in batches to improve performance.

        Args:
            ads_data_list: List of ad data dictionaries
            batch_size: The size of chunks to process for the final classification loop.
            output_csv: Path to save all results
            output_deduplicated_csv: Path to save deduplicated results
            deduplication_fields: Fields to use for deduplication

        Returns:
            List of ClassificationResult objects
        """
        if not ads_data_list:
            logger.warning("classify_batch called with no ads to classify.")
            return []

        console = Console()
        total_ads = len(ads_data_list)

        # --- Phase 1: NER Pre-warming (Single Efficient Pass) ---
        if not self.embeddings_only and self.ner_model:
            console.print("\n[cyan]🔍 Pre-warming NER cache for all unique texts...[/cyan]")
            # CRITICAL FIX: Removed the 'n_process' argument. It is not a valid
            # parameter for the updated _batch_process_ner method.
            self._batch_process_ner(ads_data_list, pipe_batch_size=128)
            console.print("[green]✓ NER cache warming complete.[/green]")

        # --- Phase 2: Embedding Pre-warming (Single Efficient Pass) ---
        if self.embedder and not self.ner_only:
            console.print("\n[cyan]🧠 Pre-warming embedding cache for all unique texts...[/cyan]")

            # Collect all unique, non-empty texts from the entire dataset
            unique_texts_to_embed = set()
            for ad in track(ads_data_list, description="[cyan]Preparing texts...", console=console, transient=True):
                raw_text, _ = HybridClassifier._prepare_ad_text_for_processing(ad, self.text_processing_fields,
                                                                   self.invalid_summary_strings)
                if raw_text:
                    unique_texts_to_embed.add(raw_text)

            # Convert set to list for encoding
            unique_texts_list = list(unique_texts_to_embed)

            if unique_texts_list:
                # Ensure in-memory cache is loaded before checking
                if not self.embedder.use_disk_cache and hasattr(self.embedder, '_ensure_cache_loaded_memory'):
                    self.embedder._ensure_cache_loaded_memory()
                
                # Check which texts are NOT already in cache
                uncached_texts = []
                for text in unique_texts_list:
                    if self.embedder.use_disk_cache:
                        if self.embedder._cache.get_embedding(text) is None:
                            uncached_texts.append(text)
                    else:  # In-memory cache
                        text_hash = self.embedder._get_hash(text)
                        if text_hash not in self.embedder._cache:
                            uncached_texts.append(text)
                
                # Debug: show cache statistics
                cache_size = len(self.embedder._cache) if not self.embedder.use_disk_cache else "N/A (disk cache)"
                logger.info(f"Cache contains {cache_size} embeddings, checking {len(unique_texts_list):,} unique texts...")
                
                if uncached_texts:
                    logger.info(f"Found {len(unique_texts_list):,} unique texts, {len(uncached_texts):,} not in cache. Encoding uncached texts...")
                    # Encode only the uncached texts
                    embeddings = self.embedder.model.encode(
                        uncached_texts,
                        batch_size=self.batch_size,
                        show_progress_bar=True,
                        convert_to_numpy=True,
                        device=self.embedder.device
                    )
                    
                    # Store the new embeddings in cache
                    for text, embedding in zip(uncached_texts, embeddings):
                        if self.embedder.use_disk_cache:
                            self.embedder._cache.set_embedding(text, embedding)
                        else:
                            text_hash = self.embedder._get_hash(text)
                            if self.embedder.max_cache_entries and len(self.embedder._cache) >= self.embedder.max_cache_entries:
                                self.embedder._cache.popitem(last=False)
                            self.embedder._cache[text_hash] = embedding
                            self.embedder._cache.move_to_end(text_hash)
                    
                    console.print(f"[green]✓ Embedding cache warming complete. Added {len(uncached_texts):,} new embeddings.[/green]")
                else:
                    logger.info(f"All {len(unique_texts_list):,} unique texts are already in the cache.")
                    console.print("[green]✓ All embeddings already cached. No pre-warming needed.[/green]")
            else:
                logger.info("No unique texts found to pre-warm embedding cache.")

        import gc
        gc.collect()

        # --- Phase 3: Classification (Looping with fully-warmed cache) ---
        console.print("\n[cyan]🏷️  Classifying ads (using cached data)...[/cyan]")
        classification_results = []
        with Progress(TextColumn("[green]Classifying Ads..."), BarColumn(), TextColumn("{task.percentage:>3.0f}%"),
                      TimeRemainingColumn(), console=console, transient=False) as progress:
            classify_task = progress.add_task("Classify", total=total_ads)

            # This loop is now extremely fast as both NER and embeddings are cached.
            for ad_data in ads_data_list:
                try:
                    result = self._classify_single_ad_item(ad_data)
                    classification_results.append(result)
                except Exception as e:
                    ad_id = ad_data.get('AdArchiveID', 'unknown')
                    logger.error(f"Failed to classify ad {ad_id}: {e}", exc_info=True)
                    # Create a placeholder error result
                    error_result = ClassificationResult(
                        ad_id=ad_id, campaign="Error", company=None, original_name=None,
                        confidence=0.0, method="worker_error", needs_review=True, rule_source=None
                    )
                    classification_results.append(error_result)
                progress.update(classify_task, advance=1)

        # --- Phase 3.5: DBSCAN Clustering for "Other" Category ---
        if self.improve_rules_active and self.DBSCAN and self.embedder:
            console.print("\n[cyan]🔍 Performing DBSCAN clustering for 'Other' category items...[/cyan]")
            
            # Create a map from ad_id to ad_data for efficient lookup
            ads_data_map = {str(ad.get('AdArchiveID', ad.get('ad_id', ''))): ad 
                           for ad in ads_data_list 
                           if ad.get('AdArchiveID') or ad.get('ad_id')}
            
            # Collect "Other" results with their embeddings
            other_results_with_embeddings = []
            other_embeddings = []
            
            for result in classification_results:
                if result.campaign == "Other":
                    # Find the original ad data using ad_id
                    ad_data = ads_data_map.get(str(result.ad_id))
                    if ad_data:
                        # Use the same text preparation as classification
                        raw_text, _ = HybridClassifier._prepare_ad_text_for_processing(
                            ad_data, self.text_processing_fields, self.invalid_summary_strings
                        )
                        
                        if raw_text and raw_text.strip():
                            # Get embedding (will use cache if available)
                            embedding = self.embedder.encode(raw_text, show_progress=False)
                            if embedding is not None and embedding.ndim > 0:
                                other_results_with_embeddings.append(result)
                                other_embeddings.append(embedding)
            
            if len(other_embeddings) >= 5:  # Need at least 5 samples for DBSCAN
                # Convert to numpy array and scale
                embeddings_array = np.array(other_embeddings)
                
                if self.StandardScaler:
                    scaler = self.StandardScaler()
                    embeddings_scaled = scaler.fit_transform(embeddings_array)
                else:
                    embeddings_scaled = embeddings_array
                
                # Estimate eps parameter
                min_samples = min(5, len(other_embeddings) // 10)
                eps = self._estimate_dbscan_eps(embeddings_scaled, min_samples)
                
                if eps:
                    # Perform DBSCAN clustering
                    clusterer = self.DBSCAN(eps=eps, min_samples=min_samples, metric='euclidean')
                    cluster_labels = clusterer.fit_predict(embeddings_scaled)
                    
                    # Assign cluster labels to results
                    for i, (result, label) in enumerate(zip(other_results_with_embeddings, cluster_labels)):
                        if label != -1:  # -1 is noise in DBSCAN
                            result.dbscan_cluster = int(label)
                    
                    # Count clusters
                    unique_clusters = set(label for label in cluster_labels if label != -1)
                    noise_count = sum(1 for label in cluster_labels if label == -1)
                    
                    console.print(f"[green]✓ DBSCAN clustering complete:[/green]")
                    console.print(f"  • Found {len(unique_clusters)} clusters")
                    console.print(f"  • {noise_count} items marked as noise")
                    console.print(f"  • {len(other_embeddings)} total 'Other' items processed")
                else:
                    console.print("[yellow]⚠ Could not estimate DBSCAN eps parameter[/yellow]")
            else:
                console.print(f"[yellow]⚠ Not enough 'Other' items for clustering ({len(other_embeddings)} < 5)[/yellow]")

        # --- Phase 4: Save Results & Report ---
        if output_csv:
            self.save_results_to_csv(classification_results, ads_data_list, output_csv)

        if output_deduplicated_csv and deduplication_fields:
            self.save_deduplicated_results_to_csv(classification_results, ads_data_list, output_deduplicated_csv, deduplication_fields)
            console.print(f"[green]✓ Saved deduplicated results to {output_deduplicated_csv}[/green]")

        self._print_classification_summary(classification_results)
        
        # Print cache statistics
        console.print("\n[bold cyan]Cache Performance Statistics:[/bold cyan]")
        
        # Embedding cache stats
        if self.embedder:
            embedder_hits = getattr(self.embedder, '_cache_hits_count', 0)
            embedder_misses = getattr(self.embedder, '_cache_misses_count', 0)
            embedder_total = embedder_hits + embedder_misses
            if embedder_total > 0:
                embedder_hit_rate = (embedder_hits / embedder_total) * 100
                console.print(f"[green]Embedding Cache:[/green]")
                console.print(f"  • Hits: {embedder_hits:,} ({embedder_hit_rate:.1f}%)")
                console.print(f"  • Misses: {embedder_misses:,}")
                console.print(f"  • Total requests: {embedder_total:,}")
            
            # Save embedding cache
            if hasattr(self.embedder, 'save_cache'):
                self.embedder.save_cache(force=True)
                console.print(f"  • Cache saved to: {self.embedder.cache_file}")
        
        # NER cache stats
        if self._ner_cache_hits > 0 or self._ner_cache_misses > 0:
            ner_total = self._ner_cache_hits + self._ner_cache_misses
            ner_hit_rate = (self._ner_cache_hits / ner_total) * 100
            console.print(f"\n[green]NER Cache:[/green]")
            console.print(f"  • Hits: {self._ner_cache_hits:,} ({ner_hit_rate:.1f}%)")
            console.print(f"  • Misses: {self._ner_cache_misses:,}")
            console.print(f"  • Total requests: {ner_total:,}")
            
            # Save NER cache
            self.save_ner_cache()
            console.print(f"  • Cache saved to: {self.ner_cache_file}")

        return classification_results


    def _print_classification_summary(self, results: List[ClassificationResult]):
        """Print summary statistics of classification results."""
        console = Console()

        # Count by campaign
        campaign_counts = Counter(r.campaign for r in results if r.campaign)
        unknown_count = sum(1 for r in results if not r.campaign)

        # Count by method
        method_counts = Counter(r.method for r in results)

        # Count needs_review
        needs_review_count = sum(1 for r in results if r.needs_review)

        # Create summary table
        table = Table(title="Classification Summary", box=box.ROUNDED)
        table.add_column("Metric", style="cyan")
        table.add_column("Count", style="magenta")

        table.add_row("Total Ads", f"{len(results):,}")
        table.add_row("Classified", f"{len(results) - unknown_count:,}")
        table.add_row("Unknown", f"{unknown_count:,}")
        table.add_row("Needs Review", f"{needs_review_count:,}")

        console.print("\n")
        console.print(table)

        # Top campaigns table
        if campaign_counts:
            top_campaigns = Table(title="Top 10 Campaigns", box=box.ROUNDED)
            top_campaigns.add_column("Campaign", style="cyan")
            top_campaigns.add_column("Count", style="magenta")

            for campaign, count in campaign_counts.most_common(10):
                top_campaigns.add_row(campaign, f"{count:,}")

            console.print("\n")
            console.print(top_campaigns)

        # Classification methods table
        method_table = Table(title="Classification Methods", box=box.ROUNDED)
        method_table.add_column("Method", style="cyan")
        method_table.add_column("Count", style="magenta")

        for method, count in method_counts.most_common():
            method_table.add_row(method, f"{count:,}")

        console.print("\n")
        console.print(method_table)

    def _ensure_ner_cache_loaded(self):
        """Ensure NER cache is loaded into memory."""
        if self._ner_cache_loaded:
            return

        if self.ner_cache_file and self.ner_cache_file.exists():
            try:
                with open(self.ner_cache_file, 'rb') as f:
                    self._ner_results_cache = pickle.load(f)
                logger.info(f"Loaded {len(self._ner_results_cache)} NER results from cache")
            except Exception as e:
                logger.warning(f"Failed to load NER cache: {e}")
                self._ner_results_cache = {}

        self._ner_cache_loaded = True

    def save_ner_cache(self):
        """Save NER results cache to disk."""
        if not self.ner_cache_file:
            return

        try:
            self.ner_cache_file.parent.mkdir(parents=True, exist_ok=True)
            with open(self.ner_cache_file, 'wb') as f:
                pickle.dump(self._ner_results_cache, f)
            logger.info(f"Saved {len(self._ner_results_cache)} NER results to cache")
        except Exception as e:
            logger.error(f"Failed to save NER cache: {e}", exc_info=True)

    @staticmethod
    def _prepare_ad_text_for_processing(ad_data: Dict[str, Any], text_fields: List[str],
                                        invalid_strings: set) -> Tuple[str, str]:
        """
        Prepare ad text for processing by extracting and normalizing text from specified fields.

        Returns:
            Tuple of (raw_text, normalized_text)
        """
        text_parts = []

        for field in text_fields:
            field_value = ad_data.get(field, '')
            if field_value and str(field_value).strip():
                # Skip invalid strings like placeholders
                if field == 'Summary' and str(field_value) in invalid_strings:
                    continue
                text_parts.append(str(field_value).strip())

        raw_text = ' '.join(text_parts)

        # Basic normalization
        normalized_text = raw_text.lower()
        normalized_text = re.sub(r'\s+', ' ', normalized_text)  # Normalize whitespace
        normalized_text = normalized_text.strip()

        return raw_text, normalized_text

    def _extract_entities_from_doc(self, doc, ad_data_context: Dict[str, Any]) -> Dict[str, List[str]]:
        """
        Extract entities from a SpaCy doc object.

        Args:
            doc: SpaCy Doc object
            ad_data_context: Ad data for context (used for logging)

        Returns:
            Dictionary mapping entity types to lists of entity texts
        """
        entities_by_type = defaultdict(list)

        for ent in doc.ents:
            # Filter for relevant entity types
            if ent.label_ in ['ORG', 'PERSON', 'GPE', 'PRODUCT']:
                entities_by_type[ent.label_].append(ent.text)

        return dict(entities_by_type)

    def _get_campaign_config_by_name(self, campaign_name: str) -> Optional[Dict[str, Any]]:
        """Get campaign configuration by name."""
        return self._campaign_config_map.get(campaign_name)

    def extract_company_info(self, ad_data: Dict[str, Any], campaign_name: str,
                             entities: Dict[str, List[str]], raw_text: str) -> Tuple[List[str], List[str]]:
        """
        Extract company information from entities.

        Returns:
            Tuple of (companies_list, original_names_list)
        """
        companies = []
        original_names = []

        # Look for organization entities
        orgs = entities.get('ORG', [])

        for org in orgs:
            # Skip law firms by checking if the name is in known law firms
            if self.known_law_firms and org.lower() in self.known_law_firms:
                continue

            normalized = self.company_normalizer._normalize_company_name(org) if self.company_normalizer else org
            if normalized and normalized not in companies:
                companies.append(normalized)
                original_names.append(org)

        # If no company found, check campaign config for default company
        if not companies:
            campaign_config = self._get_campaign_config_by_name(campaign_name)
            if campaign_config and campaign_config.get('Company'):
                companies.append(campaign_config['Company'])
                original_names.append(campaign_config['Company'])

        return companies, original_names

    def _suggest_individual_campaign(self, ad_data: Dict[str, Any], text: str,
                                     entities: Dict[str, List[str]]) -> Optional[str]:
        """
        Suggest a campaign based on individual ad analysis.

        This is a placeholder for more sophisticated campaign suggestion logic.
        """
        # For now, return None to indicate no suggestion
        return None

    def _batch_process_ner(self, all_ads_data: List[Dict[str, Any]], pipe_batch_size: int = 256):
        if not self.ner_model:
            return

        self._ensure_ner_cache_loaded()

        unique_texts = set(
            HybridClassifier._prepare_ad_text_for_processing(ad, self.text_processing_fields, self.invalid_summary_strings)[0]
            for ad in all_ads_data
        )
        texts_for_ner_pipe = [text for text in unique_texts if text and text not in self._ner_results_cache]

        if not texts_for_ner_pipe:
            logger.info("Batch NER (Cache Warming): All texts are already in the cache.")
            return

        logger.info(f"Batch NER: Processing {len(texts_for_ner_pipe):,} unique new texts on GPU...")
        max_spacy_len = self.ner_model.max_length

        try:
            docs_iterator = self.ner_model.pipe(
                (text[:max_spacy_len] for text in texts_for_ner_pipe),
                batch_size=pipe_batch_size
            )

            for doc, original_text in track(
                    zip(docs_iterator, texts_for_ner_pipe),
                    description="[bold green]NER on GPU...[/bold green]",
                    total=len(texts_for_ner_pipe),
            ):
                self._ner_results_cache[original_text] = self._extract_entities_from_doc(doc, {})

        except Exception as e:
            logger.error(f"Error during NER pipe processing: {e}", exc_info=True)

        self.save_ner_cache()

    def save_results_to_csv(self,
                            results: List[ClassificationResult],
                            original_ads_data: List[Dict[str, Any]],
                            filepath: str):
        if not results:
            logger.info("No results to save to CSV.")
            return

        data_to_save = []
        ads_data_map = {str(ad.get('AdArchiveID', ad.get('ad_id'))): ad for ad in original_ads_data if
                        ad.get('AdArchiveID') or ad.get('ad_id')}

        for res in results:
            row = dataclasses.asdict(res)
            original_ad = ads_data_map.get(str(res.ad_id))
            raw_text_for_output = ""  # Default empty string
            if original_ad:
                raw_text_for_output, _ = HybridClassifier._prepare_ad_text_for_processing(
                    original_ad, self.text_processing_fields, self.invalid_summary_strings
                )
            row['raw_text_combined'] = raw_text_for_output
            # Ensure DBSCAN_Cluster field is consistently named as in dataclass
            row['DBSCAN_Cluster'] = row.pop('dbscan_cluster',
                                            None)  # Use .pop to handle if key is missing or already correct

            details_flat = row.pop('details', {})  # Remove details dict and flatten its contents
            for k, v in details_flat.items():
                # Flair-specific column naming removed, always use detail_{k}
                row[f"detail_{k}"] = str(v)
            data_to_save.append(row)

        df = pd.DataFrame(data_to_save)

        # Sort by campaign first, then by DBSCAN_Cluster for 'Other' campaigns
        if 'DBSCAN_Cluster' in df.columns:
            # Create a sorting key where 'Other' campaigns with DBSCAN clusters are sorted by cluster ID
            df['_sort_key'] = df.apply(
                lambda r: (r['campaign'],
                           r['DBSCAN_Cluster'] if r['campaign'] == 'Other' and pd.notna(r['DBSCAN_Cluster']) else float(
                               'inf')),
                axis=1
            )
            df = df.sort_values(by=['_sort_key'])
            df = df.drop(columns=['_sort_key'])

        try:
            Path(filepath).parent.mkdir(parents=True, exist_ok=True)
            df.to_csv(filepath, index=False, encoding='utf-8')
            self.console.print(f"[green]✓ Classification results saved to {filepath}[/green]")
        except Exception as e:
            self.console.print(f"[red]Error saving results to CSV {filepath}: {e}[/red]")
            logger.error(f"Error saving results to CSV {filepath}: {e}", exc_info=True)

    def save_deduplicated_results_to_csv(self,
                                         results: List[ClassificationResult],
                                         original_ads_data: List[Dict[str, Any]],
                                         filepath: str,
                                         deduplication_fields: List[str]):
        if not results:
            logger.info("No results to deduplicate and save.")
            return
        if not deduplication_fields:
            logger.warning(
                "No deduplication fields provided. Cannot deduplicate. Saving non-deduplicated results instead.")
            self.save_results_to_csv(results, original_ads_data, filepath)  # Save non-deduplicated instead
            return

        df_data = []
        ads_data_map = {str(ad.get('AdArchiveID', ad.get('ad_id'))): ad for ad in original_ads_data if
                        ad.get('AdArchiveID') or ad.get('ad_id')}

        for res in results:
            row = dataclasses.asdict(res)
            original_ad = ads_data_map.get(str(res.ad_id))
            raw_text_for_output = ""  # Default empty string
            dedup_text_parts = []

            if original_ad:
                raw_text_for_output, _ = HybridClassifier._prepare_ad_text_for_processing(
                    original_ad, self.text_processing_fields, self.invalid_summary_strings
                )
                for field_name in deduplication_fields:
                    field_val = original_ad.get(field_name)
                    if isinstance(field_val,
                                  str) and field_val.strip() and field_val.lower() not in self.invalid_summary_strings:
                        dedup_text_parts.append(field_val.strip())

            row['raw_text_combined'] = raw_text_for_output
            row['deduplication_key_text'] = _normalize_text_for_matching(" ".join(dedup_text_parts))
            row['DBSCAN_Cluster'] = row.pop('dbscan_cluster', None)  # Use .pop

            details_flat = row.pop('details', {})
            for k, v in details_flat.items():
                # Flair-specific column naming removed
                row[f"detail_{k}"] = str(v)
            df_data.append(row)

        df = pd.DataFrame(df_data)
        if df.empty:
            logger.info("DataFrame for deduplication is empty.")
            return

        logger.info(f"Attempting deduplication on {len(df)} results using fields: {deduplication_fields}")
        df.sort_values(by='confidence', ascending=False, inplace=True)
        # Use .copy() to avoid SettingWithCopyWarning on the slice
        deduplicated_df = df.drop_duplicates(subset=['deduplication_key_text'], keep='first').copy()

        if 'deduplication_key_text' in deduplicated_df.columns:
            deduplicated_df.drop(columns=['deduplication_key_text'], inplace=True)  # Use inplace=True

        num_dropped = len(df) - len(deduplicated_df)
        self.console.print(
            f"[cyan]Deduplication: Kept {len(deduplicated_df)} unique ads, removed {num_dropped} duplicates.[/cyan]")

        if 'DBSCAN_Cluster' in deduplicated_df.columns:
            # Use .loc for assignment to avoid SettingWithCopyWarning
            deduplicated_df.loc[:, '_sort_key'] = deduplicated_df.apply(
                lambda r: (r['campaign'],
                           r['DBSCAN_Cluster'] if r['campaign'] == 'Other' and pd.notna(r['DBSCAN_Cluster']) else float(
                               'inf')),
                axis=1
            )
            deduplicated_df.sort_values(by=['_sort_key'], inplace=True)
            deduplicated_df.drop(columns=['_sort_key'], inplace=True)

        try:
            Path(filepath).parent.mkdir(parents=True, exist_ok=True)
            deduplicated_df.to_csv(filepath, index=False, encoding='utf-8')
            self.console.print(f"[green]✓ Deduplicated classification results saved to {filepath}[/green]")
        except Exception as e:
            self.console.print(f"[red]Error saving deduplicated results to CSV {filepath}: {e}[/red]")
            logger.error(f"Error saving deduplicated results: {e}", exc_info=True)


class OptimizedDynamoDBLoader:
    """Parallel DynamoDB loader - Scans ALL attributes."""

    def __init__(self, table_name: str = 'FBAdArchive', region: str = 'us-west-2',
                 endpoint_url: Optional[str] = None, max_workers: Optional[int] = 16,
                 image_hash_table: str = 'FBImageHash'):  # Removed text_fields_to_project
        self.table_name = table_name
        self.image_hash_table = image_hash_table
        self.region = region
        self.endpoint_url = endpoint_url
        self.max_workers = max_workers or (os.cpu_count() or 4)
        self.console = Console()

        logger.info(f"DynamoDBLoader initialized to scan ALL attributes from table: {self.table_name}")

        try:
            import boto3
            from boto3.dynamodb.conditions import Key, Attr
            self.boto3 = boto3
            self.Key = Key
            self.Attr = Attr
        except ImportError:
            logger.error("Boto3 is not installed. DynamoDB loading will not work.")
            self.boto3 = None
            self.Key = None
            self.Attr = None

    def _convert_decimals(self, obj):
        if isinstance(obj, self.boto3.dynamodb.types.Decimal):
            return float(obj)
        if isinstance(obj, list):
            return [self._convert_decimals(i) for i in obj]
        if isinstance(obj, dict):
            return {k: self._convert_decimals(v) for k, v in obj.items()}
        return obj

    def load_data_parallel(self, limit: Optional[int] = None, start_date: Optional[str] = None,
                           batch_size: int = 500, stream_to_disk: bool = True) -> pd.DataFrame:
        """
        Load data from DynamoDB with memory-efficient streaming and batch processing.

        Args:
            limit: Maximum number of items to load
            start_date: Filter for items with StartDate >= this value
            batch_size: Number of items to process in each page from DynamoDB
            stream_to_disk: If True, stream large results to temporary parquet files
        """
        if not self.boto3:
            return pd.DataFrame()

        import tempfile
        import shutil

        dynamodb_args = {'region_name': self.region}
        if self.endpoint_url:
            dynamodb_args['endpoint_url'] = self.endpoint_url
            logger.info(f"Using DynamoDB endpoint: {self.endpoint_url}")

        dynamodb = self.boto3.resource('dynamodb', **dynamodb_args)
        table = dynamodb.Table(self.table_name)

        scan_kwargs = {'Limit': min(batch_size, 1000)}

        if start_date and self.Attr:
            scan_kwargs['FilterExpression'] = self.Attr('StartDate').gte(start_date)
            logger.info(f"Filtering ads with StartDate >= {start_date}")

        total_items_in_table = table.item_count

        temp_dir = None
        if stream_to_disk and (not limit or limit > 20000):
            temp_dir = tempfile.mkdtemp()
            logger.info(f"Using temporary directory for large dataset streaming: {temp_dir}")

        all_items_in_memory = []
        temp_file_paths = []

        try:
            items_processed = 0
            page_num = 0
            with Progress(console=self.console, transient=True) as progress:
                task_desc = f"[cyan]Scanning DynamoDB '{self.table_name}'..."
                scan_task = progress.add_task(task_desc, total=limit or total_items_in_table)

                response = table.scan(**scan_kwargs)

                while True:
                    batch_items = response.get('Items', [])
                    if not batch_items:
                        break

                    processed_items = [self._convert_decimals(item) for item in batch_items]

                    if not processed_items:
                        logger.warning("DynamoDB scan returned an empty page, continuing...")
                        if 'LastEvaluatedKey' not in response: break
                        scan_kwargs['ExclusiveStartKey'] = response['LastEvaluatedKey']
                        response = table.scan(**scan_kwargs)
                        continue

                    batch_df = pd.DataFrame(processed_items)

                    # --- Data Cleaning and Type Coercion ---
                    # Define columns and their expected types to prevent errors with to_parquet
                    string_columns = ['AdCreativeId', 'AdArchiveID', 'PageID', 'PageName',
                                      'BylineText', 'SpenderInfo', 'ImageURL', 'VideoURL',
                                      'CapturedImageURL', 'CampaignName', 'Title', 'Body',
                                      'Summary', 'LinkDescription']
                    numeric_columns = ['SpendLower', 'SpendUpper', 'ImpressionsLower', 'ImpressionsUpper']

                    for col in string_columns:
                        if col in batch_df.columns:
                            # Fill NaNs with empty string and convert to string type robustly
                            batch_df[col] = batch_df[col].fillna('').astype(str)
                            # A final replace for any literal 'nan' strings that might appear
                            batch_df[col] = batch_df[col].replace('nan', '')

                    for col in numeric_columns:
                        if col in batch_df.columns:
                            # Coerce to numeric, setting errors to NaN, then fill with 0
                            batch_df[col] = pd.to_numeric(batch_df[col], errors='coerce').fillna(0)

                    # --- End Data Cleaning ---

                    if temp_dir:
                        page_num += 1
                        temp_file = os.path.join(temp_dir, f"batch_{page_num}.parquet")
                        batch_df.to_parquet(temp_file, index=False, engine='pyarrow')
                        temp_file_paths.append(temp_file)
                    else:
                        all_items_in_memory.append(batch_df)  # Append DF, not dict list

                    items_processed += len(batch_items)
                    progress.update(scan_task, completed=items_processed)

                    if limit and items_processed >= limit:
                        break

                    if 'LastEvaluatedKey' not in response:
                        break

                    scan_kwargs['ExclusiveStartKey'] = response['LastEvaluatedKey']
                    if limit:
                        scan_kwargs['Limit'] = min(batch_size, limit - items_processed, 1000)

                    response = table.scan(**scan_kwargs)

            if temp_file_paths:
                self.console.print(
                    f"[cyan]Combining {len(temp_file_paths)} temporary files into final DataFrame...[/cyan]")
                df = pd.concat((pd.read_parquet(f) for f in temp_file_paths), ignore_index=True)
            elif all_items_in_memory:
                df = pd.concat(all_items_in_memory, ignore_index=True)
            else:
                df = pd.DataFrame()

            if df.empty:
                self.console.print(f"[yellow]Warning: No data loaded from DynamoDB table '{self.table_name}'.[/yellow]")
                return df

            if limit:
                df = df.head(limit)

            selected_count = len(df)
            percentage = (selected_count / total_items_in_table) * 100 if total_items_in_table > 0 else 100.0
            self.console.print(
                f"[green]✓ Loaded {selected_count:,} items ({percentage:.1f}% of {total_items_in_table:,} total items)[/green]")

            if 'AdArchiveID' not in df.columns and 'ad_id' in df.columns:
                df.rename(columns={'ad_id': 'AdArchiveID'}, inplace=True)
            if 'AdArchiveID' not in df.columns:
                logger.error("CRITICAL: 'AdArchiveID' column not found and could not be inferred.")
                df['AdArchiveID'] = [f"generated_id_{i}" for i in range(len(df))]

            return df

        except Exception as e:
            self.console.print(f"[bold red]Error loading data from DynamoDB: {e}[/bold red]")
            logger.error(f"DynamoDB load error: {e}", exc_info=True)
            return pd.DataFrame()
        finally:
            if temp_dir:
                try:
                    shutil.rmtree(temp_dir)
                    logger.info(f"Cleaned up temporary directory: {temp_dir}")
                except Exception as e:
                    logger.warning(f"Failed to clean up temp directory {temp_dir}: {e}")


class RuleImprover:
    """Analyze results and suggest rule improvements, including clustering and LLM analysis."""

    def __init__(self,
                 campaign_config_path: Path,
                 embedder: Optional[M4OptimizedEmbedder],
                 llm_classifier: Optional[LocalLLMClassifier],
                 text_processing_fields: List[str],
                 invalid_summary_strings: set,
                 logger_instance: logging.Logger):
        self.campaign_config_path = campaign_config_path
        self.embedder = embedder
        self.llm_classifier = llm_classifier
        self.text_processing_fields = text_processing_fields
        self.invalid_summary_strings = invalid_summary_strings
        self.logger = logger_instance
        self.console = Console()
        self.campaign_rules = self._load_rules()

        self.sklearn_available = False
        self.KMeans = None
        self.silhouette_score = None
        self.StandardScaler = None
        self.DBSCAN = None
        self.NearestNeighbors = None

        try:
            from sklearn.cluster import KMeans, DBSCAN
            from sklearn.metrics import silhouette_score
            from sklearn.preprocessing import StandardScaler
            from sklearn.neighbors import NearestNeighbors
            import numpy as np  # Ensure numpy is available if used directly here

            self.KMeans = KMeans
            self.DBSCAN = DBSCAN
            self.NearestNeighbors = NearestNeighbors
            self.silhouette_score = silhouette_score
            self.StandardScaler = StandardScaler
            self.sklearn_available = True
            self.logger.info("scikit-learn found. Clustering (KMeans, DBSCAN) for rule improvement is available.")
        except ImportError:
            self.logger.warning(
                "scikit-learn not found. Advanced rule improvement via clustering will be unavailable.")
            self.sklearn_available = False
        global np  # Make numpy available for NpEncoder if it's defined late
        if 'np' not in globals() and self.sklearn_available:  # cheap way to ensure np is global if sklearn is used
            pass

    def _load_rules(self) -> List[Dict[str, Any]]:
        if not self.campaign_config_path.exists():
            self.logger.error(f"Campaign config file not found at {self.campaign_config_path} for RuleImprover.")
            return []
        try:
            with open(self.campaign_config_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            if isinstance(data, list):
                return data
            else:
                self.logger.error(f"Campaign config at {self.campaign_config_path} is not a list.")
                return []
        except json.JSONDecodeError:
            self.logger.error(f"Error decoding JSON from {self.campaign_config_path}")
            return []
        except Exception as e:
            self.logger.error(f"Failed to load campaign rules from {self.campaign_config_path}: {e}")
            return []

    def save_improved_config(self, suggestions: Dict, output_path_str: str):
        output_path = Path(output_path_str)
        self.console.print(
            f"[yellow]Saving rule suggestions to {output_path}. Manual review and integration needed.[/yellow]")
        try:
            output_path.parent.mkdir(parents=True, exist_ok=True)
            with open(output_path, 'w', encoding='utf-8') as f:
                class NpEncoder(json.JSONEncoder):
                    def default(self, obj):
                        if isinstance(obj, np.integer):
                            return int(obj)
                        if isinstance(obj, np.floating):
                            return float(obj)
                        if isinstance(obj, np.ndarray):
                            return obj.tolist()
                        if isinstance(obj, (Path)):
                            return str(obj)
                        return super(NpEncoder, self).default(obj)

                json.dump(suggestions, f, indent=2, cls=NpEncoder, ensure_ascii=False)
            self.console.print(f"[green]✓ Saved rule suggestions to {output_path}[/green]")
        except Exception as e:
            self.console.print(f"[red]Error saving suggestions to {output_path}: {e}[/red]")
            self.logger.error(f"Error saving rule suggestions to {output_path}: {e}", exc_info=True)

    def _get_optimal_k_for_kmeans(self, embeddings_scaled: np.ndarray, max_k: int = 10) -> int:
        if not self.sklearn_available or self.silhouette_score is None or self.KMeans is None:
            heuristic_k = int(max(2, min(5, np.ceil(np.sqrt(len(embeddings_scaled)) / 2.5))))
            self.logger.debug(f"KMeans: sklearn not fully available, using heuristic K={heuristic_k}")
            return heuristic_k
        if len(embeddings_scaled) < 4:
            self.logger.debug(f"KMeans: Not enough samples ({len(embeddings_scaled)}) for silhouette. Returning K=1.")
            return 1 if len(embeddings_scaled) > 0 else 0

        upper_k_bound = min(max_k + 1, len(embeddings_scaled))
        k_range = range(2, upper_k_bound)

        if not list(k_range):
            self.logger.debug(
                f"KMeans: k_range empty for silhouette. n_samples={len(embeddings_scaled)}, max_k_to_test={upper_k_bound - 1}. Returning K=1.")
            return 1 if len(embeddings_scaled) > 0 else 0

        silhouette_scores = []
        for k_val in k_range:
            try:
                kmeans = self.KMeans(n_clusters=k_val, random_state=42, n_init='auto')
                cluster_labels = kmeans.fit_predict(embeddings_scaled)
                if len(set(cluster_labels)) > 1:
                    score = self.silhouette_score(embeddings_scaled, cluster_labels)
                    silhouette_scores.append(score)
                else:
                    silhouette_scores.append(-1)
            except Exception as e:
                self.logger.warning(f"Error calculating silhouette score for K={k_val} (KMeans): {e}")
                silhouette_scores.append(-1)

        if not silhouette_scores or max(silhouette_scores) <= -1:
            heuristic_k = int(max(2, min(5, np.ceil(np.sqrt(len(embeddings_scaled)) / 2.5))))
            self.logger.debug(f"KMeans: Silhouette scores not helpful, using heuristic K={heuristic_k}")
            return heuristic_k

        optimal_k = k_range[np.argmax(silhouette_scores)]
        self.logger.info(f"Optimal K for KMeans: {optimal_k} (Max Silhouette: {max(silhouette_scores):.3f})")
        return optimal_k

    def _estimate_dbscan_eps(self, embeddings_scaled: np.ndarray, min_samples: int, percentile: float = 80.0) -> \
            Optional[float]:
        if not self.NearestNeighbors or len(embeddings_scaled) < min_samples:
            self.logger.warning(
                f"DBSCAN: NearestNeighbors not available or insufficient samples ({len(embeddings_scaled)} < {min_samples}) for eps estimation.")
            return None
        try:
            k_for_eps_calc = min_samples

            nn = self.NearestNeighbors(n_neighbors=k_for_eps_calc, metric='euclidean')
            nn.fit(embeddings_scaled)
            distances, _ = nn.kneighbors(embeddings_scaled)

            if distances.shape[1] < k_for_eps_calc:
                self.logger.warning(
                    f"DBSCAN eps: Requested {k_for_eps_calc} neighbors, but data only supports {distances.shape[1]}. Using {distances.shape[1]}.")
                k_for_eps_calc = distances.shape[1]
                if k_for_eps_calc == 0: return None

            kth_distances = np.sort(distances[:, k_for_eps_calc - 1])
            eps_value = np.percentile(kth_distances, percentile)
            self.logger.info(
                f"Estimated DBSCAN eps: {eps_value:.4f} (using {k_for_eps_calc}-th neighbor distances at {percentile}th percentile)")

            if eps_value <= 1e-6:
                self.logger.warning(
                    f"Estimated eps ({eps_value:.4f}) is very small or zero. DBSCAN might not be effective.")
                non_zero_kth_distances = kth_distances[kth_distances > 1e-6]
                if len(non_zero_kth_distances) > 0:
                    num_to_avg = max(1, int(len(non_zero_kth_distances) * 0.15))
                    eps_value = np.mean(np.sort(non_zero_kth_distances)[:num_to_avg]) * 1.5
                    self.logger.info(f"Adjusted eps based on smallest non-zero k-th distances to: {eps_value:.4f}")
                    if eps_value <= 1e-6:
                        self.logger.warning(f"Adjusted eps ({eps_value:.4f}) is still too small. DBSCAN may fail.")
                        return None
                else:
                    self.logger.warning(
                        "All k-th distances are zero or near-zero. Cannot determine a suitable eps for DBSCAN.")
                    return None
            return eps_value
        except Exception as e:
            self.logger.error(f"Error during DBSCAN eps estimation: {e}", exc_info=True)
            return None

    def _get_frequent_entities_from_samples(self,
                                            sample_items_for_llm: List[Dict[str, Any]],
                                            min_entity_occurrence: int = 2,
                                            top_n_entities: int = 3) -> Tuple[List[str], List[str]]:
        org_counts = Counter()
        prod_counts = Counter()

        for item_data in sample_items_for_llm:
            details = item_data.get('classification_result_details', {})
            entities = details.get('extracted_entities', {})

            for org_entity in entities.get("ORG", []): org_counts[org_entity] += 1
            for prod_entity in entities.get("PRODUCT", []): prod_counts[prod_entity] += 1

        frequent_orgs = [org for org, count in org_counts.most_common(top_n_entities) if count >= min_entity_occurrence]
        frequent_prods = [prod for prod, count in prod_counts.most_common(top_n_entities) if
                          count >= min_entity_occurrence]

        return frequent_orgs, frequent_prods

    def analyze_and_suggest(self, classified_ads: List[ClassificationResult],
                            ads_data_list_param: List[Dict[str, Any]]) -> Dict:
        suggestions = {
            "new_triggers": defaultdict(Counter),
            "potential_new_campaigns_ngram": Counter(),
            "potential_new_campaigns_llm_cluster": []
        }
        stopwords = set([
            "a", "an", "the", "is", "are", "was", "were", "be", "been", "being", "have", "has", "had", "do", "does",
            "did", "will", "would", "should", "can", "could", "may", "might", "must", "and", "but", "or", "nor", "for",
            "so",
            "yet", "if", "then", "else", "when", "where", "why", "how", "what", "which", "who", "whom", "whose",
            "this", "that", "these", "those", "am", "i", "you", "he", "she", "it", "we", "they", "me", "him", "her",
            "us", "them", "my", "your", "his", "its", "our", "their", "mine", "yours", "hers", "ours", "theirs",
            "to", "of", "in", "on", "at", "by", "from", "with", "about", "above", "after", "again", "against", "all",
            "any", "both", "each", "few", "more", "most", "other", "some", "such", "no", "not", "only", "own", "same",
            "than", "too", "very", "s", "t", "just", "don", "now", "d", "ll", "m", "o", "re", "ve", "y",
            "ad", "advertisement", "advertising", "attorney", "attorneys", "lawyer", "lawyers", "law", "legal", "firm",
            "llp", "llc", "pc", "call", "contact", "click", "visit", "learn", "more", "apply", "submit", "find", "out",
            "free",
            "consultation", "evaluation", "case", "review", "claim", "claims", "lawsuit", "litigation", "settlement",
            "compensation", "entitled",
            "eligible", "rights", "options", "help", "assistance", "confidential", "risk", "obligation", "no", "fee",
            "unless",
            "win", "recover", "injured", "injury", "harm", "suffered", "affected", "victim", "victims", "important",
            "notice",
            "attention", "act", "now", "today", "limited", "time", "deadline", "sponsored", "results", "message",
            "information",
            "you've", "we're", "they're", "it's", "don't", "qualified", "experienced", "professional", "group",
            "center", "financial", "medical", "treatment", "diagnosis", "condition", "health", "doctor", "hospital",
            "insurance",
            "investigation", "seeking", "participants", "if", "may", "possible", "potential", "serious", "significant",
            "get", "up", "down", "over", "under", "again", "further", "then", "once", "here", "there", "when", "where",
            "why", "how", "all", "any", "both", "each", "few", "more", "most", "other", "some", "such", "no", "nor",
            "not",
            "only", "own", "same", "so", "than", "too", "very", "s", "t", "can", "will", "just", "don", "should",
            "january", "february", "march", "april", "may", "june", "july", "august", "september", "october",
            "november", "december", "monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday",
            "amp", "ll", "inc", "corp", "ltd", "co", "etc", "eg", "ie", "vs"
        ])

        ads_data_map_by_id = {str(ad.get('AdArchiveID', ad.get('ad_id', ''))): ad
                              for ad in ads_data_list_param
                              if ad.get('AdArchiveID') or ad.get('ad_id')}

        other_items_data = []

        for result in classified_ads:
            current_ad_dict = ads_data_map_by_id.get(str(result.ad_id))
            if not current_ad_dict:
                self.logger.warning(
                    f"RuleImprover: Ad data for ad_id {result.ad_id} not found in ads_data_map. Skipping.")
                continue

            ad_content_raw, ad_content_norm = HybridClassifier._prepare_ad_text_for_processing(
                current_ad_dict,
                self.text_processing_fields,
                self.invalid_summary_strings
            )

            words = re.findall(r'\b[a-z][a-z0-9\'-]*[a-z0-9]\b', ad_content_norm)
            meaningful_words = [w for w in words if w not in stopwords and len(w) > 2]

            if result.campaign == "Other":
                if ad_content_raw.strip():
                    other_items_data.append({
                        "id": result.ad_id,
                        "raw_text": ad_content_raw,
                        "normalized_text": ad_content_norm,
                        "classification_result_details": result.details
                    })
                elif result.campaign == "Other":
                    self.logger.debug(f"RuleImprover: Ad {result.ad_id} is 'Other' but has no processable raw text.")

                if meaningful_words and len(meaningful_words) > 1:
                    for j in range(len(meaningful_words) - 1):
                        bigram = tuple(sorted((meaningful_words[j], meaningful_words[j + 1])))
                        suggestions["potential_new_campaigns_ngram"][bigram] += 1

            elif result.needs_review and meaningful_words:
                current_campaign_triggers = []
                for camp_rule in self.campaign_rules:
                    if camp_rule.get("LitigationName") == result.campaign:
                        current_campaign_triggers = [_normalize_text_for_matching(t) for t in
                                                     camp_rule.get("triggers", [])]
                        break
                for word in meaningful_words:
                    if word not in current_campaign_triggers:
                        suggestions["new_triggers"][result.campaign][word] += 1

        suggestions["potential_new_campaigns_ngram"] = {
            " ".join(phrase_tuple): count
            for phrase_tuple, count in suggestions["potential_new_campaigns_ngram"].most_common(10)
            if count > 2
        }
        suggestions["new_triggers"] = {
            camp: [wc[0] for wc in words.most_common(5) if wc[1] > 1]
            for camp, words in suggestions["new_triggers"].items()
        }

        MIN_OTHER_ITEMS_FOR_CLUSTERING = 10
        MIN_CLUSTER_SIZE_FOR_LLM_ANALYSIS = 3
        DBSCAN_MIN_SAMPLES_PARAM = 4
        DBSCAN_EPS_ESTIMATION_MIN_SAMPLES = DBSCAN_MIN_SAMPLES_PARAM
        DBSCAN_EPS_PERCENTILE = 80.0

        if self.embedder and self.sklearn_available and len(other_items_data) >= MIN_OTHER_ITEMS_FOR_CLUSTERING:
            self.logger.info(f"RuleImprover: Found {len(other_items_data)} 'Other' items with text for clustering.")

            # Sample data if too large to prevent memory issues
            MAX_ITEMS_FOR_CLUSTERING = 5000
            if len(other_items_data) > MAX_ITEMS_FOR_CLUSTERING:
                self.logger.warning(
                    f"Too many items for clustering ({len(other_items_data)}). Sampling {MAX_ITEMS_FOR_CLUSTERING} items.")
                import random
                random.seed(42)  # For reproducibility
                other_items_data = random.sample(other_items_data, MAX_ITEMS_FOR_CLUSTERING)

            other_texts_for_embedding = [item['raw_text'] for item in other_items_data]

            try:
                # Use smaller batch size for memory efficiency
                other_embeddings = self.embedder.encode(other_texts_for_embedding,
                                                        batch_size=8,
                                                        show_progress=False)

                if other_embeddings is not None and len(other_embeddings) > 0 and other_embeddings.ndim == 2:
                    scaler = self.StandardScaler()
                    embeddings_scaled = scaler.fit_transform(other_embeddings)

                    cluster_labels_for_llm = None
                    used_method_for_llm = None

                    if self.DBSCAN and self.NearestNeighbors:
                        eps_val = self._estimate_dbscan_eps(embeddings_scaled, DBSCAN_EPS_ESTIMATION_MIN_SAMPLES,
                                                            DBSCAN_EPS_PERCENTILE)
                        if eps_val and eps_val > 1e-6:
                            dbscan = self.DBSCAN(eps=eps_val, min_samples=DBSCAN_MIN_SAMPLES_PARAM, metric='euclidean')
                            labels_dbscan = dbscan.fit_predict(embeddings_scaled)
                            n_clusters_dbscan = len(set(labels_dbscan)) - (1 if -1 in labels_dbscan else 0)
                            if n_clusters_dbscan > 0 and n_clusters_dbscan < len(other_items_data) * 0.8:
                                cluster_labels_for_llm = labels_dbscan
                                used_method_for_llm = "DBSCAN"
                                self.logger.info(
                                    f"DBSCAN found {n_clusters_dbscan} clusters. Eps={eps_val:.3f}, MinPts={DBSCAN_MIN_SAMPLES_PARAM}")
                            else:
                                self.logger.info(
                                    f"DBSCAN produced trivial clustering ({n_clusters_dbscan} clusters). Trying KMeans.")
                        else:
                            self.logger.info("DBSCAN eps estimation failed or eps too small. Trying KMeans.")

                    if used_method_for_llm is None and self.KMeans:
                        k_optimal = self._get_optimal_k_for_kmeans(embeddings_scaled)
                        if k_optimal > 1 and k_optimal < len(other_items_data) * 0.8:
                            kmeans = self.KMeans(n_clusters=k_optimal, random_state=42, n_init='auto')
                            labels_kmeans = kmeans.fit_predict(embeddings_scaled)
                            cluster_labels_for_llm = labels_kmeans
                            used_method_for_llm = "KMeans"
                            self.logger.info(f"KMeans found {k_optimal} clusters.")
                        else:
                            self.logger.info(
                                f"KMeans optimal K is {k_optimal}, not suitable for LLM analysis of distinct groups.")

                    if cluster_labels_for_llm is not None and used_method_for_llm:
                        unique_cluster_ids = sorted(
                            [l for l in set(cluster_labels_for_llm) if l != -1])
                        for cluster_id in unique_cluster_ids:
                            member_indices = [i for i, label in enumerate(cluster_labels_for_llm) if
                                              label == cluster_id]
                            if len(member_indices) >= MIN_CLUSTER_SIZE_FOR_LLM_ANALYSIS:
                                self.logger.info(
                                    f"{used_method_for_llm} Cluster {cluster_id}: Analyzing with LLM ({len(member_indices)} items).")

                                sample_indices = np.random.choice(member_indices, size=min(len(member_indices), 5),
                                                                  replace=False)
                                sample_items_for_entities = [other_items_data[idx] for idx in sample_indices]
                                sample_texts_for_llm_prompt = [item['raw_text'][:500] for item in
                                                               sample_items_for_entities]

                                frequent_orgs_hint, frequent_prods_hint = self._get_frequent_entities_from_samples(
                                    sample_items_for_entities)

                                can_use_llm_for_rule_improvement = (
                                        self.llm_classifier and self.llm_classifier.model and self.llm_classifier.model_path
                                )

                                if can_use_llm_for_rule_improvement:
                                    prompt_text = (
                                        "You are an expert legal advertising analyst. Your task is to identify potential new litigation campaigns by finding specific, distinguishing themes in ad texts.\n"
                                        "The following text snippets are from online advertisements categorized as 'Other' because they did not fit well into predefined legal litigation categories. "
                                        "They form a distinct group based on semantic clustering.\n\n"
                                        "Based SOLELY on the provided texts and contextual hints, suggest:\n"
                                        "1. A CONCISE and SPECIFIC name for a new potential litigation campaign (e.g., 'XYZ Drug Heart Problems Lawsuit', 'Faulty ABC Product Injuries'). AVOID overly generic names like 'Injury Claims' or 'Consumer Protection'.\n"
                                        "2. A list of 3-5 common and DISTINCTIVE keywords or phrases (1-3 words each) found in these texts that could serve as triggers for this new campaign. AVOID extremely common words like 'lawsuit', 'compensation', 'attorney', 'call', 'free', 'help', 'you', 'your', 'been', 'have', 'suffered', 'may', 'if', 'eligible', 'entitled' UNLESS they are part of a very specific product or company name.\n"
                                    )
                                    if frequent_orgs_hint or frequent_prods_hint:
                                        prompt_text += "\nContextual Hints from Named Entity Recognition within these samples (These might be central to the litigation theme):\n"
                                        if frequent_orgs_hint: prompt_text += f"- Potentially relevant organizations: {', '.join(frequent_orgs_hint)}\n"
                                        if frequent_prods_hint: prompt_text += f"- Potentially relevant products/services: {', '.join(frequent_prods_hint)}\n"
                                    prompt_text += (
                                        "\nReview the following examples of input ad text snippets and desired JSON output (notice the specificity):\n\n"
                                        "Example 1:\nInput Ad Text Snippet: \"Our investigation suggests that the Ram Promaster 2022-2023 models are incapable of actually activating the 8th and 9th gears under normal driving conditions.\"\nCorresponding Suggested JSON:\n{\"campaign_name\": \"Ram Promaster Transmission Defect Lawsuit\", \"trigger_keywords\": [\"Ram Promaster\", \"transmission defect\", \"gears not activating\", \"2022-2023 models\", \"gear shift problem\"]}\n\n"
                                        "Example 2:\nInput Ad Text Snippet: \"Nitrous oxide addiction can start small — but lead to serious health consequences and a lasting impact on your life. If you or someone you care about is struggling with this addiction and experiencing harm, we’re here to help. Legal options may be available.\"\nCorresponding Suggested JSON:\n{\"campaign_name\": \"Nitrous Oxide Inhalant Addiction Harm\", \"trigger_keywords\": [\"nitrous oxide\", \"whippits\", \"inhalant addiction\", \"neurological damage\", \"serious health effects\"]}\n\n"
                                        "Example 3:\nInput Ad Text Snippet: \"Sharpen App users: If you accessed study videos through iOS, Android, or Chrome starting 10/11/2022 and had an account, you could be eligible for compensation. Tap 'Apply Now' to find out if you're eligible. Bursor & Fisher, P.A. ...\"\nCorresponding Suggested JSON:\n{\"campaign_name\": \"Sharpen App User Data Privacy Claim\", \"trigger_keywords\": [\"Sharpen App\", \"study videos\", \"user data\", \"Bursor & Fisher\", \"iOS Android privacy\"]}\n\n"
                                        "Example 4:\nInput Ad Text Snippet: \"Ticketmaster customers—were you charged more than expected for tickets? You’re not alone, and these practices could be illegal. We recently filed a case in California and we are now looking for participants in Maryland. ... Tycko & Zavareei LLP ...\"\nCorresponding Suggested JSON:\n{\"campaign_name\": \"Ticketmaster Excessive Fees Lawsuit (Maryland)\", \"trigger_keywords\": [\"Ticketmaster\", \"junk fees\", \"hidden charges\", \"Maryland consumers\", \"Tycko & Zavareei\"]}\n\n"
                                        "IMPORTANT: Respond ONLY with a single valid JSON object containing the keys 'campaign_name' (a string) and 'trigger_keywords' (a list of strings). Ensure keywords are specific and distinguishing. Do not include any other text, explanations, or apologies.\n\n"
                                        "Texts to analyze for new campaign suggestion:\n"
                                    )
                                    for i, stxt in enumerate(
                                            sample_texts_for_llm_prompt): prompt_text += f"{i + 1}. \"{stxt}\"\n"

                                    messages = [{'role': 'user', 'content': prompt_text}]
                                    # For Ollama, format="json" is an option. For other backends, this might differ.
                                    # The execute_cached_llm_call will handle backend-specifics if expanded.
                                    # For now, RuleImprover's LLM use is Ollama-focused as per original code.
                                    llm_call_options = {'temperature': 0.1}
                                    if self.llm_classifier.backend == 'ollama':
                                        llm_call_options['format'] = 'json'

                                    response_dict = self.llm_classifier.execute_cached_llm_call(
                                        messages=messages,
                                        call_options=llm_call_options
                                    )

                                    if response_dict and response_dict.get('message') and response_dict['message'].get(
                                            'content'):
                                        llm_response_content = response_dict['message']['content']
                                        try:
                                            parsed_suggestion = json.loads(llm_response_content)
                                            if 'campaign_name' in parsed_suggestion and 'trigger_keywords' in parsed_suggestion:
                                                suggestions["potential_new_campaigns_llm_cluster"].append({
                                                    "method": used_method_for_llm,
                                                    "suggested_campaign_name": parsed_suggestion['campaign_name'],
                                                    "suggested_triggers": parsed_suggestion['trigger_keywords'],
                                                    "cluster_id": int(cluster_id), "cluster_size": len(member_indices),
                                                    "frequent_orgs_hint": frequent_orgs_hint,
                                                    "frequent_products_hint": frequent_prods_hint,
                                                    "sample_texts_provided_to_llm": sample_texts_for_llm_prompt
                                                })
                                            else:
                                                self.logger.warning(
                                                    f"LLM response for {used_method_for_llm} cluster {cluster_id} missing required keys. Response: {llm_response_content}")
                                        except json.JSONDecodeError:
                                            self.logger.error(
                                                f"Failed to parse LLM JSON for {used_method_for_llm} cluster {cluster_id}. Raw response: '{llm_response_content}'")
                                    elif response_dict is None:
                                        self.logger.error(
                                            f"LLM call failed for {used_method_for_llm} cluster {cluster_id} (returned None).")
                                    else:  # Response dict exists but message or content is missing
                                        self.logger.error(
                                            f"LLM response malformed for {used_method_for_llm} cluster {cluster_id}. Full response: {str(response_dict)[:500]}")
                                else:
                                    self.logger.info(
                                        f"LLM analysis for {used_method_for_llm} cluster {cluster_id} skipped: LLM not configured/ready for RuleImprover.")
                            else:
                                self.logger.debug(
                                    f"{used_method_for_llm} Cluster {cluster_id} too small ({len(member_indices)} items), skipping LLM analysis.")
                    else:
                        self.logger.info("No suitable clusters found by any method for LLM analysis.")
                else:
                    self.logger.warning(
                        "Could not generate valid embeddings for 'Other' items. Skipping clustering analysis.")
            except Exception as e:
                self.logger.error(f"Error during 'Other' items clustering or embedding process: {e}", exc_info=True)
        else:
            if not self.embedder:
                self.logger.info("RuleImprover: Embedder unavailable for clustering.")
            elif not self.sklearn_available:
                self.logger.info("RuleImprover: Scikit-learn unavailable for clustering.")
            else:
                self.logger.info(
                    f"RuleImprover: Too few 'Other' items ({len(other_items_data)}) for clustering. Min required: {MIN_OTHER_ITEMS_FOR_CLUSTERING}.")

        return suggestions


class ClassificationAnalyzer:
    """Analyze classification results (Adapted from hybrid_campaign_classifier_m4.py)"""

    def __init__(self):
        self.console = Console()

    def generate_report(self,
                        classification_results: List[ClassificationResult],
                        ads_data: List[Dict[str, Any]],
                        text_processing_fields: List[str],  # Added
                        invalid_summary_strings: set):  # Added
        num_total = len(classification_results)
        if num_total == 0:
            self.console.print("[yellow]No classification results to analyze.[/yellow]")
            return

        campaign_counts = Counter(res.campaign for res in classification_results)
        method_counts = Counter(res.method for res in classification_results)
        reviewed_needed_count = sum(1 for res in classification_results if res.needs_review)

        avg_confidence = np.mean([res.confidence for res in classification_results if res.campaign != "Other"]) if any(
            res.campaign != "Other" for res in classification_results) else 0

        self.console.print(
            Panel(f"[bold green]Classification Report[/bold green] (Total Ads: {num_total})", border_style="green"))

        camp_table = Table(title="Campaign Distribution", box=box.ROUNDED)
        camp_table.add_column("Campaign", style="cyan", overflow="fold")
        camp_table.add_column("Count", style="magenta", justify="right")
        camp_table.add_column("% of Total", style="green", justify="right")
        for campaign, count in campaign_counts.most_common():
            camp_table.add_row(campaign, str(count), f"{(count / num_total) * 100:.2f}%")
        self.console.print(camp_table)

        method_table = Table(title="Classification Method Usage", box=box.ROUNDED)
        method_table.add_column("Method", style="cyan")
        method_table.add_column("Count", style="magenta", justify="right")
        method_table.add_column("% of Total", style="green", justify="right")
        for method, count in method_counts.most_common():
            method_table.add_row(method, str(count), f"{(count / num_total) * 100:.2f}%")
        self.console.print(method_table)

        stats_panel = Panel(
            f"Average Confidence (excluding 'Other'): {avg_confidence:.3f}\n"
            f"Ads Flagged for Review: {reviewed_needed_count} ({(reviewed_needed_count / num_total) * 100:.2f}%)",
            title="Overall Statistics", border_style="blue"
        )
        self.console.print(stats_panel)

        self.console.print("\n[bold yellow]Sample Ads for Review (Campaign 'Other' or Needs Review):[/bold yellow]")
        review_samples = []
        for i, res in enumerate(classification_results):
            if res.campaign == "Other" or res.needs_review:
                # Use the static method to get the text
                raw_text, _ = HybridClassifier._prepare_ad_text_for_processing(
                    ads_data[i],
                    text_processing_fields,  # Pass through
                    invalid_summary_strings  # Pass through
                )
                review_samples.append({
                    "ID": res.ad_id, "Text": raw_text[:150] + "...",
                    "Assigned Campaign": res.campaign, "Conf": f"{res.confidence:.2f}", "Method": res.method
                })
            if len(review_samples) >= 10: break

        if review_samples:
            review_table = Table(title="Review Samples", box=box.SIMPLE)
            for col_name in review_samples[0].keys():
                review_table.add_column(col_name)
            for sample in review_samples:
                review_table.add_row(*sample.values())
            self.console.print(review_table)
        else:
            self.console.print("No ads found matching review criteria for sample display.")


class InteractiveConfig:
    # ... (show_welcome, show_main_menu, configure_input_output, configure_processing,
    #      configure_text_fields, _get_embedder_info, show_current_config, to_args_namespace - remain mostly unchanged)
    # Only configure_models needs specific updates related to ner_backend removal

    def configure_models(self):
        """Configure AI model options"""
        self.console.print(Panel("[bold cyan]AI Models Configuration[/bold cyan]", border_style="cyan"))

        # Embedder model with predefined options
        self.console.print("\n[yellow]SentenceTransformer Embedding Models:[/yellow]")
        self.console.print("[dim]1. all-MiniLM-L6-v2 (Fast, 22M params)[/dim]")
        self.console.print("[dim]2. all-mpnet-base-v2 (Balanced, 109M params)[/dim]")
        self.console.print("[dim]3. all-roberta-large-v1 (Best accuracy, 355M params) - Current default[/dim]")
        self.console.print("[dim]4. Custom model name[/dim]")

        current_embedder_model = self.config.get('embedder_model', 'all-roberta-large-v1')
        default_choice_embedder = "3"  # Default to all-roberta-large-v1
        if current_embedder_model == 'all-MiniLM-L6-v2':
            default_choice_embedder = "1"
        elif current_embedder_model == 'all-mpnet-base-v2':
            default_choice_embedder = "2"
        elif current_embedder_model != 'all-roberta-large-v1':  # Custom
            default_choice_embedder = "4"

        model_choice = Prompt.ask(
            "[green]Choose embedding model[/green]",
            choices=["1", "2", "3", "4"],
            default=default_choice_embedder
        )

        if model_choice == "1":
            self.config['embedder_model'] = 'all-MiniLM-L6-v2'
        elif model_choice == "2":
            self.config['embedder_model'] = 'all-mpnet-base-v2'
        elif model_choice == "3":
            self.config['embedder_model'] = 'all-roberta-large-v1'
        else:  # Custom
            embedder_model_input = Prompt.ask(  # Renamed variable
                "[green]Enter custom SentenceTransformer model name[/green]",
                default=self.config['embedder_model'] if default_choice_embedder == "4" else 'all-roberta-large-v1'
            )
            self.config['embedder_model'] = embedder_model_input

        default_cache_name = f"cache/embedding_{self.config['embedder_model'].replace('/', '_')}.pkl"
        cache_path = Prompt.ask(
            "[green]Embedding cache file path[/green]",
            default=self.config.get('embedder_cache', default_cache_name)
        )
        self.config['embedder_cache'] = cache_path

        # NER backend is fixed to SpaCy
        self.config['ner_backend'] = 'spacy'  # Hardcode this
        self.console.print("\n[dim]NER backend: spacy (using transformer model 'en_core_web_trf' by default)[/dim]")

        # NER model (SpaCy) - default to en_core_web_trf
        ner_model_input = Prompt.ask(  # Renamed variable
            "[green]SpaCy NER model name[/green]",
            default=self.config.get('ner_model', 'en_core_web_trf')
        )
        self.config['ner_model'] = ner_model_input
        # Removed prompt for NER backend as it's fixed.

        # LLM configuration
        use_llm = Confirm.ask(
            "\n[yellow]Enable LLM for classification refinement?[/yellow]",
            default=self.config.get('use_llm', False)  # Use .get for safety
        )
        self.config['use_llm'] = use_llm

        if use_llm:
            llm_backend = Prompt.ask(
                "[green]LLM backend[/green]",
                choices=['ollama', 'mlx', 'llama_cpp', 'transformers'],
                default=self.config.get('llm_backend', 'ollama')
            )
            self.config['llm_backend'] = llm_backend

            model_help = {
                'ollama': 'Model tag (e.g., "llama3:8b")',
                'mlx': 'MLX model path or HuggingFace repo',
                'llama_cpp': 'Local .gguf file path',
                'transformers': 'HuggingFace model name'
            }

            llm_model_input = Prompt.ask(  # Renamed variable
                f"[green]LLM model ({model_help.get(llm_backend, 'Enter model details')})[/green]",
                default=self.config.get('llm_model', "")  # Use .get and default to empty string
            )
            if llm_model_input.strip():
                self.config['llm_model'] = llm_model_input
            else:
                self.config['llm_model'] = None

            enhanced_llm = Confirm.ask(
                "[yellow]Enable Enhanced LLM post-processing for 'Other' category items?[/yellow]\n"
                "[dim](Uses more sophisticated prompts to rescue mis-classified ads)[/dim]",
                default=self.config.get('enhanced_llm', True)
            )
            self.config['enhanced_llm'] = enhanced_llm

        self.console.print("[bold green]✓ AI models configuration updated![/bold green]\n")


def run_interactive_mode():
    """Run the interactive configuration and processing mode"""
    config_manager = InteractiveConfig()
    config_manager.show_welcome()

    while True:
        try:
            choice = config_manager.show_main_menu()

            if choice == 1:
                config_manager.configure_input_output()
            elif choice == 2:
                config_manager.configure_processing()
            elif choice == 3:
                config_manager.configure_models()
            elif choice == 4:
                config_manager.show_current_config()
            elif choice == 5:
                # Start processing
                config_manager.console.print("\n[bold green]🚀 Starting classification process...[/bold green]\n")
                args = config_manager.to_args_namespace()

                # Run the main processing function
                if sys.platform == 'win32' and sys.version_info >= (3, 8):
                    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

                asyncio.run(main_async(args))

                # Ask if user wants to continue
                continue_processing = Confirm.ask(
                    "\n[yellow]Return to main menu?[/yellow]",
                    default=True
                )
                if not continue_processing:
                    break

            elif choice == 6:
                config_manager.console.print("\n[bold yellow]👋 Goodbye![/bold yellow]")
                break

        except KeyboardInterrupt:
            config_manager.console.print("\n\n[bold red]Process interrupted by user.[/bold red]")
            break
        except Exception as e:
            config_manager.console.print(f"\n[bold red]Error: {e}[/bold red]")
            logger.error(f"Interactive mode error: {e}", exc_info=True)

            continue_after_error = Confirm.ask(
                "[yellow]Continue anyway?[/yellow]",
                default=True
            )
            if not continue_after_error:
                break


# If you need those as well, please let me know.
async def main_async(args):
    console = Console()
    console.print(Panel.fit(
        "[bold cyan]Hybrid Legal Ad Classifier[/bold cyan]",
        subtitle="M4 Optimized Version",
        box=box.DOUBLE
    ))

    start_time = time.time()
    logger.setLevel(args.log_level.upper())  # log_level should be an attribute of args

    logger.info(f"Using SpaCy NER model from args: {args.ner_model}")
    console.print(f"[yellow]Using SpaCy NER model: {args.ner_model}[/yellow]")

    try:
        classifier = HybridClassifier(
            campaign_config_path=args.campaign_config_path,
            text_processing_fields=args.text_fields,
            embedder_model_name=args.embedder_model,
            embedder_cache_file=args.embedder_cache,
            company_ner_model=args.ner_model,
            use_llm=args.use_llm,
            llm_backend=args.llm_backend,
            llm_model_path=args.llm_model,
            llm_cache_file=getattr(args, 'llm_cache_file', 'cache/llm_response_cache.pkl'),
            enhanced_llm=getattr(args, 'enhanced_llm', True),
            llm_timeout=getattr(args, 'llm_timeout', 120),
            llm_max_retries=getattr(args, 'llm_max_retries', 3),
            llm_retry_delay=getattr(args, 'llm_retry_delay', 5),
            ner_cache_file=getattr(args, 'ner_cache_file', 'cache/ner_results_cache.pkl'),
            log_level=args.log_level.upper(),
            skip_terms_file=getattr(args, 'skip_terms_file', None),
            company_similarity_threshold=getattr(args, 'company_similarity_threshold', 85),
            improve_rules_active=getattr(args, 'improve_rules', False),
            max_workers=getattr(args, 'max_workers', None),  # Pass user-defined workers
            parallel_chunk_size=getattr(args, 'parallel_chunk_size', 100),
            embedding_similarity_threshold=getattr(args, 'embedding_similarity_threshold', 0.50),
            embeddings_only=getattr(args, 'embeddings_only', False),
            ner_only=getattr(args, 'ner_only', False),
            batch_size=getattr(args, 'batch_size', 32),
            max_memory_mb=getattr(args, 'max_memory_mb', 512),
            max_cache_entries=getattr(args, 'max_cache_entries', 50000),
            use_disk_cache=getattr(args, 'use_disk_cache', False)
        )
    except Exception as e:
        console.print(f"[bold red]Error initializing HybridClassifier: {e}[/bold red]")
        logger.critical(f"HybridClassifier init failed: {e}", exc_info=True)
        return

    ads_df = pd.DataFrame()
    if args.input_csv:
        console.print(f"[cyan]Loading data from CSV: {args.input_csv}[/cyan]")
        try:
            ads_df = pd.read_csv(args.input_csv, dtype={'AdArchiveID': str, 'ad_id': str})
            # ... (rest of CSV loading logic remains the same)
        except Exception as e:
            console.print(f"[bold red]Error loading CSV {args.input_csv}: {e}[/bold red]")
            if hasattr(classifier, 'close'): classifier.close()
            return

    if args.input_dynamodb_table_name and (ads_df.empty or args.input_csv is None):
        console.print(f"[cyan]Loading data from DynamoDB table: {args.input_dynamodb_table_name}[/cyan]")
        loader = OptimizedDynamoDBLoader(
            table_name=args.input_dynamodb_table_name,
            region=args.aws_region,
            endpoint_url=args.dynamodb_endpoint_url if args.local_dynamodb else None
        )
        ads_df = loader.load_data_parallel(limit=args.limit, start_date=getattr(args, 'start_date', None))
        if not ads_df.empty:
            console.print(f"[green]✓ Loaded {len(ads_df)} ads from DynamoDB.[/green]")

    if ads_df.empty:
        console.print("[bold red]No ad data to process. Exiting.[/bold red]")
        if hasattr(classifier, 'close'): classifier.close()
        return

    # Standardize AdArchiveID and clean data
    if 'ad_id' in ads_df.columns and 'AdArchiveID' not in ads_df.columns:
        ads_df.rename(columns={'ad_id': 'AdArchiveID'}, inplace=True)
    if 'AdArchiveID' not in ads_df.columns:
        ads_df['AdArchiveID'] = [f"temp_id_{i}" for i in range(len(ads_df))]
    ads_df['AdArchiveID'] = ads_df['AdArchiveID'].astype(str).fillna('')
    for col in ads_df.select_dtypes(include=['object']).columns:
        ads_df[col] = ads_df[col].fillna("").astype(str)

    initial_count = len(ads_df)
    ads_df.drop_duplicates(subset=['AdArchiveID'], keep='first', inplace=True)
    if len(ads_df) < initial_count:
        console.print(
            f"[yellow]Input Deduplication: Reduced from {initial_count:,} to {len(ads_df):,} unique AdArchiveIDs.[/yellow]")

    ads_data_list = ads_df.to_dict(orient='records')

    console.print(f"\n[bold green]🚀 Starting Classification Process[/bold green]")
    console.print(f"[cyan]Input data: {len(ads_data_list):,} ads for classification...[/cyan]")

    classification_results = classifier.classify_batch(
        ads_data_list,
        batch_size=args.batch_size,
        output_csv=args.output_csv,
        output_deduplicated_csv=args.output_deduplicated_csv,
        deduplication_fields=args.deduplication_fields
    )

    if not classification_results:
        console.print(
            "[yellow]No classification results were generated. Skipping report and rule improvement.[/yellow]")
    else:
        analyzer = ClassificationAnalyzer()
        # Correctly pass the full original data for analysis
        analyzer.generate_report(classification_results, ads_data_list,
                                 classifier.text_processing_fields, classifier.invalid_summary_strings)

        if getattr(args, 'improve_rules', False):
            console.print("\n[cyan]Analyzing for rule improvement suggestions...[/cyan]")
            rule_improver = RuleImprover(
                campaign_config_path=Path(classifier.campaign_config_path),
                embedder=classifier.embedder,
                llm_classifier=classifier.llm_classifier,
                text_processing_fields=classifier.text_processing_fields,
                invalid_summary_strings=classifier.invalid_summary_strings,
                logger_instance=logger
            )
            suggestions = rule_improver.analyze_and_suggest(classification_results, ads_data_list)
            if args.output_rule_suggestions:
                rule_improver.save_improved_config(suggestions, args.output_rule_suggestions)
            else:
                console.print("[bold yellow]Rule Suggestions (JSON):[/bold yellow]")
                console.print_json(data=suggestions)

    if hasattr(classifier, 'close'): classifier.close()
    end_time = time.time()
    processing_time = end_time - start_time
    console.print(f"\n[bold green]Total processing time: {processing_time:.2f} seconds.[/bold green]")
    if ads_data_list and processing_time > 0:
        console.print(f"[bold cyan]Average speed: {len(ads_data_list) / processing_time:.2f} ads/second.[/bold cyan]")


def load_yaml_config(config_path: str) -> Dict[str, Any]:
    """Load configuration from YAML file."""
    config_file = Path(config_path)

    # If the path doesn't exist and it's just a filename, check in the default location
    if not config_file.exists() and not config_file.is_absolute() and len(config_file.parts) == 1:
        # Try the default location
        default_location = Path("config/data/fb_ad_categorizer") / config_file.name
        if default_location.exists():
            config_file = default_location
        else:
            raise FileNotFoundError(f"Config file not found: {config_path} (also checked {default_location})")
    elif not config_file.exists():
        raise FileNotFoundError(f"Config file not found: {config_path}")

    with open(config_file, 'r') as f:
        return yaml.safe_load(f)


def yaml_config_to_args(config: Dict[str, Any]) -> Dict[str, Any]:
    """Convert YAML config to args dictionary, ensuring ner_backend is spacy."""
    args = {}
    console = Console()
    console.print("[bold green]Configuration Loaded from YAML:[/bold green]")

    # ... (input_config, campaigns_config, processing, models_config.embedder, models_config.llm, output, rules, logging, aws mappings)
    # The existing mapping logic can be kept, but we need to ensure ner_backend is overridden.

    input_config = config.get('input', {})
    args['input_csv'] = input_config.get('csv_file')
    args['input_dynamodb_table_name'] = input_config.get('dynamodb_table_name')
    args['local_dynamodb'] = input_config.get('local_dynamodb', False)
    args['dynamodb_endpoint_url'] = input_config.get('dynamodb_endpoint_url')
    args['start_date'] = input_config.get('start_date')
    args['limit'] = input_config.get('limit')
    args['aws_region'] = config.get('aws', {}).get('region', 'us-west-2')

    campaigns_config = config.get('campaigns', {})
    args['campaign_config_path'] = campaigns_config.get('config_file',
                                                        'config/data/fb_ad_categorizer/campaign_config.json')
    args['skip_terms_file'] = campaigns_config.get('skip_terms_file')

    processing_config = config.get('processing', {})  # Renamed to avoid conflict
    args['batch_size'] = processing_config.get('batch_size', 256)  # Default from your YAML
    args['text_fields'] = processing_config.get('text_fields',
                                                ['Title', 'Body', 'Summary', 'LinkDescription', 'PageName'])
    args['deduplication_fields'] = processing_config.get('deduplication_fields', ['Title', 'Body', 'Summary'])

    models_config = config.get('../../../src/models', {})
    embedder_config = models_config.get('embedder', {})  # Renamed
    args['embedder_model'] = embedder_config.get('model_name', 'all-roberta-large-v1')
    # Ensure cache file has proper prefix
    embedder_cache = embedder_config.get('cache_file', 'cache/embedding_roberta-large-v1.pkl')
    if embedder_cache and not embedder_cache.startswith('cache/'):
        embedder_cache = f'cache/{embedder_cache}'
    args['embedder_cache'] = embedder_cache
    args['embedding_similarity_threshold'] = embedder_config.get('similarity_threshold', 0.65)  # From your YAML

    ner_config = models_config.get('ner', {})  # Renamed
    args['ner_model'] = ner_config.get('model_name', 'en_core_web_trf')
    # Ensure cache file has proper prefix
    ner_cache = ner_config.get('cache_file', 'cache/ner_cache_results.pkl')
    if ner_cache and not ner_cache.startswith('cache/'):
        ner_cache = f'cache/{ner_cache}'
    args['ner_cache_file'] = ner_cache
    args['ner_backend'] = 'spacy'  # Hardcode to spacy, ignore YAML ner.backend

    llm_config = models_config.get('llm', {})  # Renamed
    args['use_llm'] = llm_config.get('enabled', False)
    args['llm_backend'] = llm_config.get('backend', 'ollama')
    args['llm_model'] = llm_config.get('model')
    # Ensure cache file has proper prefix
    llm_cache = llm_config.get('cache_file', 'cache/llm_response_cache.pkl')
    if llm_cache and not llm_cache.startswith('cache/'):
        llm_cache = f'cache/{llm_cache}'
    args['llm_cache_file'] = llm_cache
    args['enhanced_llm'] = llm_config.get('enhanced_llm', False)  # From your YAML
    args['llm_timeout'] = llm_config.get('timeout', 120)
    args['llm_max_retries'] = llm_config.get('max_retries', 3)
    args['llm_retry_delay'] = llm_config.get('retry_delay', 5)

    output_config = config.get('output', {})  # Renamed
    args['output_csv'] = output_config.get('csv_file', 'classified_ads_spacy.csv')
    args['output_deduplicated_csv'] = output_config.get('deduplicated_csv_file', 'classified_ads_spacy_dedup.csv')

    rules_config = config.get('rules', {})  # Renamed
    args['improve_rules'] = rules_config.get('improve_rules', True)  # From your YAML
    args['output_rule_suggestions'] = rules_config.get('output_suggestions', 'rule_suggestions_new.json')

    args['log_level'] = config.get('logging', {}).get('level', 'INFO')
    args['interactive'] = False  # Not typically set from YAML for batch runs

    # Other args from your main script's default config that might be in YAML
    args['company_similarity_threshold'] = config.get('company_similarity_threshold', 85)
    args['max_workers'] = config.get('max_workers', None)  # Allow YAML to specify this
    args['parallel_chunk_size'] = config.get('parallel_chunk_size', 500)  # Allow YAML
    args['force_parallel_preprocessing'] = config.get('force_parallel_preprocessing', False)  # Allow YAML
    # Cache rebuild flags are usually CLI only, but can be added if needed
    args['rebuild_ner'] = config.get('rebuild_ner', False)
    args['rebuild_embeddings'] = config.get('rebuild_embeddings', False)
    args['rebuild_llm'] = config.get('rebuild_llm', False)
    args['rebuild_all_caches'] = config.get('rebuild_all_caches', False)
    args['embeddings_only'] = config.get('processing', {}).get('embeddings_only', False)  # From your YAML structure
    args['ner_only'] = config.get('processing', {}).get('ner_only',
                                                        False)  # Assuming ner_only might be a processing stage

    for key, value in args.items():
        if key not in ['interactive']:  # Don't log interactive for non-interactive runs
            console.print(f"  [dim]{key}:[/dim] {value}")
    console.print(
        f"  [bold yellow]Note: NER backend is fixed to 'spacy'. Any 'ner.backend' in YAML is ignored.[/bold yellow]")
    console.print("[bold green]YAML configuration parsing complete.[/bold green]\n")
    return args


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Hybrid Legal Ad Classifier",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    # --- Add all arguments from original script here ---
    parser.add_argument('--config', type=str, help='YAML config file path.')
    parser.add_argument('--interactive', '-i', action='store_true', help='Run in interactive configuration mode.')
    # Add all other arguments...

    # This is a simplified placeholder for the full argument setup
    # In your final code, the full parser from the original file should be here.
    parser.add_argument('--limit', type=int, default=None)
    parser.add_argument('--input-csv', type=str, default=None)
    parser.add_argument('--input-dynamodb-table-name', type=str, default=None)
    parser.add_argument('--output-csv', type=str, default='classified_ads_output.csv')
    parser.add_argument('--log-level', type=str, default='INFO')
    parser.add_argument('--campaign-config-path', type=str, default='config/data/fb_ad_categorizer/campaign_config.json')
    parser.add_argument('--text-fields', nargs='+', default=['Title', 'Body', 'Summary', 'LinkDescription', 'PageName'])
    parser.add_argument('--embedder-model', type=str, default='all-roberta-large-v1')
    parser.add_argument('--ner-model', type=str, default='en_core_web_trf')

    cli_args = parser.parse_args()

    # --- Config Loading ---
    config = vars(cli_args)
    if cli_args.config:
        try:
            yaml_data = load_yaml_config(cli_args.config)
            yaml_args = yaml_config_to_args(yaml_data)
            config.update(yaml_args)
            # Re-apply CLI args to ensure they have top precedence
            for arg_name, value in vars(cli_args).items():
                if value is not None and value != parser.get_default(arg_name):
                    config[arg_name] = value
        except Exception as e:
            print(f"Error loading YAML config: {e}")
            sys.exit(1)

    final_args = argparse.Namespace(**config)

    console = Console()
    try:
        if final_args.interactive:
            run_interactive_mode()
        else:
            if sys.platform == 'win32' and sys.version_info >= (3, 8):
                asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
            asyncio.run(main_async(final_args))
    except KeyboardInterrupt:
        console.print("\n[bold red]Process interrupted by user.[/bold red]")
        sys.exit(1)
    except Exception as e:
        console.print(f"\n[bold red]FATAL ERROR: {e}[/bold red]")
        logger.critical("Top-level execution error", exc_info=True)
        sys.exit(1)
