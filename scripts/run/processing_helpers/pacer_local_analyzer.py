#!/usr/bin/env python3
import sys
import os

print("--- sys.path at script start ---")
for p in sys.path:
    print(p)
print("-------------------------------")

print(f"--- Current Working Directory: {os.getcwd()} ---") # ADD THIS LINE


import csv
import ast
import concurrent.futures
import json
import os
import sys
import time
import re  # Import the re module for regex
from collections import defaultdict, Counter
from pathlib import Path
from typing import List, Dict, Any, Set
from datetime import datetime


# Orphaned code from analyze_mdl_details function removed - will be added to class if needed

from rich.table import Table
from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TimeElapsedColumn
from rich.prompt import Prompt

# Add project root to path for imports
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "../..")))

try:
    # Try new migration helper first
    from src.migration import create_manager_replacement
    _using_new_architecture = True
except ImportError:
    _using_new_architecture = False
    
try:
    from src.lib.pacer_dockets_manager import PacerDocketsManager
    from src.lib.config_adapter import load_config
except ImportError as e_imp:
    print(f"Initial import failed: {e_imp}. Attempting relative import for dev environment...")
    current_dir = os.path.dirname(os.path.abspath(__file__))
    parent_dir = os.path.dirname(current_dir)
    if os.path.basename(parent_dir) != "src":
        sys.path.insert(0, os.path.abspath(os.path.join(current_dir, "..")))
    else:
        sys.path.insert(0, os.path.abspath(os.path.join(parent_dir, "..")))

    from src.lib.pacer_dockets_manager import PacerDocketsManager
    from src.lib.config_adapter import load_config

console = Console()


class PacerLocalAnalyzer:
    def __init__(self, max_workers: int = 10):
        self.console = console
        self.pacer_manager: PacerDocketsManager | None = None
        self.logger = None
        try:
            config = load_config('01/01/70')
            if _using_new_architecture:
                self.pacer_manager = create_manager_replacement('PacerDocketsManager', config)
            else:
                self.pacer_manager = PacerDocketsManager(config, use_local=True)
            self.logger = self.pacer_manager.logger
            self.logger.info("PacerLocalAnalyzer initialized with PacerDocketsManager for local DB.")
        except Exception as e:
            self.console.print(f"[bold red]Error initializing PacerManager: {e}[/bold red]")
            import logging
            self.logger = logging.getLogger("PacerLocalAnalyzer_fallback")
            if not self.logger.hasHandlers():
                handler = logging.StreamHandler(sys.stdout)
                formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
                handler.setFormatter(formatter)
                self.logger.addHandler(handler)
                self.logger.setLevel(logging.INFO)
            self.logger.error(f"PacerManager initialization failed: {e}", exc_info=True)

        self.pacer_items: List[Dict[str, Any]] = []
        self.max_workers = max_workers
        self.analyzed_mdl_data: Dict[str, Dict[str, List[str]]] = {}

        # Automatically load data when the analyzer is initialized
        self.load_pacer_data()

    @staticmethod
    def _normalize_item_attributes(item: Dict[str, Any]) -> Dict[str, Any]:
        """
        Normalizes attribute names for consistency for in-memory items:
        - If 'Defendants' (plural) exists, its value is moved to 'Defendant' (singular)
          and 'Defendants' (plural) is removed.
        - If 'Defendant' (singular) does not exist after this check (meaning 'Defendants' also didn't exist),
          it's initialized as an empty list to ensure consistency for downstream parsing.
        """
        if 'Defendants' in item:
            item['Defendant'] = item['Defendants']
            del item['Defendants']
        elif 'Defendant' not in item:
            item['Defendant'] = []
        return item

    def _parallel_scan_worker(self, segment: int, total_segments: int) -> List[Dict[str, Any]]:
        """Worker to scan a single segment of the Pacer table and normalize items."""
        if not self.pacer_manager or not self.pacer_manager.table:
            return []
        try:
            segment_items_raw = list(self.pacer_manager.scan_table(
                Segment=segment,
                TotalSegments=total_segments
            ))
            # Normalize attributes immediately after scanning each item
            normalized_segment_items = [self._normalize_item_attributes(item) for item in segment_items_raw]
            return normalized_segment_items
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error scanning Pacer segment {segment}: {e}", exc_info=True)
            self.console.print(f"[red]Error in worker for segment {segment}: {e}[/red]")
            return []

    def load_pacer_data(self) -> None:
        """Loads data from the local Pacer table in parallel and shows summary."""
        if not self.pacer_manager or not self.pacer_manager.table:
            self.console.print(
                "[bold red]PacerManager not available or table not initialized. Cannot load data.[/bold red]")
            return

        table_name = "PacerDockets (local)"
        if hasattr(self.pacer_manager, 'table_name') and self.pacer_manager.table_name:
            table_name = f"local Pacer table ('{self.pacer_manager.table_name}')"

        # Clear existing data before loading new
        self.pacer_items = []
        self.console.print(f"[cyan]Starting parallel scan of {table_name}...[/cyan]")
        start_time = time.time()

        total_segments = min(self.max_workers * 2, 50)

        with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                BarColumn(),
                TextColumn("[progress.percentage]{task.percentage:>3.1f}%"),
                TextColumn("({task.completed} of {task.total} segments)"),
                TimeElapsedColumn(),
                console=self.console,
                transient=False
        ) as progress:
            scan_task = progress.add_task("Scanning Pacer table", total=total_segments)

            with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                futures = [executor.submit(self._parallel_scan_worker, i, total_segments)
                           for i in range(total_segments)]

                for future in concurrent.futures.as_completed(futures):
                    try:
                        segment_results = future.result()
                        if segment_results:
                            self.pacer_items.extend(segment_results)
                    except Exception as e:
                        if self.logger:
                            self.logger.error(f"A segment future completed with error: {e}", exc_info=True)
                        self.console.print(f"[red]Error processing a segment result: {e}[/red]")
                    progress.update(scan_task, advance=1)

        elapsed_time = time.time() - start_time
        if self.pacer_items:
            self.console.print(
                f"[green]Successfully loaded {len(self.pacer_items)} items from Pacer table in {elapsed_time:.2f} seconds.[/green]")
            
            # Show summary analysis
            self._show_data_summary()
        else:
            self.console.print(
                f"[yellow]No items loaded from Pacer table (or scan failed). Elapsed: {elapsed_time:.2f}s.[/yellow]")

    def _show_data_summary(self) -> None:
        """Shows a summary of the loaded data."""
        if not self.pacer_items:
            return
        
        self.console.rule("[bold blue]Data Summary[/bold blue]")
        
        # Basic stats
        total_items = len(self.pacer_items)
        self.console.print(f"[cyan]Total items loaded:[/cyan] {total_items:,}")
        
        # Attribute frequency analysis
        attribute_counts = defaultdict(int)
        for item in self.pacer_items:
            for key, value in item.items():
                if value is not None and not (isinstance(value, str) and value.strip().upper() == "NA"):
                    attribute_counts[key] += 1
        
        # Top attributes
        self.console.print(f"\n[cyan]Most common attributes:[/cyan]")
        sorted_attrs = sorted(attribute_counts.items(), key=lambda x: x[1], reverse=True)
        for attr, count in sorted_attrs[:10]:
            percentage = (count / total_items) * 100
            self.console.print(f"  {attr}: {count:,} ({percentage:.1f}%)")
        
        if len(sorted_attrs) > 10:
            self.console.print(f"  ... and {len(sorted_attrs) - 10} more attributes")
        
        # Date range analysis
        filing_dates = []
        for item in self.pacer_items:
            filing_date = item.get('FilingDate', '')
            if filing_date and filing_date != 'NA':
                filing_dates.append(filing_date)
        
        if filing_dates:
            self.console.print(f"\n[cyan]Date range:[/cyan]")
            self.console.print(f"  Earliest: {min(filing_dates)}")
            self.console.print(f"  Latest: {max(filing_dates)}")
            self.console.print(f"  Items with dates: {len(filing_dates):,} ({(len(filing_dates)/total_items)*100:.1f}%)")
        
        # MDL analysis
        mdl_counts = defaultdict(int)
        for item in self.pacer_items:
            mdl_num = item.get('MdlNum', '')
            if mdl_num and str(mdl_num).strip().upper() not in ['', 'NA']:
                mdl_counts[str(mdl_num)] += 1
        
        if mdl_counts:
            self.console.print(f"\n[cyan]Top MDLs:[/cyan]")
            sorted_mdls = sorted(mdl_counts.items(), key=lambda x: x[1], reverse=True)
            for mdl, count in sorted_mdls[:10]:
                percentage = (count / total_items) * 100
                self.console.print(f"  MDL {mdl}: {count:,} items ({percentage:.1f}%)")
            
            if len(sorted_mdls) > 10:
                self.console.print(f"  ... and {len(sorted_mdls) - 10} more MDLs")
        
        self.console.print(f"\n[green]Data loaded and ready for analysis![/green]")

    def _parse_attribute_list(self, attribute_value: Any, item_context: str = "") -> List[str]:
        """
        Safely parses an attribute that might be a list or a string representation of a list.
        Returns a list of strings.
        """
        if attribute_value is None:
            return []
        if isinstance(attribute_value, list):
            return [str(x) for x in attribute_value if x is not None and str(x).strip()]

        if isinstance(attribute_value, str):
            val_str = attribute_value.strip()
            if not val_str:
                return []
            try:
                if val_str.startswith('[') and val_str.endswith(']'):
                    parsed_list = ast.literal_eval(val_str)
                    if isinstance(parsed_list, list):
                        return [str(x) for x in parsed_list if x is not None and str(x).strip()]
                    else:
                        if self.logger:
                            self.logger.warning(
                                f"Parsed attribute string '{val_str}' for {item_context} was not a list (type: {type(parsed_list)}). Treating as single element.")
                        return [val_str]
                else:
                    return [s.strip() for s in val_str.split(',') if s.strip()]
            except (ValueError, SyntaxError, TypeError) as e:
                if self.logger:
                    self.logger.warning(
                        f"Failed to parse attribute string '{val_str}' for {item_context}: {e}. Treating as single element.")
                return [val_str]

        str_val = str(attribute_value).strip()
        return [str_val] if str_val else []

    def analyze_mdl_details(self) -> None:
        """Analyzes loaded Pacer data for unique MDLs, Flags, and Defendant, prints as JSON, and saves to file."""
        if not self.pacer_items:
            output = {"type": "warning", "source": "analyze_mdl_details",
                      "message": "No Pacer data loaded. Please load data first."}
            print(json.dumps(output))
            return

        print(json.dumps({"type": "info", "source": "analyze_mdl_details", "message": "Analyzing MDL details..."}))
        raw_mdl_data: Dict[str, Dict[str, Set[str]]] = defaultdict(lambda: {"Flags": set(), "Defendant": set()})
        items_processed_for_mdl = 0

        for idx, item in enumerate(self.pacer_items):
            mdl_num_raw = item.get('MdlNum')
            mdl_num = str(mdl_num_raw).strip() if mdl_num_raw is not None else ""

            if not mdl_num or mdl_num.upper() == "NA":
                continue

            items_processed_for_mdl += 1
            item_context = f"item (PK: {item.get('FilingDate', 'N/A')}/{item.get('DocketNum', 'N/A')})"

            flags_val = item.get('Flags')
            defendants_val = item.get('Defendant')

            parsed_flags = self._parse_attribute_list(flags_val, f"Flags for {mdl_num} from {item_context}")
            parsed_defendants = self._parse_attribute_list(defendants_val,
                                                           f"Defendant for {mdl_num} from {item_context}")

            raw_mdl_data[mdl_num]["Flags"].update(parsed_flags)
            raw_mdl_data[mdl_num]["Defendant"].update(parsed_defendants)

        if not raw_mdl_data:
            output = {"type": "warning", "source": "analyze_mdl_details",
                      "message": "No valid MDL data found after processing items."}
            print(json.dumps(output))
            self.analyzed_mdl_data = {}
            return

        self.analyzed_mdl_data = {
            mdl: {
                "Flags": sorted(list(details["Flags"])),
                "Defendant": sorted(list(details["Defendant"]))
            }
            for mdl, details in raw_mdl_data.items()
        }

        output_data = {
            "type": "success",
            "source": "analyze_mdl_details",
            "message": f"Analysis complete. Found details for {len(self.analyzed_mdl_data)} unique MDL numbers from {items_processed_for_mdl} relevant items.",
            "unique_mdl_count": len(self.analyzed_mdl_data),
            "relevant_items_processed": items_processed_for_mdl,
            "analysis_results": self.analyzed_mdl_data
        }

        # Print to console
        print(json.dumps(output_data, indent=2))

        # Save to file
        output_path = Path("mdl_cleaning.json")
        try:
            with output_path.open('w', encoding='utf-8') as f:
                json.dump(output_data, f, indent=2, ensure_ascii=False)
            print(json.dumps({
                "type": "info",
                "source": "analyze_mdl_details",
                "message": f"Successfully saved MDL analysis to {output_path.absolute()}"
            }))
        except Exception as e:
            print(json.dumps({
                "type": "error",
                "source": "analyze_mdl_details",
                "message": f"Failed to save MDL analysis to file: {str(e)}"
            }))

    def display_mdl_analysis(self) -> None:
        """Displays the analyzed MDL Flags and Defendants as JSON."""
        if not self.analyzed_mdl_data:
            print(json.dumps({"type": "warning", "source": "display_mdl_analysis",
                              "message": "No analyzed MDL data to display. Run analysis first."}))
            return

        print(json.dumps(self.analyzed_mdl_data, indent=2))

    def analyze_attribute_frequencies(self) -> None:
        """Analyzes and displays frequency of each attribute key present in the Pacer items."""
        if not self.pacer_items:
            console.print("[yellow]No Pacer data loaded. Please load data first (Option 1).[/yellow]")
            return

        console.print("[cyan]Analyzing attribute key frequencies...[/cyan]")

        attribute_key_frequencies: Dict[str, int] = defaultdict(int)
        items_processed = 0
        items_with_valid_attributes = 0

        for item in self.pacer_items:
            items_processed += 1
            has_valid_attribute = False
            for key, value in item.items():
                if value is not None:
                    if isinstance(value, str) and value.strip().upper() == "NA":
                        continue
                    attribute_key_frequencies[key] += 1
                    has_valid_attribute = True
            if has_valid_attribute:
                items_with_valid_attributes += 1

        if not attribute_key_frequencies:
            console.print("[yellow]No attributes found or all items had only NA/null/None values.[/yellow]")
            return

        console.print(
            f"[green]Attribute key frequency analysis complete. Processed {items_processed} items, found attributes in {items_with_valid_attributes} items.[/green]")
        console.rule("[bold blue]Frequency of Attribute Keys[/bold blue]")

        sorted_frequencies = sorted(attribute_key_frequencies.items(), key=lambda x: x[1], reverse=True)

        for attr_key, count in sorted_frequencies:
            console.print(f"  - '{attr_key}': {count}")
        console.print("\n")

    def analyze_attribute_frequencies_by_time(self) -> None:
        """Analyzes attribute frequencies grouped by time periods to show evolution."""
        if not self.pacer_items:
            console.print("[yellow]No Pacer data loaded. Please load data first.[/yellow]")
            return

        console.print("[cyan]Analyzing attribute frequencies by time periods...[/cyan]")
        
        # Group items by year-month based on FilingDate
        time_periods: Dict[str, List[Dict[str, Any]]] = defaultdict(list)
        
        for item in self.pacer_items:
            filing_date = item.get('FilingDate', '')
            if filing_date:
                try:
                    # Assume FilingDate is in format like "01/15/23" or "2023-01-15"
                    if '/' in filing_date:
                        date_parts = filing_date.split('/')
                        if len(date_parts) == 3:
                            month, day, year = date_parts
                            # Handle 2-digit years
                            if len(year) == 2:
                                year = f"20{year}" if int(year) < 50 else f"19{year}"
                            period_key = f"{year}-{month.zfill(2)}"
                    elif '-' in filing_date:
                        date_parts = filing_date.split('-')
                        if len(date_parts) >= 2:
                            year, month = date_parts[0], date_parts[1]
                            period_key = f"{year}-{month}"
                    else:
                        period_key = "unknown"
                except:
                    period_key = "unknown"
            else:
                period_key = "no_date"
            
            time_periods[period_key].append(item)
        
        # Analyze attributes for each time period
        period_analysis: Dict[str, Dict[str, int]] = {}
        
        for period, items in time_periods.items():
            attribute_frequencies: Dict[str, int] = defaultdict(int)
            
            for item in items:
                for key, value in item.items():
                    if value is not None and not (isinstance(value, str) and value.strip().upper() == "NA"):
                        attribute_frequencies[key] += 1
            
            period_analysis[period] = dict(attribute_frequencies)
        
        # Display results
        console.rule("[bold blue]Attribute Frequencies by Time Period[/bold blue]")
        
        # Get all unique attributes across all periods
        all_attributes = set()
        for period_attrs in period_analysis.values():
            all_attributes.update(period_attrs.keys())
        
        # Create comparison table
        table = Table(title="Attribute Evolution Over Time")
        table.add_column("Attribute", style="cyan")
        
        sorted_periods = sorted([p for p in period_analysis.keys() if p not in ['unknown', 'no_date']])
        if 'unknown' in period_analysis:
            sorted_periods.append('unknown')
        if 'no_date' in period_analysis:
            sorted_periods.append('no_date')
        
        for period in sorted_periods:
            table.add_column(f"{period}\n({len(time_periods[period])} items)", style="magenta")
        
        for attr in sorted(all_attributes):
            row = [attr]
            for period in sorted_periods:
                count = period_analysis[period].get(attr, 0)
                row.append(str(count) if count > 0 else "-")
            table.add_row(*row)
        
        console.print(table)
        
        # Show summary of new/removed attributes
        if len(sorted_periods) > 1:
            console.rule("[bold blue]Attribute Changes Over Time[/bold blue]")
            
            for i in range(1, len(sorted_periods)):
                prev_period = sorted_periods[i-1]
                curr_period = sorted_periods[i]
                
                prev_attrs = set(period_analysis[prev_period].keys())
                curr_attrs = set(period_analysis[curr_period].keys())
                
                new_attrs = curr_attrs - prev_attrs
                removed_attrs = prev_attrs - curr_attrs
                
                if new_attrs or removed_attrs:
                    console.print(f"\n[bold]Changes from {prev_period} to {curr_period}:[/bold]")
                    if new_attrs:
                        console.print(f"  [green]New attributes:[/green] {', '.join(sorted(new_attrs))}")
                    if removed_attrs:
                        console.print(f"  [red]Removed attributes:[/red] {', '.join(sorted(removed_attrs))}")
        
        # Interactive drill-down for unknown/no_date items
        console.print("\n[cyan]Options for detailed analysis:[/cyan]")
        console.print("1. Examine 'unknown' date items in detail")
        console.print("2. Examine 'no_date' items in detail") 
        console.print("3. Compare specific time periods")
        console.print("4. Show attribute evolution timeline")
        console.print("5. Return to main menu")
        
        choice = Prompt.ask("Select option", choices=["1", "2", "3", "4", "5"], default="5")
        
        if choice == "1" and "unknown" in time_periods:
            self._examine_unknown_date_items(time_periods["unknown"])
        elif choice == "2" and "no_date" in time_periods:
            self._examine_no_date_items(time_periods["no_date"])
        elif choice == "3":
            self._compare_specific_periods(period_analysis, time_periods)
        elif choice == "4":
            self._show_attribute_evolution_timeline(period_analysis, sorted_periods)

    def analyze_single_occurrence_attributes(self) -> None:
        """Identifies attributes that appear in only one item for potential removal."""
        if not self.pacer_items:
            console.print("[yellow]No Pacer data loaded. Please load data first.[/yellow]")
            return

        console.print("[cyan]Analyzing single-occurrence attributes...[/cyan]")
        
        attribute_occurrences: Dict[str, List[Dict[str, Any]]] = defaultdict(list)
        
        for item in self.pacer_items:
            for key, value in item.items():
                if value is not None and not (isinstance(value, str) and value.strip().upper() == "NA"):
                    attribute_occurrences[key].append(item)
        
        # Find attributes that appear in only one item
        single_occurrence_attrs = {k: v for k, v in attribute_occurrences.items() if len(v) == 1}
        
        if not single_occurrence_attrs:
            console.print("[green]No single-occurrence attributes found.[/green]")
            return
        
        console.rule("[bold blue]Single-Occurrence Attributes[/bold blue]")
        console.print(f"Found {len(single_occurrence_attrs)} attributes that appear in only one item:\n")
        
        table = Table(title="Single-Occurrence Attributes")
        table.add_column("Attribute", style="cyan")
        table.add_column("Value", style="magenta")
        table.add_column("Item Context", style="yellow")
        
        for attr, items in sorted(single_occurrence_attrs.items()):
            item = items[0]  # Only one item
            value = str(item.get(attr, ''))
            
            # Build context with court_id if available
            court_id = item.get('court_id', item.get('Court', 'N/A'))
            filing_date = item.get('FilingDate', 'N/A')
            docket_num = item.get('DocketNum', 'N/A')
            context = f"Court: {court_id} | {filing_date}/{docket_num}"
            
            # Truncate long values
            if len(value) > 50:
                value = value[:47] + "..."
            
            table.add_row(attr, value, context)
        
        console.print(table)
        
        # Interactive examination
        while True:
            console.print("\nOptions:")
            console.print("1. View full details for a specific attribute")
            console.print("2. Generate removal list")
            console.print("3. Return to main menu")
            
            choice = Prompt.ask("Select option", choices=["1", "2", "3"], default="3")
            
            if choice == "1":
                attr_name = Prompt.ask("Enter attribute name to examine")
                if attr_name in single_occurrence_attrs:
                    item = single_occurrence_attrs[attr_name][0]
                    console.print(f"\n[bold]Full item details for attribute '{attr_name}':[/bold]")
                    console.print(json.dumps(item, indent=2, ensure_ascii=False))
                else:
                    console.print(f"[red]Attribute '{attr_name}' not found in single-occurrence list.[/red]")
            
            elif choice == "2":
                removal_list = sorted(single_occurrence_attrs.keys())
                console.print(f"\n[bold]Suggested attributes for removal ({len(removal_list)} total):[/bold]")
                console.print(", ".join(removal_list))
                
                save_list = Prompt.ask("Save removal list to file?", choices=["y", "n"], default="n")
                if save_list == "y":
                    filename = Prompt.ask("Enter filename", default="single_occurrence_attributes.json")
                    try:
                        with open(filename, 'w', encoding='utf-8') as f:
                            json.dump({
                                "single_occurrence_attributes": removal_list,
                                "total_count": len(removal_list),
                                "analysis_date": datetime.now().isoformat()
                            }, f, indent=2)
                        console.print(f"[green]Saved to {filename}[/green]")
                    except Exception as e:
                        console.print(f"[red]Error saving file: {e}[/red]")
            
            elif choice == "3":
                break

    def analyze_attribute_values_for_normalization(self) -> None:
        """Analyzes attribute values to identify potential duplicates for normalization."""
        if not self.pacer_items:
            console.print("[yellow]No Pacer data loaded. Please load data first.[/yellow]")
            return

        console.print("[cyan]Analyzing attribute values for potential normalization...[/cyan]")
        
        # Get attribute to analyze
        console.print("\nFirst, let's see all available attributes:")
        all_attributes = set()
        for item in self.pacer_items:
            all_attributes.update(item.keys())
        
        sorted_attrs = sorted(all_attributes)
        for i, attr in enumerate(sorted_attrs, 1):
            console.print(f"{i:3d}. {attr}")
        
        attr_choice = Prompt.ask("Enter attribute name to analyze")
        if attr_choice not in all_attributes:
            console.print(f"[red]Attribute '{attr_choice}' not found.[/red]")
            return
        
        # Collect all values for this attribute
        value_occurrences: Dict[str, List[Dict[str, Any]]] = defaultdict(list)
        
        for item in self.pacer_items:
            value = item.get(attr_choice)
            if value is not None and not (isinstance(value, str) and value.strip().upper() == "NA"):
                # Handle list values
                if isinstance(value, list):
                    parsed_values = self._parse_attribute_list(value, f"{attr_choice} analysis")
                    for parsed_val in parsed_values:
                        if parsed_val:
                            value_occurrences[parsed_val].append(item)
                else:
                    str_value = str(value).strip()
                    if str_value:
                        value_occurrences[str_value].append(item)
        
        if not value_occurrences:
            console.print(f"[yellow]No valid values found for attribute '{attr_choice}'.[/yellow]")
            return
        
        # Sort by frequency
        sorted_values = sorted(value_occurrences.items(), key=lambda x: len(x[1]), reverse=True)
        
        console.rule(f"[bold blue]Values for '{attr_choice}' (Total: {len(sorted_values)} unique values)[/bold blue]")
        
        table = Table(title=f"Value Frequency for '{attr_choice}'")
        table.add_column("Value", style="cyan")
        table.add_column("Count", style="magenta")
        table.add_column("Sample Context", style="yellow")
        
        for value, items in sorted_values:
            count = len(items)
            sample_item = items[0]
            context = f"PK: {sample_item.get('FilingDate', 'N/A')}/{sample_item.get('DocketNum', 'N/A')}"
            
            # Truncate long values
            display_value = value
            if len(display_value) > 40:
                display_value = display_value[:37] + "..."
            
            table.add_row(display_value, str(count), context)
        
        console.print(table)
        
        # Look for potential duplicates (case-insensitive, whitespace differences)
        console.print("\n[cyan]Analyzing for potential duplicates...[/cyan]")
        
        normalized_groups: Dict[str, List[str]] = defaultdict(list)
        for value, _ in sorted_values:
            normalized = value.lower().strip().replace(' ', '').replace('-', '').replace('_', '')
            normalized_groups[normalized].append(value)
        
        potential_duplicates = {k: v for k, v in normalized_groups.items() if len(v) > 1}
        
        if potential_duplicates:
            console.print(f"\n[bold]Found {len(potential_duplicates)} groups of potential duplicates:[/bold]")
            
            dup_table = Table(title="Potential Duplicate Values")
            dup_table.add_column("Group", style="cyan")
            dup_table.add_column("Values", style="magenta")
            dup_table.add_column("Total Items", style="yellow")
            
            for i, (norm_key, values) in enumerate(potential_duplicates.items(), 1):
                total_items = sum(len(value_occurrences[v]) for v in values)
                dup_table.add_row(str(i), ", ".join(values), str(total_items))
            
            console.print(dup_table)
            
            # Interactive examination
            while True:
                console.print("\nOptions:")
                console.print("1. Examine specific duplicate group")
                console.print("2. Generate normalization suggestions")
                console.print("3. Return to main menu")
                
                choice = Prompt.ask("Select option", choices=["1", "2", "3"], default="3")
                
                if choice == "1":
                    group_num = Prompt.ask("Enter group number to examine")
                    try:
                        group_idx = int(group_num) - 1
                        group_values = list(potential_duplicates.values())[group_idx]
                        
                        console.print(f"\n[bold]Group {group_num} details:[/bold]")
                        for value in group_values:
                            items = value_occurrences[value]
                            console.print(f"\n  Value: '{value}' ({len(items)} occurrences)")
                            for item in items[:3]:  # Show first 3 items
                                context = f"    PK: {item.get('FilingDate', 'N/A')}/{item.get('DocketNum', 'N/A')}"
                                console.print(context)
                            if len(items) > 3:
                                console.print(f"    ... and {len(items) - 3} more")
                    
                    except (ValueError, IndexError):
                        console.print(f"[red]Invalid group number: {group_num}[/red]")
                
                elif choice == "2":
                    normalization_suggestions = []
                    for values in potential_duplicates.values():
                        # Suggest keeping the most frequent value
                        value_counts = [(v, len(value_occurrences[v])) for v in values]
                        value_counts.sort(key=lambda x: x[1], reverse=True)
                        
                        canonical = value_counts[0][0]
                        alternatives = [v for v, _ in value_counts[1:]]
                        
                        if alternatives:
                            normalization_suggestions.append({
                                "canonical": canonical,
                                "alternatives": alternatives,
                                "total_affected": sum(len(value_occurrences[v]) for v in alternatives)
                            })
                    
                    console.print(f"\n[bold]Normalization suggestions:[/bold]")
                    for i, suggestion in enumerate(normalization_suggestions, 1):
                        console.print(f"{i}. Keep: '{suggestion['canonical']}'")
                        console.print(f"   Replace: {suggestion['alternatives']}")
                        console.print(f"   Affects: {suggestion['total_affected']} items\n")
                    
                    save_suggestions = Prompt.ask("Save suggestions to file?", choices=["y", "n"], default="n")
                    if save_suggestions == "y":
                        filename = Prompt.ask("Enter filename", default=f"{attr_choice}_normalization_suggestions.json")
                        try:
                            with open(filename, 'w', encoding='utf-8') as f:
                                json.dump({
                                    "attribute": attr_choice,
                                    "suggestions": normalization_suggestions,
                                    "analysis_date": datetime.now().isoformat()
                                }, f, indent=2)
                            console.print(f"[green]Saved to {filename}[/green]")
                        except Exception as e:
                            console.print(f"[red]Error saving file: {e}[/red]")
                
                elif choice == "3":
                    break
        else:
            console.print("[green]No potential duplicates found.[/green]")

    def remove_attributes_from_database(self) -> None:
        """Removes specified attributes from all items in the local DynamoDB table."""
        if not self.pacer_manager or not self.pacer_manager.table:
            self.console.print(
                "[bold red]PacerManager not available or table not initialized. Cannot remove attributes from DB.[/bold red]")
            return

        # Get attributes to remove
        self.console.print("[cyan]Select attributes to remove from the database:[/cyan]")
        
        # First show current attributes in the database
        self.console.print("\nAnalyzing current attributes in database...")
        all_attributes = set()
        sample_count = 0
        
        for item in self.pacer_manager.scan_table():
            all_attributes.update(item.keys())
            sample_count += 1
            if sample_count >= 100:  # Sample first 100 items for performance
                break
        
        sorted_attrs = sorted(all_attributes)
        self.console.print(f"\nFound {len(sorted_attrs)} unique attributes (from {sample_count} items):")
        for i, attr in enumerate(sorted_attrs, 1):
            self.console.print(f"{i:3d}. {attr}")
        
        # Get user input for attributes to remove
        attrs_input = Prompt.ask("\nEnter attribute names to remove (comma-separated)")
        if not attrs_input.strip():
            self.console.print("[yellow]No attributes specified.[/yellow]")
            return
        
        attributes_to_remove = [attr.strip() for attr in attrs_input.split(',') if attr.strip()]
        
        # Validate attributes exist
        invalid_attrs = [attr for attr in attributes_to_remove if attr not in all_attributes]
        if invalid_attrs:
            self.console.print(f"[red]Invalid attributes: {', '.join(invalid_attrs)}[/red]")
            return
        
        # Confirm removal
        self.console.print(f"\n[bold red]WARNING: This will permanently remove these attributes from ALL items in the database:[/bold red]")
        for attr in attributes_to_remove:
            self.console.print(f"  - {attr}")
        
        confirm = Prompt.ask("\nAre you sure you want to proceed?", choices=["yes", "no"], default="no")
        if confirm != "yes":
            self.console.print("[yellow]Operation cancelled.[/yellow]")
            return
        
        # Perform removal
        self.console.print("[cyan]Removing attributes from database...[/cyan]")
        
        pk_key = self.pacer_manager.pk_name
        sk_key = self.pacer_manager.sk_name
        
        if not pk_key or not sk_key:
            self.console.print(
                f"[bold red]Primary key names for '{self.pacer_manager.table_name}' not defined.[/bold red]")
            return
        
        removed_count = 0
        total_items_scanned = 0
        
        try:
            with Progress(
                    SpinnerColumn(),
                    TextColumn("[progress.description]{task.description}"),
                    BarColumn(),
                    TextColumn("[progress.completed] items processed"),
                    TimeElapsedColumn(),
                    console=self.console,
                    transient=False
            ) as progress:
                scan_task = progress.add_task("Removing attributes", total=None)
                
                for item in self.pacer_manager.scan_table():
                    total_items_scanned += 1
                    progress.update(scan_task, advance=1, description=f"Processing item {total_items_scanned}")
                    
                    # Check if item has any of the attributes to remove
                    attrs_to_remove_from_item = [attr for attr in attributes_to_remove if attr in item]
                    
                    if attrs_to_remove_from_item:
                        pk_value = item.get(pk_key)
                        sk_value = item.get(sk_key)
                        
                        if pk_value is None or sk_value is None:
                            self.console.print(
                                f"[yellow]Skipping item due to missing PK/SK: PK={pk_value}, SK={sk_value}[/yellow]")
                            continue
                        
                        # Build REMOVE expression
                        remove_expression = "REMOVE " + ", ".join(f"#{attr}" for attr in attrs_to_remove_from_item)
                        expression_attribute_names = {f"#{attr}": attr for attr in attrs_to_remove_from_item}
                        
                        try:
                            self.pacer_manager.table.update_item(
                                Key={
                                    pk_key: pk_value,
                                    sk_key: sk_value
                                },
                                UpdateExpression=remove_expression,
                                ExpressionAttributeNames=expression_attribute_names
                            )
                            removed_count += 1
                            
                            if self.logger:
                                self.logger.debug(f"Removed attributes {attrs_to_remove_from_item} from item PK: {pk_value}, SK: {sk_value}")
                        
                        except Exception as update_e:
                            self.console.print(
                                f"[red]Error updating item PK: {pk_value}, SK: {sk_value}: {update_e}[/red]")
                            if self.logger:
                                self.logger.error(f"Failed to remove attributes from item", exc_info=True)
            
            self.console.print(
                f"[bold green]Attribute removal complete. Scanned {total_items_scanned} items. Updated {removed_count} items.[/bold green]")
        
        except Exception as e:
            self.console.print(f"[bold red]Error during attribute removal: {e}[/bold red]")
            if self.logger:
                self.logger.error(f"Error during attribute removal:", exc_info=True)

    def rename_attribute_values_in_database(self) -> None:
        """Renames/normalizes specific attribute values in the local DynamoDB table."""
        if not self.pacer_manager or not self.pacer_manager.table:
            self.console.print(
                "[bold red]PacerManager not available or table not initialized. Cannot rename attribute values.[/bold red]")
            return

        self.console.print("[cyan]Attribute Value Renaming/Normalization Tool[/cyan]")
        
        # Get attribute to normalize
        self.console.print("\nFirst, let's see all available attributes:")
        all_attributes = set()
        sample_count = 0
        
        for item in self.pacer_manager.scan_table():
            all_attributes.update(item.keys())
            sample_count += 1
            if sample_count >= 100:
                break
        
        sorted_attrs = sorted(all_attributes)
        for i, attr in enumerate(sorted_attrs, 1):
            self.console.print(f"{i:3d}. {attr}")
        
        attr_to_normalize = Prompt.ask("\nEnter attribute name to normalize")
        if attr_to_normalize not in all_attributes:
            self.console.print(f"[red]Attribute '{attr_to_normalize}' not found.[/red]")
            return
        
        # Get normalization rules
        self.console.print(f"\n[cyan]Define normalization rules for '{attr_to_normalize}':[/cyan]")
        self.console.print("Enter mappings in format: old_value1->new_value1,old_value2->new_value2")
        self.console.print("Example: Bard Corporation->Bard,BD Bard->Bard")
        
        mappings_input = Prompt.ask("Enter mappings")
        if not mappings_input.strip():
            self.console.print("[yellow]No mappings specified.[/yellow]")
            return
        
        # Parse mappings
        try:
            mappings = {}
            for mapping in mappings_input.split(','):
                if '->' in mapping:
                    old_val, new_val = mapping.split('->', 1)
                    mappings[old_val.strip()] = new_val.strip()
                else:
                    self.console.print(f"[red]Invalid mapping format: {mapping}[/red]")
                    return
        except Exception as e:
            self.console.print(f"[red]Error parsing mappings: {e}[/red]")
            return
        
        if not mappings:
            self.console.print("[yellow]No valid mappings found.[/yellow]")
            return
        
        # Show what will be changed
        self.console.print(f"\n[bold]Normalization plan for '{attr_to_normalize}':[/bold]")
        for old_val, new_val in mappings.items():
            self.console.print(f"  '{old_val}' -> '{new_val}'")
        
        confirm = Prompt.ask("\nProceed with normalization?", choices=["yes", "no"], default="no")
        if confirm != "yes":
            self.console.print("[yellow]Operation cancelled.[/yellow]")
            return
        
        # Perform normalization
        self.console.print("[cyan]Normalizing attribute values in database...[/cyan]")
        
        pk_key = self.pacer_manager.pk_name
        sk_key = self.pacer_manager.sk_name
        
        if not pk_key or not sk_key:
            self.console.print(
                f"[bold red]Primary key names for '{self.pacer_manager.table_name}' not defined.[/bold red]")
            return
        
        updated_count = 0
        total_items_scanned = 0
        
        try:
            with Progress(
                    SpinnerColumn(),
                    TextColumn("[progress.description]{task.description}"),
                    BarColumn(),
                    TextColumn("[progress.completed] items processed"),
                    TimeElapsedColumn(),
                    console=self.console,
                    transient=False
            ) as progress:
                scan_task = progress.add_task("Normalizing values", total=None)
                
                for item in self.pacer_manager.scan_table():
                    total_items_scanned += 1
                    progress.update(scan_task, advance=1, description=f"Processing item {total_items_scanned}")
                    
                    current_value = item.get(attr_to_normalize)
                    if current_value is None:
                        continue
                    
                    # Handle list values
                    needs_update = False
                    new_value = current_value
                    
                    if isinstance(current_value, list):
                        new_list = []
                        for val in current_value:
                            str_val = str(val) if val is not None else ""
                            if str_val in mappings:
                                new_list.append(mappings[str_val])
                                needs_update = True
                            else:
                                new_list.append(val)
                        if needs_update:
                            new_value = new_list
                    else:
                        str_value = str(current_value)
                        if str_value in mappings:
                            new_value = mappings[str_value]
                            needs_update = True
                    
                    if needs_update:
                        pk_value = item.get(pk_key)
                        sk_value = item.get(sk_key)
                        
                        if pk_value is None or sk_value is None:
                            continue
                        
                        try:
                            self.pacer_manager.table.update_item(
                                Key={
                                    pk_key: pk_value,
                                    sk_key: sk_value
                                },
                                UpdateExpression=f"SET #{attr_to_normalize} = :new_val",
                                ExpressionAttributeNames={f"#{attr_to_normalize}": attr_to_normalize},
                                ExpressionAttributeValues={":new_val": new_value}
                            )
                            updated_count += 1
                            
                            if self.logger:
                                self.logger.debug(f"Updated {attr_to_normalize} for item PK: {pk_value}, SK: {sk_value}")
                        
                        except Exception as update_e:
                            self.console.print(
                                f"[red]Error updating item PK: {pk_value}, SK: {sk_value}: {update_e}[/red]")
                            if self.logger:
                                self.logger.error(f"Failed to update attribute value", exc_info=True)
            
            self.console.print(
                f"[bold green]Value normalization complete. Scanned {total_items_scanned} items. Updated {updated_count} items.[/bold green]")
        
        except Exception as e:
            self.console.print(f"[bold red]Error during value normalization: {e}[/bold red]")
            if self.logger:
                self.logger.error(f"Error during value normalization:", exc_info=True)

    def analyze_docket_subfolder_frequencies(self) -> None:
        base_path = Path("../../../src/data")
        year_month_day_folders = [p for p in base_path.iterdir() if
                                  p.is_dir() and len(p.name) == 8 and p.name.isdigit()]
        if not year_month_day_folders:
            self.console.print("[bold red]No YYYYMMDD folders found in 'data'.[/bold red]")
            return

        key_frequencies: Counter = Counter()
        all_json_items = []
        total_json_files_to_process = []

        for date_folder in sorted(year_month_day_folders, key=lambda p: p.name):
            dockets_folder = date_folder / "dockets"
            if not dockets_folder.exists() or not dockets_folder.is_dir():
                self.console.print(
                    f"[yellow]Dockets folder not found or is not a directory: {dockets_folder}, skipping.[/yellow]")
                continue

            current_folder_json_files = list(dockets_folder.glob("**/*.json"))
            if not current_folder_json_files:
                self.console.print(
                    f"[yellow]No JSON files found in {dockets_folder} or its subdirectories, skipping.[/yellow]")
                continue

            total_json_files_to_process.extend(current_folder_json_files)

        if not total_json_files_to_process:
            self.console.print(
                f"[bold yellow]No JSON files found in any 'data/YYYYMMDD/dockets' subdirectories.[/bold yellow]")
            return

        self.console.print(
            f"[cyan]Analyzing {len(total_json_files_to_process)} JSON files from all 'data/YYYYMMDD/dockets' subdirectories...[/cyan]")
        with Progress(console=self.console) as progress:
            task = progress.add_task("[green]Processing JSON files...[/green]", total=len(total_json_files_to_process))
            for json_file_path in total_json_files_to_process:
                try:
                    with open(json_file_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                        if isinstance(data, list):
                            for item in data:
                                if isinstance(item, dict):
                                    all_json_items.append(item)
                                    for key in item.keys():
                                        key_frequencies[key] += 1
                        elif isinstance(data, dict):
                            all_json_items.append(data)
                            for key in data.keys():
                                key_frequencies[key] += 1
                except json.JSONDecodeError:
                    self.console.print(f"[bold red]Error decoding JSON from {json_file_path}[/bold red]")
                except Exception as e:
                    self.console.print(f"[bold red]Error processing file {json_file_path}: {e}[/bold red]")
                progress.update(task, advance=1)

        if not key_frequencies:
            self.console.print("[bold yellow]No keys found in any JSON files.[/bold yellow]")
            return

        table = Table(title="Key Frequencies in all 'data/YYYYMMDD/dockets' folders")
        table.add_column("Key", style="cyan")
        table.add_column("Frequency", style="magenta")

        for key, count in key_frequencies.most_common():
            table.add_row(key, str(count))
        self.console.print(table)

        while True:
            self.console.print("\nEnter a key to see items containing it, or 'q' to return to the main menu:")
            user_key_input = self.console.input("[bold green]Key > [/bold green]").strip()

            if user_key_input.lower() == 'q':
                break

            if not user_key_input:
                self.console.print("[bold yellow]Please enter a key.[/bold yellow]")
                continue

            found_items = False
            for item in all_json_items:
                if user_key_input in item:
                    self.console.print(json.dumps(item, indent=2, ensure_ascii=False))
                    found_items = True

            if not found_items:
                self.console.print(f"[bold yellow]No items found with key: '{user_key_input}'[/bold yellow]")

    ATTRIBUTES_TO_REMOVE = [
        "jantz", "greenberg", "attyna", "kim", "transferredoutcase-donotdocket",
        "ifppnd", "transo", "efile", "tof", "lg", "nprose", "ignore", "m/ifp",
        "lc02", "mcshain", "standard", "type-b", "(marx)", "consent_due",
        "nocnst", "appenteng", "refdsp", "ps-c", "pslc-dp", "pso", "(pdx)",
        "5div", "(jprx)", "cab", "194", "pab", "ene1", "green", "_wyrick-lc2",
        "(pvcx)", "xmdl", "jbl", "jjv", "jfa", "open_mj", "pend_consent",
        "stayed", "eis", "_krc", "mapj", "rar", "pmh", "9cca_remand",
        "intradist-transf", "schneider", "attyopen", "pro-se1", "case_info",
        "filingfeedue", "henderson", "ps4", "3m", "3m_prose", "bp_b3", "b3",
        "(ajrx)", "holleb_hotaling", "dcr", "ere", "file", "mapadmin", "ene",
        "greenville", "jms", "_rtc", "jydmd", "srn", "lr16.2_tr4"
    ]

    @staticmethod
    def _contains_any_keyword(text_list: List[str], keywords: List[str]) -> bool:
        """Checks if any string in text_list contains any of the keywords (case-insensitive)."""
        normalized_keywords = [k.lower() for k in keywords]
        for text in text_list:
            normalized_text = text.lower()
            if any(kw in normalized_text for kw in normalized_keywords):
                return True
        return False

    def filter_and_export_mdl_data(self) -> None:
        """
        Filters loaded Pacer data based on specific MDL, Flags, and Defendant criteria,
        then exports the matching items to a CSV file.
        """
        if not self.pacer_items:
            self.console.print("[yellow]No Pacer data loaded. Please load data first (Option 1).[/yellow]")
            return

        self.console.print("[cyan]Applying filters and preparing data for export...[/cyan]")
        matched_items = []

        MDL_3084_DEFENDANTS_KEYWORDS = [
            "Alphabet", "amazon.com", "amazoncom services", "Facebook", "Foshan",
            "GHI Insurance", "Gold Kernal", "Google", "Instagram",
            "JKL Insurance company", "DEF insurance company", "MParticle", "Maxlead",
            "meta", "rokt us corp", "samba tv", "siculus", "snap", "oasis space",
            "tiktok", "youtube"
        ]
        MDL_2570_DEFENDANTS_KEYWORDS = ["Bard"]
        MDL_3094_DEFENDANTS_KEYWORDS = ["caremark", "express scripts"]
        FLAGS_MDL_KEYWORDS = ["MDL 3026", "MDL 3037", "MDL-3074"]
        MDL_2738_DEFENDANTS_KEYWORDS = ["THE ESTATE OF DECEDENT GABRIELE HAMMA"]
        MDL_2753_DEFENDANTS_KEYWORDS = ["maquet"]

        with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                BarColumn(),
                TextColumn("[progress.percentage]{task.percentage:>3.1f}%"),
                TextColumn("({task.completed} of {task.total} items)"),
                TimeElapsedColumn(),
                console=self.console,
                transient=False
        ) as progress:
            filter_task = progress.add_task("Filtering items", total=len(self.pacer_items))

            for item in self.pacer_items:
                mdl_num_raw = item.get('MdlNum')
                mdl_num = str(mdl_num_raw).strip().lower() if mdl_num_raw is not None else ""

                flags_value = item.get('Flags')
                defendants_value = item.get('Defendant')

                parsed_defendants = self._parse_attribute_list(defendants_value, "Defendant")
                parsed_flags = self._parse_attribute_list(flags_value, "Flags")

                matched = False
                if mdl_num == '3084' and self._contains_any_keyword(parsed_defendants, MDL_3084_DEFENDANTS_KEYWORDS):
                    matched = True
                elif mdl_num == '2570' and self._contains_any_keyword(parsed_defendants, MDL_2570_DEFENDANTS_KEYWORDS):
                    matched = True
                elif mdl_num == '3094' and self._contains_any_keyword(parsed_defendants, MDL_3094_DEFENDANTS_KEYWORDS):
                    matched = True
                elif self._contains_any_keyword(parsed_flags, FLAGS_MDL_KEYWORDS):
                    matched = True
                elif mdl_num == '2738' and self._contains_any_keyword(parsed_defendants, MDL_2738_DEFENDANTS_KEYWORDS):
                    matched = True
                elif mdl_num == '2753' and self._contains_any_keyword(parsed_defendants, MDL_2753_DEFENDANTS_KEYWORDS):
                    matched = True

                if matched:
                    matched_items.append(item)
                progress.update(filter_task, advance=1)

        if not matched_items:
            self.console.print("[yellow]No items matched the specified criteria.[/yellow]")
            return

        self.console.print(f"[green]Found {len(matched_items)} items matching the criteria.[/green]")

        all_keys = set()
        for item in matched_items:
            all_keys.update(item.keys())

        preferred_order = ["MdlNum", "FilingDate", "DocketNum", "Court", "CaseName", "Flags", "Defendant", "PacerUrl"]
        sorted_remaining_keys = sorted([k for k in all_keys if k not in preferred_order])
        final_header = [k for k in preferred_order if k in all_keys] + sorted_remaining_keys

        output_filename = Prompt.ask(
            "[bold green]Enter output CSV filename[/bold green]",
            default="filtered_pacer_data.csv",
            console=self.console
        )

        try:
            with open(output_filename, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=final_header)
                writer.writeheader()
                for item in matched_items:
                    row_data = {key: str(item.get(key, '')) for key in final_header}
                    writer.writerow(row_data)
            self.console.print(
                f"[bold green]Successfully exported {len(matched_items)} items to '{output_filename}'[/bold green]")
        except Exception as e:
            self.console.print(f"[bold red]Error exporting to CSV: {e}[/bold red]")
            if self.logger:
                self.logger.error(f"Error exporting filtered data to CSV: {e}", exc_info=True)

    def normalize_database_defendants_attribute(self) -> None:
        """
        Scans the local DynamoDB table, renames 'Defendants' attribute to 'Defendant'
        if 'Defendants' exists, and removes the original 'Defendants' attribute.
        This modifies the database directly.
        """
        if not self.pacer_manager or not self.pacer_manager.table:
            self.console.print(
                "[bold red]PacerManager not available or table not initialized. Cannot normalize attributes in DB.[/bold red]")
            return

        self.console.print(
            "[cyan]Starting normalization of 'Defendants' attribute to 'Defendant' in local DynamoDB table...[/cyan]")

        table = self.pacer_manager.table
        normalized_items_count = 0
        total_items_scanned = 0

        pk_key = self.pacer_manager.pk_name
        sk_key = self.pacer_manager.sk_name

        if not pk_key or not sk_key:
            self.console.print(
                f"[bold red]Primary key names for '{self.pacer_manager.table_name}' not defined in PacerDocketsManager. Cannot normalize attributes.[/bold red]")
            if self.logger:
                self.logger.error(f"PK/SK names missing for table {self.pacer_manager.table_name}.")
            return

        try:
            with Progress(
                    SpinnerColumn(),
                    TextColumn("[progress.description]{task.description}"),
                    BarColumn(),
                    TextColumn("[progress.percentage]{task.percentage:>3.1f}%"),
                    TextColumn("[progress.completed] items processed"),
                    TimeElapsedColumn(),
                    console=self.console,
                    transient=False
            ) as progress:
                scan_task = progress.add_task("Scanning and normalizing PacerDockets", total=None)

                for item in self.pacer_manager.scan_table():
                    total_items_scanned += 1
                    progress.update(scan_task, advance=1, description=f"Processing item {total_items_scanned}")

                    if 'Defendants' in item:
                        pk_value = item.get(pk_key)
                        sk_value = item.get(sk_key)

                        if pk_value is None or sk_value is None:
                            self.console.print(
                                f"[yellow]Skipping item PK: {item.get(pk_key, 'N/A')}, SK: {item.get(sk_key, 'N/A')} for update: Primary key component(s) are missing or None.[/yellow]")
                            if self.logger:
                                self.logger.warning(f"Skipping update for item due to missing PK/SK components: {item}")
                            continue

                        defendants_value = item['Defendants']

                        parsed_defendants_value = self._parse_attribute_list(
                            defendants_value,
                            f"Defendants for PK: {pk_value}, SK: {sk_value}"
                        )

                        update_expression = "SET #def_singular = :def_val REMOVE #def_plural"
                        expression_attribute_names = {
                            "#def_singular": "Defendant",
                            "#def_plural": "Defendants"
                        }
                        expression_attribute_values = {
                            ":def_val": parsed_defendants_value
                        }

                        try:
                            self.pacer_manager.table.update_item(
                                Key={
                                    pk_key: pk_value,
                                    sk_key: sk_value
                                },
                                UpdateExpression=update_expression,
                                ExpressionAttributeValues=expression_attribute_values,
                                ExpressionAttributeNames=expression_attribute_names
                            )
                            normalized_items_count += 1
                            if self.logger:
                                self.logger.debug(
                                    f"Normalized 'Defendants' to 'Defendant' for item PK: {pk_value}, SK: {sk_value}")
                        except Exception as update_e:
                            self.console.print(
                                f"[bold red]Error updating item PK: {pk_value}, SK: {sk_value}: {update_e}[/bold red]")
                            if self.logger:
                                self.logger.error(f"Failed to update item PK: {pk_value}, SK: {sk_value}",
                                                  exc_info=True)

            self.console.print(
                f"[bold green]Normalization complete. Scanned {total_items_scanned} items. Normalized {normalized_items_count} items in the local DynamoDB table.[/bold green]")

        except Exception as scan_e:
            self.console.print(f"[bold red]Error during scan for normalization: {scan_e}[/bold red]")
            if self.logger:
                self.logger.error(f"Error during DynamoDB scan for normalization:", exc_info=True)

    def normalize_titles_in_database(self) -> None:
        """
        Scans the local DynamoDB table, finds titles starting with "IN RE: " (case-sensitive),
        and removes that prefix, updating the item directly in the database.
        """
        if not self.pacer_manager or not self.pacer_manager.table:
            self.console.print(
                "[bold red]PacerManager not available or table not initialized. Cannot normalize titles in DB.[/bold red]")
            return

        self.console.print("[cyan]Starting normalization of 'Title' attribute in local DynamoDB table...[/cyan]")

        table = self.pacer_manager.table
        normalized_titles_count = 0
        total_items_scanned = 0

        pk_key = self.pacer_manager.pk_name
        sk_key = self.pacer_manager.sk_name

        if not pk_key or not sk_key:
            self.console.print(
                f"[bold red]Primary key names for '{self.pacer_manager.table_name}' not defined in PacerDocketsManager. Cannot normalize titles.[/bold red]")
            if self.logger:
                self.logger.error(f"PK/SK names missing for table {self.pacer_manager.table_name}.")
            return

        prefix = "IN RE: "

        try:
            with Progress(
                    SpinnerColumn(),
                    TextColumn("[progress.description]{task.description}"),
                    BarColumn(),
                    TextColumn("[progress.percentage]{task.percentage:>3.1f}%"),
                    TextColumn("[progress.completed] items processed"),
                    TimeElapsedColumn(),
                    console=self.console,
                    transient=False
            ) as progress:
                scan_task = progress.add_task("Scanning and normalizing Titles", total=None)

                # Iterate through all items in the table
                for item in self.pacer_manager.scan_table():
                    total_items_scanned += 1
                    progress.update(scan_task, advance=1, description=f"Processing item {total_items_scanned}")

                    current_title = item.get('Title')

                    if isinstance(current_title, str) and current_title.startswith(prefix):
                        new_title = current_title[len(prefix):].strip()

                        pk_value = item.get(pk_key)
                        sk_value = item.get(sk_key)

                        if pk_value is None or sk_value is None:
                            self.console.print(
                                f"[yellow]Skipping item PK: {item.get(pk_key, 'N/A')}, SK: {item.get(sk_key, 'N/A')} for update: Primary key component(s) are missing or None.[/yellow]")
                            if self.logger:
                                self.logger.warning(f"Skipping update for item due to missing PK/SK components: {item}")
                            continue

                        update_expression = "SET #title_attr = :new_title"
                        expression_attribute_names = {"#title_attr": "Title"}
                        expression_attribute_values = {":new_title": new_title}

                        try:
                            self.pacer_manager.table.update_item(
                                Key={
                                    pk_key: pk_value,
                                    sk_key: sk_value
                                },
                                UpdateExpression=update_expression,
                                ExpressionAttributeNames=expression_attribute_names,
                                ExpressionAttributeValues=expression_attribute_values
                            )
                            normalized_titles_count += 1
                            if self.logger:
                                self.logger.debug(
                                    f"Normalized title for item PK: {pk_value}, SK: {sk_value} from '{current_title}' to '{new_title}'")
                        except Exception as update_e:
                            self.console.print(
                                f"[bold red]Error updating title for item PK: {pk_value}, SK: {sk_value}: {update_e}[/bold red]")
                            if self.logger:
                                self.logger.error(f"Failed to update title for item PK: {pk_value}, SK: {sk_value}",
                                                  exc_info=True)

            self.console.print(
                f"[bold green]Normalization complete. Scanned {total_items_scanned} items. Normalized titles for {normalized_titles_count} items in the local DynamoDB table.[/bold green]")

        except Exception as scan_e:
            self.console.print(f"[bold red]Error during scan for title normalization: {scan_e}[/bold red]")
            if self.logger:
                self.logger.error(f"Error during DynamoDB scan for title normalization:", exc_info=True)

    def interactive_loop(self) -> None:
        if not self.pacer_manager:
            self.console.print(
                "[bold red]PacerDocketsManager could not be initialized. Analyzer cannot run.[/bold red]")
            return

        self.console.print("[bold cyan]Pacer Local Analyzer Initialized (interactive loop).[/bold cyan]")
        while True:
            self.console.print("\n[bold cyan]Select an option:[/bold cyan]")
            self.console.print("1. Analyze Pacer Table (Local DynamoDB)")
            self.console.print("2. Analyze MDL Details from Pacer Table (Output as JSON)")
            self.console.print("3. Analyze Attribute Frequencies from Pacer Table")
            self.console.print("4. Analyze Attribute Frequencies by Time Period")
            self.console.print("5. Analyze Single-Occurrence Attributes (for removal)")
            self.console.print("6. Analyze Attribute Values for Normalization")
            self.console.print("7. Analyze Docket Subfolder Frequencies")
            self.console.print("8. Clean JSON Attributes in Docket Subfolders")
            self.console.print("9. Filter and Export Pacer Data (CSV)")
            self.console.print("10. Remove Attributes from DynamoDB")
            self.console.print("11. Rename/Normalize Attribute Values in DynamoDB")
            self.console.print("12. Compare JSON Files vs PacerDockets Fields")
            self.console.print("13. Find Date Ranges with Missing Reference Fields")
            self.console.print("14. Normalize 'Defendants' attribute to 'Defendant' in DynamoDB")
            self.console.print("15. Normalize 'Title' attribute (remove 'IN RE: ') in DynamoDB")
            self.console.print("q. Quit")

            choice = Prompt.ask("Select an option", choices=["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "q", "Q"],
                                default="1",
                                console=self.console)

            if choice == "1":
                self.load_pacer_data()
            elif choice == "2":
                self.analyze_mdl_details()
            elif choice == "3":
                self.analyze_attribute_frequencies()
            elif choice == "4":
                self.analyze_attribute_frequencies_by_time()
            elif choice == "5":
                self.analyze_single_occurrence_attributes()
            elif choice == "6":
                self.analyze_attribute_values_for_normalization()
            elif choice == "7":
                self.analyze_docket_subfolder_frequencies()
            elif choice == "8":
                self.clean_json_attributes()
            elif choice == "9":
                self.filter_and_export_mdl_data()
            elif choice == "10":
                self.remove_attributes_from_database()
            elif choice == "11":
                self.rename_attribute_values_in_database()
            elif choice == "12":
                self.compare_json_files_vs_pacer_fields()
            elif choice == "13":
                self.find_date_ranges_missing_reference_fields()
            elif choice == "14":
                self.normalize_database_defendants_attribute()
            elif choice == "15":
                self.normalize_titles_in_database()
            elif choice.lower() == "q":
                console.print("[bold]Exiting Pacer Local Analyzer.[/bold]")
                break
            else:
                console.print("[red]Invalid choice. Please try again.[/red]")

    def clean_json_attributes(self):
        """Iterates through all JSON files in data/YYYYMMDD/dockets, removes specified attributes, and saves the modified JSON."""
        attributes_to_remove = Prompt.ask(
            "Enter a comma-separated list of attributes to remove (e.g., jantz,greenberg)"
        ).split(',')
        attributes_to_remove = [attr.strip() for attr in attributes_to_remove if attr.strip()]

        if not attributes_to_remove:
            self.console.print("No attributes specified for removal.")
            return

        data_folder = Path("../../../src/data")

        date_folders = sorted(
            [d for d in data_folder.iterdir() if d.is_dir() and len(d.name) == 8 and d.name.isdigit()])

        if not date_folders:
            self.console.print(f"No date-stamped subfolders found in '{data_folder}'.")
            return

        base_data_folder = Path("../../../src/data")
        if not base_data_folder.exists() or not base_data_folder.is_dir():
            self.console.print(f"[bold red]Base data folder '{base_data_folder}' not found.[/bold red]")
            return

        year_month_day_folders = [p for p in base_data_folder.iterdir() if
                                  p.is_dir() and p.name.isdigit() and len(p.name) == 8]
        if not year_month_day_folders:
            self.console.print("[bold red]No YYYYMMDD folders found in 'data'.[/bold red]")
            return

        total_json_files_processed = 0
        total_attributes_removed = 0

        for date_folder in sorted(year_month_day_folders, key=lambda p: p.name):
            dockets_folder = date_folder / "dockets"
            if not dockets_folder.exists() or not dockets_folder.is_dir():
                self.console.print(
                    f"[yellow]Dockets folder not found or is not a directory: {dockets_folder}, skipping.[/yellow]")
                continue

            json_files_to_process = list(dockets_folder.glob("**/*.json"))
            if not json_files_to_process:
                self.console.print(
                    f"[yellow]No JSON files found in {dockets_folder} or its subdirectories, skipping.[/yellow]")
                continue

            self.console.print(f"[cyan]Cleaning JSON files in: {dockets_folder} and its subdirectories[/cyan]")
            with Progress(console=self.console) as progress:
                task = progress.add_task(f"[green]Cleaning files in {dockets_folder.name}...[/green]",
                                         total=len(json_files_to_process))
                for json_file_path in json_files_to_process:
                    attributes_removed_in_file = 0
                    try:
                        with open(json_file_path, 'r', encoding='utf-8') as f:
                            data = json.load(f)

                        modified = False
                        if isinstance(data, list):
                            for item in data:
                                if isinstance(item, dict):
                                    for attr in attributes_to_remove:
                                        if attr in item:
                                            del item[attr]
                                            attributes_removed_in_file += 1
                                            modified = True
                        elif isinstance(data, dict):
                            for attr in attributes_to_remove:
                                if attr in data:
                                    del data[attr]
                                    attributes_removed_in_file += 1
                                    modified = True

                        if modified:
                            with open(json_file_path, 'w', encoding='utf-8') as f:
                                json.dump(data, f, indent=2, ensure_ascii=False)
                            total_attributes_removed += attributes_removed_in_file
                            total_json_files_processed += 1

                    except json.JSONDecodeError:
                        self.console.print(f"[bold red]Error decoding JSON from {json_file_path}[/bold red]")
                    except Exception as e:
                        self.console.print(f"[bold red]Error processing file {json_file_path}: {e}[/bold red]")
                    progress.update(task, advance=1)

        if hasattr(self, 'ATTRIBUTES_TO_REMOVE') and self.ATTRIBUTES_TO_REMOVE:
            self.console.print(
                f"[cyan]Also cleaning JSON files based on hardcoded attributes: {', '.join(self.ATTRIBUTES_TO_REMOVE)}[/cyan]")
            for date_folder in sorted(year_month_day_folders, key=lambda p: p.name):
                dockets_folder = date_folder / "dockets"
                if not dockets_folder.exists() or not dockets_folder.is_dir():
                    continue

                json_files_to_process = list(dockets_folder.glob("**/*.json"))
                if not json_files_to_process:
                    continue

                with Progress(console=self.console) as progress:
                    task = progress.add_task(f"[green]Applying static cleanup in {dockets_folder.name}...[/green]",
                                             total=len(json_files_to_process))
                    for json_file_path in json_files_to_process:
                        attributes_removed_in_file = 0
                        try:
                            with open(json_file_path, 'r', encoding='utf-8') as f:
                                data = json.load(f)

                            modified = False
                            if isinstance(data, list):
                                for item in data:
                                    if isinstance(item, dict):
                                        for attr in self.ATTRIBUTES_TO_REMOVE:
                                            if attr in item:
                                                del item[attr]
                                                attributes_removed_in_file += 1
                                                modified = True
                            elif isinstance(data, dict):
                                for attr in self.ATTRIBUTES_TO_REMOVE:
                                    if attr in data:
                                        del data[attr]
                                        attributes_removed_in_file += 1
                                        modified = True

                            if modified:
                                with open(json_file_path, 'w', encoding='utf-8') as f:
                                    json.dump(data, f, indent=2, ensure_ascii=False)
                                total_attributes_removed += attributes_removed_in_file
                                total_json_files_processed += 1

                        except json.JSONDecodeError:
                            self.console.print(f"[bold red]Error decoding JSON from {json_file_path}[/bold red]")
                        except Exception as e:
                            self.console.print(f"[bold red]Error processing file {json_file_path}: {e}[/bold red]")
                        progress.update(task, advance=1)

        self.console.print(f"[bold green]Finished cleaning JSON attributes.[/bold green]")
        self.console.print(f"Processed and saved changes in {total_json_files_processed} JSON files.")
        self.console.print(f"Total attributes removed: {total_attributes_removed}.")

    def _examine_unknown_date_items(self, unknown_items: List[Dict[str, Any]]) -> None:
        """Examines items with unknown/unparseable dates in detail."""
        console.print(f"\n[bold blue]Examining {len(unknown_items)} items with unknown dates[/bold blue]")
        
        # Group by court to see patterns
        court_groups = defaultdict(list)
        for item in unknown_items:
            court = item.get('court_id', item.get('Court', 'unknown_court'))
            court_groups[court].append(item)
        
        console.print(f"\n[cyan]Distribution by court:[/cyan]")
        for court, items in sorted(court_groups.items(), key=lambda x: len(x[1]), reverse=True):
            console.print(f"  {court}: {len(items)} items")
        
        # Show attribute patterns
        attr_counts = defaultdict(int)
        for item in unknown_items:
            for key, value in item.items():
                if value is not None and not (isinstance(value, str) and value.strip().upper() == "NA"):
                    attr_counts[key] += 1
        
        console.print(f"\n[cyan]Attributes in unknown date items:[/cyan]")
        sorted_attrs = sorted(attr_counts.items(), key=lambda x: x[1], reverse=True)
        for attr, count in sorted_attrs:
            percentage = (count / len(unknown_items)) * 100
            console.print(f"  {attr}: {count} ({percentage:.1f}%)")
        
        # Show sample items
        console.print(f"\n[cyan]Sample items (showing first 5):[/cyan]")
        for i, item in enumerate(unknown_items[:5]):
            console.print(f"\n[bold]Item {i+1}:[/bold]")
            filing_date = item.get('FilingDate', 'N/A')
            court = item.get('court_id', item.get('Court', 'N/A'))
            docket = item.get('DocketNum', 'N/A')
            console.print(f"  Court: {court} | FilingDate: '{filing_date}' | Docket: {docket}")
            
            # Show a few key attributes
            for key in ['CaseName', 'MdlNum', 'Defendant']:
                if key in item and item[key]:
                    value = str(item[key])
                    if len(value) > 60:
                        value = value[:57] + "..."
                    console.print(f"  {key}: {value}")

    def _examine_no_date_items(self, no_date_items: List[Dict[str, Any]]) -> None:
        """Examines items with no FilingDate field."""
        console.print(f"\n[bold blue]Examining {len(no_date_items)} items with no FilingDate[/bold blue]")
        
        # Group by court
        court_groups = defaultdict(list)
        for item in no_date_items:
            court = item.get('court_id', item.get('Court', 'unknown_court'))
            court_groups[court].append(item)
        
        console.print(f"\n[cyan]Distribution by court:[/cyan]")
        for court, items in sorted(court_groups.items(), key=lambda x: len(x[1]), reverse=True):
            console.print(f"  {court}: {len(items)} items")
        
        # Show what attributes these items have instead
        attr_counts = defaultdict(int)
        for item in no_date_items:
            for key, value in item.items():
                if value is not None and not (isinstance(value, str) and value.strip().upper() == "NA"):
                    attr_counts[key] += 1
        
        console.print(f"\n[cyan]Attributes in no-date items:[/cyan]")
        sorted_attrs = sorted(attr_counts.items(), key=lambda x: x[1], reverse=True)
        for attr, count in sorted_attrs:
            percentage = (count / len(no_date_items)) * 100
            console.print(f"  {attr}: {count} ({percentage:.1f}%)")
        
        # Show sample items
        console.print(f"\n[cyan]Sample items (showing first 5):[/cyan]")
        for i, item in enumerate(no_date_items[:5]):
            console.print(f"\n[bold]Item {i+1}:[/bold]")
            court = item.get('court_id', item.get('Court', 'N/A'))
            docket = item.get('DocketNum', 'N/A')
            console.print(f"  Court: {court} | Docket: {docket}")
            console.print(f"  All keys: {', '.join(sorted(item.keys()))}")

    def _compare_specific_periods(self, period_analysis: Dict[str, Dict[str, int]], time_periods: Dict[str, List[Dict[str, Any]]]) -> None:
        """Allows detailed comparison between specific time periods."""
        console.print(f"\n[cyan]Available time periods:[/cyan]")
        periods = sorted(period_analysis.keys())
        for i, period in enumerate(periods, 1):
            item_count = len(time_periods[period])
            console.print(f"{i:2d}. {period} ({item_count} items)")
        
        try:
            period1_idx = int(Prompt.ask("Select first period (number)")) - 1
            period2_idx = int(Prompt.ask("Select second period (number)")) - 1
            
            if 0 <= period1_idx < len(periods) and 0 <= period2_idx < len(periods):
                period1 = periods[period1_idx]
                period2 = periods[period2_idx]
                
                attrs1 = set(period_analysis[period1].keys())
                attrs2 = set(period_analysis[period2].keys())
                
                console.print(f"\n[bold]Comparing {period1} vs {period2}:[/bold]")
                console.print(f"  {period1}: {len(time_periods[period1])} items, {len(attrs1)} attributes")
                console.print(f"  {period2}: {len(time_periods[period2])} items, {len(attrs2)} attributes")
                
                only_in_1 = attrs1 - attrs2
                only_in_2 = attrs2 - attrs1
                common = attrs1 & attrs2
                
                if only_in_1:
                    console.print(f"\n[red]Only in {period1}:[/red] {', '.join(sorted(only_in_1))}")
                if only_in_2:
                    console.print(f"\n[green]Only in {period2}:[/green] {', '.join(sorted(only_in_2))}")
                
                console.print(f"\n[cyan]Common attributes ({len(common)}):[/cyan]")
                table = Table(title=f"Attribute Comparison: {period1} vs {period2}")
                table.add_column("Attribute", style="cyan")
                table.add_column(f"{period1}", style="magenta")
                table.add_column(f"{period2}", style="magenta")
                table.add_column("Change", style="yellow")
                
                for attr in sorted(common):
                    count1 = period_analysis[period1][attr]
                    count2 = period_analysis[period2][attr]
                    change = count2 - count1
                    change_str = f"+{change}" if change > 0 else str(change) if change < 0 else "0"
                    table.add_row(attr, str(count1), str(count2), change_str)
                
                console.print(table)
            else:
                console.print("[red]Invalid period selection.[/red]")
        except ValueError:
            console.print("[red]Invalid input. Please enter numbers.[/red]")

    def _show_attribute_evolution_timeline(self, period_analysis: Dict[str, Dict[str, int]], sorted_periods: List[str]) -> None:
        """Shows when each attribute first appeared and disappeared."""
        console.print(f"\n[bold blue]Attribute Evolution Timeline[/bold blue]")
        
        # Track first and last appearance of each attribute
        attr_timeline = {}
        all_attrs = set()
        for period_attrs in period_analysis.values():
            all_attrs.update(period_attrs.keys())
        
        for attr in all_attrs:
            first_seen = None
            last_seen = None
            
            for period in sorted_periods:
                if attr in period_analysis.get(period, {}):
                    if first_seen is None:
                        first_seen = period
                    last_seen = period
            
            attr_timeline[attr] = {
                'first': first_seen,
                'last': last_seen,
                'periods': [p for p in sorted_periods if attr in period_analysis.get(p, {})]
            }
        
        # Show attributes that disappeared
        disappeared = [attr for attr, info in attr_timeline.items() 
                      if info['last'] != sorted_periods[-1] and info['last'] not in ['unknown', 'no_date']]
        
        if disappeared:
            console.print(f"\n[red]Attributes that disappeared ({len(disappeared)}):[/red]")
            for attr in sorted(disappeared):
                info = attr_timeline[attr]
                console.print(f"  {attr}: {info['first']} → {info['last']}")
        
        # Show attributes that appeared later
        appeared_later = [attr for attr, info in attr_timeline.items() 
                         if info['first'] != sorted_periods[0] and info['first'] not in ['unknown', 'no_date']]
        
        if appeared_later:
            console.print(f"\n[green]Attributes that appeared later ({len(appeared_later)}):[/green]")
            for attr in sorted(appeared_later):
                info = attr_timeline[attr]
                console.print(f"  {attr}: first seen in {info['first']}")
        
        # Show persistent attributes
        persistent = [attr for attr, info in attr_timeline.items() 
                     if len(info['periods']) == len([p for p in sorted_periods if p not in ['unknown', 'no_date']])]
        
        if persistent:
            console.print(f"\n[cyan]Persistent attributes ({len(persistent)}):[/cyan]")
            for attr in sorted(persistent):
                console.print(f"  {attr}")

    def compare_json_files_vs_pacer_fields(self) -> None:
        """Compares JSON file fields in data directory with PacerDockets fields."""
        console.print("[cyan]JSON Files vs PacerDockets Field Comparison[/cyan]")
        
        # Get directory path
        default_path = "data/20250529/dockets/"
        data_dir = Prompt.ask("Enter path to dockets directory", default=default_path)
        
        if not os.path.exists(data_dir):
            console.print(f"[red]Directory not found: {data_dir}[/red]")
            return
        
        # Scan JSON files and collect all unique fields
        console.print(f"[cyan]Scanning JSON files in {data_dir}...[/cyan]")
        
        json_fields = set()
        json_files_processed = 0
        sample_json_data = {}  # Store sample data for each field
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TextColumn("[progress.completed] files processed"),
            TimeElapsedColumn(),
            console=console,
            transient=False
        ) as progress:
            scan_task = progress.add_task("Scanning JSON files", total=None)
            
            for root, dirs, files in os.walk(data_dir):
                for file in files:
                    if file.endswith('.json'):
                        file_path = os.path.join(root, file)
                        try:
                            with open(file_path, 'r', encoding='utf-8') as f:
                                data = json.load(f)
                                
                            # Handle both single objects and arrays
                            items = data if isinstance(data, list) else [data]
                            
                            for item in items:
                                if isinstance(item, dict):
                                    for key, value in item.items():
                                        json_fields.add(key)
                                        # Store sample data for later analysis
                                        if key not in sample_json_data and value is not None:
                                            sample_json_data[key] = str(value)[:100]  # Truncate long values
                            
                            json_files_processed += 1
                            progress.update(scan_task, advance=1, description=f"Processed {json_files_processed} JSON files")
                            
                        except (json.JSONDecodeError, Exception) as e:
                            console.print(f"[yellow]Warning: Could not parse {file_path}: {e}[/yellow]")
        
        console.print(f"[green]Scanned {json_files_processed} JSON files, found {len(json_fields)} unique fields[/green]")
        
        # Get PacerDockets fields
        if not self.pacer_items:
            console.print("[yellow]No PacerDockets data loaded. Loading now...[/yellow]")
            self.load_pacer_data()
        
        if not self.pacer_items:
            console.print("[red]Failed to load PacerDockets data. Cannot compare.[/red]")
            return
        
        # Collect PacerDockets fields
        pacer_fields = set()
        for item in self.pacer_items:
            pacer_fields.update(item.keys())
        
        # Show comparison
        console.rule("[bold blue]Field Comparison Results[/bold blue]")
        
        console.print(f"[cyan]JSON Files:[/cyan] {len(json_fields)} unique fields")
        console.print(f"[cyan]PacerDockets:[/cyan] {len(pacer_fields)} unique fields")
        
        # Find differences
        only_in_json = json_fields - pacer_fields
        only_in_pacer = pacer_fields - json_fields
        common_fields = json_fields & pacer_fields
        
        console.print(f"[cyan]Common fields:[/cyan] {len(common_fields)}")
        console.print(f"[yellow]Only in JSON files:[/yellow] {len(only_in_json)}")
        console.print(f"[red]Only in PacerDockets:[/red] {len(only_in_pacer)}")
        
        # Display detailed results
        if only_in_json:
            console.print(f"\\n[bold yellow]Fields only in JSON files ({len(only_in_json)}):[/bold yellow]")
            table = Table(title="JSON-Only Fields")
            table.add_column("Field Name", style="cyan")
            table.add_column("Sample Value", style="magenta")
            
            for field in sorted(only_in_json):
                sample = sample_json_data.get(field, "N/A")
                if len(sample) > 50:
                    sample = sample[:47] + "..."
                table.add_row(field, sample)
            
            console.print(table)
        
        if only_in_pacer:
            console.print(f"\\n[bold red]Fields only in PacerDockets ({len(only_in_pacer)}):[/bold red]")
            for field in sorted(only_in_pacer):
                console.print(f"  - {field}")
        
        # Interactive field analysis
        while True:
            console.print("\\n[cyan]Analysis Options:[/cyan]")
            console.print("1. Select fields to exclude from mismatch analysis")
            console.print("2. Find date ranges where fields don't match")
            console.print("3. Export field comparison to JSON")
            console.print("4. Return to main menu")
            
            choice = Prompt.ask("Select option", choices=["1", "2", "3", "4"], default="4")
            
            if choice == "1":
                self._select_fields_to_exclude(only_in_json, only_in_pacer, common_fields)
            elif choice == "2":
                self._analyze_field_mismatches_by_date(only_in_json, only_in_pacer)
            elif choice == "3":
                self._export_field_comparison(json_fields, pacer_fields, only_in_json, only_in_pacer, common_fields)
            elif choice == "4":
                break

    def _select_fields_to_exclude(self, only_in_json: Set[str], only_in_pacer: Set[str], common_fields: Set[str]) -> Set[str]:
        """Allow user to select fields to exclude from analysis."""
        console.print("\\n[cyan]Select fields to exclude from mismatch analysis:[/cyan]")
        
        all_problem_fields = list(only_in_json) + list(only_in_pacer)
        
        if not all_problem_fields:
            console.print("[green]No field mismatches to exclude.[/green]")
            return set()
        
        console.print("\\nProblem fields:")
        for i, field in enumerate(sorted(all_problem_fields), 1):
            source = "JSON-only" if field in only_in_json else "Pacer-only"
            console.print(f"{i:3d}. {field} ({source})")
        
        exclude_input = Prompt.ask("\\nEnter field numbers to exclude (comma-separated, or 'all' for all problem fields)", default="none")
        
        excluded_fields = set()
        
        if exclude_input.lower() == "all":
            excluded_fields = set(all_problem_fields)
        elif exclude_input.lower() != "none":
            try:
                indices = [int(x.strip()) for x in exclude_input.split(',') if x.strip()]
                for idx in indices:
                    if 1 <= idx <= len(all_problem_fields):
                        excluded_fields.add(all_problem_fields[idx - 1])
                    else:
                        console.print(f"[yellow]Warning: Invalid index {idx}[/yellow]")
            except ValueError:
                console.print("[red]Invalid input format.[/red]")
                return set()
        
        if excluded_fields:
            console.print(f"\\n[yellow]Excluding {len(excluded_fields)} fields from analysis:[/yellow]")
            for field in sorted(excluded_fields):
                console.print(f"  - {field}")
        
        return excluded_fields

    def _analyze_field_mismatches_by_date(self, only_in_json: Set[str], only_in_pacer: Set[str]) -> None:
        """Analyzes when field mismatches occur by date ranges."""
        console.print("\\n[cyan]Analyzing field mismatches by date ranges...[/cyan]")
        
        if not self.pacer_items:
            console.print("[red]No PacerDockets data available.[/red]")
            return
        
        # Allow user to exclude certain fields
        excluded_fields = self._select_fields_to_exclude(only_in_json, only_in_pacer, set())
        
        # Filter out excluded fields
        analysis_json_only = only_in_json - excluded_fields
        analysis_pacer_only = only_in_pacer - excluded_fields
        
        if not analysis_json_only and not analysis_pacer_only:
            console.print("[green]No field mismatches to analyze after exclusions.[/green]")
            return
        
        # Group PacerDockets items by date ranges
        date_ranges = defaultdict(list)
        
        for item in self.pacer_items:
            filing_date = item.get('FilingDate', '')
            if filing_date:
                try:
                    # Parse date and group by year-month
                    if '/' in filing_date:
                        date_parts = filing_date.split('/')
                        if len(date_parts) == 3:
                            month, day, year = date_parts
                            if len(year) == 2:
                                year = f"20{year}" if int(year) < 50 else f"19{year}"
                            period_key = f"{year}-{month.zfill(2)}"
                    elif '-' in filing_date:
                        date_parts = filing_date.split('-')
                        if len(date_parts) >= 2:
                            year, month = date_parts[0], date_parts[1]
                            period_key = f"{year}-{month}"
                    else:
                        period_key = "unknown_format"
                except:
                    period_key = "parse_error"
            else:
                period_key = "no_date"
            
            date_ranges[period_key].append(item)
        
        # Analyze field presence by date range
        console.rule("[bold blue]Field Mismatch Analysis by Date Range[/bold blue]")
        
        # Check which date ranges have the problem fields
        results = {}
        
        for period, items in date_ranges.items():
            period_fields = set()
            for item in items:
                period_fields.update(item.keys())
            
            # Check for JSON-only fields in this period
            json_only_in_period = analysis_json_only & period_fields
            pacer_only_missing_in_period = analysis_pacer_only - period_fields
            
            if json_only_in_period or pacer_only_missing_in_period:
                results[period] = {
                    'item_count': len(items),
                    'has_json_only_fields': json_only_in_period,
                    'missing_pacer_fields': pacer_only_missing_in_period,
                    'sample_item': items[0] if items else None
                }
        
        if not results:
            console.print("[green]No field mismatches found in any date ranges.[/green]")
            return
        
        # Display results
        table = Table(title="Date Ranges with Field Mismatches")
        table.add_column("Date Range", style="cyan")
        table.add_column("Items", style="magenta")
        table.add_column("Unexpected Fields", style="yellow")
        table.add_column("Missing Fields", style="red")
        table.add_column("Sample Court", style="green")
        
        for period in sorted(results.keys()):
            data = results[period]
            
            unexpected = ", ".join(sorted(data['has_json_only_fields'])) if data['has_json_only_fields'] else "-"
            missing = ", ".join(sorted(data['missing_pacer_fields'])) if data['missing_pacer_fields'] else "-"
            
            # Truncate long field lists
            if len(unexpected) > 40:
                unexpected = unexpected[:37] + "..."
            if len(missing) > 40:
                missing = missing[:37] + "..."
            
            sample_court = "N/A"
            if data['sample_item']:
                sample_court = data['sample_item'].get('court_id', data['sample_item'].get('Court', 'N/A'))
            
            table.add_row(period, str(data['item_count']), unexpected, missing, sample_court)
        
        console.print(table)
        
        # Show summary
        console.print(f"\\n[bold]Summary:[/bold]")
        console.print(f"  Date ranges with mismatches: {len(results)}")
        console.print(f"  Total date ranges analyzed: {len(date_ranges)}")
        console.print(f"  Fields causing issues: {len(analysis_json_only | analysis_pacer_only)}")

    def _export_field_comparison(self, json_fields: Set[str], pacer_fields: Set[str], 
                                only_in_json: Set[str], only_in_pacer: Set[str], 
                                common_fields: Set[str]) -> None:
        """Exports field comparison results to JSON file."""
        export_data = {
            "analysis_date": datetime.now().isoformat(),
            "summary": {
                "json_fields_count": len(json_fields),
                "pacer_fields_count": len(pacer_fields),
                "common_fields_count": len(common_fields),
                "json_only_count": len(only_in_json),
                "pacer_only_count": len(only_in_pacer)
            },
            "fields": {
                "json_fields": sorted(list(json_fields)),
                "pacer_fields": sorted(list(pacer_fields)),
                "common_fields": sorted(list(common_fields)),
                "only_in_json": sorted(list(only_in_json)),
                "only_in_pacer": sorted(list(only_in_pacer))
            }
        }
        
        filename = Prompt.ask("Enter filename for export", default="field_comparison_results.json")
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2)
            console.print(f"[green]Results exported to {filename}[/green]")
        except Exception as e:
            console.print(f"[red]Error exporting results: {e}[/red]")

    def find_date_ranges_missing_reference_fields(self) -> None:
        """Finds date ranges where PacerDockets items are missing reference JSON fields."""
        console.print("[cyan]Find Date Ranges Missing Reference Fields[/cyan]")
        
        # Reference JSON structure (from your example)
        reference_fields = {
            "court_id", "docket_num", "versus", "added_date", "added_date_iso", 
            "source_page", "cause_from_report", "nos_from_report", "cause", "nos",
            "base_filename", "filing_date", "flags", "office", "assigned_to",
            "transferred_from", "jury_demand", "jurisdiction", "plaintiff", "attorney",
            "defendant", "original_filename", "s3_html", "is_removal", "transferred_in",
            "transferor_court_name", "transferor_court_id", "transferor_docket_num",
            "mdl_num", "title", "initial_filing_date", "s3_link", "new_filename",
            "_processing_notes", "law_firms", "law_firm", "transferor_docket_law_firm",
            "allegations", "summary", "plaintiffs_gpt", "attorneys_gpt", "mdl_cat",
            "date_filed", "is_transferred", "pending_cto", "added_on"
        }
        
        console.print(f"\\n[cyan]Reference JSON contains {len(reference_fields)} fields:[/cyan]")
        
        # Show reference fields in a nice table
        fields_per_row = 4
        reference_list = sorted(reference_fields)
        
        ref_table = Table(title="Reference Fields", show_header=False)
        for i in range(fields_per_row):
            ref_table.add_column("", style="cyan")
        
        for i in range(0, len(reference_list), fields_per_row):
            row = reference_list[i:i+fields_per_row]
            while len(row) < fields_per_row:
                row.append("")
            ref_table.add_row(*row)
        
        console.print(ref_table)
        
        # Get user input for fields to analyze
        console.print("\\n[cyan]Options:[/cyan]")
        console.print("1. Analyze all reference fields")
        console.print("2. Exclude case-specific fields (transfers, etc.)")
        console.print("3. Select specific fields to analyze")
        console.print("4. Load fields from custom JSON file")
        
        choice = Prompt.ask("Select option", choices=["1", "2", "3", "4"], default="2")
        
        if choice == "2":
            # Exclude case-specific fields that are only populated for certain case types
            case_specific_fields = {
                "transferor_court_id", "transferor_court_name", "transferor_docket_num",
                "transferred_from", "transferred_in", "is_transferred", 
                "transferor_docket_law_firm", "initial_filing_date", "allegations",
                "summary", "pending_cto", "is_removal", "_processing_notes"
            }
            
            analysis_fields = reference_fields - case_specific_fields
            excluded_count = len(case_specific_fields & reference_fields)
            
            console.print(f"\\n[yellow]Excluded {excluded_count} case-specific fields:[/yellow]")
            for field in sorted(case_specific_fields & reference_fields):
                console.print(f"  - {field}")
            console.print(f"\\n[cyan]Analyzing {len(analysis_fields)} core fields that should be present in all cases[/cyan]")
            
        elif choice == "3":
            # Let user select specific fields
            console.print("\\nEnter field names to analyze (comma-separated):")
            field_input = Prompt.ask("Fields")
            if not field_input.strip():
                console.print("[yellow]No fields specified.[/yellow]")
                return
            
            selected_fields = set(field.strip() for field in field_input.split(',') if field.strip())
            invalid_fields = selected_fields - reference_fields
            if invalid_fields:
                console.print(f"[yellow]Warning: Unknown fields will be ignored: {', '.join(invalid_fields)}[/yellow]")
            
            analysis_fields = selected_fields & reference_fields
            
        elif choice == "4":
            # Load from custom JSON file
            json_file = Prompt.ask("Enter path to JSON file")
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                if isinstance(data, dict):
                    analysis_fields = set(data.keys())
                else:
                    console.print("[red]JSON file must contain a single object.[/red]")
                    return
                    
                console.print(f"[green]Loaded {len(analysis_fields)} fields from {json_file}[/green]")
                
            except Exception as e:
                console.print(f"[red]Error loading JSON file: {e}[/red]")
                return
        else:
            # Use all reference fields
            analysis_fields = reference_fields
        
        if not analysis_fields:
            console.print("[yellow]No fields to analyze.[/yellow]")
            return
        
        console.print(f"\\n[cyan]Analyzing {len(analysis_fields)} fields...[/cyan]")
        
        # Convert reference fields to PascalCase to match DynamoDB storage
        # JSON files use snake_case, but DynamoDB stores in PascalCase
        if hasattr(self, 'pacer_manager') and self.pacer_manager:
            # Use the same conversion function as the pacer manager
            from src.lib.dynamodb_base_manager import DynamoDbBaseManager
            dummy_dict = {field: "dummy" for field in analysis_fields}
            pascal_dict = DynamoDbBaseManager.snake_or_camel_to_pascal_case(dummy_dict)
            analysis_fields_pascal = set(pascal_dict.keys())
            
            console.print(f"[yellow]Converted {len(analysis_fields)} snake_case fields to PascalCase for DynamoDB comparison[/yellow]")
            console.print(f"[cyan]Example conversions:[/cyan]")
            # Show actual conversions, not sorted mismatches
            for snake_field in sorted(list(analysis_fields)[:5]):
                pascal_field = DynamoDbBaseManager.snake_or_camel_to_pascal_case({snake_field: "dummy"})
                pascal_key = list(pascal_field.keys())[0]
                console.print(f"  {snake_field} → {pascal_key}")
        else:
            analysis_fields_pascal = analysis_fields
        
        # Load PacerDockets data if needed
        if not self.pacer_items:
            console.print("[yellow]Loading PacerDockets data...[/yellow]")
            self.load_pacer_data()
        
        if not self.pacer_items:
            console.print("[red]Failed to load PacerDockets data.[/red]")
            return
        
        # Group items by date ranges and analyze missing fields
        date_ranges = defaultdict(list)
        
        # First, let's see what date formats we actually have
        date_formats = defaultdict(list)
        for item in self.pacer_items[:100]:  # Sample first 100 items
            filing_date = item.get('FilingDate', item.get('filing_date', ''))
            date_formats[str(filing_date)[:20]].append(filing_date)  # Group by first 20 chars
        
        console.print("\\n[yellow]Sample date formats found:[/yellow]")
        for pattern, examples in list(date_formats.items())[:10]:
            console.print(f"  '{pattern}': {len(examples)} items")
        
        # Ask user which date field to use
        console.print("\\n[cyan]Which date field should we use for grouping?[/cyan]")
        date_field_choice = Prompt.ask("Enter field name", default="FilingDate")
        
        for item in self.pacer_items:
            filing_date = item.get(date_field_choice, '')
            
            # Try multiple date parsing strategies
            period_key = "unknown_format"
            
            if filing_date and isinstance(filing_date, str):
                filing_date = filing_date.strip()
                
                try:
                    # Strategy 1: MM/DD/YY or MM/DD/YYYY
                    if '/' in filing_date and len(filing_date.split('/')) == 3:
                        month, day, year = filing_date.split('/')
                        if len(year) == 2:
                            year = f"20{year}" if int(year) < 50 else f"19{year}"
                        if len(year) == 4 and len(month) <= 2 and len(day) <= 2:
                            period_key = f"{year}-{month.zfill(2)}"
                    
                    # Strategy 2: YYYYMMDD
                    elif filing_date.isdigit() and len(filing_date) == 8:
                        year, month = filing_date[:4], filing_date[4:6]
                        period_key = f"{year}-{month}"
                    
                    # Strategy 3: YYYY-MM-DD
                    elif '-' in filing_date and len(filing_date) >= 7:
                        parts = filing_date.split('-')
                        if len(parts) >= 2 and len(parts[0]) == 4:
                            year, month = parts[0], parts[1]
                            if year.isdigit() and month.isdigit():
                                period_key = f"{year}-{month.zfill(2)}"
                    
                    # Strategy 4: Just year (YYYY)
                    elif filing_date.isdigit() and len(filing_date) == 4:
                        period_key = f"{filing_date}-00"
                        
                except:
                    period_key = "parse_error"
            else:
                period_key = "no_date"
            
            date_ranges[period_key].append(item)
        
        # Analyze missing fields for each date range
        console.rule("[bold blue]Date Ranges Missing Reference Fields[/bold blue]")
        
        results = {}
        
        for period, items in date_ranges.items():
            # Get all fields present in this period
            period_fields = set()
            field_counts = defaultdict(int)
            
            for item in items:
                item_fields = set(item.keys())
                period_fields.update(item_fields)
                
                # Count how many items have each field
                for field in item_fields:
                    if item[field] is not None and not (isinstance(item[field], str) and item[field].strip().upper() == "NA"):
                        field_counts[field] += 1
            
            # Find missing reference fields
            missing_fields = analysis_fields_pascal - period_fields
            
            # Find fields with low coverage (present but not in many items)
            low_coverage_fields = {}
            for field in analysis_fields_pascal & period_fields:
                coverage_pct = (field_counts[field] / len(items)) * 100
                if coverage_pct < 50:  # Less than 50% coverage
                    low_coverage_fields[field] = coverage_pct
            
            if missing_fields or low_coverage_fields:
                results[period] = {
                    'item_count': len(items),
                    'missing_fields': missing_fields,
                    'low_coverage_fields': low_coverage_fields,
                    'sample_item': items[0] if items else None,
                    'total_fields': len(period_fields),
                    'coverage_stats': field_counts
                }
        
        if not results:
            console.print("[green]All date ranges have complete reference field coverage.[/green]")
            return
        
        # Display results table
        results_table = Table(title="Date Ranges with Missing Reference Fields")
        results_table.add_column("Date Range", style="cyan")
        results_table.add_column("Items", style="magenta")
        results_table.add_column("Missing Fields", style="red")
        results_table.add_column("Low Coverage Fields", style="yellow")
        results_table.add_column("Sample Court", style="green")
        
        for period in sorted(results.keys()):
            data = results[period]
            
            missing_str = f"{len(data['missing_fields'])} fields"
            if data['missing_fields']:
                missing_list = list(data['missing_fields'])[:3]  # Show first 3
                missing_str = ", ".join(missing_list)
                if len(data['missing_fields']) > 3:
                    missing_str += f" (+{len(data['missing_fields']) - 3} more)"
            else:
                missing_str = "-"
            
            low_cov_str = f"{len(data['low_coverage_fields'])} fields"
            if data['low_coverage_fields']:
                low_cov_list = list(data['low_coverage_fields'].keys())[:2]
                low_cov_str = ", ".join(low_cov_list)
                if len(data['low_coverage_fields']) > 2:
                    low_cov_str += f" (+{len(data['low_coverage_fields']) - 2})"
            else:
                low_cov_str = "-"
            
            sample_court = "N/A"
            if data['sample_item']:
                sample_court = data['sample_item'].get('court_id', data['sample_item'].get('Court', 'N/A'))
            
            results_table.add_row(period, str(data['item_count']), missing_str, low_cov_str, sample_court)
        
        console.print(results_table)
        
        # Find fields that exist in other periods but NOT in 2025-05 (deprecated/removed fields)
        latest_period = "2025-05"
        if latest_period in date_ranges:
            latest_period_fields = set()
            for item in date_ranges[latest_period]:
                latest_period_fields.update(item.keys())
            
            deprecated_fields_by_period = {}
            for period, items in date_ranges.items():
                if period != latest_period and period not in ["unknown_format", "parse_error", "no_date"]:
                    period_fields = set()
                    for item in items:
                        period_fields.update(item.keys())
                    
                    # Find fields in this period that don't exist in latest period
                    deprecated_in_period = period_fields - latest_period_fields
                    if deprecated_in_period:
                        deprecated_fields_by_period[period] = {
                            'deprecated_fields': deprecated_in_period,
                            'item_count': len(items)
                        }
            
            if deprecated_fields_by_period:
                console.print(f"\\n[bold red]Fields that existed in other periods but are missing from {latest_period}:[/bold red]")
                
                deprecated_table = Table(title=f"Deprecated/Removed Fields (not in {latest_period})")
                deprecated_table.add_column("Date Range", style="cyan")
                deprecated_table.add_column("Items", style="magenta")
                deprecated_table.add_column("Deprecated Fields", style="red")
                
                for period in sorted(deprecated_fields_by_period.keys()):
                    data = deprecated_fields_by_period[period]
                    
                    deprecated_list = sorted(list(data['deprecated_fields']))
                    if len(deprecated_list) > 5:
                        deprecated_str = ", ".join(deprecated_list[:5]) + f" (+{len(deprecated_list) - 5} more)"
                    else:
                        deprecated_str = ", ".join(deprecated_list)
                    
                    deprecated_table.add_row(period, str(data['item_count']), deprecated_str)
                
                console.print(deprecated_table)
                
                # Show all unique deprecated fields
                all_deprecated = set()
                for data in deprecated_fields_by_period.values():
                    all_deprecated.update(data['deprecated_fields'])
                
                console.print(f"\\n[yellow]All deprecated fields ({len(all_deprecated)} total):[/yellow]")
                for field in sorted(all_deprecated):
                    console.print(f"  - {field}")
            else:
                console.print(f"\\n[green]No deprecated fields found - {latest_period} structure includes all historical fields.[/green]")
        else:
            console.print(f"\\n[yellow]No {latest_period} data found for deprecated field analysis.[/yellow]")
        
        # Interactive detailed analysis
        while True:
            console.print("\\n[cyan]Detailed Analysis Options:[/cyan]")
            console.print("1. Examine specific date range in detail")
            console.print("2. Show field coverage statistics")
            console.print("3. Export missing fields analysis")
            console.print("4. Return to main menu")
            
            detail_choice = Prompt.ask("Select option", choices=["1", "2", "3", "4"], default="4")
            
            if detail_choice == "1":
                periods = sorted(results.keys())
                console.print("\\nAvailable date ranges:")
                for i, period in enumerate(periods, 1):
                    console.print(f"{i:2d}. {period} ({results[period]['item_count']} items)")
                
                try:
                    period_idx = int(Prompt.ask("Select date range (number)")) - 1
                    if 0 <= period_idx < len(periods):
                        selected_period = periods[period_idx]
                        self._examine_missing_fields_detail(selected_period, results[selected_period], analysis_fields_pascal)
                    else:
                        console.print("[red]Invalid selection.[/red]")
                except ValueError:
                    console.print("[red]Invalid input.[/red]")
            
            elif detail_choice == "2":
                self._show_field_coverage_statistics(results, analysis_fields_pascal)
            
            elif detail_choice == "3":
                self._export_missing_fields_analysis(results, analysis_fields_pascal)
            
            elif detail_choice == "4":
                break

    def _examine_missing_fields_detail(self, period: str, period_data: Dict[str, Any], reference_fields: Set[str]) -> None:
        """Examines missing fields for a specific date range in detail."""
        console.print(f"\\n[bold blue]Detailed Analysis for {period}[/bold blue]")
        console.print(f"Items in period: {period_data['item_count']}")
        console.print(f"Total unique fields: {period_data['total_fields']}")
        
        missing_fields = period_data['missing_fields']
        low_coverage = period_data['low_coverage_fields']
        
        if missing_fields:
            console.print(f"\\n[red]Completely missing fields ({len(missing_fields)}):[/red]")
            for field in sorted(missing_fields):
                console.print(f"  - {field}")
        
        if low_coverage:
            console.print(f"\\n[yellow]Low coverage fields:[/yellow]")
            coverage_table = Table()
            coverage_table.add_column("Field", style="cyan")
            coverage_table.add_column("Coverage %", style="yellow")
            coverage_table.add_column("Items with Field", style="magenta")
            
            for field, coverage_pct in sorted(low_coverage.items(), key=lambda x: x[1]):
                item_count = period_data['coverage_stats'][field]
                coverage_table.add_row(field, f"{coverage_pct:.1f}%", str(item_count))
            
            console.print(coverage_table)
        
        # Show sample item
        if period_data['sample_item']:
            console.print(f"\\n[cyan]Sample item fields:[/cyan]")
            sample_fields = set(period_data['sample_item'].keys())
            console.print(f"Has {len(sample_fields & reference_fields)}/{len(reference_fields)} reference fields")
            
            missing_in_sample = reference_fields - sample_fields
            if missing_in_sample:
                console.print(f"[red]Missing in sample:[/red] {', '.join(sorted(list(missing_in_sample)[:10]))}")

    def _show_field_coverage_statistics(self, results: Dict[str, Dict[str, Any]], reference_fields: Set[str]) -> None:
        """Shows field coverage statistics across all date ranges."""
        console.print(f"\\n[bold blue]Field Coverage Statistics[/bold blue]")
        
        # Calculate overall field statistics
        field_stats = defaultdict(lambda: {'total_periods': 0, 'total_items': 0, 'periods_with_field': 0})
        
        for period, data in results.items():
            for field in reference_fields:
                field_stats[field]['total_periods'] += 1
                field_stats[field]['total_items'] += data['item_count']
                
                if field in data['coverage_stats']:
                    field_stats[field]['periods_with_field'] += 1
        
        # Create statistics table
        stats_table = Table(title="Field Coverage Across All Date Ranges")
        stats_table.add_column("Field", style="cyan")
        stats_table.add_column("Periods Present", style="green")
        stats_table.add_column("Periods Missing", style="red")
        stats_table.add_column("Coverage %", style="yellow")
        
        for field in sorted(reference_fields):
            stats = field_stats[field]
            periods_present = stats['periods_with_field']
            periods_missing = stats['total_periods'] - periods_present
            coverage_pct = (periods_present / stats['total_periods']) * 100 if stats['total_periods'] > 0 else 0
            
            stats_table.add_row(
                field,
                str(periods_present),
                str(periods_missing),
                f"{coverage_pct:.1f}%"
            )
        
        console.print(stats_table)

    def _export_missing_fields_analysis(self, results: Dict[str, Dict[str, Any]], reference_fields: Set[str]) -> None:
        """Exports missing fields analysis to JSON file."""
        export_data = {
            "analysis_date": datetime.now().isoformat(),
            "reference_fields": sorted(list(reference_fields)),
            "summary": {
                "total_date_ranges": len(results),
                "reference_fields_count": len(reference_fields)
            },
            "date_range_analysis": {}
        }
        
        for period, data in results.items():
            export_data["date_range_analysis"][period] = {
                "item_count": data['item_count'],
                "missing_fields": sorted(list(data['missing_fields'])),
                "low_coverage_fields": {field: pct for field, pct in data['low_coverage_fields'].items()},
                "total_fields_in_period": data['total_fields']
            }
        
        filename = Prompt.ask("Enter filename for export", default="missing_fields_analysis.json")
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2)
            console.print(f"[green]Analysis exported to {filename}[/green]")
        except Exception as e:
            console.print(f"[red]Error exporting analysis: {e}[/red]")


if __name__ == "__main__":
    import logging

    # Configure basic logging; PacerManager might use this.
    logging.basicConfig(level=logging.INFO,
                        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                        handlers=[logging.StreamHandler(sys.stdout)])  # Ensure logs go to console for CLI

    # Silence overly verbose loggers if necessary
    logging.getLogger('boto3').setLevel(logging.WARNING)
    logging.getLogger('botocore').setLevel(logging.WARNING)
    logging.getLogger('urllib3').setLevel(logging.WARNING)

    console.print("[bold green]Initializing Pacer Local Analyzer...[/bold green]")  # Reverted to Rich
    analyzer = PacerLocalAnalyzer(max_workers=os.cpu_count() or 4)  # Default to CPU count or 4 workers
    analyzer.interactive_loop()
