#!/usr/bin/env python3
"""
Process Review Cases Script

This script processes review cases collected during PACER scraping. It loads cases from
the review_cases_all.json file for a specific date, filters them based on various criteria,
and allows the user to review and select which cases to keep. The final list is saved to
review_cases_final.json.

Usage:
    python -m src.scripts.process_review_cases --date YYYYMMDD
    python -m src.scripts.process_review_cases --log-level INFO --date YYYYMMDD
    python -m src.scripts.process_review_cases --date YYYYMMDD --dicts

Arguments:
    --date: Date in YYYYMMDD format (e.g., 20240601 for June 1, 2024)
    --log-level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
    --dicts: If present, save the final cases as a list of dictionaries instead of list of lists.
"""

import logging
import os
import json
from datetime import datetime
import sys
import argparse
import asyncio

# Add the project root to the Python path if running as a script
if __name__ == "__main__":
    project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    sys.path.insert(0, project_root)

from src.repositories.pacer_repository import PacerRepository
from src.infrastructure.storage.dynamodb_async import AsyncDynamoDBStorage
from rich.console import Console
from rich.progress import track
from rich.table import Table

console = Console()
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
DATA_PATH = os.path.join(PROJECT_ROOT, 'data')


def get_valid_date(date_str):
    """Validate date format and return the logs directory path."""
    if not date_str:
        console.print("[red]Error: Date is required. Use --date YYYYMMDD[/red]")
        sys.exit(1)

    try:
        # Validate date format
        datetime.strptime(date_str, "%Y%m%d")
    except ValueError:
        console.print(f"[red]Error: Invalid date format '{date_str}'. Use YYYYMMDD format.[/red]")
        sys.exit(1)

    # Construct the logs directory path
    logs_path = os.path.join(DATA_PATH, date_str, 'logs')
    return logs_path


def setup_logging(log_level):
    """Setup logging configuration."""
    numeric_level = getattr(logging, log_level.upper(), None)
    if not isinstance(numeric_level, int):
        console.print(f"[red]Invalid log level: {log_level}[/red]")
        sys.exit(1)

    logging.basicConfig(
        level=numeric_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )


def load_json_files(directory):
    """Load and combine all JSON files from the directory."""
    all_cases = []
    
    # Try new filename first, then legacy filename
    file_path = os.path.join(directory, "review_cases_all.json")
    legacy_file_path = os.path.join(directory, "irrelevant_cases_all.json")
    
    try:
        if os.path.exists(file_path):
            with open(file_path, 'r') as f:
                data = json.load(f)
                if isinstance(data, list):
                    all_cases.extend(data)
                else:
                    all_cases.append(data)  # Should ideally be a list, but handle single object
        elif os.path.exists(legacy_file_path):
            console.print(f"[yellow]Using legacy file: {legacy_file_path}[/yellow]")
            with open(legacy_file_path, 'r') as f:
                data = json.load(f)
                if isinstance(data, list):
                    all_cases.extend(data)
                else:
                    all_cases.append(data)  # Should ideally be a list, but handle single object
        else:
            console.print(f"[red]File not found: {file_path} or {legacy_file_path}[/red]")
    except json.JSONDecodeError:
        console.print(f"[red]Error reading JSON file. Skipping...[/red]")
    except Exception as exc:
        console.print(f"[red]Unexpected error reading file: {str(exc)}[/red]")

    return all_cases


def deduplicate_cases(cases):
    """Remove duplicate cases based on court_id and docket_num."""
    seen = set()
    unique_cases = []
    for case in cases:
        if isinstance(case, dict):
            key = (case.get('court_id'), case.get('docket_num'))
            if key not in seen:
                seen.add(key)
                unique_cases.append(case)
        else:
            # Handle non-dict cases (shouldn't happen, but be safe)
            unique_cases.append(case)
    return unique_cases


def filter_excluded_keywords(cases):
    """Filter out cases containing excluded keywords in versus field."""
    excluded_keywords = [
        'kroger', 'elevator', 'amazon.com', 'walmart', 'wal-mart', 'target',
        'cvs', 'walgreens', "lowe's", 'united states marshall service',
        'fbi', 'federal bureau of investigation'
    ]
    
    filtered_cases = []
    for case in cases:
        if isinstance(case, dict):
            versus = str(case.get('versus', '')).lower()
            if not any(keyword.lower() in versus for keyword in excluded_keywords):
                filtered_cases.append(case)
        else:
            filtered_cases.append(case)
    
    return filtered_cases


async def filter_existing_dockets(cases, pacer_repository):
    """Filter out cases that already exist in the database."""
    filtered_cases = []
    
    for case in track(cases, description="Checking existing dockets..."):
        if isinstance(case, dict):
            court_id = case.get('court_id')
            docket_num = case.get('docket_num')
            
            if court_id and docket_num:
                try:
                    results = await pacer_repository.check_docket_exists(court_id, docket_num)
                    if not results:
                        filtered_cases.append(case)
                except Exception as e:
                    console.print(f"[yellow]Warning: Could not check existence for {court_id}:{docket_num}: {e}[/yellow]")
                    filtered_cases.append(case)  # Include if we can't check
            else:
                filtered_cases.append(case)  # Include if missing required fields
        else:
            filtered_cases.append(case)
    
    return filtered_cases


async def main(args, config):
    """Main function to process review cases for a specific date.

    This function processes review cases by:
    1. Validating the date and finding the corresponding logs directory
    2. Loading review cases from JSON files
    3. Removing duplicates and filtering by excluded keywords
    4. Checking if cases already exist in the database
    5. Allowing user to review and select cases to keep
    6. Saving the final list of cases (as dicts) to a JSON file

    Args:
        args: Command line arguments including date and log level
        config: Application configuration
    """
    date_path = get_valid_date(args.date)
    date_str = os.path.basename(os.path.dirname(date_path))  # YYYYMMDD
    console.print(f"[cyan]Processing review cases for date: {date_str}[/cyan]")

    if not os.path.isdir(date_path):
        console.print(f"[red]Logs directory not found: {date_path}[/red]")
        sys.exit(1)

    # Initialize async storage and repository using context manager
    async with AsyncDynamoDBStorage(config) as storage:
        pacer_repository = PacerRepository(storage)
        
        console.print("[cyan]Loading review cases from 'review_cases_all.json'...[/cyan]")
        all_cases = load_json_files(date_path)
        console.print(f"[green]Total cases loaded: {len(all_cases)}[/green]")
        if not all_cases:
            console.print("[yellow]No cases to process. Exiting.[/yellow]")
            return

        console.print("[cyan]Removing duplicates...[/cyan]")
        unique_cases = deduplicate_cases(all_cases)
        console.print(f"[green]Cases after deduplication: {len(unique_cases)}[/green]")

        console.print("[cyan]Filtering by excluded keywords...[/cyan]")
        filtered_by_keywords = filter_excluded_keywords(unique_cases)
        console.print(f"[green]Cases after keyword filtering: {len(filtered_by_keywords)}[/green]")

        cases_to_review = await filter_existing_dockets(filtered_by_keywords, pacer_repository)
        console.print(f"[green]Cases after filtering existing dockets: {len(cases_to_review)}[/green]")

        if not cases_to_review:
            console.print("[yellow]No cases need review after filtering. Exiting.[/yellow]")
            return

        final_cases = []
        total_to_review = len(cases_to_review)

        for idx, case in enumerate(cases_to_review, 1):
            console.rule(f"[yellow]Reviewing Case {idx} of {total_to_review}[/yellow]")

            table = Table(show_header=False, box=None, padding=(0, 1))
            if isinstance(case, dict):
                display_order = ["court_id", "docket_num", "versus", "_reason_review", "_reason_irrelevant", "filing_date", "added_date_iso"]
                for key in display_order:
                    if key in case and case[key]:
                        table.add_row(f"[bold cyan]{key.replace('_', ' ').title()}[/bold cyan]", str(case[key]))

                other_fields = {k: v for k, v in case.items() if v and k not in display_order}
                for key, value in other_fields.items():
                    table.add_row(f"[cyan]{key.replace('_', ' ').title()}[/cyan]", str(value))
            else:
                table.add_row("[red]Error[/red]", "Case data is not in the expected dictionary format.")

            console.print(table)

            response = console.input("\n[yellow]Keep this case? (Y/N/Q to quit) [N]:[/yellow] ").strip().upper()
            if response == "Q":
                break
            elif response == "Y":
                # Check if this is a removal case that needs a link number
                if isinstance(case, dict) and (case.get('is_removal') or case.get('_gpt_removal_na')):
                    console.print("\n[cyan]This is a removal case.[/cyan]")
                    link_num = console.input("[yellow]Enter the attachment link number to download (e.g., 1, 2, 3): [/yellow]").strip()
                    
                    # Validate the input is a number
                    if link_num.isdigit():
                        case['is_removal_case'] = True
                        case['removal_link_number'] = link_num
                        console.print(f"[green]Link number {link_num} saved for this removal case.[/green]")
                    else:
                        console.print("[red]Invalid input. Link number must be a digit. Case will be saved without link number.[/red]")
                        case['is_removal_case'] = True
                        case['removal_link_number'] = None
                
                final_cases.append(case)

        # Default output filename reflects the dict structure
        output_filename = "review_cases_final.json"
        output_path = os.path.join(date_path, output_filename)

        if final_cases:
            save_response = console.input(
                f"\n[yellow]Save the {len(final_cases)} selected cases to '{output_filename}'? (Y/N) [Y]:[/yellow] ").strip().upper() or "Y"
            if save_response != "N":
                with open(output_path, 'w') as f:
                    json.dump(final_cases, f, indent=4)
                console.print(f"[green]Selected cases saved to {output_path}[/green]")
                console.print(f"[green]Saved {len(final_cases)} cases.[/green]")
            else:
                console.print("[yellow]File save canceled.[/yellow]")
        else:
            console.print(
                f"[yellow]No cases were selected to keep. No output file generated as '{output_filename}'.[/yellow]")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Process review cases for a specific date")
    parser.add_argument("--date", required=True, help="Date in YYYYMMDD format")
    parser.add_argument("--log-level", default="INFO", help="Logging level")
    parser.add_argument("--dicts", action="store_true", help="Save as list of dictionaries (default behavior)")

    args = parser.parse_args()

    # Setup logging
    setup_logging(args.log_level)
    logger = logging.getLogger(__name__)

    try:
        from src.config_models.loader import load_config

        effective_date_for_config = args.date if args.date else datetime.now().strftime("%Y%m%d")
        try:
            config_date_mmddyy = datetime.strptime(effective_date_for_config, "%Y%m%d").strftime("%m/%d/%y")
        except ValueError:
            logger.critical(f"Invalid effective date for config: {effective_date_for_config}")
            sys.exit(1)

        config = load_config('scraper', {'date': config_date_mmddyy})
    except ImportError:
        logger.critical("Failed to import load_config. Ensure src.lib.config is correct.")
        sys.exit(1)
    except Exception as e:
        logger.critical(f"Failed to load configuration: {e}")
        sys.exit(1)

    asyncio.run(main(args, config))
