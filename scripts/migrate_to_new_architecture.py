#!/usr/bin/env python3
"""
Migration script to update all files from old manager pattern to new service/repository pattern.
This script automates the migration process across the entire codebase.
"""
import os
import re
import sys
from pathlib import Path
from typing import List, Tuple, Set
import logging
from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TextColumn

console = Console()
logger = logging.getLogger(__name__)

# Define migration patterns
MANAGER_REPLACEMENTS = {
    'PacerManager': 'PacerManager',
    'DistrictCourtsManager': 'DistrictCourtsManager', 
    'LawFirmsManager': 'LawFirmsManager',
    'FBAdArchiveManager': 'FBArchiveManager',
    'FacebookArchiveManager': 'FBArchiveManager',
    'PacerDocketsManager': 'PacerDocketsManager'
}

# Files to exclude from migration
EXCLUDE_PATTERNS = {
    'src/lib/',  # Don't modify the old manager files themselves
    'src/migration/',  # Don't modify migration helpers
    'tests/mocks/',  # Keep test mocks unchanged for now
    '.git/',
    '__pycache__/',
    '.pyc',
    'scripts/migrate_to_new_architecture.py'  # Don't modify this script
}

def should_process_file(file_path: Path) -> bool:
    """Check if a file should be processed."""
    str_path = str(file_path)
    
    # Check exclusion patterns
    for pattern in EXCLUDE_PATTERNS:
        if pattern in str_path:
            return False
    
    # Only process Python files
    return file_path.suffix == '.py'

def migrate_imports(content: str) -> Tuple[str, List[str]]:
    """Migrate import statements to use new pattern."""
    changes = []
    lines = content.split('\n')
    modified_lines = []
    in_migration_block = False
    
    i = 0
    while i < len(lines):
        line = lines[i]
        
        # Check for old manager imports
        if any(manager in line for manager in MANAGER_REPLACEMENTS.keys()):
            # Check if it's an import line
            if re.match(r'^\s*from\s+src\.lib\.\w+_manager\s+import', line):
                # Start migration block if not already in one
                if not in_migration_block:
                    modified_lines.append('try:')
                    modified_lines.append('    # Try new migration helper first')
                    modified_lines.append('    from src.migration import create_manager_replacement')
                    modified_lines.append('    _using_new_architecture = True')
                    modified_lines.append('except ImportError:')
                    modified_lines.append('    _using_new_architecture = False')
                    in_migration_block = True
                    changes.append(f"Added migration import block at line {i}")
                
                # Add the original import in the except block
                modified_lines.append('    ' + line)
                
                # Skip any immediately following manager imports
                j = i + 1
                while j < len(lines) and re.match(r'^\s*from\s+src\.lib\.\w+_manager\s+import', lines[j]):
                    modified_lines.append('    ' + lines[j])
                    j += 1
                i = j - 1
            else:
                modified_lines.append(line)
        else:
            modified_lines.append(line)
        
        i += 1
    
    return '\n'.join(modified_lines), changes

def migrate_instantiations(content: str) -> Tuple[str, List[str]]:
    """Migrate manager instantiations to use new pattern."""
    changes = []
    
    # Pattern to find manager instantiations
    for old_manager, new_manager in MANAGER_REPLACEMENTS.items():
        # Pattern: variable = OldManager(config)
        pattern = rf'(\w+)\s*=\s*{old_manager}\s*\([^)]*\)'
        
        def replacement(match):
            var_name = match.group(1)
            original = match.group(0)
            
            # Create conditional instantiation
            new_code = f"""if _using_new_architecture:
            {var_name} = create_manager_replacement('{new_manager}', config)
        else:
            {original}"""
            
            changes.append(f"Migrated instantiation: {original}")
            return new_code
        
        # Check if pattern exists before replacing
        if re.search(pattern, content):
            content = re.sub(pattern, replacement, content)
    
    return content, changes

def add_migration_check_to_class_init(content: str) -> Tuple[str, List[str]]:
    """Add migration check to class __init__ methods that use managers."""
    changes = []
    
    # This is more complex and would need careful AST parsing
    # For now, we'll focus on the simpler import/instantiation migrations
    
    return content, changes

def migrate_file(file_path: Path) -> Tuple[bool, List[str]]:
    """Migrate a single file."""
    try:
        content = file_path.read_text()
        original_content = content
        all_changes = []
        
        # Skip if already migrated
        if 'from src.migration import create_manager_replacement' in content:
            return False, ["File already migrated"]
        
        # Check if file uses any managers
        uses_managers = any(manager in content for manager in MANAGER_REPLACEMENTS.keys())
        if not uses_managers:
            return False, ["No managers used in file"]
        
        # Migrate imports
        content, changes = migrate_imports(content)
        all_changes.extend(changes)
        
        # Migrate instantiations
        content, changes = migrate_instantiations(content)
        all_changes.extend(changes)
        
        # Only write if changes were made
        if content != original_content:
            file_path.write_text(content)
            return True, all_changes
        
        return False, ["No changes needed"]
        
    except Exception as e:
        return False, [f"Error: {str(e)}"]

def find_python_files(root_dir: Path) -> List[Path]:
    """Find all Python files that should be migrated."""
    python_files = []
    
    for file_path in root_dir.rglob('*.py'):
        if should_process_file(file_path):
            python_files.append(file_path)
    
    return python_files

def main():
    """Main migration function."""
    console.print("[bold blue]LexGenius Architecture Migration Script[/bold blue]")
    console.print("=" * 50)
    
    # Get project root
    project_root = Path(__file__).parent.parent
    console.print(f"Project root: {project_root}")
    
    # Find all Python files
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console
    ) as progress:
        task = progress.add_task("Finding Python files...", total=None)
        python_files = find_python_files(project_root)
        progress.update(task, completed=True)
    
    console.print(f"Found {len(python_files)} Python files to check")
    
    # Process each file
    migrated_files = []
    skipped_files = []
    error_files = []
    
    with Progress(console=console) as progress:
        task = progress.add_task("Migrating files...", total=len(python_files))
        
        for file_path in python_files:
            progress.update(task, description=f"Processing {file_path.name}")
            
            success, changes = migrate_file(file_path)
            
            if success:
                migrated_files.append((file_path, changes))
            elif "Error" in str(changes):
                error_files.append((file_path, changes))
            else:
                skipped_files.append((file_path, changes))
            
            progress.advance(task)
    
    # Print summary
    console.print("\n[bold green]Migration Summary[/bold green]")
    console.print("=" * 50)
    console.print(f"Total files checked: {len(python_files)}")
    console.print(f"Files migrated: [green]{len(migrated_files)}[/green]")
    console.print(f"Files skipped: [yellow]{len(skipped_files)}[/yellow]")
    console.print(f"Files with errors: [red]{len(error_files)}[/red]")
    
    # Show migrated files
    if migrated_files:
        console.print("\n[bold]Migrated Files:[/bold]")
        for file_path, changes in migrated_files[:10]:  # Show first 10
            console.print(f"  • {file_path.relative_to(project_root)}")
            for change in changes[:3]:  # Show first 3 changes
                console.print(f"    - {change}")
        if len(migrated_files) > 10:
            console.print(f"  ... and {len(migrated_files) - 10} more files")
    
    # Show errors
    if error_files:
        console.print("\n[bold red]Files with Errors:[/bold red]")
        for file_path, errors in error_files:
            console.print(f"  • {file_path.relative_to(project_root)}")
            for error in errors:
                console.print(f"    - {error}")
    
    console.print("\n[bold]Next Steps:[/bold]")
    console.print("1. Review the migrated files to ensure correctness")
    console.print("2. Run tests to verify functionality")
    console.print("3. Once verified, remove old manager files from src/lib/")
    console.print("4. Update any remaining manual cases")

if __name__ == "__main__":
    main()