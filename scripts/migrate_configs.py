#!/usr/bin/env python3
"""
Migration script to move existing configuration files to new structure.
Run this script to reorganize configuration files according to the new architecture.
"""

import json
import yaml
import shutil
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Tuple

# Base directories
OLD_CONFIG_DIR = Path("src/config")
OLD_ROOT_CONFIG_DIR = Path("config")
NEW_CONFIG_DIR = Path("config")
NEW_SRC_CONFIG_DIR = Path("src/config_models")

# Configuration mappings
WORKFLOW_CONFIGS = [
    "scrape.yml",
    "transform.yml",
    "transform_batch.yml", 
    "report.yml",
    "weekly_report.yml",
    "fb_ads.yml"
]

STATIC_DATA_MAPPINGS = {
    # Courts data
    "courts/district_courts.json": "data/courts/district_courts.json",
    "courts/pacer_ecm_cf_courts.json": "data/courts/pacer_ecm_cf_courts.json",
    
    # MDL data
    "mdl/mdl_lookup.json": "data/mdl/mdl_lookup.json",
    "mdl/mdl_classifier.json": "data/mdl/mdl_classifier.json",
    "mdl/mdl_short_name_lookup.json": "data/mdl/mdl_short_name_lookup.json",
    
    # Attorney data
    "attorneys/attorney_lookup.json": "data/attorneys/attorney_lookup.json",
    
    # Defendant data
    "defendants/new_defendants.json": "data/defendants/new_defendants.json",
    "defendants/relevant_defendants.json": "data/defendants/relevant_defendants.json",
    
    # Law firm data
    "law_firms/capitalization_lookup.json": "data/law_firms/capitalization_lookup.json",
    "law_firms/name_normalization.json": "data/law_firms/name_normalization.json",
    
    # Facebook ads data
    "fb_ad_categorizer/company_name_mapping.json": "data/fb_ads/company_name_mapping.json",
    "fb_ad_categorizer/campaign_config.json": "data/fb_ads/campaign_config.json",
    "fb_ad_categorizer/campaign_skip_terms.json": "data/fb_ads/campaign_skip_terms.json",
    "fb_ads/ignore_firms.json": "data/fb_ads/ignore_firms.json",
    
    # PACER data
    "pacer/ignore_download/ignore_download.json": "data/pacer/ignore_download.json",
    
    # Reports data
    "reports/afff_stats.json": "data/reports/afff_stats.json",
    "reports/announcements5.json": "data/reports/announcements5.json",
    "reports/attorney_lookup.json": "data/reports/attorney_lookup.json",
    "reports/content_filters.json": "data/reports/content_filters.json",
    "reports/general_sponsorships.json": "data/reports/general_sponsorships.json",
    "reports/litigation_sponsorships.json": "data/reports/litigation_sponsorships.json",
    "reports/news.json": "data/reports/news.json",
    "reports/upcoming_hearings.json": "data/reports/upcoming_hearings.json",
}

# Files to skip (duplicates, backups, etc.)
SKIP_FILES = [
    "campaign_config_bak.json",
    "announcements2.json",
    "announcements3.json",
    "announcements4.json",
    "pacer/defendants/relevant_defendants.json",  # Duplicate
]


class ConfigMigrator:
    """Handles migration of configuration files to new structure"""
    
    def __init__(self, dry_run: bool = True):
        self.dry_run = dry_run
        self.migrations: List[Tuple[Path, Path]] = []
        self.errors: List[str] = []
        
    def migrate(self):
        """Run the migration"""
        print(f"{'DRY RUN' if self.dry_run else 'MIGRATION'} starting...")
        print("-" * 60)
        
        # Create backup if not dry run
        if not self.dry_run:
            self._create_backup()
        
        # Migrate workflow configs
        self._migrate_workflow_configs()
        
        # Migrate static data files
        self._migrate_static_data()
        
        # Create new Pydantic config directory
        self._create_config_models_dir()
        
        # Create migration report
        self._print_report()
        
        if not self.dry_run and not self.errors:
            print("\n✅ Migration completed successfully!")
            print(f"   Migrated {len(self.migrations)} files")
        elif self.errors:
            print("\n❌ Migration encountered errors:")
            for error in self.errors:
                print(f"   - {error}")
    
    def _create_backup(self):
        """Create backup of existing configurations"""
        backup_dir = Path(f"config_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
        
        print(f"Creating backup in {backup_dir}...")
        
        # Backup old config directories
        if OLD_CONFIG_DIR.exists():
            shutil.copytree(OLD_CONFIG_DIR, backup_dir / "src_config")
        if OLD_ROOT_CONFIG_DIR.exists():
            shutil.copytree(OLD_ROOT_CONFIG_DIR, backup_dir / "root_config")
    
    def _migrate_workflow_configs(self):
        """Migrate workflow configuration files"""
        print("\nMigrating workflow configurations...")
        
        for config_file in WORKFLOW_CONFIGS:
            old_path = OLD_ROOT_CONFIG_DIR / config_file
            new_path = NEW_CONFIG_DIR / "workflows" / config_file
            
            if old_path.exists():
                self._migrate_file(old_path, new_path)
    
    def _migrate_static_data(self):
        """Migrate static data files"""
        print("\nMigrating static data files...")
        
        for old_rel_path, new_rel_path in STATIC_DATA_MAPPINGS.items():
            old_path = OLD_CONFIG_DIR / old_rel_path
            new_path = NEW_CONFIG_DIR / new_rel_path
            
            # Skip files in skip list
            if any(skip in str(old_path) for skip in SKIP_FILES):
                continue
            
            if old_path.exists():
                self._migrate_file(old_path, new_path)
    
    def _create_config_models_dir(self):
        """Create the new config_models directory structure"""
        print("\nCreating config_models directory...")
        
        if not self.dry_run:
            NEW_SRC_CONFIG_DIR.mkdir(parents=True, exist_ok=True)
            print(f"   Created {NEW_SRC_CONFIG_DIR}")
    
    def _migrate_file(self, old_path: Path, new_path: Path):
        """Migrate a single file"""
        try:
            if self.dry_run:
                print(f"   {old_path} → {new_path}")
            else:
                # Create parent directory
                new_path.parent.mkdir(parents=True, exist_ok=True)
                
                # Copy file
                shutil.copy2(old_path, new_path)
                print(f"   ✓ {old_path} → {new_path}")
            
            self.migrations.append((old_path, new_path))
            
        except Exception as e:
            error_msg = f"Failed to migrate {old_path}: {e}"
            self.errors.append(error_msg)
            print(f"   ✗ {error_msg}")
    
    def _print_report(self):
        """Print migration report"""
        print("\n" + "=" * 60)
        print("MIGRATION REPORT")
        print("=" * 60)
        
        # Group by directory
        by_dir: Dict[str, List[Tuple[Path, Path]]] = {}
        for old, new in self.migrations:
            dir_name = str(new.parent.relative_to(NEW_CONFIG_DIR))
            if dir_name not in by_dir:
                by_dir[dir_name] = []
            by_dir[dir_name].append((old, new))
        
        for dir_name, files in sorted(by_dir.items()):
            print(f"\n{dir_name}/ ({len(files)} files)")
            for old, new in files[:5]:  # Show first 5
                print(f"  - {new.name}")
            if len(files) > 5:
                print(f"  ... and {len(files) - 5} more")
        
        # Summary
        print(f"\nTotal files to migrate: {len(self.migrations)}")
        print(f"Errors encountered: {len(self.errors)}")
        
        # Next steps
        print("\n" + "-" * 60)
        print("NEXT STEPS:")
        print("-" * 60)
        if self.dry_run:
            print("1. Review the migration plan above")
            print("2. Run with --execute to perform the migration")
            print("3. A backup will be created automatically")
        else:
            print("1. Update imports in Python code to use new config loader")
            print("2. Test all workflows with new configuration")
            print("3. Remove old configuration directories once verified")
            print("4. Commit the changes to git")


def main():
    """Main entry point"""
    import argparse
    
    parser = argparse.ArgumentParser(
        description="Migrate LexGenius configuration files to new structure"
    )
    parser.add_argument(
        "--execute",
        action="store_true",
        help="Execute the migration (default is dry run)"
    )
    
    args = parser.parse_args()
    
    migrator = ConfigMigrator(dry_run=not args.execute)
    migrator.migrate()


if __name__ == "__main__":
    main()