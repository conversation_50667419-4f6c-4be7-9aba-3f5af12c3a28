[{"Company": "PowerSchool Holdings", "LitigationName": "Power School Holdings Data Breach Litigation", "triggers": ["powerschool", "power school", "powerschool holdings", "power school holdings"]}, {"Company": "RealPage", "LitigationName": "RealPage Antitrust Investigation", "triggers": ["realpage", "real page"]}, {"Company": null, "LitigationName": "Juvenile Detention Abuse", "triggers": ["youth center", "juvenile detention", "youth facility", "juvenile detention center", "juvenile detention centers", "detention center", "detention centers", "detained youth", "youth abused", "detained youth facility", "detained youth facilities", "foster care"], "include": ["sexual assault", "sexual abuse", "abuse", "inappropriate touching"]}, {"Company": "Various", "LitigationName": "Ultra-processed foods Type-2 Diabetes Products Liability", "MdlNum": ["9002"], "triggers": ["type 2 diabetes", "type-2 diabetes", "type 2 diabetes", "ultra processed foods", "ultra-processed foods", "ultra processed food", "ultra-processed food", "ultra processed food"], "include": ["type 2 diabetes", "type-2 diabetes", "type 2 diabetes", "ultra processed foods", "ultra-processed foods"], "exclude": ["glp-1", "stomach paralysis", "intestinal blockage", "eye stroke", "vomitting", "water contamination", "military", "firefighter", "aqueous-film-forming-foam", "afff", "(afff)", "ozempic", "wegovy"]}, {"Company": "Monsanto", "LitigationName": "Glyhphosate Type 2 Diabetes Products Liability", "triggers": ["glyphosate", "roundup"], "include": ["type 2 diabetes", "diabetes"]}, {"Company": "Monsanto", "LitigationName": "RoundUp Products Liability", "MdlNum": ["2741"], "triggers": ["weedkiller", "roundup", "glyphosate", "bayer roundup", "roundup weed killer", "exposed to roundup", "bayer/monsanto", "non-hodgkin", "non-hodgkins", "lymphoma", "non hodgkins"], "exclude": ["oxb<PERSON>ta", "afff", "firefighting", "fire fighting", "firefighting", "gard<PERSON>l", "suboxone", "merick", "indivior", "biozorb", "pfizer-biontech", "igora royal", "false labeling", "playboy", "cigna", "columbus bd", "jessup elite spice", "north haven", "river rouge", "baxter healthcare", "virginia beach", "sterilization", "river rouge", "cancer contamination", "zantac", "johnson & johnson", "firefighting foam", "firefighter", "coventry", "boston scientific", "talcum powder", "medtronic", "xomed", "camp lejeune", "girl scout", "girl scouts", "groveland", "sterigenics", "cosmed", "ethylene oxide", "toxic air", "lakewood", "hair dye", "pfas", "<PERSON><PERSON><PERSON>'s disease", "parkinsons", "ethicon", "hair-dye", "vals<PERSON>an", "losartan", "irb<PERSON><PERSON>an"]}, {"Company": "Huhai, Arbudino, and Hetero Drugs", "LitigationName": "Valsartan, Losartan, and Irbesartan Products Liability Litigation", "MdlNum": ["2875"], "triggers": ["vals<PERSON>an", "losartan", "irb<PERSON><PERSON>an"]}, {"Company": "Paragard Products Liability Litigation", "LitigationName": "Paragard IUD Products Liability", "MdlNum": ["2974"], "triggers": ["para<PERSON>", "<PERSON><PERSON> ud", "<PERSON><PERSON> iud", "IUD"]}, {"Company": "Syngenta", "LitigationName": "Paraquat Products Liability", "MdlNum": ["3004"], "triggers": ["paraquat", "herbicide", "<PERSON><PERSON><PERSON>'s disease", "\"paraquat\""], "exclude": ["pregnant", "asd", "adhd", "add/adhd", "birth defect", "birth defects", "dcpa", "dact<PERSON>"]}, {"Company": "Pfizer", "LitigationName": "Depo-Provera Products Liability", "triggers": ["depo-provera", "depoprovera", "birth control", "injectable birth control", "brain tumor lawsuit", "meningioma", "depo shot", "depo-shot", "depo provera", "brain tumor", "brain tumors", "brain-tumor"], "exclude": ["filshie®", "oxb<PERSON>ta", "afff", "hpv", "gardasil vaccine", "firefighting", "fire fighting", "firefighting", "gardisal", "suboxone", "merick", "indivior", "biozorb", "pfizer-biontech", "pfas", "pfas chemicals", "filshie", "filshie clip", "biozorb marker", "biozorb® implant", "biozorb® implants", "biozorb implant", "biozorb implants", "merck", "filshie clips"]}, {"Company": "<PERSON><PERSON><PERSON>", "LitigationName": "In-App Game Purchases Consumer Fraud", "triggers": ["in-game purchases", "in game purchases", "in-game purchase", "in game purchase", "dark patterns", "dark pattern", "in-game purchases", "in game purchases", "in-app purchase", "in app purchase", "in-app-purchase", "in-app-purchases", "in-app-purchase", "in-app-purchases", "in-app-purchase", "dark patterns", "dark pattern", "dark-patterns", "dark-pattern"], "exclude": ["taking over your child’s life", "your child's well being", "your child's well-being", "child's gaming habit", "video game addiction", "gaming addiction", "video games addiction", "video gaming addiction"]}, {"Company": "Chiquita Canyon/Waste Connections", "LitigationName": "Chiquita Canyon Landfill Environmental Contamination", "description": "Cases involving Chiquita Canyon Landfill, Sunshine Canyon Landfill, toxic exposure, chemical fires, gas leaks, or environmental contamination. If the case mentions specific locations like Val Verde, Castaic, Santa Clarita, or other nearby communities but involves the Chiquita Canyon or Sunshine Canyon landfills, classify under this campaign.", "triggers": ["chiquita canyon", "sunshine canyon", "landfill", "toxic exposure", "chemical fires", "val verde", "castaic", "santa clarita"], "include": ["landfil", "canyon"], "exclude": ["banana", "bananas"]}, {"Company": "Pfizer", "LitigationName": "Oxbryta Products Liability", "triggers": ["oxb<PERSON>ta", "sickle cell", "global blood therapeutics", "voxelotor"]}, {"Company": "Abbott Laboratories", "LitigationName": "Abbott Laboratories Preterm Infant Nutrition Products Liability", "MdlNum": ["3026"], "triggers": ["necrotizing enterocolitis", "fed enfamil", "necrotizing", "nec", "cow's milk", "cow's milk-based", "similac", "enfamil", "nec baby formula", "enfamil/similac"], "exclude": ["fascitis"]}, {"Company": "Johnson & Johnson", "LitigationName": "Johnson & Johnson Talcum Powder Litigation", "MdlNum": ["2738"], "triggers": ["talcum powder", "baby powder", "johnson & johnson", "<PERSON><PERSON><PERSON>", "johnson", "fallopian", "peritoneal", "ovarian cancer"], "include": ["talcum", "talcum powder", "baby powder", "talc"]}, {"Company": "Bard/AngioDynamics", "LitigationName": "Bard/AngioDynamics PowerPort Products Liability", "MdlNum": ["3081", "3125"], "triggers": ["port catheter", "bard powerport lawsuit", "angio dynamics", "angiodynamics", "catheter", "powerport", "angiodynamics", "angiodynamics'", "smartport", "xcela", "ventralex", "smart-port", "chemo port", "chemo ports", "chemo port", "port-a-cath", "port-a-caths", "Bard® PowerPorts®", "powerports®", "powerports", "bard powerport", "bard powerport", "smart port ct", "BardPort®", "bard access systems"], "exclude": ["atrium health", "her2", "breast cancer", "abiomed", "hernia mesh"]}, {"Company": "Sterigenics", "LitigationName": "Ethylene Oxide Toxic Tort", "triggers": ["ethylene oxide"]}, {"Company": "Sterigenics", "LitigationName": "Ethylene Oxide Toxic Tort", "triggers": ["coon rapids", "columbus bd", "arvada, co", "arvada", "northborough steris", "new tazewell", "columbus bd", "jessup elite spice", "north haven", "river rouge", "baxter healthcare", "virginia beach", "sterilization", "river rouge", "cancer contamination", "cvr", "xomed", "lifenet", "midwest sterilization", "lemco ardmore", "groveland", "trigen laboratories", "trigen", "jorgenson labs", "erie medical center", "san engelo", "sterigenics", "medtronic", "toxic air", "sterilization", "arthrex"], "include": ["chemicals", "industrial chemicals", "air pollution", "toxic air", "cancer", "cancers", "toxic air", "unexplained illness"]}, {"Company": "Indivior", "LitigationName": "Suboxone Products Liability", "MdlNum": ["3092"], "triggers": ["suboxone", "indivior", "suboxone film", "Suboxone®"]}, {"Company": "Uber Technologies", "LitigationName": "Uber Technologies Sexual Assault", "MdlNum": ["3084"], "triggers": ["uber", "lyft", "uber/lyft", "ride-sharing", "ridesharing", "rideshare"], "include": ["dating app", "dating", "harm", "assault", "sexual assault", "inappropriate", "forced kissing", "genital exposure", "sexually assaulted", "sexual abuse", "physical and sexual"]}, {"Company": "Various", "LitigationName": "Hair Relaxer Products Liability", "MdlNum": ["3060"], "triggers": ["ovarian cancer", "uterine cancer", "endometrial cancer"], "include": ["hair relaxers", "hair relaxer", "hair straightener", "hair straighteners", "chemical hair relaxers", "chemical hair straightener"], "exclude": ["pfas", "AFFF", "firefighting", "fire fighting foam", "firefighting foam", "firefighters", "firefighting gear", "oxb<PERSON>ta", "afff", "firefighting", "fire fighting", "gard<PERSON>l", "suboxone", "merick", "indivior", "biozorb", "pfizer-biontech", "pfas"]}, {"Company": "Various", "LitigationName": "GLP-1 Products Liability", "description": "Cases involving weight loss injections, injectable weight loss medications, weight loss drugs, diabetes injections, or any weight loss and diabetes medications including but not limited to: Ozempic, Wegovy, Rybelsus, Trulicity, semaglutide, GLP-1. If ANY content mentions 'weight loss injection', 'injectable weight loss', 'diabetes injection', 'weight loss medication', 'weight loss drug', 'weight loss and diabetes' or similar variations, classify as this campaign.", "MdlNum": ["3094"], "triggers": ["ozempic", "wegovy", "ry<PERSON><PERSON>", "trulicity", "semaglutide", "mou<PERSON><PERSON>", "glp-1", "naion", "weight loss injection", "diabetes injection", "injectable weight loss", "weight loss drug and have stomach issues", "gastrointenstinal issues", "non-arteritic anterior ischemic optic neuropathy", "vision loss while taking Weight Loss Drugs"], "exclude": ["metformin"]}, {"Company": "3M", "LitigationName": "AFFF Products Liability", "MdlNum": ["2873"], "triggers": ["afff", "firefighting foam", "fire fighting foam", "aqueous fire fighting foam", "aqueous film forming foam", "afff foam", "afff exposure", "firefighters", "firefighting gear", "forever chemicals"], "include": ["afff", "firefighting foam", "firefighters", "firefighting"], "exclude": ["los angeles", "insurance payout", "total loss", "wildfire survivors", "wildfire damage", "fire lawsuit", "explosion", "recent wildfires", "eaton fire", "wildfire", "wildfires", "propane explostions", "propane explosion", "toxic water contamination", "toxic water", "hair relaxer", "dog bites", "water-resistant fabrics", "cookware", "auguste", "fout"]}, {"Company": "3M", "LitigationName": "Bair Hugger Products Liability", "MdlNum": ["2666"], "triggers": ["bair hugger"], "include": ["warming device", "infections", "warming blanket"]}, {"Company": "Me<PERSON><PERSON>", "LitigationName": "Gardasil Products Liability", "MdlNum": ["3036"], "triggers": ["gard<PERSON>l", "gard<PERSON>l", "merck"], "include": ["hpv vaccine", "hpv"]}, {"Company": "Various", "LitigationName": "Baby Food Products Liability", "MdlNum": ["3101"], "triggers": ["toxic baby food", "heavy metal contamination", "gerber", "best organic", "beech nut", "beech-nut", "happy baby", "baby food heavy metals", "plum organic", "earth's best organic baby food"], "exclude": ["data breach"]}, {"Company": "Exactech", "LitigationName": "Exactech Products Liability", "MdlNum": ["3044"], "triggers": ["exactech"], "include": ["recall", "knee", "ankle", "hip", "joint replacement"]}, {"Company": "Various", "LitigationName": "Online Gambling Products Liability", "triggers": ["online gambling", "online casino", "casino slots", "unauthorized gambling"]}, {"Company": "Various", "LitigationName": "Video Game Addiction Products Liability", "triggers": ["justice for gaming", "is gaming taking over your child’s life", "video game addiction", "gaming disorder", "video gaming addiction", "gaming addiction", "fortnite", "call of duty", "roblo<PERSON>", "gaming", "gaming habits", "child stealing", "epic games", "minecraft", "grand theft auto", "gta", "overwatch", "world of warcraft", "rainbox six", "battlefield", "apex legends", "nba 2k", "candy crush"], "include": ["addicting", "addiction", "hooked", "gaming", "mental", "physical", "health", "sleep", "disturbances", "emotional", "distress", "exhaustion", "treatment", "recovery", "oppositional defiant disorder", "ADHD", "suicide", "harm", "habit", "habits", "addicted", "issues", "highly addictive"]}, {"Company": "Fisher-Price", "LitigationName": "Fisher-Price Snuga Infant Swing Products Liability", "triggers": ["fisher-price"], "include": ["swing", "recall", "snuga swing", "snuga swing"]}, {"Company": "Various", "LitigationName": "Social Media Addiction Products Liability", "MdlNum": ["3047"], "triggers": ["social media addiction", "social media harm", "instagram", "tiktok", "youtube", "facebook", "instagram", "snapchat", "meta", "social media use,", "excessive social media use", "addictive social media", "social media usage."], "include": ["social media addiction", "child harm", "mental health"], "exclude": ["video games", "te<PERSON><PERSON>", "<PERSON><PERSON>", "c-8", "oxy", "asbestos", "oxycontin", "oxy kingpins", "mass arbitration", "privacy violations", "arbitration", "extortion"]}, {"Company": "Hologic", "LitigationName": "Hologic Biomarker Implant Products Liability", "triggers": ["hologic", "biozorb", "biozorb® implant lawsuit", "biozorb implant", "biozorb implants", "breast cancer implant lawsuit", "biozorb® implant", "biozorb® implants", "biozorb marker", "BioZorb® Marker"]}, {"Company": "<PERSON>immer <PERSON>", "LitigationName": "Zimmer Biomet Products Liability", "triggers": ["zimmer biomet"], "include": ["hip implant", "complications", "hip"]}, {"Company": "<PERSON><PERSON><PERSON>", "LitigationName": "Veozah Products Liability", "triggers": ["<PERSON><PERSON><PERSON><PERSON>"], "include": ["liver"]}, {"Company": "Etsy", "LitigationName": "Etsy Privacy Litigation", "triggers": ["etsy", "etsy's", "etsy.com"]}, {"Company": "Event Ticket Center", "LitigationName": "Event Ticket Center Junk Fee Investigation", "triggers": ["event ticket center", "event ticket centers"]}, {"Company": "Expedia", "LitigationName": "Expedia Deceptive Fees Investigation", "triggers": ["expedia"], "include": ["new york"]}, {"Company": "Experian", "LitigationName": "Experian Consumer Protection", "triggers": ["experian", "identityworks", "creditworks"], "include": ["illegally restrict", "illegally restricting", "illegal cause"]}, {"Company": "AMVAC Chemical Corporation", "LitigationName": "Dacthal Pesticide Products Liability", "triggers": ["dact<PERSON>", "dcpa", "pesticide"], "include": ["fetal", "birth defects", "child", "birth defect"], "exclude": ["paraqut"]}, {"Company": "Various", "LitigationName": "Asbestos Products Liability", "triggers": ["asbestos", "mesothelioma"], "exclude": ["pfas", "talcum", "talc", "toxic chemicals"]}, {"Company": "Various", "LitigationName": "Mohawk & Shaw PFAS Carpet & Rug Contamination", "triggers": ["mohawk and shaw", "mohawk", "shaw"], "include": ["groundwater", "contamination", "fort", "exposed"], "exclude": ["water", "firefighting", "fire fighting", "firefighting", "firefighting foam", "fire fighting foam", "firefighters", "firefighting gear", "afff", "(afff)", "afff firefighting foam"]}, {"Company": "Various", "LitigationName": "PFAS Drinking Water Contamination", "triggers": ["pfas", "water contamination", "toxic water contamination", "'forever chemicals", "‘Forever Chemicals’"], "include": ["wilmington", "groundwater", "contamination", "fort", "exposed"], "exclude": ["camp lejeune", "fertilizer", "firefighting", "fire fighting", "firefighting", "firefighting foam", "fire fighting foam", "firefighters", "firefighting gear", "afff", "(afff)", "afff firefighting foam", "mohawk", "shaw", "stain resistant"]}, {"Company": "Various", "LitigationName": "PFAS Fertilizer Contamination", "triggers": ["pfas", "toxic"], "include": ["fertilizer"]}, {"Company": "Various", "LitigationName": "Pressure Cooker Products Liability", "triggers": ["pressure cooker", "pressure cookers", "instapot", "insta-pot"]}, {"Company": "<PERSON>", "LitigationName": "<PERSON><PERSON> Sex Abuse Litigation", "triggers": ["sean combs", "p. diddy", "diddy", "puff daddy"]}, {"Company": "Playboy", "LitigationName": "Playboy Sexual Assault Litigation", "triggers": ["playboy", "hugh hefner"], "include": ["sexual assault"], "exclude": []}, {"Company": "Bard/Covidien", "LitigationName": "Bard/Covidien Hernia Mesh Products Liability", "MdlNum": ["2846", "3029"], "triggers": ["hernia mesh", "hernia surgery", "hernia surgeries"]}, {"Company": "Te<PERSON>zza", "LitigationName": "Tepezza Products Liability", "MdlNum": ["3079"], "triggers": ["te<PERSON><PERSON>"], "include": ["hearing loss", "hearing", "tinnitus"]}, {"Company": "<PERSON><PERSON><PERSON><PERSON>", "LitigationName": "Filshie Clips Product Liability", "triggers": ["filshie"]}, {"Company": "Cooper Surgical", "LitigationName": "Cooper Surgical IVF Culture Media", "triggers": ["cooper surgical", "coopersurgical"], "include": ["ivf", "culture medium"], "exclude": ["pgt-a", "ptg-a", "genetic", "aneuploidy"]}, {"Company": "Cooper Surgical", "LitigationName": "Cooper Surgical Defective Aneuploidy Testing", "triggers": ["ptg-a", "pgt-a", "genetic", "aneuploidy", "testing"], "include": ["cooper surgical", "coopersurgical"], "exclude": ["culture medium"]}, {"Company": "<PERSON><PERSON>med", "LitigationName": "Abiomed Impella Heart Pump Products Liability", "triggers": ["abiomed heart pump", "abiomet impella", "impella heart pump"]}, {"Company": null, "LitigationName": "Unpaid Wage Claims", "triggers": ["unpaid wages", "overtime", "worker classification"]}, {"Company": null, "LitigationName": "TCPA Violations", "triggers": ["tcpa", "spam texts"]}, {"Company": "<PERSON><PERSON><PERSON>", "LitigationName": "Zantac Products Liability Litigation", "MdlNum": ["2924"], "triggers": ["zantac", "ranitidine"]}, {"Company": "Various", "LitigationName": "Hair Dye/Color Health Risk Investigation", "triggers": ["hair color", "hair dye", "color exposure", "bladder cancer"], "include": ["hair"], "exclude": ["hair relaxer", "hair relaxers", "advance magazine", "allure's", "pfas", "AFFF", "firefighting", "fire fighting foam", "firefighting foam", "firefighters", "firefighting gear", "oxb<PERSON>ta", "afff", "firefighting", "fire fighting", "gard<PERSON>l", "suboxone", "merck", "false labeling", "indivior", "biozorb", "pfizer-biontech", "pfas", "playboy", "sexual assault"]}, {"Company": "Southern ConEd", "LitigationName": "LA County Wildfire Litigation", "triggers": ["eaton", "california", "los angeles", "eaton fire", "LA", "California", "<PERSON><PERSON>", "Palisades", "CA", "L.A.", "L.A. County", "Palisades", "lidia", "m<PERSON><PERSON><PERSON>", "mountain fire", "palisade", "altadena", "southern california edison", "sunset", "wildfire", "cal fair", "california", "california's", "california"], "include": ["wildfire", "fire", "fires", "wildfires"], "exclude": ["maui", "moss landing", "mountain", "hawaii", "biolab", "conyers fire lawsuit", "biolab explosition", "airport", "biolab", "bio-lab"]}, {"Company": "Airport", "LitigationName": "Airport County Wildfire Litigation", "triggers": ["airport"], "include": ["wildfire"], "exclude": ["biolab", "bio-lab"]}, {"Company": "Johnson & Johnson/Janssen Healthcare", "LitigationName": "Risperdal (Invega) Products Liability", "triggers": ["risperdal", "invega"]}, {"Company": "Honey/PayPal", "LitigationName": "Honey (PayPal) Commission Theft Investigation", "triggers": ["honey", "paypal"], "include": ["commission", "commissions", "commission theft", "browser extension"]}, {"Company": "The Church of Jesus Christ of Latter-day Saints", "LitigationName": "The Church of Jesus Christ of Latter-day Saints Sex Abuse Investigation", "triggers": ["mormon church", "mormon", "lds", "latter-day", "church of jesus christ"], "include": ["sexual abuse", "church", "sexual abuse", "abuse", "clergy abuse", "bishop", "stake president"]}, {"Company": "Biolab", "LitigationName": "Bio-Lab. Chemical Plan Explosion Liability", "triggers": ["biolab", "bio-lab"]}, {"Company": "Chicago IVF", "LitigationName": "Chicago IVF Lost Embryos Litigation", "triggers": ["chicago IVF", "chicago-ivf"]}, {"Company": "CrowdStrike", "LitigationName": "Crowdstrike Outage", "triggers": ["crowdstrike", "crowd strike"], "include": ["software glitch", "outage"]}, {"Company": "<PERSON><PERSON>'s Head", "LitigationName": "Listeria Products Liability", "description": "Cases involving Listeria", "triggers": ["boar's head", "boars head", "listeria"], "include": ["outbreak", "sick", "ill", "hospitalized", "diagnosed", "recall", "contamination", "monocytogenes"]}, {"Company": "Dr. <PERSON>", "LitigationName": "Dr. <PERSON> Sex Abuse Litigation", "description": "Cases involving sexual abuse allegations against Dr. <PERSON>", "triggers": ["dr. barry brock", "barry brock"], "include": ["abuse", "assault", "victim", "survivor"]}, {"Company": "Various", "LitigationName": "Nexium/Prilosec Cancer Products Liability", "description": "Cases involving prolonged use of heartburn medications Nexium or Prilosec linked to development of stomach, gastric, or esophageal cancers.", "triggers": ["nexium", "prilosec"], "include": ["cancer", "gastric", "stomach", "esophageal"]}, {"Company": "Atrium Health Wake Forest Baptist", "LitigationName": "Atrium Health Wake Forest HER2+ Breast Cancer Diagnosis Products Liability", "description": "Cases involving patients at Atrium Health Wake Forest Baptist who were misdiagnosed with HER2 positive breast cancer and underwent unnecessary treatments. This includes patients who received incorrect diagnoses and underwent treatments such as chemotherapy, radiation, or surgery before being informed of the diagnostic error.", "triggers": ["atrium health wake forest", "her2", "her2+", "her2 positive"], "include": ["breast cancer", "misdiagnosis", "wrong type", "diagnostic error", "incorrectly diagnosed", "unnecessary treatment"]}, {"Company": "Barnes & Noble", "LitigationName": "Barnes & Noble Digital Textbook Fees Mass Arbitration", "description": "Cases involving Barnes & Noble campus bookstores' alleged practice of adding undisclosed digital delivery fees for textbooks during checkout. Particularly focuses on university bookstores including Howard and Georgetown, where students may be owed up to $1,500 for potentially illegal fee practices.", "triggers": ["barnes & noble", "digital textbook", "digital delivery fee"], "include": ["howard", "georgetown", "AU's", "American University", "mass arbitration", "bookstore", "textbooks", "digital delivery", "checkout process"], "exclude": ["store closing", "job opening", "book signing", "new release"]}, {"LitigationName": "Data Breach", "triggers": ["data breach", "data breach?", "security breach", "hacked", "personal information exposed", "cyberattack", "unauthorized access", "data leak", "information compromised", "stolen data"]}, {"LitigationName": "Privacy Violation", "triggers": ["online registration", "watch videos on", "personal info was shared", "schedule appointment online", "schedule consultation online", "data illegally shared", "appointment online", "illegal sharing", "schedule a consultation online", "privacy investigation", "privacy violation", "illegal tracking", "vppa", "biometrics", "uploaded photo id to verify", "video privacy protection act", "biometric information", "bipa", "illinois biometric information privacy act", "facial recognition", "cipa", "cookie tracking", "california invasion of privacy act", "wiretapping", "illegal recording", "unlawful surveillance", "session replay", "pixel tracking", "data scraping", "unauthorized data collection", "sharing users' information", "unauthorized use of data", "violated consumers' privacy", "sharing users' personal information", "data sharing", "data shared", "secret tracking software", "data collection", "data privacy"]}, {"LitigationName": "<PERSON><PERSON>", "triggers": ["usury", "predatory lending", "high interest loan", "excessive interest", "payday loan trap", "illegal loan terms", "loan shark"], "exclude": ["etsy"]}, {"LitigationName": "False Advertising", "triggers": ["false advertising", "false marketing", "misleading claims", "deceptive marketing", "bait and switch", "false promises", "product misrepresentation"]}, {"LitigationName": "Overdraft & NSF Fees", "triggers": ["overdraft fee", "nsf fee", "non-sufficient funds fee", "excessive bank fees", "unlawful bank charges", "surprise bank fee", "bounced check", "overdraft", "non-sufficient funds", "bank charge"]}, {"LitigationName": "Wildfire/Fire Claims", "triggers": ["la wildfire", "wildfire damage", "fire damage", "burn injury", "property loss fire", "utility caused fire", "smoke inhalation", "airport fire", "eaton fire", "palisades fire", "maui wildfire"], "exclude": ["uomeod", "mini steamers"]}, {"LitigationName": "Antitrust Litigation", "triggers": ["price fixing", "antitrust", "collusion", "anti-competitive practices", "price gouging", "monopoly pricing", "bid rigging"]}, {"Company": "Multiplan", "LitigationName": "Multiplan Antitrust Investigation", "triggers": ["insurance payment reimbursements", "out-of-network", "multiplan", "data isight", "viant"], "include": ["price-fixing", "price fixing"]}, {"Company": "Syngenta", "LitigationName": "Syngenta Antitrust Investigation", "triggers": ["syngenta", "corte<PERSON>", "basf"], "include": ["price-fixing", "price fixing", "antitrust"]}, {"Company": "PBMs", "LitigationName": "Insulin Pricing Litigation", "triggers": ["self-insured health plans", "eli lilly"], "include": ["insulin", "glp-1 medications"]}, {"Company": "PBM", "LitigationName": "Independent Pharamcy Antitrust Investigation", "triggers": ["independent pharmacy owners"], "include": ["price-fixing", "price fixing"]}, {"LitigationName": "Online Gambling Addiction Investigation", "triggers": ["underog fantasy", "draftkings", "fanduel", "sports bettting apps", "online sports gambling", "online sports betting"], "include": ["gambling addiction", "gambling disorder", "gambling harm", "gambling addiction investigation"], "exclude": ["video game addiction"]}, {"LitigationName": "Accolade Pacemaker Products Liability", "triggers": ["acolade pacemaker"]}, {"LitigationName": "Leqembi/Kisunla Products Liability", "triggers": ["leq<PERSON><PERSON>", "kisunla"], "include": ["brain", "brain injury"]}, {"LitigationName": "Kratom Products Liability", "triggers": ["kratom"]}, {"LitigationName": "Tabletop Fire Pits Products Liability", "triggers": ["table top fire pit", "tabletop fire pit", "tabletop fire pits", "tabletop fire pit recall"]}, {"LitigationName": "Tepezza Products Liability", "triggers": ["te<PERSON><PERSON>"]}, {"LitigationName": "Low Interest Rate Cash Sweep", "triggers": ["low interest rates", "cash sweep"]}, {"LitigationName": "Boy Scouts of America Sexual Abuse Litigation", "triggers": ["boy scouts of america", "bsa", "boy scouts"], "include": ["sexual abuse", "sexual assault", "abuse", "assault"], "exclude": ["catholic church", "catholic clergy", "catholic priests", "catholic priest"]}, {"LitigationName": "Catholic Church Sexual Abuse Litigation", "triggers": ["catholic church", "catholic church sexual abuse survivors", "catholic clergy", "catholic priests", "catholic priest", "church leaders", "clergy"], "include": ["sexual abuse", "sexual assault", "abuse", "assault"]}, {"LitigationName": "Real Page Pricing Fixing Lawsuit", "triggers": ["realpage", "rent"], "exclude": ["hertz", "runway", "job", "knetbooks.com", "knetbooks", "capital one", "credit card"]}, {"LitigationName": "Antitrust Lawsuit", "triggers": ["antitrust"]}, {"Company": "3M", "LitigationName": "3M Earplugs Products Liability", "triggers": ["earplugs", "ear plugs", "3m earplugs", "3m ear plugs", "3m earplug", "3m ear plug", "hearing loss", "tinnitus"], "exclude": ["oxb<PERSON>ta", "te<PERSON><PERSON>", "va disability attorneys", "va disability attorney"]}]