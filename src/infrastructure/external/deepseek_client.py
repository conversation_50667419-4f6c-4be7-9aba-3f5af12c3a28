"""DeepSeek AI client implementation with OpenRouter fallback and retry logic."""
import asyncio
import json
import logging
import os
import re
import time
from datetime import datetime, time as dt_time, timezone
from typing import Dict, Any, Optional, List
from pathlib import Path

import aiohttp
import backoff
from aiohttp import ClientTimeout, ClientError, ClientConnectorError, ClientResponseError

from src.protocols.external import ILLMService

# --- Constants ---
OPENROUTER_BASE_URL = "https://openrouter.ai/api/v1"
DEEPSEEK_DIRECT_BASE_URL = "https://api.deepseek.com/v1"
DEFAULT_FREE_OPENROUTER_MODEL = "deepseek/deepseek-chat-v3-0324"  # Use paid model since free doesn't work
DEFAULT_OPENROUTER_MODEL = "deepseek/deepseek-chat-v3-0324"
DEFAULT_DEEPSEEK_DIRECT_MODEL = "deepseek-chat"

# --- Custom Exceptions ---
class ServiceUnavailableError(Exception):
    """Raised when the service is temporarily unavailable after retries."""
    pass

class RetryableError(Exception):
    """Raised when the API request should be retried (e.g., rate limit, server error)."""
    pass

class FreeTierRateLimitError(Exception):
    """Raised specifically when OpenRouter free tier rate limit is hit."""
    pass

class ConfigurationError(Exception):
    """Raised for configuration-related issues."""
    pass

class LLMResponseError(Exception):
    """Raised for errors in the LLM response payload."""
    pass


class DeepSeekClient(ILLMService):
    """
    DeepSeek API client with OpenRouter fallback and comprehensive retry logic.
    
    Features:
    - Tries DeepSeek direct API during optimal hours (9 AM - 5 PM PST)
    - Falls back to OpenRouter free tier, then paid tier
    - Comprehensive retry logic with exponential backoff
    - Rate limit handling and state management
    """
    
    def __init__(self, api_key: str = None, config: Dict[str, Any] = None):
        """
        Initialize DeepSeek client.
        
        Args:
            api_key: DeepSeek API key (optional, will try config and env)
            config: Configuration dictionary (optional)
        """
        self.logger = logging.getLogger(__name__)
        
        # Configuration from multiple sources
        self.config = config or {}
        
        # API Keys
        self.deepseek_api_key = (
            api_key or 
            self.config.get('deepseek_api_key') or 
            os.getenv('DEEPSEEK_API_KEY')
        )
        self.openrouter_api_key = (
            self.config.get('openrouter_api_key') or 
            os.getenv('OPENROUTER_API_KEY')
        )
        
        # Model configurations
        self.deepseek_direct_model = (
            self.config.get('deepseek_direct_model') or 
            os.getenv('DEEPSEEK_DIRECT_MODEL') or 
            DEFAULT_DEEPSEEK_DIRECT_MODEL
        )
        self.openrouter_model = (
            self.config.get('openrouter_model') or 
            os.getenv('OPENROUTER_MODEL') or 
            DEFAULT_OPENROUTER_MODEL
        )
        
        # OpenRouter headers
        self.openrouter_site_url = (
            self.config.get('openrouter_site_url') or 
            os.getenv('OPENROUTER_SITE_URL')
        )
        self.openrouter_site_name = (
            self.config.get('openrouter_site_name') or 
            os.getenv('OPENROUTER_SITE_NAME')
        )
        
        # Retry configuration
        self.max_retries = int(self.config.get('max_retries', 5))
        self.initial_retry_delay = float(self.config.get('initial_retry_delay', 1.0))
        self.max_retry_delay = float(self.config.get('max_retry_delay', 60.0))
        self.timeout = int(self.config.get('timeout', 300))
        
        # Session management
        self._session: Optional[aiohttp.ClientSession] = None
        self.session_id = None
        
        # State for service selection logic
        self.deepseek_direct_timeout_count = 0
        self.openrouter_free_rate_limited = False
        self.use_deepseek_direct = True
        
        # Validate essential configuration
        if not self.openrouter_api_key:
            self.logger.warning("OpenRouter API key is not configured. Primary API calls will fail if DeepSeek Direct is unavailable.")
        if not self.deepseek_api_key:
            self.logger.warning("DeepSeek API key is not configured. Fallback mechanism will be disabled.")
    
    async def _initialize(self):
        """Initialize the aiohttp session."""
        if not self._session:
            timeout = ClientTimeout(total=self.timeout)
            self._session = aiohttp.ClientSession(timeout=timeout)
            self.session_id = id(self._session)
            self.logger.debug(f"Initialized session with ID: {self.session_id}")
    
    async def _ensure_session(self):
        """Ensure session is initialized."""
        if not self._session:
            await self._initialize()
    
    async def __aenter__(self):
        """Async context manager entry."""
        await self._initialize()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self._session:
            await self._session.close()
    
    async def close_session(self):
        """Close the aiohttp session. Called by transformer cleanup."""
        if self._session and not self._session.closed:
            await self._session.close()
            self.logger.debug(f"Closed session with ID: {self.session_id}")
            self._session = None
            self.session_id = None
    
    def _is_within_deepseek_window(self) -> bool:
        """Check if current time is within DeepSeek optimal hours (16:30 - 00:30 UTC)."""
        try:
            now_utc = datetime.now(timezone.utc)
            current_utc_time = now_utc.time().replace(tzinfo=timezone.utc)
            start_time = dt_time(16, 30, tzinfo=timezone.utc)  # 16:30 UTC
            end_time = dt_time(0, 30, tzinfo=timezone.utc)     # 00:30 UTC (midnight crossing)
            
            # Check if current time is within the allowed window (handles midnight crossing)
            is_within_window = (start_time <= current_utc_time) or (current_utc_time <= end_time)
            
            self.logger.debug(
                f"Current UTC time: {current_utc_time}. DeepSeek Direct window (16:30-00:30 UTC): {is_within_window}")
            
            return is_within_window
        except Exception as e:
            self.logger.warning(f"Error checking DeepSeek time window: {e}")
            return False  # Default to NOT allowing DeepSeek on error
    
    @backoff.on_exception(
        backoff.expo,
        (RetryableError, ClientConnectorError, asyncio.TimeoutError, ClientResponseError),
        max_tries=5,
        max_time=300
    )
    async def _attempt_chat_completion(
        self, 
        url: str, 
        payload: Dict[str, Any], 
        headers: Dict[str, str],
        service_name: str,
        json_mode: bool = False
    ) -> Any:
        """
        Attempt a single chat completion request with retry logic.
        """
        await self._ensure_session()
        
        try:
            async with self._session.post(url, json=payload, headers=headers) as response:
                self.logger.debug(f"[{service_name}] Response status: {response.status}")
                
                # Handle HTTP errors
                if response.status == 429:
                    self.logger.warning(f"[{service_name}] Rate limit hit (HTTP 429)")
                    if service_name == "OpenRouterFree":
                        raise FreeTierRateLimitError("OpenRouter free tier rate limit exceeded")
                    raise RetryableError(f"Rate limit exceeded for {service_name}")
                
                if response.status >= 400:
                    error_text = await response.text()
                    self.logger.error(f"[{service_name}] HTTP {response.status}: {error_text}")
                    if response.status >= 500:
                        raise RetryableError(f"Server error {response.status}: {error_text}")
                    else:
                        raise ClientResponseError(
                            response.request_info, 
                            response.history,
                            status=response.status,
                            message=error_text
                        )
                
                # Parse response
                try:
                    result = await response.json()
                except json.JSONDecodeError:
                    text_result = await response.text()
                    self.logger.warning(f"[{service_name}] Non-JSON response, treating as text")
                    result = text_result
                
                # Handle API errors in response payload
                if isinstance(result, dict) and 'error' in result:
                    error_info = result['error']
                    error_message = error_info.get('message', 'Unknown API error')
                    error_type = error_info.get('type', 'api_error')
                    
                    self.logger.error(f"[{service_name}] API error: [{error_type}] {error_message}")
                    
                    if "Rate limit exceeded: free-models-per-min" in error_message:
                        raise FreeTierRateLimitError(error_message)
                    if "context_length_exceeded" in error_type:
                        raise LLMResponseError(f"Context length exceeded: {error_message}")
                    
                    raise ClientResponseError(
                        response.request_info,
                        response.history,
                        status=response.status,
                        message=f"API error: {error_message}"
                    )
                
                # Extract content from response
                if isinstance(result, dict):
                    try:
                        message = result.get('choices', [{}])[0].get('message', {})
                        content = message.get('content')
                    except (KeyError, IndexError, TypeError) as e:
                        self.logger.error(f"[{service_name}] Failed to extract content: {e}")
                        raise LLMResponseError(f"Invalid response structure: {e}")
                elif isinstance(result, str):
                    content = result
                else:
                    raise LLMResponseError(f"Unexpected response type: {type(result)}")
                
                if content is None:
                    raise LLMResponseError("Response content is null")
                
                # Handle JSON mode
                if json_mode and isinstance(content, str):
                    return self._parse_json_response(content, service_name)
                
                return content
                
        except (ClientConnectorError, asyncio.TimeoutError) as e:
            self.logger.warning(f"[{service_name}] Connection/timeout error: {e}")
            if service_name == "DeepSeekDirect":
                self.deepseek_direct_timeout_count += 1
            raise RetryableError(f"Connection error: {e}")
    
    def _parse_json_response(self, content: str, service_name: str) -> Dict[str, Any]:
        """Parse JSON response, handling markdown code blocks and truncation."""
        content_str = content.strip()
        
        # Try to extract JSON from markdown code blocks
        match = re.search(r"```(?:json)?\s*([\{[].*[}\]])\s*```", content_str, re.DOTALL | re.IGNORECASE)
        if match:
            json_str = match.group(1).strip()
        else:
            json_str = content_str
        
        try:
            return json.loads(json_str)
        except json.JSONDecodeError as e:
            # Check if this looks like truncated JSON
            if "Unterminated string" in str(e) or "Expecting" in str(e):
                self.logger.warning(f"[{service_name}] Detected truncated JSON response: {e}")
                
                # Try to recover partial data from truncated JSON
                try:
                    # Attempt to fix common truncation issues
                    fixed_json = self._attempt_json_repair(json_str)
                    if fixed_json:
                        return json.loads(fixed_json)
                except:
                    pass
                
                # If repair fails, return partial data that we can extract
                partial_data = self._extract_partial_json_data(json_str)
                if partial_data:
                    self.logger.info(f"[{service_name}] Recovered partial data from truncated response")
                    return partial_data
            
            self.logger.error(f"[{service_name}] JSON parsing failed: {e}")
            self.logger.debug(f"[{service_name}] Content: {content_str[:1000]}")
            raise LLMResponseError(f"Failed to parse JSON: {e}")
    
    def _attempt_json_repair(self, json_str: str) -> str:
        """Attempt to repair truncated JSON by closing incomplete structures."""
        try:
            # Count open/close brackets and braces
            open_braces = json_str.count('{')
            close_braces = json_str.count('}')
            open_brackets = json_str.count('[')
            close_brackets = json_str.count(']')
            
            # If we have unclosed structures, try to close them
            repaired = json_str
            
            # Close unclosed strings (common truncation point)
            if repaired.count('"') % 2 == 1:
                repaired += '"'
            
            # Close unclosed arrays
            for _ in range(open_brackets - close_brackets):
                repaired += ']'
            
            # Close unclosed objects  
            for _ in range(open_braces - close_braces):
                repaired += '}'
            
            return repaired
        except:
            return None
    
    def _extract_partial_json_data(self, json_str: str) -> Dict[str, Any]:
        """Extract what data we can from truncated JSON."""
        partial_data = {}
        
        try:
            # Extract complete fields using regex
            # Extract case_title
            title_match = re.search(r'"case_title":\s*"([^"]*)"', json_str)
            if title_match:
                partial_data['case_title'] = title_match.group(1)
            
            # Extract plaintiffs array (even if incomplete)
            plaintiffs_match = re.search(r'"plaintiffs":\s*\[(.*?)\]', json_str, re.DOTALL)
            if plaintiffs_match:
                plaintiffs_str = plaintiffs_match.group(1)
                plaintiffs = re.findall(r'"([^"]*)"', plaintiffs_str)
                if plaintiffs:
                    partial_data['plaintiffs'] = plaintiffs
            
            # Extract defendants array (even if incomplete) 
            defendants_match = re.search(r'"defendants":\s*\[(.*?)(?:\]|$)', json_str, re.DOTALL)
            if defendants_match:
                defendants_str = defendants_match.group(1)
                defendants = re.findall(r'"([^"]*)"', defendants_str)
                if defendants:
                    partial_data['defendants'] = defendants
            
            # Extract attorneys array (even if incomplete)
            attorneys_match = re.search(r'"attorneys":\s*\[(.*?)(?:\]|$)', json_str, re.DOTALL)
            if attorneys_match:
                attorneys_str = attorneys_match.group(1)
                attorneys = re.findall(r'"([^"]*)"', attorneys_str)
                if attorneys:
                    partial_data['attorneys'] = attorneys
            
            return partial_data if partial_data else None
            
        except Exception as e:
            self.logger.debug(f"Failed to extract partial JSON data: {e}")
            return None
    
    async def _chat_completion(
        self,
        messages: List[Dict[str, str]],
        temperature: float = 0.7,
        max_tokens: int = 4000,
        json_mode: bool = False
    ) -> Any:
        """
        Perform chat completion with service fallback logic.
        """
        await self._ensure_session()
        
        # Determine service attempt sequence
        attempt_sequence = []
        is_within_deepseek_window = self._is_within_deepseek_window()
        
        if (self.use_deepseek_direct and 
            is_within_deepseek_window and 
            self.deepseek_api_key and 
            self.deepseek_direct_timeout_count < 3):
            
            # Try DeepSeek Direct first
            attempt_sequence.append({
                "service": "DeepSeekDirect",
                "model": self.deepseek_direct_model,
                "url": DEEPSEEK_DIRECT_BASE_URL,
                "api_key": self.deepseek_api_key
            })
            
            # Add OpenRouter Paid as fallback
            if self.openrouter_api_key:
                attempt_sequence.append({
                    "service": "OpenRouterPaid",
                    "model": self.openrouter_model,
                    "url": OPENROUTER_BASE_URL,
                    "api_key": self.openrouter_api_key
                })
        else:
            # OpenRouter primary sequence
            # Always skip free tier since it's not available for this account
            force_paid = self.config.get('force_openrouter_paid', True)  # Default to True
            
            if force_paid:
                self.logger.debug("Skipping OpenRouter Free tier (not available for this account)")
            else:
                # This code path is kept for compatibility but shouldn't be used
                if not self.openrouter_free_rate_limited and self.openrouter_api_key:
                    attempt_sequence.append({
                        "service": "OpenRouterFree",
                        "model": DEFAULT_FREE_OPENROUTER_MODEL,
                        "url": OPENROUTER_BASE_URL,
                        "api_key": self.openrouter_api_key
                    })
                    self.logger.debug(f"Added OpenRouterFree to sequence (force_paid={force_paid})")
            
            if self.openrouter_api_key:
                attempt_sequence.append({
                    "service": "OpenRouterPaid",
                    "model": self.openrouter_model,
                    "url": OPENROUTER_BASE_URL,
                    "api_key": self.openrouter_api_key
                })
        
        if not attempt_sequence:
            raise ConfigurationError("No usable API endpoints configured")
        
        # Try each service in sequence
        last_error = None
        for attempt_info in attempt_sequence:
            service_name = attempt_info["service"]
            model_name = attempt_info["model"]
            url = f"{attempt_info['url']}/chat/completions"
            api_key = attempt_info["api_key"]
            
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {api_key}"
            }
            
            # Add OpenRouter-specific headers
            if service_name.startswith("OpenRouter"):
                if self.openrouter_site_url:
                    headers["HTTP-Referer"] = self.openrouter_site_url
                if self.openrouter_site_name:
                    headers["X-Title"] = self.openrouter_site_name
            
            payload = {
                "model": model_name,
                "messages": messages,
                "temperature": temperature,
                "max_tokens": max_tokens,
            }
            
            # Handle json_mode
            if json_mode:
                payload["response_format"] = {"type": "json_object"}
            
            self.logger.info(f"Attempting chat completion via {service_name} (Model: {model_name})")
            
            try:
                result = await self._attempt_chat_completion(
                    url=url,
                    payload=payload,
                    headers=headers,
                    service_name=service_name,
                    json_mode=json_mode
                )
                
                self.logger.info(f"Successfully received response from {service_name}")
                
                # Reset state on success
                if service_name == "DeepSeekDirect":
                    self.deepseek_direct_timeout_count = 0
                
                return result
                
            except FreeTierRateLimitError as e:
                if service_name == "OpenRouterFree":
                    self.openrouter_free_rate_limited = True
                    self.logger.warning(f"OpenRouter Free tier rate limit hit: {e}")
                    last_error = e
                    continue
                else:
                    raise
                    
            except (RetryableError, ServiceUnavailableError, LLMResponseError, ClientResponseError) as e:
                self.logger.error(f"Failed to get response from {service_name}: {e}")
                if service_name == "DeepSeekDirect":
                    self.deepseek_direct_timeout_count += 1
                last_error = e
                continue
        
        # All services failed
        raise ServiceUnavailableError(f"All configured services failed. Last error: {last_error}")
    
    async def generate_text(self, prompt: str, **kwargs) -> str:
        """
        Generate text from a prompt.
        
        Args:
            prompt: Input prompt
            **kwargs: Additional parameters (model, temperature, max_tokens, etc.)
            
        Returns:
            Generated text
        """
        system_prompt = kwargs.get('system_prompt', "You are a helpful assistant.")
        temperature = kwargs.get('temperature', 0.7)
        max_tokens = kwargs.get('max_tokens', 4000)
        
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": prompt}
        ]
        
        return await self._chat_completion(
            messages=messages,
            temperature=temperature,
            max_tokens=max_tokens,
            json_mode=False
        )
                    
    async def process_batch(self, prompts: List[str], **kwargs) -> List[str]:
        """
        Process multiple prompts efficiently.
        
        Args:
            prompts: List of input prompts
            **kwargs: Additional parameters
            
        Returns:
            List of generated texts
        """
        tasks = [self.generate_text(prompt, **kwargs) for prompt in prompts]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        return [
            result if isinstance(result, str) else None
            for result in results
        ]
        
    async def extract_structured_data(self, text: str, schema: Dict[str, Any], **kwargs) -> Dict[str, Any]:
        """
        Extract structured data from text using a schema.
        
        Args:
            text: Input text
            schema: Expected output schema
            **kwargs: Additional parameters
            
        Returns:
            Extracted data matching the schema
        """
        # Create prompt for structured extraction
        prompt = f"""Extract the following information from the text and return it as valid JSON matching this schema:

Schema: {json.dumps(schema, indent=2)}

Text: {text}

Return only valid JSON, no additional text."""

        system_prompt = kwargs.get('system_prompt', "You are a helpful assistant that extracts structured data from text.")
        temperature = kwargs.get('temperature', 0.2)
        max_tokens = kwargs.get('max_tokens', 8000)  # Increased to prevent truncation
        
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": prompt}
        ]
        
        # Try up to 2 times if we get truncated responses
        for attempt in range(2):
            try:
                # Use json_mode
                result = await self._chat_completion(
                    messages=messages,
                    temperature=temperature,
                    max_tokens=max_tokens,
                    json_mode=True
                )
                
                # Check if we got a complete response
                if isinstance(result, dict) and result:
                    return result
                elif attempt == 0:  # First attempt failed or incomplete
                    self.logger.warning(f"Attempt {attempt + 1} returned incomplete data, retrying with higher token limit")
                    max_tokens = min(max_tokens * 1.5, 16000)  # Increase token limit for retry
                    continue
                else:
                    return result if isinstance(result, dict) else {}
                    
            except LLMResponseError as e:
                if "Failed to parse JSON" in str(e) and attempt == 0:
                    self.logger.warning(f"JSON parsing failed on attempt {attempt + 1}, retrying with higher token limit")
                    max_tokens = min(max_tokens * 1.5, 16000)  # Increase token limit for retry
                    continue
                else:
                    self.logger.error(f"Failed to extract structured data after {attempt + 1} attempts: {e}")
                    return {}
            except Exception as e:
                self.logger.error(f"Failed to extract structured data: {e}")
                return {}
        
        return {}
    
    def _load_prompt(self, filename: str) -> str:
        """Load prompt from file."""
        try:
            # Use the same path structure as the original
            from pathlib import Path
            script_location = Path(__file__).resolve()
            project_root = script_location
            while project_root.name != 'src' and project_root.parent != project_root:
                project_root = project_root.parent
            if project_root.name == 'src':
                project_root = project_root.parent
            
            prompt_path = project_root / 'src' / 'config' / 'prompts' / 'post_processor' / filename
            
            if not prompt_path.exists():
                raise FileNotFoundError(f"Prompt file not found: {prompt_path}")
                
            with open(prompt_path, 'r', encoding='utf-8') as f:
                content = f.read().strip()
                
            if not content:
                raise ValueError(f"Prompt file is empty: {prompt_path}")
                
            return content
            
        except Exception as e:
            self.logger.error(f"Error loading prompt {filename}: {e}")
            raise

    async def extract_attorneys(self, data: dict, filing: str) -> None:
        """Extract attorneys from filing text using the original working method."""
        if not isinstance(data, dict):
            self.logger.warning("Data parameter must be a dictionary")
            return
            
        if not filing or not isinstance(filing, str) or len(filing.strip()) < 100:
            self.logger.warning("Filing text is empty, invalid, or too short for attorney extraction")
            data.setdefault('attorneys_gpt', [])
            return
            
        data.setdefault('attorneys_gpt', [])  # Ensure key exists
        
        try:
            # Load original prompts
            system_prompt = self._load_prompt('extract_attorneys_system.md')
            user_prompt_template = self._load_prompt('extract_attorneys_user.md')
            
            # Use last 15000 chars like original
            truncated_filing = filing[-15000:]
            user_prompt = user_prompt_template.replace('{filing}', truncated_filing)
            
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]
            
            # Use json_mode like original
            response = await self._chat_completion(
                messages=messages,
                temperature=0.1,
                max_tokens=4096,
                json_mode=True
            )
            
            # Parse response
            if isinstance(response, dict):
                attorneys = response.get("attorneys", [])
            elif isinstance(response, str):
                try:
                    parsed = json.loads(response)
                    attorneys = parsed.get("attorneys", [])
                except:
                    self.logger.error("Failed to parse attorney response as JSON")
                    attorneys = []
            else:
                attorneys = []
            
            self.logger.info(f"LLM extracted {len(attorneys)} attorney candidates")
            
            # Process and validate attorneys like original
            attorneys_list = []
            for atty in attorneys:
                if not isinstance(atty, dict):
                    continue
                    
                name = str(atty.get("attorney_name", "")).strip()
                firm = str(atty.get("law_firm", "")).strip()
                email = str(atty.get("email", "")).strip()
                
                if not name and not firm:
                    continue
                    
                attorney_entry = {
                    "attorney_name": name or "N/A",
                    "law_firm": firm or "N/A",
                    "email": email or "N/A"
                }
                attorneys_list.append(attorney_entry)
            
            # Deduplicate by attorney name
            seen_names = set()
            deduped_attorneys = []
            for attorney in attorneys_list:
                name_key = attorney["attorney_name"].lower()
                if name_key not in seen_names and name_key != "n/a":
                    seen_names.add(name_key)
                    deduped_attorneys.append(attorney)
            
            data['attorneys_gpt'] = deduped_attorneys
            self.logger.info(f"Stored {len(deduped_attorneys)} deduplicated attorneys with law firms")
            
        except Exception as e:
            self.logger.error(f"Error during attorney extraction: {e}")
            data['attorneys_gpt'] = []

    async def extract_versus(self, data: dict) -> None:
        """Extract properly formatted versus title from case information."""
        if not isinstance(data, dict):
            self.logger.warning("Data parameter must be a dictionary")
            return
            
        try:
            # Load versus prompts
            system_prompt = self._load_prompt('extract_versus_system.md')
            user_prompt_template = self._load_prompt('extract_versus_user.md')
            
            # Get required fields with defaults
            original_filename = data.get('original_filename', data.get('new_filename', ''))
            plaintiffs = data.get('plaintiff', [])
            defendants = data.get('defendant', [])
            
            # Ensure plaintiffs and defendants are lists
            if not isinstance(plaintiffs, list):
                plaintiffs = [str(plaintiffs)] if plaintiffs else []
            if not isinstance(defendants, list):
                defendants = [str(defendants)] if defendants else []
            
            # Skip if we don't have required data
            if not plaintiffs or not defendants:
                self.logger.warning("Missing plaintiff or defendant data for versus extraction")
                return
            
            # Format user prompt with case data
            user_prompt = user_prompt_template.format(
                original_filename=original_filename,
                plaintiff=plaintiffs,
                defendant=defendants
            )
            
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]
            
            # Use json_mode for structured response
            response = await self._chat_completion(
                messages=messages,
                temperature=0.1,
                max_tokens=1024,
                json_mode=True
            )
            
            # Parse response
            if isinstance(response, dict):
                versus = response.get("versus", "")
                plaintiffs_gpt = response.get("plaintiffs_gpt", [])
            elif isinstance(response, str):
                try:
                    parsed = json.loads(response)
                    versus = parsed.get("versus", "")
                    plaintiffs_gpt = parsed.get("plaintiffs_gpt", [])
                except:
                    self.logger.error("Failed to parse versus response as JSON")
                    versus = ""
                    plaintiffs_gpt = []
            else:
                versus = ""
                plaintiffs_gpt = []
            
            # Update data with extracted versus and plaintiffs_gpt
            if versus:
                data['versus'] = versus
                self.logger.info(f"Extracted versus: '{versus}'")
            else:
                self.logger.warning("No versus extracted from LLM response")
                
            if plaintiffs_gpt:
                data['plaintiffs_gpt'] = plaintiffs_gpt
                self.logger.info(f"Extracted {len(plaintiffs_gpt)} plaintiffs_gpt entries")
                
        except Exception as e:
            self.logger.error(f"Error during versus extraction: {e}")

    async def extract_all_information(self, data: dict, pdf_text: str) -> None:
        """
        Extract legal case information from PDF text and update data dictionary in-place.
        Uses the original working extraction method.
        """
        if not isinstance(data, dict):
            self.logger.warning("Data parameter must be a dictionary")
            return
            
        if not pdf_text or not isinstance(pdf_text, str):
            self.logger.warning("PDF text is empty or invalid")
            return
        
        # Use the original working method for attorney extraction
        await self.extract_attorneys(data, pdf_text)
        
        # Extract versus title from case information
        await self.extract_versus(data)
        
        # Extract case information (MDL, title, allegations)
        await self.extract_case_information(data, pdf_text)
        
        # Ensure required fields have defaults
        data.setdefault('plaintiff', [])
        data.setdefault('defendant', [])
        data.setdefault('attorneys', [])
        data.setdefault('plaintiffs_gpt', [])
        data.setdefault('attorneys_gpt', [])

    async def extract_case_information(self, data: dict, pdf_text: str) -> None:
        """
        Extract case information including MDL number, title, and allegations.
        """
        try:
            system_prompt = self._load_prompt('extract_case_information_system.md')
            user_prompt_template = self._load_prompt('extract_case_information_user.md')
            
            # Limit text length to avoid token limits
            max_chars = 10000
            text_sample = pdf_text[:max_chars] if len(pdf_text) > max_chars else pdf_text
            
            user_prompt = user_prompt_template.replace('{filing}', text_sample)
            
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]
            
            response = await self._chat_completion(
                messages=messages,
                temperature=0.1,
                max_tokens=1000,
                json_mode=True
            )
            
            if not response:
                self.logger.warning("No response from case information extraction")
                return
                
            # Handle response - it should already be a dict from json_mode
            try:
                if isinstance(response, dict):
                    case_info = response
                elif isinstance(response, str):
                    case_info = json.loads(response.strip())
                else:
                    self.logger.error(f"Unexpected response type: {type(response)}")
                    return
                
                # Extract fields based on prompt structure
                mdl_num = case_info.get('mdl_num', 'NA')
                mdl_cat = case_info.get('mdl_cat', 'NA')
                title = case_info.get('title')
                allegations = case_info.get('allegations')
                
                # Update data dictionary
                if mdl_num and mdl_num != 'NA':
                    data['mdl_num'] = mdl_num
                    if mdl_cat and mdl_cat != 'NA':
                        data['mdl_cat'] = mdl_cat
                    self.logger.info(f"Extracted MDL info: {mdl_num}, category: {mdl_cat}")
                    # Set title and allegations to null for MDL cases
                    data['title'] = None
                    data['allegations'] = None
                else:
                    # Non-MDL case, set title and allegations
                    data['mdl_num'] = 'NA'
                    data['mdl_cat'] = 'NA'
                    if title:
                        data['title'] = title
                        self.logger.info(f"Extracted case title: {title}")
                    if allegations:
                        data['allegations'] = allegations
                        self.logger.info(f"Extracted allegations: {allegations[:100]}...")
                        
            except json.JSONDecodeError as e:
                self.logger.error(f"Failed to parse case information JSON: {e}")
                self.logger.debug(f"Raw response: {response}")
                
        except Exception as e:
            self.logger.error(f"Error during case information extraction: {e}")