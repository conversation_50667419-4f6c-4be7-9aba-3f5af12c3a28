"""Async S3 storage implementation."""
import asyncio
import functools
import logging
import mimetypes
import os
from typing import Dict, List, Optional, Tuple, Union
from urllib.parse import urlparse

import aioboto3
from botocore.exceptions import Client<PERSON>rror, NoCredentialsError
from botocore.config import Config

# Note: Avoiding circular import by implementing try_remove locally

S3_ACCESS_DENIED_MARKER = "S3_ACCESS_DENIED"


def try_remove(file_path: str, logger, context: str = ""):
    """
    Local implementation to avoid circular import.
    Try to remove a file, logging any errors but not raising exceptions.
    """
    try:
        if os.path.exists(file_path):
            os.remove(file_path)
            logger.debug(f"Removed file: {file_path} ({context})")
    except OSError as e:
        logger.warning(f"Could not remove file {file_path} ({context}): {e}")
    except Exception as e:
        logger.error(f"Unexpected error removing file {file_path} ({context}): {e}")


class S3AsyncStorage:
    """Async S3 storage implementation with connection pooling and retry logic."""
    
    def __init__(self, bucket_name: str, aws_access_key: str, aws_secret_key: str, 
                 aws_region: Optional[str] = None, max_pool_connections: int = 50):
        """
        Initialize async S3 storage.
        
        Args:
            bucket_name: S3 bucket name
            aws_access_key: AWS access key
            aws_secret_key: AWS secret key
            aws_region: AWS region (optional)
            max_pool_connections: Maximum connection pool size
        """
        self.bucket_name = bucket_name
        self.aws_access_key = aws_access_key
        self.aws_secret_key = aws_secret_key
        # Get region from environment if not provided
        if aws_region is None:
            aws_region = (
                os.getenv('AWS_REGION') or 
                os.getenv('LEXGENIUS_AWS_REGION') or
                os.getenv('REGION_NAME') or
                'us-west-2'  # Default to us-west-2 to match project default
            )
        self.aws_region = aws_region
        self.max_pool_connections = max_pool_connections
        self.logger = logging.getLogger(__name__)
        self._session = None
        self._client = None
        self.folder_cache = set()
        
    async def __aenter__(self):
        """Async context manager entry."""
        await self._initialize()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.close()
        
    async def _initialize(self):
        """Initialize the S3 client."""
        try:
            # Configure boto3 client
            botocore_config = Config(
                max_pool_connections=self.max_pool_connections,
                region_name=self.aws_region
            )
            
            self.logger.info(f"Initializing async S3 client for bucket '{self.bucket_name}' in region '{self.aws_region}'")
            
            # Create session and client
            self._session = aioboto3.Session(
                aws_access_key_id=self.aws_access_key,
                aws_secret_access_key=self.aws_secret_key,
                region_name=self.aws_region
            )
            
            self._client = await self._session.client(
                's3',
                config=botocore_config
            ).__aenter__()
            
            self.logger.info("S3 async client initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize S3 client: {e}")
            raise
            
    async def close(self):
        """Close the S3 client."""
        if self._client:
            await self._client.__aexit__(None, None, None)
            self._client = None
        # aioboto3 sessions don't have a close() method - just reset reference
        if self._session:
            self._session = None
        
    async def file_exists(self, object_key: str) -> bool:
        """
        Check if an object exists in S3.
        
        Args:
            object_key: S3 object key
            
        Returns:
            True if object exists, False otherwise
        """
        if not object_key:
            return False
            
        try:
            await self._client.head_object(Bucket=self.bucket_name, Key=object_key)
            self.logger.debug(f"S3 object exists: {object_key}")
            return True
        except ClientError as e:
            error_code = e.response.get('Error', {}).get('Code')
            if error_code in ('404', 'NoSuchKey'):
                self.logger.debug(f"S3 object not found: {object_key}")
                return False
            elif error_code in ('403', 'AccessDenied'):
                self.logger.warning(f"Access denied for S3 object: {object_key}")
                return False
            else:
                self.logger.error(f"Error checking S3 object {object_key}: {e}")
                return False
        except Exception as e:
            self.logger.error(f"Unexpected error checking S3 object {object_key}: {e}")
            return False
    
    async def check_s3_existence_async(self, bucket_name: str, object_key: str) -> bool:
        """Backward compatibility alias for file_exists."""
        # Note: bucket_name parameter is ignored since it's set at init
        return await self.file_exists(object_key)
            
    async def upload_file(self, file_path: str, object_key: str, 
                         content_type: Optional[str] = None,
                         force_upload: bool = False) -> Tuple[str, str]:
        """
        Upload a file to S3.
        
        Args:
            file_path: Local file path
            object_key: S3 object key
            content_type: MIME type (optional)
            force_upload: Force upload even if exists
            
        Returns:
            Tuple of (file_path, status)
        """
        object_url = f"https://{self.bucket_name}.s3.{self.aws_region}.amazonaws.com/{object_key}"
        
        exists = await self.file_exists(object_key)
        
        if not force_upload and exists:
            self.logger.info(f"File already exists: {object_url}")
            return file_path, "already exists"
            
        if not content_type:
            content_type = mimetypes.guess_type(file_path)[0] or 'application/octet-stream'
            
        extra_args = {
            'ContentType': content_type,
            'ContentDisposition': 'inline'
        }
        
        try:
            # Read file and upload
            with open(file_path, 'rb') as f:
                file_data = f.read()
                
            await self._client.put_object(
                Bucket=self.bucket_name,
                Key=object_key,
                Body=file_data,
                **extra_args
            )
            
            status = "reuploaded" if exists else "uploaded"
            self.logger.info(f"File {status}: {object_url}")
            return file_path, status
            
        except Exception as e:
            self.logger.error(f"Error uploading {file_path}: {e}")
            return file_path, f"error: {str(e)}"
    
    async def upload_file_async(self, file_path: str, bucket_name: str, object_key: str) -> bool:
        """Backward compatibility alias for upload_file.
        
        Returns True on success to match legacy interface.
        """
        # Note: bucket_name parameter is ignored since it's set at init
        _, status = await self.upload_file(file_path, object_key)
        return status in ("uploaded", "reuploaded", "already exists")
    
    def list_existing_files(self, prefix: str = '') -> List[str]:
        """List files with given prefix - synchronous wrapper for backward compatibility."""
        loop = asyncio.get_event_loop()
        return loop.run_until_complete(self.list_objects(prefix))
    
    def download_file_as_string(self, object_key: str) -> Optional[str]:
        """Download file content as string - synchronous wrapper for backward compatibility."""
        loop = asyncio.get_event_loop()
        content = loop.run_until_complete(self.download_content(object_key))
        if content and isinstance(content, bytes):
            return content.decode('utf-8')
        return content
    
    
    async def upload_html_string_async(self, html_content: str, object_key: str) -> bool:
        """Upload HTML content as string - backward compatibility method."""
        return await self.upload_content(html_content, object_key, content_type='text/html')
    
    def upload_html_string(self, html_string: str, object_name: str, 
                          content_type: str = 'text/html', overwrite: bool = True) -> bool:
        """Sync wrapper for upload_content - for backward compatibility."""
        import asyncio
        try:
            # Run the async method in a new event loop
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                result = loop.run_until_complete(
                    self.upload_content(html_string, object_name, content_type, overwrite)
                )
                return result
            finally:
                loop.close()
        except Exception as e:
            self.logger.error(f"Error in sync upload_html_string: {e}")
            return False
            
    async def upload_content(self, content: Union[str, bytes], object_key: str,
                            content_type: str = 'text/plain',
                            overwrite: bool = True) -> bool:
        """
        Upload content directly to S3.
        
        Args:
            content: Content to upload (string or bytes)
            object_key: S3 object key
            content_type: MIME type
            overwrite: Whether to overwrite existing
            
        Returns:
            True if successful, False otherwise
        """
        if not overwrite and await self.file_exists(object_key):
            self.logger.info(f"Object already exists and overwrite=False: {object_key}")
            return False
            
        try:
            # Convert string to bytes if needed
            if isinstance(content, str):
                content = content.encode('utf-8')
                
            await self._client.put_object(
                Bucket=self.bucket_name,
                Key=object_key,
                Body=content,
                ContentType=content_type,
                ContentDisposition='inline'
            )
            
            self.logger.info(f"Content uploaded successfully to {object_key}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error uploading content to {object_key}: {e}")
            return False
            
    async def download_file(self, object_key: str, local_path: str) -> bool:
        """
        Download a file from S3.
        
        Args:
            object_key: S3 object key
            local_path: Local file path
            
        Returns:
            True if successful, False otherwise
        """
        if not await self.file_exists(object_key):
            self.logger.warning(f"S3 object does not exist: {object_key}")
            return False
            
        try:
            # Ensure directory exists
            os.makedirs(os.path.dirname(local_path), exist_ok=True)
            
            # Download file
            response = await self._client.get_object(
                Bucket=self.bucket_name,
                Key=object_key
            )
            
            # Write to local file
            content = await response['Body'].read()
            with open(local_path, 'wb') as f:
                f.write(content)
                
            self.logger.info(f"Downloaded {object_key} to {local_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error downloading {object_key}: {e}")
            if os.path.exists(local_path):
                try_remove(local_path, self.logger, "partial download")
            return False
            
    async def download_content(self, object_key: str) -> Optional[Union[bytes, str]]:
        """
        Download content from S3.
        
        Args:
            object_key: S3 object key
            
        Returns:
            Content as bytes, S3_ACCESS_DENIED_MARKER for 403, or None for errors
        """
        try:
            response = await self._client.get_object(
                Bucket=self.bucket_name,
                Key=object_key
            )
            
            content = await response['Body'].read()
            self.logger.debug(f"Downloaded content from {object_key}")
            return content
            
        except ClientError as e:
            error_code = e.response.get('Error', {}).get('Code')
            if error_code == 'NoSuchKey':
                self.logger.error(f"S3 object not found: {object_key}")
                return None
            elif error_code == 'AccessDenied':
                self.logger.warning(f"Access denied for S3 object: {object_key}")
                return S3_ACCESS_DENIED_MARKER
            else:
                self.logger.error(f"S3 error downloading {object_key}: {e}")
                return None
        except Exception as e:
            self.logger.error(f"Unexpected error downloading {object_key}: {e}")
            return None
            
    async def list_objects(self, prefix: str = '') -> List[str]:
        """
        List objects in S3 with given prefix.
        
        Args:
            prefix: Object key prefix
            
        Returns:
            List of object keys
        """
        try:
            objects = []
            paginator = self._client.get_paginator('list_objects_v2')
            
            async for page in paginator.paginate(Bucket=self.bucket_name, Prefix=prefix):
                for obj in page.get('Contents', []):
                    objects.append(obj['Key'])
                    
            self.logger.debug(f"Listed {len(objects)} objects with prefix '{prefix}'")
            return objects
            
        except Exception as e:
            self.logger.error(f"Error listing objects with prefix '{prefix}': {e}")
            return []
    
    # Alias for backward compatibility
    list_files = list_objects
            
    async def batch_upload_files(self, file_paths: List[str], object_keys: List[str],
                                force_upload: bool = False) -> Dict[str, str]:
        """
        Upload multiple files concurrently.
        
        Args:
            file_paths: List of local file paths
            object_keys: List of S3 object keys
            force_upload: Force upload even if exists
            
        Returns:
            Dict mapping object keys to status
        """
        results = {}
        
        # Create upload tasks
        tasks = []
        for file_path, object_key in zip(file_paths, object_keys):
            task = self.upload_file(file_path, object_key, force_upload=force_upload)
            tasks.append(task)
            
        # Execute uploads concurrently
        upload_results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Process results
        for (file_path, object_key), result in zip(zip(file_paths, object_keys), upload_results):
            if isinstance(result, Exception):
                results[object_key] = f"error: {str(result)}"
            else:
                _, status = result
                results[object_key] = status
                
        return results
        
    async def copy_object(self, source_key: str, dest_key: str) -> bool:
        """
        Copy an object within S3.
        
        Args:
            source_key: Source object key
            dest_key: Destination object key
            
        Returns:
            True if successful, False otherwise
        """
        try:
            copy_source = {'Bucket': self.bucket_name, 'Key': source_key}
            await self._client.copy_object(
                CopySource=copy_source,
                Bucket=self.bucket_name,
                Key=dest_key
            )
            self.logger.info(f"Copied {source_key} to {dest_key}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error copying {source_key} to {dest_key}: {e}")
            return False
            
    async def delete_object(self, object_key: str) -> bool:
        """
        Delete an object from S3.
        
        Args:
            object_key: S3 object key
            
        Returns:
            True if successful, False otherwise
        """
        try:
            await self._client.delete_object(
                Bucket=self.bucket_name,
                Key=object_key
            )
            self.logger.info(f"Deleted object: {object_key}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error deleting {object_key}: {e}")
            return False
            
    async def create_folder(self, folder_key: str) -> Optional[str]:
        """
        Create a folder in S3 (by creating an empty object).
        
        Args:
            folder_key: Folder key (should end with /)
            
        Returns:
            Folder URL if successful, None otherwise
        """
        if not folder_key.endswith('/'):
            folder_key += '/'
            
        if folder_key in self.folder_cache:
            return f"https://{self.bucket_name}.s3.{self.aws_region}.amazonaws.com/{folder_key}"
            
        if await self.file_exists(folder_key):
            self.folder_cache.add(folder_key)
            return f"https://{self.bucket_name}.s3.{self.aws_region}.amazonaws.com/{folder_key}"
            
        try:
            await self._client.put_object(
                Bucket=self.bucket_name,
                Key=folder_key,
                Body=b''
            )
            self.folder_cache.add(folder_key)
            object_url = f"https://{self.bucket_name}.s3.{self.aws_region}.amazonaws.com/{folder_key}"
            self.logger.info(f"Created folder: {object_url}")
            return object_url
            
        except Exception as e:
            self.logger.error(f"Error creating folder {folder_key}: {e}")
            return None