"""
Async DynamoDB Storage Implementation
"""
from typing import Dict, List, Any, Optional, Union
import aioboto3
from boto3.dynamodb.conditions import Key, Attr
from botocore.exceptions import ClientError
import logging
import asyncio
import random


class AsyncDynamoDBStorage:
    """Async DynamoDB storage implementation"""
    
    def __init__(self, config: Any):
        self.config = config
        self.session = aioboto3.Session()
        self.dynamodb = None
        self.logger = logging.getLogger(__name__)
        
        # Configure retry settings from config or use defaults
        self.default_max_retries = getattr(config, 'dynamodb_max_retries', 15)
        self.default_base_delay = getattr(config, 'dynamodb_base_delay', 1.0)
        self.default_max_delay = getattr(config, 'dynamodb_max_delay', 300.0)  # 5 minutes
        
    async def __aenter__(self):
        """Async context manager entry"""
        import os
        # Get region from environment or config with correct default
        aws_region = (
            os.getenv('AWS_REGION') or 
            os.getenv('LEXGENIUS_AWS_REGION') or
            os.getenv('REGION_NAME') or
            getattr(self.config, 'aws_region', 'us-west-2')
        )
        
        # Get credentials from config if available
        aws_access_key_id = getattr(self.config, 'aws_access_key_id', None)
        aws_secret_access_key = getattr(self.config, 'aws_secret_access_key', None)
        
        # Build resource parameters
        resource_params = {
            'region_name': aws_region,
            'endpoint_url': getattr(self.config, 'dynamodb_endpoint', None)
        }
        
        # Only add credentials if they're provided in config
        if aws_access_key_id and aws_secret_access_key:
            resource_params['aws_access_key_id'] = aws_access_key_id
            resource_params['aws_secret_access_key'] = aws_secret_access_key
        
        self.dynamodb = await self.session.resource('dynamodb', **resource_params).__aenter__()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.dynamodb:
            await self.dynamodb.__aexit__(exc_type, exc_val, exc_tb)
        # aioboto3 sessions don't have a close() method - just reset reference
        if self.session:
            self.session = None
    
    async def get_table(self, table_name: str):
        """Get DynamoDB table reference"""
        if not self.dynamodb:
            # Initialize dynamodb if not already done
            import os
            # Get region from environment or config with correct default
            aws_region = (
                os.getenv('AWS_REGION') or 
                os.getenv('LEXGENIUS_AWS_REGION') or
                os.getenv('REGION_NAME') or
                getattr(self.config, 'aws_region', 'us-west-2')
            )
            
            # Get credentials from config if available
            aws_access_key_id = getattr(self.config, 'aws_access_key_id', None)
            aws_secret_access_key = getattr(self.config, 'aws_secret_access_key', None)
            
            # Build resource parameters
            resource_params = {
                'region_name': aws_region,
                'endpoint_url': getattr(self.config, 'dynamodb_endpoint', None)
            }
            
            # Only add credentials if they're provided in config
            if aws_access_key_id and aws_secret_access_key:
                resource_params['aws_access_key_id'] = aws_access_key_id
                resource_params['aws_secret_access_key'] = aws_secret_access_key
            
            resource = self.session.resource('dynamodb', **resource_params)
            self.dynamodb = await resource.__aenter__()
        table = self.dynamodb.Table(table_name)
        # In aioboto3, Table() returns a coroutine that needs to be awaited
        if hasattr(table, '__await__'):
            return await table
        return table
    
    async def get_item(self, table_name: str, key: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Get single item by key"""
        try:
            table = await self.get_table(table_name)
            response = await table.get_item(Key=key, ConsistentRead=True)
            return response.get('Item')
        except ClientError as e:
            self.logger.error(f"Error getting item: {e}")
            raise
    
    async def put_item(self, table_name: str, item: Dict[str, Any], max_retries: Optional[int] = None) -> None:
        """
        Put single item with exponential backoff for throughput exceeded errors.
        
        Args:
            table_name: Name of the DynamoDB table
            item: Item to put
            max_retries: Maximum number of retry attempts (defaults to config value)
        """
        table = await self.get_table(table_name)
        retries = max_retries if max_retries is not None else self.default_max_retries
        
        # Extract key info for logging
        key_info = ""
        if table_name == "FBAdArchive":
            ad_id = item.get('AdArchiveID', 'Unknown')
            start_date = item.get('StartDate', 'Unknown')
            key_info = f"AdArchiveID={ad_id}, StartDate={start_date}"
        else:
            key_info = f"table={table_name}"
        
        # self.logger.debug(f"Attempting DynamoDB put_item for {key_info}")
        
        async def _put_operation():
            try:
                await table.put_item(Item=item)
                # self.logger.debug(f"Successfully put item: {key_info}")
            except Exception as e:
                self.logger.error(f"Error in put_item operation for {key_info}: {str(e)}")
                if hasattr(e, 'response'):
                    self.logger.error(f"DynamoDB response: {e.response}")
                raise
        
        await self._handle_dynamodb_retries(f"put_item({key_info})", _put_operation, retries)
    
    async def _handle_dynamodb_retries(self, operation_name: str, operation_func, max_retries: int = 15):
        """
        Generic retry handler for DynamoDB operations with exponential backoff.
        
        Args:
            operation_name: Name of the operation for logging
            operation_func: Async function to execute
            max_retries: Maximum number of retry attempts
        """
        for attempt in range(max_retries + 1):
            try:
                return await operation_func()
                
            except ClientError as e:
                error_code = e.response['Error']['Code']
                
                # Handle throughput exceeded errors with exponential backoff
                if error_code == 'ProvisionedThroughputExceededException':
                    if attempt < max_retries:
                        # Exponential backoff with jitter
                        base_delay = min(self.default_max_delay, (self.default_base_delay * (2 ** attempt)))
                        jitter = random.uniform(0.5, 1.5)  # Add randomness
                        delay = base_delay * jitter
                        
                        self.logger.warning(
                            f"DynamoDB throughput exceeded during {operation_name} on attempt {attempt + 1}/{max_retries + 1}. "
                            f"Retrying in {delay:.1f}s..."
                        )
                        await asyncio.sleep(delay)
                        continue
                    else:
                        self.logger.error(
                            f"DynamoDB throughput exceeded during {operation_name}. Failed after {max_retries + 1} attempts. "
                            f"Consider increasing provisioned throughput or using auto-scaling."
                        )
                        raise
                
                # Handle other throttling errors
                elif error_code in ['ThrottlingException', 'RequestLimitExceeded']:
                    if attempt < max_retries:
                        base_delay = min(60, (self.default_base_delay * (2 ** attempt)))
                        delay = base_delay * random.uniform(0.5, 1.5)
                        self.logger.warning(
                            f"DynamoDB throttling during {operation_name} on attempt {attempt + 1}/{max_retries + 1}. "
                            f"Retrying in {delay:.1f}s..."
                        )
                        await asyncio.sleep(delay)
                        continue
                    else:
                        self.logger.error(f"DynamoDB throttling during {operation_name} failed after {max_retries + 1} attempts")
                        raise
                
                # For all other errors, fail immediately
                else:
                    error_message = e.response.get('Error', {}).get('Message', 'Unknown error')
                    self.logger.error(f"DynamoDB error during {operation_name}: Code={error_code}, Message={error_message}")
                    self.logger.error(f"Full error response: {e.response}")
                    raise
    
    async def update_item(
        self, 
        table_name: str, 
        key: Dict[str, Any],
        update_data: Optional[Union[str, Dict[str, Any]]] = None,
        expression_values: Optional[Dict[str, Any]] = None,
        expression_names: Optional[Dict[str, str]] = None,
        max_retries: Optional[int] = None
    ) -> None:
        """
        Update single item with exponential backoff - supports both dict and expression interfaces.
        
        Args:
            table_name: Name of the DynamoDB table
            key: Primary key of the item to update
            update_data: Update data (dict or expression string)
            expression_values: Expression attribute values
            expression_names: Expression attribute names
            max_retries: Maximum number of retry attempts (defaults to config value)
        """
        table = await self.get_table(table_name)
        retries = max_retries if max_retries is not None else self.default_max_retries
        
        # Support both interfaces for backward compatibility
        if isinstance(update_data, dict):
            # Legacy dict interface
            if not update_data:
                return
            
            # Build update expression from dict
            update_parts = []
            expr_vals = {}
            expr_names = {}
            
            for idx, (attr, value) in enumerate(update_data.items()):
                # Skip key attributes
                if attr in key:
                    continue
                
                attr_name = f"#attr{idx}"
                attr_value = f":val{idx}"
                
                update_parts.append(f"{attr_name} = {attr_value}")
                expr_names[attr_name] = attr
                expr_vals[attr_value] = value
            
            if not update_parts:
                return
            
            update_expression = "SET " + ", ".join(update_parts)
            params = {
                'Key': key,
                'UpdateExpression': update_expression,
                'ExpressionAttributeValues': expr_vals,
                'ExpressionAttributeNames': expr_names
            }
        else:
            # New expression interface
            update_expression = update_data
            params = {
                'Key': key,
                'UpdateExpression': update_expression,
                'ExpressionAttributeValues': expression_values or {}
            }
            if expression_names:
                params['ExpressionAttributeNames'] = expression_names
        
        async def _update_operation():
            await table.update_item(**params)
        
        await self._handle_dynamodb_retries("update_item", _update_operation, retries)
    
    async def query(
        self, 
        table_name: str, 
        key_condition: Any,
        index_name: Optional[str] = None,
        filter_expression: Optional[Any] = None,
        projection_expression: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """Query table with pagination support"""
        # self.logger.info(f"[DYNAMODB] Starting query on table: {table_name}, index: {index_name}")
        try:
            table = await self.get_table(table_name)
            
            params = {'KeyConditionExpression': key_condition}
            if index_name:
                params['IndexName'] = index_name
            if filter_expression:
                params['FilterExpression'] = filter_expression
            if projection_expression:
                params['ProjectionExpression'] = projection_expression
            
            
            # Handle pagination like the scan method
            items = []
            response = await table.query(**params)
            items.extend(response.get('Items', []))
            
            # Handle pagination
            while 'LastEvaluatedKey' in response:
                params['ExclusiveStartKey'] = response['LastEvaluatedKey']
                response = await table.query(**params)
                page_items = response.get('Items', [])
                items.extend(page_items)
            
            return items
            
        except ClientError as e:
            self.logger.error(f"Error querying table: {e}")
            raise
        except Exception as e:
            self.logger.error(f"Unexpected error in query: {e}")
            raise
    
    async def scan(
        self, 
        table_name: str,
        filter_expression: Optional[Any] = None
    ) -> List[Dict[str, Any]]:
        """Scan table"""
        try:
            table = await self.get_table(table_name)
            
            params = {}
            if filter_expression:
                params['FilterExpression'] = filter_expression
            
            items = []
            response = await table.scan(**params)
            items.extend(response.get('Items', []))
            
            # Handle pagination
            while 'LastEvaluatedKey' in response:
                params['ExclusiveStartKey'] = response['LastEvaluatedKey']
                response = await table.scan(**params)
                items.extend(response.get('Items', []))
            
            return items
            
        except ClientError as e:
            self.logger.error(f"Error scanning table: {e}")
            raise
    
    async def batch_write_item(self, table_name: str, items: List[Dict[str, Any]], max_retries: int = 15) -> None:
        """
        Write multiple items in batches with exponential backoff for throughput exceeded errors.
        
        Args:
            table_name: Name of the DynamoDB table
            items: List of items to write (max 25 per batch)
            max_retries: Maximum number of retry attempts (default: 15)
        """
        if not items:
            return
        
        # Process items in batches of 25 (DynamoDB limit)
        batch_size = 25
        for i in range(0, len(items), batch_size):
            batch = items[i:i + batch_size]
            
            # Prepare request items
            request_items = {
                table_name: [
                    {'PutRequest': {'Item': item}} for item in batch
                ]
            }
            
            async def _batch_write_operation():
                response = await self.dynamodb.batch_write_item(RequestItems=request_items)
                
                # Handle unprocessed items
                unprocessed = response.get('UnprocessedItems', {})
                while unprocessed:
                    self.logger.warning(f"Retrying {len(unprocessed.get(table_name, []))} unprocessed items...")
                    # Use exponential backoff for unprocessed items
                    await asyncio.sleep(random.uniform(0.1, 0.5))
                    response = await self.dynamodb.batch_write_item(RequestItems=unprocessed)
                    unprocessed = response.get('UnprocessedItems', {})
            
            await self._handle_dynamodb_retries(f"batch_write_item (batch {i//batch_size + 1})", _batch_write_operation, max_retries)
    
    async def put_item_with_condition(
        self, 
        table_name: str, 
        item: Dict[str, Any], 
        condition_expression: str,
        expression_values: Optional[Dict[str, Any]] = None,
        max_retries: int = 15
    ) -> None:
        """
        Put item with condition expression and retry logic.
        
        Args:
            table_name: Name of the DynamoDB table
            item: Item to put
            condition_expression: Condition expression for the put operation
            expression_values: Expression attribute values for condition
            max_retries: Maximum number of retry attempts (default: 15)
        """
        table = await self.get_table(table_name)
        
        async def _conditional_put_operation():
            params = {
                'Item': item,
                'ConditionExpression': condition_expression
            }
            if expression_values:
                params['ExpressionAttributeValues'] = expression_values
            
            await table.put_item(**params)
        
        await self._handle_dynamodb_retries("put_item_with_condition", _conditional_put_operation, max_retries)
    
    async def delete_item(self, table_name: str, key: Dict[str, Any], max_retries: Optional[int] = None) -> None:
        """
        Delete single item by key with exponential backoff for throughput exceeded errors.
        
        Args:
            table_name: Name of the DynamoDB table
            key: Key to identify the item to delete
            max_retries: Maximum number of retry attempts (defaults to config value)
        """
        table = await self.get_table(table_name)
        retries = max_retries if max_retries is not None else self.default_max_retries
        
        async def _delete_operation():
            await table.delete_item(Key=key)
        
        await self._handle_dynamodb_retries("delete_item", _delete_operation, retries)