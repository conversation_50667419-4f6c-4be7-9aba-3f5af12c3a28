"""Logging configuration for Facebook Ads module.

This module provides a centralized logging configuration for the Facebook Ads module,
ensuring logs are saved to the specified location with rich formatting.
"""

import logging
import os
from typing import Dict, Any, Optional

from rich.console import Console
from rich.logging import RichHandler


class FBAdsLogger:
    """Configures and provides logging for the Facebook Ads module."""
    
    @staticmethod
    def setup_logging(config: Dict[str, Any]) -> logging.Logger:
        """Set up logging for the Facebook Ads module.
        
        Args:
            config: The application configuration dictionary
            
        Returns:
            A configured logger instance
        """
        # Get the iso_date from config
        iso_date = config.get('iso_date', 'default')
        
        # Determine log directory
        log_dir = config.get('directories', {}).get(
            'log_dir',
            os.path.join(config.get('DATA_DIR', os.path.join(os.getcwd(), 'data')), f"{iso_date}/logs")
        )
        
        # Ensure log directory exists
        os.makedirs(log_dir, exist_ok=True)
        
        # Set up log file path
        log_file = os.path.join(log_dir, 'fb_ads.log')
        
        # Create console for rich formatting
        console = Console()
        
        # Get the fb_ads logger
        logger = logging.getLogger('src.fb_ads')
        logger.setLevel(logging.DEBUG)
        logger.propagate = False  # Prevent propagation to root logger
        
        # Remove any existing handlers
        for handler in logger.handlers[:]:  
            logger.removeHandler(handler)
        
        # File handler with standard formatting
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(logging.DEBUG)
        file_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(module)s.%(funcName)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        file_handler.setFormatter(file_formatter)
        logger.addHandler(file_handler)
        
        # Console handler with rich formatting
        console_handler = RichHandler(rich_tracebacks=True, console=console, show_path=False)
        console_handler.setLevel(logging.INFO)
        logger.addHandler(console_handler)
        
        # Check if debug mode is enabled in config
        if config.get('development', {}).get('enable_debug_output', False):
            logger.setLevel(logging.DEBUG)
            console_handler.setLevel(logging.DEBUG)
            logger.info("Debug logging enabled due to config setting")
        
        # Also enable debug logging for DynamoDB and repository modules
        dynamodb_logger = logging.getLogger('src.infrastructure.storage.dynamodb_async')
        dynamodb_logger.setLevel(logging.DEBUG)
        dynamodb_handler = logging.FileHandler(log_file)
        dynamodb_handler.setFormatter(file_formatter)
        dynamodb_logger.addHandler(dynamodb_handler)
        
        repo_logger = logging.getLogger('src.repositories.fb_archive_repository')
        repo_logger.setLevel(logging.DEBUG)
        repo_handler = logging.FileHandler(log_file)
        repo_handler.setFormatter(file_formatter)
        repo_logger.addHandler(repo_handler)
        
        logger.info(f"Facebook Ads logging configured. Log file: {log_file}")
        logger.info(f"Logger level: {logger.level}, File handler level: {file_handler.level}")
        return logger