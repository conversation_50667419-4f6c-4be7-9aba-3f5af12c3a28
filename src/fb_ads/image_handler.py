import logging
import os
from io import By<PERSON><PERSON>
from typing import Dict, Any, Optional
import datetime

import requests
from PIL import Image
from botocore.exceptions import Client<PERSON>rror
from requests.exceptions import RequestException
import imagehash

from .session_manager import FacebookSessionManager
from src.infrastructure.storage.s3_async import S3AsyncStorage
from .bandwidth_logger import <PERSON>width<PERSON>ogger, calculate_request_size, calculate_response_size
from .image_utils import FBImageHashManager, calculate_image_hash


class ImageHandler:
    """Handles downloading, processing, and uploading of ad images."""

    def __init__(self, config: Dict[str, Any], s3_manager: S3AsyncStorage, session_manager: FacebookSessionManager):
        self.config = config
        self.s3_manager = s3_manager
        self.session_manager = session_manager
        # Use our custom logging setup for consistent logging
        self._logger = logging.getLogger('src.fb_ads')
        self.temp_download_dir = config.get('temp_image_dir', './temp_images')
        
        # Initialize FBImageHashManager
        self.hash_manager = None
        try:
            # Get table name from config - try both locations
            fb_image_hash_table_name = (
                config.get('fb_image_hash_table_name') or 
                config.get('dynamodb', {}).get('fb_image_hash_table_name')
            )
            if fb_image_hash_table_name:
                self.hash_manager = FBImageHashManager(
                    table_name=fb_image_hash_table_name,
                    config=config,
                    use_local=config.get('use_local_dynamodb', False),
                    local_port=config.get('local_dynamodb_port', 8000)
                )
                self._logger.info(f"Initialized FBImageHashManager with table: {fb_image_hash_table_name}")
            else:
                self._logger.warning("FBImageHashManager not initialized: fb_image_hash_table_name not found in config")
        except Exception as e:
            self._logger.error(f"Failed to initialize FBImageHashManager: {e}")
            self.hash_manager = None

        # Initialize bandwidth logger (use the same instance as session_manager if available)
        # Disable periodic logging by default to avoid potential hanging issues
        if hasattr(session_manager, 'bandwidth_logger'):
            self.bandwidth_logger = session_manager.bandwidth_logger
            self._logger.debug("Using session manager's bandwidth logger instance")
        else:
            bandwidth_config = config.copy() if config else {}
            bandwidth_config['disable_bandwidth_periodic_logging'] = bandwidth_config.get('disable_bandwidth_periodic_logging', True)
            self.bandwidth_logger = BandwidthLogger(bandwidth_config)
            self._logger.info("Initialized new bandwidth logger instance for image handler with periodic logging disabled")

        try:
            os.makedirs(self.temp_download_dir, exist_ok=True)
        except OSError as e:
            self._logger.error(f"Failed to create temporary image directory {self.temp_download_dir}: {e}")

    async def process_and_upload_ad_image(self, ad_archive_id: str, creative_id: str, image_url: Optional[str]) -> tuple[
        Optional[str], Optional[bool]]:
        """
        Checks S3 existence, downloads if needed (with explicit proxy handling),
        processes, uploads, and CONSISTENTLY returns (s3_key|None, s3_exists_bool|None).
        - Returns (key, True) if file exists on S3.
        - Returns (key, False) if file did NOT exist but was successfully downloaded/uploaded.
        - Returns (None, False) if file did NOT exist and download/upload FAILED.
        - Returns (None, None) if S3 check failed or process was skipped early (e.g., bad URL).
        - Returns (None, check_result) if blocked or proxy error during download after check.
        """
        s3_exists_result: Optional[bool] = None  # Variable to store the check result
        local_temp_path = None  # Initialize to prevent potential UnboundLocalError in finally
        logger = self._logger  # Local reference for convenience

        if not image_url or not isinstance(image_url, str) or not image_url.startswith('http'):
            logger.debug(f"IH: No valid image URL provided for ad {ad_archive_id}/{creative_id}. Skipping.")
            return None, None  # Return two values

        s3_prefix = self.config.get('s3_ad_archive_prefix', 'adarchive/fb').strip('/')
        s3_key = f'{s3_prefix}/{ad_archive_id}/{creative_id}.jpg'
        # Define temp path using creative_id to avoid collisions in parallel runs
        local_temp_path = os.path.join(self.temp_download_dir, f'{creative_id}_temp_{os.getpid()}.jpg')

        # --- Step 1: Check S3 Existence ---
        try:
            logger.debug(f"IH_CHECK: Checking S3 existence for key: '{s3_key}'")
            s3_exists_result = await self.s3_manager.file_exists(s3_key)
            logger.debug(f"IH_CHECK: s3_manager.file_exists reported: {s3_exists_result} for key: '{s3_key}'")

            if s3_exists_result is True:
                logger.info(f"IH: Image exists in S3, skipping download: {s3_key}")
                if os.path.exists(local_temp_path):
                    try:
                        os.remove(local_temp_path)
                    except OSError as e:
                        logger.warning(f"IH_CHECK: Could not remove pre-existing temp file {local_temp_path}: {e}")
                return s3_key, True  # Return key and True for exists

            logger.info(
                f"IH: Image not found in S3 (or check failed: {s3_exists_result}), proceeding to download: {s3_key}")

        except ClientError as e:
            error_code = e.response.get('Error', {}).get('Code')
            logger.error(
                f"IH_CHECK: S3 ClientError during existence check for {s3_key} (Code: {error_code}): {e}. Skipping image.")
            return None, None
        except Exception as e_generic_s3_check:
            logger.error(
                f"IH_CHECK: Generic error during S3 file existence check for {s3_key}: {e_generic_s3_check}. Skipping image.")
            return None, None

        # --- PHash handling is done during actual image processing ---
        # If S3 file exists, PHash record should already exist
        # If S3 file doesn't exist, PHash will be calculated after download
        existing_phash_record = None

        # --- Step 2, 3, 4: Download, Process, Upload (Only if S3 check didn't return True) ---
        try:
            session = self.session_manager.get_session()
            if not session:
                logger.error(f"IH_DOWNLOAD: Cannot download image {s3_key}: Session not available.")
                return None, s3_exists_result

            # --- Explicit Proxy Setup for Download ---
            proxies = None
            # Check if proxy use is intended for image downloads
            use_image_proxy = self.config.get('use_proxy',
                                              False)  # Or a more specific config like 'use_image_download_proxy'
            if use_image_proxy and self.session_manager:
                # Get proxy settings dictionary WITH AUTHENTICATION from session manager
                # ASSUMES session_manager has a method returning {'http': '*********************:port', 'https': '...'}
                proxy_settings = self.session_manager.get_current_proxy_settings()
                if proxy_settings:
                    proxies = proxy_settings
                    proxy_host = list(proxies.values())[0].split('@')[-1] if proxies else 'N/A'  # Log host only
                    logger.debug(f"IH_DOWNLOAD: Explicitly using proxy {proxy_host} for image download.")
                else:
                    logger.error(
                        "IH_DOWNLOAD: Proxy enabled but failed to get AUTHENTICATED proxy settings from session manager for image download.")
                    # Fail fast if proxy is required but settings are missing
                    return None, s3_exists_result
            else:
                logger.debug("IH_DOWNLOAD: Proxy not configured or not enabled for image download.")
            # --- End Explicit Proxy Setup ---

            logger.debug(f"IH_DOWNLOAD: Attempting download: {image_url}")
            headers = {'User-Agent': 'Mozilla/5.0'}  # Simple user agent
            timeout = self.config.get('image_download_timeout', 30)

            # Calculate request size before making the request
            request_size = calculate_request_size(headers)

            # *** Use the explicit proxies in the get call ***
            # Assuming session is a requests.Session object (uses 'proxies')
            # If it's aiohttp.ClientSession, use 'proxy=list(proxies.values())[0]'
            response = session.get(
                image_url,
                timeout=timeout,
                stream=True,
                headers=headers,
                proxies=proxies  # Pass the explicit proxy dict here
            )

            # Check for FB blocks... (keep this logic)
            response_text_preview = "[Binary Content]"
            is_likely_text = 'text' in response.headers.get('Content-Type', '').lower()
            if is_likely_text:
                try:
                    response_text_preview = next(response.iter_content(chunk_size=512, decode_unicode=True), "")
                except Exception:
                    pass
            elif response.status_code != 200:
                try:
                    response_text_preview = response.text[:500]
                except Exception:
                    pass

            if response.status_code in [403, 429, 503] or \
                    "Temporarily Blocked" in response_text_preview or \
                    "Try again later" in response_text_preview:
                logger.warning(
                    f"IH_DOWNLOAD: Block/Rate limit ({response.status_code}) downloading image: {image_url} for {s3_key}")
                self.session_manager.handle_block_or_rate_limit("Image download block")
                return None, s3_exists_result

            response.raise_for_status()  # Check for other HTTP errors AFTER block checks

            # --- Process downloaded content (keep this logic) ---
            image_content_buffer = BytesIO()
            content_length = 0
            for chunk in response.iter_content(chunk_size=8192):
                image_content_buffer.write(chunk)
                content_length += len(chunk)
            logger.debug(f"IH_DOWNLOAD: Downloaded {content_length} bytes for {s3_key}")

            # Log bandwidth usage for image download
            content_type = response.headers.get('content-type', '')
            self.bandwidth_logger.log_request(
                url=image_url,
                request_size=request_size,
                response_size=content_length,
                content_type=content_type
            )
            logger.debug(f"Logged bandwidth for image download: {content_length} bytes from {image_url}")

            if content_length == 0:
                logger.warning(f"IH_DOWNLOAD: Downloaded empty content from {image_url} for {s3_key}. Skipping.")
                return None, s3_exists_result

            image_content_buffer.seek(0)
            saved_locally = self._save_image_to_jpeg(image_content_buffer, local_temp_path)
            if not saved_locally:
                logger.error(f"IH_DOWNLOAD: Failed to save/convert image locally: {local_temp_path} for {s3_key}")
                return None, s3_exists_result

            # --- Calculate and store PHash (only if we don't already have one) ---
            if self.hash_manager and saved_locally and not existing_phash_record:
                try:
                    # Read the saved JPEG file for hashing
                    with open(local_temp_path, 'rb') as f:
                        image_bytes = f.read()
                    
                    # Calculate PHash
                    phash_value = calculate_image_hash(image_bytes)
                    if phash_value:
                        phash_str = str(phash_value)
                        timestamp = datetime.datetime.utcnow().isoformat()
                        
                        # Create record for FBImageHash table
                        hash_record = {
                            'PHash': phash_str,  # Partition Key
                            'AdArchiveID': ad_archive_id,  # Sort Key
                            'AdCreativeID': creative_id,
                            'S3Key': s3_key,
                            'LastUpdated': timestamp,
                            'ImageText': ''  # Will be populated later by LLaVA
                        }
                        
                        # Store in DynamoDB
                        if self.hash_manager.put_item(hash_record):
                            logger.info(f"IH_PHASH: Successfully stored new PHash {phash_str} for ad {ad_archive_id}/{creative_id}")
                        else:
                            logger.warning(f"IH_PHASH: Failed to store PHash in DynamoDB for ad {ad_archive_id}/{creative_id}")
                    else:
                        logger.warning(f"IH_PHASH: Failed to calculate PHash for ad {ad_archive_id}/{creative_id}")
                except Exception as e:
                    logger.error(f"IH_PHASH: Error processing PHash for ad {ad_archive_id}/{creative_id}: {e}")
            elif existing_phash_record:
                logger.debug(f"IH_PHASH: Skipping PHash calculation - already exists for ad {ad_archive_id}/{creative_id}")

            # --- Upload to S3 (keep this logic) ---
            logger.debug(f"IH_UPLOAD: Attempting upload to S3 for key: {s3_key} from {local_temp_path}")
            # Assuming s3_manager.upload_file returns bool or similar indicating success
            upload_result = await self.s3_manager.upload_file(local_temp_path, s3_key)
            # Check the status string returned by upload_file
            if isinstance(upload_result, tuple) and len(upload_result) == 2:
                status = upload_result[1]
                if status in ["uploaded", "reuploaded",
                              "already exists"]:  # Consider "already exists" a success here too? Adjust if needed.
                    logger.info(f"IH_UPLOAD: Upload successful (Status: {status}) to S3: {s3_key}")
                    return s3_key, False  # Return key, and False (since it didn't exist initially)
                else:
                    logger.error(f"IH_UPLOAD: Upload FAILED to S3 for key: {s3_key}. Status: {status}")
                    return None, False
            else:  # Handle unexpected return type from upload_file
                logger.error(f"IH_UPLOAD: Upload to S3 for key {s3_key} returned unexpected result: {upload_result}")
                return None, False


        # --- Exception Handling ---
        except requests.exceptions.ProxyError as pe:
            # Specific handling for ProxyError - includes the 407 Auth error
            logger.error(f"IH_DOWNLOAD: ProxyError downloading image {image_url} (S3 key {s3_key}): {pe}",
                         exc_info=False)
            if '407' in str(pe):
                logger.error(
                    "IH_DOWNLOAD: *** Proxy Authentication Required (407). Check credentials used in proxy setup. ***")
            return None, s3_exists_result  # Return None key and the original check result

        except RequestException as e:
            # Handle other network/HTTP errors
            status_code = e.response.status_code if hasattr(e, 'response') and e.response is not None else 'N/A'
            logger.error(
                f"IH_DOWNLOAD: Download failed for image {image_url} (S3 key {s3_key}): {e} (Status: {status_code})",
                exc_info=False)
            if hasattr(e, 'response') and e.response is not None and e.response.status_code in [403, 429]:
                self.session_manager.handle_block_or_rate_limit("Image download RequestException")
            return None, s3_exists_result

        except IOError as e:
            logger.error(f"IH_DOWNLOAD: File/Image processing IO error for {s3_key} ({local_temp_path}): {e}")
            return None, s3_exists_result
        except Exception as e:
            logger.error(f"IH_DOWNLOAD: Unexpected error processing image {s3_key} from {image_url}: {e}",
                         exc_info=self.config.get('verbose', False))
            return None, s3_exists_result
        finally:
            # Ensure temp file is removed
            if local_temp_path and os.path.exists(local_temp_path):
                try:
                    os.remove(local_temp_path)
                    logger.debug(f"IH_CLEANUP: Removed temporary file {local_temp_path}")
                except OSError as e:
                    logger.warning(f"IH_CLEANUP: Could not remove temporary image file {local_temp_path}: {e}")

    # _save_image_to_jpeg remains the same...
    def _save_image_to_jpeg(self, image_content_stream: BytesIO, save_path: str) -> bool:
        # (Keep the implementation from the previous response)
        try:
            with Image.open(image_content_stream) as image:
                image_to_save = None
                self._logger.debug(
                    f"IH_SAVE: Processing image {os.path.basename(save_path)} (Mode: {image.mode}, Size: {image.size})")
                if image.mode == 'P':
                    image = image.convert('RGBA')
                    self._logger.debug(f"IH_SAVE: Converted image {os.path.basename(save_path)} from P to RGBA.")
                if image.mode in ('RGBA', 'LA'):
                    self._logger.debug(
                        f"IH_SAVE: Converting image {os.path.basename(save_path)} from {image.mode} to RGB with white background.")
                    background = Image.new("RGB", image.size, (255, 255, 255))
                    try:
                        mask = image.getchannel('A')
                        background.paste(image, mask=mask)
                    except ValueError:
                        try:  # Try splitting as fallback
                            *_, mask = image.split()
                            background.paste(image, mask=mask)
                        except Exception:
                            self._logger.warning(
                                f"IH_SAVE: Could not get or split alpha channel for {save_path}. Using simple convert.")
                            image_to_save = image.convert('RGB')  # Fallback conversion

                    if image_to_save is None: image_to_save = background
                elif image.mode != 'RGB':
                    self._logger.debug(
                        f"IH_SAVE: Converting image {os.path.basename(save_path)} from {image.mode} to RGB.")
                    image_to_save = image.convert('RGB')
                else:
                    image_to_save = image
                if image_to_save:
                    os.makedirs(os.path.dirname(save_path), exist_ok=True)
                    image_to_save.save(save_path, format='JPEG', quality=85)
                    self._logger.debug(f"IH_SAVE: Saved image {os.path.basename(save_path)} as JPEG to {save_path}")
                    return True
                else:
                    self._logger.error(f"IH_SAVE: Image object became None during conversion for {save_path}")
                    return False
        except Exception as e:
            # Use PIL.UnidentifiedImageError if available/needed, else generic Exception
            # from PIL import UnidentifiedImageError
            # except UnidentifiedImageError: ...
            self._logger.error(f"IH_SAVE: Pillow error processing/saving image to {save_path}: {e}",
                               exc_info=self.config.get('verbose', False))
            return False


    # save_profile_picture remains the same...
    def save_profile_picture(self, law_firm_id: str, image_uri: Optional[str], local_dir: str) -> Optional[bool]:
        # (Keep the implementation from the previous response)
        if not law_firm_id or not image_uri:
            self._logger.warning(f"Missing ID ({law_firm_id}) or URI ({image_uri}) for profile pic.")
            return None
        local_img_dir = os.path.join(local_dir, 'img')
        os.makedirs(local_img_dir, exist_ok=True)
        local_save_path = os.path.join(local_img_dir, f'{law_firm_id}.jpg')
        s3_save_path = f"assets/images/law-firm-logos/{law_firm_id}.jpg"
        s3_exists = None
        uploaded_to_s3 = False  # Track upload status
        try:
            self._logger.debug(f"PROFILE PIC: Checking S3 for {s3_save_path}")
            s3_exists = self.s3_manager.file_exists(s3_save_path)
            self._logger.debug(f"PROFILE PIC: s3_manager.file_exists returned: {s3_exists} for {s3_save_path}")
            if s3_exists:
                self._logger.debug(f"PROFILE PIC: Already exists in S3: {s3_save_path}. Skipping download.")
                return True
            self._logger.info(f"PROFILE PIC: Not found in S3 ({s3_save_path}). Proceeding with download.")
            session = self.session_manager.get_session()
            if not session:
                self._logger.error(
                    f"PROFILE PIC: Cannot download profile pic for {law_firm_id}: Session not available.")
                return False
            self._logger.debug(f"PROFILE PIC: Attempting download via session: {image_uri}")

            # Calculate request size before making the request
            request_size = calculate_request_size({'User-Agent': session.headers.get('User-Agent', 'Mozilla/5.0')})

            response = session.get(image_uri, timeout=15, stream=True)
            response.raise_for_status()

            # Get content and log bandwidth usage
            content = response.content
            content_length = len(content)
            content_type = response.headers.get('content-type', '')

            self.bandwidth_logger.log_request(
                url=image_uri,
                request_size=request_size,
                response_size=content_length,
                content_type=content_type
            )
            self._logger.debug(f"Logged bandwidth for profile picture download: {content_length} bytes from {image_uri}")

            image_content_buffer = BytesIO(content)
            saved_locally = self._save_image_to_jpeg(image_content_buffer, local_save_path)
            if not saved_locally:
                self._logger.error(f"PROFILE PIC: Failed to save profile pic locally to {local_save_path}")
                return False
            self._logger.info(f"PROFILE PIC: Saved locally: {local_save_path}")
            self._logger.info(f"PROFILE PIC: Attempting upload to S3: {s3_save_path}")
            uploaded_to_s3 = self.s3_manager.upload_file(local_save_path, s3_save_path)
            if uploaded_to_s3:
                self._logger.info(f"PROFILE PIC: Uploaded to S3: {s3_save_path}")
                return True
            else:
                self._logger.error(f"PROFILE PIC: Failed to upload profile pic to S3: {s3_save_path}")
                return False
        except RequestException as e:
            err_msg = f"PROFILE PIC: Failed to download profile pic for {law_firm_id} from {image_uri}: {e}"
            if e.response is not None: err_msg += f" (Status: {e.response.status_code})"
            self._logger.error(err_msg)
            return False
        except ClientError as e:
            error_code = e.response.get('Error', {}).get('Code')
            status_code = e.response.get('ResponseMetadata', {}).get('HTTPStatusCode')
            self._logger.error(
                f"PROFILE PIC: S3 ClientError during processing for {law_firm_id} (Code: {error_code}, Status: {status_code}): {e}")
            return False
        except IOError as e:
            self._logger.error(f"PROFILE PIC: IOError saving profile picture {local_save_path} for {law_firm_id}: {e}")
            return False
        except Exception as e:
            self._logger.error(f"PROFILE PIC: Unexpected error saving profile picture for {law_firm_id}: {e}",
                               exc_info=self.config.get('verbose', False))
            return False
        finally:
            if os.path.exists(local_save_path):
                # Remove if S3 check was True OR if upload failed after local save
                should_remove = s3_exists is True or not uploaded_to_s3
                if should_remove:
                    try:
                        os.remove(local_save_path)
                        self._logger.debug(f"PROFILE PIC: Removed local file {local_save_path}")
                    except OSError as e:
                        self._logger.warning(
                            f"PROFILE PIC: Could not remove local profile pic file {local_save_path}: {e}")
