"""
Bandwidth Logger for Facebook Ads module.

This module provides utilities for tracking and logging bandwidth usage
when making HTTP requests to Facebook Ads API and downloading images.
"""

import logging
import time
import threading
from typing import Dict, Any, Optional
from threading import Lock, RLock
import traceback

class BandwidthLogger:
    """
    Tracks and logs bandwidth usage for HTTP requests.
    Thread-safe singleton to ensure consistent tracking across components.
    """
    _instance = None
    _lock = RLock()  # Use RLock instead of Lock to allow reentrant locking
    _instance_lock = Lock()  # Separate lock for instance creation

    # Constants
    LOCK_TIMEOUT = 2.0  # 2 seconds timeout for acquiring locks

    def __new__(cls, *args, **kwargs):
        # Use a separate lock for instance creation to avoid deadlocks
        if cls._instance is None:
            with cls._instance_lock:
                if cls._instance is None:  # Double-check pattern
                    cls._instance = super(BandwidthLogger, cls).__new__(cls)
                    cls._instance._initialized = False
        return cls._instance

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize the bandwidth logger with configuration."""
        # Use a lock to ensure thread-safe initialization
        try:
            # Only initialize once (singleton pattern)
            if hasattr(self, '_initialized') and self._initialized:
                return

            # Acquire the lock with a timeout for initialization
            if not hasattr(self, '_init_lock'):
                self._init_lock = Lock()

            if not self._init_lock.acquire(timeout=self.LOCK_TIMEOUT):
                # If we can't acquire the lock, assume another thread is initializing
                # and just return
                return

            try:
                # Double-check pattern after acquiring the lock
                if hasattr(self, '_initialized') and self._initialized:
                    return

                # Set up logger using our custom logging setup
                self._logger = logging.getLogger('src.fb_ads')
                self.config = config or {}

                # Initialize counters
                self.total_bytes_downloaded = 0
                self.total_bytes_uploaded = 0
                self.request_count = 0
                self.image_bytes = 0
                self.html_bytes = 0
                self.api_bytes = 0
                self.other_bytes = 0

                # Track by domain
                self.domain_stats = {}

                # Timestamps
                self.start_time = time.time()
                self.last_log_time = self.start_time

                # Logging frequency (in seconds)
                self.log_frequency = self.config.get('bandwidth_log_frequency', 300)  # 5 minutes default

                # Disable periodic logging if requested
                self.disable_periodic_logging = self.config.get('disable_bandwidth_periodic_logging', False)

                self._initialized = True
                self._logger.info("Bandwidth logger initialized")
            finally:
                # Always release the initialization lock
                self._init_lock.release()
        except Exception as e:
            # Catch any exceptions during initialization to prevent hanging
            try:
                logger = logging.getLogger(__name__)
                logger.error(f"Error initializing BandwidthLogger: {e}\n{traceback.format_exc()}")
            except:
                # If even basic logging fails, print to stderr as a last resort
                import sys
                print(f"CRITICAL ERROR initializing BandwidthLogger: {e}", file=sys.stderr)

    def log_request(self, url: str, request_size: int, response_size: int,
                   content_type: Optional[str] = None):
        """
        Log bandwidth usage for a single request.

        Args:
            url: The URL that was requested
            request_size: Size of the request in bytes
            response_size: Size of the response in bytes
            content_type: Content type of the response (if available)
        """
        # Use a timeout to prevent hanging if the lock can't be acquired
        lock_acquired = False
        try:
            # Try to acquire the lock with a timeout
            lock_acquired = self._lock.acquire(timeout=self.LOCK_TIMEOUT)
            if not lock_acquired:
                self._logger.warning(f"Could not acquire lock for bandwidth logging after {self.LOCK_TIMEOUT}s timeout. Skipping log for {url}")
                return

            # Validate inputs to prevent errors
            if not isinstance(request_size, (int, float)) or request_size < 0:
                request_size = 0
            if not isinstance(response_size, (int, float)) or response_size < 0:
                response_size = 0

            # Update counters
            self.total_bytes_uploaded += request_size
            self.total_bytes_downloaded += response_size
            self.request_count += 1

            # Categorize by content type - use a try block to catch any unexpected errors
            try:
                if content_type:
                    content_type_lower = content_type.lower()
                    if 'image' in content_type_lower:
                        self.image_bytes += response_size
                    elif 'html' in content_type_lower:
                        self.html_bytes += response_size
                    elif 'json' in content_type_lower or 'application' in content_type_lower:
                        self.api_bytes += response_size
                    else:
                        self.other_bytes += response_size
                else:
                    # Try to guess based on URL or extension
                    url_lower = url.lower() if url else ''
                    if any(ext in url_lower for ext in ['.jpg', '.jpeg', '.png', '.gif']):
                        self.image_bytes += response_size
                    elif 'graphql' in url_lower or 'api' in url_lower:
                        self.api_bytes += response_size
                    else:
                        self.other_bytes += response_size
            except Exception as e:
                # If categorization fails, just count it as 'other'
                self.other_bytes += response_size
                self._logger.warning(f"Error categorizing content type: {e}")

            # Track by domain
            try:
                from urllib.parse import urlparse
                domain = urlparse(url).netloc if url else 'unknown'
                if domain not in self.domain_stats:
                    self.domain_stats[domain] = {
                        'bytes_downloaded': 0,
                        'bytes_uploaded': 0,
                        'request_count': 0
                    }
                self.domain_stats[domain]['bytes_downloaded'] += response_size
                self.domain_stats[domain]['bytes_uploaded'] += request_size
                self.domain_stats[domain]['request_count'] += 1
            except Exception as e:
                self._logger.warning(f"Error parsing domain from URL {url}: {e}")

            # Log detailed info at debug level - only if debug logging is enabled
            if self._logger.isEnabledFor(logging.DEBUG):
                self._logger.debug(
                    f"Bandwidth: {url} - Uploaded: {request_size} bytes, "
                    f"Downloaded: {response_size} bytes, "
                    f"Content-Type: {content_type or 'unknown'}"
                )

            # Check if it's time for a periodic summary - use a separate try block
            try:
                # Skip periodic logging if disabled
                if not getattr(self, 'disable_periodic_logging', False):
                    current_time = time.time()
                    if current_time - self.last_log_time > self.log_frequency:
                        # Don't call log_summary() with the lock held - schedule it for later
                        self.last_log_time = current_time
                        # Use a thread to log the summary to avoid blocking
                        threading.Thread(target=self.log_summary, daemon=True).start()
            except Exception as e:
                self._logger.warning(f"Error checking periodic summary time: {e}")
        except Exception as e:
            self._logger.error(f"Unexpected error in log_request: {e}\n{traceback.format_exc()}")
        finally:
            # Always release the lock if we acquired it
            if lock_acquired:
                try:
                    self._lock.release()
                except Exception as e:
                    self._logger.error(f"Error releasing lock: {e}")

    def log_summary(self):
        """Log a summary of bandwidth usage."""
        # Use a timeout to prevent hanging if the lock can't be acquired
        lock_acquired = False
        try:
            # Try to acquire the lock with a timeout
            lock_acquired = self._lock.acquire(timeout=self.LOCK_TIMEOUT)
            if not lock_acquired:
                self._logger.warning(f"Could not acquire lock for bandwidth summary after {self.LOCK_TIMEOUT}s timeout. Skipping summary.")
                return

            try:
                elapsed_time = time.time() - self.start_time
                hours = int(elapsed_time // 3600)
                minutes = int((elapsed_time % 3600) // 60)
                seconds = int(elapsed_time % 60)

                # Safely calculate MB values with error handling
                try:
                    total_mb_down = self.total_bytes_downloaded / (1024 * 1024)
                    total_mb_up = self.total_bytes_uploaded / (1024 * 1024)
                    image_mb = self.image_bytes / (1024 * 1024)
                    html_mb = self.html_bytes / (1024 * 1024)
                    api_mb = self.api_bytes / (1024 * 1024)
                    other_mb = self.other_bytes / (1024 * 1024)
                except Exception as e:
                    self._logger.warning(f"Error calculating bandwidth values: {e}")
                    total_mb_down = total_mb_up = image_mb = html_mb = api_mb = other_mb = 0.0

                # Log the summary
                self._logger.info(
                    f"FB_ADS BANDWIDTH SUMMARY (after {hours:02d}:{minutes:02d}:{seconds:02d}):\n"
                    f"  Total Downloaded: {total_mb_down:.2f} MB\n"
                    f"  Total Uploaded: {total_mb_up:.2f} MB\n"
                    f"  Total Requests: {self.request_count}\n"
                    f"  By Content Type:\n"
                    f"    Images: {image_mb:.2f} MB\n"
                    f"    HTML: {html_mb:.2f} MB\n"
                    f"    API/JSON: {api_mb:.2f} MB\n"
                    f"    Other: {other_mb:.2f} MB"
                )

                # Log top domains by bandwidth - in a separate try block
                try:
                    if self.domain_stats:
                        self._logger.info("FB_ADS BANDWIDTH BY DOMAIN:")
                        sorted_domains = sorted(
                            self.domain_stats.items(),
                            key=lambda x: x[1]['bytes_downloaded'],
                            reverse=True
                        )
                        for domain, stats in sorted_domains[:5]:  # Top 5 domains
                            domain_mb = stats['bytes_downloaded'] / (1024 * 1024)
                            self._logger.info(
                                f"  {domain}: {domain_mb:.2f} MB, "
                                f"{stats['request_count']} requests"
                            )
                except Exception as e:
                    self._logger.warning(f"Error logging domain statistics: {e}")
            except Exception as e:
                self._logger.error(f"Error generating bandwidth summary: {e}\n{traceback.format_exc()}")
        except Exception as e:
            self._logger.error(f"Unexpected error in log_summary: {e}\n{traceback.format_exc()}")
        finally:
            # Always release the lock if we acquired it
            if lock_acquired:
                try:
                    self._lock.release()
                except Exception as e:
                    self._logger.error(f"Error releasing lock in log_summary: {e}")

    def get_total_bandwidth(self):
        """Return total bandwidth usage in bytes."""
        # Use a timeout to prevent hanging if the lock can't be acquired
        lock_acquired = False
        try:
            # Try to acquire the lock with a timeout
            lock_acquired = self._lock.acquire(timeout=self.LOCK_TIMEOUT)
            if not lock_acquired:
                self._logger.warning(f"Could not acquire lock for get_total_bandwidth after {self.LOCK_TIMEOUT}s timeout. Returning default values.")
                # Return default values if we can't acquire the lock
                return {
                    'downloaded': 0,
                    'uploaded': 0,
                    'requests': 0,
                    'images': 0,
                    'html': 0,
                    'api': 0,
                    'other': 0,
                    'elapsed_seconds': 0
                }

            # Create a copy of the data to avoid potential race conditions
            result = {
                'downloaded': self.total_bytes_downloaded,
                'uploaded': self.total_bytes_uploaded,
                'requests': self.request_count,
                'images': self.image_bytes,
                'html': self.html_bytes,
                'api': self.api_bytes,
                'other': self.other_bytes,
                'elapsed_seconds': time.time() - self.start_time
            }
            return result
        except Exception as e:
            self._logger.error(f"Error in get_total_bandwidth: {e}\n{traceback.format_exc()}")
            # Return default values on error
            return {
                'downloaded': 0,
                'uploaded': 0,
                'requests': 0,
                'images': 0,
                'html': 0,
                'api': 0,
                'other': 0,
                'elapsed_seconds': 0
            }
        finally:
            # Always release the lock if we acquired it
            if lock_acquired:
                try:
                    self._lock.release()
                except Exception as e:
                    self._logger.error(f"Error releasing lock in get_total_bandwidth: {e}")

    def save_summary(self):
        """Save summary to log - alias for log_summary for backward compatibility."""
        try:
            self.log_summary()
        except Exception as e:
            self._logger.error(f"Error in save_summary: {e}")

# Helper function to calculate request and response sizes
def calculate_request_size(headers, body=None):
    """Calculate the size of an HTTP request in bytes."""
    try:
        size = 0

        # Add headers size
        if headers:
            try:
                for name, value in headers.items():
                    try:
                        size += len(str(name)) + len(str(value)) + 4  # 4 for ': ' and '\r\n'
                    except Exception:
                        # Skip this header if there's an error
                        pass
            except Exception:
                # If headers can't be iterated, just estimate
                size += 200  # Rough estimate for headers

        # Add body size
        if body:
            try:
                if isinstance(body, str):
                    size += len(body.encode('utf-8'))
                elif isinstance(body, bytes):
                    size += len(body)
                elif hasattr(body, 'tell') and hasattr(body, 'seek'):
                    # Handle file-like objects
                    try:
                        current_pos = body.tell()
                        body.seek(0, 2)  # Seek to end
                        size += body.tell()
                        body.seek(current_pos)  # Restore position
                    except Exception:
                        # If seeking fails, just estimate
                        size += 1024  # Rough estimate
                elif isinstance(body, dict):
                    # Handle dict bodies (common for JSON)
                    try:
                        import json
                        size += len(json.dumps(body).encode('utf-8'))
                    except Exception:
                        # If JSON conversion fails, just estimate
                        size += len(str(body).encode('utf-8', errors='ignore'))
                else:
                    # For other types, convert to string
                    size += len(str(body).encode('utf-8', errors='ignore'))
            except Exception:
                # If body size calculation fails, just estimate
                size += 1024  # Rough estimate

        return max(0, size)  # Ensure non-negative
    except Exception:
        # If anything goes wrong, return a safe default
        return 100  # Minimal default size

def calculate_response_size(response):
    """
    Calculate the size of an HTTP response in bytes.
    Works with requests.Response objects.
    """
    try:
        if response is None:
            return 0

        size = 0

        # Add headers size
        if hasattr(response, 'headers'):
            try:
                for name, value in response.headers.items():
                    try:
                        size += len(str(name)) + len(str(value)) + 4  # 4 for ': ' and '\r\n'
                    except Exception:
                        # Skip this header if there's an error
                        pass
            except Exception:
                # If headers can't be iterated, just estimate
                size += 200  # Rough estimate for headers

        # Add content size
        try:
            if hasattr(response, 'content') and response.content is not None:
                size += len(response.content)
            elif hasattr(response, 'text') and response.text is not None:
                size += len(response.text.encode('utf-8', errors='ignore'))
            elif isinstance(response, (bytes, bytearray)):
                size += len(response)
            elif isinstance(response, str):
                size += len(response.encode('utf-8', errors='ignore'))
        except Exception:
            # If content size calculation fails, just estimate
            size += 1024  # Rough estimate

        return max(0, size)  # Ensure non-negative
    except Exception:
        # If anything goes wrong, return a safe default
        return 100  # Minimal default size
