"""
Concurrent Workflow Service for FB Ads Processing

Provides high-performance concurrent processing capabilities while maintaining
session management and error handling from the base WorkflowService.
"""
import asyncio
import logging
import random
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from concurrent.futures import <PERSON>hr<PERSON><PERSON>ool<PERSON>xecutor
from datetime import datetime

from .workflow_service import WorkflowService


@dataclass
class ProcessingBatch:
    """Represents a batch of firms to process concurrently."""
    firms: List[Dict[str, Any]]
    batch_id: int
    total_batches: int


@dataclass
class ProcessingResult:
    """Result of processing a single firm."""
    firm_id: str
    firm_name: str
    success: bool
    error_msg: Optional[str] = None
    ads_fetched: int = 0
    items_processed: int = 0
    items_updated: int = 0
    items_added: int = 0
    processing_time: float = 0.0


class ConcurrentWorkflowService(WorkflowService):
    """
    Enhanced workflow service with concurrent processing capabilities.
    
    Features:
    - Concurrent processing of multiple firms
    - Intelligent batching to prevent session overload
    - Session management across concurrent operations
    - Performance monitoring and optimization
    - Graceful degradation on errors
    """

    def __init__(self, config: Dict[str, Any], logger: logging.Logger):
        """Initialize concurrent workflow service."""
        super().__init__(config, logger)
        
        # Concurrent processing configuration
        self.max_concurrent_firms = config.get('max_concurrent_firms', 3)
        self.batch_size = config.get('concurrent_batch_size', 5) 
        self.session_pool_size = config.get('session_pool_size', 2)
        self.enable_concurrent_processing = config.get('enable_concurrent_processing', True)
        
        # Performance tracking
        self.processing_stats = {
            'total_firms': 0,
            'successful_firms': 0,
            'failed_firms': 0,
            'total_processing_time': 0.0,
            'concurrent_batches': 0,
            'avg_batch_time': 0.0
        }
        
        # Session pool for concurrent operations
        self._session_pool = []
        self._session_pool_initialized = False
        
        self._logger.info(f"Concurrent processing enabled: {self.enable_concurrent_processing}")
        if self.enable_concurrent_processing:
            self._logger.info(f"Max concurrent firms: {self.max_concurrent_firms}")
            self._logger.info(f"Batch size: {self.batch_size}")

    async def _initialize_session_pool(self):
        """Initialize a pool of sessions for concurrent processing."""
        if self._session_pool_initialized:
            return
        
        self._logger.info(f"Initializing session pool with {self.session_pool_size} sessions")
        
        for i in range(self.session_pool_size):
            try:
                # Create additional session managers for concurrent use
                session_config = self.config.copy()
                session_config['session_id'] = f"concurrent_{i}"
                
                from ..session_manager import FacebookSessionManager
                session_manager = FacebookSessionManager(session_config)
                
                # Initialize session
                if await session_manager.create_new_session():
                    self._session_pool.append(session_manager)
                    self._logger.debug(f"Session {i} initialized successfully")
                else:
                    self._logger.warning(f"Failed to initialize session {i}")
                    
            except Exception as e:
                self._logger.error(f"Error initializing session {i}: {e}")
        
        self._session_pool_initialized = True
        self._logger.info(f"Session pool initialized with {len(self._session_pool)} sessions")

    async def _get_available_session(self):
        """Get an available session from the pool."""
        if not self._session_pool:
            # Fallback to main session manager
            return self.session_manager
        
        # Simple round-robin selection
        return random.choice(self._session_pool)

    async def run_full_scrape_workflow_concurrent(self) -> bool:
        """Run full scrape workflow with concurrent processing."""
        if not self.enable_concurrent_processing:
            self._logger.info("Concurrent processing disabled, using sequential workflow")
            return await super().run_full_scrape_workflow()
        
        self._logger.info("Starting concurrent full scrape workflow")
        start_time = datetime.now()
        
        try:
            # Clear errors and initialize
            self.error_handling_service.clear_errors()
            
            # Create initial session
            if not await self.session_manager.create_new_session():
                self._logger.error("Failed to create initial session")
                return False
            
            # Initialize session pool for concurrent operations
            await self._initialize_session_pool()
            
            # Get filtered law firms
            law_firms_to_process = await self._get_filtered_law_firms()
            if not law_firms_to_process:
                self._logger.info("No firms eligible for processing")
                self.error_handling_service.summarize_errors()
                return True
            
            # Process firms concurrently in batches
            success = await self._process_firms_concurrent_batches(law_firms_to_process)
            
            # Generate summary
            self.error_handling_service.summarize_errors()
            self._log_processing_stats(datetime.now() - start_time)
            
            return success
            
        except Exception as e:
            self._logger.error(f"Error in concurrent workflow: {e}")
            return False
        finally:
            await self._cleanup_session_pool()

    async def _process_firms_concurrent_batches(self, firms_to_process: List[Dict[str, Any]]) -> bool:
        """Process firms in concurrent batches."""
        total_firms = len(firms_to_process)
        self.processing_stats['total_firms'] = total_firms
        
        # Create batches
        batches = self._create_processing_batches(firms_to_process)
        self._logger.info(f"Created {len(batches)} batches for {total_firms} firms")
        
        overall_success = True
        
        with self.progress:
            main_task_id = self.progress.add_task(
                f"[cyan]Processing {total_firms} firms in {len(batches)} concurrent batches...", 
                total=total_firms
            )
            
            for batch in batches:
                batch_start_time = datetime.now()
                
                self._logger.info(f"Processing batch {batch.batch_id}/{batch.total_batches} with {len(batch.firms)} firms")
                
                # Process batch concurrently
                batch_success = await self._process_single_batch(batch, main_task_id)
                if not batch_success:
                    overall_success = False
                
                # Update stats
                batch_time = (datetime.now() - batch_start_time).total_seconds()
                self.processing_stats['concurrent_batches'] += 1
                self.processing_stats['total_processing_time'] += batch_time
                
                # Session refresh between batches
                if batch.batch_id < batch.total_batches:  # Not the last batch
                    await self._refresh_session_pool()
                    
                    # Pause between batches
                    pause_time = random.uniform(2.0, 5.0)
                    await asyncio.sleep(pause_time)
        
        return overall_success

    def _create_processing_batches(self, firms: List[Dict[str, Any]]) -> List[ProcessingBatch]:
        """Create optimized batches for concurrent processing."""
        batches = []
        total_batches = (len(firms) + self.batch_size - 1) // self.batch_size
        
        for i in range(0, len(firms), self.batch_size):
            batch_firms = firms[i:i + self.batch_size]
            batch_id = (i // self.batch_size) + 1
            
            batch = ProcessingBatch(
                firms=batch_firms,
                batch_id=batch_id,
                total_batches=total_batches
            )
            batches.append(batch)
        
        return batches

    async def _process_single_batch(self, batch: ProcessingBatch, main_task_id) -> bool:
        """Process a single batch of firms concurrently."""
        semaphore = asyncio.Semaphore(self.max_concurrent_firms)
        batch_success = True
        
        async def process_firm_with_semaphore(firm_data: Dict[str, Any]) -> ProcessingResult:
            async with semaphore:
                return await self._process_single_firm_concurrent(firm_data)
        
        # Create tasks for concurrent processing
        tasks = [
            process_firm_with_semaphore(firm_data)
            for firm_data in batch.firms
        ]
        
        # Execute tasks concurrently
        try:
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Process results
            for result in results:
                if isinstance(result, Exception):
                    self._logger.error(f"Concurrent processing error: {result}")
                    batch_success = False
                elif isinstance(result, ProcessingResult):
                    if result.success:
                        self.processing_stats['successful_firms'] += 1
                    else:
                        self.processing_stats['failed_firms'] += 1
                        batch_success = False
                    
                    # Update main progress
                    self.progress.update(main_task_id, advance=1)
                
        except Exception as e:
            self._logger.error(f"Batch processing error: {e}")
            batch_success = False
        
        return batch_success

    async def _process_single_firm_concurrent(self, firm_data: Dict[str, Any]) -> ProcessingResult:
        """Process a single firm with concurrent-safe operations."""
        firm_id = firm_data.get('ID', 'Unknown')
        firm_name = firm_data.get('Name', 'Unknown')
        start_time = datetime.now()
        
        try:
            # Get a session from the pool
            session_manager = await self._get_available_session()
            
            # Validate firm data
            if not self.data_validation_service.validate_firm_data(firm_data):
                return ProcessingResult(
                    firm_id=firm_id,
                    firm_name=firm_name,
                    success=False,
                    error_msg="Invalid firm data"
                )
            
            # Process with the selected session
            success, error_msg, ads_fetched, items_processed, items_updated, items_added = await self.ad_processing_service.process_single_firm_core(
                firm_data, update_db=True
            )
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            if success:
                self._logger.debug(f"Concurrent processing SUCCEEDED for {firm_name} ({processing_time:.2f}s)")
            else:
                self._logger.warning(f"Concurrent processing FAILED for {firm_name}: {error_msg}")
                self.error_handling_service.add_firm_error(firm_id, firm_name, error_msg)
            
            return ProcessingResult(
                firm_id=firm_id,
                firm_name=firm_name,
                success=success,
                error_msg=error_msg,
                ads_fetched=ads_fetched,
                items_processed=items_processed,
                items_updated=items_updated,
                items_added=items_added,
                processing_time=processing_time
            )
            
        except Exception as e:
            processing_time = (datetime.now() - start_time).total_seconds()
            error_msg = f"Concurrent processing exception: {str(e)[:200]}"
            
            self._logger.error(f"Concurrent processing error for {firm_name}: {e}")
            self.error_handling_service.add_firm_error(firm_id, firm_name, error_msg)
            
            return ProcessingResult(
                firm_id=firm_id,
                firm_name=firm_name,
                success=False,
                error_msg=error_msg,
                processing_time=processing_time
            )

    async def _refresh_session_pool(self):
        """Refresh sessions in the pool."""
        if not self._session_pool:
            return
        
        self._logger.debug("Refreshing session pool")
        
        refresh_tasks = []
        for session_manager in self._session_pool:
            refresh_tasks.append(session_manager.create_new_session())
        
        try:
            results = await asyncio.gather(*refresh_tasks, return_exceptions=True)
            successful_refreshes = sum(1 for r in results if r is True)
            self._logger.debug(f"Refreshed {successful_refreshes}/{len(self._session_pool)} pool sessions")
        except Exception as e:
            self._logger.error(f"Error refreshing session pool: {e}")

    async def _cleanup_session_pool(self):
        """Clean up session pool resources."""
        if not self._session_pool:
            return
        
        self._logger.info("Cleaning up session pool")
        
        cleanup_tasks = []
        for session_manager in self._session_pool:
            if hasattr(session_manager, 'cleanup'):
                cleanup_tasks.append(session_manager.cleanup())
        
        if cleanup_tasks:
            try:
                await asyncio.gather(*cleanup_tasks, return_exceptions=True)
            except Exception as e:
                self._logger.error(f"Error cleaning up session pool: {e}")
        
        self._session_pool.clear()
        self._session_pool_initialized = False

    def _log_processing_stats(self, total_time):
        """Log comprehensive processing statistics."""
        stats = self.processing_stats
        
        if stats['concurrent_batches'] > 0:
            stats['avg_batch_time'] = stats['total_processing_time'] / stats['concurrent_batches']
        
        success_rate = (stats['successful_firms'] / max(stats['total_firms'], 1)) * 100
        
        self._logger.info("=== Concurrent Processing Statistics ===")
        self._logger.info(f"Total firms processed: {stats['total_firms']}")
        self._logger.info(f"Successful: {stats['successful_firms']}")
        self._logger.info(f"Failed: {stats['failed_firms']}")
        self._logger.info(f"Success rate: {success_rate:.1f}%")
        self._logger.info(f"Total processing time: {total_time.total_seconds():.2f}s")
        self._logger.info(f"Concurrent batches: {stats['concurrent_batches']}")
        self._logger.info(f"Average batch time: {stats['avg_batch_time']:.2f}s")
        
        if stats['total_firms'] > 0:
            throughput = stats['total_firms'] / total_time.total_seconds()
            self._logger.info(f"Processing throughput: {throughput:.2f} firms/second")

    # Override methods to use concurrent processing when enabled
    
    async def run_full_scrape_workflow(self) -> bool:
        """Run full scrape workflow (concurrent if enabled)."""
        if self.enable_concurrent_processing:
            return await self.run_full_scrape_workflow_concurrent()
        else:
            return await super().run_full_scrape_workflow()

    async def run_ignore_list_workflow(self) -> bool:
        """Run ignore list workflow with concurrent processing."""
        if not self.enable_concurrent_processing:
            return await super().run_ignore_list_workflow()
        
        self._logger.info("Starting concurrent ignore list processing")
        
        try:
            # Clear errors and initialize
            self.error_handling_service.clear_errors()
            
            # Create initial session
            if not await self.session_manager.create_new_session():
                self._logger.error("Failed to create initial session")
                return False
            
            # Get ignore list firms
            ignore_firm_ids = self.data_validation_service.get_ignore_list_firms()
            if not ignore_firm_ids:
                self._logger.info("No firms in ignore list")
                return True
            
            # Fetch firm data
            ignore_list_firms = await self._fetch_ignore_list_firms(ignore_firm_ids)
            if not ignore_list_firms:
                self._logger.info("No ignore list firms found in database")
                self.error_handling_service.summarize_errors()
                return True
            
            # Adjust date range for ignore list processing
            original_start, original_end = self._adjust_date_range_for_ignore_list()
            
            try:
                # Process with concurrent batching
                await self._initialize_session_pool()
                success = await self._process_firms_concurrent_batches(ignore_list_firms)
                
                self.error_handling_service.summarize_errors()
                return success
                
            finally:
                # Restore original date range
                self._restore_date_range(original_start, original_end)
                await self._cleanup_session_pool()
                
        except Exception as e:
            self._logger.error(f"Error in concurrent ignore list workflow: {e}")
            return False

    def get_processing_stats(self) -> Dict[str, Any]:
        """Get current processing statistics."""
        return self.processing_stats.copy()

    def reset_processing_stats(self):
        """Reset processing statistics."""
        self.processing_stats = {
            'total_firms': 0,
            'successful_firms': 0,
            'failed_firms': 0,
            'total_processing_time': 0.0,
            'concurrent_batches': 0,
            'avg_batch_time': 0.0
        }