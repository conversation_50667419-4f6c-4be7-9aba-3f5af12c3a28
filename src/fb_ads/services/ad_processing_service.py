"""
Ad Processing Service

Handles core ad processing functionality including:
- Fetching ads from Facebook API
- Processing individual firms and ads
- Database operations (read/write)
- Batch processing and validation
"""
import json
import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from boto3.dynamodb.conditions import Key

from src.utils.date import DateUtils


class AdProcessingService:
    """Service responsible for core ad processing operations."""
    
    KEYS_TO_REMOVE_BEFORE_SAVE = [
        'original_image_url', 'resized_image_url', 'video_preview_image_url',
        'video_hd_url', 'video_sd_url', '_temp_source_image_url', '_s3_check_result',
        'ImageText'  # Remove ImageText to save space - it's now stored in FBImageHash table
    ]
    
    def __init__(self, config: Dict[str, Any], logger: logging.Logger):
        """Initialize the ad processing service."""
        self.config = config
        self._logger = logger
        self.end_date_iso = config.get('iso_date')
        self.start_date_iso = DateUtils.get_date_before_n_days(
            config.get('default_date_range_days', 14), self.end_date_iso
        )
        
        # Repository references (set by orchestrator)
        self.law_firms_repo = None
        self.fb_archive_repo = None
        
        # Legacy DB managers (for fallback)
        self.law_firm_db = None
        self.fb_ad_db = None
        
        # External dependencies (injected)
        self.api_client = None
        self.ad_processor = None
        self.session_manager = None
    
    def set_repositories(self, law_firms_repo, fb_archive_repo):
        """Set async repository instances."""
        self.law_firms_repo = law_firms_repo
        self.fb_archive_repo = fb_archive_repo
    
    def set_legacy_managers(self, law_firm_db, fb_ad_db):
        """Set legacy database managers for fallback."""
        self.law_firm_db = law_firm_db
        self.fb_ad_db = fb_ad_db
    
    def set_external_dependencies(self, api_client, ad_processor, session_manager):
        """Set external service dependencies."""
        self.api_client = api_client
        self.ad_processor = ad_processor
        self.session_manager = session_manager
    
    async def get_all_law_firms_async(self) -> List[Dict[str, Any]]:
        """Fetch all law firm records using async repositories."""
        projection = "ID, #nm, AdArchiveLastUpdated, NumAds"
        expression_names = {"#nm": "Name"}
        
        all_firms = await self.law_firms_repo.scan_all(
            projection_expression=projection,
            expression_attribute_names=expression_names
        )
        self._logger.info(f"Fetched {len(all_firms)} total law firm records (async).")
        return all_firms
    
    def get_all_law_firms_sync(self) -> List[Dict[str, Any]]:
        """Fetch all law firm records using legacy managers."""
        projection = "ID, #nm, AdArchiveLastUpdated, NumAds"
        expression_names = {"#nm": "Name"}
        
        all_firms = self.law_firm_db.get_all_records(
            projection_expression=projection,
            expression_attribute_names=expression_names
        )
        self._logger.info(f"Fetched {len(all_firms)} total law firm records (sync).")
        return all_firms
    
    async def get_single_firm_data_async(self, firm_id: str) -> Optional[Dict[str, Any]]:
        """Fetch data for a single law firm by ID (async version)."""
        if not firm_id or not isinstance(firm_id, str):
            return None
        
        try:
            # Use async repository if available
            if hasattr(self, 'law_firms_repo') and self.law_firms_repo:
                firms = await self.law_firms_repo.get_by_id(firm_id)
                if firms:
                    return firms[0]
                else:
                    self._logger.debug(f"Firm ID {firm_id} not found.")
                    return None
            
            # Fallback to sync method
            return self.get_single_firm_data(firm_id)
                
        except Exception as e:
            self._logger.error(f"Error fetching firm {firm_id}: {e}",
                               exc_info=self.config.get('verbose', False))
            return None

    def get_single_firm_data(self, firm_id: str) -> Optional[Dict[str, Any]]:
        """Fetch data for a single law firm by ID (sync version)."""
        if not firm_id or not isinstance(firm_id, str):
            return None
        
        try:
            # Fallback to old method for sync calls
            projection = "ID, #nm, AdArchiveLastUpdated, NumAds, pageAlias, category, imageURI"
            expression_names = {"#nm": "Name"}
            
            response = self.law_firm_db.table.query(
                KeyConditionExpression=Key('ID').eq(firm_id),
                ProjectionExpression=projection,
                ExpressionAttributeNames=expression_names
            )
            
            items = response.get('Items', [])
            if items:
                return items[0]
            else:
                self._logger.debug(f"Firm ID {firm_id} not found.")
                return None
                
        except Exception as e:
            self._logger.error(f"Error fetching firm {firm_id}: {e}",
                               exc_info=self.config.get('verbose', False))
            return None
    
    async def fetch_all_ad_payloads(self, company_id: str) -> Optional[List[List[Dict[str, Any]]]]:
        """Fetch all ad result pages for a company."""
        all_ads_raw_groups = []
        forward_cursor = None
        page_num = 1
        max_pages = self.config.get('max_ad_pages', 50)
        total_ads_reported = 0
        
        while page_num <= max_pages:
            await self.session_manager.random_sleep(short=True)
            
            if self.session_manager.use_proxy and self.config.get('rotate_proxy_per_page', True):
                self.session_manager.rotate_proxy()
            
            payload_response = await self.api_client.fetch_ads_page(
                company_id, self.start_date_iso, self.end_date_iso, forward_cursor
            )
            
            if payload_response is None:
                return None if page_num == 1 else all_ads_raw_groups
            
            payload_data = payload_response.get('payload')
            if not isinstance(payload_data, dict):
                break
            
            results_data = payload_data.get('results')
            if not isinstance(results_data, list):
                break
            
            if page_num == 1:
                total_ads_reported = int(payload_data.get('totalCount', 0))
            
            if results_data:
                valid_groups = [group for group in results_data if isinstance(group, list) and group]
                all_ads_raw_groups.extend(valid_groups)
            
            forward_cursor = payload_data.get('forwardCursor')
            if not forward_cursor or not payload_data.get('hasNextPage', bool(forward_cursor)):
                break
            
            page_num += 1
        
        self._logger.info(f"Retrieved {len(all_ads_raw_groups)} groups for {company_id}.")
        return all_ads_raw_groups
    
    async def process_single_firm_core(
        self, 
        firm_data: Dict[str, Any],
        update_db: bool = True
    ) -> Tuple[bool, str, int, int, int, int]:
        """
        Core processing logic for a single firm.
        
        Returns:
            Tuple of (success, error_msg, ads_fetched, items_processed, items_updated, items_added)
        """
        firm_id = firm_data.get('ID')
        name = firm_data.get('Name')
        
        if not firm_id or not name:
            return False, f"Missing ID/Name: {firm_data}", 0, 0, 0, 0
        
        self._logger.info(f"Processing firm: {name} (ID: {firm_id})")
        
        processed_ads_list = None
        success_flag = True
        error_msg = "Unknown processing error"
        ads_fetched_count = 0
        
        try:
            # Fetch raw ad data
            raw_ad_groups = await self.fetch_all_ad_payloads(firm_id)
            ads_fetched_count = len(raw_ad_groups) if raw_ad_groups is not None else 0
            
            if raw_ad_groups is None:
                raise RuntimeError(f"Failed to fetch ad payloads ({getattr(self.api_client, 'last_error_type', 'Fetch Error')})")
            
            if not raw_ad_groups:
                processed_ads_list = []
            else:
                # Process ads through AdProcessor
                self._logger.info(f"Sending {len(raw_ad_groups)} ad groups to AdProcessor for {name}")
                
                # Log sample input data structure
                if raw_ad_groups and len(raw_ad_groups) > 0:
                    sample_group = raw_ad_groups[0]
                    if sample_group and len(sample_group) > 0:
                        sample_ad = sample_group[0]
                        sample_keys = list(sample_ad.keys()) if isinstance(sample_ad, dict) else "Not a dict"
                        self._logger.info(f"Sample input ad group structure for {name}: {sample_keys}")
                
                processed_ads_list = await self.ad_processor.process_raw_ads(raw_ad_groups, name)
                self._logger.info(f"AdProcessor returned {len(processed_ads_list) if processed_ads_list else 0} processed ads for {name}")
                
                # Log sample output data structure
                if processed_ads_list and len(processed_ads_list) > 0:
                    sample_processed = processed_ads_list[0]
                    if isinstance(sample_processed, dict):
                        sample_output_keys = list(sample_processed.keys())
                        self._logger.info(f"Sample processed ad structure for {name}: {sample_output_keys}")
                        # Log a few key fields to see if they have values
                        key_fields = ['ad_archive_id', 'start_date', 'law_firm', 'title', 'body']
                        field_values = {k: sample_processed.get(k) for k in key_fields if k in sample_processed}
                        self._logger.info(f"Sample processed ad key field values for {name}: {field_values}")
                    else:
                        self._logger.warning(f"Sample processed ad for {name} is not a dict: {type(sample_processed)}")
                
                if processed_ads_list is None:
                    error_msg = "AdProcessor returned None"
                    success_flag = False
                    processed_ads_list = []
                elif not isinstance(processed_ads_list, list):
                    error_msg = f"AdProcessor returned non-list ({type(processed_ads_list).__name__})"
                    success_flag = False
                    processed_ads_list = []
                else:
                    # Validate and clean processed ads
                    valid_ads = [ad for ad in processed_ads_list if isinstance(ad, dict)]
                    if len(processed_ads_list) - len(valid_ads) > 0:
                        self._logger.warning(f"AdProcessor returned invalid items for {name}.")
                    
                    processed_ads_list = valid_ads
                    
                    # Remove keys that shouldn't be saved
                    if processed_ads_list:
                        for ad_dict in processed_ads_list:
                            for key_to_remove in self.KEYS_TO_REMOVE_BEFORE_SAVE:
                                ad_dict.pop(key_to_remove, None)
                                
        except Exception as e:
            error_msg = str(e) or "Error during fetch/process"
            self._logger.error(f'Error for firm {name}: {error_msg}', 
                             exc_info=self.config.get('verbose', False))
            success_flag = False
            processed_ads_list = []
        
        # Process database operations
        total_items_processed = 0
        total_items_updated = 0
        total_items_added = 0
        
        if success_flag and processed_ads_list:
            self._logger.info(f"Starting database operations for {name} with {len(processed_ads_list)} processed ads")
            try:
                processed_counts = await self._process_database_operations(
                    processed_ads_list, name, firm_id, update_db
                )
                total_items_processed, total_items_updated, total_items_added = processed_counts
                
            except Exception as e:
                error_msg = f"Database operations failed: {e}"
                self._logger.error(f"Database error for {name}: {e}", 
                                 exc_info=self.config.get('verbose', False))
                success_flag = False
        elif success_flag and not processed_ads_list:
            self._logger.warning(f"AdProcessor returned empty list for {name} even though fetch succeeded")
        
        return (success_flag, error_msg, ads_fetched_count, 
                total_items_processed, total_items_updated, total_items_added)
    
    async def _process_database_operations(
        self, 
        processed_ads_list: List[Dict[str, Any]], 
        firm_name: str,
        firm_id: str,
        update_db: bool
    ) -> Tuple[int, int, int]:
        """Handle database operations for processed ads using async repositories."""
        items_to_batch_write = []
        total_items_processed = 0
        total_items_updated = 0
        total_items_added = 0
        processed_ads_map = {}
        
        # Prepare data for batch operations
        keys_to_fetch = []
        for i, ad_dict in enumerate(processed_ads_list):
            # Convert to PascalCase for DB storage
            ad_pascal = self._convert_to_pascal_case(ad_dict)
            ad_id = ad_pascal.get('AdArchiveID')  # This should be the correct key after mapping
            start_date = ad_pascal.get('StartDate')
            
            # Log conversion details for first few items
            if i < 3:
                self._logger.info(f"PascalCase conversion #{i+1}: Original keys: {list(ad_dict.keys())}")
                self._logger.info(f"PascalCase conversion #{i+1}: Converted keys: {list(ad_pascal.keys())}")
                # Check if the expected keys exist
                has_archive_id = 'AdArchiveID' in ad_pascal
                has_start_date = 'StartDate' in ad_pascal
                self._logger.info(f"PascalCase conversion #{i+1}: Has AdArchiveID={has_archive_id}, Has StartDate={has_start_date}")
                self._logger.info(f"PascalCase conversion #{i+1}: AdArchiveID={ad_id}, StartDate={start_date}")
                # Log the actual ad_archive_id value from original
                orig_archive_id = ad_dict.get('ad_archive_id')
                self._logger.info(f"PascalCase conversion #{i+1}: Original ad_archive_id={orig_archive_id}")
            
            if ad_id and start_date:
                pk_tuple = (str(ad_id), str(start_date))
                keys_to_fetch.append({'AdArchiveID': str(ad_id), 'StartDate': str(start_date)})
                processed_ads_map[pk_tuple] = ad_pascal
            else:
                self._logger.warning(f"Missing AdArchiveID or StartDate after conversion: AdArchiveID={ad_id}, StartDate={start_date}")
        
        if not keys_to_fetch:
            self._logger.warning(f"No valid ad keys found from {len(processed_ads_list)} processed ads")
            return 0, 0, 0
        
        # Fetch existing items for comparison using async repository
        existing_items_list = []
        if self.fb_archive_repo:
            # Use async batch get with repository
            for key in keys_to_fetch:
                try:
                    existing_item = await self.fb_archive_repo.get_item(key)
                    if existing_item:
                        existing_items_list.append(existing_item)
                except Exception as e:
                    self._logger.warning(f"Failed to fetch existing item {key}: {e}")
        else:
            # Fallback to wrapper
            existing_items_list = list(self.fb_ad_db.batch_get_items(keys_to_fetch))
            
        existing_items_map = {
            (item['AdArchiveID'], item['StartDate']): item 
            for item in existing_items_list
        }
        
        processing_date = self.end_date_iso
        
        # Compare and prepare items for writing
        for pk_tuple, new_ad_pascal in processed_ads_map.items():
            total_items_processed += 1
            fields_changed_count = 0
            existing_ad = existing_items_map.get(pk_tuple)
            
            ad_archive_id = pk_tuple[0]
            self._logger.debug(f"Processing ad {ad_archive_id} (item {total_items_processed}/{len(processed_ads_map)})")
            
            final_item_to_write = self._sanitize_record(new_ad_pascal)
            final_item_to_write['LastUpdated'] = processing_date
            is_active_new = final_item_to_write.get('IsActive', False)
            
            if existing_ad:
                # Update existing record
                existing_end_date = existing_ad.get('EndDate')
                if existing_end_date and processing_date and existing_end_date >= processing_date:
                    final_item_to_write['EndDate'] = existing_end_date
                elif is_active_new and (not existing_end_date or existing_end_date < processing_date):
                    final_item_to_write['EndDate'] = processing_date
                
                # Count changes with detailed logging
                for key, new_value in final_item_to_write.items():
                    if key in ['AdArchiveID', 'StartDate', 'LastUpdated']:
                        continue
                    existing_value = existing_ad.get(key)
                    if new_value != existing_value:
                        fields_changed_count += 1
                        if total_items_processed <= 3:  # Log changes for first few items
                            self._logger.debug(f"Ad {ad_archive_id}: Field '{key}' changed: '{existing_value}' -> '{new_value}'")
                
                # Additional debug logging for first few items
                if total_items_processed <= 3:
                    self._logger.info(f"Ad {ad_archive_id}: Detailed comparison complete - {fields_changed_count} fields changed out of {len(final_item_to_write)} total fields")
                
                if fields_changed_count > 0:
                    total_items_updated += 1
                    self._logger.debug(f"Ad {ad_archive_id}: {fields_changed_count} fields changed, will update")
                elif final_item_to_write.get('LastUpdated') != existing_ad.get('LastUpdated'):
                    total_items_updated += 1
                    self._logger.debug(f"Ad {ad_archive_id}: LastUpdated changed ({existing_ad.get('LastUpdated')} -> {final_item_to_write.get('LastUpdated')}), will update")
                else:
                    self._logger.debug(f"Ad {ad_archive_id}: No changes detected, skipping database write")
                    continue  # Skip if no changes
            else:
                # New record
                total_items_added += 1
                self._logger.debug(f"Ad {ad_archive_id}: New record, will insert")
            
            items_to_batch_write.append(final_item_to_write)
        
        # Batch write to database using async repository
        self._logger.info(f"Database comparison complete for {firm_name}: {total_items_processed} processed, {total_items_updated} updates, {total_items_added} additions, {len(items_to_batch_write)} to write")
        
        if items_to_batch_write:
            self._logger.info(f"Saving {len(items_to_batch_write)} ads to FBAdArchive DynamoDB table for {firm_name}")
            
            db_save_success_count = 0
            db_save_failure_count = 0
            
            if self.fb_archive_repo:
                # Use async repository for batch write - save each item individually
                self._logger.info(f"Using async repository to save {len(items_to_batch_write)} items to FBAdArchive table")
                
                # Log item details for debugging
                sample_item = items_to_batch_write[0] if items_to_batch_write else {}
                self._logger.info(f"Sample item structure: AdArchiveID={sample_item.get('AdArchiveID')}, "
                                f"StartDate={sample_item.get('StartDate')}, "
                                f"PageID={sample_item.get('PageID')}, "
                                f"LawFirm={sample_item.get('LawFirm')}")
                
                for i, item in enumerate(items_to_batch_write):
                    ad_archive_id = item.get('AdArchiveID', 'Unknown')
                    start_date = item.get('StartDate', 'Unknown')
                    
                    try:
                        # Log every 10th item for debugging
                        if i < 3 or i % 25 == 0:
                            self._logger.info(f"Saving item {i+1}/{len(items_to_batch_write)}: "
                                           f"AdArchiveID={ad_archive_id}, StartDate={start_date}")
                        
                        # Check if this is an update or insert by querying existing record
                        try:
                            existing_record = await self.fb_archive_repo.get_item({
                                'AdArchiveID': ad_archive_id,
                                'StartDate': start_date
                            })
                            operation_type = "UPDATE" if existing_record else "INSERT"
                            
                            if i < 3:  # Log operation type for first few items
                                self._logger.info(f"Operation type for {ad_archive_id}: {operation_type}")
                                
                        except Exception as check_error:
                            self._logger.warning(f"Could not check existing record for {ad_archive_id}: {check_error}")
                            operation_type = "UNKNOWN"
                        
                        # Perform the actual save
                        await self.fb_archive_repo.put_item(item)
                        db_save_success_count += 1
                        
                        if i % 25 == 0 and i > 0:
                            self._logger.info(f"Progress: {i+1}/{len(items_to_batch_write)} items processed, "
                                           f"{db_save_success_count} successful, {db_save_failure_count} failed")
                            
                    except Exception as e:
                        error_details = f"AdArchiveID={ad_archive_id}, StartDate={start_date}, Error={str(e)}"
                        self._logger.error(f"Failed to save ad item {i+1}: {error_details}")
                        
                        # Log additional details for debugging
                        if hasattr(e, 'response'):
                            self._logger.error(f"DynamoDB error response: {e.response}")
                        
                        db_save_failure_count += 1
            else:
                # Fallback to wrapper
                self._logger.warning("Using fallback wrapper for database save - async repository not available")
                db_save_success_count, db_save_failure_count = self.fb_ad_db.batch_insert_items(items_to_batch_write)
            
            # Final summary
            self._logger.info(f"FBAdArchive database save completed: {db_save_success_count} success, {db_save_failure_count} failures")
            self._logger.info(f"Success rate: {(db_save_success_count / len(items_to_batch_write) * 100):.1f}% ({db_save_success_count}/{len(items_to_batch_write)})")
            
            if db_save_failure_count > 0:
                self._logger.error(f"CRITICAL: {db_save_failure_count} items failed to save to FBAdArchive out of {len(items_to_batch_write)} total")
                self._logger.error(f"This means existing items are NOT being updated properly!")
                self._logger.error("Check the error logs above for specific DynamoDB error details")
                # Don't raise exception, just warn
                # raise Exception(f"FBAdArchive DB Save Failed for {db_save_failure_count} items")
            else:
                self._logger.info("All items saved successfully to FBAdArchive")
        
        # Update law firm record if requested
        if update_db:
            try:
                update_data = {
                    'AdArchiveLastUpdated': self.end_date_iso, 
                    'NumAds': len(processed_ads_map)
                }
                
                if self.law_firms_repo:
                    # Use async repository
                    key_item = {'ID': firm_id, 'Name': firm_name, **update_data}
                    await self.law_firms_repo.add_or_update_record(key_item)
                    self._logger.info(f"Updated law firm {firm_name} record")
                else:
                    # Fallback to wrapper
                    key_dict = {'ID': firm_id, 'Name': firm_name}
                    if not self.law_firm_db.update_item(key_dict, update_data):
                        self._logger.warning(f"LawFirm DB update may have failed for {firm_name}")
                    
            except Exception as e:
                self._logger.error(f"LawFirm DB Update Exception: {e}")
        
        return total_items_processed, total_items_updated, total_items_added
    
    def _convert_to_pascal_case(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Convert snake_case keys to PascalCase for DynamoDB storage."""
        if not isinstance(data, dict):
            return data
            
        # Special mappings for DynamoDB field names that need specific casing
        special_mappings = {
            'ad_archive_id': 'AdArchiveID',
            'ad_creative_id': 'AdCreativeID',
            'page_id': 'PageID',
            'start_date': 'StartDate',
            'end_date': 'EndDate',
            'law_firm': 'LawFirm',
            'page_name': 'PageName',
            'is_active': 'IsActive',
            'publisher_platform': 'PublisherPlatform',
            'link_url': 'LinkUrl',
            'link_description': 'LinkDescription',
            'last_updated': 'LastUpdated',
            's3_image_key': 'S3ImageKey'
        }
            
        result = {}
        for key, value in data.items():
            # Check for special mappings first
            if key in special_mappings:
                pascal_key = special_mappings[key]
                # Debug logging for critical fields
                if key in ['ad_archive_id', 'start_date']:
                    self._logger.debug(f"Special mapping applied: {key} -> {pascal_key}")
            # Convert snake_case to PascalCase
            elif '_' in key:
                # Split by underscore and capitalize each part
                parts = key.split('_')
                pascal_key = ''.join(word.capitalize() for word in parts)
                # Debug logging for critical fields
                if key in ['ad_archive_id', 'start_date']:
                    self._logger.debug(f"Standard conversion applied: {key} -> {pascal_key}")
            else:
                # Just capitalize first letter
                pascal_key = key.capitalize() if key else key
            
            result[pascal_key] = value
            
        return result
    
    def _sanitize_record(self, record: Dict[str, Any]) -> Dict[str, Any]:
        """Sanitize record for DynamoDB by removing None values and ensuring proper types."""
        if not isinstance(record, dict):
            return record
            
        sanitized = {}
        for key, value in record.items():
            # Skip None values
            if value is None:
                continue
                
            # Convert empty strings to None and skip
            if isinstance(value, str) and not value.strip():
                continue
                
            # Ensure boolean types are preserved
            if isinstance(value, bool):
                sanitized[key] = value
            # Convert numeric strings to proper types if needed
            elif isinstance(value, str):
                sanitized[key] = value
            else:
                sanitized[key] = value
                
        return sanitized