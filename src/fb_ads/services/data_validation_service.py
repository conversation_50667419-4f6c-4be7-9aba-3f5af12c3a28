"""
Data Validation Service

Handles data validation, filtering, and transformation logic:
- Law firm filtering and validation
- Skip list management
- Data sanitization and transformation
- Configuration validation
"""
import random
from typing import Dict, List, Any, Set
from pathlib import Path


class DataValidationService:
    """Service responsible for data validation and filtering operations."""
    
    def __init__(self, config: Dict[str, Any], logger, processing_tracker):
        """Initialize the data validation service."""
        self.config = config
        self._logger = logger
        self.processing_tracker = processing_tracker
        self.end_date_iso = config.get('iso_date')
        
        # Load ignore firms data
        self.ignore_firm_data = self._load_ignore_firms()
    
    def _load_ignore_firms(self) -> Dict[str, str]:
        """Load the ignore firms data from a JSON file."""
        ignore_file_path = Path(__file__).parent.parent.parent / 'config/fb_ads/ignore_firms.json'
        self._logger.info(f"Attempting to load ignored firms from: {ignore_file_path}")
        
        try:
            import json
            with open(ignore_file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            if not isinstance(data, dict):
                self._logger.error(f"Invalid format in {ignore_file_path}: Expected dict.")
                return {}
            
            self._logger.info(f"Successfully loaded {len(data)} firms from {ignore_file_path}")
            return data
            
        except FileNotFoundError:
            self._logger.error(f"Ignore firms file not found: {ignore_file_path}.")
            return {}
        except json.JSONDecodeError as e:
            self._logger.error(f"Error decoding JSON: {e}.")
            return {}
        except Exception as e:
            self._logger.error(f"Unexpected error loading {ignore_file_path}: {e}", exc_info=True)
            return {}
    
    def filter_law_firms(self, all_firms: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Filter law firms based on ignore lists, update status, and development settings.
        
        Args:
            all_firms: List of all law firm records
            
        Returns:
            List of firms eligible for processing
        """
        if not all_firms:
            return []
        
        # Check for development/testing mode with sample firms
        development_config = self.config.get('development', {})
        sample_firms = development_config.get('sample_firms', [])
        max_test_firms = development_config.get('max_test_firms', None)
        
        if sample_firms:
            self._logger.info(f"Development mode: Testing with sample firms: {sample_firms}")
            # Filter to only include sample firms
            sample_firm_data = []
            for firm in all_firms:
                firm_id = firm.get('ID')
                if firm_id in sample_firms:
                    sample_firm_data.append(firm)
            
            if not sample_firm_data:
                self._logger.warning(f"No sample firms found in database. Requested: {sample_firms}")
                return []
            
            # Apply max_test_firms limit if configured
            if max_test_firms and len(sample_firm_data) > max_test_firms:
                sample_firm_data = sample_firm_data[:max_test_firms]
                self._logger.info(f"Limited to {max_test_firms} test firms")
            
            self._logger.info(f"Development mode: Processing {len(sample_firm_data)} sample firms")
            return sample_firm_data
        
        # Normal production filtering
        # Get skip lists
        tracker_skip_ids = self.processing_tracker.get_skip_list()
        loaded_ignore_ids = set(self.ignore_firm_data.keys())
        combined_skip_ids = loaded_ignore_ids.union(tracker_skip_ids)
        
        filtered_firms = []
        skipped_ignored = 0
        skipped_tracker = 0
        skipped_updated = 0
        
        check_last_updated = self.config.get('skip_firms_updated_today', True)
        
        for firm in all_firms:
            firm_id = firm.get('ID')
            if not firm_id:
                continue
            
            # Check skip lists
            if firm_id in combined_skip_ids:
                if firm_id in loaded_ignore_ids:
                    skipped_ignored += 1
                else:
                    skipped_tracker += 1
                continue
            
            # Check if already updated today
            if check_last_updated and firm.get('AdArchiveLastUpdated') == self.end_date_iso:
                skipped_updated += 1
                continue
            
            filtered_firms.append(firm)
        
        self._logger.info(
            f"Processing {len(filtered_firms)} firms. Skipped: {skipped_ignored} (ignored), "
            f"{skipped_tracker} (tracker), {skipped_updated} (updated today)."
        )
        
        # Shuffle order if configured
        if filtered_firms and self.config.get('shuffle_firm_order', True):
            random.shuffle(filtered_firms)
        
        return filtered_firms
    
    def get_ignore_list_firms(self) -> List[str]:
        """Get list of firm IDs from the ignore list."""
        return list(self.ignore_firm_data.keys())
    
    def validate_firm_data(self, firm_data: Dict[str, Any]) -> bool:
        """
        Validate that firm data contains required fields.
        
        Args:
            firm_data: Dictionary containing firm information
            
        Returns:
            bool: True if valid, False otherwise
        """
        required_fields = ['ID', 'Name']
        
        for field in required_fields:
            if not firm_data.get(field):
                self._logger.error(f"Missing required field '{field}' in firm data: {firm_data}")
                return False
        
        # Validate ID format
        firm_id = firm_data.get('ID')
        if not isinstance(firm_id, str) or not firm_id.strip():
            self._logger.error(f"Invalid firm ID format: {firm_id}")
            return False
        
        return True
    
    def sanitize_firm_data(self, firm_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Sanitize firm data for database storage.
        
        Args:
            firm_data: Raw firm data
            
        Returns:
            Dict: Sanitized firm data
        """
        sanitized = {}
        
        # Copy required fields
        required_fields = ['ID', 'Name', 'pageAlias', 'category', 'imageURI']
        for field in required_fields:
            value = firm_data.get(field)
            if value is not None:
                sanitized[field] = str(value).strip() if isinstance(value, str) else value
        
        # Set defaults for missing fields
        sanitized.setdefault('AdArchiveLastUpdated', None)
        sanitized.setdefault('NumAds', 0)
        
        return sanitized
    
    def validate_ad_data(self, ad_data: Dict[str, Any]) -> bool:
        """
        Validate ad data structure.
        
        Args:
            ad_data: Dictionary containing ad information
            
        Returns:
            bool: True if valid, False otherwise
        """
        if not isinstance(ad_data, dict):
            return False
        
        # Check for required fields
        required_fields = ['AdArchiveID', 'StartDate']
        for field in required_fields:
            if field not in ad_data or ad_data[field] is None:
                return False
        
        return True
    
    def get_processing_date_range(self, mode: str = 'default') -> tuple:
        """
        Get date range for processing based on mode.
        
        Args:
            mode: Processing mode ('default', 'single_firm', 'ignore_list')
            
        Returns:
            tuple: (start_date_iso, end_date_iso)
        """
        if mode == 'single_firm':
            from src.utils.date import DateUtils
            single_run_days = self.config.get('single_firm_date_range_days', 30)
            start_date = DateUtils.get_date_before_n_days(single_run_days, self.end_date_iso)
            return start_date, self.end_date_iso
        
        elif mode == 'ignore_list':
            from src.utils.date import DateUtils
            ignore_list_days = self.config.get('ignore_list_date_range_days', 30)
            start_date = DateUtils.get_date_before_n_days(ignore_list_days, self.end_date_iso)
            return start_date, self.end_date_iso
        
        else:  # default mode
            from src.utils.date import DateUtils
            default_days = self.config.get('default_date_range_days', 14)
            start_date = DateUtils.get_date_before_n_days(default_days, self.end_date_iso)
            return start_date, self.end_date_iso
    
    def validate_config(self) -> bool:
        """
        Validate configuration settings.
        
        Returns:
            bool: True if configuration is valid
        """
        required_config = ['iso_date', 'bucket_name']
        
        for key in required_config:
            if not self.config.get(key):
                self._logger.error(f"Missing required configuration: {key}")
                return False
        
        # Validate date format
        try:
            from datetime import datetime
            datetime.strptime(self.config['iso_date'], '%Y%m%d')
        except ValueError:
            self._logger.error(f"Invalid iso_date format: {self.config['iso_date']}")
            return False
        
        return True
    
    def clean_ad_data_for_storage(self, ad_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Clean ad data before database storage.
        
        Args:
            ad_data: Raw ad data
            
        Returns:
            Dict: Cleaned ad data
        """
        # Keys to remove before saving
        keys_to_remove = [
            'original_image_url', 'resized_image_url', 'video_preview_image_url',
            'video_hd_url', 'video_sd_url', '_temp_source_image_url', 
            '_s3_check_result', 'ImageText'
        ]
        
        cleaned_data = ad_data.copy()
        for key in keys_to_remove:
            cleaned_data.pop(key, None)
        
        return cleaned_data