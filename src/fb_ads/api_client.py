import asyncio
import json
import logging
import random
import time
from typing import Dict, Any, Optional, List

import requests
from requests.exceptions import RequestException

# Assuming FacebookSessionManager is importable from the same directory or path
from .session_manager import FacebookSessionManager # Make sure this import works
from .bandwidth_logger import <PERSON>width<PERSON>ogger, calculate_request_size, calculate_response_size

class Facebook<PERSON>IClient:
    """
    Handles interactions with Facebook Ads Library API endpoints, including
    GraphQL for searching and the async endpoint for fetching ad pages.
    Includes retry logic for HTTP errors, session issues, and specific
    payload-level blocks (error code 3252001).
    """

    GRAPHQL_URL = 'https://www.facebook.com/api/graphql/'
    SEARCH_ADS_URL = 'https://www.facebook.com/ads/library/async/search_ads/'
    DEFAULT_REFERER = 'https://www.facebook.com/ads/library/' # Added default referer
    # Example Doc ID - Ensure this is up-to-date by inspecting network requests if needed
    COMPANY_SEARCH_DOC_ID = '9333890689970605'

    def __init__(self, session_manager: 'FacebookSessionManager', config: Dict[str, Any]):
        """
        # Use our custom logging setup for consistent logging across the fb_ads module

        Initializes the FacebookAPIClient.

        Args:
            session_manager: An instance of FacebookSessionManager.
            config: The application configuration dictionary.
        """
        self.session_manager = session_manager
        self.config = config
        # Use our custom logging setup for consistent logging
        self._logger = logging.getLogger('src.fb_ads')
        # Tracks the type of the last significant error encountered by this client instance
        self.last_error_type: str = "None"

        # Initialize bandwidth logger (use the same instance as session_manager if available)
        # Disable periodic logging by default to avoid potential hanging issues
        if hasattr(session_manager, 'bandwidth_logger'):
            self.bandwidth_logger = session_manager.bandwidth_logger
            self._logger.debug("Using session manager's bandwidth logger instance")
        else:
            bandwidth_config = config.copy() if config else {}
            bandwidth_config['disable_bandwidth_periodic_logging'] = bandwidth_config.get('disable_bandwidth_periodic_logging', True)
            self.bandwidth_logger = BandwidthLogger(bandwidth_config)
            self._logger.info("Initialized new bandwidth logger instance for API client with periodic logging disabled")

    # --- MODIFIED: Made async ---
    async def _make_request(self, method: str, url: str, **kwargs) -> Optional[requests.Response]:
        """
        Makes an HTTP request using the managed session (now ASYNCHRONOUSLY).

        Handles retries for network errors, HTTP-level blocks (4xx/5xx status codes),
        and HTTP-level session issues. Reports proxy failures. Adds default Referer.
        Uses asyncio.to_thread for the blocking requests call.
        """
        session = self.session_manager.get_session()
        if not session:
            self._logger.error("Session not available. Cannot make request.")
            self.last_error_type = "SessionError"
            return None

        max_retries = self.config.get('api_retries', 5)
        base_backoff = self.config.get('api_backoff_base', 1.7)

        for attempt in range(max_retries):
            current_proxy_url = self.session_manager.get_current_proxy() # Get proxy for logging/error reporting

            # Check if proxy is properly set
            if not current_proxy_url and self.session_manager.use_proxy:
                self._logger.warning("Proxy is enabled but no proxy URL is set. Attempting to set proxy...")
                # Try to regenerate the proxy list and set a proxy
                if not self.session_manager.proxies:
                    self._logger.info("Regenerating proxy list...")
                    self.session_manager.proxies = self.session_manager._generate_proxy_list()

                if self.session_manager.proxies:
                    self._logger.info(f"Setting proxy from regenerated list of {len(self.session_manager.proxies)} proxies")
                    self.session_manager._set_proxy()
                    current_proxy_url = self.session_manager.get_current_proxy()
                    if current_proxy_url:
                        self._logger.info(f"Successfully set proxy: {current_proxy_url.split('@')[-1]}")
                    else:
                        self._logger.error("Failed to set proxy even after regenerating list")
                else:
                    self._logger.error("Failed to regenerate proxy list")

            try:
                # --- Prepare Request Data/Headers ---
                current_headers = session.headers.copy()
                current_data = kwargs.get('../../config/data', {}).copy() if kwargs.get('../../config/data') else {}

                # --- ADDED: Default Referer ---
                if 'Referer' not in current_headers and url in [self.GRAPHQL_URL, self.SEARCH_ADS_URL]:
                     current_headers['Referer'] = self.DEFAULT_REFERER
                     self._logger.debug(f"Added default Referer header: {self.DEFAULT_REFERER}")
                # --- End Default Referer ---

                if method.upper() == 'POST' and 'application/x-www-form-urlencoded' in current_headers.get('Content-Type', ''):
                    session_data = self.session_manager.get_session_data()
                    current_data.setdefault('__req', str(self.session_manager.get_request_counter()))
                    for key in ['fb_dtsg', 'lsd', 'jazoest', '__user', '__a', '__rev', '__hsi', '__spin_r', '__spin_b', '__spin_t', '__ccg']:
                        current_data.setdefault(key, session_data.get(key, ''))
                    kwargs['data'] = current_data
                    self.session_manager.increment_request_counter()

                if 'headers' in kwargs: current_headers.update(kwargs['headers'])
                kwargs['headers'] = current_headers

                # --- Make the HTTP Request (Run blocking call in thread) ---
                proxy_info_log = f"via proxy {current_proxy_url.split('@')[-1]}" if current_proxy_url else "without proxy"
                self._logger.debug(f"Making {method} request to {url} (Attempt {attempt + 1}/{max_retries}) {proxy_info_log}")

                # Calculate request size before making the request
                request_size = calculate_request_size(kwargs.get('headers', {}), kwargs.get('../../config/data', {}))

                # Use asyncio.to_thread for the blocking requests call
                response = await asyncio.to_thread(
                    session.request,
                    method,
                    url,
                    **kwargs
                 )

                # --- Check for HTTP-Level Blocks/Rate Limits ---
                response_text_preview = "[Binary Content]"
                is_likely_text = 'text' in response.headers.get('content-type', '').lower()
                if is_likely_text or response.status_code != 200:
                    try: response_text_preview = response.text[:500]
                    except Exception: pass

                block_detected = False
                if response.status_code in [403, 429, 503]:
                    block_detected = True; block_reason = f"HTTP {response.status_code}"
                elif "Temporarily Blocked" in response_text_preview or "Try again later" in response_text_preview:
                    block_detected = True; block_reason = "Block page content"

                if block_detected:
                    self.last_error_type = "Block"
                    self._logger.warning(f"{block_reason} detected on {method} {url} (HTTP level).")
                    # --- Record proxy failure before handling block ---
                    self.session_manager.record_proxy_failure(current_proxy_url)
                    self.session_manager.handle_block_or_rate_limit(block_reason) # This might rotate the proxy
                    # Re-fetch potentially updated proxy URL for next attempt logging
                    current_proxy_url = self.session_manager.get_current_proxy()
                    wait_after_block = 2
                    self._logger.info(f"Waiting {wait_after_block}s after HTTP block handling before retry...")
                    # Use await for asyncio.sleep
                    await asyncio.sleep(wait_after_block)
                    continue

                # --- Check for HTTP-Level Session Issues ---
                response_full_text = None
                if is_likely_text or response.status_code != 200:
                    try: response_full_text = response.text
                    except Exception: pass

                if response_full_text and ("login required" in response_full_text.lower() or \
                                           "session has expired" in response_full_text.lower() or \
                                           "checkpoint" in response_full_text.lower()):
                    self.last_error_type = "SessionIssue"
                    self._logger.warning("Detected session issue during API request (HTTP level). Refreshing session...")
                    # --- Record proxy failure on session issue ---
                    self.session_manager.record_proxy_failure(current_proxy_url)
                    # Use await for async session creation
                    if await self.session_manager.create_new_session():
                        self._logger.info("Session refreshed successfully. Retrying original request.")
                        session = self.session_manager.get_session()
                        if not session:
                            self._logger.error("Failed to get session object after refresh. Aborting request.")
                            return None
                        continue
                    else:
                        self._logger.error("Session refresh failed. Aborting request.")
                        return None

                # --- Check for other HTTP errors ---
                response.raise_for_status()

                # --- HTTP Success ---
                self._logger.debug(f"HTTP request successful ({method} {url})")
                # self.last_error_type = "None" # Reset by calling methods on full success

                # Log bandwidth usage for successful requests
                response_size = calculate_response_size(response)
                content_type = response.headers.get('content-type', '')
                self.bandwidth_logger.log_request(
                    url=url,
                    request_size=request_size,
                    response_size=response_size,
                    content_type=content_type
                )
                self._logger.debug(f"Logged bandwidth for {method} {url}: {response_size} bytes downloaded")

                return response

            # --- Handle Exceptions during the HTTP request attempt ---
            except RequestException as e:
                self.last_error_type = "NetworkError"
                error_msg = f"API request network error ({method} {url}) (Attempt {attempt + 1}/{max_retries}): {e}"
                # --- Record proxy failure on network error ---
                self._logger.error(error_msg)
                self.session_manager.record_proxy_failure(current_proxy_url)
                # Rotate proxy on timeout or connection error if using proxies
                if isinstance(e, (requests.exceptions.Timeout, requests.exceptions.ConnectionError, requests.exceptions.ProxyError)):
                     if self.session_manager.use_proxy:
                         self._logger.warning(f"Rotating proxy due to {type(e).__name__}.")
                         self.session_manager.rotate_proxy()
                         # Re-fetch potentially updated proxy URL for next attempt logging
                         current_proxy_url = self.session_manager.get_current_proxy()

            except Exception as e:
                self.last_error_type = "UnexpectedError"
                self._logger.error(f"Unexpected error during API request ({method} {url}) (Attempt {attempt + 1}): {e}", exc_info=self.config.get('verbose', False))
                # --- Record proxy failure on unexpected error ---
                self.session_manager.record_proxy_failure(current_proxy_url)

            # --- Wait before next HTTP retry attempt ---
            if attempt < max_retries - 1:
                # Calculate wait time using exponential backoff with jitter
                sleep_time = (base_backoff ** attempt) + random.uniform(0.5, 2.0)
                self._logger.info(f"Waiting {sleep_time:.2f}s before retrying API request (Last Error: {self.last_error_type})...")
                # Use await for asyncio.sleep
                await asyncio.sleep(sleep_time)
                # Optional: Consider rotating proxy more aggressively here too?
                # if self.session_manager.use_proxy and self.last_error_type == "NetworkError":
                #    self.session_manager.rotate_proxy()
                #    await asyncio.sleep(1) # Await sleep after rotation

        # --- Failed after all HTTP retries ---
        self._logger.error(f"API request failed after {max_retries} HTTP attempts ({method} {url}). Last error: {self.last_error_type}")
        return None

    # --- MODIFIED: Made async ---
    async def search_company_by_name(self, company_name: str) -> Optional[List[Dict[str, Any]]]:
        """
        Searches for company/page IDs using the GraphQL endpoint (now ASYNCHRONOUS).

        Args:
            company_name: The name of the company/page to search for.

        Returns:
            A list of dictionaries representing potential matches, or None on failure.
        """
        if self.session_manager.testing:
            self._logger.info("Testing mode: Skipping company search.")
            return [{'id': 'dummy_id_test', 'name': company_name, 'pageIsDeleted': False}]

        # Ensure session is valid before proceeding
        session_data = self.session_manager.get_session_data()
        if not all(k in session_data for k in ['fb_dtsg', 'lsd', 'jazoest']):
            self._logger.warning("Session tokens missing. Attempting session refresh before company search.")
            # Use await for async session creation
            if not await self.session_manager.create_new_session():
                self._logger.error("Session refresh failed. Cannot search company.")
                return None
            session_data = self.session_manager.get_session_data()  # Re-fetch after refresh

        self._logger.info(f"Searching company '{company_name}' via GraphQL...")
        await self.session_manager.random_sleep(short=True)  # Use await for async sleep
        self.session_manager.update_ajax_headers()  # Ensure AJAX headers are set

        # Prepare variables for the GraphQL query
        variables = json.dumps({
            "queryString": company_name,
            "isMobile": False,
            "country": "US",
            "adType": "ALL"
        })

        # Prepare form data
        post_data = {
            **session_data,  # Include all common fields from session
            'fb_api_caller_class': 'RelayModern',
            'fb_api_req_friendly_name': 'useAdLibraryTypeaheadSuggestionDataSourceQuery',
            'variables': variables,
            'server_timestamps': 'true',
            'doc_id': self.COMPANY_SEARCH_DOC_ID
            # __req is handled by _make_request
        }

        # Make the request using the internal helper (handles HTTP retries)
        # Use await as _make_request is now async
        response = await self._make_request(
            'POST',
            self.GRAPHQL_URL,
            data=post_data,
            timeout=self.session_manager.reqs.timeout if self.session_manager.reqs else 30
        )

        if not response:
            self._logger.error(f"GraphQL company search failed for '{company_name}' after HTTP retries.")
            # self.last_error_type is already set by _make_request
            return None

        # Process the response payload
        try:
            # Use await for potential async JSON parsing in future libraries
            # For standard requests, this doesn't change behavior
            payload = await asyncio.to_thread(response.json)

            # Check for GraphQL specific errors within the payload
            if 'errors' in payload:
                self._logger.error(f"GraphQL API returned errors for '{company_name}': {payload['errors']}")
                self.last_error_type = "GraphQLAPIError"
                return None

            # Check for expected data structure
            if 'data' in payload and \
                    payload['data'].get('ad_library_main', {}).get('typeahead_suggestions'):
                results = self._parse_graphql_search_response(payload)
                self._logger.info(f"Found {len(results)} potential matches via GraphQL for '{company_name}'.")
                self.last_error_type = "None"  # Reset on success
                return results
            else:
                self._logger.warning(
                    f"GraphQL response missing expected data structure for '{company_name}': {json.dumps(payload)[:500]}...")
                self.last_error_type = "GraphQLStructureError"
                return None

        except json.JSONDecodeError as e:
            self._logger.error(
                f"Failed to decode GraphQL JSON response for '{company_name}': {e}. Response: {response.text[:500]}...")
            self.last_error_type = "JSONError"
            return None
        except Exception as e:
            self._logger.error(f"Unexpected error parsing GraphQL response for '{company_name}': {e}",
                               exc_info=self.config.get('verbose', False))
            self.last_error_type = "UnexpectedError"
            return None

    # _parse_graphql_search_response remains synchronous as it only processes data
    def _parse_graphql_search_response(self, response_data: dict) -> List[Dict[str, Any]]:
        """Parses the GraphQL structure for Ad Library typeahead results."""
        results = []
        try:
            # Navigate safely through the nested dictionary structure
            suggestions = response_data.get('../../config/data', {}).get('ad_library_main', {}).get('typeahead_suggestions', {})
            page_results_list = suggestions.get('page_results', [])

            if not isinstance(page_results_list, list):
                self._logger.warning("Could not find 'page_results' list in GraphQL data.")
                return []

            for node in page_results_list:
                # Ensure the node is a dictionary and has the required fields
                if isinstance(node, dict) and node.get('page_id') and node.get('name'):
                    # Check if page is restricted
                    page_is_restricted = node.get('page_is_restricted', False)

                    # If page is restricted, track it
                    if page_is_restricted:
                        self._logger.info(f"Detected restricted page: {node.get('name')} (ID: {node.get('page_id')})")
                        self._track_restricted_page(str(node.get('page_id')), node.get('name'))

                    # Standardize keys for consistency downstream
                    results.append({
                        'id': str(node.get('page_id')),  # Ensure ID is string
                        'name': node.get('name'),
                        'imageURI': node.get('image_uri'),
                        'pageAlias': node.get('page_alias'),
                        'category': node.get('category'),
                        'pageIsDeleted': node.get('page_is_deleted', False),
                        'pageIsRestricted': page_is_restricted
                    })
            return results
        except Exception as e:
            self._logger.error(f"Error parsing GraphQL search response structure: {e}",
                               exc_info=self.config.get('verbose', False))
            return []  # Return empty list on parsing error

    # _track_restricted_page remains synchronous
    def _track_restricted_page(self, page_id: str, page_name: str) -> None:
        """Tracks a restricted page by adding it to the login_required.json file."""
        import os
        from pathlib import Path

        # Define the path to the login_required.json file
        login_required_path = Path(__file__).parent.parent / 'config/fb_ads/login_required.json'

        # Ensure the directory exists
        os.makedirs(os.path.dirname(login_required_path), exist_ok=True)

        # Load existing data if file exists
        restricted_pages = {}
        if os.path.exists(login_required_path):
            try:
                with open(login_required_path, 'r', encoding='utf-8') as f:
                    content = f.read().strip()
                    if content:  # Only try to parse if file is not empty
                        restricted_pages = json.load(f)
            except json.JSONDecodeError as e:
                self._logger.error(f"Error decoding login_required.json: {e}. Creating new file.")
            except Exception as e:
                self._logger.error(f"Error reading login_required.json: {e}. Creating new file.")

        # Add the new restricted page if not already present
        if page_id not in restricted_pages:
            restricted_pages[page_id] = page_name

            # Save the updated data
            try:
                with open(login_required_path, 'w', encoding='utf-8') as f:
                    json.dump(restricted_pages, f, indent=2, ensure_ascii=False)
                self._logger.info(f"Added restricted page {page_name} (ID: {page_id}) to login_required.json")
            except Exception as e:
                self._logger.error(f"Failed to save restricted page to login_required.json: {e}")
        else:
            self._logger.debug(f"Restricted page {page_name} (ID: {page_id}) already in login_required.json")

    # --- MODIFIED: Made async ---
    async def fetch_ads_page(self, company_id: str, start_date: str, end_date: str, forward_cursor: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """
        Fetches a single page of ad results (now ASYNCHRONOUS).
        Handles payload-level retries.
        """
        if self.session_manager.testing:
            self._logger.info(f"Testing mode: Simulating fetch ads page for {company_id}.")
            return {"payload": {"results": [], "forwardCursor": None, "totalCount": 0}}

        max_payload_retries = self.config.get('payload_retries', 3)
        payload_attempt = 0

        while payload_attempt < max_payload_retries:
            payload_attempt += 1
            self._logger.debug(f"Attempting to fetch ads page for {company_id} (Payload Attempt {payload_attempt}/{max_payload_retries})")
            current_proxy_url = self.session_manager.get_current_proxy() # Get proxy for error reporting

            # --- Session Check ---
            session_data = self.session_manager.get_session_data()
            current_session_uuid = self.session_manager.get_current_session_id()
            if not all(k in session_data for k in ['fb_dtsg', 'lsd', 'jazoest']) or not current_session_uuid:
                self._logger.warning("Session tokens/ID missing. Refreshing session before fetching ads.")
                # Use await for async session creation
                if not await self.session_manager.create_new_session():
                    self._logger.error("Session refresh failed. Cannot fetch ads.")
                    return None
                session_data = self.session_manager.get_session_data(); current_session_uuid = self.session_manager.get_current_session_id()
                if not current_session_uuid: self._logger.error("Session ID missing after refresh."); return None

            # --- Prepare Request ---
            self.session_manager.update_ajax_headers() # Sets base headers
            params = { # ... params setup ...
                'session_id': current_session_uuid, 'count': str(random.randint(25, 30)), 'active_status': 'all',
                'ad_type': 'all', 'countries[0]': 'ALL', 'view_all_page_id': company_id, 'media_type': 'all',
                'sort_data[direction]': 'desc', 'sort_data[mode]': 'relevancy_monthly_grouped',
                'search_type': 'page',
                'start_date[min]': f"{start_date[:4]}-{start_date[4:6]}-{start_date[6:]}",
                'start_date[max]': f"{end_date[:4]}-{end_date[4:6]}-{end_date[6:]}",
            }
            if forward_cursor: params['forward_cursor'] = forward_cursor
            post_data = {k: v for k, v in session_data.items() if k.startswith('__') or k in ['fb_dtsg', 'lsd', 'jazoest', 'dpr']}
            post_data['__jssesw'] = '1'
            # Referer added by _make_request if needed
            request_headers = {} # No specific headers needed here beyond session+default referer

            # --- Make HTTP Request ---
            # Use await as _make_request is now async
            response = await self._make_request(
                'POST', self.SEARCH_ADS_URL, params=params, data=post_data, headers=request_headers,
                timeout=self.session_manager.reqs.timeout if self.session_manager.reqs else 30
            )

            if not response:
                # _make_request failed, indicates persistent HTTP issue
                self._logger.error(f"HTTP request failed definitively for ads page {company_id}. Cannot proceed.")
                return None

            # --- Process Payload ---
            response_text = response.text
            if response_text.startswith('for (;;);'): response_text = response_text[len('for (;;);'):]

            try:
                # Use await for potential async json loading
                payload = await asyncio.to_thread(json.loads, response_text)

                # --- Check Payload Errors ---
                if 'error' in payload or 'errors' in payload:
                    error_summary = payload.get('errorSummary') or payload.get('errorDescription') or str(payload.get('errors', 'Unknown API Error'))
                    error_code = payload.get('error') # Can be dict or int
                    if isinstance(error_code, dict): error_code = error_code.get('code')
                    self._logger.error(f"API error in ad payload for {company_id}: {error_summary} (Code: {error_code}) (Payload Attempt {payload_attempt})")

                    # --- Record proxy failure for payload errors ---
                    if current_proxy_url:
                        self._logger.info(f"Recording failure for proxy: {current_proxy_url.split('@')[-1]}")
                        self.session_manager.record_proxy_failure(current_proxy_url)
                    else:
                        self._logger.warning("Cannot record proxy failure: current_proxy_url is None")
                        # Try to get the current proxy again
                        current_proxy_url = self.session_manager.get_current_proxy()
                        if current_proxy_url:
                            self._logger.info(f"Retrieved current proxy: {current_proxy_url.split('@')[-1]}")
                        else:
                            self._logger.error("Still no proxy URL available. Check proxy configuration.")

                    if error_code == 3252001: # Payload Block
                        self.last_error_type = "PayloadBlock"
                        if payload_attempt < max_payload_retries:
                            self._logger.warning("Payload block (3252001) detected. Handling and retrying...")
                            # Get the current proxy before handling the block
                            current_proxy = self.session_manager.get_current_proxy()
                            self._logger.info(f"Current proxy before handling block: {current_proxy.split('@')[-1] if current_proxy else 'None'}")

                            self.session_manager.handle_block_or_rate_limit("Payload block 3252001")

                            # Check if proxy was rotated
                            new_proxy = self.session_manager.get_current_proxy()
                            self._logger.info(f"Proxy after handling block: {new_proxy.split('@')[-1] if new_proxy else 'None'}")

                            # Use await for asyncio.sleep
                            await asyncio.sleep(2); continue
                        else: self._logger.error("Payload block (3252001) persisted."); break
                    elif error_code == 1357001: # Payload Session Issue
                        self.last_error_type = "PayloadSessionIssue"
                        if payload_attempt < max_payload_retries:
                            self._logger.warning("Payload session error (1357001) detected. Refreshing session and retrying...")
                            # Use await for async session creation
                            if await self.session_manager.create_new_session(): continue
                            else: self._logger.error("Session refresh failed."); break
                        else: self._logger.error("Payload session error (1357001) persisted."); break
                    else: # Other API errors
                        self.last_error_type = "APIError"; break

                elif 'payload' not in payload: # Structure error
                    self._logger.warning(f"Unexpected ad payload structure for {company_id}: {response_text[:500]}...")
                    self.last_error_type = "StructureError"; break
                else: # Payload Success
                    self.last_error_type = "None"
                    self._logger.debug(f"Successfully fetched/parsed ads page for {company_id} (Payload Attempt {payload_attempt})")
                    return payload

            except json.JSONDecodeError as e:
                self._logger.error(f"Failed to decode ad payload JSON for {company_id}: {e}. Response: {response_text[:500]}...")
                self.last_error_type = "JSONError"; break
            except Exception as e:
                self._logger.error(f"Unexpected error processing ad payload response for {company_id}: {e}", exc_info=self.config.get('verbose', False))
                self.last_error_type = "UnexpectedError"; break

        # --- Failed after loop ---
        self._logger.error(f"Failed to fetch valid ads page payload for {company_id} after {payload_attempt} attempts. Last error: {self.last_error_type}")
        return None