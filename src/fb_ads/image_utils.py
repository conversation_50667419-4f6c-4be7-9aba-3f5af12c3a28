# -*- coding: utf-8 -*-
# --- START OF FILE src/lib/fb_ads/image_utils.py ---
# !/usr/bin/env python3
"""
Utility functions and classes for Facebook image processing and hashing.

This module provides:
1. FBImageHashManager - A class for managing image hashes in DynamoDB
2. extract_ids_from_key - A function to extract AdArchiveID and AdCreativeID from S3 keys
3. process_single_image - Function to download, hash, and prepare an image record
4. Helper functions for image processing
"""
import argparse
import datetime  # <-- Ensure datetime is imported
import io
import logging
import os
import re
import sys
import time
from asyncio import as_completed
from collections import defaultdict
from concurrent.futures import ThreadPoolExecutor
from typing import Optional, Tuple, Dict, Any, List

import boto3
import imagehash
from PIL import Image, UnidentifiedImageError
from boto3.dynamodb.conditions import Key
from botocore.config import Config
from botocore.exceptions import ClientError, NoCredentialsError
from rich.logging import RichHandler
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TimeRemainingColumn, TimeElapsedColumn, \
    MofNCompleteColumn

# Config should be passed in, not loaded from module
# from src.lib.config import load_config
# REMOVED: from hash_fb_images import process_single_image
# Import the base manager class
from src.infrastructure.storage.dynamodb_base import DynamoDBBaseStorage as DynamoDbBaseManager

# Configure logging
logger = logging.getLogger(__name__)

# Constants
BATCH_SIZE = 25  # DynamoDB BatchWriteItem limit


# --- Helper Functions ---

def calculate_image_hash(image_content: bytes) -> Optional[imagehash.ImageHash]:
    """Calculates the perceptual hash of an image."""
    try:
        img = Image.open(io.BytesIO(image_content))
        img_hash = imagehash.phash(img)
        return img_hash
    except UnidentifiedImageError:
        logger.warning("Could not identify image format.")
        return None
    except Exception as e:
        logger.error(f"Error processing image for hashing: {e}", exc_info=True)
        return None


def extract_ids_from_key(s3_key: str, key_format: str = 'original') -> Tuple[Optional[str], Optional[str]]:
    """
    Extract AdArchiveID and AdCreativeID from an S3 key based on the specified format.

    Args:
        s3_key: The S3 key to parse
        key_format: The format of the key ('original', 'archive_id_creative_id', etc.)

    Returns:
        Tuple of (AdArchiveID, AdCreativeID), either may be None if extraction fails
    """
    if not s3_key:
        logger.debug(f"Empty S3 key provided to extract_ids_from_key")
        return None, None

    # Default values
    ad_archive_id = None
    ad_creative_id = None

    try:
        # Handle different key formats
        if key_format == 'original':
            # Example format: adarchive/fb/123456789/creative_12345.jpg
            parts = s3_key.split('/')
            logger.debug(f"Parsing key '{s3_key}' with format 'original', parts: {parts}")

            if len(parts) >= 3:
                ad_archive_id = parts[2]  # Extract archive ID from path
                logger.debug(f"Extracted AdArchiveID: {ad_archive_id}")

                # Extract creative ID from filename if possible
                if len(parts) >= 4:
                    filename = parts[3]
                    # Try different patterns for creative ID
                    patterns = [
                        r'creative_([0-9]+)',  # Standard pattern
                        r'creative([0-9]+)',  # No underscore
                        r'([0-9]+)_creative',  # Reversed order
                        r'([0-9]+)'  # Just look for numbers
                    ]

                    for pattern in patterns:
                        creative_match = re.search(pattern, filename)
                        if creative_match:
                            ad_creative_id = creative_match.group(1)
                            logger.debug(f"Extracted AdCreativeID: {ad_creative_id} using pattern {pattern}")
                            break

                    if not ad_creative_id:
                        # If no pattern matched, use a default creative ID based on the filename
                        ad_creative_id = f"default_{hash(filename) % 10000}"
                        logger.debug(f"Using default AdCreativeID: {ad_creative_id} for filename {filename}")

        elif key_format == 'archive_id_creative_id':
            # Example format: adarchive/fb/archive_123456789_creative_12345.jpg
            logger.debug(f"Parsing key '{s3_key}' with format 'archive_id_creative_id'")
            match = re.search(r'archive_([0-9]+)_creative_([0-9]+)', s3_key)
            if match:
                ad_archive_id = match.group(1)
                ad_creative_id = match.group(2)
                logger.debug(f"Extracted AdArchiveID: {ad_archive_id}, AdCreativeID: {ad_creative_id}")

        # Flexible format - try to extract any numeric IDs from the path
        if not ad_archive_id or not ad_creative_id:
            logger.debug(f"Trying flexible format extraction for '{s3_key}'")
            # Look for numeric sequences that could be IDs
            numeric_ids = re.findall(r'/([0-9]{5,})/?', s3_key)
            if numeric_ids:
                if not ad_archive_id and len(numeric_ids) > 0:
                    ad_archive_id = numeric_ids[0]
                    logger.debug(f"Flexibly extracted AdArchiveID: {ad_archive_id}")
                if not ad_creative_id and len(numeric_ids) > 1:
                    ad_creative_id = numeric_ids[1]
                    logger.debug(f"Flexibly extracted AdCreativeID: {ad_creative_id}")

    except Exception as e:
        logger.error(f"Error extracting IDs from S3 key '{s3_key}': {e}")
        return None, None

    # If we still don't have a creative ID but have an archive ID, generate a default one
    if ad_archive_id and not ad_creative_id:
        ad_creative_id = f"default_{hash(s3_key) % 10000}"
        logger.debug(f"Generated default AdCreativeID: {ad_creative_id} for key with AdArchiveID: {ad_archive_id}")

    return ad_archive_id, ad_creative_id


# --- MOVED FUNCTION DEFINITION HERE ---
def process_single_image(
        s3_client,  # Use the passed client instance
        bucket_name: str,
        s3_key: str,
        ad_archive_id: str,
        ad_creative_id: str,
        hash_manager  # hash_manager is passed but not used in this implementation - can be removed if not needed later
) -> Optional[Dict[str, Any]]:
    """
    Downloads an image from S3, calculates its hash, and returns a record dictionary.

    Args:
        s3_client: Initialized S3 client.
        bucket_name: S3 bucket name.
        s3_key: S3 key of the image.
        ad_archive_id: AdArchiveID associated with the image.
        ad_creative_id: AdCreativeID associated with the image.
        hash_manager: FBImageHashManager instance (currently unused).

    Returns:
        A dictionary representing the hash record, or None if processing fails.
    """
    image_content = None
    try:
        logger.debug(f"Processing S3 key: {s3_key} (Archive: {ad_archive_id}, Creative: {ad_creative_id})")
        response = s3_client.get_object(Bucket=bucket_name, Key=s3_key)
        image_content = response['Body'].read()
        logger.debug(f"Downloaded {len(image_content)} bytes for {s3_key}")
    except ClientError as e:
        if e.response['Error']['Code'] == 'NoSuchKey':
            logger.warning(f"S3 key not found: {s3_key}")
        elif e.response['Error']['Code'] == 'AccessDenied':
            logger.error(f"S3 access denied for key: {s3_key}")
        else:
            logger.error(f"S3 ClientError downloading {s3_key}: {e}")
        return None
    except Exception as e:
        logger.error(f"Error downloading {s3_key}: {e}", exc_info=True)
        return None

    if not image_content:
        logger.warning(f"No image content downloaded for {s3_key}")
        return None

    # Calculate hash
    image_hash_val = calculate_image_hash(image_content)
    if image_hash_val is None:
        logger.warning(f"Failed to calculate hash for {s3_key}. Skipping record.")
        return None

    hash_str = str(image_hash_val)
    timestamp = datetime.datetime.utcnow().isoformat()

    # Create record
    record = {
        'PHash': hash_str,  # Partition Key (CORRECT NAME)
        'AdArchiveID': ad_archive_id,  # Sort Key
        'AdCreativeID': ad_creative_id,
        'S3Key': s3_key,
        'LastUpdated': timestamp,
        # Initialize ImageText as empty string - it will be populated later
        'ImageText': '',
        # Other fields can be added later if needed
    }
    logger.debug(f"Generated record for {s3_key}: PHash={hash_str}, AdArchiveID={ad_archive_id}")
    return record


# --- END MOVED FUNCTION ---


class FBImageHashManager(DynamoDbBaseManager):
    """
    Manager for Facebook image hash data in DynamoDB.
    Extends DynamoDbBaseManager with specific functionality for image hash records.
    """

    def __init__(self, table_name: str, config: Dict[str, Any] = None, region_name: str = None,
                 use_local: bool = False, local_port: int = 8000, remove_empty_str: bool = True):
        """
        Initialize the FBImageHashManager.

        Args:
            table_name: Name of the DynamoDB table
            config: Configuration dictionary
            region_name: AWS region name (can be provided in config instead)
            use_local: Whether to use a local DynamoDB instance
            local_port: Port for local DynamoDB instance
            remove_empty_str: Whether to remove empty strings during record processing
        """
        # If config is not provided, create a minimal one
        if config is None:
            config = {}

        # Ensure region_name is in the config
        if 'region_name' not in config and region_name is not None:
            config['region_name'] = region_name
        elif 'region_name' not in config:
            # Get region from environment or use default
            config['region_name'] = (
                os.getenv('AWS_REGION') or 
                os.getenv('LEXGENIUS_AWS_REGION') or
                os.getenv('REGION_NAME') or
                'us-west-2'
            )

        # Ensure dynamodb configuration exists
        if 'dynamodb' not in config:
            config['dynamodb'] = {}

        # Ensure fb_image_hash_table_name is in the dynamodb config
        if 'fb_image_hash_table_name' not in config['dynamodb']:
            config['dynamodb']['fb_image_hash_table_name'] = table_name

        # Initialize the base manager
        super().__init__(config, table_name, use_local)

        # Set specific attributes for this manager
        self.remove_empty_str = remove_empty_str

        # Set key names based on the table configuration
        self.pk_name = 'PHash'  # Correct primary key name
        self.sk_name = 'AdArchiveID'

    def table_exists(self) -> bool:
        """
        Check if the DynamoDB table exists.

        Returns:
            bool: True if the table exists, False otherwise
        """
        try:
            self.table.load()
            return True
        except ClientError as e:
            if e.response['Error']['Code'] == 'ResourceNotFoundException':
                return False
            # Re-raise other errors
            raise

    def filter_record(self, record: Dict[str, Any], no_filter: bool = False) -> Dict[str, Any]:
        """
        Filter a record based on include/exclude attributes.
        Optionally remove empty strings based on the remove_empty_str setting.

        Args:
            record: The record to filter
            no_filter: If True, bypass filtering

        Returns:
            The filtered record
        """
        # First apply the base class filtering
        filtered = super().filter_record(record, no_filter)

        # Then remove empty strings if configured to do so
        if self.remove_empty_str:
            return {k: v for k, v in filtered.items() if v != ''}

        return filtered

    def process_records(self, records: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Process DynamoDB records - simply return them as-is.
        No filtering should be applied when searching by PHash.
        
        Args:
            records: Raw DynamoDB records
            
        Returns:
            The records as-is
        """
        return records if records else []

    def get_record_by_phash(self, phash: str) -> Optional[Dict[str, Any]]:
        """
        Get image hash record by PHash (primary key).
        
        Args:
            phash: The PHash value to query
            
        Returns:
            The image hash record if found, None otherwise
        """
        try:
            response = self.table.get_item(
                Key={'PHash': phash}
            )
            item = response.get('Item')
            return self.process_records([item])[0] if item else None
            
        except ClientError as e:
            logger.error(f"Error getting record by PHash '{phash}': {e}")
            return None

    def query_by_hash(self, hash_str: str) -> List[Dict[str, Any]]:
        """
        Query the table for records with a specific ImageHash value.
        Includes exponential backoff retry for ProvisionedThroughputExceededException.

        Args:
            hash_str: The perceptual hash string to query

        Returns:
            List of matching hash records
        """
        import random
        
        max_retries = 5
        base_delay = 1.0  # Start with 1 second
        max_delay = 30.0  # Cap at 30 seconds
        
        for attempt in range(max_retries + 1):
            try:
                response = self.table.query(
                    KeyConditionExpression=boto3.dynamodb.conditions.Key(self.pk_name).eq(hash_str)
                )
                return self.process_records(response.get('Items', []))
                
            except ClientError as e:
                error_code = e.response.get('Error', {}).get('Code', '')
                
                if error_code == 'ProvisionedThroughputExceededException' and attempt < max_retries:
                    # Calculate delay with exponential backoff and jitter
                    delay = min(base_delay * (2 ** attempt), max_delay)
                    jitter = random.uniform(0, 0.1 * delay)  # Add up to 10% jitter
                    total_delay = delay + jitter
                    
                    logger.warning(
                        f"ProvisionedThroughputExceededException querying by ImageHash '{hash_str}', "
                        f"attempt {attempt + 1}/{max_retries + 1}. Retrying in {total_delay:.2f}s..."
                    )
                    time.sleep(total_delay)
                    continue
                else:
                    logger.error(f"Error querying by ImageHash '{hash_str}': {e}")
                    return []
        
        logger.error(f"Failed to query by ImageHash '{hash_str}' after {max_retries + 1} attempts")
        return []

    def put_item(self, item: Dict[str, Any]) -> bool:
        """
        Put a single item into the DynamoDB table.
        Includes exponential backoff retry for ProvisionedThroughputExceededException.

        Args:
            item: The item to put into the table

        Returns:
            True if successful, False otherwise
        """
        import random
        
        # Ensure required keys are present
        if self.pk_name not in item or self.sk_name not in item:
            logger.error(f"Missing required keys in item: {self.pk_name} or {self.sk_name}")
            return False
        
        max_retries = 5
        base_delay = 1.0  # Start with 1 second
        max_delay = 30.0  # Cap at 30 seconds
        
        for attempt in range(max_retries + 1):
            try:
                # Put the item
                self.table.put_item(Item=item)
                return True
                
            except ClientError as e:
                error_code = e.response.get('Error', {}).get('Code', '')
                
                if error_code == 'ProvisionedThroughputExceededException' and attempt < max_retries:
                    # Calculate delay with exponential backoff and jitter
                    delay = min(base_delay * (2 ** attempt), max_delay)
                    jitter = random.uniform(0, 0.1 * delay)  # Add up to 10% jitter
                    total_delay = delay + jitter
                    
                    logger.warning(
                        f"ProvisionedThroughputExceededException putting item with {self.pk_name}='{item.get(self.pk_name)}', "
                        f"attempt {attempt + 1}/{max_retries + 1}. Retrying in {total_delay:.2f}s..."
                    )
                    time.sleep(total_delay)
                    continue
                else:
                    logger.error(f"Error putting item into table: {e}")
                    return False
        
        logger.error(f"Failed to put item with {self.pk_name}='{item.get(self.pk_name)}' after {max_retries + 1} attempts")
        return False

    def update_image_text(
            self,
            phash: str,
            ad_archive_id: str,  # Assuming AdArchiveID is the Sort Key
            image_text: str
    ) -> bool:
        """
        Updates the ImageText attribute for a SPECIFIC item identified by
        its full primary key (PHash and AdArchiveID).

        Args:
            phash: The Partition Key (PHash) of the item.
            ad_archive_id: The Sort Key (AdArchiveID) of the item.
            image_text: The ImageText value to set.

        Returns:
            True if update was successful, False otherwise.
        """
        if not phash or not ad_archive_id:
            logger.error(f"Cannot update ImageText: PHash ('{phash}') or AdArchiveID ('{ad_archive_id}') is missing.")
            return False
        if image_text is None:
            logger.warning(
                f"Received None for ImageText for PHash {phash}, AdArchiveID {ad_archive_id}. Storing empty string.")
            image_text = ""  # Store empty string instead of None if desired

        import random
        
        max_retries = 5
        base_delay = 1.0  # Start with 1 second
        max_delay = 30.0  # Cap at 30 seconds
        
        logger.debug(f"Attempting to update ImageText for PHash={phash}, AdArchiveID={ad_archive_id}")
        
        for attempt in range(max_retries + 1):
            timestamp = datetime.datetime.utcnow().isoformat()
            try:
                response = self.table.update_item(
                    Key={
                        self.pk_name: phash,  # Use self.pk_name ('PHash')
                        self.sk_name: ad_archive_id  # Use self.sk_name ('AdArchiveID')
                    },
                    UpdateExpression='SET ImageText = :it, LastUpdated = :lu',
                    ExpressionAttributeValues={
                        ':it': image_text,
                        ':lu': timestamp
                    },
                    # Ensure the item exists before updating
                    ConditionExpression=f"attribute_exists({self.pk_name}) AND attribute_exists({self.sk_name})",
                    ReturnValues='NONE'  # Don't need old/new values back
                )
                logger.info(
                    f"Successfully updated ImageText for PHash={phash}, AdArchiveID={ad_archive_id}. Status: {response.get('ResponseMetadata', {}).get('HTTPStatusCode', 'N/A')}")
                return True
                
            except ClientError as e:
                error_code = e.response.get('Error', {}).get('Code', '')
                
                # Handle specific non-retryable errors
                if error_code == "ConditionalCheckFailedException":
                    logger.warning(
                        f"Did not update ImageText for PHash {phash}, AdArchiveID {ad_archive_id}: Item does not exist (or condition failed).")
                    return False  # Indicate update didn't happen because item wasn't found
                
                # Handle retryable throughput errors
                elif error_code == 'ProvisionedThroughputExceededException' and attempt < max_retries:
                    # Calculate delay with exponential backoff and jitter
                    delay = min(base_delay * (2 ** attempt), max_delay)
                    jitter = random.uniform(0, 0.1 * delay)  # Add up to 10% jitter
                    total_delay = delay + jitter
                    
                    logger.warning(
                        f"ProvisionedThroughputExceededException updating ImageText for PHash={phash}, AdArchiveID={ad_archive_id}, "
                        f"attempt {attempt + 1}/{max_retries + 1}. Retrying in {total_delay:.2f}s..."
                    )
                    time.sleep(total_delay)
                    continue
                else:
                    logger.error(
                        f"DynamoDB ClientError updating ImageText for PHash {phash}, AdArchiveID {ad_archive_id}: {error_code} - {e.response.get('Error', {}).get('Message', '')}")
                    return False
                    
            except Exception as e:
                logger.error(f"Unexpected error updating ImageText for PHash {phash}, AdArchiveID {ad_archive_id}: {e}",
                             exc_info=True)
                return False
        
        logger.error(f"Failed to update ImageText for PHash={phash}, AdArchiveID={ad_archive_id} after {max_retries + 1} attempts")
        return False

    def update_image_text_for_all_phash_matches(
            self,
            phash: str,
            image_text: str,
            original_ad_archive_id: Optional[str] = None  # Pass the ID of the item you *just* updated
    ) -> Tuple[int, int]:
        """
        Finds all items with the given PHash and updates their ImageText,
        leaving other attributes untouched.

        Args:
            phash: The PHash value to query for.
            image_text: The ImageText value to set.
            original_ad_archive_id: The AdArchiveID of the item that was initially
                                     updated (to avoid re-updating it unnecessarily).

        Returns:
            Tuple (updated_count, failed_count)
        """
        if not phash:
            logger.error("Cannot propagate ImageText: PHash is missing.")
            return 0, 0
        # Allow empty string, but handle None safety
        if image_text is None:
            logger.warning(f"ImageText is None for propagation to PHash {phash}. Setting to empty string.")
            image_text = ""

        logger.info(
            f"Propagating ImageText for PHash: {phash} (excluding original AdArchiveID: {original_ad_archive_id})")
        updated_count = 0
        failed_count = 0
        items_to_update = []

        # 1. Query for all items with the same PHash
        try:
            # Only fetch the primary keys (PK and SK) as that's all we need for the update
            response = self.table.query(
                KeyConditionExpression=Key(self.pk_name).eq(phash),
                ProjectionExpression=f"{self.pk_name}, {self.sk_name}"  # Fetch only PK and SK
            )
            items_to_update = response.get('Items', [])
            logger.debug(f"Query found {len(items_to_update)} items matching PHash {phash}")

            # Handle pagination if necessary
            while 'LastEvaluatedKey' in response:
                logger.info(f"Query for PHash {phash} paginating...")
                response = self.table.query(
                    KeyConditionExpression=Key(self.pk_name).eq(phash),
                    ProjectionExpression=f"{self.pk_name}, {self.sk_name}",  # Fetch only PK and SK
                    ExclusiveStartKey=response['LastEvaluatedKey']
                )
                items_to_update.extend(response.get('Items', []))
                logger.debug(f"Found {len(response.get('Items', []))} more items, total {len(items_to_update)}")

        except ClientError as e:
            logger.error(
                f"DynamoDB ClientError querying for PHash {phash}: {e.response['Error']['Code']} - {e.response['Error']['Message']}")
            return 0, len(items_to_update)  # Assume all potential updates failed if query failed
        except Exception as e:
            logger.error(f"Unexpected error querying for PHash {phash}: {e}", exc_info=True)
            return 0, len(items_to_update)

        if not items_to_update:
            logger.info(f"No items found for PHash {phash} during propagation query.")
            return 0, 0

        # 2. Loop and Update each item individually
        timestamp = datetime.datetime.utcnow().isoformat()
        for item in items_to_update:
            item_phash = item.get(self.pk_name)
            item_ad_archive_id = item.get(self.sk_name)  # Get the specific Sort Key (AdArchiveID)

            # --- Validation ---
            if not item_phash or not item_ad_archive_id:
                logger.warning(f"Skipping update for item missing primary key components: {item}")
                failed_count += 1
                continue

            # --- Skip the original item if its AdArchiveID was provided ---
            if original_ad_archive_id and item_ad_archive_id == original_ad_archive_id:
                logger.debug(f"Skipping update for original item: PHash={item_phash}, AdArchiveID={item_ad_archive_id}")
                continue

            # --- Perform targeted update ---
            logger.debug(f"Attempting targeted update for PHash={item_phash}, AdArchiveID={item_ad_archive_id}")
            try:
                update_response = self.table.update_item(
                    Key={
                        self.pk_name: item_phash,  # Partition Key from item
                        self.sk_name: item_ad_archive_id  # Sort Key from item
                    },
                    # ONLY SET the desired attributes
                    UpdateExpression='SET ImageText = :it, LastUpdated = :lu',
                    ExpressionAttributeValues={
                        ':it': image_text,
                        ':lu': timestamp
                    },
                    # Optional: Condition to ensure item still exists (belt & suspenders)
                    ConditionExpression=f"attribute_exists({self.pk_name}) AND attribute_exists({self.sk_name})",
                    ReturnValues='NONE'  # Don't need updated values back
                )
                logger.debug(
                    f"Update successful for AdArchiveID {item_ad_archive_id} with PHash {item_phash}. Status: {update_response.get('ResponseMetadata', {}).get('HTTPStatusCode', 'N/A')}")
                updated_count += 1
            except ClientError as e:
                # Handle potential condition check failure if added
                if e.response['Error']['Code'] == "ConditionalCheckFailedException":
                    logger.warning(
                        f"Did not update PHash {item_phash}, AdArchiveID {item_ad_archive_id}: Item no longer exists or condition failed.")
                else:
                    logger.error(
                        f"DynamoDB ClientError updating PHash {item_phash}, AdArchiveID {item_ad_archive_id}: {e.response['Error']['Code']} - {e.response['Error']['Message']}")
                failed_count += 1
            except Exception as e:
                logger.error(f"Unexpected error updating PHash {item_phash}, AdArchiveID {item_ad_archive_id}: {e}",
                             exc_info=True)
                failed_count += 1

        logger.info(
            f"Finished propagating ImageText for PHash {phash}. Updated: {updated_count}, Failed/Skipped: {failed_count}")
        return updated_count, failed_count


def process_images(
        config: Dict[str, Any],
        bucket_name: str,  # Bucket name is now passed in directly
        use_local_db: bool,
        local_db_port: int,
        max_workers: int,
        limit: Optional[int] = None
):
    """
    Lists images in the specified S3 prefix, calculates hashes,
    and stores them in DynamoDB. Focuses on images within multi-creative archives.
    """
    s3_prefix = config.get('s3_fb_ad_archive_prefix', 'adarchive/fb/')
    expected_key_format = config.get('s3_key_format', 'original')  # e.g., 'original' or 'archive_id_creative_id'

    logger.info(f"Starting processing for bucket='{bucket_name}', prefix='{s3_prefix}'")  # Use the passed bucket_name
    logger.info(f"Using {'local' if use_local_db else 'AWS'} DynamoDB. Workers={max_workers}, BatchSize={BATCH_SIZE}")
    logger.info(f"Expecting S3 key format: '{expected_key_format}'")

    # --- Initialize S3 and DynamoDB Clients/Managers ---
    s3_client = None
    hash_manager = None
    try:
        # *** Configure S3 client with increased connection pool ***
        s3_config = Config(max_pool_connections=max_workers + 5)  # Adjust pool size based on workers

        if use_local_db:
            logger.info(
                f"Initializing LOCAL mode for table '{config['dynamodb']['fb_image_hash_table_name']}' port {local_db_port}")
            aws_profile = config.get('aws_profile')
            session = boto3.Session(profile_name=aws_profile) if aws_profile else boto3.Session()
            s3_client = session.client('s3', region_name=config['aws_region'], config=s3_config)  # Pass config

            hash_manager = FBImageHashManager(
                table_name=config['dynamodb']['fb_image_hash_table_name'],
                config=config,  # Pass the main config dictionary
                use_local=use_local_db,
                local_port=local_db_port,
                remove_empty_str=False  # Explicitly prevent removing empty keys
            )
        else:
            # Production DynamoDB and S3
            logger.info(
                f"Initializing AWS mode for table '{config['dynamodb']['fb_image_hash_table_name']}' region '{config['aws_region']}'")
            aws_profile = config.get('aws_profile')
            session = boto3.Session(profile_name=aws_profile) if aws_profile else boto3.Session()
            s3_client = session.client('s3', region_name=config['aws_region'], config=s3_config)  # Pass config

            hash_manager = FBImageHashManager(
                table_name=config['dynamodb']['fb_image_hash_table_name'],
                config=config,  # Pass the main config dictionary
                use_local=False,
                remove_empty_str=False  # Explicitly prevent removing empty keys
            )

        # Ensure table exists (applies to both local and AWS)
        if not hash_manager.table_exists():
            logger.error(f"DynamoDB table '{hash_manager.table_name}' not found or accessible.")
            return  # Exit processing if table isn't there

    except NoCredentialsError:
        logger.error(
            "AWS credentials not found. Configure credentials (e.g., ~/.aws/credentials, environment variables, IAM role).")
        sys.exit(1)
    except ClientError as e:
        logger.error(f"AWS ClientError during initialization: {e}", exc_info=True)
        sys.exit(1)
    except Exception as e:
        logger.error(f"Failed to initialize AWS clients or DynamoDB manager: {e}", exc_info=True)
        sys.exit(1)

    # --- List S3 Objects and Identify Multi-Creative Archives ---
    logger.info("Listing S3 objects to identify archives...")
    paginator = s3_client.get_paginator('list_objects_v2')
    objects_by_archive_id = defaultdict(list)
    all_ad_archive_ids = set()
    processed_object_count = 0  # Renamed for clarity
    start_time = time.time()

    try:
        for page in paginator.paginate(Bucket=bucket_name, Prefix=s3_prefix):
            if 'Contents' not in page:
                continue
            for obj in page['Contents']:
                s3_key = obj['Key']
                # Basic filtering for common image/video types
                if not any(s3_key.lower().endswith(ext) for ext in
                           ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.mp4', '.mov']):
                    continue

                ad_archive_id, ad_creative_id = extract_ids_from_key(s3_key, expected_key_format)

                # Stricter validation for extracted IDs
                is_valid_archive_id = ad_archive_id and isinstance(ad_archive_id, str) and len(
                    ad_archive_id.strip()) > 0
                is_valid_creative_id = ad_creative_id and isinstance(ad_creative_id, str) and len(
                    ad_creative_id.strip()) > 0

                if not is_valid_archive_id or not is_valid_creative_id:
                    logger.debug(  # Use debug level for potentially noisy logs
                        f"Could not extract valid, non-empty ArchiveID ('{ad_archive_id}') "
                        f"and CreativeID ('{ad_creative_id}') from S3 key: {s3_key}. Skipping."
                    )
                    continue

                all_ad_archive_ids.add(ad_archive_id)
                objects_by_archive_id[ad_archive_id].append({'key': s3_key, 'creative_id': ad_creative_id})
                processed_object_count += 1

                if limit and processed_object_count >= limit:
                    logger.info(f"Reached processing limit of {limit} S3 objects.")
                    break  # Stop processing objects
            if limit and processed_object_count >= limit:
                break  # Stop paginating

    except ClientError as e:
        logger.error(f"Error listing S3 objects: {e}", exc_info=True)
        return
    except Exception as e:
        logger.error(f"Unexpected error during S3 listing: {e}", exc_info=True)
        return

    elapsed_time = time.time() - start_time
    logger.info(
        f"Found {len(all_ad_archive_ids)} unique AdArchiveIDs from {processed_object_count} listed objects in {elapsed_time:.2f}s.")

    # Identify archives with more than one creative (potential for different hashes)
    multi_creative_archives = {
        archive_id for archive_id, objects in objects_by_archive_id.items() if len(objects) > 1
    }
    logger.info(f"Found {len(multi_creative_archives)} AdArchiveIDs with more than one creative.")

    # Create list of images to process (only those in multi-creative archives)
    images_to_process = []
    for archive_id in multi_creative_archives:
        images_to_process.extend(objects_by_archive_id[archive_id])

    if not images_to_process:
        logger.info("No images found belonging to multi-creative archives. Exiting.")
        return

    logger.info(f"Identified {len(images_to_process)} images belonging to multi-creative archives to process.")

    # --- Process Images Concurrently ---
    hashes_to_write = []
    processed_image_count = 0
    failed_image_count = 0
    future_to_info: Dict[Any, str] = {}  # Map future to s3_key for logging

    # Setup Rich progress bar
    with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            MofNCompleteColumn(),
            TimeElapsedColumn(),
            TimeRemainingColumn(),
            transient=False  # Keep progress bar after completion
    ) as progress:

        task_id = progress.add_task("Processing images", total=len(images_to_process))

        try:
            # Use ThreadPoolExecutor for I/O bound tasks (downloading from S3)
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                # Submit tasks
                for image_info in images_to_process:
                    s3_key = image_info['key']
                    # Re-extract archive_id to ensure consistency, or retrieve if stored reliably
                    extracted_archive_id, _ = extract_ids_from_key(s3_key, expected_key_format)
                    ad_creative_id = image_info['creative_id']

                    # Double check IDs before submitting (belt and suspenders)
                    if not extracted_archive_id or not ad_creative_id:
                        logger.warning(
                            f"Skipping submission for key {s3_key} due to missing IDs in image_info or re-extraction.")
                        progress.update(task_id, advance=1)  # Advance progress even if skipped
                        failed_image_count += 1
                        continue

                    # *** UPDATED CALL: Pass hash_manager=None or adjust args if needed ***
                    future = executor.submit(
                        process_single_image,  # Function is now defined in this file
                        s3_client, bucket_name, s3_key, extracted_archive_id, ad_creative_id,
                        None  # Pass None for hash_manager as it's not used by the moved function
                    )
                    future_to_info[future] = s3_key  # Store s3_key for context on error/completion

                # Process results as they complete
                for future in as_completed(future_to_info):
                    s3_key = future_to_info[future]
                    try:
                        image_hash_record = future.result()
                        if image_hash_record:
                            hashes_to_write.append(image_hash_record)
                            processed_image_count += 1

                            # Write to DynamoDB in batches
                            if len(hashes_to_write) >= BATCH_SIZE:
                                logger.debug(f"Writing batch of {len(hashes_to_write)} hashes to DynamoDB...")
                                success_count, failure_count = hash_manager.batch_insert_items(hashes_to_write)
                                logger.info(
                                    f"Batch write result: Successful={success_count}, Failed/Skipped={failure_count}")
                                if failure_count > 0:
                                    logger.warning(f"{failure_count} items failed pre-processing or batch write.")
                                hashes_to_write = []  # Clear batch
                        else:
                            # Hash calculation or download failed (already logged in process_single_image)
                            failed_image_count += 1

                    except Exception as exc:
                        logger.error(f"Error processing future for S3 key {s3_key}: {exc}", exc_info=True)
                        failed_image_count += 1
                    finally:
                        progress.update(task_id, advance=1)  # Advance progress bar for each completed future

            # Write any remaining hashes after the loop finishes
            if hashes_to_write:
                logger.info(f"Writing final batch of {len(hashes_to_write)} hashes to DynamoDB...")
                success_count, failure_count = hash_manager.batch_insert_items(hashes_to_write)
                logger.info(f"Final batch write result: Successful={success_count}, Failed/Skipped={failure_count}")
                if failure_count > 0:
                    logger.warning(f"{failure_count} items failed pre-processing or batch write in final batch.")

        except KeyboardInterrupt:
            logger.warning(
                "Keyboard interrupt received. Shutting down workers and attempting to write remaining hashes...")
            # Executor shutdown happens automatically in __exit__
            if hashes_to_write:
                logger.info(f"Writing final batch of {len(hashes_to_write)} hashes due to interrupt...")
                success_count, failure_count = hash_manager.batch_insert_items(hashes_to_write)
                logger.info(f"Final batch write result: Successful={success_count}, Failed/Skipped={failure_count}")
            logger.info("Processing stopped by user.")
            # Allow finally block to run
        except Exception as e:
            logger.error(f"An unexpected error occurred during concurrent processing: {e}", exc_info=True)
            # Allow finally block to run
        finally:
            # Ensure progress bar stops cleanly
            if not progress.finished:
                progress.update(task_id, completed=progress.tasks[task_id].completed)

    logger.info(f"Finished processing.")
    logger.info(f"Successfully processed and attempted write for {processed_image_count} images.")
    logger.info(f"Failed to process/hash {failed_image_count} images.")


# --- Main Execution ---
if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Calculate perceptual hashes for FB ad images in S3 and store in DynamoDB.")
    parser.add_argument("--local-db", action='store_true', help="Use local DynamoDB instance.")
    parser.add_argument("--db-port", type=int, default=8000, help="Port for local DynamoDB instance.")
    parser.add_argument("--workers", type=int, default=os.cpu_count() or 4,
                        help="Number of concurrent workers for processing.")
    parser.add_argument("--limit", type=int, default=None,
                        help="Limit the number of S3 objects listed for processing (for testing).")
    parser.add_argument("--debug", action='store_true', help="Enable debug logging.")

    args = parser.parse_args()

    # Set up basic logging immediately for RichHandler compatibility
    logging.basicConfig(
        level="INFO",
        format="%(message)s",
        datefmt="[%X]",
        handlers=[RichHandler(rich_tracebacks=True, show_path=False)]
    )
    logger = logging.getLogger()  # Get root logger

    if args.debug:
        logger.setLevel(logging.DEBUG)
        for handler in logger.handlers:
            if isinstance(handler, RichHandler):
                handler.setLevel(logging.DEBUG)
        logger.debug("Debug logging enabled.")
    else:
        # Keep libraries quieter in INFO mode
        logging.getLogger("botocore").setLevel(logging.WARNING)
        logging.getLogger("boto3").setLevel(logging.WARNING)
        logging.getLogger("urllib3").setLevel(logging.WARNING)
        logging.getLogger("PIL").setLevel(logging.WARNING)

    # --- Load Configuration ---
    try:
        # Assuming load_config doesn't strictly need a date for this script's purpose
        # Config should be passed in or use defaults
        # config = load_config('01/01/70')  # Using default date
        # Get region from environment or use default
        config = {'aws_region': (
            os.getenv('AWS_REGION') or 
            os.getenv('LEXGENIUS_AWS_REGION') or
            os.getenv('REGION_NAME') or
            'us-west-2'
        )}
        logger.warning("Using default config - load_config has been removed")
        if not config.get('dynamodb', {}).get('fb_image_hash_table_name'):
            raise ValueError("Missing 'dynamodb.fb_image_hash_table_name' in config.")
    except FileNotFoundError:
        logger.error(f"Configuration file not found. Ensure config exists at expected location for load_config.")
        sys.exit(1)
    except ValueError as ve:
        logger.error(f"Configuration Error: {ve}")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Failed to load configuration: {e}", exc_info=True)
        sys.exit(1)

    # --- Determine Bucket Name (from config ONLY) ---
    config_bucket_key = 'bucket_name'
    bucket_to_use = config.get(config_bucket_key)
    if not bucket_to_use:
        logger.error(f"S3 bucket name must be defined as '{config_bucket_key}' in the configuration file.")
        sys.exit(1)
    else:
        logger.info(f"Using bucket name '{bucket_to_use}' from configuration key '{config_bucket_key}'.")

    # --- Validate Worker Count ---
    if args.workers <= 0:
        logger.error("Number of workers must be positive.")
        sys.exit(1)
    if args.workers > 100:  # Example threshold
        logger.warning(f"High number of workers ({args.workers}) requested. Ensure sufficient system resources.")

    # --- Run Processing ---
    process_images(
        config=config,
        bucket_name=bucket_to_use,  # Use the bucket name from config
        use_local_db=args.local_db,
        local_db_port=args.db_port,
        max_workers=args.workers,
        limit=args.limit,
    )

