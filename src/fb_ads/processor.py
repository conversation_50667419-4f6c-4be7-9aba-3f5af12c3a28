import re
import os
from typing import Dict, Any, Optional, List, TYPE_CHECKING

import requests  # Make sure requests is imported if used by _check_cdn_url_accessible
from bs4 import BeautifulSoup

# Use TYPE_CHECKING to avoid circular import
if TYPE_CHECKING:
    from src.services.ai.ai_orchestrator import AIOrchestrator
from src.utils.date import DateUtils
from .vector_clusterer import VectorClusterer

from .image_handler import ImageHandler
from .local_image_queue import LocalImageQueue
import logging
import json
import contextlib
from tqdm import tqdm  # Ensure tqdm is imported
import asyncio  # Ensure asyncio is imported


class AdProcessor:
    """Processes raw ad data into structured format and coordinates AI enhancement and rule-based campaign tagging."""

    def __init__(self, config: Dict[str, Any],
                 image_handler: ImageHandler,
                 ai_integrator: 'AIOrchestrator',
                 vector_clusterer: VectorClusterer,  # Add VectorClusterer instance
                 current_process_date: str,
                 fb_ad_db: Any = None,  # Add fb_ad_db parameter
                 s3_manager: Any = None):  # Add s3_manager parameter
        self.config = config
        self.image_handler = image_handler
        self.ai_integrator = ai_integrator
        self.vector_clusterer = vector_clusterer  # Store the instance
        self.current_process_date = current_process_date  # YYYYMMDD
        self._logger = logging.getLogger('src.fb_ads')
        self.fb_ad_db = fb_ad_db  # Accept as parameter
        self.s3_manager = s3_manager  # Accept as parameter
        self.use_tqdm_flag = self.config.get('use_tqdm', True)
        
        # Initialize local queue if deferred processing is enabled
        # Check multiple config sources
        self.defer_image_processing = (
            self.config.get('defer_image_processing', False) or
            self.config.get('processing', {}).get('defer_image_processing', False) or
            os.environ.get('DEFER_IMAGE_PROCESSING', '').lower() == 'true'
        )
        
        self.local_queue = None
        if self.defer_image_processing:
            # Get queue directory from config hierarchy
            queue_dir = (
                self.config.get('image_queue_dir') or
                self.config.get('processing', {}).get('image_queue', {}).get('directory') or
                self.config.get('image_queue', {}).get('directory') or
                './data/image_queue'
            )
            self.local_queue = LocalImageQueue(queue_dir)
            self._logger.info(f"Local image queue initialized at {queue_dir}")
        
        self._logger.info(
            f"AdProcessor initialized. VectorClusterer instance {'provided' if vector_clusterer else 'NOT provided'}. "
            f"Deferred image processing: {self.defer_image_processing}")

    async def process_raw_ads(self, raw_ad_groups: List[List[Dict[str, Any]]], law_firm_name: str) -> List[
        Dict[str, Any]]:
        """
        Processes ad groups. Structures raw data, handles images, enhances with AI (summary, LLM classification),
        and then applies rule-based campaign categorization using VectorClusterer.
        """
        processed_ads_for_ai = []
        ad_ids_processed = set()
        logger = getattr(self, '_logger', logging.getLogger(__name__))

        logger.info(f"AdProcessor: Processing {len(raw_ad_groups)} ad groups for {law_firm_name}...")
        if not raw_ad_groups:
            logger.warning("AdProcessor: Received empty raw_ad_groups list.")
            return []

        pbar_desc = f"Structuring {law_firm_name[:20]}"
        use_tqdm = getattr(self, 'use_tqdm_flag', True)
        progress_context = tqdm(total=len(raw_ad_groups), desc=pbar_desc, leave=False, disable=not use_tqdm,
                                unit="group") \
            if use_tqdm else contextlib.nullcontext()

        structuring_or_input_count = 0

        with progress_context as pbar_struct:
            for group_index, ad_group in enumerate(raw_ad_groups):
                if pbar_struct is not None and hasattr(pbar_struct, 'update'): pbar_struct.update(1)
                log_prefix_group = f"AdGroup {group_index + 1}/{len(raw_ad_groups)} ({law_firm_name}): "

                if not isinstance(ad_group, list) or not ad_group:
                    logger.warning(f"{log_prefix_group}Skipping - group is not a non-empty list.")
                    continue
                ad_raw_input = ad_group[0]
                if not isinstance(ad_raw_input, dict):
                    logger.warning(f"{log_prefix_group}Skipping - first item is not a dictionary.")
                    continue

                is_already_structured = ('start_date' in ad_raw_input and
                                         (
                                                     'original_image_url' in ad_raw_input or 'video_preview_image_url' in ad_raw_input) and
                                         'snapshot' not in ad_raw_input)

                ad_dict_structured = None
                if is_already_structured:
                    logger.debug(f"{log_prefix_group}Input dictionary appears pre-structured.")
                    ad_dict_structured = ad_raw_input
                    if ad_dict_structured.get('law_firm') != law_firm_name:
                        ad_dict_structured['law_firm'] = law_firm_name
                else:
                    logger.debug(f"{log_prefix_group}Input dictionary appears raw. Calling _create_ad_dictionary.")
                    ad_dict_structured = self._create_ad_dictionary(ad_raw_input, law_firm_name)

                if not ad_dict_structured:
                    logger.warning(f"{log_prefix_group}Skipping - Failed to obtain structured ad dictionary.")
                    continue

                ad_archive_id = ad_dict_structured.get('ad_archive_id')
                if not ad_archive_id:
                    logger.error(f"{log_prefix_group}CRITICAL: ad_archive_id missing after structuring. Skipping.")
                    continue
                log_prefix_ad = f"Ad {ad_archive_id} (Grp {group_index + 1}, {law_firm_name}): "

                if ad_archive_id in ad_ids_processed:
                    logger.debug(f"{log_prefix_ad}Skipping duplicate ad_archive_id.")
                    continue
                ad_ids_processed.add(ad_archive_id)

                ad_creative_id = ad_dict_structured.get('ad_creative_id')
                source_image_url_for_handler = ad_dict_structured.get('original_image_url') or ad_dict_structured.get(
                    'video_preview_image_url')

                if ad_creative_id and source_image_url_for_handler:
                    logger.debug(
                        f"{log_prefix_ad}Calling ImageHandler for creative {ad_creative_id} (URL: {source_image_url_for_handler[:60]}...)")
                    try:
                        # Always upload the image to S3 first
                        s3_key_result, s3_exists_initial = await self.image_handler.process_and_upload_ad_image(
                            ad_archive_id, ad_creative_id, source_image_url_for_handler
                        )
                        ad_dict_structured['s3_image_key'] = s3_key_result
                        ad_dict_structured['_s3_exists_initial'] = s3_exists_initial  # Keep temporary flag
                        
                        # If deferred processing is enabled and we have a valid S3 key, add to queue
                        if self.defer_image_processing and s3_key_result and self.local_queue:
                            logger.debug(f"{log_prefix_ad}Adding image to processing queue for later text extraction")
                            
                            # Use ID-based hash for queue tracking
                            # The queue processor will handle PHash lookup and duplicate detection
                            queue_hash = f"{ad_archive_id}_{ad_creative_id}"
                            start_date = ad_dict_structured.get('start_date', self.current_process_date)
                            
                            self.local_queue.add_to_queue(
                                image_hash=queue_hash,
                                ad_archive_id=ad_archive_id,
                                start_date=start_date,
                                s3_path=s3_key_result,
                                scrape_date=self.current_process_date,
                                creative_id=ad_creative_id,
                                law_firm_name=law_firm_name
                            )
                            ad_dict_structured['image_hash'] = queue_hash
                            ad_dict_structured['ImageText'] = None  # Explicitly set to None for deferred processing
                            logger.debug(f"{log_prefix_ad}Added to queue with hash: {queue_hash}")
                    except Exception as img_e:
                        logger.error(f"{log_prefix_ad}Error calling ImageHandler: {img_e}", exc_info=True)
                        ad_dict_structured['s3_image_key'] = None
                        ad_dict_structured['_s3_exists_initial'] = None
                else:
                    ad_dict_structured['s3_image_key'] = None
                    ad_dict_structured['_s3_exists_initial'] = None

                # Ensure basic snake_case keys for AI and rule matching
                # AdProcessor produces snake_case keys primarily in _create_ad_dictionary
                # AIIntegrator expects snake_case for its input fields generally.
                # VectorClusterer.get_category_for_ad_data also expects snake_case.
                # So, ad_dict_structured should be mostly snake_case here.

                processed_ads_for_ai.append(ad_dict_structured)
                structuring_or_input_count += 1

        logger.info(
            f"AdProcessor ({law_firm_name}): Finished structuring/image handling. {structuring_or_input_count} ads ready for AI enhancement.")

        final_processed_ads_list = []
        if processed_ads_for_ai:
            # Check if AI enhancement is enabled via feature flags
            ai_enhancement_enabled = self.config.get('feature_flags', {}).get('enable_ai_enhancement', True)
            
            if ai_enhancement_enabled:
                logger.info(
                    f"AdProcessor ({law_firm_name}): Enhancing {len(processed_ads_for_ai)} ads with AI (summary, LLM class)...")

                ai_tasks = [self.ai_integrator.enhance_ad_data(ad_dict) for ad_dict in processed_ads_for_ai]
            else:
                logger.info(
                    f"AdProcessor ({law_firm_name}): AI enhancement disabled via feature flags, skipping AI processing...")
                # Skip AI enhancement, go directly to rule-based categorization
                ai_tasks = []

            ai_enhanced_results_with_exceptions = []
            # Batch AI tasks to manage concurrency if needed, or simply gather all
            # For now, gather all. Add batching if OOM or API rate limits become an issue.
            if ai_tasks:
                ai_enhanced_results_with_exceptions = await asyncio.gather(*ai_tasks, return_exceptions=True)
                logger.info(
                    f"AdProcessor ({law_firm_name}): AI enhancement tasks completed. Processing {len(ai_enhanced_results_with_exceptions)} results...")
            else:
                # No AI tasks - use the structured ads directly
                ai_enhanced_results_with_exceptions = processed_ads_for_ai.copy()
                logger.info(
                    f"AdProcessor ({law_firm_name}): No AI enhancement performed. Processing {len(ai_enhanced_results_with_exceptions)} structured ads...")

            pbar_desc_rules = f"Rule-Matching {law_firm_name[:15]}"
            progress_context_rules = tqdm(total=len(ai_enhanced_results_with_exceptions), desc=pbar_desc_rules,
                                          leave=False, disable=not use_tqdm, unit="ad") \
                if use_tqdm else contextlib.nullcontext()

            with progress_context_rules as pbar_rules:
                for i, ai_result in enumerate(ai_enhanced_results_with_exceptions):
                    if pbar_rules is not None and hasattr(pbar_rules, 'update'): pbar_rules.update(1)

                    original_ad_for_logging = processed_ads_for_ai[i] if i < len(processed_ads_for_ai) else {}
                    ad_id_for_log = original_ad_for_logging.get('ad_archive_id', f'index_{i}')
                    log_prefix_ai_rule = f"Ad {ad_id_for_log} ({law_firm_name}, AI/Rule Result {i + 1}): "

                    if isinstance(ai_result, Exception):
                        logger.error(f"{log_prefix_ai_rule}Error during AI enhancement task: {ai_result}",
                                     exc_info=False)
                        # Optionally, still try to rule_match original_ad_for_logging if desired, or skip
                        # For now, we assume if AI enhancement fails, we might not have all data for rules.
                        # Or, we could decide to proceed with rule matching on the structured ad.
                        # Let's try rule matching on the original structured ad if AI fails.
                        ad_dict_to_rule_match = original_ad_for_logging
                        if not ad_dict_to_rule_match.get('summary'):  # If summary failed, set placeholder
                            ad_dict_to_rule_match['summary'] = "Summary generation failed"
                    elif isinstance(ai_result, dict):
                        # Check if this is an AI-enhanced result or original structured ad (when AI is disabled)
                        if ai_enhancement_enabled:
                            logger.debug(f"{log_prefix_ai_rule}Successfully AI-enhanced.")
                            ad_dict_to_rule_match = ai_result  # This dict has summary and LLM classification fields
                        else:
                            logger.debug(f"{log_prefix_ai_rule}Using structured ad (AI disabled).")
                            ad_dict_to_rule_match = ai_result  # This is the original structured ad
                            # Add basic summary if missing (for rule matching)
                            if not ad_dict_to_rule_match.get('summary'):
                                title = ad_dict_to_rule_match.get('title', '')
                                body = ad_dict_to_rule_match.get('body', '')
                                basic_summary = f"{title[:50]}{'...' if len(title) > 50 else ''}"
                                if not basic_summary.strip() and body:
                                    basic_summary = f"{body[:50]}{'...' if len(body) > 50 else ''}"
                                ad_dict_to_rule_match['summary'] = basic_summary or "No content summary"
                    else:
                        logger.error(
                            f"{log_prefix_ai_rule}AI enhancement returned unexpected type: {type(ai_result)}. Using original structured ad for rules.")
                        ad_dict_to_rule_match = original_ad_for_logging
                        if not ad_dict_to_rule_match.get('summary'):
                            ad_dict_to_rule_match['summary'] = "Summary generation failed"  # Placeholder

                    # --- Apply Rule-Based Campaign Categorization ---
                    if self.vector_clusterer:
                        logger.debug(f"{log_prefix_ai_rule}Applying rule-based campaign categorization.")
                        # get_category_for_ad_data modifies ad_dict_to_rule_match in-place
                        # to add 'campaign' and potentially update 'summary'.
                        # It expects snake_case keys like 'title', 'body', 'summary'.
                        # Ensure ad_dict_to_rule_match has these keys (from structuring or AI enhancement).
                        self.vector_clusterer.get_category_for_ad_data(ad_dict_to_rule_match)
                        logger.debug(
                            f"{log_prefix_ai_rule}Rule-based campaign: '{ad_dict_to_rule_match.get('campaign', 'N/A')}', Summary: '{str(ad_dict_to_rule_match.get('summary', 'N/A'))[:50]}...'")
                    else:
                        logger.warning(
                            f"{log_prefix_ai_rule}VectorClusterer not available. Skipping rule-based campaign categorization.")
                        ad_dict_to_rule_match.setdefault('campaign', 'Other (No Rules Engine)')

                    final_processed_ads_list.append(ad_dict_to_rule_match)
        else:
            logger.warning(
                f"AdProcessor ({law_firm_name}): No ads were structured/prepared for AI enhancement. Returning empty list.")

        # Final logging
        if not final_processed_ads_list and len(raw_ad_groups) > 0 and structuring_or_input_count > 0:
            logger.error(
                f"AdProcessor ({law_firm_name}): CRITICAL - Prepared {structuring_or_input_count} ads, but final list is EMPTY. Check AI/Rule logs.")
        elif not final_processed_ads_list and len(raw_ad_groups) > 0 and structuring_or_input_count == 0:
            logger.error(
                f"AdProcessor ({law_firm_name}): CRITICAL - Started with ads, but NONE were structured/prepared. Check structuring logs.")

        logger.info(
            f"AdProcessor ({law_firm_name}): Finished processing. Returning {len(final_processed_ads_list)} ads.")
        return final_processed_ads_list

    # _create_ad_dictionary, _extract_media_info, _strip_html_tags remain unchanged
    # _construct_cdn_url, _check_cdn_url_accessible remain unchanged
    # _handle_ad_image (legacy) remains unchanged
    def _create_ad_dictionary(self, record: Dict[str, Any], law_firm_name: str) -> Optional[Dict[str, Any]]:
        """Creates a structured dictionary. Ensures Booleans and Dates are correct types."""
        logger = getattr(self, '_logger', logging.getLogger(__name__))
        # --- Log received record keys ---
        if logger.isEnabledFor(logging.DEBUG):
            logger.debug(f"_create_ad_dict: Received raw record keys: {list(record.keys())}")
            try:
                logger.debug(f"_create_ad_dict: Raw record sample:\n{json.dumps(record, indent=2, default=str)}")
            except:
                logger.debug(f"_create_ad_dict: Raw record sample (repr): {record!r}")

        # --- Check essential keys ---
        ad_archive_id_raw = record.get('adArchiveID') or record.get('ad_archive_id')
        if not ad_archive_id_raw: logger.error(
            "_create_ad_dict: Returning None - Missing 'adArchiveID'/'ad_archive_id'."); return None
        ad_archive_id = str(ad_archive_id_raw)
        log_prefix = f"Ad {ad_archive_id}: "
        snapshot = record.get('snapshot')
        if not isinstance(snapshot, dict): logger.error(
            f"{log_prefix}Returning None - Missing or invalid 'snapshot'."); return None

        ad_creative_id_raw = snapshot.get('ad_creative_id', record.get('collation_id'))
        ad_creative_id = str(ad_creative_id_raw) if ad_creative_id_raw else None
        if not ad_creative_id: logger.warning(f"{log_prefix}Missing 'ad_creative_id'.")

        try:
            # --- Card extraction ---
            cards = snapshot.get('cards', [])
            card = cards[0] if cards and isinstance(cards[0], dict) else {}

            # --- Media Info Extraction ---
            video_info = self._extract_media_info(snapshot, card, 'videos')
            image_info = self._extract_media_info(snapshot, card, 'images')
            original_fb_url = image_info.get('original_image_url')
            preview_fb_url = video_info.get('video_preview_url')

            # --- Date Extraction & Calculation ---
            start_ts_raw = record.get('startDate') or record.get('start_date')
            end_ts_raw = record.get('endDate') or record.get('end_date')
            start_date_iso = DateUtils.convert_unix_to_date_str(start_ts_raw, "%Y%m%d")
            scheduled_end_date_iso = DateUtils.convert_unix_to_date_str(end_ts_raw, "%Y%m%d")

            if not start_date_iso:
                logger.error(f"{log_prefix}Returning None - Missing valid start date (Raw: {start_ts_raw}).")
                return None  # Essential key

            # --- *** BOOLEAN CONVERSION *** ---
            is_active_raw = record.get('isActive') or record.get('is_active')
            # Convert common truthy/falsy strings/numbers to Python bool
            if isinstance(is_active_raw, str):
                is_active = is_active_raw.lower() in ['true', '1', 'yes', 'on']
            elif isinstance(is_active_raw, (int, float)):
                is_active = bool(is_active_raw)
            elif isinstance(is_active_raw, bool):
                is_active = is_active_raw
            else:
                is_active = False  # Default to False if unknown type or None
            logger.debug(
                f"{log_prefix}Raw IsActive: '{is_active_raw}' -> Converted IsActive: {is_active} (type: {type(is_active).__name__})")
            # --- *** END BOOLEAN CONVERSION *** ---

            # --- Effective End Date Logic (using Python bool `is_active`) ---
            effective_end_date = scheduled_end_date_iso
            if is_active:  # Now uses the actual boolean
                if not scheduled_end_date_iso or scheduled_end_date_iso < self.current_process_date:
                    effective_end_date = self.current_process_date
            elif not scheduled_end_date_iso:
                effective_end_date = start_date_iso  # Start date is guaranteed to exist
            logger.debug(f"{log_prefix}Calculated EffectiveEndDate: {effective_end_date}")

            # --- Text Field Extraction ---
            body_from_card = card.get('body')
            body_from_snapshot_text = snapshot.get('body', {}).get('text')
            body_html = snapshot.get('body', {}).get('markup', {}).get('__html')
            body_from_html_stripped = self._strip_html_tags(body_html) if body_html else None
            body_text = body_from_card or body_from_snapshot_text or body_from_html_stripped

            # --- Assemble the ad dictionary (Ensure boolean type for IsActive) ---
            ad = {
                'law_firm': law_firm_name,
                'ad_archive_id': ad_archive_id,
                'ad_creative_id': ad_creative_id,
                'page_name': record.get('pageName') or record.get('page_name'),
                'page_id': str(record.get('pageID') or record.get('page_id')) if (
                        record.get('pageID') or record.get('page_id')) else None,
                'is_active': is_active,  # *** Use the converted boolean ***
                'start_date': start_date_iso,  # String YYYYMMDD
                'end_date': effective_end_date,  # String YYYYMMDD
                'publisher_platform': record.get('publisherPlatform') or record.get('publisher_platform'),
                'link_url': card.get('link_url') or snapshot.get('link_url'),
                'video_hd_url': video_info.get('video_hd_url'),
                'video_sd_url': video_info.get('video_sd_url'),
                'video_preview_image_url': preview_fb_url,
                'original_image_url': original_fb_url,
                'resized_image_url': image_info.get('resized_image_url'),
                'link_description': snapshot.get('link_description') or card.get('link_description'),
                'body': body_text,
                'caption': snapshot.get('caption') or card.get('caption'),
                'cta_text': snapshot.get('cta_text') or card.get('cta_text'),
                'title': card.get('title') or snapshot.get('title'),
                'last_updated': self.current_process_date,  # String YYYYMMDD
                # Placeholders
                's3_image_key': None, 'ImageText': None, 'Summary': None, 'LLM': None, 'IsForbidden403': False,
                # Initial False
                'Category': None, 'Company': None, 'Product': None, 'Injuries': None, 'LitigationType': None,
                'LitigationName': None, 'MdlName': None,
                # campaign field will be added by VectorClusterer call later
            }

            # Clean template placeholders
            for key in ['link_description', 'body', 'title', 'caption', 'cta_text']:
                value = ad.get(key)
                if isinstance(value, str) and '{{' in value and '}}' in value:
                    cleaned_value = re.sub(r'\{\{.*?\}\}', '', value).strip()
                    ad[key] = cleaned_value if cleaned_value else None

            logger.info(f"{log_prefix}Successfully created ad dictionary.")
            return ad

        except Exception as e:
            logger.error(f"{log_prefix}Exception during dictionary creation: {e}", exc_info=True)
            return None

    @staticmethod
    def _extract_media_info(snapshot: Dict[str, Any], card: Dict[str, Any], media_type: str) -> Dict[
        str, Optional[str]]:
        """Extracts media URLs (image or video), preferring card data."""
        media_data = {}
        keys_map = {
            'videos': ['video_hd_url', 'video_sd_url', 'video_preview_url'],
            'images': ['original_image_url', 'resized_image_url']
        }
        keys_to_find = keys_map.get(media_type, [])
        if not keys_to_find: return {}

        if isinstance(card, dict):
            for key in keys_to_find:
                card_value = card.get(key)
                if card_value:
                    media_data[key] = card_value

        if not all(k in media_data for k in keys_to_find):
            snapshot_media_source = snapshot.get(media_type)
            source_to_check = None
            if isinstance(snapshot_media_source, list) and snapshot_media_source:
                if isinstance(snapshot_media_source[0], dict):
                    source_to_check = snapshot_media_source[0]
            elif isinstance(snapshot_media_source, dict):
                source_to_check = snapshot_media_source

            if source_to_check:
                for key in keys_to_find:
                    source_value = source_to_check.get(key)
                    if key not in media_data and source_value:
                        media_data[key] = source_value

        for key in keys_to_find:
            media_data.setdefault(key, None)
        return media_data

    @staticmethod
    def _strip_html_tags(html: Optional[str]) -> Optional[str]:
        if not html: return None
        try:
            if not isinstance(html, str) or not ('<' in html and '>' in html):
                return html
            parser = 'lxml'
            try:
                soup = BeautifulSoup(html, parser)
            except ImportError:  # Fallback if lxml is not installed
                parser = 'html.parser'
                soup = BeautifulSoup(html, parser)
            except Exception:  # Catch other potential BeautifulSoup init errors
                logging.getLogger(__name__).warning(
                    f"BeautifulSoup failed to initialize with parser {parser}. Trying html.parser.")
                parser = 'html.parser'  # Try html.parser if initial choice failed
                soup = BeautifulSoup(html, parser)  # This might also fail

            text = soup.get_text(separator=' ', strip=True)
            return text if text else None
        except Exception as e:
            logging.getLogger(__name__).warning(
                f"BeautifulSoup failed to strip HTML (parser: {parser}): {e}. Content: {str(html)[:100]}...")
            try:
                return str(html) if html is not None else None
            except:
                return None

    def _construct_cdn_url(self, ad_archive_id: Optional[str], ad_creative_id: Optional[str]) -> Optional[str]:
        """Constructs the expected CDN URL for an ad image."""
        if not ad_archive_id or not ad_creative_id:
            self._logger.debug(f"Cannot construct CDN URL: missing IDs ({ad_archive_id}, {ad_creative_id})")
            return None
        prefix = self.config.get('s3_ad_archive_prefix', 'adarchive/fb').strip('/')
        s3_key = f'{prefix}/{ad_archive_id}/{ad_creative_id}.jpg'
        if not s3_key: return None
        bucket_name = getattr(self.s3_manager, 'bucket_name', self.config.get('bucket_name', 'default-bucket'))
        cdn_base = self.config.get('s3_cdn_base_url', f"https://{bucket_name}.s3.amazonaws.com")
        return f"{cdn_base.strip('/')}/{s3_key.lstrip('/')}"

    def _check_cdn_url_accessible(self, cdn_url: Optional[str]) -> bool:
        """Performs a HEAD request to check if the CDN URL is accessible."""
        if not cdn_url:
            self._logger.debug("No CDN URL provided for accessibility check.")
            return False
        self._logger.debug(f"Checking CDN URL accessibility (HEAD): {cdn_url}")
        try:
            session = self.image_handler.session_manager.get_session()  # Assumes ImageHandler has session_manager
            if session:
                response = session.head(cdn_url, timeout=10, allow_redirects=True)
            else:
                response = requests.head(cdn_url, timeout=10, allow_redirects=True)
            if response.status_code == 200:
                self._logger.debug(f"CDN URL check PASSED (200 OK): {cdn_url}")
                return True
            else:
                self._logger.warning(f"CDN URL check FAILED (Status: {response.status_code}): {cdn_url}")
                return False
        except requests.exceptions.Timeout:
            self._logger.warning(f"CDN URL check TIMEOUT: {cdn_url}")
            return False
        except requests.exceptions.RequestException as e:
            self._logger.warning(f"CDN URL check FAILED (Request Exception: {e}): {cdn_url}")
            return False
        except Exception as e:
            self._logger.error(f"Unexpected error during CDN URL check for {cdn_url}: {e}")
            return False

    def _handle_ad_image(self, ad_dict: Dict[str, Any]) -> Optional[str]:
        """Processes the ad's image (uploads to S3) and returns the S3 key. (LEGACY - Not actively called)"""
        # This method is effectively legacy due to changes in process_raw_ads
        # Since process_and_upload_ad_image is now async, this legacy method is disabled
        self._logger.warning("_handle_ad_image is legacy and disabled due to async changes")
        return None