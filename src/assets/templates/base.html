
<!DOCTYPE html>
<html lang="en" prefix="og: http://ogp.me/ns# fb: http://ogp.me/ns/fb#">
<head>
    {% include "includes/_head_common.html" ignore missing %} {# Common meta tags, title etc. #}

    {% if is_web %}
        {# --- START: WEB-ONLY HEAD CONTENT --- #}
        <!-- Web-only resources -->
        <script async src="https://www.googletagmanager.com/gtag/js?id=G-9CREQVVWNF"></script>
        <script> window.dataLayer = window.dataLayer || []; function gtag() { dataLayer.push(arguments); } gtag('js', new Date()); gtag('config', 'G-9CREQVVWNF'); </script>
        <script src="https://cdn.tailwindcss.com?plugins=typography"></script>
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
        <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.1/dist/chart.umd.min.js"></script>
        <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels@2.2.0/dist/chartjs-plugin-datalabels.min.js"></script>
        <style>
            /* --- START: WEB-ONLY STYLES --- */
            html { scroll-behavior: smooth; }
            .gradient-header { background: linear-gradient(135deg, #115197 0%, #1E88E5 100%); }
            .toggle-content { display: none; max-height: 0; overflow: hidden; transition: max-height 0.5s ease-in-out; }
            .toggle-content.active { display: block; max-height: 5000px; }
            .back-to-top { position: fixed; bottom: 20px; right: 20px; display: none; background-color: #3b82f6; color: white; padding: 10px 15px; border-radius: 50%; text-align: center; font-size: 18px; z-index: 1000; transition: background-color 0.3s; }
            .back-to-top:hover { background-color: #1e3a8a; }
            .animate-on-scroll { opacity: 0; transform: translateY(20px); transition: opacity 0.5s ease-out, transform 0.5s ease-out; }
            .animate-on-scroll.visible { opacity: 1; transform: translateY(0); }
            .no-hover-effect { pointer-events: none; }
            .expandable-section-hover { transition: box-shadow 0.3s ease, transform 0.2s ease, width 0.2s ease, margin 0.2s ease; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); width: 100%; margin: 0 auto; position: relative; z-index: 1; left: 0; transform-origin: center; }
            .expandable-section-hover:hover, .expandable-section-hover.hovering { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); transform: translateY(-2px) scaleX(1.01); z-index: 2; }
            .expandable-section-hover.expanded { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); transform: scaleX(1.01); }
            .expandable-section-hover.expanded:hover { box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04); transform: scaleX(1.01); }
            .expandable-section-hover.expanded:not(:hover) { transform: scaleX(1); }
            .collapsible-header { display: flex; align-items: center; justify-content: space-between; transition: background-color 0.3s ease; }
            .collapsible-header-group { display: flex; align-items: center; min-width: 0; }
            .collapsible-header-title { flex-grow: 1; margin-right: 8px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; }
            .toggle-icon { flex-shrink: 0; transition: transform 0.3s ease; }
            .expandable-section-hover.expanded .collapsible-header { background-color: #f8fafc; }
            /* --- END: WEB-ONLY STYLES --- */
        </style>
        <script>
        // Handle filtering based on 'from' parameter in URL
        function handleFromParameter() {
            var urlParams = new URLSearchParams(window.location.search);
            var fromParam = urlParams.get('from');
            
            if (fromParam && fromParam.startsWith('litigation_')) {
                // Extract the litigation ID from the from parameter
                var litigationId = fromParam;
                
                // Find all litigation sections within the current firm detail section
                var currentHash = window.location.hash.replace('#', '');
                var firmSection = document.getElementById(currentHash);
                
                if (firmSection) {
                    // Find all litigation subsections within this firm
                    var litigationDivs = firmSection.querySelectorAll('.mb-4.bg-blue-50');
                    
                    // Hide all litigation sections first
                    litigationDivs.forEach(function(div) {
                        div.style.display = 'none';
                    });
                    
                    // Show only the litigation section that matches the from parameter
                    litigationDivs.forEach(function(div) {
                        var litigationLink = div.querySelector('h4 a');
                        if (litigationLink && litigationLink.getAttribute('href')) {
                            var href = litigationLink.getAttribute('href').replace('#', '');
                            if (href === litigationId) {
                                div.style.display = 'block';
                                // Also highlight this section
                                div.style.border = '2px solid #2563eb';
                                div.style.backgroundColor = '#dbeafe';
                            }
                        }
                    });
                    
                    // Auto-expand the firm section
                    var contentElement = document.getElementById(currentHash.replace('detail_', 'firm_content_'));
                    if (contentElement && !contentElement.classList.contains('active')) {
                        // Find the trigger element (the header that was clicked)
                        var triggerElement = firmSection.querySelector('.collapsible-header');
                        if (triggerElement) {
                            triggerElement.click(); // Simulate a click to expand
                        }
                    }
                }
            }
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            handleFromParameter();
        });
        </script>
        {# --- END: WEB-ONLY HEAD CONTENT --- #}
    {% else %}
        {# --- START: EMAIL-ONLY HEAD CONTENT --- #}
        {% include "includes/_head_email.html" ignore missing %} {# Includes all email CSS definitions #}
        {# --- END: EMAIL-ONLY HEAD CONTENT --- #}
    {% endif %}
</head>
<body class="{{ 'bg-gray-100 font-sans' if is_web else '' }}">

{# Import macros #}
{% import 'includes/_macros.html' as macros %}

{# ******** START: Main Content Area Wrapper ******** #}
{% if is_web %}
    {# --- WEB VERSION --- #}
    {% include "includes/_navigation_web.html" ignore missing %}
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 overflow-hidden">
        {% include "includes/_header_web.html" %}
        {% include "includes/_header_web_sponsorship.html" ignore missing %}
        {# --- Web Sections Loop --- #}
        {% for loop_section_item in active_sections %}
            {% if loop_section_item.web_template_wrapper %}
                <!-- DEBUG BASE (Web): Including wrapper '{{ loop_section_item.web_template_wrapper }}' for section '{{ loop_section_item.id }}' -->
                {% with section=loop_section_item %}
                    {% include loop_section_item.web_template_wrapper %}
                {% endwith %}
            {% else %}<!-- Warning: No web wrapper for section {{ loop_section_item.id }} -->{% endif %}
        {% endfor %}
        {# --- Web Footer --- #}
        {% set footer_section = { 'id': 'footer', 'title': 'Footer Information', 'icon_class': 'fas fa-info-circle', 'web_content_template': 'includes/_footer_web_content.html', 'web_default_expanded': False, 'extra_wrapper_classes': 'bg-gray-800', 'extra_header_classes': 'text-gray-400', 'extra_icon_classes': 'text-gray-400', 'web_template_wrapper': 'includes/sections/_section_collapsable_web.html' } %}
        {% with section=footer_section %}{% include "includes/sections/_section_collapsable_web.html" ignore missing %}{% endwith %}
    </div>

{% else %}
    {# --- EMAIL VERSION --- #}
    <table role="presentation" class="email-container" width="100%" border="0" cellpadding="0" cellspacing="0" style="max-width: 800px; margin: 0 auto; background-color: #ffffff;">
        <tr>
            <td class="content-padding" style="padding: 20px 15px;">
                {% if config_obj and config_obj.minimal_email_mode %}
                    {# --- MINIMAL EMAIL MODE --- #}
                    <!-- Minimal Email Header with Logo -->
                    <div style="text-align: center; margin-bottom: 24px; padding-top:10px;">
                        <img src="https://cdn.lexgenius.ai/assets/images/lexgenius_preview-360.jpg" alt="LexGenius Logo" height="60" style="height: 60px; max-width: 100%; width:auto; margin: 0 auto 10px auto; display: block;">
                        <h1 style="margin: 0 0 6px 0; font-size: 24px; line-height: 1.3; font-weight: bold; color: #1f2937;">LexGenius Mass Tort Report</h1>
                        <p style="margin: 0; font-size: 16px; color: #374151; font-weight: 500;">{{ report_date if report_date is defined else 'Report Date' }}</p>
                    </div>

                    <!-- Summary Section -->
                    {% include "includes/_summary_email_content.html" ignore missing %}

                    <!-- Footer -->
                    {% include "includes/_footer_email.html" ignore missing %}
                {% else %}
                    {# --- REGULAR EMAIL MODE --- #}
                    {# --- Email Header --- #}
                    <div class="email-header" style="background: linear-gradient(135deg, #115197 0%, #1E88E5 100%); border-radius: 12px; margin-bottom: 32px; padding: 24px 15px; text-align: center;">
                        <h1 style="margin: 0 0 6px 0; font-size: 28px; line-height: 1.3; font-weight: bold; color: #ffffff;">Mass Tort Report</h1>
                        <p style="margin: 0; font-size: 16px; color: #ffffff; font-weight: 500;">{{ report_date if report_date is defined else 'Report Date' }}</p>
                    </div>

                    {# --- Email Header Sponsorship --- #}
    {#                {% include "includes/_header_sponsorship_email.html" ignore missing %}#}

                    {# --- Email Sections Loop (REVISED EXPLICIT SUMMARY LOGIC) --- #}
                    {% for loop_section_item in active_sections %}
                        {% set section = loop_section_item.copy() %}
                        {% if section.title %}{% set section = section.update({'title': section.title | replace(']]>', '') | trim}) or section %}{% endif %}

                        <!-- DEBUG BASE (Email): Processing section '{{ section.id }}' -->

                        {# *** START: AGGRESSIVE HANDLING *** #}
                        {# Step 1: Render Summary IF it's the summary section #}
                        {% if section.id == 'summary' %}
                            <!-- Explicitly including summary section by ID -->
                            {% include "includes/_summary_email_content.html" ignore missing %}

                        {# Step 2: Render Collapsed Sections (Ensure it's NOT summary) #}
                        {% elif section.email_template_type == 'collapsed' %}
                            <!-- Including collapsed section: {{ section.id }} -->
                            {% include "includes/sections/_section_collapsible_email.html" ignore missing %}

                        {# Step 3: Render Full Sections (Ensure it's NOT summary) #}
                        {% elif section.email_template_type == 'full' %}
                            <!-- Including full section: {{ section.id }} -->
                            {% include "includes/sections/_section_full_email.html" ignore missing %}

                        {# Step 4: Catch anything else (that isn't summary) #}
                        {% else %}
                             {% if section.id != 'summary' %} {# Double-check it's not summary #}
                                <!-- Warning: Unknown/Unhandled email template type '{{ section.email_template_type }}' OR unhandled ID '{{ section.id }}' -->
                             {% endif %}
                        {% endif %}
                        {# *** END: AGGRESSIVE HANDLING *** #}

                        <!-- DEBUG BASE (Email): Finished processing section '{{ section.id }}' -->
                    {% endfor %}
                    {# --- End Email Sections Loop --- #}

                    {# --- Email Footer --- #}
                    {% include "includes/_footer_email.html" ignore missing %}
                {% endif %}
            </td>
        </tr>
    </table>
{% endif %} {# End of is_web / Email Version #}
{# ******** END: Main Content Area Wrapper ******** #}

{# Web Scripts #}
{% if is_web %}
    {% include "includes/_scripts_web.html" ignore missing %}
{% endif %}

</body>
</html>
