{# File: _calendly_content.html (Updated for Email - Relies on Tables) #}

{# Calendly/Book Meeting Section Content #}
{# Assumes 'calendly_link' is available in context #}

{# Gradient background achieved via inline style #}
<div style="background: linear-gradient(to right, #eff6ff, #e0f2fe); padding: 20px;">
    {# Use a table for 2-column layout in email #}
    <table width="100%" cellpadding="0" cellspacing="0" border="0">
        <tr>
            {# Text Column #}
            <td width="65%" style="width: 65%; padding-right: 10px; vertical-align: middle; text-align: left;">
                {# Use email text classes #}
                <h3 class="text-lg-email font-bold-email text-gray-800-email mb-1-email" {# Assuming mb-1-email defined #}
                    style="font-size: 18px; font-weight: bold; color: #1f2937; margin: 0 0 4px 0;">Want Deeper Insights?</h3>
                <p class="text-gray-700-email text-sm-email"
                   style="color: #374151; font-size: 14px; line-height: 1.5; margin: 0;">Schedule a quick call...</p>
            </td>
            {# Button Column #}
            <td width="35%" style="width: 35%; padding-left: 10px; vertical-align: middle; text-align: right;">
                 {# Use email-button class (defined in _head_email.html) with specific color #}
                 {# Added basic icon support if FontAwesome doesn't render entities #}
                <a href="{{ calendly_link }}" target="_blank"
                   class="email-button email-button-green" {# Add specific class if needed for green #}
                   style="display: inline-block; background-color: #16a34a; color: #ffffff !important; font-weight: 500; padding: 10px 18px; border-radius: 6px; text-decoration: none !important; font-size: 14px; white-space: nowrap;">
                    📅 Book a Demo {# Using calendar emoji + non-breaking spaces #}
                   {# Alt: <i class="fas fa-calendar-check mr-2"></i> Book a Demo #}
                </a>
            </td>
        </tr>
    </table>
</div>