{% set section_id = section.id %}
{% set content_id = 'content_' ~ section_id %} {# Consistent ID prefix #}

{# Apply card-like styling and hover class directly to the section #}
<section id="{{ section.id }}"
         class="mb-12 expandable-section-hover bg-white rounded-xl shadow-md overflow-hidden border border-gray-200 animate-on-scroll {{ section.extra_wrapper_classes | default('') }}"
         style="{{ section.extra_wrapper_style | default('') }}">
    <!-- DEBUG WRAPPER: Section tag rendered for {{ section.id }} -->

    {# Header - Clickable to toggle content #}
    <div class="collapsible-header px-4 py-3 sm:px-6 cursor-pointer border-b border-gray-200 {{ 'bg-gray-50' if not section.extra_header_bg else section.extra_header_bg }}"
         onclick="toggleContent('{{ content_id }}', this)"> {# Passing 'this' #}
         <!-- DEBUG WRAPPER: Header div rendered for {{ section.id }} -->
         {# Group for icon and title #}
         <div class="collapsible-header-group">
            {% if section.icon_class %}
                <i class="{{ section.icon_class }} {{ section.extra_icon_classes | default('text-blue-600') }} mr-3"></i>
            {% endif %}
            <h2 class="collapsible-header-title text-xl font-bold text-gray-800 {{ section.extra_title_style | default('') }}" title="{{ section.title }}">{{ section.title }}</h2>
            {# Toggle Icon (+/-) #}
            <span id="{{ content_id }}_icon"
                  class="fas {{ 'fa-minus' if section.web_default_expanded else 'fa-plus' }} toggle-icon text-gray-500 text-base flex-shrink-0 ml-2 {{ section.extra_arrow_style | default('') }}">
            </span>
         </div>
         {# Optional: Placeholder for extra content #}
    </div>

    {# Content Area - Toggled by JS #}
    <div id="{{ content_id }}" class="toggle-content {{ 'active' if section.web_default_expanded }}" data-initially-expanded="{{ 'true' if section.web_default_expanded else 'false' }}">
         <!-- DEBUG WRAPPER: Content div rendered for {{ section.id }} -->
        {# Padding typically goes around the content itself #}
        <div class="p-6">
             <!-- DEBUG WRAPPER: Including content template '{{ section.web_content_template }}' for {{ section.id }} -->
            {# Include the specific content template defined in the section config #}
            {% include section.web_content_template %} {# Removed ignore missing #}
             <!-- DEBUG WRAPPER: Finished including content template for {{ section.id }} -->
        </div>
    </div>
</section>
<!-- DEBUG WRAPPER: END Wrapper included for section: {{ section.id }} -->
