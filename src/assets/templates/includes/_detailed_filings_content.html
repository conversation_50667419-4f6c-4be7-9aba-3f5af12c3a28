{# File: _detailed_filings_content.html (No direct changes needed) #}

{# --- START OF _detailed_filings_content.html --- #}
<!-- DEBUG: START _detailed_filings_content.html -->
{# Assumes macros is imported in base.html, detailed_filings_grouped, is_web, s3_prod, iso_date are in context #}
<!-- DEBUG: Detailed Filings - About to call render_detailed_filings with detailed_filings_grouped -->
{# This template just calls the macro. The email-specific changes are within the macro itself. #}
{{ macros.render_detailed_filings(detailed_filings_grouped, is_web, s3_prod, iso_date, is_weekly_report) }}
<!-- DEBUG: Detailed Filings - Finished call render_detailed_filings -->
<!-- DEBUG: END _detailed_filings_content.html -->