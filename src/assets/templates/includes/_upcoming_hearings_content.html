{# File: _upcoming_hearings_content.html (Updated for Email) #}

{# src/assets/templates/includes/_upcoming_hearings_content.html #}

{# Define a list of month abbreviations for lookup #}
{% set month_abbrs = ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN', 'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC'] %}

{# Upcoming Hearings Section Content - Using INLINE STYLES for Web & Email Consistency #}
{# Assumes 'upcoming_hearings' list is available in context #}
{% if upcoming_hearings %}
    {# This outer div helps contain the items #}
    <div>
        {% for hearing in upcoming_hearings %}
            {# --- START: Date Processing Logic --- #}
            {% set date_parts = hearing.date.split('/') if hearing.date and '/' in hearing.date else [] %}
            {% set month_display = 'ERR' %} {# Default error values #}
            {% set day_display = '?' %}
            {% set year_display = '?' %}

            {% if date_parts | length == 3 %}
                {% set month_num_str = date_parts[0] %}
                {% set day_num_str = date_parts[1] %}
                {% set year_num_str = date_parts[2] %}

                {# Attempt to convert month string to integer #}
                {% set month_index = month_num_str | int(-1) %}

                {# Check if month number is valid (1-12) and get abbreviation #}
                {% if month_index >= 1 and month_index <= 12 %}
                    {% set month_display = month_abbrs[month_index - 1] %} {# Adjust for 0-based list index #}
                    {# Attempt to convert day string to integer #}
                    {% set day_display = day_num_str | int('?') %}
                    {% set year_display = year_num_str %}
                {% endif %}
            {% endif %}
            {# --- END: Date Processing Logic --- #}

            {# Use email margin/border classes #}
            {# Added class="hearing-item" for potential CSS targeting #}
            <div class="hearing-item mb-4-email pb-4-email border-b-email border-gray-200-email" {# Assuming border-gray-200-email defined #}
                 style="margin-bottom: 16px; padding-bottom: 16px; border-bottom: 1px solid #eee;">
                <table width="100%" cellpadding="0" cellspacing="0" border="0" style="border-collapse: collapse;">
                    <tr>
                        {# Date Column - USE THE PROCESSED VARIABLES & inline styles #}
                        <td width="70" style="width: 70px; padding-right: 15px; vertical-align: top;" valign="top">
                            {# Date box uses inline styles - ok #}
                            <div style="background-color: #e0f2fe; color: #374151; border-radius: 6px; padding: 8px; text-align: center;">
                                <div style="font-size: 11px; font-weight: bold; text-transform: uppercase; margin-bottom: 2px;">{{ month_display }}</div>
                                <div style="font-size: 18px; font-weight: bold; line-height: 1;">{{ day_display }}</div>
                                <div style="font-size: 10px; margin-top: 2px;">{{ year_display }}</div>
                            </div>
                        </td>
                        {# Details Column #}
                        <td style="vertical-align: top;" valign="top">
                             {# Use email text classes for title heading #}
                            <h3 class="mb-1-email text-base-email font-medium-email text-gray-800-email" {# Assuming text-base-email defined #}
                                style="margin: 0 0 5px 0; font-size: 15px; font-weight: 500; color: #1f2937; line-height: 1.4;">
                                {% if hearing.link %}
                                    <a href="{{ hearing.link }}" target="_blank"
                                       title="{{ hearing.date }}" {# Display raw date in title tooltip #}
                                       class="text-blue-600-email" {# Use email color class #}
                                       style="color: #2563eb; text-decoration: underline;">{{ hearing.title }}</a>
                                {% else %}
                                    <span title="{{ hearing.date }}">{{ hearing.title }}</span> {# Display raw date in title tooltip #}
                                {% endif %}
                            </h3>
                            {% if hearing.details %}
                                 {# Use email text classes for details paragraph #}
                                <p class="text-sm-email text-gray-600-email"
                                   style="margin: 0; font-size: 13px; color: #4b5563; line-height: 1.5;">{{ hearing.details }}</p>
                            {% endif %}
                        </td>
                    </tr>
                </table>
            </div>
        {% endfor %}
    </div>
{% else %}
    {# Use inline style for the 'no hearings' message too for consistency #}
    <p style="margin: 0; font-size: 14px; color: #6b7280; padding: 10px 0;">No upcoming hearings listed.</p>
{% endif %}