{# AdSpy Report Section Content (Extracted from original) #}
{# Assumes macros is imported in base.html, grouped_ads, is_web, s3_prod, iso_date are in context #}

{# Specific Web wrapper style for AdSpy - slightly different from simple_wrapper #}
{% if is_web %}
    <div class="bg-white rounded-xl shadow-md overflow-hidden">
        <div class="p-6">
            {# Title moved to wrapper #}
            {# <h2 class="text-2xl font-bold text-gray-800 mb-6 flex items-center"><i class="fab fa-facebook-square text-blue-600 mr-3"></i> AdSpy Report </h2> #}
            {# Sponsorship moved to wrapper #}
            {# <div class="mb-6 text-center"> ... </div> #}
            {{ macros.render_ad_report(grouped_ads, is_web, s3_prod, iso_date) }}
        </div>
    </div>
{% else %}
    {# Email version just calls the macro #}
    {{ macros.render_ad_report(grouped_ads, is_web, s3_prod, iso_date) }}
{% endif %}