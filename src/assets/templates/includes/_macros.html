{# --- START OF COMPLETE _macros.html (with email class updates) --- #}

{# --- UPDATED render_litigation_table MACRO --- #}
{% macro render_litigation_table(title, allegations, litigation_data, is_web, s3_prod, iso_date, general_sponsorships, litigation_sponsorships, is_weekly_report) %}
    <!-- DEBUG MACRO: START render_litigation_table for {{ title }} -->
    {% set allegations_and_causes = allegations %}
    {% set title_id = title|clean_id %}
    {% set current_mdl_num = litigation_data.mdl_num if litigation_data and litigation_data.mdl_num else '' %}
    <!-- DEBUG MACRO: Litigation Table - current_mdl_num = {{ current_mdl_num }} -->

    {% set cleaned_mdl_num_str = current_mdl_num|string|trim|replace('.0', '') %}
    {% set sponsor_data = litigation_sponsorships.get(cleaned_mdl_num_str) if litigation_sponsorships and cleaned_mdl_num_str else None %}
    {% set sponsor_display = sponsor_data if sponsor_data and sponsor_data.get('display', False) else None %}
    <!-- DEBUG MACRO: Litigation Table - Sponsor data retrieved (exists and display=True: {{ sponsor_display is not none }}) -->

    {% set firm_records = litigation_data.firm_records if litigation_data and litigation_data.firm_records else [] %}
    {% set total_filings_for_title = litigation_data.total_filings if litigation_data and litigation_data.total_filings else 0 %}
    <!-- DEBUG MACRO: Litigation Table - firm_records count: {{ firm_records|length }} -->

    {# --- Web Version - REMAINS UNCHANGED --- #}
    {% if is_web %}
        <!-- DEBUG MACRO: Litigation Table - Rendering WEB version -->
        <div id="litigation_{{ title_id }}" class="tort-card animate-on-scroll expandable-section-hover">
            {# Collapsible Header #}
            <div class="collapsible-header px-4 py-3 sm:px-6 cursor-pointer border-b border-gray-200 bg-gray-50"
                 onclick="toggleContent('tort_content_{{ title_id }}', this)">
                <div class="flex items-center justify-between w-full">
                    <h3 class="text-lg font-medium text-gray-800 flex-grow mr-2" title="{{ title }}">{{ title }}</h3> {# Changed font-semibold to font-medium #}
                    <div class="text-sm text-gray-600 whitespace-nowrap mr-4">
                        <span>{{ firm_records|length }} {{ 'Firm' if firm_records|length == 1 else 'Firms' }}</span>
                        <span class="mx-1 text-gray-300">|</span>
                        <span>{{ total_filings_for_title }} {{ 'Filing' if total_filings_for_title == 1 else 'Filings' }}</span>
                    </div>
                    <span id="tort_content_{{ title_id }}_icon"
                          class="fas fa-plus text-gray-500 text-base flex-shrink-0"></span>
                </div>
            </div>

            {# Collapsible Content Area #}
            <div id="tort_content_{{ title_id }}" class="toggle-content">
                <!-- DEBUG MACRO: Litigation Table - Rendering Content Area for {{ title }} -->
                <div class="p-5">
                    {# Sponsor Section #}
                    {% if sponsor_display %} {# Use the derived sponsor_display variable #}
                        <!-- DEBUG MACRO: Litigation Table - Rendering Sponsor -->
                        {% set sponsor_ad_copy = sponsor_display.web_ad_copy if sponsor_display.web_ad_copy else sponsor_display.email_ad_copy %}
                        <div style="margin-bottom: 1rem; padding: 1rem; border: 1px solid #e5e7eb; border-radius: 0.5rem; overflow: hidden; background-color:
                                {% if sponsor_display.background_color %}{{ sponsor_display.background_color }}{% else %}#f9fafb{% endif %} !important;">
                            <div style="display: flex; flex-wrap: wrap; align-items: center; gap: 1rem;">
                                <div style="flex: 1; min-width: 120px; display: flex; justify-content: center; align-items: center;">
                                    {% if sponsor_display.logo_svg_link %}
                                        <div style="position: relative; width: 100%; max-width: 120px; height: 64px; {% if sponsor_display.logo_background_color %}background-color: {{ sponsor_display.logo_background_color }} !important;{% endif %} border-radius: 4px; padding: 4px;">
                                            <img src="{{ sponsor_display.logo_svg_link }}" alt="Sponsor Logo"
                                                 style="height: 100%; width: auto; max-height: 100%; max-width: 100%; object-fit: contain; margin-left: auto; margin-right: auto; display: block;">
                                        </div>
                                    {% endif %}
                                </div>
                                <div style="flex: 2; min-width: 200px; text-align: center; font-size: 0.875rem; color: #374151;">
                                    {{ sponsor_ad_copy|safe }} {# Use derived ad copy #}
                                </div>
                                <div style="flex: 1; min-width: 120px; display: flex; justify-content: center;">
                                    {% if sponsor_display.cta_link %}
                                        <a href="{{ sponsor_display.cta_link }}" target="_blank"
                                           style="display: inline-flex; align-items: center; padding: 0.5rem 1rem; border: none; border-radius: 0.375rem; font-size: 0.875rem; font-weight: 500; color: white !important; background-color: #2563eb; box-shadow: 0 1px 2px rgba(0,0,0,0.05); white-space: nowrap; text-decoration: none;">
                                            <i class="fas fa-calendar-alt"
                                               style="margin-right: 0.5rem; color: white;"></i>
                                            <span style="color: white;">Book Meeting</span>
                                        </a>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <!-- DEBUG MACRO: Litigation Table - Finished Sponsor -->
                    {% endif %}

                    {# Allegations Section - Rendered only in Web #}
                    {% if allegations_and_causes %}
                        <!-- DEBUG MACRO: Litigation Table - Rendering Allegations -->
                        <div class="text-gray-700 text-sm mb-4 prose prose-sm max-w-none">{{ allegations_and_causes|convert_newlines|safe }}</div>
                    {% endif %}

                    {# --- FIRM TABLE SECTION --- #}
                    <!-- DEBUG MACRO: Litigation Table - Checking firm_records for table -->
                    {% if firm_records %}
                        <!-- DEBUG MACRO: Litigation Table - firm_records exist. Rendering table. -->
                        <div class="overflow-x-auto mt-4">
                            <table class="min-w-full divide-y divide-gray-200 w-full text-sm">
                                <thead class="bg-gray-100">
                                <tr>
                                    <th scope="col"
                                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Law Firm
                                    </th>
                                    <th scope="col"
                                        class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Filings
                                    </th>
                                </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                <!-- DEBUG MACRO: Litigation Table - About to loop firm_records for {{ title }} -->
                                {% for row in firm_records %} {# Iterate the list of dicts #}
                                    <!-- DEBUG MACRO: Litigation Table - Processing row for {{ row.law_firm }} -->
                                    {% set law_firm_id = row.law_firm|clean_id %}
                                    <tr class="table-row-hover border-t">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium"><a
                                                href='#detail_{{ law_firm_id }}?from=litigation_{{ title_id }}'
                                                class="text-blue-600 hover:underline">{{ row.law_firm }}</a></td>
                                        <td class='px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-right'>{{ row.Filings }}</td>
                                    </tr>
                                {% else %}
                                    <!-- DEBUG MACRO: Litigation Table - firm_records loop was EMPTY for {{ title }} -->
                                    <tr>
                                        <td colspan="2" class="px-6 py-4 text-sm text-gray-500 italic">No firms found in
                                            this sub-group.
                                        </td>
                                    </tr>
                                {% endfor %}
                                <!-- DEBUG MACRO: Litigation Table - Finished loop for {{ title }} -->
                                <tr class="bg-gray-100 font-bold border-t">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-800">Total</td>
                                    <td class='px-6 py-4 whitespace-nowrap text-sm text-gray-800 text-right'>{{ total_filings_for_title }}</td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <!-- DEBUG MACRO: Litigation Table - firm_records is EMPTY or UNDEFINED -->
                        <p class="text-sm text-gray-500 mt-4">No filing data available.</p>
                    {% endif %}
                    {# --- END FIRM TABLE SECTION --- #}
                </div>
            </div>
        </div>

    {# --- Email Version - UPDATED with email classes --- #}
    {% else %}
        <!-- DEBUG MACRO: Litigation Table - Rendering EMAIL version -->
        {# Use email margin and border classes #}
        <div class="mb-4-email border-email rounded-lg-email" style="background-color: #ffffff; border: 1px solid #e5e7eb; border-radius: 8px; overflow: hidden; margin-bottom: 16px;">
            {# Header uses inline styles - ok #}
            <div style="background-color: #115197; padding: 12px 20px;">
                <a href='{{ s3_prod }}/{{ iso_date }}/index{{ "-weekly" if is_weekly_report else "" }}.html#litigation_{{ title_id }}'
                   target="_blank"
                   style="color: #ffffff !important; text-decoration: none !important; font-size: 16px; font-weight: 600; display: block; margin: 0;">{{ title }}</a>
            </div>
            {# Use email padding class for content area #}
            <div class="p-4-email" style="padding: 20px;">
                <!-- DEBUG MACRO: Litigation Table EMAIL - Checking sponsor -->
                {% if sponsor_display %}
                    {% set sponsor_ad_copy = sponsor_display.email_ad_copy if sponsor_display.email_ad_copy else sponsor_display.web_ad_copy %}
                    <!-- DEBUG MACRO: Litigation Table EMAIL - Rendering sponsor -->
                    {# Use email margin/border classes; sponsor table uses inline styles/widths - ok #}
                    <table width="100%" cellpadding="0" cellspacing="0" border="0"
                           class="mb-4-email border-email rounded-md-email" {# Assuming rounded-md-email defined #}
                           style="margin-bottom: 16px; border: 1px solid #e0e0e0; border-radius: 6px; background-color:
                                   {% if sponsor_display.background_color %}{{ sponsor_display.background_color }}{% else %}#f9fafb{% endif %} !important; font-size: 0;">
                        <tr>
                            {# Sponsor logo cell - inline styles ok #}
                            <td width="25%"
                                style="width: 25%; padding: 10px 5px 10px 15px; vertical-align: middle; font-size: 14px;">
                                {% if sponsor_display.logo_svg_link %}
                                    <div style="{% if sponsor_display.logo_background_color %}background-color: {{ sponsor_display.logo_background_color }} !important;{% endif %} border-radius: 4px; padding: 4px; display: inline-block;">
                                        <img src="{{ sponsor_display.logo_svg_link }}" alt="Sponsor Logo"
                                             style="display: block; width: 100%; max-width: 120px; height: auto; border: 0;">
                                    </div>
                                {% endif %}
                            </td>
                             {# Sponsor text cell - Use email text classes for <p> tag #}
                            <td width="50%"
                                style="width: 50%; padding: 10px 15px; vertical-align: middle; font-size: 14px;">
                                <p class="text-sm-email text-gray-800-email text-left-email" style="margin: 0; font-size: 14px; color: #333333; line-height: 1.5; text-align: left;">{{ sponsor_ad_copy|safe }}</p>
                            </td>
                            {# Sponsor button cell - inline styles ok, assuming email-button defined in CSS #}
                            {% if sponsor_display.cta_link %}
                                <td width="25%" align="right"
                                    style="width: 25%; padding: 10px 15px 10px 5px; vertical-align: middle; text-align: right; font-size: 14px;">
                                    <a href="{{ sponsor_display.cta_link }}" target="_blank"
                                       class="email-button" {# Use class defined in _head_email.html #}
                                       style="display: inline-block; background-color: #2563eb; color: #ffffff !important; padding: 8px 16px; text-decoration: none !important; border-radius: 4px; font-weight: bold; font-size: 13px; white-space: nowrap; line-height: 1.4;">Book Meeting</a>
                                </td>
                            {% else %}
                                <td width="25%"
                                    style="width: 25%; padding: 10px 15px 10px 5px; vertical-align: middle; font-size: 14px;"></td>
                            {% endif %}
                        </tr>
                    </table>
                {% endif %}

                {# *** FIXED: Allegations Section - Only render if is_web is true *** #}
                {% if allegations_and_causes and is_web %}
                     {# This block is only rendered for the web version now #}
                    <div class="text-gray-700-email text-sm-email mb-4-email prose-email" {# Assuming prose-email defined #}
                         style="color: #374151; font-size: 14px; line-height: 1.5; margin-bottom: 16px;">
                        {{ allegations_and_causes|convert_newlines|safe }}
                    </div>
                 {% elif not is_web and allegations_and_causes %}
                      <!-- Allegations/Description intentionally omitted in email version -->
                 {% endif %}
                 {# *** END FIX *** #}

                <!-- DEBUG MACRO: Litigation Table EMAIL - Checking firm_records -->
                {% if firm_records %}
                    <!-- DEBUG MACRO: Litigation Table EMAIL - firm_records found, rendering table -->
                    {# Use email-table class (defined in _head_email.html) #}
                    <table class="email-table" width="100%" cellpadding="0" cellspacing="0" border="0"
                           style="width: 100%; border-collapse: collapse; margin-bottom: 0;">
                        <thead>
                        <tr>
                            {# th should inherit from email-table styles #}
                            <th style="border: 1px solid #e5e7eb; padding: 8px 12px; text-align: left; font-size: 12px; background-color: #f9fafb; font-weight: 500; color: #6b7280; text-transform: uppercase;">
                                Law Firm
                            </th>
                            <th class="text-right-email"
                                style="border: 1px solid #e5e7eb; padding: 8px 12px; text-align: right; font-size: 12px; background-color: #f9fafb; font-weight: 500; color: #6b7280; text-transform: uppercase;">
                                Filings
                            </th>
                        </tr>
                        </thead>
                        <tbody>
                        <!-- DEBUG MACRO: Litigation Table EMAIL - About to loop firm_records for {{ title }} -->
                        {% for row in firm_records %}
                            <!-- DEBUG MACRO: Litigation Table EMAIL - Processing row for {{ row.law_firm }} -->
                            {% set law_firm_id = row.law_firm|clean_id %}
                            <tr>
                                {# Use email text classes, td inherits from email-table #}
                                <td class="font-medium-email"
                                    style="border: 1px solid #e5e7eb; padding: 8px 12px; font-size: 14px; line-height: 1.5; font-weight: 500;">
                                    <a href='{{ s3_prod }}/{{ iso_date }}/index{{ "-weekly" if is_weekly_report else "" }}.html#detail_{{ law_firm_id }}?from=litigation_{{ title_id }}'
                                       target="_blank"
                                       class="text-blue-600-email" {# Use email color class #}
                                       style="color: #2563eb; text-decoration: underline;">{{ row.law_firm }}</a>
                                </td>
                                <td class='text-gray-500-email text-right-email'
                                    style="border: 1px solid #e5e7eb; padding: 8px 12px; font-size: 14px; line-height: 1.5; color: #6b7280; text-align: right;">{{ row.Filings }}</td>
                            </tr>
                        {% else %}
                            <!-- DEBUG MACRO: Litigation Table EMAIL - firm_records loop was EMPTY for {{ title }} -->
                            <tr>
                                <td colspan="2"
                                    {# Use email text classes, td inherits from email-table #}
                                    class="text-sm-email text-gray-500-email italic-email" {# Assuming italic-email defined #}
                                    style="border: 1px solid #e5e7eb; padding: 8px 12px; font-size: 14px; line-height: 1.5; color: #6b7280; font-style: italic;">
                                    No firms found in this sub-group.
                                </td>
                            </tr>
                        {% endfor %}
                        <!-- DEBUG MACRO: Litigation Table EMAIL - Finished loop for {{ title }} -->
                        {# Use email-total-row class (defined in _head_email.html) #}
                        <tr class="email-total-row">
                            <td style="border: 1px solid #e5e7eb; padding: 8px 12px; font-size: 14px; line-height: 1.5; background-color: #f3f4f6; font-weight: bold; color: #1f2937;">
                                Total
                            </td>
                            <td class='text-right-email'
                                style="border: 1px solid #e5e7eb; padding: 8px 12px; font-size: 14px; line-height: 1.5; background-color: #f3f4f6; font-weight: bold; color: #1f2937; text-align: right;">{{ total_filings_for_title }}</td>
                        </tr>
                        </tbody>
                    </table>
                {% else %}
                    <!-- DEBUG MACRO: Litigation Table EMAIL - firm_records is EMPTY or UNDEFINED -->
                    {# Use email text classes #}
                    <p class="text-sm-email text-gray-500-email" style="font-size: 14px; color: #6b7280; margin: 0;">No filing data
                        available.</p>
                {% endif %}
            </div>
        </div>
    {% endif %} {# End Web/Email if #}
    <!-- DEBUG MACRO: END render_litigation_table for {{ title }} -->
{% endmacro %}
{# --- END OF UPDATED render_litigation_table MACRO --- #}


{# --- START OF render_detailed_filings MACRO - UPDATED Email Part --- #}
{% macro render_detailed_filings(detailed_filings_grouped, is_web, s3_prod, iso_date, is_weekly_report=False) %}
    {# Accepts detailed_filings_grouped (dict: law_firm -> dict: title -> list[dict: case]) #}
    {% if detailed_filings_grouped is defined and detailed_filings_grouped %}
        {# --- Web Version - UPDATED --- #}
        {% if is_web %}
            <div class="space-y-6">
                {% for law_firm, firm_data in detailed_filings_grouped.items() %}
                    {% set law_firm_id = law_firm|clean_id %}
                    {% set total_filings = firm_data.values() | map('length') | sum %}
                    {% set num_litigations = firm_data.keys() | length %}
                    <div id='detail_{{ law_firm_id }}' class="firm-card animate-on-scroll expandable-section-hover">
                        {# Collapsible Header - Law Firm Level #}
                        <div class="collapsible-header px-4 py-3 sm:px-6 cursor-pointer border-b border-gray-200"
                             onclick="toggleContent('firm_content_{{ law_firm_id }}', this)">
                            <div class="flex items-center justify-between w-full">
                                <h3 class="text-lg font-medium text-gray-800 flex-grow mr-2"
                                    title="{{ law_firm }}">{{ law_firm }}</h3>
                                <div class="text-sm text-gray-600 whitespace-nowrap mr-4">
                                    <span>{{ num_litigations }} {{ 'Litigation' if num_litigations == 1 else 'Litigations' }}</span>
                                    <span class="mx-1 text-gray-300">|</span>
                                    <span>{{ total_filings }} {{ 'Filing' if total_filings == 1 else 'Filings' }}</span>
                                </div>
                                <span id="firm_content_{{ law_firm_id }}_icon"
                                      class="fas fa-plus text-gray-500 text-base flex-shrink-0"></span>
                            </div>
                        </div>
                        {# Collapsible Content - Law Firm Level #}
                        <div id="firm_content_{{ law_firm_id }}" class="toggle-content">
                            <div class="p-5 mt-4 firm-card-content">
                                {# Sort litigations by number of filings (descending) #}
                                {% set sorted_firm_data = [] %}
                                {% for title, cases_list in firm_data.items() %}
                                    {% if sorted_firm_data.append((title, cases_list, cases_list|length)) %}{% endif %}
                                {% endfor %}
                                {% for title, cases_list, num_filings in sorted_firm_data | sort(attribute='2', reverse=True) %}
                                    {% set title_id = title|clean_id %}
                                    {% set litigation_num_filings = cases_list|length %}
                                    <div class="mb-4 bg-blue-50 p-4 rounded-lg expandable-section-hover">
                                        {# Collapsible Header - Litigation Level #}
                                        <div class="collapsible-header cursor-pointer"
                                             onclick="handleLitigationClick('litigation_content_{{ law_firm_id }}_{{ title_id }}', 'litigation_{{ title_id }}', this)">
                                            <div class="flex items-center justify-between w-full">
                                                <h4 class="tort-subtitle text-md font-medium text-blue-800 flex-grow mr-2">
                                                    <span class="hover:underline">{{ title }}</span>
                                                </h4>
                                                <div class="text-sm text-blue-600 whitespace-nowrap mr-4">
                                                    <span>{{ litigation_num_filings }} {{ 'filing' if litigation_num_filings == 1 else 'filings' }}</span>
                                                </div>
                                                <span id="litigation_content_{{ law_firm_id }}_{{ title_id }}_icon"
                                                      class="fas fa-plus text-blue-500 text-sm flex-shrink-0"></span>
                                            </div>
                                        </div>
                                        {# Collapsible Content - Litigation Level #}
                                        <div id="litigation_content_{{ law_firm_id }}_{{ title_id }}" class="toggle-content">
                                            <div class="mt-3">
                                                <ul class="space-y-2">
                                                    {% for case in cases_list %}
                                                        {% set versus = case.versus %}
                                                        {% set asterisks = '**' if case.get('transferred_in', False) else '*' if case.get('pending_cto', False) else '' %}
                                                        {% set plaintiff_display = '' %}
                                                        {% if case.get('mdl_num') in ['2873', '3092'] and case.get('num_plaintiffs', 0) > 0 %}
                                                            {% set plaintiff_display = ' (' ~ case.num_plaintiffs ~ (' plaintiffs)' if case.num_plaintiffs > 1 else ' plaintiff)') %}
                                                        {% endif %}
                                                        <li class="text-sm text-gray-700 case-link">
                                                            {# Generate link only if s3_link is present and not empty #}
                                                            {% if case.s3_link and case.s3_link | trim != '' %}
                                                                <a href='{{ case.s3_link }}' target='_blank'
                                                                   class="text-blue-600 hover:underline">
                                                                    {{- versus -}}{{- plaintiff_display -}}</a>
                                                            {% else %}
                                                                {{- versus -}}{{- plaintiff_display -}}
                                                                {# Display text if no link #}
                                                            {% endif %}
                                                            {% if asterisks %}
                                                                <span class="font-bold text-blue-700 ml-1">{{ asterisks }}</span>{% endif %}
                                                        </li>
                                                    {% else %}
                                                        <li>No cases found for this title.</li>
                                                    {% endfor %}
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                {% endfor %}
                                {# Check for asterisks across ALL cases for this firm #}
                                {% set firm_has_pending = false %}{% set firm_has_transferred = false %}
                                {% for cases in firm_data.values() %}{% for case in cases %}
                                    {% if case.get('pending_cto', False) %}{% set firm_has_pending = true %}{% endif %}
                                    {% if case.get('transferred_in', False) %}
                                        {% set firm_has_transferred = true %}{% endif %}
                                {% endfor %}{% endfor %}
                                {% if firm_has_pending or firm_has_transferred %}
                                    <div class="text-sm text-gray-500 mt-4"> {% if firm_has_pending %}
                                        <p class="mb-1"><span class="font-bold">*</span> CTO Filed (Pending Transfer)
                                        </p> {% endif %} {% if firm_has_transferred %}
                                        <p><span class="font-bold">**</span> Case Transferred In (CTO Finalized)
                                        </p> {% endif %} </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                {% else %}
                    <p class="text-sm text-gray-500">No detailed filings data available for this firm.</p>
                    {# Fallback if outer loop empty #}
                {% endfor %}
            </div>

        {# --- Email version - UPDATED --- #}
        {% else %}
            {% for law_firm, firm_data in detailed_filings_grouped.items() %}
                {% set law_firm_id = law_firm|clean_id %}
                {% set total_filings = firm_data.values() | map('length') | sum %}
                {% set num_litigations = firm_data.keys() | length %}
                {# Law Firm Level - Collapsed by default #}
                <div id='detail_{{ law_firm_id }}' class="mb-6-email" style="margin-bottom: 24px;">
                    {# Law Firm Header #}
                    <h3 class="text-lg-email font-semibold-email text-gray-800-email mb-3-email"
                        style="font-size: 18px; font-weight: 600; color: #1f2937; margin: 0 0 12px 0;">
                        <span>{{ law_firm }}</span>
                        {# Total filings count #}
                        <span class="font-normal-email text-gray-600-email ml-1-email"
                              style="font-weight: normal; color: #4b5563; margin-left: 6px;">({{ total_filings }} new {{ 'filing' if total_filings == 1 else 'filings' }})</span>
                    </h3>
                    {# Litigation Names with Filing Counts - All visible by default, sorted by num filings #}
                    {% set sorted_firm_data_email = [] %}
                    {% for title, cases_list in firm_data.items() %}
                        {% if sorted_firm_data_email.append((title, cases_list, cases_list|length)) %}{% endif %}
                    {% endfor %}
                    {% for title, cases_list, num_filings in sorted_firm_data_email | sort(attribute='2', reverse=True) %}
                        {% set title_id = title|clean_id %}
                        {% set litigation_num_filings = cases_list|length %}
                        <div class="mb-3-email bg-blue-50-email p-4-email rounded-lg-email"
                             style="background-color: #eff6ff; border-radius: 6px; padding: 16px; margin-bottom: 12px;">
                            {# Litigation Name with Filing Count and Link to Web #}
                            <h4 class="font-medium-email text-base-email text-blue-800-email mb-2-email"
                                style="font-weight: 500; color: #1e40af; margin: 0; font-size: 15px;">
                                {# Link to web version with firm expanded and litigation collapsed #}
                                <a href='{{ s3_prod }}/{{ iso_date }}/index{{ "-weekly" if is_weekly_report else "" }}.html#detail_{{ law_firm_id }}'
                                   target="_blank"
                                   class="text-blue-800-email"
                                   style="color: #1e40af; text-decoration: underline;">{{ title }}</a>
                                {# Filing count #}
                                <span class="font-normal-email text-blue-600-email ml-1-email"
                                      style="font-weight: normal; color: #2563eb; margin-left: 6px;">({{ litigation_num_filings }} {{ 'filing' if litigation_num_filings == 1 else 'filings' }})</span>
                            </h4>
                        </div>
                    {% endfor %}
                </div>
            {% else %}
                {# Fallback message - use email text classes #}
                <p class="text-sm-email text-gray-500-email" style="font-size: 14px; color: #6b7280;">No detailed filings data
                    available for this firm.</p>
            {% endfor %}
            {# Overall Asterisk Legend for Email #}
            {% set has_any_pending = false %}{% set has_any_transferred = false %}
            {% for firm_data in detailed_filings_grouped.values() %}{% for cases in firm_data.values() %}
                {% for case in cases %}
                    {% if case.get('pending_cto', False) %}{% set has_any_pending = true %}{% endif %}
                    {% if case.get('transferred_in', False) %}{% set has_any_transferred = true %}{% endif %}
                {% endfor %}{% endfor %}{% endfor %}
            {% if has_any_pending or has_any_transferred %}
                 {# Use email text classes for legend #}
                <div class="text-sm-email text-gray-500-email mt-6-email" style="font-size: 14px; color: #6b7280; margin-top: 24px;">
                    {% if has_any_pending %}
                        <p style="margin: 0 0 4px 0;"><span class="font-bold-email" style="font-weight: bold;">*</span> CTO Filed (Pending
                            Transfer)</p>
                    {% endif %}
                    {% if has_any_transferred %}
                        <p style="margin: 0;"><span class="font-bold-email" style="font-weight: bold;">**</span> Case Transferred In (CTO
                            Finalized)</p>
                    {% endif %}
                </div>
            {% endif %}
        {% endif %} {# End Web/Email if #}
    {% else %}
        {# Fallback if detailed_filings_grouped is missing/empty - use email text classes #}
        <p class="text-sm-email text-gray-500-email" style="font-size: 14px; color: #6b7280;">No detailed filings data
            available.</p>
    {% endif %}
{% endmacro %}
{# --- END OF render_detailed_filings MACRO --- #}


{# --- START OF render_ad_report MACRO - UPDATED Email Part --- #}
{% macro render_ad_report(grouped_ads, is_web, s3_prod, iso_date) %}
    <!-- DEBUG MACRO: START render_ad_report -->
    {# Accepts grouped_ads (dict: law_firm -> DataFrame), is_web, s3_prod, iso_date #}
    {# ASSUMES grouped_ads contains ALREADY FILTERED dataframes #}

    {# --- Web Version - REMAINS UNCHANGED --- #}
    {% if is_web %}
        <!-- DEBUG MACRO: Ad Report - Rendering WEB version -->
        <div class="space-y-6">
            <!-- DEBUG MACRO: Ad Report - Checking grouped_ads -->
            {% if grouped_ads is defined and grouped_ads %} {# Check if dict is not empty #}
                <!-- DEBUG MACRO: Ad Report - grouped_ads dict exists -->
                {% set has_ads = false %}
                <!-- DEBUG MACRO: Ad Report - About to loop firms -->
                {% for name, group_df in grouped_ads.items() %} {# Iterate dict items #}
                    <!-- DEBUG MACRO: Ad Report - Processing firm: {{ name }} -->
                    {# Check if group_df is a DataFrame and not empty #}
                    {% if group_df is defined and not group_df.empty %}
                        {% set valid_ads = group_df %}
                        {% set num_ads = valid_ads|length %}
                        <!-- DEBUG MACRO: Ad Report - Firm {{ name }} has {{ num_ads }} ads (pre-filtered) -->
                        {% if num_ads > 0 %}
                            {% set has_ads = True %}
                            <div>
                                <h3 class="text-lg font-semibold text-gray-800 mb-3">{{ name }} <span
                                        class="text-blue-600">({{ num_ads }} {{ 'new ad' if num_ads == 1 else 'new ads' }})</span>
                                </h3>
                                <div class="bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm">
                                    <div class="border-b border-gray-200 bg-gray-50 px-4 py-2 sm:px-6 sm:py-3 grid grid-cols-10 gap-4">
                                        <span class="col-span-6 text-xs font-medium text-gray-500 uppercase tracking-wider">Summary</span>
                                        <span class="col-span-2 text-xs font-medium text-gray-500 uppercase tracking-wider text-right">Start Date</span>
                                        <span class="col-span-2 text-xs font-medium text-gray-500 uppercase tracking-wider text-right">End Date</span>
                                    </div>
                                    <div class="divide-y divide-gray-200">
                                        <!-- DEBUG MACRO: Ad Report - About to loop valid_ads.iterrows() for {{ name }} -->
                                        {% for _, row in valid_ads.iterrows() %} {# Iterate DataFrame rows #}
                                            <!-- DEBUG MACRO: Ad Report - Processing ad ID {{ row.ad_archive_id }} -->
                                            <div class="p-4 hover:bg-gray-50 transition-colors duration-150 grid grid-cols-10 gap-4 items-start">
                                                <div class="col-span-6 text-sm"><a
                                                        href='{{ s3_prod }}/{{ iso_date }}/ads/{{ row.ad_archive_id }}.html'
                                                        target='_blank'
                                                        class="text-blue-600 hover:underline font-medium">{{ row.summary }}</a>
                                                </div>
                                                <div class="col-span-2 text-sm text-gray-500 text-right whitespace-nowrap">{{ row.start_date }}</div>
                                                <div class="col-span-2 text-sm text-gray-500 text-right whitespace-nowrap">{{ row.end_date }}</div>
                                            </div>
                                        {% else %}
                                            <!-- DEBUG MACRO: Ad Report - valid_ads.iterrows() loop EMPTY for {{ name }} -->
                                        {% endfor %}
                                        <!-- DEBUG MACRO: Ad Report - Finished looping valid_ads for {{ name }} -->
                                    </div>
                                </div>
                            </div>
                        {% endif %} {# End if num_ads > 0 #}
                    {% else %}
                        <!-- DEBUG MACRO: Ad Report - Group for firm {{ name }} is empty or not a valid DataFrame -->
                    {% endif %} {# End if group_df checks pass #}
                    <!-- DEBUG MACRO: Ad Report - Finished processing firm: {{ name }} -->
                {% else %}
                    <!-- DEBUG MACRO: Ad Report - grouped_ads loop EMPTY -->
                {% endfor %}
                <!-- DEBUG MACRO: Ad Report - Finished looping firms -->
                {% if not has_ads %}
                    <!-- DEBUG MACRO: Ad Report - No firms had ads -->
                    <p class="text-sm text-gray-500">No new ad data available.</p>
                {% endif %}
            {% else %}
                <!-- DEBUG MACRO: Ad Report - grouped_ads is empty or not defined -->
                <p class="text-sm text-gray-500">No ad data available.</p>
            {% endif %}
        </div>

    {# --- Email Version - UPDATED with email classes --- #}
    {% else %}
        <!-- DEBUG MACRO: Ad Report - Rendering EMAIL version -->
        <div>
            <!-- DEBUG MACRO: Ad Report EMAIL - Checking grouped_ads -->
            {% if grouped_ads is defined and grouped_ads %} {# Check if dict is not empty #}
                <!-- DEBUG MACRO: Ad Report EMAIL - grouped_ads dict exists -->
                {% set has_ads = False %}
                <!-- DEBUG MACRO: Ad Report EMAIL - About to loop firms -->
                {% for name, group_df in grouped_ads.items() %} {# Iterate dict items #}
                    <!-- DEBUG MACRO: Ad Report EMAIL - Processing firm: {{ name }} -->
                     {# Check if group_df is a DataFrame and not empty #}
                    {% if group_df is defined and not group_df.empty %}
                        {% set valid_ads = group_df %}
                        {% set num_ads = valid_ads|length %}
                        <!-- DEBUG MACRO: Ad Report EMAIL - Firm {{ name }} has {{ num_ads }} ads (pre-filtered) -->
                        {% if num_ads > 0 %}
                            {% set has_ads = True %}
                            {# Use email margin class #}
                            <div class="mb-6-email" style="margin-bottom: 24px;">
                                {# Use email text classes for firm header #}
                                <h3 class="text-lg-email font-semibold-email text-gray-800-email mb-3-email"
                                    style="font-size: 18px; font-weight: 600; color: #1f2937; margin: 0 0 12px 0;">{{ name }}
                                    <span class="text-blue-600-email" style="color: #2563eb;">({{ num_ads }} {{ 'new ad' if num_ads == 1 else 'new ads' }})</span>
                                </h3>
                                {# Use email-table and ad-table classes (defined in _head_email.html) #}
                                <table class="email-table ad-table" width="100%" cellpadding="0" cellspacing="0"
                                       border="0"
                                       style="width: 100%; border-collapse: collapse; margin-bottom: 15px; border: 1px solid #e5e7eb; border-radius: 6px; overflow: hidden;">
                                    <thead>
                                    <tr style="background-color: #f9fafb;">
                                        {# th styles inherited from email-table #}
                                        <th style="width: 60%; border: 1px solid #e5e7eb; padding: 8px 12px; text-align: left; font-size: 12px; background-color: #f9fafb; font-weight: 500; color: #6b7280; text-transform: uppercase;">
                                            Summary
                                        </th>
                                        <th style="width: 20%; border: 1px solid #e5e7eb; padding: 8px 12px; text-align: right; font-size: 12px; background-color: #f9fafb; font-weight: 500; color: #6b7280; text-transform: uppercase;">
                                            Start
                                        </th>
                                        <th style="width: 20%; border: 1px solid #e5e7eb; padding: 8px 12px; text-align: right; font-size: 12px; background-color: #f9fafb; font-weight: 500; color: #6b7280; text-transform: uppercase;">
                                            End
                                        </th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <!-- DEBUG MACRO: Ad Report EMAIL - About to loop valid_ads.iterrows() for {{ name }} -->
                                    {% for _, row in valid_ads.iterrows() %} {# Iterate DataFrame rows #}
                                        <!-- DEBUG MACRO: Ad Report EMAIL - Processing ad ID {{ row.ad_archive_id }} -->
                                        <tr style="background-color: {% if loop.index is even %}#ffffff{% else %}#f9fafb{% endif %};">
                                            {# Use email text classes for td, inheriting border/padding #}
                                            <td class="text-sm-email text-gray-700-email"
                                                style="border: 1px solid #e5e7eb; padding: 12px; font-size: 14px; color: #374151;">
                                                <a href='{{ s3_prod }}/{{ iso_date }}/ads/{{ row.ad_archive_id }}.html'
                                                   target='_blank'
                                                   class="text-blue-600-email font-medium-email" {# Use email classes #}
                                                   style="color: #2563eb; text-decoration: underline; font-weight: 500;">{{ row.summary }}</a>
                                            </td>
                                            <td class="text-sm-email text-gray-500-email text-right-email" {# Use email classes #}
                                                style="border: 1px solid #e5e7eb; padding: 12px; font-size: 14px; color: #6b7280; text-align: right; white-space: nowrap;">{{ row.start_date }}</td>
                                            <td class="text-sm-email text-gray-500-email text-right-email" {# Use email classes #}
                                                style="border: 1px solid #e5e7eb; padding: 12px; font-size: 14px; color: #6b7280; text-align: right; white-space: nowrap;">{{ row.end_date }}</td>
                                        </tr>
                                    {% else %}
                                        <!-- DEBUG MACRO: Ad Report EMAIL - valid_ads.iterrows() loop EMPTY for {{ name }} -->
                                    {% endfor %}
                                    <!-- DEBUG MACRO: Ad Report EMAIL - Finished looping valid_ads for {{ name }} -->
                                    </tbody>
                                </table>
                            </div>
                        {% endif %} {# End if num_ads > 0 #}
                     {% else %}
                         <!-- DEBUG MACRO: Ad Report EMAIL - Group for firm {{ name }} is empty or not valid -->
                     {% endif %} {# End if group_df checks pass #}
                    <!-- DEBUG MACRO: Ad Report EMAIL - Finished processing firm: {{ name }} -->
                {% else %}
                    <!-- DEBUG MACRO: Ad Report EMAIL - grouped_ads loop EMPTY -->
                {% endfor %}
                <!-- DEBUG MACRO: Ad Report EMAIL - Finished looping firms -->
                {% if not has_ads %}
                    <!-- DEBUG MACRO: Ad Report EMAIL - No firms had ads -->
                    {# Use email text classes #}
                    <p class="text-sm-email text-gray-500-email" style="font-size: 14px; color: #6b7280;">No new ad data
                        available.</p>
                {% endif %}
            {% else %}
                <!-- DEBUG MACRO: Ad Report EMAIL - grouped_ads is empty or not defined -->
                 {# Use email text classes #}
                <p class="text-sm-email text-gray-500-email" style="font-size: 14px; color: #6b7280;">No ad data available.</p>
            {% endif %}
        </div>
    {% endif %}
    <!-- DEBUG MACRO: END render_ad_report -->
{% endmacro %}
{# --- END OF render_ad_report MACRO --- #}


{# --- START OF (ORIGINAL) icon MACRO --- #}
{% macro icon(name) %}
    <i class="fas fa-{{ name }}"></i>
{% endmacro %}
{# --- END OF (ORIGINAL) icon MACRO --- #}


{# --- START OF (ORIGINAL) icon_entity MACRO --- #}
{% macro icon_entity(name) %}
    {# ... (icon_entity definition unchanged) ... #}
    {% if name == 'chevron-right' or name == 'angle-right' or name == 'arrow-right' %}
        »
    {% elif name == 'chevron-down' or name == 'angle-down' or name == 'arrow-down' %}
        ↓
    {% elif name == 'chevron-up' or name == 'angle-up' or name == 'arrow-up' %}
        ↑
    {% elif name == 'check' %}
        ✔
    {% elif name == 'times' %}
        ✘
    {% elif name == 'info-circle' %}
        (i)
    {% elif name == 'exclamation-circle' or name == 'exclamation-triangle' %}
        (!)
    {% elif name == 'plus' %}
        +
    {% elif name == 'minus' %}
        -
    {% elif name == 'calendar' or name == 'calendar-alt' %}
        📅
    {% elif name == 'file' or name == 'file-alt' %}
        📄
    {% elif name == 'link' %}
        🔗
    {% else %}
        •
    {% endif %}
{% endmacro %}
{# --- END OF (ORIGINAL) icon_entity MACRO --- #}

{# --- END OF COMPLETE _macros.html (with email class updates) --- #}