
{# Web Scripts - COPIED DIRECTLY FROM index.html #}
<a href="#" id="back-to-top" class="back-to-top"><i class="fas fa-arrow-up"></i></a>
<script>
    // Mobile Menu Toggle (Unchanged from index.html)
    try {
        const menuButton = document.getElementById('mobile-menu-button');
        const mobileMenu = document.getElementById('mobile-menu');
        if (menuButton && mobileMenu) {
            menuButton.addEventListener('click', () => mobileMenu.classList.toggle('hidden'));
            mobileMenu.querySelectorAll('.mobile-nav-link').forEach(link => link.addEventListener('click', () => mobileMenu.classList.add('hidden')));
        }
    } catch (e) {
        console.error("Mobile menu error:", e);
    }

    // Handle litigation click behavior: first click expands, second click collapses and navigates
    function handleLitigationClick(contentId, litigationTargetId, triggerElement) {
        try {
            const contentElement = document.getElementById(contentId);
            const isCurrentlyActive = contentElement ? contentElement.classList.contains('active') : false;
            
            if (isCurrentlyActive) {
                // Second click: collapse and navigate to litigation report
                toggleContent(contentId, triggerElement);
                // Add a small delay to let the collapse animation start, then navigate
                setTimeout(() => {
                    window.location.hash = '#' + litigationTargetId;
                }, 100);
            } else {
                // First click: expand the section
                toggleContent(contentId, triggerElement);
            }
        } catch (e) {
            console.error("Litigation click handler error:", e);
        }
    }

    // Generic Content Toggle Function (Enhanced for hover effects)
    function toggleContent(contentId, triggerElement) {
        try {
            const contentElement = document.getElementById(contentId);
            const iconElement = document.getElementById(contentId + '_icon');
            // Find the closest ancestor card element that needs hover reset
            const cardElement = triggerElement ? triggerElement.closest('.tort-card, .firm-card, .expandable-section-hover') : null;

            if (contentElement && iconElement) {
                const isCurrentlyActive = contentElement.classList.contains('active');
                const isMobile = window.innerWidth <= 768; // Check screen width

                // Perform the toggle
                contentElement.classList.toggle('active');
                const isNowActive = contentElement.classList.contains('active'); // State after toggle
                
                // Update the expanded state of the card element for CSS styling
                if (cardElement) {
                    if (isNowActive) {
                        cardElement.classList.add('expanded');
                    } else {
                        cardElement.classList.remove('expanded');
                        
                        // For mobile only: disable hover effects briefly when collapsing
                        if (isMobile) {
                            cardElement.classList.add('no-hover-effect');
                            // Remove the class shortly after the collapse transition should finish
                            setTimeout(() => {
                                if (cardElement) { // Check if element still exists
                                    cardElement.classList.remove('no-hover-effect');
                                }
                            }, 350); // 300ms transition + 50ms buffer
                        }
                    }
                }
                
                // Update icon
                iconElement.classList.toggle('fa-plus', !isNowActive);
                iconElement.classList.toggle('fa-minus', isNowActive);
            } else {
                if (!contentElement) console.warn(`Toggle target content not found: #${contentId}`);
                if (!iconElement) console.warn(`Toggle target icon not found: #${contentId}_icon`);
                if (triggerElement && !cardElement) console.warn(`Could not find parent card for hover reset on element:`, triggerElement);
            }
        } catch (e) {
            console.error("Toggle content error:", e);
        }
    }
    
    // Add hover functionality for expandable sections
    document.addEventListener('DOMContentLoaded', function() {
        // Find all expandable sections
        const expandableSections = document.querySelectorAll('.expandable-section-hover');
        
        expandableSections.forEach(section => {
            // Find the content element within this section
            const contentId = section.querySelector('.toggle-content')?.id;
            if (!contentId) return;
            
            // Find if this section is already expanded
            const isExpanded = section.querySelector('.toggle-content.active') !== null;
            if (isExpanded) {
                section.classList.add('expanded');
            }
            
            // Apply hover effects for both the header and entire section
            const header = section.querySelector('.collapsible-header');
            if (header) {
                // Adding hover effects for the header
                header.addEventListener('mouseenter', function() {
                    section.classList.add('hovering');
                });
                
                header.addEventListener('mouseleave', function() {
                    section.classList.remove('hovering');
                });
                
                // Also handle hover on the entire section for consistent behavior
                section.addEventListener('mouseenter', function() {
                    // Add hover class regardless of expanded state
                    // This will trigger the width expansion
                    section.classList.add('hovering');
                });
                
                section.addEventListener('mouseleave', function() {
                    // Remove hover class when mouse leaves
                    // This will allow the section to shrink when not hovered
                    section.classList.remove('hovering');
                });
            }
        });
    });

    // Back to Top Button (Unchanged from index.html)
    try {
        const backToTopButton = document.getElementById('back-to-top');
        if (backToTopButton) {
            window.onscroll = () => {
                backToTopButton.style.display = (document.body.scrollTop > 300 || document.documentElement.scrollTop > 300) ? "block" : "none";
            };
            backToTopButton.addEventListener('click', e => {
                e.preventDefault();
                window.scrollTo({top: 0, behavior: 'smooth'});
            });
        }
    } catch (e) {
        console.error("Back-to-top error:", e);
    }

    // Smooth Scrolling, Hash Handling & Auto-Expand (Unchanged from index.html)
    try {
        const HEADER_HEIGHT_NAV = 180; // Updated for larger navbar
        const EXTRA_PADDING = 20;     // Matches index.html

        function smoothScroll(targetId) {
            const targetElement = document.getElementById(targetId);
            if (targetElement) {
                const elementPosition = targetElement.getBoundingClientRect().top;
                const offsetPosition = elementPosition + window.pageYOffset - HEADER_HEIGHT_NAV - EXTRA_PADDING;
                window.scrollTo({top: offsetPosition, behavior: "smooth"});
                console.log(`Scrolled to ${targetId}`);
            } else {
                console.warn("Smooth scroll target element not found:", targetId);
            }
        }

        function expandTargetContent(targetElementId) {
            // Enhanced mapping logic for nested collapsible structure
            const mappings = {
                'litigation_': 'tort_content_',
                'detail_': 'firm_content_',
                'announcements': 'announcements_content',
                'afff-numbers': 'afff_numbers_content',
                'upcoming-hearings': 'upcoming_hearings_content',
                'book-meeting': 'book_meeting_content',
                'special-reports': 'special_reports_content',
                'news': 'news_content',
                'footer': 'footer_content'
            };
            let contentId = null;
            for (const prefix in mappings) {
                if (targetElementId.startsWith(prefix)) {
                    if (mappings[prefix].endsWith('_')) {
                        contentId = targetElementId.replace(prefix, mappings[prefix]);
                    } else {
                        contentId = mappings[prefix];
                    }
                    break;
                }
            }
            // Handle cases where the ID matches a key directly (like 'news', 'footer', etc.)
            if (!contentId && mappings[targetElementId]) {
                contentId = mappings[targetElementId];
            }

            if (contentId) {
                const contentElement = document.getElementById(contentId);
                // IMPORTANT: Need the trigger element for index.html's toggleContent
                const trigger = contentElement ? contentElement.closest('.expandable-section-hover, .tort-card, .firm-card')?.querySelector('.collapsible-header') : null;
                // Check if content exists, trigger exists, and content is NOT already active
                if (contentElement && trigger && !contentElement.classList.contains('active')) {
                    console.log(`Auto-expanding content: ${contentId}`);
                    // Use a slight delay to ensure smooth scrolling calculation happens after expansion starts
                    setTimeout(() => {
                        // Pass the trigger element to toggleContent as index.html does
                        toggleContent(contentId, trigger);
                    }, 150); // Delay from index.html
                } else if (contentElement) {
                     console.log(`Content already expanded or trigger missing: ${contentId}`);
                } else {
                    console.warn(`Content element not found for auto-expand: ${contentId}`);
                }
            } else {
                console.log(`No content mapping found for target: ${targetElementId}`);
            }
        }
        
        // Enhanced function to handle nested expansion for detailed filings
        function expandNestedContent(firmId, litigationId) {
            // First expand the firm content
            const firmContentId = `firm_content_${firmId}`;
            const firmContentElement = document.getElementById(firmContentId);
            const firmTrigger = firmContentElement ? firmContentElement.closest('.firm-card')?.querySelector('.collapsible-header') : null;
            
            if (firmContentElement && firmTrigger && !firmContentElement.classList.contains('active')) {
                console.log(`Auto-expanding firm content: ${firmContentId}`);
                toggleContent(firmContentId, firmTrigger);
                
                // Then expand the litigation content after a delay to ensure firm is expanded first
                if (litigationId) {
                    setTimeout(() => {
                        const litigationContentId = `litigation_content_${firmId}_${litigationId}`;
                        const litigationContentElement = document.getElementById(litigationContentId);
                        const litigationTrigger = litigationContentElement ? 
                            litigationContentElement.closest('.expandable-section-hover')?.querySelector('.collapsible-header') : null;
                        
                        if (litigationContentElement && litigationTrigger && !litigationContentElement.classList.contains('active')) {
                            console.log(`Auto-expanding litigation content: ${litigationContentId}`);
                            toggleContent(litigationContentId, litigationTrigger);
                        }
                    }, 200); // Allow time for firm expansion
                }
            }
        }

        // Attach click listener to all internal links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                const targetIdRaw = this.getAttribute('href');
                // Ensure it's a valid hash link and not the back-to-top button
                if (targetIdRaw && targetIdRaw.startsWith('#') && targetIdRaw.length > 1 && this.id !== 'back-to-top') {
                    try {
                        // Use URL constructor for robust hash parsing
                        const targetUrl = new URL(targetIdRaw, window.location.href);
                        const targetElementId = targetUrl.hash.substring(1).split('?')[0]; // Get ID part before '?'
                        const targetElement = document.getElementById(targetElementId);

                        if (targetElement) {
                            e.preventDefault(); // Prevent default jump
                            // Manually trigger hash change to use the unified handler
                            // This mirrors how index.html likely implicitly handles clicks
                            window.location.hash = targetIdRaw;
                            // Call handlers immediately as hashchange might have a slight delay browser-side
                            smoothScroll(targetElementId);
                            expandTargetContent(targetElementId);
                        } else {
                            console.warn("Anchor scroll target not found:", targetElementId);
                        }
                    } catch (urlError) {
                        console.error("Error parsing anchor href or scrolling:", urlError);
                    }
                }
            });
        });

        // Handle initial hash on page load
        function handleInitialHash() {
            if (window.location.hash && window.location.hash.length > 1) {
                try {
                    const targetElementId = window.location.hash.substring(1).split('?')[0];
                    const targetElement = document.getElementById(targetElementId);
                    if (targetElement) {
                        console.log(`Handling initial hash: ${targetElementId}`);
                        // Delay slightly to ensure page layout is stable
                        setTimeout(() => {
                            smoothScroll(targetElementId);
                            expandTargetContent(targetElementId);
                        }, 150); // Matches index.html
                    } else {
                        console.warn("Initial Hash - Target element not found:", targetElementId);
                    }
                } catch (e) {
                    console.error("Error handling initial hash:", e);
                }
            }
        }

        // Listen for load event to handle initial hash
        window.addEventListener('load', handleInitialHash);

        // Also listen for hashchange events *after* initial load
         window.addEventListener('hashchange', () => {
             console.log("Hash changed, running handleInitialHash logic again.");
             handleInitialHash(); // Re-run the logic on hash change
         });


    } catch (e) {
        console.error("Smooth scroll/auto-expand setup error:", e);
    }

    // Intersection Observer for Animations (Unchanged from index.html)
    try {
        const animatedElements = document.querySelectorAll('.animate-on-scroll');
        if (animatedElements.length > 0) {
            const observer = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('visible');
                        observer.unobserve(entry.target);
                    }
                });
            }, {threshold: 0.1});
            animatedElements.forEach(el => observer.observe(el));
        } else {
            // console.log("No elements found with .animate-on-scroll class."); // Optional log
        }
    } catch (e) {
        console.error("Intersection Observer error:", e);
    }

    // Chart.js Initialization (Unchanged from index.html)
    try {
        if (typeof Chart !== 'undefined' && typeof ChartDataLabels !== 'undefined') {
            const ctx = document.getElementById('tort-chart')?.getContext('2d');
            const fallbackElement = document.getElementById('chart-fallback');
            // Register plugin if needed
             if (!Chart.registry.plugins.get('datalabels')) {
                 Chart.register(ChartDataLabels);
                 console.log("ChartDataLabels plugin registered.");
             }

            if (ctx) {
                // --- CHART DATA - Use Jinja variables directly ---
                const finalChartData = {{ chart_data|tojson|safe if chart_data is defined else '[]' }};
                const finalChartLabels = {{ chart_labels|tojson|safe if chart_labels is defined else '[]' }};
                // --- END CHART DATA ---

                // Basic validation
                if (Array.isArray(finalChartData) && Array.isArray(finalChartLabels) && finalChartData.length > 0 && finalChartData.length === finalChartLabels.length) {
                    if (fallbackElement) fallbackElement.classList.add('hidden'); // Hide fallback if data is valid

                    // Gradient function (no change)
                    const createGradient = (chartContext, chartArea) => {
                        const chart = chartContext.chart || chartContext;
                        const area = chartArea || chart.chartArea;
                        if (!area) return null;
                        const gradient = chart.ctx.createLinearGradient(0, area.bottom, 0, area.top);
                        gradient.addColorStop(0, 'rgba(57,190,250,0.6)');
                        gradient.addColorStop(1, 'rgba(17,81,151,0.8)');
                        return gradient;
                    };

                    const isMobile = () => window.innerWidth <= 768;
                    const getFontSize = () => isMobile() ? 10 : 12;
                    const barsToShow = isMobile() ? 5 : 10;

                    {#const sortedData = chartDataRaw.map((v, i) => ({ value: v || 0, label: chartLabelsRaw[i] || 'Unknown' })).sort((a, b) => b.value - a.value);#}
                    {#const dataToShow = sortedData.slice(0, barsToShow);#}
                    {#const sortedLabels = dataToShow.map(item => item.label);#}
                    {#const sortedValues = dataToShow.map(item => item.value);#}


                    new Chart(ctx, {
                        type: 'bar',
                        data: {
                            // *** USE FINAL LISTS DIRECTLY ***
                            labels: finalChartLabels, // Use the variable directly from context
                            datasets: [{
                                data: finalChartData, // Use the variable directly from context
                                borderWidth: 0,
                                borderRadius: 4,
                                backgroundColor: (c) => createGradient(c, c.chart.chartArea)
                            }]
                        },
                        options: { // Keep existing options
                            indexAxis: 'y', responsive: true, maintainAspectRatio: false,
                            plugins: {
                                legend: { display: false },
                                tooltip: { backgroundColor: '#115197', titleColor: '#ffffff', bodyColor: '#ffffff', padding: 10, cornerRadius: 4 },
                                datalabels: {
                                    color: '#ffffff', anchor: 'center', align: 'center', font: { size: getFontSize(), weight: 'bold' },
                                    formatter: (v) => v > 0 ? v : '', // Format value
                                    // Display label based on value (e.g., hide if too small)
                                    display: (context) => context.dataset.data[context.dataIndex] > 0 // Show all non-zero values
                                }
                            },
                            layout: { padding: { left: 5, right: 20, top: 10, bottom: 5 } },
                            scales: {
                                x: { // Keep x-axis config
                                    type: 'logarithmic', min: 0.5,
                                    grid: { drawBorder: false, color: (c) => (c.tick.value === 1 || [10, 100, 1000, 10000].includes(c.tick.value)) ? 'rgba(0,0,0,0.05)' : 'transparent' },
                                    ticks: { display: false },
                                    title: { display: true, text: 'Number of Filings (past 30 days, log scale)', font: { size: getFontSize(), weight: 'normal' }, color: '#6b7280', padding: { top: 10 } }
                                },
                                y: { // Keep y-axis config
                                    grid: { display: false },
                                    ticks: {
                                        font: { size: getFontSize() }, color: '#374151',
                                        // Ensure callback uses the correct label from the `labels` array
                                        callback: function (value, index, ticks) {
                                            // In v3/v4, the first arg is tick value, second is index. Use index to get label.
                                            const label = this.chart.data.labels[index];
                                            if (!label) return ''; // Handle undefined label
                                            if (label.includes('Lejeune')) return 'Camp Lejeune';
                                            return label.length > 25 ? label.substring(0, 22) + '...' : label;
                                        }
                                    }
                                }
                            }
                        }
                    });
                    console.log("Chart initialized successfully with pre-processed data.");
                } else {
                    console.warn("Chart rendering skipped: Invalid or empty data received from context.");
                    if (fallbackElement) { fallbackElement.textContent = 'Chart data unavailable or empty.'; fallbackElement.classList.remove('hidden'); }
                }
            } else {
                console.warn("Chart canvas element ('tort-chart') not found.");
                if (fallbackElement) { fallbackElement.textContent = 'Chart canvas not found.'; fallbackElement.classList.remove('hidden'); }
            }
        } else {
            console.warn("Chart.js or ChartDataLabels plugin not loaded.");
            const fallbackElement = document.getElementById('chart-fallback');
            if (fallbackElement) { fallbackElement.textContent = 'Chart library not loaded.'; fallbackElement.classList.remove('hidden'); }
        }
    } catch (e) {
        console.error("Chart.js initialization error:", e);
        const fallbackElement = document.getElementById('chart-fallback');
        if (fallbackElement) { fallbackElement.textContent = 'Error loading chart.'; fallbackElement.classList.remove('hidden'); fallbackElement.style.color = 'red'; }
    }

</script>
