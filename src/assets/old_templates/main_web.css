/* Common styles */
body {
    font-family: '<PERSON><PERSON>', <PERSON>l, sans-serif;
    line-height: 1.6;
    color: #333;
    margin: 0;
    padding: 0;
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

h1, h2, h3 {
    color: #115197;
}

a {
    color: #39BEFA;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

/* Header styles */
header {
    background-color: #115197;
    color: white;
    text-align: center;
    padding: 20px 0;
}

.newsletter-signup {
    background-color: #39BEFA;
    color: white;
    padding: 10px;
    margin-bottom: 20px;
}

/* Table styles */
table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

th, td {
    padding: 10px;
    border: 1px solid #ddd;
}

th {
    background-color: #f2f2f2;
    font-weight: bold;
}

.title-row {
    background-color: #115197;
    color: white;
}

.secondary-title {
    background-color: #39BEFA;
    color: white;
}

/* Responsive design */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }

    table {
        font-size: 14px;
    }

    th, td {
        padding: 5px;
    }
}

/* Web-specific styles */
#top-nav {
    background-color: #115197;
    position: sticky;
    top: 0;
    z-index: 1000;
}

#top-nav ul {
    list-style-type: none;
    padding: 0;
    margin: 0;
    display: flex;
    justify-content: space-around;
}

#top-nav li {
    padding: 10px;
}

#top-nav a {
    color: white;
    text-decoration: none;
}

#back-to-top {
    display: none;
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 99;
    border: none;
    outline: none;
    background-color: #115197;
    color: white;
    cursor: pointer;
    padding: 15px;
    border-radius: 50%;
}

/* Email-specific styles */
.email-specific {
    background-color: #f2f2f2;
    padding: 10px;
    margin-bottom: 20px;
}

.keep-receiving {
    color: #32CD32;
}

/* Chart styles */
.chart-container {
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
}

/* Accessibility improvements */
.visually-hidden {
    position: absolute !important;
    height: 1px;
    width: 1px;
    overflow: hidden;
    clip: rect(1px 1px 1px 1px); /* IE6, IE7 */
    clip: rect(1px, 1px, 1px, 1px);
    white-space: nowrap; /* added line */
}

/* Dark mode styles */
@media (prefers-color-scheme: dark) {
    body {
        background-color: #222;
        color: #f0f0f0;
    }

    .container {
        background-color: #333;
    }

    th, td {
        border-color: #444;
    }

    th {
        background-color: #444;
    }

    .title-row {
        background-color: #115197;
    }

    .secondary-title {
        background-color: #39BEFA;
        color: #222;
    }
}