<!DOCTYPE html>
<html lang="en" prefix="og: http://ogp.me/ns# fb: http://ogp.me/ns/fb#">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LexGenius: Mass Tort Report</title>
    <link rel="icon" type="image/x-icon" href="https://cdn.lexgenius.ai/bimi/lexgenius_logo.svg">
    {# Web only assets #}
    {% if is_web %}
        <script src="https://cdn.tailwindcss.com?plugins=typography"></script>
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"
              rel="stylesheet">
        <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.7.1/chart.min.js"></script>
        <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels@2.0.0"></script>
    {% endif %}

    <meta property="fb:app_id" content="495908463340329">
    <meta property="og:title" content="LexGenius Mass Tort Report">
    <meta property="og:description" content="Daily report on mass tort filings and legal ads">
    <meta property="og:image" content="https://cdn.lexgenius.ai/assets/images/lexgenius_preview.jpg">
    <meta property="og:image:width" content="1024">
    <meta property="og:image:height" content="1024">
    <meta property="og:url" content="https://cdn.lexgenius.ai/assets/images/lexgenius_preview.jpg">
    <meta property="og:type" content="website">
    <meta property="og:site_name" content="LexGenius">
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="LexGenius Mass Tort Report">
    <meta name="twitter:description" content="Daily report on mass tort filings and legal ads">
    <meta name="twitter:image" content="https://cdn.lexgenius.ai/assets/images/lexgenius_preview.jpg">
    <meta name="apple-mobile-web-app-title" content="LexGenius Report">

    {# Basic Email Styles #}
    <style type="text/css">
        body {
            margin: 0;
            padding: 0;
            background-color: #f8fafc;
            font-family: 'Inter', Arial, sans-serif;
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
        }

        table {
            border-collapse: collapse;
            mso-table-lspace: 0pt;
            mso-table-rspace: 0pt;
        }

        td {
            vertical-align: top;
        }

        img {
            display: block;
            border: 0;
            outline: none;
            text-decoration: none;
            -ms-interpolation-mode: bicubic;
            max-width: 100%;
            height: auto;
        }

        a {
            text-decoration: underline;
            color: #2563eb;
        }

        a:hover {
            text-decoration: none;
        }

        .email-container {
            width: 100%;
            max-width: 800px;
            margin: 0 auto;
            background-color: #ffffff;
        }

        .content-padding {
            padding: 20px 30px;
        }

        .section-title {
            font-size: 22px;
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 24px;
            margin-top: 0;
        }

        .section-title a {
            color: #1f2937;
            text-decoration: none;
        }

        .section-title a:hover {
            color: #2563eb;
            text-decoration: underline;
        }

        .text-sm {
            font-size: 14px;
            line-height: 1.6;
        }

        .text-gray-700 {
            color: #374151;
        }

        .text-gray-600 {
            color: #4b5563;
        }

        .text-gray-500 {
            color: #6b7280;
        }

        .font-bold {
            font-weight: bold;
        }

        .font-medium {
            font-weight: 500;
        }

        .text-blue-600 {
            color: #2563eb;
        }

        .text-blue-800 {
            color: #1e40af;
        }

        .text-right {
            text-align: right;
        }

        .mb-4 {
            margin-bottom: 16px;
        }

        .mb-6 {
            margin-bottom: 24px;
        }

        .mb-8 {
            margin-bottom: 32px;
        }

        .mt-4 {
            margin-top: 16px;
        }

        .mt-6 {
            margin-top: 24px;
        }

        .mt-8 {
            margin-top: 32px;
        }

        .email-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
        }

        .email-table th, .email-table td {
            border: 1px solid #e5e7eb;
            padding: 8px 12px;
            text-align: left;
            font-size: 14px;
            vertical-align: top;
            line-height: 1.5;
        }

        .email-table th {
            background-color: #f9fafb;
            font-weight: 500;
            color: #6b7280;
            text-transform: uppercase;
            font-size: 12px;
        }

        .email-total-row td {
            background-color: #f3f4f6;
            font-weight: bold;
            color: #1f2937;
        }

        .email-button {
            display: inline-block;
            background-color: #2563eb;
            color: #ffffff !important;
            padding: 12px 25px;
            text-decoration: none !important;
            border-radius: 6px;
            font-weight: bold;
            font-size: 14px;
            line-height: 1;
            white-space: nowrap;
        }

        .email-button:hover {
            background-color: #1d4ed8;
        }

        .category-header {
            font-size: 20px;
            font-weight: 600;
            color: #374151;
            padding-bottom: 8px;
            border-bottom: 1px solid #d1d5db;
            margin-bottom: 16px;
        }

        .email-collapsed-section {
            background-color: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 0;
            margin-bottom: 16px;
            display: block;
            text-decoration: none !important;
        }

        .email-collapsed-section:hover {
            background-color: #f3f4f6;
            border-color: #d1d5db;
        }

        .email-collapsed-section td {
            padding: 12px 15px;
        }

        .email-collapsed-section a {
            display: block;
            text-decoration: none !important;
            color: #1f2937 !important;
        }

        .email-collapsed-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937 !important;
            line-height: 1.4;
            margin: 0;
            padding: 0;
            text-decoration: none !important;
            display: inline; /* Changed */
            overflow: hidden;
            vertical-align: middle;
        }

        .email-collapsed-arrow {
            color: #6b7280 !important;
            font-size: 18px;
            font-weight: bold;
            margin-left: 6px; /* Reduced margin */
            text-decoration: none !important;
            display: inline; /* Changed */
            line-height: 1.4;
            vertical-align: middle;
        }

        /* --- START: EMAIL RESPONSIVE --- */
        @media screen and (max-width: 600px) {
            body {
                padding: 0 !important;
            }

            .email-container {
                width: 100% !important;
                max-width: none !important;
                margin: 0 !important;
            }

            .content-padding {
                padding: 15px 15px !important;
            }

            .email-header {
                margin-left: 0 !important;
                margin-right: 0 !important;
                padding: 20px 15px !important;
            }

            .section-title {
                font-size: 20px !important;
                margin-bottom: 15px !important;
            }

            .stat-card-table td {
                display: block !important;
                width: 100% !important;
                max-width: none !important;
                padding: 0 0 15px 0 !important;
                box-sizing: border-box;
                vertical-align: top !important;
            }

            .stat-card-table tr td:last-child {
                padding-bottom: 0 !important;
            }

            .stat-card-email-inner {
                padding: 15px !important;
            }

            .stat-card-link {
                display: block;
                text-decoration: none !important;
                color: inherit !important;
            }

            .ad-table td, .ad-table th, .email-table th, .email-table td {
                font-size: 13px !important;
                padding: 8px 10px !important;
            }

            .ad-table td:nth-child(2), .ad-table td:nth-child(3) {
                white-space: normal !important;
            }

            .category-header {
                font-size: 18px !important;
            }

            .email-header h1 {
                font-size: 24px !important;
                margin-bottom: 6px !important;
            }

            .email-header p.email-date {
                font-size: 16px !important;
                margin: 0 !important;
            }

            .stat-card-email-inner p {
                font-size: 13px !important;
            }

            .stat-card-email-inner .stat-value {
                font-size: 20px !important;
            }

            .email-collapsed-title {
                font-size: 15px !important;
                display: inline !important;
            }

            /* Ensure inline */
            .email-collapsed-arrow {
                font-size: 16px !important; /* Slightly smaller arrow */
                display: inline !important; /* Ensure inline */
                margin-left: 4px !important; /* Adjust spacing */
                float: none !important; /* Override float if needed */
            }

            /* Ensure the container TD allows inline elements */
            .email-collapsed-section td {
                padding: 10px 12px !important;
                line-height: 1.4; /* Match title/arrow line-height */
            }

            .email-collapsed-section a > div { /* Target the inner div if present */
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis; /* Prevent wrapping if title is very long */
            }

            .litigation-sponsor-email .sponsor-text-cell, .litigation-sponsor-email .sponsor-button-cell {
                display: inline-block !important;
                width: 66% !important;
                max-width: 66% !important;
                vertical-align: middle !important;
                padding-right: 5px !important;
                padding-left: 0 !important;
                padding-bottom: 0 !important;
                box-sizing: border-box !important;
                font-size: 14px !important;
            }

            .litigation-sponsor-email .sponsor-button-cell {
                width: 33% !important;
                max-width: 33% !important;
                text-align: center !important;
                padding-right: 0 !important;
                padding-left: 5px !important;
                font-size: 14px !important;
            }

            .litigation-sponsor-email .sponsor-text-cell p {
                text-align: left !important;
                font-size: 13px !important;
                line-height: 1.4 !important;
                margin: 0 !important;
            }

            .litigation-sponsor-email .email-button {
                padding: 8px 12px !important;
                font-size: 12px !important;
            }

            .header-sponsor-email-outer td {
                padding: 15px 15px !important;
            }

            .header-sponsor-email-inner td {
                display: block !important;
                width: 100% !important;
                max-width: 100% !important;
                text-align: center !important;
                padding: 0 !important;
                vertical-align: middle !important;
            }

            .header-sponsor-email-inner .sponsor-logo-cell {
                padding-bottom: 10px !important;
            }

            .header-sponsor-email-inner .sponsor-logo-cell img {
                margin: 0 auto !important;
                max-width: 180px !important;
                display: block !important;
            }

            .header-sponsor-email-inner .sponsor-text-cell {
                font-size: 14px !important;
                line-height: 1.5 !important;
                text-align: center !important;
                padding: 0 10px 10px 10px !important;
            }

            .header-sponsor-email-inner .sponsor-button-cell {
                padding-top: 5px !important;
                text-align: center !important;
            }

            .header-sponsor-email-inner .email-button {
                padding: 10px 20px !important;
                font-size: 14px !important;
            }

            #detailed-filings .mb-8 {
                margin-bottom: 24px !important;
            }

            #detailed-filings h3 {
                font-size: 17px !important;
                margin-bottom: 10px !important;
            }

            #detailed-filings .mb-4 {
                margin-bottom: 12px !important;
            }

            #detailed-filings .mb-4 > div {
                padding: 12px !important;
            }

            #detailed-filings h4 {
                font-size: 14px !important;
                margin-bottom: 6px !important;
            }

            #detailed-filings ul li {
                font-size: 13px !important;
                margin-bottom: 6px !important;
            }
        }

        /* --- END: EMAIL RESPONSIVE --- */
    </style>

    {# Web only styles #}
    {% if is_web %}
        <style>
            body {
                font-family: 'Inter', sans-serif;
                background-color: #f8fafc;
            }

            .gradient-header {
                background: linear-gradient(135deg, #115197 0%, #39BEFA 100%);
            }

            .highlight-box {
                background: linear-gradient(135deg, rgba(17, 81, 151, 0.1) 0%, rgba(57, 190, 250, 0.1) 100%);
                border-left: 4px solid #115197;
            }

            .tort-card, .firm-card, .expandable-section-hover {
                transition: transform 0.3s ease, box-shadow 0.3s ease;
                box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
                background-color: #ffffff;
                border-radius: 0.75rem;
                overflow: hidden;
                border: 1px solid #e5e7eb;
                position: relative;
            }

            /* Default Hover (Desktop) */
            .tort-card:hover, .firm-card:hover, .expandable-section-hover:not(#footer *):hover {
                transform: scale(1.03);
                box-shadow: 0 12px 28px rgba(0, 0, 0, 0.12);
                z-index: 10;
            }

            /* --- START: Web Hover Reset CSS (Mobile Only via Media Query) --- */
            @media (max-width: 768px) {
                /* When the 'no-hover-effect' class is present (added by JS on collapse), disable hover styles */
                .tort-card.no-hover-effect:hover,
                .firm-card.no-hover-effect:hover,
                .expandable-section-hover.no-hover-effect:hover {
                    transform: none !important; /* Prevent scaling */
                    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05) !important; /* Revert to base shadow */
                    z-index: auto !important; /* Revert z-index */
                }
            }

            /* --- END: Web Hover Reset CSS --- */
            #footer > div.expandable-section-hover {
                background-color: inherit;
            }

            /* Keep footer dark */
            #footer > div.expandable-section-hover:hover {
                transform: none;
                box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
                z-index: auto;
            }

            /* Prevent footer hover effect */
            .nav-link {
                position: relative;
                padding-bottom: 4px;
            }

            .nav-link:after {
                content: '';
                position: absolute;
                width: 0;
                height: 2px;
                bottom: 0;
                left: 0;
                background-color: white;
                transition: width 0.3s ease;
            }

            .nav-link:hover:after {
                width: 100%;
            }

            .stat-card {
                background: white;
                border-radius: 8px;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
                display: block;
                text-decoration: none;
                color: inherit;
                transition: transform 0.2s ease, box-shadow 0.2s ease;
            }

            .stat-card:hover {
                transform: translateY(-3px);
                box-shadow: 0 6px 12px rgba(0, 0, 0, 0.08);
            }

            .stat-card-primary {
                border-top: 4px solid #115197;
            }

            .stat-card-secondary {
                border-top: 4px solid #39BEFA;
            }

            .toggle-content {
                max-height: 0;
                overflow: hidden;
                transition: max-height 0.5s ease-in-out;
            }

            .toggle-content.active {
                max-height: 5000px;
            }

            .toggle-icon {
                transition: transform 0.3s ease;
            }

            .toggle-icon.rotate-180 {
                transform: rotate(180deg);
            }

            .back-to-top {
                position: fixed;
                bottom: 20px;
                right: 20px;
                z-index: 99;
                width: 50px;
                height: 50px;
                border-radius: 50%;
                background-color: #115197;
                color: white;
                text-align: center;
                line-height: 50px;
                font-size: 20px;
                box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
                transition: all 0.3s ease;
                display: none;
            }

            .back-to-top:hover {
                background-color: #39BEFA;
                transform: translateY(-3px);
            }

            .table-row-hover:hover {
                background-color: #f1f5f9;
            }

            #litigation-report a, #detailed-filings a, #adspy-report a, #upcoming-hearings a, #special-reports a, #news a {
                color: #2563eb;
                text-decoration: none;
            }

            #litigation-report a:hover, #detailed-filings a:hover, #adspy-report a:hover, #upcoming-hearings a:hover, #special-reports a:hover, #news a:hover {
                text-decoration: underline;
            }

            #summary .stat-card a {
                text-decoration: none;
            }

            #detailed-filings .firm-card-content a {
                color: #2563eb;
            }

            #detailed-filings .tort-subtitle a {
                color: #1e40af;
                font-weight: 500;
            }

            #detailed-filings .tort-subtitle a:hover {
                text-decoration: underline;
            }

            #detailed-filings .case-link a {
                color: #2563eb;
            }

            .list-item-link {
                display: flex;
                align-items: center;
                padding: 10px 15px;
                border: 1px solid #e5e7eb;
                border-radius: 6px;
                text-decoration: none;
                transition: background-color 0.2s ease, border-color 0.2s ease;
                margin-bottom: 10px;
                background-color: #fff;
            }

            .list-item-link:hover {
                background-color: #f9fafb;
                border-color: #d1d5db;
            }

            .list-item-icon {
                color: #4b5563;
                margin-right: 12px;
                width: 18px;
                text-align: center;
            }

            .list-item-text {
                color: #1f2937;
                font-weight: 500;
                font-size: 14px;
                line-height: 1.4;
            }

            .list-item-link:hover .list-item-text {
                color: #115197;
            }

            section[id] {
                scroll-margin-top: 90px;
            }

            @keyframes fadeIn {
                from {
                    opacity: 0;
                }
                to {
                    opacity: 1;
                }
            }

            @keyframes slideUp {
                from {
                    transform: translateY(20px);
                    opacity: 0;
                }
                to {
                    transform: translateY(0);
                    opacity: 1;
                }
            }

            .animate-on-scroll {
                opacity: 0;
                transform: translateY(20px);
                transition: opacity 0.6s ease-out, transform 0.6s ease-out;
                will-change: opacity, transform;
            }

            .animate-on-scroll.visible {
                opacity: 1;
                transform: translateY(0);
            }

            /* --- START: Arrow Alignment Style (Web Only) --- */
            .collapsible-header { /* Applied to the main header div (the flex container) */
                display: flex;
                align-items: center;
                justify-content: space-between; /* Pushes groups apart */
            }

            .collapsible-header-group { /* Applied to the div grouping icon+title+arrow */
                display: flex;
                align-items: center;
                min-width: 0; /* Allows text truncation */
                flex-shrink: 1; /* Allow this group to shrink if needed */
                margin-right: 0.5rem; /* Space before counts/other elements */
            }

            .collapsible-header-title { /* Applied to H2/H3 inside the group */
                /* Ensure truncate works */
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                margin-right: 0.5rem; /* Space between title and arrow */
            }

            .collapsible-header > span.toggle-icon { /* Direct child icon if not in group */
                flex-shrink: 0;
            }

            .collapsible-header-group > span.toggle-icon { /* Icon inside the group */
                flex-shrink: 0; /* Prevent icon shrinking */
            }

            /* --- END: Arrow Alignment Style --- */

            @media (max-width: 768px) {
                section[id] {
                    scroll-margin-top: 70px;
                }

                .list-item-text {
                    font-size: 13px;
                }

                .firm-stats {
                    flex-wrap: wrap;
                    justify-content: flex-end;
                }

                .litigation-sponsor-web .md\:w-1\/4 {
                    flex-basis: 20%;
                }

                .litigation-sponsor-web .md\:w-1\/2 {
                    flex-basis: 55%;
                }

                .litigation-sponsor-web .md\:w-1\/4:last-child {
                    flex-basis: 25%;
                }

                .litigation-sponsor-web img {
                    height: 2.5rem;
                }

                .summary-stats-grid {
                    grid-template-columns: 1fr;
                }

                .space-y-6 > :not([hidden]) ~ :not([hidden]) {
                    margin-top: 1rem;
                    margin-bottom: 0;
                }

                .space-y-4 > :not([hidden]) ~ :not([hidden]) {
                    margin-top: 0.75rem;
                    margin-bottom: 0;
                }

                .header-sponsorship-web .md\:flex-row {
                    flex-direction: column;
                }

                .header-sponsorship-web .md\:items-center {
                    align-items: center;
                }

                .header-sponsorship-web .md\:w-1\/4, .header-sponsorship-web .md\:w-1\/2 {
                    width: 100%;
                }

                .header-sponsorship-web .md\:justify-start {
                    justify-content: center;
                }

                .header-sponsorship-web .md\:text-left {
                    text-align: center;
                }

                .header-sponsorship-web .md\:justify-end {
                    justify-content: center;
                }

                .header-sponsorship-web .md\:space-x-4 > :not([hidden]) ~ :not([hidden]) {
                    margin-left: 0;
                    margin-right: 0;
                }

                .header-sponsorship-web .md\:mt-0 {
                    margin-top: 0.75rem;
                }

                .header-sponsorship-web .md\:pr-2 {
                    padding-right: 0;
                }

                .header-sponsorship-web .md\:w-1\/4:last-child {
                    margin-top: 1rem;
                }
            }
        </style>
    {% endif %}

    {% if is_web %} {# GA #}
        <script async src="https://www.googletagmanager.com/gtag/js?id=G-9CREQVVWNF"></script>
        <script> window.dataLayer = window.dataLayer || [];

        function gtag() {
            dataLayer.push(arguments);
        }

        gtag('js', new Date());
        gtag('config', 'G-9CREQVVWNF'); </script> {% endif %}
</head>
<body {% if not is_web %}style="margin: 0; padding: 0; background-color: #f8fafc; font-family: 'Inter', Arial, sans-serif;"{% endif %}
      class="{% if is_web %}antialiased text-gray-800 bg-gray-50{% else %}bg-gray-50{% endif %}">

{# Macros #}
{% macro render_litigation_table(title, allegations_and_causes, sub_df, is_web, s3_prod, iso_date) %}
    {% set title_id = title|clean_id %}
    {% set current_mdl_num = sub_df.mdl_num.iloc[0] if sub_df is defined and not sub_df.empty and 'mdl_num' in sub_df.columns and not sub_df.mdl_num.empty and not sub_df.mdl_num.iloc[0] in ['NA', None, ''] else '' %}
    {% set sponsor = get_litigation_sponsor(current_mdl_num, is_web) %}
    {% if is_web %}
        <div id="litigation_{{ title_id }}" class="tort-card animate-on-scroll expandable-section-hover">
            {# Added expandable-section-hover #}
            <div class="collapsible-header px-4 py-3 sm:px-6 cursor-pointer border-b border-gray-200"
                 onclick="toggleContent('tort_content_{{ title_id }}', this)">
                {# Group Title #}
                <div class="collapsible-header-group"> {# Removed flex-1 here #}
                    <h3 class="collapsible-header-title text-lg font-semibold text-gray-800"
                        title="{{ title }}">{{ title }}</h3>
                    {# Icon follows title in this group #}
                    <span id="tort_content_{{ title_id }}_icon"
                          class="fas fa-plus toggle-icon text-gray-500 text-base flex-shrink-0 ml-2"></span>
                </div>
                {# Counts pushed right by justify-between on parent #}
                {% if sub_df is defined and not sub_df.empty %}
                    <div class="text-sm text-gray-600 text-right flex-shrink-0 whitespace-nowrap ml-2">
                        {# ml-2 for spacing #}
                        <span>{{ sub_df|length }} {{ 'Firm' if sub_df|length == 1 else 'Firms' }}</span><span
                            class="mx-1 text-gray-300">|</span><span>{{ sub_df.Filings.sum() }} {{ 'Filing' if sub_df.Filings.sum() == 1 else 'Filings' }}</span>
                    </div>
                {% endif %}
            </div>
            <div id="tort_content_{{ title_id }}" class="toggle-content">
                <div class="p-5 mt-4">
                    {% if sponsor %}
                        <div class="litigation-sponsor-web mb-4 p-4 border border-gray-200 rounded-lg bg-gray-50 flex flex-col md:flex-row md:items-center space-y-3 md:space-y-0 md:space-x-4">
                            <div class="w-full md:w-1/4 flex-shrink-0 flex justify-center md:justify-start items-center pr-0 md:pr-2">
                                {% if sponsor.logo_svg_link %}<img src="{{ sponsor.logo_svg_link }}" alt="Sponsor Logo"
                                                                   class="h-10 w-auto object-contain object-left"> {% endif %}
                            </div>
                            <div class="w-full md:w-1/2 flex-grow text-sm text-gray-700 text-center md:text-left"> {{ sponsor.ad_copy|safe }} </div>
                            <div class="w-full md:w-1/4 flex-shrink-0 flex justify-center md:justify-end items-center mt-2 md:mt-0">
                                {% if sponsor.cta_link %}<a href="{{ sponsor.cta_link }}" target="_blank"
                                                            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 whitespace-nowrap transition">
                                    <i class="fas fa-calendar-alt mr-2 text-white"></i><span class="text-white">Book Meeting</span>
                                </a> {% endif %} </div>
                        </div> {% endif %}
                    {% if allegations_and_causes %}
                        <div class="text-gray-700 text-sm mb-4 prose prose-sm max-w-none">{{ allegations_and_causes|convert_newlines|safe }}</div> {% endif %}
                    {% if sub_df is defined and not sub_df.empty %}
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200 w-full text-sm">
                                <thead class="bg-gray-100">
                                <tr>
                                    <th scope="col"
                                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Law Firm
                                    </th>
                                    <th scope="col"
                                        class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Filings
                                    </th>
                                </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200"> {% for _, row in sub_df.iterrows() %}
                                    {% set law_firm_id = row.law_firm|clean_id %}
                                    <tr class="table-row-hover border-t">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium"><a
                                                href='#detail_{{ law_firm_id }}?from=litigation_{{ title_id }}'
                                                class="text-blue-600 hover:underline">{{ row.law_firm }}</a></td>
                                        <td class='px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-right'>{{ row.Filings }}</td>
                                    </tr> {% endfor %}
                                <tr class="bg-gray-100 font-bold border-t">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-800">Total</td>
                                    <td class='px-6 py-4 whitespace-nowrap text-sm text-gray-800 text-right'>{{ sub_df.Filings.sum() }}</td>
                                </tr>
                                </tbody>
                            </table>
                        </div> {% else %} <p class="text-sm text-gray-500">No filing data available.</p> {% endif %}
                </div>
            </div>
        </div>
    {% else %} {# Email version #}
        <div style="background-color: #ffffff; border: 1px solid #e5e7eb; border-radius: 8px; overflow: hidden; margin-bottom: 16px;">
            <div style="background-color: #115197; padding: 12px 20px;"><a
                    href='{{ s3_prod }}/{{ iso_date }}/index.html#litigation_{{ title_id }}?gmasstrack=false'
                    target="_blank"
                    style="color: #ffffff !important; text-decoration: none !important; font-size: 16px; font-weight: 600; display: block; margin: 0;">{{ title }}</a>
            </div>
            <div style="padding: 20px;"> {% if sponsor %}
                <table class="litigation-sponsor-email" width="100%" cellpadding="0" cellspacing="0" border="0"
                       style="margin-bottom: 16px; border: 1px solid #e0e0e0; border-radius: 6px; background-color: #f9fafb; font-size: 0;">
                    <tr>
                        <td class="sponsor-text-cell" width="66%"
                            style="width: 66%; padding: 10px 5px 10px 15px; vertical-align: middle; font-size: 14px;"><p
                                style="margin: 0; font-size: 14px; color: #333333; line-height: 1.5; text-align: left;">{{ sponsor.ad_copy }}</p>
                        </td> {% if sponsor.cta_link %}
                        <td class="sponsor-button-cell" width="33%" align="center"
                            style="width: 33%; padding: 10px 15px 10px 5px; vertical-align: middle; text-align: center; font-size: 14px;">
                            <a href="{{ sponsor.cta_link }}" target="_blank"
                               style="display: inline-block; background-color: #2563eb; color: #ffffff !important; padding: 8px 16px; text-decoration: none !important; border-radius: 4px; font-weight: bold; font-size: 13px; white-space: nowrap; line-height: 1.4;">Book
                                Meeting</a></td> {% else %}
                        <td class="sponsor-button-cell" width="33%"
                            style="width: 33%; padding: 10px 15px 10px 5px; vertical-align: middle; font-size: 14px;"> 
                        </td> {% endif %} </tr>
                </table> {% endif %} {% if sub_df is defined and not sub_df.empty %}
                <table class="email-table" width="100%" cellpadding="0" cellspacing="0" border="0"
                       style="width: 100%; border-collapse: collapse; margin-bottom: 0;">
                    <thead>
                    <tr>
                        <th style="border: 1px solid #e5e7eb; padding: 8px 12px; text-align: left; font-size: 12px; background-color: #f9fafb; font-weight: 500; color: #6b7280; text-transform: uppercase;">
                            Law Firm
                        </th>
                        <th class="text-right"
                            style="border: 1px solid #e5e7eb; padding: 8px 12px; text-align: right; font-size: 12px; background-color: #f9fafb; font-weight: 500; color: #6b7280; text-transform: uppercase;">
                            Filings
                        </th>
                    </tr>
                    </thead>
                    <tbody> {% for _, row in sub_df.iterrows() %}{% set law_firm_id = row.law_firm|clean_id %}
                        <tr>
                            <td class="font-medium"
                                style="border: 1px solid #e5e7eb; padding: 8px 12px; font-size: 14px; line-height: 1.5; font-weight: 500;">
                                <a href='{{ s3_prod }}/{{ iso_date }}/index.html#detail_{{ law_firm_id }}?gmasstrack=false'
                                   target="_blank"
                                   style="color: #2563eb; text-decoration: underline;">{{ row.law_firm }}</a></td>
                            <td class='text-gray-500 text-right'
                                style="border: 1px solid #e5e7eb; padding: 8px 12px; font-size: 14px; line-height: 1.5; color: #6b7280; text-align: right;">{{ row.Filings }}</td>
                        </tr> {% endfor %}
                    <tr class="email-total-row">
                        <td style="border: 1px solid #e5e7eb; padding: 8px 12px; font-size: 14px; line-height: 1.5; background-color: #f3f4f6; font-weight: bold; color: #1f2937;">
                            Total
                        </td>
                        <td class='text-right'
                            style="border: 1px solid #e5e7eb; padding: 8px 12px; font-size: 14px; line-height: 1.5; background-color: #f3f4f6; font-weight: bold; color: #1f2937; text-align: right;">{{ sub_df.Filings.sum() }}</td>
                    </tr>
                    </tbody>
                </table> {% else %}
                <p class="text-sm text-gray-500" style="font-size: 14px; color: #6b7280; margin: 0;">No filing data
                    available.</p> {% endif %} </div>
        </div> {% endif %}
{% endmacro %}

{% macro render_detailed_filings(docket_df, is_web, s3_prod, iso_date) %}
    {# Reverted macro content to previous known-good state internally #}
    {% if docket_df is defined and not docket_df.empty %}
        {% if 'law_firm' in docket_df.columns and 'title' in docket_df.columns and 'versus' in docket_df.columns and 's3_link' in docket_df.columns and 'mdl_num' in docket_df.columns and 'num_plaintiffs' in docket_df.columns and 'transferred_in' in docket_df.columns and 'pending_cto' in docket_df.columns %}
            {% set docket_groups = docket_df.groupby('law_firm') %} {% if not docket_groups.groups %}
            <p class="text-sm text-gray-500" style="font-size: 14px; color: #6b7280;">No detailed filings data to
                display.</p> {% else %}{% if is_web %}
                <div class="space-y-6"> {% for law_firm, group in docket_groups %}
                    {% set law_firm_id = law_firm|clean_id %} {% set total_filings = group.shape[0] %}
                    {% set num_litigations = group['title'].nunique() %}
                    <div id='detail_{{ law_firm_id }}' class="firm-card animate-on-scroll expandable-section-hover">
                        {# Added expandable-section-hover #}
                        <div class="collapsible-header px-4 py-3 sm:px-6 cursor-pointer border-b border-gray-200"
                             onclick="toggleContent('firm_content_{{ law_firm_id }}', this)">
                            <div class="collapsible-header-group"><h3
                                    class="collapsible-header-title text-lg font-semibold text-gray-800"
                                    title="{{ law_firm }}">{{ law_firm }}</h3> <span
                                    id="firm_content_{{ law_firm_id }}_icon"
                                    class="fas fa-plus toggle-icon text-gray-500 text-base flex-shrink-0 ml-2"></span>
                            </div>
                            <div class="firm-stats text-sm text-gray-600 text-right flex-shrink-0 whitespace-nowrap ml-2">
                                <span>{{ num_litigations }} {{ 'Litigation' if num_litigations == 1 else 'Litigations' }}</span><span
                                    class="mx-1 text-gray-300">|</span><span>{{ total_filings }} {{ 'Filing' if total_filings == 1 else 'Filings' }}</span>
                            </div>
                        </div>
                        <div id="firm_content_{{ law_firm_id }}" class="toggle-content">
                            <div class="p-5 mt-4 firm-card-content">
                                {% for title, title_group in group.groupby('title') %}
                                    {% set title_id = title|clean_id %}
                                    <div class="mb-4 bg-blue-50 p-4 rounded-lg"><h4
                                            class="tort-subtitle text-md font-medium text-blue-800 mb-2"><a
                                            href='#litigation_{{ title_id }}' class="hover:underline">{{ title }}</a>
                                    </h4>
                                        <ul class="space-y-2"> {% for _, case in title_group.iterrows() %}
                                            {% set versus = case.versus %}
                                            {% set asterisks = '**' if case.transferred_in else '*' if case.pending_cto else '' %}
                                            {% set plaintiff_display = '' %}
                                            {% if case.mdl_num in ['2873', '3092'] and case.num_plaintiffs is defined and case.num_plaintiffs > 0 %}
                                                {% set plaintiff_display = ' (' ~ case.num_plaintiffs ~ (' plaintiffs)' if case.num_plaintiffs > 1 else ' plaintiff)') %}
                                            {% endif %}
                                            <li class="text-sm text-gray-700 case-link"><a href='{{ case.s3_link }}'
                                                                                           target='_blank'
                                                                                           class="text-blue-600 hover:underline">
                                                {{- versus -}}{{- plaintiff_display -}}</a>{% if asterisks %}
                                                <span class="font-bold text-blue-700 ml-1">{{ asterisks }}</span>{% endif %}
                                            </li> {% endfor %} </ul> {% if title_group.shape[0] > 0 %}
                                            <div class="mt-3 text-right"><span
                                                    class="text-xs font-medium text-gray-600">Cases Filed: <span
                                                    class="font-bold">{{ title_group.shape[0] }}</span></span>
                                            </div> {% endif %} </div> {% endfor %}
                                {% if group.transferred_in.any() or group.pending_cto.any() %}
                                    <div class="text-sm text-gray-500 mt-4"> {% if group.pending_cto.any() %}
                                        <p class="mb-1"><span class="font-bold">*</span> CTO Filed (Pending Transfer)
                                        </p> {% endif %} {% if group.transferred_in.any() %}
                                        <p><span class="font-bold">**</span> Case Transferred In (CTO Finalized)
                                        </p> {% endif %} </div> {% endif %} </div>
                        </div>
                    </div> {% endfor %} </div> {% else %} {# Email version #} {% for law_firm, group in docket_groups %}
                    {% set law_firm_id = law_firm|clean_id %}{% set total_filings = group.shape[0] %}
                    <div id='detail_{{ law_firm_id }}' class="mb-8" style="margin-bottom: 24px;"><h3
                            style="font-size: 18px; font-weight: 600; color: #1f2937; margin: 0 0 12px 0;">
                        <span>{{ law_firm }}</span><span style="font-weight: normal; color: #4b5563; margin-left: 6px;">({{ total_filings }} new {{ 'filing' if total_filings == 1 else 'filings' }})</span>
                    </h3> {% for title, title_group in group.groupby('title') %}{% set title_id = title|clean_id %}
                        <div class="mb-4"
                             style="background-color: #eff6ff; border-radius: 6px; padding: 16px; margin-bottom: 12px;">
                            <h4 class="font-medium mb-2"
                                style="font-weight: 500; color: #1e40af; margin: 0 0 8px 0; font-size: 15px;"><a
                                    href='{{ s3_prod }}/{{ iso_date }}/index.html#litigation_{{ title_id }}?gmasstrack=false'
                                    target="_blank" style="color: #1e40af; text-decoration: underline;">{{ title }}</a>
                            </h4>
                            <ul style="margin: 0; padding: 0; list-style: none;">
                                {% for _, case in title_group.iterrows() %} {% set versus = case.versus %}
                                    {% set asterisks = '**' if case.transferred_in else '*' if case.pending_cto else '' %}
                                    {% set plaintiff_display = '' %}
                                    {% if case.mdl_num in ['2873', '3092'] and case.num_plaintiffs is defined and case.num_plaintiffs > 0 %}
                                        {% set plaintiff_display = ' (' ~ case.num_plaintiffs ~ (' pl.)' if case.num_plaintiffs > 1 else ' pl.)') %}
                                    {% endif %}
                                    <li class="text-sm text-gray-700"
                                        style="font-size: 14px; color: #374151; margin-bottom: 6px;"><a
                                            href='{{ case.s3_link }}' target='_blank'
                                            style="color: #2563eb; text-decoration: underline;">
                                        {{- versus -}}{{- plaintiff_display -}}</a>{% if asterisks %}
                                        <span style="font-weight: bold; color: #1d4ed8; margin-left: 4px;">{{ asterisks }}</span>{% endif %}
                                    </li> {% endfor %} </ul> {% if title_group.shape[0] > 0 %}
                            <div class="mt-3 text-right" style="margin-top: 12px; text-align: right;"><span
                                    class="text-xs font-medium text-gray-600"
                                    style="font-size: 12px; font-weight: 500; color: #4b5563;">Cases Filed: <span
                                    style="font-weight: bold;">{{ title_group.shape[0] }}</span></span>
                            </div> {% endif %} </div> {% endfor %} </div> {% endfor %}
                {% if docket_df.transferred_in.any() or docket_df.pending_cto.any() %}
                    <div class="text-sm text-gray-500 mt-6" style="font-size: 14px; color: #6b7280; margin-top: 24px;">
                        {% if docket_df.pending_cto.any() %}
                            <p style="margin: 0 0 4px 0;"><span style="font-weight: bold;">*</span> CTO Filed (Pending
                                Transfer)</p> {% endif %} {% if docket_df.transferred_in.any() %}
                        <p style="margin: 0;"><span style="font-weight: bold;">**</span> Case Transferred In (CTO
                            Finalized)</p> {% endif %} </div> {% endif %} {% endif %} {% endif %} {% else %}
            <p class="text-sm text-red-600" style="font-size: 14px; color: #dc2626;">Error: Required columns missing for
                detailed filings.</p> {% endif %} {% else %}
        <p class="text-sm text-gray-500" style="font-size: 14px; color: #6b7280;">No detailed filings data
            available.</p> {% endif %}
{% endmacro %}

{% macro render_ad_report(grouped_ads, is_web, s3_prod, iso_date) %}
    {# Reverted macro content to previous known-good state internally #}
    {% if is_web %}
        <div class="space-y-6"> {% if grouped_ads and grouped_ads.groups %} {% set has_ads = False %}
            {% for name, group in grouped_ads %}
                {% set valid_ads = group[group.summary != 'Summary generation failed'] %}
                {% set num_ads = valid_ads|length %} {% if num_ads > 0 %}{% set has_ads = True %}
                <div><h3 class="text-lg font-semibold text-gray-800 mb-3">{{ name }} <span
                        class="text-blue-600">({{ num_ads }} {{ 'new ad' if num_ads == 1 else 'new ads' }})</span></h3>
                    <div class="bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm">
                        <div class="border-b border-gray-200 bg-gray-50 px-4 py-2 sm:px-6 sm:py-3 grid grid-cols-10 gap-4">
                            <span class="col-span-6 text-xs font-medium text-gray-500 uppercase tracking-wider">Summary</span>
                            <span class="col-span-2 text-xs font-medium text-gray-500 uppercase tracking-wider text-right">Start Date</span>
                            <span class="col-span-2 text-xs font-medium text-gray-500 uppercase tracking-wider text-right">End Date</span>
                        </div>
                        <div class="divide-y divide-gray-200"> {% for _, row in valid_ads.iterrows() %}
                            <div class="p-4 hover:bg-gray-50 transition-colors duration-150 grid grid-cols-10 gap-4 items-start">
                                <div class="col-span-6 text-sm"><a
                                        href='{{ s3_prod }}/{{ iso_date }}/ads/{{ row.ad_archive_id }}.html'
                                        target='_blank'
                                        class="text-blue-600 hover:underline font-medium">{{ row.summary }}</a></div>
                                <div class="col-span-2 text-sm text-gray-500 text-right whitespace-nowrap">{{ row.start_date }}</div>
                                <div class="col-span-2 text-sm text-gray-500 text-right whitespace-nowrap">{{ row.end_date }}</div>
                            </div> {% endfor %} </div>
                    </div>
                </div> {% endif %} {% endfor %} {% if not has_ads %}
                <p class="text-sm text-gray-500">No new ad data available.</p> {% endif %} {% else %}
            <p class="text-sm text-gray-500">No ad data available.</p> {% endif %} </div>
    {% else %} {# Email Version #}
        <div> {% if grouped_ads and grouped_ads.groups %} {% set has_ads = False %} {% for name, group in grouped_ads %}
            {% set valid_ads = group[group.summary != 'Summary generation failed'] %}
            {% set num_ads = valid_ads|length %} {% if num_ads > 0 %}{% set has_ads = True %}
                <div class="mb-6" style="margin-bottom: 24px;"><h3
                        style="font-size: 18px; font-weight: 600; color: #1f2937; margin: 0 0 12px 0;">{{ name }} <span
                        style="color: #2563eb;">({{ num_ads }} {{ 'new ad' if num_ads == 1 else 'new ads' }})</span>
                </h3>
                    <table class="email-table ad-table" width="100%" cellpadding="0" cellspacing="0" border="0"
                           style="width: 100%; border-collapse: collapse; margin-bottom: 15px; border: 1px solid #e5e7eb; border-radius: 6px; overflow: hidden;">
                        <thead>
                        <tr style="background-color: #f9fafb;">
                            <th style="width: 60%; border: 1px solid #e5e7eb; padding: 8px 12px; text-align: left; font-size: 12px; background-color: #f9fafb; font-weight: 500; color: #6b7280; text-transform: uppercase;">
                                Summary
                            </th>
                            <th style="width: 20%; border: 1px solid #e5e7eb; padding: 8px 12px; text-align: right; font-size: 12px; background-color: #f9fafb; font-weight: 500; color: #6b7280; text-transform: uppercase;">
                                Start
                            </th>
                            <th style="width: 20%; border: 1px solid #e5e7eb; padding: 8px 12px; text-align: right; font-size: 12px; background-color: #f9fafb; font-weight: 500; color: #6b7280; text-transform: uppercase;">
                                End
                            </th>
                        </tr>
                        </thead>
                        <tbody> {% for _, row in valid_ads.iterrows() %}
                            <tr style="background-color: {% if loop.index is even %}#ffffff{% else %}#f9fafb{% endif %};">
                                <td style="border: 1px solid #e5e7eb; padding: 12px; font-size: 14px; color: #374151;">
                                    <a href='{{ s3_prod }}/{{ iso_date }}/ads/{{ row.ad_archive_id }}.html'
                                       target='_blank'
                                       style="color: #2563eb; text-decoration: underline; font-weight: 500;">{{ row.summary }}</a>
                                </td>
                                <td style="border: 1px solid #e5e7eb; padding: 12px; font-size: 14px; color: #6b7280; text-align: right; white-space: nowrap;">{{ row.start_date }}</td>
                                <td style="border: 1px solid #e5e7eb; padding: 12px; font-size: 14px; color: #6b7280; text-align: right; white-space: nowrap;">{{ row.end_date }}</td>
                            </tr> {% endfor %} </tbody>
                    </table>
                </div> {% endif %} {% endfor %} {% if not has_ads %}
            <p class="text-sm text-gray-500" style="font-size: 14px; color: #6b7280;">No new ad data available.</p>
        {% endif %} {% else %}<p class="text-sm text-gray-500" style="font-size: 14px; color: #6b7280;">No ad data
            available.</p> {% endif %} </div> {% endif %}
{% endmacro %}
{# ******** END: MACROS ******** #}

{# Web Navigation (Unchanged) #}
{% if is_web %}
    <nav id="top-nav" class="gradient-header shadow-md sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-3">
                <div class="flex items-center"><img
                        src="https://cdn.lexgenius.ai/assets/images/icons/lexgenius_logo.svg" alt="LexGenius Logo"
                        class="h-9 sm:h-10 mr-3"></div>
                <div class="hidden md:flex space-x-6 lg:space-x-8"><a href="#summary"
                                                                      class="nav-link text-white font-medium hover:text-white text-sm sm:text-base">Summary</a>
                    {% if show_announcements is defined and show_announcements and announcements %}
                        <a href="#announcements"
                           class="nav-link text-white font-medium hover:text-white text-sm sm:text-base">Announcements</a> {% endif %}
                    {% if show_afff_stats is defined and show_afff_stats and afff_stats %}<a href="#afff-numbers"
                                                                                             class="nav-link text-white font-medium hover:text-white text-sm sm:text-base">AFFF
                        Stats</a> {% endif %}
                    {% if show_upcoming_hearings is defined and show_upcoming_hearings and upcoming_hearings %}
                        <a href="#upcoming-hearings"
                           class="nav-link text-white font-medium hover:text-white text-sm sm:text-base">Hearings</a> {% endif %}
                    {% if show_calendly is defined and show_calendly %}<a href="#book-meeting"
                                                                          class="nav-link text-white font-medium hover:text-white text-sm sm:text-base">Demo</a> {% endif %}
                    {% if show_special_reports is defined and show_special_reports and special_reports %}
                        <a href="#special-reports"
                           class="nav-link text-white font-medium hover:text-white text-sm sm:text-base">Reports</a> {% endif %}
                    {% if show_news is defined and show_news and news_items %}
                        <a href="#news" class="nav-link text-white font-medium hover:text-white text-sm sm:text-base">News</a> {% endif %}
                    <a href="#litigation-report"
                       class="nav-link text-white font-medium hover:text-white text-sm sm:text-base">Litigation</a> <a
                            href="#detailed-filings"
                            class="nav-link text-white font-medium hover:text-white text-sm sm:text-base">Filings</a> <a
                            href="#adspy-report"
                            class="nav-link text-white font-medium hover:text-white text-sm sm:text-base">AdSpy</a>
                </div>
                <div class="md:hidden">
                    <button id="mobile-menu-button" class="text-white focus:outline-none p-2">
                        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M4 6h16M4 12h16M4 18h16"/>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
        <div id="mobile-menu" class="hidden md:hidden bg-white shadow-lg absolute w-full">
            <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3"><a href="#summary"
                                                             class="mobile-nav-link block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:bg-gray-100">Summary</a>
                {% if show_announcements is defined and show_announcements and announcements %}<a href="#announcements"
                                                                                                  class="mobile-nav-link block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:bg-gray-100">Announcements</a> {% endif %}
                {% if show_afff_stats is defined and show_afff_stats and afff_stats %}<a href="#afff-numbers"
                                                                                         class="mobile-nav-link block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:bg-gray-100">AFFF
                    Stats</a> {% endif %}
                {% if show_upcoming_hearings is defined and show_upcoming_hearings and upcoming_hearings %}
                    <a href="#upcoming-hearings"
                       class="mobile-nav-link block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:bg-gray-100">Hearings</a> {% endif %}
                {% if show_calendly is defined and show_calendly %}<a href="#book-meeting"
                                                                      class="mobile-nav-link block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:bg-gray-100">Demo</a> {% endif %}
                {% if show_special_reports is defined and show_special_reports and special_reports %}
                    <a href="#special-reports"
                       class="mobile-nav-link block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:bg-gray-100">Reports</a> {% endif %}
                {% if show_news is defined and show_news and news_items %}<a href="#news"
                                                                             class="mobile-nav-link block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:bg-gray-100">News</a> {% endif %}
                <a href="#litigation-report"
                   class="mobile-nav-link block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:bg-gray-100">Litigation</a>
                <a href="#detailed-filings"
                   class="mobile-nav-link block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:bg-gray-100">Filings</a>
                <a href="#adspy-report"
                   class="mobile-nav-link block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:bg-gray-100">AdSpy</a>
            </div>
        </div>
    </nav> {% endif %}

{# ******** START: Main Content Area Wrapper (Conditional) ******** #}
{% if is_web %}
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">

        {# Web Header (Unchanged) #}
        <header class="gradient-header rounded-xl shadow-lg mb-8 p-6 text-center">
            {#        <img src="https://cdn.lexgenius.ai/assets/images/icons/lexgenius_logo.svg"#}
            {#             alt="LexGenius Logo"#}
            {#             class="h-14 sm:h-16 mx-auto mb-4"> {# Increased from h-9/h-10 to h-14/h-16 #}
            <h1 class="text-3xl md:text-4xl font-bold text-white mb-2">Mass Tort Report</h1>
            <p class="text-lg sm:text-xl text-white opacity-90">{{ report_date if report_date is defined else 'Report Date' }}</p>
        </header>

        {# Header Sponsorship (Unchanged) #}
        {% if sponsorships and sponsorships.header_sponsorship_data and sponsorships.header_sponsorship_data.display %}
            {% set sponsor_data = sponsorships.header_sponsorship_data %}
            <div class="header-sponsorship-web bg-white rounded-lg shadow-md border border-gray-200 p-4 md:p-6 mb-8"><p
                    class="text-center text-sm text-gray-600 font-medium mb-4"><strong>LexGenius</strong> is sponsored
                by <b>Weitz & Luxenberg PC</b></p>
                <div class="flex flex-col md:flex-row md:items-center md:space-x-4 space-y-3 md:space-y-0">
                    <div class="w-full md:w-1/4 flex-shrink-0 flex justify-center md:justify-start items-center pr-0 md:pr-2">
                        {% if sponsor_data.logo_svg_link %}
                            <img src="{{ sponsor_data.logo_svg_link }}" alt="Sponsor Logo"
                                 class="h-12 w-auto object-contain object-left"> {% endif %} </div>
                    <div class="w-full md:w-1/2 flex-grow text-sm text-gray-700 text-center md:text-left"> {{ sponsor_data.web_ad_copy|safe if sponsor_data.web_ad_copy else '' }} </div>
                    <div class="w-full md:w-1/4 flex-shrink-0 flex justify-center md:justify-end items-center mt-2 md:mt-0">
                        {% if sponsor_data.cta_link %}<a href="{{ sponsor_data.cta_link }}" target="_blank"
                                                         class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 whitespace-nowrap transition">
                            <i class="fas fa-calendar-alt mr-2 text-white"></i><span
                                class="text-white">Book Meeting</span> </a> {% endif %} </div>
                </div>
            </div> {% endif %}

        {# --- Summary Section (Unchanged) --- #}
        <section id="summary">
            <div class="bg-white rounded-xl shadow-md overflow-hidden mb-12">
                <div class="p-6"><h2 class="text-2xl font-bold text-gray-800 mb-6 flex items-center"><i
                        class="fas fa-chart-line text-blue-600 mr-3"></i> Report Summary </h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8 summary-stats-grid"><a
                            href="#litigation-report" class="stat-card stat-card-primary p-5">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-blue-50 text-blue-600 mr-4"><i
                                    class="fas fa-gavel text-xl"></i></div>
                            <div><p class="text-sm text-gray-500">New Tort Claims Filed*</p>
                                <p class="text-2xl font-bold text-gray-800">{{ num_torts if num_torts is defined else 'N/A' }}</p>
                            </div>
                        </div>
                    </a> <a href="#adspy-report" class="stat-card stat-card-secondary p-5">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-sky-50 text-sky-600 mr-4"><i class="fas fa-ad text-xl"></i>
                            </div>
                            <div><p class="text-sm text-gray-500">New Legal Ads (14 days)</p>
                                <p class="text-2xl font-bold text-gray-800">{{ num_ads if num_ads is defined else 'N/A' }}</p>
                            </div>
                        </div>
                    </a></div>
                    <div class="highlight-box p-5 rounded-lg mb-6"><p class="text-gray-700 mb-3">
                        {% if is_weekly_report %}For the week
                            {{ summary_date if summary_date is defined else 'this week' }}{% else %}On
                            {{ summary_date if summary_date is defined else 'this date' }}{% endif %}, there were <span
                            class="font-bold text-blue-600">{{ num_torts if num_torts is defined else 'N/A' }}</span>
                        new claims filed* and <span
                            class="font-bold text-blue-600">{{ num_ads if num_ads is defined else 'N/A' }}</span> new
                        legal ads detected. </p>
                        <p class="text-gray-700 text-sm mb-2">* Claims count reflects total plaintiffs for AFFF &
                            Suboxone filings, individual filings otherwise.</p>
                        <p class="text-gray-700">Prefer a weekly summary? Reply to this email.</p></div>
                    <div class="bg-gray-50 rounded-lg p-4 h-96 md:h-[500px] flex items-center justify-center mt-8">
                        <canvas id="tort-chart"></canvas>
                        <p id="chart-fallback" class="text-center text-gray-500 hidden">Chart data is loading or
                            unavailable.</p></div>
                </div>
            </div>
        </section>

        {# --- Announcements Section (Unchanged HTML Structure) --- #}
        {% if show_announcements is defined and show_announcements and announcements %}
            <section id="announcements" class="mb-12">
                <div class="expandable-section-hover"> {# Uses expandable-section-hover class #}
                    <div class="collapsible-header px-4 py-3 sm:px-5 bg-gray-100 text-gray-800 cursor-pointer border-b border-gray-200"
                         onclick="toggleContent('announcements_content', this)">
                        <div class="collapsible-header-group">
                            <i class="fas fa-bullhorn text-blue-600 mr-3"></i>
                            <h2 class="collapsible-header-title text-xl font-bold"> Announcements </h2>
                            <span id="announcements_content_icon"
                                  class="fas fa-plus toggle-icon text-gray-500 text-base flex-shrink-0 ml-2"></span>
                        </div>
                    </div>
                    <div id="announcements_content" class="toggle-content">
                        <div class="p-6">
                            <div class="space-y-4"> {% for announcement in announcements %}
                                <div class="border border-blue-100 bg-blue-50 rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow duration-200">
                                    <h3 class="text-lg font-semibold text-blue-800 mb-2">{{ announcement.title }}</h3>
                                    <div class="text-gray-700 prose prose-sm max-w-none">{{ announcement.content | safe }}</div>
                                </div> {% else %}
                                <p class="text-sm text-gray-500 italic">No current announcements.</p> {% endfor %}</div>
                        </div>
                    </div>
                </div>
            </section>
        {% endif %}

        {# --- Upcoming Hearings Section (MOVED HERE & MODIFIED FOR DEFAULT EXPANSION) --- #}
        {% if show_upcoming_hearings is defined and show_upcoming_hearings and upcoming_hearings %}
            <section id="upcoming-hearings" class="mb-12">
                <div class="expandable-section-hover"> {# Uses expandable-section-hover class #}
                    <div class="collapsible-header px-4 py-3 sm:px-5 bg-gray-100 text-gray-800 cursor-pointer border-b border-gray-200"
                         onclick="toggleContent('upcoming_hearings_content', this)">
                        <div class="collapsible-header-group">
                            <i class="fas fa-calendar-alt text-blue-600 mr-3"></i>
                            <h2 class="collapsible-header-title text-xl font-bold"> Upcoming Hearings </h2>
                            {# MODIFIED icon class from fa-plus to fa-minus #}
                            <span id="upcoming_hearings_content_icon"
                                  class="fas fa-minus toggle-icon text-gray-500 text-base flex-shrink-0 ml-2"></span>
                        </div>
                    </div>
                    {# MODIFIED content div class to add 'active' #}
                    <div id="upcoming_hearings_content" class="toggle-content active">
                        <div class="p-6">
                            <div class="space-y-4">
                                {% for hearing in upcoming_hearings %}
                                    <div class="flex items-start p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-150">
                                        <div class="bg-blue-100 text-blue-800 rounded-lg p-3 mr-4 text-center min-w-[70px]">
                                            <div class="text-sm font-bold uppercase">{{ hearing.date_month_short }}</div>
                                            <div class="text-xl font-bold">{{ hearing.date_day }}</div>
                                            <div class="text-xs">{{ hearing.date_year }}</div>
                                        </div>
                                        <div>
                                            <h3 class="font-medium text-gray-800 mb-1">
                                                {% if hearing.link %}
                                                    <a href="{{ hearing.link }}" target="_blank"
                                                       title="{{ hearing.formatted_date }}"
                                                       class="text-blue-600 hover:underline">{{ hearing.title }}</a>
                                                {% else %}
                                                    <span title="{{ hearing.formatted_date }}">{{ hearing.title }}</span>
                                                {% endif %}
                                            </h3>
                                            {% if hearing.details %}
                                                <p class="text-sm text-gray-600">{{ hearing.details }}</p>
                                            {% endif %}
                                        </div>
                                    </div>
                                {% else %}
                                    <p class="text-sm text-gray-500">No upcoming hearings listed.</p>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        {% endif %}

        {# --- AFFF Stats Section (NOW AFTER HEARINGS) --- #}
        {% if show_afff_stats is defined and show_afff_stats %}
            <section id="afff-numbers" class="mb-12">
                {# ... Keep the existing AFFF Stats section code here ... #}
                {# It remains collapsed by default (no 'active' class, 'fa-plus' icon) #}
                <div class="expandable-section-hover">
                    <div class="collapsible-header px-4 py-3 sm:px-5 bg-gray-100 text-gray-800 cursor-pointer border-b border-gray-200"
                         onclick="toggleContent('afff_numbers_content', this)">
                        <div class="collapsible-header-group">
                            <i class="fas fa-calculator text-red-600 mr-3"></i>
                            <h2 class="collapsible-header-title text-xl font-bold"> AFFF By The Numbers* </h2>
                            <span id="afff_numbers_content_icon"
                                  class="fas fa-plus toggle-icon text-gray-500 text-base flex-shrink-0 ml-2"></span>
                        </div>
                    </div>
                    <div id="afff_numbers_content" class="toggle-content">
                        <div class="p-6">
                            <div class="p-6">
                                {# --- START: ORIGINAL AFFF CONTENT TO BE RE-INSERTED --- #}
                                <div class="overflow-x-auto">
                                    <table class="min-w-full divide-y divide-gray-200 email-table">
                                        <thead class="bg-gray-100">
                                        <tr>
                                            <th scope="col"
                                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Metric
                                            </th>
                                            <th scope="col"
                                                class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Value
                                            </th>
                                        </tr>
                                        </thead>
                                        <tbody class="bg-white divide-y divide-gray-200">
                                        <tr class="table-row-hover">
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">PACER filings
                                                this
                                                period (pending CTO)
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-blue-600 text-right"> {{ afff_stats.pacer_cases_period }} {{ 'case' if afff_stats.pacer_cases_period == 1 else 'cases' }}
                                                {% if afff_stats.pacer_plaintiffs_period is defined and afff_stats.pacer_plaintiffs_period > 0 %}
                                                    <span class="font-semibold text-gray-700"> ({{ afff_stats.pacer_plaintiffs_period }} {{ 'plaintiff' if afff_stats.pacer_plaintiffs_period == 1 else 'plaintiffs' }})</span> {% endif %}
                                            </td>
                                        </tr>
                                        <tr class="table-row-hover">
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">Cases
                                                transferred
                                                into MDL this period
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-blue-600 text-right"> {{ afff_stats.transferred_in_period }} {{ 'case' if afff_stats.transferred_in_period == 1 else 'cases' }}
                                                {% if afff_stats.transferred_in_plaintiffs_period is defined and afff_stats.transferred_in_plaintiffs_period > 0 %}
                                                    <span class="font-semibold text-gray-700"> ({{ afff_stats.transferred_in_plaintiffs_period }} {{ 'plaintiff' if afff_stats.transferred_in_plaintiffs_period == 1 else 'plaintiffs' }})</span> {% endif %}
                                            </td>
                                        </tr>
                                        <tr class="table-row-hover">
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">Direct filings
                                                this period (est.)
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-blue-600 text-right">{{ afff_stats.direct_filings_placeholder }}
                                                filings
                                            </td>
                                        </tr>
                                        <tr class="table-row-hover">
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">Direct filings
                                                -
                                                past 30 days (est.)
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-blue-600 text-right">{{ afff_stats.direct_filings_30_days_placeholder }}
                                                filings
                                            </td>
                                        </tr>
                                        <tr class="table-row-hover">
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">Total Filings
                                                YTD
                                                (est.)
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-blue-600 text-right">{{ afff_stats.filings_ytd_placeholder }}
                                                filings
                                            </td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </div>
                                <p class="text-sm text-gray-500 italic mt-4">Note: There is a 3-week delay between
                                    filing
                                    and appearance in the transferee court PACER docket, which is why individual filings
                                    do
                                    not appear on the daily report.</p>
                                {# --- END: ORIGINAL AFFF CONTENT TO BE RE-INSERTED --- #}
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        {% endif %}


        {# --- Book Meeting/Calendly Section (Unchanged HTML Structure) --- #}
        {% if show_calendly is defined and show_calendly %}
            <section id="book-meeting" class="mb-12">
                <div class="expandable-section-hover"> {# Uses expandable-section-hover class #}
                    <div class="collapsible-header px-4 py-3 sm:px-5 bg-gray-100 text-gray-800 cursor-pointer border-b border-gray-200"
                         onclick="toggleContent('book_meeting_content', this)">
                        <div class="collapsible-header-group">
                            <i class="fas fa-calendar-check text-green-600 mr-3"></i>
                            <h2 class="collapsible-header-title text-xl font-bold"> Book a Demo </h2>
                            <span id="book_meeting_content_icon"
                                  class="fas fa-plus toggle-icon text-gray-500 text-base flex-shrink-0 ml-2"></span>
                        </div>
                    </div>
                    <div id="book_meeting_content" class="toggle-content">
                        <div class="bg-gradient-to-r from-blue-50 to-sky-100 p-6">
                            <div class="flex flex-col md:flex-row items-center justify-between gap-4">
                                <div class="text-center md:text-left"><h3 class="text-xl font-bold text-gray-800 mb-2">
                                    Want Deeper Insights?</h3>
                                    <p class="text-gray-700">Schedule a quick call...</p></div>
                                <a href="{{ calendly_link }}" target="_blank"
                                   class="flex-shrink-0 bg-green-600 hover:bg-green-700 text-white font-medium py-3 px-6 rounded-lg shadow-md transition duration-300 ease-in-out transform hover:-translate-y-1 whitespace-nowrap">
                                    <i class="fas fa-calendar-check mr-2"></i> Book a Demo </a></div>
                        </div>
                    </div>
                </div>
            </section>
        {% endif %}

        {# --- Special Reports Section (Unchanged HTML Structure) --- #}
        {% if show_special_reports is defined and show_special_reports and special_reports %}
            <section id="special-reports" class="mb-12">
                <div class="expandable-section-hover"> {# Uses expandable-section-hover class #}
                    <div class="collapsible-header px-4 py-3 sm:px-5 bg-gray-100 text-gray-800 cursor-pointer border-b border-gray-200"
                         onclick="toggleContent('special_reports_content', this)">
                        <div class="collapsible-header-group">
                            <i class="fas fa-file-invoice text-blue-600 mr-3"></i>
                            <h2 class="collapsible-header-title text-xl font-bold"> Special Reports </h2>
                            <span id="special_reports_content_icon"
                                  class="fas fa-plus toggle-icon text-gray-500 text-base flex-shrink-0 ml-2"></span>
                        </div>
                    </div>
                    <div id="special_reports_content" class="toggle-content">
                        <div class="p-6">
                            <div class="space-y-3"> {% for report in special_reports %}
                                <a href="{{ report.link }}" target="_blank" class="list-item-link"> <span
                                        class="list-item-icon"><i class="fas fa-arrow-right"></i></span> <span
                                        class="list-item-text">{{ report.title }}</span> </a> {% else %}
                                <p class="text-sm text-gray-500">No special reports available
                                    currently.</p> {% endfor %} </div>
                        </div>
                    </div>
                </div>
            </section>
        {% endif %}

        {% if show_news is defined and show_news %} {# <<< THIS ENTIRE WRAPPER WAS MISSING OR INCORRECT #}
            <section id="news" class="mb-12">
                <div class="expandable-section-hover"> {# Uses expandable-section-hover class #}
                    <div class="collapsible-header px-4 py-3 sm:px-5 bg-gray-100 text-gray-800 cursor-pointer border-b border-gray-200"
                         onclick="toggleContent('news_content', this)">
                        <div class="collapsible-header-group">
                            <i class="fas fa-newspaper text-blue-600 mr-3"></i>
                            <h2 class="collapsible-header-title text-xl font-bold"> Recent JPML Panel & CMOs </h2>
                            <span id="news_content_icon"
                                  class="fas fa-plus toggle-icon text-gray-500 text-base flex-shrink-0 ml-2"></span>
                        </div>
                    </div>
                    <div id="news_content" class="toggle-content">
                        <div class="p-6">
                            {% if news_items %}
                                {# ... Keep the existing table code for news items here ... #}
                                <div class="overflow-x-auto">
                                    <table class="min-w-full divide-y divide-gray-200">
                                        <thead class="bg-gray-50">
                                        <tr>
                                            <th scope="col"
                                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Date
                                            </th>
                                            <th scope="col"
                                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Title
                                            </th>
                                        </tr>
                                        </thead>
                                        <tbody class="bg-white divide-y divide-gray-200">
                                        {% for item in news_items %}
                                            <tr class="table-row-hover">
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ item.date }}</td>
                                                <td class="px-6 py-4 text-sm font-medium text-gray-900">
                                                    <a href="{{ item.link }}" target="_blank"
                                                       class="text-blue-600 hover:underline">{{ item.title }}</a>
                                                </td>
                                            </tr>
                                        {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            {% else %}
                                <p class="text-sm text-gray-500">No Recent JPML Panel & CMOs items.</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </section>
        {% endif %} {# <<< END of the show_news wrapper #}

        {# --- Litigation by Tort Report Section (Unchanged HTML Structure) --- #}
        <section id="litigation-report" class="mb-12"><h2
                class="text-2xl font-bold text-gray-800 mb-6 flex items-center"><i
                class="fas fa-balance-scale text-blue-600 mr-3"></i> Litigation by Tort Report </h2>
            <div class="mb-6 text-center"> {{ sponsorships.sponsorship_1.html|safe if sponsorships and sponsorships.sponsorship_1 and sponsorships.sponsorship_1.display else '' }} </div>
            <div class="space-y-6"> {% if grouped is defined and grouped %}
                {% set CAT_MDL = 'Multidistrict Litigation' %} {% set CAT_DATA_BREACH = 'Data Breach' %}
                {% set CAT_PRIVACY = 'Privacy Violation' %} {% set CAT_CLASS_ACTION = 'Class Action' %}
                {% set CAT_OTHER = 'Other Litigation' %}
                {% set categorized_torts = { CAT_MDL: [], CAT_DATA_BREACH: [], CAT_PRIVACY: [], CAT_CLASS_ACTION: [], CAT_OTHER: [] } %}
                {% for title, sub_df in grouped.items() %} {% if sub_df is defined and not sub_df.empty %}
                    {% set first_row_mdl_num = sub_df.mdl_num.iloc[0] if 'mdl_num' in sub_df.columns and not sub_df.mdl_num.empty and not sub_df.mdl_num.iloc[0] in ['NA', None, ''] else '' %}
                    {% set lower_title = title|lower %} {% set category = CAT_OTHER %}
                    {% if 'data breach' in lower_title %} {% set category = CAT_DATA_BREACH %}
                    {% elif 'privacy' in lower_title %} {% set category = CAT_PRIVACY %}
                    {% elif 'class action' in lower_title %} {% set category = CAT_CLASS_ACTION %}
                    {% elif first_row_mdl_num %} {% set mdl_num_for_check = first_row_mdl_num|string|trim %}
                        {% if ((mdl_num_for_check|length >= 4 or mdl_num_for_check == '25') and mdl_num_for_check != '9000') %}
                            {% set category = CAT_MDL %} {% endif %} {% endif %}
                    {% set item_filings = sub_df.Filings.sum() if 'Filings' in sub_df else 0 %}
                    {% set allegations = get_allegations_and_causes(docket_df_global, title, first_row_mdl_num) if docket_df_global is defined and get_allegations_and_causes is defined else None %}
                    {% set _ = categorized_torts[category].append((item_filings, title, sub_df, allegations)) %}
                {% endif %} {% endfor %}{% if categorized_torts[CAT_MDL] %}
                <div><h3
                        class="text-xl font-semibold text-gray-700 pb-2 border-b border-gray-300 mb-4">{{ CAT_MDL }}</h3>
                    <div class="space-y-4"> {% set sorted_mdl_items = categorized_torts[CAT_MDL] | sort(reverse=True, attribute=0) %}
                        {% for _, title, sub_df, allegations in sorted_mdl_items %}
                            {{ render_litigation_table(title, allegations, sub_df, is_web, s3_prod if s3_prod is defined else '', iso_date if iso_date is defined else '') }} {% endfor %} </div>
                </div> {% endif %} {% set other_categories_data = [] %}
                {% for cat_name, cat_items_list in categorized_torts.items() %}
                    {% if cat_name != CAT_MDL and cat_items_list %}
                        {% set category_total = cat_items_list | sum(attribute=0) %}
                        {% set _ = other_categories_data.append({'total': category_total, 'name': cat_name, 'item_list': cat_items_list}) %}
                    {% endif %} {% endfor %}
                {% set sorted_other_categories = other_categories_data | sort(reverse=True, attribute='total') %}
                {% for category_data in sorted_other_categories %}
                    <div><h3
                            class="text-xl font-semibold text-gray-700 pb-2 border-b border-gray-300 mb-4">{{ category_data.name }}</h3>
                        <div class="space-y-4"> {% set sorted_cat_items = category_data.item_list | sort(reverse=True, attribute=0) %}
                            {% for _, title, sub_df, allegations in sorted_cat_items %}
                                {{ render_litigation_table(title, allegations, sub_df, is_web, s3_prod if s3_prod is defined else '', iso_date if iso_date is defined else '') }} {% endfor %} </div>
                    </div> {% endfor %} {% else %}
                <p class="text-sm text-gray-500">No litigation data available.</p> {% endif %} </div>
        </section>

        {# --- Detailed Filings Report Section (Unchanged HTML Structure) --- #}
        <section id="detailed-filings" class="mb-12"><h2
                class="text-2xl font-bold text-gray-800 mb-6 flex items-center"><i
                class="fas fa-file-alt text-blue-600 mr-3"></i> Detailed Filings Report </h2>
            <div class="mb-6 text-center"> {{ sponsorships.sponsorship_2.html|safe if sponsorships and sponsorships.sponsorship_2 and sponsorships.sponsorship_2.display else '' }} </div>
            {{ render_detailed_filings(docket_df if docket_df is defined else None, is_web, s3_prod if s3_prod is defined else '', iso_date if iso_date is defined else '') }}
        </section>

        {# --- AdSpy Report Section (Unchanged HTML Structure) --- #}
        <section id="adspy-report" class="mb-12">
            <div class="bg-white rounded-xl shadow-md overflow-hidden">
                <div class="p-6"><h2 class="text-2xl font-bold text-gray-800 mb-6 flex items-center"><i
                        class="fab fa-facebook-square text-blue-600 mr-3"></i> AdSpy Report </h2>
                    <div class="mb-6 text-center"> {{ sponsorships.sponsorship_3.html|safe if sponsorships and sponsorships.sponsorship_3 and sponsorships.sponsorship_3.display else '' }} </div>
                    {{ render_ad_report(grouped_ads if grouped_ads is defined else None, is_web, s3_prod if s3_prod is defined else '', iso_date if iso_date is defined else '') }}
                </div>
            </div>
        </section>

        {# --- Footer (Collapsible, Unchanged HTML Structure) --- #}
        <section id="footer" class="mt-12">
            <div class="expandable-section-hover bg-gray-800 rounded-xl shadow-md overflow-hidden">
                {# Uses expandable-section-hover class #}
                <div class="collapsible-header px-4 py-3 sm:px-5 text-gray-400 cursor-pointer"
                     onclick="toggleContent('footer_content', this)">
                    <div class="collapsible-header-group">
                        <i class="fas fa-info-circle mr-3"></i>
                        <h2 class="collapsible-header-title text-lg font-semibold"> Footer Information </h2>
                        <span id="footer_content_icon"
                              class="fas fa-plus toggle-icon text-gray-400 text-base flex-shrink-0 ml-2"></span>
                    </div>
                </div>
                <div id="footer_content" class="toggle-content">
                    <div class="p-6 text-center text-gray-400">
                        <div class="mb-4">
                            <img src="https://cdn.lexgenius.ai/assets/images/icons/lexgenius_logo.svg"
                                 alt="LexGenius Logo"
                                 class="h-14 mx-auto"> {# Increased from h-10 to h-14 #}
                        </div>
                        <p class="text-sm mb-2">LexGenius | 20 Eastgate Drive, Unit D, Boynton Beach, FL 33436</p>
                        <p class="text-sm mb-4">
                            © {{ report_date.split(',')[-1].strip() if report_date is defined else '2024' }} LexGenius.
                            All Rights Reserved.</p>
                        <a href="https://www.gmass.co/gmass/u?u=OUTBOUND" target="_blank"
                           class="text-blue-400 hover:text-blue-300 hover:underline text-sm">Unsubscribe</a>
                    </div>
                </div>
            </div>
        </section>

    </div>
    {# End max-w-7xl container #}

    {# Scripts (JS hover reset logic updated) #}
    {% if is_web %}
    <button id="back-to-top" title="Back to Top">↑</button>
    <script>
        // Chart.js implementation (Keep this part as it is)
        const ctx = document.getElementById('tort-chart').getContext('2d');
        const createGradient = (ctx, chartArea) => { /* ... gradient code ... */ return gradient; };
        const sortedData = {{ chart_data|tojson }}.map((value, index) => ({ value, label: {{ chart_labels|tojson }}[index] })).sort((a, b) => b.value - a.value);
        const dataToShow = isMobile() ? sortedData.slice(0, 5) : sortedData.slice(0, 10);
        const sortedLabels = dataToShow.map(item => item.label);
        const sortedValues = dataToShow.map(item => item.value);
        function isMobile() { /* ... isMobile check ... */ }
        function getFontSize() { /* ... getFontSize check ... */ }
        new Chart(ctx, { /* ... Chart configuration ... */ });
        // --- End of Chart.js code ---

        // --- Start of Interactivity and Scrolling Logic ---
        document.addEventListener("DOMContentLoaded", function () {
            console.log("DOM fully loaded and parsed");

            // ----- Scroll Offset Calculation -----
            const HEADER_HEIGHT = 100; // Adjust this based on your sticky header's actual height
            const EXTRA_OFFSET = 30;   // Small additional offset for breathing room below header

            function getScrollOffset() {
                // const viewportHeight = window.innerHeight; // Not strictly needed for this calculation
                const baseOffset = HEADER_HEIGHT + EXTRA_OFFSET;
                // Optional: Add more complex logic based on viewport if needed
                // const additionalOffset = Math.max(0, (768 - viewportHeight) / 10);
                // return baseOffset + additionalOffset;
                return baseOffset;
            }

            // ----- Smooth Scroll Function -----
            function smoothScrollToElement(targetElement) {
                if (!targetElement) {
                    console.error("smoothScrollToElement: Target element not provided.");
                    return;
                }
                 console.log("Smooth scrolling to:", targetElement.id || targetElement);
                const offset = getScrollOffset();
                const elementPosition = targetElement.getBoundingClientRect().top + window.pageYOffset;
                const scrollPosition = elementPosition - offset;

                 window.scrollTo({
                    top: scrollPosition,
                    behavior: 'smooth'
                });

                // Highlight the scrolled-to element temporarily
                let highlightTarget = targetElement;
                // If it's a table, highlight its first title row for better visual cue
                 if (targetElement.tagName === 'TABLE') {
                     const titleRow = targetElement.querySelector('.title-row');
                     if (titleRow) highlightTarget = titleRow;
                 } else if (targetElement.tagName === 'TR' && !highlightTarget.classList.contains('title-row')) {
                    // Make sure we're not highlighting an already styled title row again
                    // If it's a regular TR, highlight it directly
                 }

                highlightTarget.style.transition = 'background-color 0.5s ease-out';
                highlightTarget.style.backgroundColor = 'rgba(173, 216, 230, 0.6)'; // Light blue, semi-transparent

                setTimeout(() => {
                    highlightTarget.style.backgroundColor = '';
                    // Optional: remove transition after it's done if needed elsewhere
                    // highlightTarget.style.transition = '';
                }, 1500); // Highlight for 1.5 seconds
            }

            // ----- Event Listener for Internal Links -----
            function handleLinkClick(event) {
                const link = event.currentTarget;
                const targetId = link.getAttribute('href');

                // Check if it's a valid internal anchor link
                if (!targetId || !targetId.startsWith('#')) {
                    console.log("Link is not an internal anchor:", targetId);
                    // Allow default behavior for external links or non-anchor links
                    return;
                }

                // Prevent default browser jump for internal anchors
                event.preventDefault();

                const actualId = targetId.substring(1);
                console.log("Internal link clicked. Target ID:", actualId);
                const targetElement = document.getElementById(actualId);

                if (targetElement) {
                    console.log("Target element found:", targetElement);
                    smoothScrollToElement(targetElement);
                } else {
                    console.error("Target element not found for ID:", actualId);
                }
            }

            // Attach the click listener to ALL links starting with #
            document.querySelectorAll('a[href^="#"]').forEach(link => {
                // Add a check to make sure we don't attach to the back-to-top button if it was an <a>
                if (link.id !== 'back-to-top') {
                     link.addEventListener('click', handleLinkClick);
                }
            });
            console.log("Internal link click listeners attached.");


            // ----- Allegation Toggling -----
            document.querySelectorAll('.title-row[data-target]').forEach(row => {
                 row.addEventListener('click', function (event) {
                     // Prevent clicks on links inside the title row from toggling
                     if (event.target.tagName === 'A') {
                         return;
                     }
                     const id = this.getAttribute('data-target');
                     toggleVisibility(id);
                 });
             });

            function toggleVisibility(id) {
                const element = document.getElementById(id);
                const indicator = document.getElementById(id + '_indicator');
                if (!element) return; // Element not found

                if (element.style.display === 'none' || element.style.display === '') {
                    element.style.display = 'table-row'; // Or 'block' if it's not a table row
                    if (indicator) indicator.textContent = '-';
                } else {
                    element.style.display = 'none';
                    if (indicator) indicator.textContent = '+';
                }
            }
            console.log("Allegation toggle listeners attached.");

            // ----- Back to Top Button -----
            const backToTopButton = document.getElementById("back-to-top");
            if (backToTopButton) {
                 const sections = ['summary', 'afff-by-the-numbers', 'book-meeting', 'upcoming-hearings', 'litigation-by-firm-report', 'detailed-filings-report', 'adspy-report']; // Added new sections
                let currentSectionIndex = 0;

                window.onscroll = function () {
                    // Show/hide button
                    if (document.body.scrollTop > 100 || document.documentElement.scrollTop > 100) { // Increased threshold
                        backToTopButton.style.display = "block";
                    } else {
                        backToTopButton.style.display = "none";
                    }

                    // Update current section index (simple approach)
                     let closestSectionIndex = 0;
                     let minDistance = Infinity;
                     sections.forEach((id, index) => {
                         const section = document.getElementById(id);
                         if (section) {
                            const distance = Math.abs(section.getBoundingClientRect().top);
                            if (distance < minDistance) {
                                minDistance = distance;
                                closestSectionIndex = index;
                            }
                         }
                     });
                     currentSectionIndex = closestSectionIndex;
                };

                backToTopButton.onclick = function () {
                    console.log("Back to Top button clicked");
                    window.scrollTo({ top: 0, behavior: 'smooth' });
                    // Simple scroll to top for now, advanced logic removed for clarity
                };
                console.log("Back to Top button logic attached.");
            }

             // ----- Search Functionality (Keep as is) -----
             let searchResults = [];
             let currentResultIndex = -1;
             const searchForm = document.getElementById('search-form');
             const searchInput = document.getElementById('search-input');
             const searchInfo = document.getElementById('search-info');
             const nextButton = document.querySelector('button[onclick="highlightNextResult()"]'); // Need to adjust this selector if button changed

             function performSearch() { /* ... search logic ... */ }
             function highlightNextResult() { /* ... highlight logic ... */ }
             function searchInElement(element, searchTerm) { /* ... recursive search logic ... */ }

             // Remove existing highlights function (useful for re-search)
            function removeSearchHighlights() {
                document.querySelectorAll('.search-highlight').forEach((el) => {
                    // Replace the highlight span with its text content
                    el.outerHTML = el.innerHTML;
                });
                 // Need to re-normalize adjacent text nodes if spans were inserted mid-text
                 document.body.normalize(); // Normalizes text nodes throughout the body
            }

             if (searchForm && searchInput && searchInfo) {
                 searchForm.addEventListener('submit', function (e) {
                     e.preventDefault();
                     removeSearchHighlights(); // Clear previous highlights
                     performSearch();
                 });

                 searchInput.addEventListener('input', function () {
                     removeSearchHighlights(); // Clear previous highlights
                     if (this.value.length >= 2) {
                         performSearch();
                     } else if (searchInfo) {
                         searchInfo.textContent = 'Enter at least 2 characters.';
                         searchResults = []; // Clear results array
                         currentResultIndex = -1;
                     }
                 });
                 console.log("Search listeners attached.");

                // Correctly attach listener for the 'Next' button if it exists
                 const actualNextButton = document.querySelector('#search-form button[type="button"]'); // More specific selector
                 if (actualNextButton && actualNextButton.textContent.includes('Next')) { // Check text content
                    actualNextButton.addEventListener('click', highlightNextResult);
                    console.log("Next search result button listener attached.");
                 } else {
                    console.warn("Next search button not found or text doesn't match.");
                 }

             } else {
                 console.warn("Search form elements not found, search disabled.");
             }


            // ----- Lazy Loading (Keep as is) -----
            var lazyImages = [].slice.call(document.querySelectorAll("img.lazy"));
            if ("IntersectionObserver" in window) { /* ... IntersectionObserver logic ... */ }
            else { console.warn("IntersectionObserver not supported..."); }

            console.log("All relevant scripts loaded and initialized.");
        });
        // --- End of Interactivity and Scrolling Logic ---
    </script>
{% endif %}

    {# Email Version #}
    {# Email Version - CORRECTED #}
{% else %}
    <table role="presentation" class="email-container" width="100%" border="0" cellpadding="0" cellspacing="0"
           style="max-width: 800px; margin: 0 auto; background-color: #ffffff;">
        <tr>
            <td class="content-padding" style="padding: 20px 15px;">
                {# --- Email Header --- #}
                <div class="email-header"
                     style="background: linear-gradient(135deg, #115197 0%, #39BEFA 100%); border-radius: 12px; margin-bottom: 32px; padding: 24px 15px; text-align: center;">
                    <h1 style="margin: 0 0 6px 0; font-size: 28px; line-height: 1.3; font-weight: bold; color: #ffffff;">
                        Mass Tort Report </h1>
                    <p style="margin: 0; font-size: 16px; color: #ffffff; font-weight: 500;">{{ report_date if report_date is defined else 'Report Date' }}</p>
                </div>

                {# --- Email Header Sponsorship --- #}
                {% if sponsorships and sponsorships.header_sponsorship_data and sponsorships.header_sponsorship_data.display %}
                    {% set sponsor_data = sponsorships.header_sponsorship_data %}
                    <table role="presentation" width="100%" border="0" cellpadding="0" cellspacing="0"
                           class="header-sponsor-email-outer" style="margin-bottom: 32px; padding: 0;">
                        <tr>
                            <td style="background-color: #ffffff; border: 1px solid #e5e7eb; border-radius: 8px; padding: 15px;">
                                <table role="presentation" width="100%" border="0" cellpadding="0" cellspacing="0"
                                       class="header-sponsor-email-inner" style="border-collapse: collapse;">
                                    <tr>
                                        <td align="center" valign="middle" class="sponsor-logo-cell"
                                            style="padding-bottom: 10px !important;">{% if sponsor_data.logo_svg_link %}
                                            <img src="{{ sponsor_data.logo_svg_link }}" alt="Sponsor Logo" width="150"
                                                 style="display: inline-block; width: auto; max-width: 180px; height: auto; border: 0; outline: none;"> {% endif %}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="center" valign="middle" class="sponsor-text-cell"
                                            style="padding: 0 10px 10px 10px !important; font-family: 'Inter', Arial, sans-serif; font-size: 14px; color: #374151; line-height: 1.5; text-align: center !important;">
                                            <p style="margin: 0;">{{ sponsor_data.email_ad_copy|safe if sponsor_data.email_ad_copy else '' }}</p>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="center" valign="middle" class="sponsor-button-cell"
                                            style="padding-top: 5px !important; text-align: center !important;">
                                            {% if sponsor_data.cta_link %}
                                                <a href="{{ sponsor_data.cta_link }}" target="_blank"
                                                   class="email-button"
                                                   style="display: inline-block; background-color: #2563eb; color: #ffffff !important; padding: 10px 18px; text-decoration: none !important; border-radius: 6px; font-weight: bold; font-size: 14px; line-height: 1; white-space: nowrap;">Book
                                                    Meeting</a> {% endif %} </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table>
                {% endif %}

                {# --- Email Summary Section --- #}
                <section id="summary" class="mb-8" style="margin-bottom: 32px;">
                    <div style="background-color: #ffffff; padding: 24px 15px; border-radius: 12px; border: 1px solid #e5e7eb; overflow: hidden;">
                        <h2 class="section-title">Report Summary</h2>
                        <table role="presentation" width="100%" border="0" cellpadding="0" cellspacing="0"
                               class="mb-6 stat-card-table"
                               style="margin-bottom: 24px; width: 100%; border-collapse: collapse; font-size: 0;">
                            <tr>
                                <td width="50%"
                                    style="display:inline-block; width:50%; max-width:50%; padding-right: 5px; padding-left:0; padding-bottom:15px; box-sizing: border-box; vertical-align: top; font-size: 14px;">
                                    <a href="{{ s3_prod }}/{{ iso_date }}/index.html#litigation-report?gmasstrack=false"
                                       target="_blank" class="stat-card-link">
                                        <div class="stat-card-email-inner"
                                             style="background-color: #eef2ff; border-radius: 8px; padding: 15px;">
                                            <table role="presentation" width="100%" border="0" cellpadding="0"
                                                   cellspacing="0">
                                                <tr>
                                                    <td valign="top"><p
                                                            style="margin: 0 0 4px 0; font-size: 14px; color: #4f46e5; line-height: 1.4;">
                                                        New Tort Claims Filed*</p>
                                                        <p class="stat-value"
                                                           style="margin: 0; font-size: 20px; font-weight: bold; color: #3730a3;">{{ num_torts if num_torts is defined else 'N/A' }}</p>
                                                    </td>
                                                </tr>
                                            </table>
                                        </div>
                                    </a>
                                </td>
                                <td width="50%"
                                    style="display:inline-block; width:50%; max-width:50%; padding-left: 5px; padding-right:0; padding-bottom:15px; box-sizing: border-box; vertical-align: top; font-size: 14px;">
                                    <a href="{{ s3_prod }}/{{ iso_date }}/index.html#adspy-report?gmasstrack=false"
                                       target="_blank" class="stat-card-link">
                                        <div class="stat-card-email-inner"
                                             style="background-color: #e0f2fe; border-radius: 8px; padding: 15px;">
                                            <table role="presentation" width="100%" border="0" cellpadding="0"
                                                   cellspacing="0">
                                                <tr>
                                                    <td valign="top"><p
                                                            style="margin: 0 0 4px 0; font-size: 14px; color: #0284c7; line-height: 1.4;">
                                                        New Legal Ads (14d)</p>
                                                        <p class="stat-value"
                                                           style="margin: 0; font-size: 20px; font-weight: bold; color: #0369a1;">{{ num_ads if num_ads is defined else 'N/A' }}</p>
                                                    </td>
                                                </tr>
                                            </table>
                                        </div>
                                    </a>
                                </td>
                            </tr>
                        </table>
                        <div style="background: linear-gradient(135deg, rgba(17, 81, 151, 0.08) 0%, rgba(57, 190, 250, 0.08) 100%); border-left: 4px solid #115197; padding: 20px; border-radius: 8px; margin-bottom: 24px;">
                            <p style="color: #374151; margin: 0 0 12px 0; font-size: 14px; line-height: 1.6;">
                                {% if is_weekly_report %}For the week
                                    {{ summary_date if summary_date is defined else 'this week' }}{% else %}On
                                    {{ summary_date if summary_date is defined else 'this date' }}{% endif %}, there
                                were <span
                                    style="font-weight: bold; color: #115197;">{{ num_torts if num_torts is defined else 'N/A' }}</span>
                                new claims filed* and <span
                                    style="font-weight: bold; color: #115197;">{{ num_ads if num_ads is defined else 'N/A' }}</span>
                                new legal ads detected. </p>
                            <p style="color: #4b5563; margin: 0 0 12px 0; font-size: 13px; line-height: 1.5;">* Claims
                                count reflects total plaintiffs...</p>
                            <p style="color: #374151; margin: 0; font-size: 14px; line-height: 1.6;">Prefer a weekly
                                summary? Reply to this email.</p>
                        </div>
                        {% if s3_prod is defined and iso_date is defined %}
                            <div style="text-align: center; margin-top: 24px;"><p
                                    style="font-size: 14px; color: #6b7280; margin: 0 0 12px 0; text-align: center;">For
                                a better user experience, check out our updated interactive report:</p> <a
                                    href="{{ s3_prod }}/{{ iso_date }}/index.html#summary?gmasstrack=false"
                                    target="_blank" class="email-button">View Full Report</a></div>
                        {% endif %}
                    </div>
                </section>

                {# --- Optional Section Placeholders/Content - CORRECTED --- #}

                {% if show_announcements is defined and show_announcements and announcements %}
                    <section id="announcements" class="mb-8" style="margin-bottom: 16px;">
                        <table role="presentation" width="100%" border="0" cellpadding="0" cellspacing="0"
                               class="email-collapsed-section">
                            <tr>
                                <td><a href="{{ s3_prod }}/{{ iso_date }}/index.html#announcements?gmasstrack=false"
                                       target="_blank">
                                    <div><p class="email-collapsed-title">Announcements</p><span
                                            class="email-collapsed-arrow">></span></div>
                                </a></td>
                            </tr>
                        </table>
                    </section>
                {% endif %}

                {# --- Upcoming Hearings Section (EMAIL - RENDERED FULLY) --- #}
                {% if show_upcoming_hearings is defined and show_upcoming_hearings and upcoming_hearings %}
                    <section id="upcoming-hearings" class="mb-8" style="margin-bottom: 32px;">
                        <div style="background-color: #ffffff; border: 1px solid #e5e7eb; border-radius: 8px; overflow: hidden;">
                            {# Section Title #}
                            <div style="background-color: #f3f4f6; padding: 12px 20px; border-bottom: 1px solid #e5e7eb;">
                                <h2 style="margin:0; font-size: 18px; font-weight: bold; color: #1f2937;">Upcoming
                                    Hearings</h2>
                            </div>
                            {# Section Content #}
                            <div style="padding: 20px;">
                                <div style="font-size: 0;">{# Prevent whitespace issues between inline-block elements #}
                                    {% for hearing in upcoming_hearings %}
                                        <div style="margin-bottom: 16px; padding-bottom: 16px; border-bottom: 1px solid #eee;">
                                            <table width="100%" cellpadding="0" cellspacing="0" border="0"
                                                   style="border-collapse: collapse;">
                                                <tr>
                                                    <td width="70"
                                                        style="width: 70px; padding-right: 15px; vertical-align: top;"
                                                        valign="top">
                                                        <div style="background-color: #eef2ff; color: #3730a3; border-radius: 6px; padding: 8px; text-align: center;">
                                                            <div style="font-size: 11px; font-weight: bold; text-transform: uppercase; margin-bottom: 2px;">{{ hearing.date_month_short }}</div>
                                                            <div style="font-size: 18px; font-weight: bold; line-height: 1;">{{ hearing.date_day }}</div>
                                                            <div style="font-size: 10px; margin-top: 2px;">{{ hearing.date_year }}</div>
                                                        </div>
                                                    </td>
                                                    <td style="vertical-align: top;" valign="top">
                                                        <h3 style="margin: 0 0 5px 0; font-size: 15px; font-weight: 500; color: #1f2937; line-height: 1.4;">
                                                            {% if hearing.link %}
                                                                <a href="{{ hearing.link }}" target="_blank"
                                                                   title="{{ hearing.formatted_date }}"
                                                                   style="color: #2563eb; text-decoration: underline;">{{ hearing.title }}</a>
                                                            {% else %}
                                                                <span title="{{ hearing.formatted_date }}">{{ hearing.title }}</span>
                                                            {% endif %}
                                                        </h3>
                                                        {% if hearing.details %}
                                                            <p style="margin: 0; font-size: 13px; color: #4b5563; line-height: 1.5;">{{ hearing.details }}</p>
                                                        {% endif %}
                                                    </td>
                                                </tr>
                                            </table>
                                        </div>
                                    {% else %}
                                        <p style="margin: 0; font-size: 14px; color: #6b7280;">No upcoming hearings
                                            listed.</p>
                                    {% endfor %}
                                    {# Remove extra bottom margin/border from last item #}
                                    <style type="text/css"> div > div:last-child {
                                        margin-bottom: 0 !important;
                                        padding-bottom: 0 !important;
                                        border-bottom: none !important;
                                    } </style>
                                </div>
                            </div>
                        </div>
                    </section>
                {% endif %}

                {# --- AFFF Stats Placeholder --- #}
                {% if show_afff_stats is defined and show_afff_stats %}
                    <section id="afff-numbers" class="mb-8" style="margin-bottom: 16px;">
                        <table role="presentation" width="100%" border="0" cellpadding="0" cellspacing="0"
                               class="email-collapsed-section">
                            <tr>
                                <td>
                                    <a href="{{ s3_prod }}/{{ iso_date }}/index.html#afff-numbers?gmasstrack=false"
                                       target="_blank">
                                        <div><p class="email-collapsed-title">AFFF By The Numbers*</p><span
                                                class="email-collapsed-arrow">></span></div>
                                    </a>
                                </td>
                            </tr>
                        </table>
                    </section>
                {% endif %}

                {# --- Calendly Placeholder --- #}
                {% if show_calendly is defined and show_calendly %}
                    <section id="book-meeting" class="mb-8" style="margin-bottom: 16px;">
                        <table role="presentation" width="100%" border="0" cellpadding="0" cellspacing="0"
                               class="email-collapsed-section"
                               style="background-color: #dcfce7; border: 1px solid #bbf7d0;">
                            <tr>
                                <td><a href="{{ s3_prod }}/{{ iso_date }}/index.html#book-meeting?gmasstrack=false"
                                       target="_blank" style="color: #15803d !important;">
                                    <div><p class="email-collapsed-title" style="color: #15803d !important;">Book a
                                        Demo</p>
                                        <span class="email-collapsed-arrow" style="color: #15803d !important;">></span>
                                    </div>
                                </a></td>
                            </tr>
                        </table>
                    </section>
                {% endif %}

                {# --- Special Reports Placeholder --- #}
                {% if show_special_reports is defined and show_special_reports and special_reports %}
                    <section id="special-reports" class="mb-8" style="margin-bottom: 16px;">
                        <table role="presentation" width="100%" border="0" cellpadding="0" cellspacing="0"
                               class="email-collapsed-section">
                            <tr>
                                <td><a href="{{ s3_prod }}/{{ iso_date }}/index.html#special-reports?gmasstrack=false"
                                       target="_blank">
                                    <div><p class="email-collapsed-title">Special Reports</p><span
                                            class="email-collapsed-arrow">></span></div>
                                </a></td>
                            </tr>
                        </table>
                    </section>
                {% endif %}

                {# --- News Placeholder --- #}
                {% if show_news is defined and show_news %}
                    <section id="news" class="mb-8" style="margin-bottom: 16px;">
                        <table role="presentation" width="100%" border="0" cellpadding="0" cellspacing="0"
                               class="email-collapsed-section">
                            <tr>
                                <td>
                                    <a href="{{ s3_prod }}/{{ iso_date }}/index.html#news?gmasstrack=false"
                                       target="_blank">
                                        <div><p class="email-collapsed-title">Recent JPML Panel & CMOs</p><span
                                                class="email-collapsed-arrow">></span></div>
                                    </a>
                                </td>
                            </tr>
                            {% if news_items and news_items|length > 0 %}
                                <tr style="font-size: 12px; color: #6b7280; line-height: 1.5;">
                                    <td style="padding: 0px 15px 10px 15px;">
                                        {{ news_items[0].date }} -
                                        {{ news_items[0].title|striptags|truncate(60) }}{% if news_items|length > 1 %}
                                            ... (and {{ news_items|length - 1 }} more){% endif %}
                                    </td>
                                </tr>
                            {% endif %}
                        </table>
                    </section>
                {% endif %}

                {# --- Main Email Content Sections --- #}
                <section id="litigation-report" class="mb-8" style="margin-bottom: 32px;"><h2 class="section-title">
                    Litigation by Tort Report</h2>
                    <div style="margin-bottom: 24px; text-align: center;"> {{ sponsorships.sponsorship_1.html|safe if sponsorships is defined and sponsorships.sponsorship_1 and sponsorships.sponsorship_1.display else '' }} </div>
                    <div> {# --- Litigation Report Rendering Logic --- #}
                        {% if grouped is defined and grouped %}
                            {% set CAT_MDL = 'Multidistrict Litigation' %}
                            {% set CAT_DATA_BREACH = 'Data Breach' %}
                            {% set CAT_PRIVACY = 'Privacy Violation' %}
                            {% set CAT_CLASS_ACTION = 'Class Action' %}
                            {% set CAT_OTHER = 'Other Litigation' %}
                            {% set categorized_torts = { CAT_MDL: [], CAT_DATA_BREACH: [], CAT_PRIVACY: [], CAT_CLASS_ACTION: [], CAT_OTHER: [] } %}
                            {% for title, sub_df in grouped.items() %}
                                {% if sub_df is defined and not sub_df.empty %}
                                    {% set first_row_mdl_num = sub_df.mdl_num.iloc[0] if 'mdl_num' in sub_df.columns and not sub_df.mdl_num.empty and not sub_df.mdl_num.iloc[0] in ['NA', None, ''] else '' %}
                                    {% set allegations = get_allegations_and_causes(docket_df_global, title, first_row_mdl_num) if docket_df_global is defined and get_allegations_and_causes is defined else None %}
                                    {% set lower_title = title|lower %}
                                    {% set category = CAT_OTHER %}
                                    {% if 'data breach' in lower_title %} {% set category = CAT_DATA_BREACH %}
                                    {% elif 'privacy' in lower_title %} {% set category = CAT_PRIVACY %}
                                    {% elif 'class action' in lower_title %} {% set category = CAT_CLASS_ACTION %}
                                    {% elif first_row_mdl_num %}
                                        {% set mdl_num_for_check = first_row_mdl_num|string|trim %}
                                        {% if ((mdl_num_for_check|length >= 4 or mdl_num_for_check == '25') and mdl_num_for_check != '9000') %}
                                            {% set category = CAT_MDL %}
                                        {% endif %}
                                    {% endif %}
                                    {% set item_filings = sub_df.Filings.sum() if 'Filings' in sub_df else 0 %}
                                    {% set _ = categorized_torts[category].append((item_filings, title, sub_df, allegations)) %}
                                {% endif %}
                            {% endfor %}

                            {% if categorized_torts[CAT_MDL] %}
                                <div class="mb-6" style="margin-bottom: 24px;">
                                    <h3 class="category-header">{{ CAT_MDL }}</h3>
                                    <div style="margin-top: 16px;">
                                        {% set sorted_mdl_items = categorized_torts[CAT_MDL] | sort(reverse=True, attribute=0) %}
                                        {% for _, title, sub_df, allegations in sorted_mdl_items %}
                                            {{ render_litigation_table(title, allegations, sub_df, is_web, s3_prod if s3_prod is defined else '', iso_date if iso_date is defined else '') }}
                                        {% endfor %}
                                    </div>
                                </div>
                            {% endif %}

                            {% set other_categories_data = [] %}
                            {% for cat_name, cat_items_list in categorized_torts.items() %}
                                {% if cat_name != CAT_MDL and cat_items_list %}
                                    {% set category_total = cat_items_list | sum(attribute=0) %}
                                    {% set _ = other_categories_data.append({'total': category_total, 'name': cat_name, 'item_list': cat_items_list}) %}
                                {% endif %}
                            {% endfor %}
                            {% set sorted_other_categories = other_categories_data | sort(reverse=True, attribute='total') %}
                            {% for category_data in sorted_other_categories %}
                                <div class="mb-6" style="margin-bottom: 24px;">
                                    <h3 class="category-header">{{ category_data.name }}</h3>
                                    <div style="margin-top: 16px;">
                                        {% set sorted_cat_items = category_data.item_list | sort(reverse=True, attribute=0) %}
                                        {% for _, title, sub_df, allegations in sorted_cat_items %}
                                            {{ render_litigation_table(title, allegations, sub_df, is_web, s3_prod if s3_prod is defined else '', iso_date if iso_date is defined else '') }}
                                        {% endfor %}
                                    </div>
                                </div>
                            {% endfor %}
                        {% else %}
                            <p class="text-sm text-gray-500" style="font-size: 14px; color: #6b7280;">No litigation data
                                available.</p>
                        {% endif %}
                    </div>
                </section>

                <section id="detailed-filings" class="mb-8" style="margin-bottom: 32px;"><h2 class="section-title">
                    Detailed Filings Report</h2>
                    <div style="margin-bottom: 24px; text-align: center;"> {{ sponsorships.sponsorship_2.html|safe if sponsorships is defined and sponsorships.sponsorship_2 and sponsorships.sponsorship_2.display else '' }} </div>
                    {{ render_detailed_filings(docket_df if docket_df is defined else None, is_web, s3_prod if s3_prod is defined else '', iso_date if iso_date is defined else '') }}
                </section>

                <section id="adspy-report" class="mb-8" style="margin-bottom: 32px;"><h2 class="section-title">AdSpy
                    Report</h2>
                    <div style="margin-bottom: 24px; text-align: center;"> {{ sponsorships.sponsorship_3.html|safe if sponsorships is defined and sponsorships.sponsorship_3 and sponsorships.sponsorship_3.display else '' }} </div>
                    {{ render_ad_report(grouped_ads if grouped_ads is defined else None, is_web, s3_prod if s3_prod is defined else '', iso_date if iso_date is defined else '') }}
                </section>

                {# --- Email Footer Section --- #}
                <section id="footer" class="mt-8" style="margin-top: 32px;">
                    <div style="background-color: #374151; border-radius: 12px; padding: 24px 15px; text-align: center; color: #9ca3af;">
                        <div style="margin-bottom: 16px;"><img
                                src="https://cdn.lexgenius.ai/assets/images/icons/lexgenius_logo.svg"
                                alt="LexGenius Logo" height="40" style="height: 40px; margin: 0 auto; display: block;">
                        </div>
                        <p style="font-size: 13px; line-height: 1.5; margin: 0 0 8px 0;">LexGenius | 20 Eastgate Drive,
                            Unit D, Boynton Beach, FL 33436</p>
                        <p style="font-size: 13px; line-height: 1.5; margin: 0 0 16px 0;">
                            © {{ report_date.split(',')[-1].strip() if report_date is defined else '2024' }} LexGenius.
                            All Rights Reserved.</p> <a href="https://www.gmass.co/gmass/u?u=OUTBOUND" target="_blank"
                                                        style="color: #60a5fa; text-decoration: underline; font-size: 13px;">Unsubscribe</a>
                    </div>
                </section>
            </td>
        </tr>
    </table>
{% endif %} {# End of Email Version #}

</body>
</html>