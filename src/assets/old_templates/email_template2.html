<!DOCTYPE html>
<html>
<head>
    <title>LexGenius Litigation Watch</title>
    <style>
        body, html {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
        }
        .header-image {
            background-color: #18191B;
            text-align: center;
            padding: 20px 0;
        }
        .header-image img {
            height: 25%;
            width: 25%;
        }
        .main-header {
            background-color: #5585b5;
            color: white;
            text-align: center;
            padding: 20px 0;
        }
        .content-section {
            background-color: white;
            color: black;
            text-align: left;
            padding: 20px;
        }
        .preview-intro {
            text-align: center;
            padding: 20px;
            background-color: #f8f8f8; /* Light background to highlight intro text */
            font-size: 18px;
            color: #333333; /* Dark text for readability */
        }
        .link-image {
            text-align: center;
            background-color: white;
            padding: 20px 0;
        }
        .link-image img {
            width: 100%; /* Ensures the image takes the full width of its container */
            max-width: 800px; /* Limits the size to prevent overly large displays on larger screens */
        }
        .button-container {
            background-color: #d6eaf8; /* Changed to light grey-blue for better contrast */
            text-align: center;
            padding: 20px;
        }
        .button {
            background-color: #6cace4; /* Lighter blue for button */
            color: white;
            border: none;
            padding: 15px 30px; /* Increased padding for larger button */
            text-decoration: none;
            cursor: pointer;
            display: inline-block; /* Centers the button within its container */
            box-shadow: 4px 4px 8px #888888; /* Added box shadow */
        }
        .feedback-section {
            background-color: #f3f3f3; /* Light grey background for contrast */
            color: black;
            text-align: center;
            padding: 20px;
            font-size: 16px;
            margin-bottom: 30px; /* Added extra space below the button */
        }
        .feedback-button {
            background-color: #5585b5;
            color: white;
            border: none;
            padding: 10px 20px;
            cursor: pointer;
            text-decoration: none;
            box-shadow: 2px 2px 5px #888888; /* Box shadow for button */
        }
        @media (max-width: 600px) {
            .header-image img {
                width: 50%;
                height: 50%;
            }
            .link-image img {
                width: 100%; /* Larger display for the image on mobile */
            }
        }
    </style>
</head>
<body>
    <div class="header-image">
        <img src="https://lexgenius.s3.us-west-2.amazonaws.com/LexGeniusAI.png" alt="LexGenius AI">
    </div>
    <div class="main-header">
        <h1>Litigation Watch: May 22nd, 2024</h1>
    </div>
    <div class="content-section">
        <p>Gain a competitive edge with real-time updates on mass tort and product liability filings, key legal FB ads, and soon, the latest MDL decisions and hearings. Discover the fastest route to staying informed on mass tort litigation and advertising dynamics. Don&apos;t miss out!</p>
        <p>On May 21st, 2024 there were 111 mass tort/product liability filings and 174 new FB legal ads.</p>
    </div>
    <div class="button-container">
        <a class="button" href="https://lexgenius.s3.us-west-2.amazonaws.com/20240521/index.html">Explore Today's Insights</a>
    </div>
    <div class="preview-intro">
        <p>Take a sneak peek at today's detailed report!</p>
    </div>
    <div class="link-image">
        <img src="https://github.com/GratefulDave/screenshot/blob/main/demo.gif?raw=true" alt="Demo">
    </div>
    <div class="feedback-section">
        <p>We value your input! Let us know what topics or features you'd like to see in future updates.</p>
        <a class="feedback-button" href="https://www.surveymonkey.com/r/CYCBF9V">Submit Feedback</a>
    </div>
</body>
</html>
