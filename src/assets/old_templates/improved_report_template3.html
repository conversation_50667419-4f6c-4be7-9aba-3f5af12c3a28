<!DOCTYPE html>
<html lang="en" prefix="og: http://ogp.me/ns# fb: http://ogp.me/ns/fb#">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LexGenius: Mass Tort Report</title>
    <link rel="icon" type="image/x-icon" href="https://cdn.lexgenius.ai/bimi/lexgenius_logo.svg">

    <!-- Facebook-specific Meta Tags -->
    <meta property="fb:app_id" content="495908463340329">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="LexGenius Mass Tort Report">
    <meta property="og:description" content="Daily report on mass tort filings and legal ads">
    <meta property="og:image" content="https://cdn.lexgenius.ai/assets/images/lexgenius_preview.jpg">
    <meta property="og:image:width" content="1024">
    <meta property="og:image:height" content="1024">
    <meta property="og:url" content="https://cdn.lexgenius.ai/assets/images/lexgenius_preview.jpg">
    <meta property="og:type" content="website">
    <meta property="og:site_name" content="LexGenius">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="LexGenius Mass Tort Report">
    <meta name="twitter:description" content="Daily report on mass tort filings and legal ads">
    <meta name="twitter:image" content="https://cdn.lexgenius.ai/assets/images/lexgenius_preview.jpg">

    <!-- Apple-specific meta tag for iMessage -->
    <meta name="apple-mobile-web-app-title" content="LexGenius Report">
    <style>

        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.8;
            color: #333;
            margin: 0;
            padding: 0;
            background-color: #f4f4f4;
        }

        .container {
            width: 100%;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            box-sizing: border-box;
            background-color: #ffffff;
        }

        header {
            background-color: #115197;
            color: white;
            text-align: center;
            padding: 30px 0;
            margin-bottom: 20px;
        }

        h1 {
            font-size: 28px;
            margin: 0;
        }

        h2 {
            color: #115197;
            border-bottom: 2px solid #115197;
            padding-bottom: 10px;
            margin-top: 30px;
        }

        .newsletter-signup {
            background-color: #39BEFA;
            color: black;
            padding: 15px;
            margin-bottom: 20px;
            text-align: center;
        }

        .summary {
            background-color: #f9f9f9;
            border-left: 4px solid #115197;
            padding: 15px;
            margin-bottom: 20px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        th, td {
            padding: 12px;
            border: 1px solid #ddd;
            text-align: left;
        }

        th {
            background-color: #115197;
            color: white;
        }

        tr:nth-child(even) {
            background-color: #f2f2f2;
        }

        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            font-size: 12px;
            color: #666;
        }

        .title-row {
            background-color: #115197;
            color: white;
            cursor: pointer;
        }

        .filings {
            text-align: right;
            width: 15%;
            min-width: 60px;
        }

        .secondary-title, .secondary-title td {
            background-color: #39BEFA !important;
            color: black !important;
            font-weight: normal !important;
        }

        .secondary-title a {
            color: black !important;
            text-decoration: underline !important;
        }

        .alt-row {
            background-color: #f2f2f2 !important;
        }

        .law-firm, .versus {
            padding-left: 16px;
        }

        .filings {
            text-align: right;
            width: 15%;
            min-width: 60px;
        }

        a {
            color: black;
            text-decoration: none;
        }

        a:hover {
            text-decoration: underline;
        }

        .interactive-report {
            text-align: center;
            margin-top: 20px;
            padding: 20px;
            background-color: #f0f0f0;
            border-radius: 10px;
        }

        .email-specific {
            margin: 20px 0;
        }

        .adspy-summary {
            flex: 1;
            min-width: 0;
        }

        .title-row {
            background-color: #115197;
            color: white;
        }

        .secondary-title {
            background-color: #39BEFA;
        }

        .cool-button-text {
            font-size: 18px;
            color: #333;
            margin-bottom: 15px;
        }

        .cool-button {
            display: inline-block;
            padding: 12px 24px;
            font-size: 18px;
            font-weight: bold;
            text-align: center;
            text-decoration: none;
            color: #ffffff;
            background-color: #115197;
            border-radius: 50px;
            border: none;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .cool-button:hover {
            background-color: #39BEFA;
            box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);
            transform: translateY(-2px);
        }

        #litigation-by-firm-report a,
        #detailed-filings-report a,
        #adspy-report a,
        #upcoming-hearings a {
            text-decoration: underline;
        }

        .logo {
            height: 60px; /* Adjust this value as needed */
            width: auto;
            display: block;
            margin: 10px auto;
        }

        .see-all-button-container {
            text-align: center;
            margin: 20px 0;
        }

        .see-all-button {
            display: inline-block;
            padding: 12px 24px;
            font-size: 16px;
            font-weight: bold;
            text-align: center;
            text-decoration: none;
            color: #ffffff;
            background-color: #115197;
            border-radius: 50px;
            border: none;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .see-all-button:hover {
            background-color: #39BEFA;
            box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);
            transform: translateY(-2px);
        }

        {% if is_web %}
            #top-nav {
                background-color: #115197;
                position: sticky;
                top: 0;
                z-index: 1000;
                width: 100%;
            }

            #top-nav-container {
                display: flex;
                flex-direction: column;
                width: 100%;
            }

            #top-nav ul {
                list-style-type: none;
                padding: 0;
                margin: 0;
                display: flex;
                justify-content: space-around;
            }

            #top-nav li {
                padding: 10px;
            }

            #top-nav a {
                color: white;
                text-decoration: none;
            }

            #search-container {
                background-color: #115197;
                padding: 20px 0px;
                display: flex;
                justify-content: center;
                align-items: center;
                width: 100%;
            }

            #search-form {
                display: flex;
                justify-content: center;
                align-items: center;
                width: 80%;
                max-width: 600px; /* Adjust this value as needed */
            }

            #search-input {
                flex-grow: 1;
                margin-right: 10px;
                padding: 5px;
                border: 2px solid #39BEFA;
                border-radius: 4px;
                outline-color: #39BEFA;
            }

            #search-container button {
                background-color: #39BEFA;
                color: white;
                border: none;
                padding: 5px 10px;
                margin-right: 5px;
                cursor: pointer;
                border-radius: 4px;
            }

            #search-info {
                color: white;
                margin-left: 10px;
            }

            .search-highlight {
                background-color: yellow;
                padding: 2px;
                border-radius: 2px;
            }
            #back-to-top {
                display: none;
                position: fixed;
                bottom: 20px;
                right: 20px;
                z-index: 99;
                border: none;
                outline: none;
                background-color: #115197;
                color: white;
                cursor: pointer;
                padding: 15px;
                border-radius: 50%;
                font-size: 18px;
            }

            #back-to-top:hover {
                background-color: #39BEFA;
            }
        {% endif %}

        @media (max-width: 600px) {
            .container {
                padding: 10px;
            }

            .allegations {
                display: block;
                padding: 10px;
                background-color: #f9f9f9;
            }

            .adspy-row {
                flex-wrap: wrap;
            }

            .adspy-summary {
                flex: 1 1 100%;
                width: 100%;
            }

            .adspy-date {
                flex: 1 1 50%;
                text-align: left;
            }
        }

        {% if is_web %}
            #top-nav {
                background-color: #115197;
                position: sticky;
                top: 0;
                z-index: 1000;
            }

            #top-nav-container {
                display: flex;
                flex-direction: column;
            }

            #top-nav ul {
                list-style-type: none;
                padding: 0;
                margin: 0;
                display: flex;
                justify-content: space-around;
            }

            #top-nav li {
                padding: 10px;
            }

            #top-nav a {
                color: white;
                text-decoration: none;
            }

            #search-container {
                background-color: #115197;
                padding: 10px 0;
                display: flex;
                justify-content: center;
                align-items: center;
            }

            #search-input {
                margin-right: 10px;
                padding: 5px;
                border: 2px solid #39BEFA;
                border-radius: 4px;
                outline-color: #39BEFA;
            }

            #search-container button {
                background-color: #39BEFA;
                color: white;
                border: none;
                padding: 5px 10px;
                margin-right: 5px;
                cursor: pointer;
                border-radius: 4px;
            }

            #search-info {
                color: white;
                margin-left: 10px;
            }
        {% endif %}
    </style>
    {% if is_web %}
        <!-- Google tag (gtag.js) -->
        <script async src="https://www.googletagmanager.com/gtag/js?id=G-9CREQVVWNF"></script>
        <script>
            window.dataLayer = window.dataLayer || [];

            function gtag() {
                dataLayer.push(arguments);
            }

            gtag('js', new Date());

            gtag('config', 'G-9CREQVVWNF');
        </script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.7.1/chart.min.js"></script>
        <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels@2.0.0"></script>
    {% endif %}
</head>
<body>
{% macro render_litigation_table(title, allegations_and_causes, sub_df, is_web) %}
    {% set title_id = title|clean_id %}
    <table id="litigation_{{ title_id }}">
        {% if is_web %}
            <tr class="title-row" data-target="allegation_{{ title_id }}">
                <th colspan="2">
                    {{ title }}
                    <span id="allegation_{{ title_id }}_indicator" style="float:right; cursor:pointer;">+</span>
                </th>
            </tr>
            <tr class="allegations-row" id="allegation_{{ title_id }}" style="display:none;">
                <td colspan="2">{{ allegations_and_causes }}</td>
            </tr>
        {% else %}
            <tr class="title-row">
                <th colspan="2">{{ title }}</th>
            </tr>
        {% endif %}
        <tr class='light-border secondary-title'>
            <td>Law Firm</td>
            <td class='filings'>Filings</td>
        </tr>
        {% for _, row in sub_df.iterrows() %}
            {% set law_firm_id = row.law_firm|clean_id %}
            <tr class='light-border'>
                <td class='law-firm'>
                    {% if is_web %}
                        <a href='#detail_{{ law_firm_id }}'>{{ row.law_firm }}</a>
                    {% else %}
                        <a href='#detail_{{ law_firm_id }}?gmasstrack=false'>{{ row.law_firm }}</a>
                    {% endif %}
                </td>
                <td class='filings'>{{ row.Filings }}</td>
            </tr>
        {% endfor %}
        <tr class='total-row light-border'>
            <td><b>Total</b></td>
            <td class='filings'><b>{{ sub_df.Filings.sum() }}</b></td>
        </tr>
    </table>
{% endmacro %}

{% macro render_detailed_filings(docket_df, is_web, limit=0) %}
    <a href='#litigation-by-firm-report{% if not is_web %}?gmasstrack=false{% endif %}'
       style='text-decoration: none; color: inherit;'>
        <h2>Detailed Filings Report</h2>
    </a>
    <p align="center">Sponsored by Keith Newstrom at <a href="https://marketingsix.com">Marketing Six</a></p>
    <table>
        {% for law_firm, group in docket_df.groupby('law_firm') %}
            {% if loop.index0 < limit or limit == 0 %}
                {% set total_filings = group.shape[0] %}
                <tr class='title-row' id='detail_{{ law_firm|clean_id }}'>
                    <th colspan='2'>{{ law_firm }} <b>({{ total_filings }}
                        new {{ 'filing' if total_filings == 1 else 'filings' }})</b></th>
                </tr>
                {% for title, title_group in group.groupby('title') %}
                    <tr class='secondary-title'>
                        <td>
                            {% if is_web %}
                                <a href='#litigation_{{ title|clean_id }}'>{{ title }}</a>
                            {% else %}
                                <a href='#litigation_{{ title|clean_id }}?gmasstrack=false'>{{ title }}</a>
                            {% endif %}
                        </td>
                        <td class='filings'>{{ title_group.shape[0] }}</td>
                    </tr>
                    {% for _, case in title_group.iterrows() %}
                        {% set versus = case.versus|title|replace('V.', 'v.')|replace('Et Al', 'et al') %}
                        {% set asterisks = '**' if case.transferred_in else '*' if case.pending_cto else '' %}
                        <tr>
                            <td colspan='2' class='versus'>
                                <a href='{{ case.s3_link }}' target='_blank'>{{ versus }}</a><b>{{ asterisks }}</b>
                            </td>
                        </tr>
                    {% endfor %}
                    {% if title_group.shape[0] > 0 %}
                        <tr class='cases-filed-row light-border'>
                            <td><b>Cases Filed:</b></td>
                            <td class='filings'><b>{{ title_group.shape[0] }}</b></td>
                        </tr>
                    {% endif %}
                {% endfor %}
            {% endif %}
        {% endfor %}
    </table>
    <p style='font-size: 1rem; text-align:left;'><b>*</b> CTO Filed</p>
    <p style='font-size: 1rem; text-align:left;'><b>**</b> CTO Finalized</p>
{% endmacro %}

{% macro render_ad_report(grouped_ads, is_web, s3_prod, iso_date, adspy_limit=0) %}
    <p style="text-align:center">Click on the Summary to see the FB ad.</p>
    <table>
    {% for name, group in grouped_ads %}
        {% set num_ads = group.shape[0] %}
        {% if num_ads > 0 %}
            <tr class="title-row">
                <th colspan="3">{{ name }} <b>({{ num_ads }} {{ 'new ad' if num_ads == 1 else 'new ads' }})</b></th>
            </tr>
            <tr class="secondary-title">
                <td>Summary</td>
                <td>Start Date</td>
                <td>End Date</td>
            </tr>
            {% for index, row in group.iterrows() %}
                {% if loop.index0 < adspy_limit or adspy_limit == 0 %}
                    {% if row['summary'] != 'Summary generation failed' %}
                        <tr class="{{ 'alt-row' if loop.index is even else '' }}">
                            <td class="adspy-summary">
                                <a href='{{ s3_prod }}/{{ iso_date }}/ads/{{ row.ad_archive_id }}.html' target='_blank'>
                                    {{ row['summary'] }}
                                </a>
                            </td>
                            <td>{{ row['start_date'] }}</td>
                            <td>{{ row['end_date'] }}</td>
                        </tr>
                    {% endif %}
                {% endif %}
            {% endfor %}
        {% endif %}
    {% endfor %}
    </table>
{% endmacro %}

{% if is_web %}
    <nav id="top-nav">
        <div id="top-nav-container">
            <div class="logo-container">
                <img src="https://cdn.lexgenius.ai/assets/images/icons/lexgenius_logo.svg" alt="LexGenius Logo"
                     class="logo">
            </div>
            <ul>
                <li><a href="#summary" onclick="smoothScroll('summary'); return false;">Summary</a></li>
                <li><a href="#litigation-by-firm-report"
                       onclick="smoothScroll('litigation-by-firm-report'); return false;">Litigation by Tort</a></li>
                <li><a href="#detailed-filings-report" onclick="smoothScroll('detailed-filings-report'); return false;">Detailed
                    Filings</a></li>
                <li><a href="#adspy-report" onclick="smoothScroll('adspy-report'); return false;">AdSpyReport</a></li>
            </ul>
            <div id="search-container">
                <form id="search-form" onsubmit="performSearch(); return false;">
                    <input type="text" id="search-input" placeholder="Search...">
                    <button type="submit">Search</button>
                    <button type="button" onclick="highlightNextResult()">Next</button>
                    <span id="search-info"></span>
                </form>
            </div>
        </div>
    </nav>
{% endif %}

<div class="container">
    <header>
        {% if not is_web %}
            <div class="logo-container">
                <img src="https://cdn.lexgenius.ai/assets/images/icons/lexgenius_logo.svg" alt="LexGenius Logo"
                     class="logo">
            </div>
        {% endif %}
        <h1 align="center">{{ report_date }} Mass Tort Report</h1>
    </header>

    <section id="summary" class="summary">
        <h2>Summary</h2>
        <p>On {{ summary_date }}, there were <strong>{{ num_torts }}</strong> new mass tort/product liability claims
            filed and <strong>{{ num_ads }}</strong> new legal ads live on FB over the past 14 days.</p>
        <p>
            To keep receiving emails from us, <a style="color:green; text-decoration: underline; font-weight: bold;"
                                                 href="//www.campaignmonitor.com/resources/guides/whitelisting/">please
            add us to your address book.</a></p>
        {% if is_web %}
            <div class="chart-container" style="height: 500px;">
                <canvas id="tort-chart"></canvas>
            </div>
        {% endif %}
    </section>

    <section id="upcoming-hearings" class="upcoming-hearings">
        <h2>Upcoming Hearings</h2>
        <table>
            <tr>
                <th>Hearing</th>
                <th>Date</th>
            </tr>
            {% for hearing in upcoming_hearings %}
                <tr>
                    <td><a href="{{ hearing.link }}">{{ hearing.title }}</a></td>
                    <td>{{ hearing.date }}</td>
                </tr>
            {% endfor %}
        </table>
    </section>

    {% if not is_web %}
        <section id="how-to-use">
            <h2>How To Use</h2>
            <ul>
                <li><a href="{{ s3_prod }}/{{ iso_date }}/index.html#litigation-by-firm-report" target="_blank"
                       style="color: inherit; text-decoration: underline;">Litigation by Tort Report</a>:
                    View the latest filings aggregated by tort and law firm.
                </li>
                <li><a href="{{ s3_prod }}/{{ iso_date }}/index.html#detailed-filings-report" target="_blank"
                       style="color: inherit; text-decoration: underline;">Detailed Filings Report</a>:
                    Explore & view the latest case filings by law firm.
                </li>
                <li><a href="{{ s3_prod }}/{{ iso_date }}/index.html#adspy-report" target="_blank"
                       style="color: inherit; text-decoration: underline;">AdSpy Report</a>: Check out the new FB
                    legal
                    marketing ads launched, along with ad creative and landing page.
                </li>
                <li><a href="{{ s3_prod }}/{{ iso_date }}/index.html" target="_blank"
                       style="color: inherit; text-decoration: underline;">Top 10 Most Active Torts</a>: Check out the
                    Top 10 Most Active Torts by new filings over the past 30 days. Our new reporting system is coming
                    soon...
                </li>
            </ul>
        </section>
        <section class="email-specific">
            <div class="interactive-report">
                <p class="cool-button-text">Having problems viewing this email?</p>
                <a href="{{ s3_prod }}/{{ iso_date }}/index.html" class="cool-button">View Interactive Report</a>
            </div>
        </section>
    {% endif %}

    <section id="litigation-by-firm-report">
        <h2>Litigation by Tort Report</h2>
        {{ sponsorships.sponsorship_1|safe }}
        {% if grouped %}
            {% for title, sub_df in grouped.items() %}
                {{ render_litigation_table(title, get_allegations_and_causes(docket_df, title, docket_df[docket_df.title == title].mdl_num.iloc[0]), sub_df, is_web) }}
            {% endfor %}
            {% if not is_web and truncate_limits.get('litigation_by_tort', 0) > 0 and truncate_limits.get('litigation_by_tort', 0) < title_totals.shape[0] %}
                <div class="see-all-button-container">
                    <a href="{{ s3_prod }}/{{ iso_date }}/index.html#litigation-by-firm-report" class="see-all-button">
                        Unlock Complete Mass Tort Insights
                    </a>
                </div>
            {% endif %}
        {% else %}
            <p>No litigation data available for this report.</p>
        {% endif %}
    </section>

    <section id="detailed-filings-report">
        {{ render_detailed_filings(docket_df, is_web) }}
        {% if not is_web and truncate_limits.get('detailed_filings', 0) > 0 and truncate_limits.get('detailed_filings', 0) < docket_df['law_firm'].nunique() %}
            <div class="see-all-button-container">
                <a href="{{ s3_prod }}/{{ iso_date }}/index.html#detailed-filings-report" class="see-all-button">
                    Access Complete Filings Analysis
                </a>
            </div>
        {% endif %}
    </section>

    <section id="adspy-report">
        <h2>AdSpy Report</h2>
        {{ sponsorships.sponsorship_3|safe }}
        {{ render_ad_report(grouped_ads, is_web, s3_prod, iso_date) }}
        {% if not is_web and truncate_limits.get('adspy', 0) > 0 and truncate_limits.get('adspy', 0) < grouped_ads|length %}
            <div class="see-all-button-container">
                <a href="{{ s3_prod }}/{{ iso_date }}/index.html#adspy-report" class="see-all-button">
                    Reveal All Legal Marketing Strategies
                </a>
            </div>
        {% endif %}
    </section>

    <footer class="footer">
        <p>LexGenius, 20 Eastgate Drive, Unit D, Boynton Beach, FL 33436</p>
        <p>If you don't want to receive these reports, you can <a href="https://www.gmass.co/gmass/u?u=OUTBOUND">unsubscribe
            here</a>.</p>
        <p>© 2024 LexGenius, All Rights Reserved.</p>
    </footer>
</div>
{% if is_web %}
    <button id="back-to-top" title="Back to Top">↑</button>
    <script>
        // Chart.js implementation for tort filings visualization
        const ctx = document.getElementById('tort-chart').getContext('2d');

        // Create a function to generate a gradient color for the bars
        const createGradient = (ctx, chartArea) => {
            const gradient = ctx.createLinearGradient(0, chartArea.bottom, 0, chartArea.top);
            gradient.addColorStop(0, 'rgba(54, 162, 235, 0.5)');  // Blue for lowest value
            gradient.addColorStop(1, 'rgba(255, 99, 132, 0.5)');  // Red for highest value
            return gradient;
        };

        // Sort the data in descending order
        const sortedData = {{ chart_data|tojson }}.map((value, index) => ({
            value,
            label: {{ chart_labels|tojson }}[index]
        })).sort((a, b) => b.value - a.value);

        const sortedLabels = sortedData.map(item => item.label);
        const sortedValues = sortedData.map(item => item.value);

        // Function to determine if the device is mobile
        function isMobile() {
            return window.innerWidth <= 768;
        }

        // Function to get appropriate font size based on screen width
        function getFontSize() {
            return isMobile() ? 10 : 12;
        }

        new Chart(ctx, {
            type: 'bar',
            data: {
                labels: sortedLabels,
                datasets: [{
                    data: sortedValues,
                    borderColor: 'rgba(54, 162, 235, 1)',
                    borderWidth: 0,
                    backgroundColor: function (context) {
                        const chart = context.chart;
                        const {ctx, chartArea} = chart;

                        if (!chartArea) {
                            return null;
                        }

                        return createGradient(ctx, chartArea);
                    }
                }]
            },
            options: {
                indexAxis: 'y',
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: 'Top 10 Most Active Torts (30 days)',
                        font: {
                            size: isMobile() ? 18 : 24,
                            weight: 'bold'
                        },
                        padding: {
                            top: isMobile() ? 10 : 20,
                            bottom: isMobile() ? 5 : 10
                        },
                        color: '#333'
                    },
                    legend: {
                        display: false
                    },
                    datalabels: {
                        color: '#333',
                        anchor: 'end',
                        align: 'right',
                        offset: isMobile() ? 0 : 5,
                        font: {
                            size: getFontSize(),
                            weight: 'bold'
                        },
                        formatter: function (value) {
                            return isMobile() && value < 10 ? '' : value;
                        }
                    },
                    subtitle: {
                        display: true,
                        text: '** New filings. Includes pending CTOs & removals.',
                        font: {
                            size: isMobile() ? 10 : 14,
                            style: 'italic'
                        },
                        padding: {
                            top: 0,
                            bottom: isMobile() ? 5 : 10
                        },
                        color: '#666'
                    },
                    tooltip: {
                        enabled: true,
                        mode: 'nearest',
                        intersect: false,
                        callbacks: {
                            label: function (context) {
                                return `Filings: ${context.parsed.x}`;
                            }
                        }
                    },
                },
                scales: {
                    x: {
                        type: 'logarithmic',
                        ticks: {
                            display: !isMobile(),
                            font: {
                                size: getFontSize()
                            }
                        },
                        grid: {
                            drawTicks: false,
                            drawBorder: false
                        },
                        title: {
                            display: !isMobile(),
                            text: 'Number of Filings (log-scale)',
                            color: '#6D78AD',
                            font: {
                                size: isMobile() ? 12 : 16,
                                weight: 'bold'
                            },
                            padding: isMobile() ? 5 : 10
                        }
                    },
                    y: {
                        ticks: {
                            display: true,
                            font: {
                                size: getFontSize()
                            },
                            callback: function (value, index, values) {
                                const label = this.getLabelForValue(value);
                                if (label.includes('Lejeune')) {
                                    return 'CLJ';
                                }
                                return isMobile() ? label.substring(0, 4) : label;
                            }
                        },
                        grid: {
                            drawOnChartArea: false,
                            drawTicks: false,
                            drawBorder: false
                        },
                        title: {
                            display: !isMobile(),
                            text: 'MDL No.',
                            color: '#6D78AD',
                            font: {
                                size: isMobile() ? 12 : 16,
                                weight: 'bold'
                            },
                            padding: 0
                        }
                    }
                },
                layout: {
                    padding: {
                        left: isMobile() ? 0 : 10,
                        right: isMobile() ? 0 : 50,
                        top: isMobile() ? 0 : 10,
                        bottom: isMobile() ? 0 : 10
                    }
                }
            },
            plugins: [ChartDataLabels]
        });

        // Add responsive styles
        const style = document.createElement('style');
        style.textContent = `
    @media (max-width: 768px) {
        .chart-container {
            height: 100vh !important;
            width: 100% !important;
            margin: 0 !important;
            padding: 0 !important;
        }
    }
`;
        document.head.appendChild(style);
    </script>
    <script>
        document.addEventListener("DOMContentLoaded", function () {
            console.log("DOM fully loaded and parsed");
            document.querySelectorAll('.title-row').forEach(row => {
                row.addEventListener('click', function () {
                    const id = this.getAttribute('data-target');
                    toggleVisibility(id);
                });
            });

            let searchResults = [];
            let currentResultIndex = -1;

            function performSearch() {
                console.log("Performing search");
                const searchTerm = document.getElementById('search-input').value.toLowerCase();
                console.log("Search term:", searchTerm);
                searchResults = [];
                currentResultIndex = -1;

                if (searchTerm.length < 2) {
                    console.log("Search term too short, aborting search");
                    document.getElementById('search-info').textContent = 'Enter at least 2 characters to search.';
                    return;
                }

                // Remove existing highlights
                document.querySelectorAll('.search-highlight').forEach((el) => {
                    el.outerHTML = el.innerHTML;
                });

                const container = document.querySelector('.container');
                searchInElement(container, searchTerm);

                console.log("Search results found:", searchResults.length);

                if (searchResults.length > 0) {
                    currentResultIndex = -1;
                    highlightNextResult();
                } else {
                    document.getElementById('search-info').textContent = 'No results found.';
                }
            }

            function highlightNextResult() {
                if (searchResults.length === 0) return;

                currentResultIndex++;
                if (currentResultIndex >= searchResults.length) {
                    currentResultIndex = 0;
                }

                const element = searchResults[currentResultIndex];
                element.scrollIntoView({behavior: 'smooth', block: 'center'});

                document.getElementById('search-info').textContent = `Result ${currentResultIndex + 1} of ${searchResults.length}`;
                console.log(`Highlighted result ${currentResultIndex + 1} of ${searchResults.length}`);
            }

            function searchInElement(element, searchTerm) {
                if (element.childNodes.length > 0) {
                    for (let i = 0; i < element.childNodes.length; i++) {
                        const node = element.childNodes[i];
                        if (node.nodeType === Node.TEXT_NODE) {
                            const text = node.textContent.toLowerCase();
                            let index = text.indexOf(searchTerm);
                            while (index >= 0) {
                                console.log("Found match in:", text);
                                const spanNode = document.createElement('span');
                                spanNode.innerHTML = node.textContent.slice(0, index) +
                                    '<span class="search-highlight">' + node.textContent.slice(index, index + searchTerm.length) + '</span>' +
                                    node.textContent.slice(index + searchTerm.length);
                                node.parentNode.replaceChild(spanNode, node);
                                searchResults.push(spanNode.querySelector('.search-highlight'));
                                index = text.indexOf(searchTerm, index + 1);
                            }
                        } else if (node.nodeType === Node.ELEMENT_NODE) {
                            searchInElement(node, searchTerm);
                        }
                    }
                }
            }

            // Back to Top button functionality
            const backToTopButton = document.getElementById("back-to-top");
            const sections = ['summary', 'upcoming-hearings', 'litigation-by-firm-report', 'detailed-filings-report', 'adspy-report'];
            let currentSectionIndex = 0;

            window.onscroll = function () {
                if (document.body.scrollTop > 20 || document.documentElement.scrollTop > 20) {
                    backToTopButton.style.display = "block";
                } else {
                    backToTopButton.style.display = "none";
                }

                // Update current section index
                for (let i = sections.length - 1; i >= 0; i--) {
                    const section = document.getElementById(sections[i]);
                    if (section.getBoundingClientRect().top <= 100) {
                        currentSectionIndex = i;
                        break;
                    }
                }
            };

            backToTopButton.onclick = function () {
                console.log("Back to Top button clicked");
                const currentSection = document.getElementById(sections[currentSectionIndex]);
                const currentSectionTop = currentSection.getBoundingClientRect().top;

                if (currentSectionTop < -10) {
                    // If not at the top of the current section, scroll to its top
                    smoothScroll(sections[currentSectionIndex]);
                } else if (currentSectionIndex > 0) {
                    // If at the top of the current section and not the first section, go to previous section
                    currentSectionIndex--;
                    smoothScroll(sections[currentSectionIndex]);
                } else {
                    // If at the top of the first section, scroll to the very top
                    window.scrollTo({top: 0, behavior: 'smooth'});
                }
            };

            const HEADER_HEIGHT = 100; // Adjust this value based on your actual header height
            const EXTRA_OFFSET = 140; // Additional offset for better visibility

            function getScrollOffset() {
                const viewportHeight = window.innerHeight;
                const baseOffset = HEADER_HEIGHT + EXTRA_OFFSET;
                const additionalOffset = Math.max(0, (768 - viewportHeight) / 10);
                return baseOffset + additionalOffset;
            }

            function handleLinkClick(event) {
                event.preventDefault();
                const targetId = event.currentTarget.getAttribute('href').substring(1);
                console.log("Clicked link, target ID:", targetId);
                const targetElement = document.getElementById(targetId);
                if (targetElement) {
                    const offset = getScrollOffset();
                    const elementPosition = targetElement.getBoundingClientRect().top + window.pageYOffset;
                    const scrollPosition = elementPosition - offset;
                    window.scrollTo({
                        top: scrollPosition,
                        behavior: 'smooth'
                    });
                    console.log("Scrolled to target:", targetId, "Scroll position:", scrollPosition);

                    // Highlight the scrolled-to element temporarily
                    targetElement.style.transition = 'background-color 0.5s';
                    targetElement.style.backgroundColor = 'light-green';
                    setTimeout(() => {
                        targetElement.style.backgroundColor = '';
                    }, 1500);
                } else {
                    console.error("Target element not found:", targetId);
                }
            }

            // Update smoothScroll function to use the new getScrollOffset
            function smoothScroll(targetId) {
                console.log("Smooth scrolling to:", targetId);
                const target = document.getElementById(targetId);
                if (target) {
                    const offset = getScrollOffset();
                    const targetPosition = target.getBoundingClientRect().top + window.pageYOffset;
                    window.scrollTo({
                        top: targetPosition - offset,
                        behavior: 'smooth'
                    });
                } else {
                    console.error("Target element not found:", targetId);
                }
            }

            // Add event listeners for internal links
            document.querySelectorAll('a[href^="#"]').forEach(link => {
                link.addEventListener('click', handleLinkClick);
            });
            console.log("Internal link listeners added");

            // Lazy loading for images
            var lazyImages = [].slice.call(document.querySelectorAll("img.lazy"));

            if ("IntersectionObserver" in window) {
                let lazyImageObserver = new IntersectionObserver(function (entries, observer) {
                    entries.forEach(function (entry) {
                        if (entry.isIntersecting) {
                            let lazyImage = entry.target;
                            lazyImage.src = lazyImage.dataset.src;
                            lazyImage.classList.remove("lazy");
                            lazyImageObserver.unobserve(lazyImage);
                            console.log("Lazy loaded image:", lazyImage.src);
                        }
                    });
                });

                lazyImages.forEach(function (lazyImage) {
                    lazyImageObserver.observe(lazyImage);
                });
                console.log("Lazy loading set up for", lazyImages.length, "images");
            } else {
                console.warn("IntersectionObserver not supported, lazy loading disabled");
            }

            document.querySelectorAll('#top-nav a').forEach(link => {
                link.addEventListener('click', function (e) {
                    e.preventDefault();
                    const targetId = this.getAttribute('href').substring(1);
                    if (targetId === 'summary') {
                        window.scrollTo({top: 0, behavior: 'smooth'});
                    } else {
                        smoothScroll(targetId);
                    }
                });
            });

            // Attach search function to form submit and input events
            const searchForm = document.getElementById('search-form');
            const searchInput = document.getElementById('search-input');

            if (searchForm) {
                searchForm.addEventListener('submit', function (e) {
                    e.preventDefault();
                    performSearch();
                });
                console.log("Search form submit listener attached");
            } else {
                console.error("Search form not found");
            }

            if (searchInput) {
                searchInput.addEventListener('input', function () {
                    if (this.value.length >= 2) {
                        performSearch();
                    } else {
                        document.getElementById('search-info').textContent = 'Enter at least 2 characters to search.';
                    }
                });
                console.log("Search input listener attached");
            } else {
                console.error("Search input not found");
            }

            // Add event listener for the "Next" button
            const nextButton = document.querySelector('button[onclick="highlightNextResult()"]');
            if (nextButton) {
                nextButton.addEventListener('click', highlightNextResult);
                console.log("Next button listener attached");
            } else {
                console.error("Next button not found");
            }
            console.log("Search event listeners attached");

            console.log("All scripts loaded and initialized");
        });

        function toggleVisibility(id) {
            const element = document.getElementById(id);
            const indicator = document.getElementById(id + '_indicator');
            if (element.style.display === 'none' || element.style.display === '') {
                element.style.display = 'table-row';
                if (indicator) indicator.textContent = '-';
            } else {
                element.style.display = 'none';
                if (indicator) indicator.textContent = '+';
            }
        }
    </script>
{% endif %}
</body>
</html>

{#Add back to options#}
{#onClick: (event, elements) => {#}
{#            if (elements.length > 0 && !isMobile()) {#}
{#                const index = elements[0].index;#}
{#                const clickedLabel = sortedLabels[index];#}
{#                const clickedValue = sortedValues[index];#}
{#                alert(`Additional info for ${clickedLabel}: ${clickedValue} filings`);#}
{#            }#}
{#        },#}