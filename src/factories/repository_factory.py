"""Factory for creating repository instances with proper dependencies."""
from typing import Optional
from src.infrastructure.storage.dynamodb_async import AsyncDynamoDBStorage
from src.infrastructure.storage.dynamodb_base import DynamoDBBaseStorage as DynamoDBStorage
from src.repositories.pacer_dockets_repository import PacerDocketsRepository
from src.repositories.district_courts_repository import DistrictCourtsRepository
from src.repositories.law_firms_repository import LawFirmsRepository
from src.repositories.fb_archive_repository import FBArchiveRepository
from src.repositories.pacer_repository import PacerRepository


class RepositoryFactory:
    """Factory for creating repository instances."""
    
    def __init__(self, storage: Optional[DynamoDBStorage] = None):
        """Initialize factory with storage backend.
        
        Args:
            storage: DynamoDB storage instance. If None, creates default.
        """
        self.storage = storage or DynamoDBStorage()
    
    def create_pacer_repository(self) -> PacerRepository:
        """Create PACER repository instance."""
        return PacerRepository(self.storage)
    
    def create_pacer_dockets_repository(self) -> PacerDocketsRepository:
        """Create PACER dockets repository instance."""
        return PacerDocketsRepository(self.storage)
    
    def create_district_courts_repository(self) -> DistrictCourtsRepository:
        """Create district courts repository instance."""
        return DistrictCourtsRepository(self.storage)
    
    def create_law_firms_repository(self) -> LawFirmsRepository:
        """Create law firms repository instance."""
        return LawFirmsRepository(self.storage)
    
    def create_fb_archive_repository(self) -> FBArchiveRepository:
        """Create FB archive repository instance."""
        return FBArchiveRepository(self.storage)


class AsyncRepositoryFactory:
    """Factory for creating async repository instances."""
    
    def __init__(self, storage: Optional[AsyncDynamoDBStorage] = None):
        """Initialize factory with async storage backend.
        
        Args:
            storage: Async DynamoDB storage instance. If None, creates default.
        """
        self.storage = storage or AsyncDynamoDBStorage()
    
    async def create_pacer_repository(self) -> PacerRepository:
        """Create async PACER repository instance."""
        return PacerRepository(self.storage)
    
    async def create_pacer_dockets_repository(self) -> PacerDocketsRepository:
        """Create async PACER dockets repository instance."""
        return PacerDocketsRepository(self.storage)
    
    async def create_district_courts_repository(self) -> DistrictCourtsRepository:
        """Create async district courts repository instance."""
        return DistrictCourtsRepository(self.storage)
    
    async def create_law_firms_repository(self) -> LawFirmsRepository:
        """Create async law firms repository instance."""
        return LawFirmsRepository(self.storage)
    
    async def create_fb_archive_repository(self) -> FBArchiveRepository:
        """Create async FB archive repository instance."""
        return FBArchiveRepository(self.storage)