import re
import traceback
from datetime import datetime
from pprint import pprint
from typing import Dict, Any, Optional

import requests
from bs4 import BeautifulSoup


class HTMLCaseParser:
    """Parser for HTML content from PACER."""

    def __init__(self, html_content: Optional[str] = None):
        """
        Initialize the HTMLCaseParser.
        
        Args:
            html_content: Optional HTML content string. If provided, initializes parser with content.
        """
        self.html = html_content
        self.soup = BeautifulSoup(html_content, 'html.parser') if html_content else None

    def set_html(self, html_content: str) -> None:
        """
        Set or update the HTML content to parse.
        
        Args:
            html_content: HTML content string
        """
        self.html = html_content
        self.soup = BeautifulSoup(html_content, 'html.parser')

    @staticmethod
    def strip_whitespace(text):
        return ' '.join(text.strip().split()) if text else ''

    @staticmethod
    def format_phone_number(phone):
        # Convert (XXX) XXX-XXXX to XXX-XXX-XXXX format
        phone = re.sub(r'\((\d{3})\) (\d{3})-(\d{4})', r'\1-\2-\3', phone)
        # Convert XXX/XXX-XXXX to XXX-XXX-XXXX format
        phone = re.sub(r'(\d{3})/(\d{3})-(\d{4})', r'\1-\2-\3', phone)
        return phone

    @staticmethod
    def _normalize_docket_to_13_chars(docket_num: str) -> str:
        """Normalize docket number to exactly 13 characters: N:NN-XX-NNNNN"""
        if not docket_num or ':' not in docket_num:
            return docket_num
            
        try:
            prefix = docket_num.split(':')[0]  # N
            suffix_parts = docket_num.split(':')[1].split('-')
            
            if len(suffix_parts) >= 3:
                year = suffix_parts[0]  # YY
                case_type = suffix_parts[1]  # XX (cv, sf, etc.)
                docket_part = suffix_parts[2]  # NNNNN-...
                
                # Extract and zero-pad to exactly 5 digits
                num_match = re.search(r'(\d{1,5})', docket_part)
                if num_match:
                    digits = num_match.group(1)
                    # Zero-pad to 5 digits
                    five_digits = digits.zfill(5)
                    return f"{prefix}:{year}-{case_type}-{five_digits}"
            
            return docket_num  # Fallback for unexpected formats
        except Exception:
            return docket_num  # Fallback for any parsing errors

    def process_attorney_parts(self, input_parts: list[str], previous_attorneys: list[dict]) -> Dict[str, Any]:
        """Processes attorney information parts and returns a dictionary.

        Args:
            input_parts: A list of strings containing attorney information.
                         These strings are expected to be pre-stripped and have internal whitespace normalized.
            previous_attorneys: A list of previously processed attorney dictionaries.

        Returns:
            A dictionary containing attorney information.
        """
        # Initialize with default values for a consistent return structure.
        attorney_record = {
            'attorney_name': '',
            'law_firm': '',
            'phone': '',
            'fax': '',
            'email': '',
            'address1': '',
            'address2': '',
            'city': '',
            'state': '',
            'zip_code': '',
            'lead_attorney': False,
            'pro_hac_vice': False,
            'attorney_to_be_noticed': False
        }

        # Handle empty input_parts list as per original behavior (return default structure, no append).
        if not input_parts:
            # No need to append to previous_attorneys here if input_parts is empty,
            # as the original code didn't append in this specific path.
            # If appending an empty record is desired for some reason, it would be:
            # previous_attorneys.append(attorney_record.copy())
            return attorney_record

        try:
            # Filter out parts containing "COUNSEL NOT..." (case-insensitive).
            # Assumes elements in input_parts are already stripped and whitespace-normalized by the caller.
            current_processing_list = [p for p in input_parts if 'COUNSEL NOT' not in p.upper()]

            if not current_processing_list:
                # If filtering results in an empty list (e.g., input_parts only contained "COUNSEL NOT...").
                if input_parts and input_parts[0]:  # Ensure input_parts[0] is not an empty string
                    attorney_record['attorney_name'] = input_parts[0]
                # Append to previous_attorneys in this fallback, then return.
                previous_attorneys.append(attorney_record.copy())
                return attorney_record

            # Step 1: Assign attorney name and law firm (based on fixed positions).
            attorney_record['attorney_name'] = current_processing_list[0]
            if len(current_processing_list) > 1:
                attorney_record['law_firm'] = current_processing_list[1]

            # Determine the starting index for parsing detailed information (flags, contact, address).
            details_start_index = 2 if len(current_processing_list) > 1 else 1
            parts_for_details_scan = current_processing_list[details_start_index:]

            # Step 2: Extract flags (lead_attorney, pro_hac_vice, attorney_to_be_noticed).
            parts_after_flags = []
            for text_part in parts_for_details_scan:
                text_part_lower = text_part.lower()
                if 'lead attorney' in text_part_lower:
                    attorney_record['lead_attorney'] = True
                elif 'pro hac vice' in text_part_lower:
                    attorney_record['pro_hac_vice'] = True
                elif 'attorney to be noticed' in text_part_lower:
                    attorney_record['attorney_to_be_noticed'] = True
                else:
                    parts_after_flags.append(text_part)

            # Step 3: Extract email, phone, and fax from parts_after_flags.
            address_candidate_parts = []
            for text_part in parts_after_flags:
                if text_part.startswith('Email:'):
                    attorney_record['email'] = text_part.split('Email:', 1)[-1].strip()
                elif re.search(r'\d{3}-\d{3}-\d{4}|\(\d{3}\)\s*\d{3}-\d{4}', text_part):
                    formatted_number = self.format_phone_number(text_part)
                    if not attorney_record['phone']:
                        attorney_record['phone'] = formatted_number
                    else:
                        attorney_record['fax'] = formatted_number
                else:
                    address_candidate_parts.append(text_part)

            # Step 4: Process address lines from address_candidate_parts.
            _current_address1 = ''
            _current_address2 = ''
            for addr_text_part in address_candidate_parts:
                if re.search(r'[A-Z]{2}\s+\d{5}', addr_text_part):
                    city_str_part, *state_zip_str_list = addr_text_part.split(',', 1)
                    attorney_record['city'] = city_str_part.strip()
                    if state_zip_str_list:
                        state_zip_full_text = state_zip_str_list[0]
                        state_zip_match_obj = re.search(r'([A-Z]{2})\s*(\d{5}(?:-\d{4})?)', state_zip_full_text)
                        if state_zip_match_obj:
                            attorney_record['state'] = state_zip_match_obj.group(1)
                            attorney_record['zip_code'] = state_zip_match_obj.group(2)
                    _current_address2 = ''  # Reset address2 when city/state/zip found
                elif not _current_address1:
                    _current_address1 = addr_text_part
                else:
                    _current_address2 = addr_text_part
            attorney_record['address1'] = _current_address1
            attorney_record['address2'] = _current_address2

            # Step 5: Handle "(See above for address)"
            # This logic attempts to fill in details if a previous record for the same attorney exists.
            if attorney_record.get('law_firm') == '(See above for address)':
                for prev_att in reversed(previous_attorneys):  # Search most recent first
                    if prev_att.get('attorney_name') == attorney_record.get('attorney_name'):
                        # Found a previous record for the same attorney.
                        # If this previous record has a valid law firm, use its details.
                        if prev_att.get('law_firm') and prev_att['law_firm'] != '(See above for address)':
                            attorney_record['law_firm'] = prev_att['law_firm']
                            # If law firm is being copied, other details should also be copied if present in prev_att
                            # and not already set in current record from its specific "(See above...)" block.
                            # This assumes "(See above for address)" implies all address fields are from "above".
                            if prev_att.get('address1'): attorney_record['address1'] = prev_att['address1']
                            if prev_att.get('address2'): attorney_record['address2'] = prev_att['address2']
                            if prev_att.get('city'): attorney_record['city'] = prev_att['city']
                            if prev_att.get('state'): attorney_record['state'] = prev_att['state']
                            if prev_att.get('zip_code'): attorney_record['zip_code'] = prev_att['zip_code']
                            if prev_att.get('phone'): attorney_record['phone'] = prev_att['phone']
                            if prev_att.get('fax'): attorney_record['fax'] = prev_att['fax']
                            if prev_att.get('email'): attorney_record['email'] = prev_att['email']
                            # Once a suitable previous record is found and used, break.
                            break

            # Final cleanup for 'fax' field, removing "Fax:" prefix if present.
            if attorney_record['fax']:
                attorney_record['fax'] = attorney_record['fax'].replace('Fax:', '').strip()

            previous_attorneys.append(attorney_record.copy())
            return attorney_record

        except Exception as e:
            print(f"Error processing attorney information: {e}")
            print(f"Traceback: {traceback.format_exc()}")

            fallback_record = {
                'attorney_name': input_parts[0] if input_parts and input_parts[0] else '',
                'law_firm': '', 'phone': '', 'fax': '', 'email': '',
                'address1': '', 'address2': '', 'city': '', 'state': '', 'zip_code': '',
                'lead_attorney': False, 'pro_hac_vice': False, 'attorney_to_be_noticed': False
            }
            previous_attorneys.append(fallback_record.copy())
            return fallback_record

    def parse(self) -> Dict:
        """Parses the HTML content and returns a dictionary containing case information."""
        if not self.soup:
            return {
                'case_info': {},
                'plaintiffs': [],
                'defendants': []
            }

        case_info = {}
        plaintiffs = []
        defendants = []
        previous_attorneys = []
        case_details = []

        main_content = self.soup.find('div', id='cmecfMainContent')

        if not main_content:
            return {
                'case_info': {},
                'plaintiffs': [],
                'defendants': []
            }

        case_info['flags'] = []
        first_table = main_content.find('table')
        if first_table and first_table.find('td', align='right'):
            spans = first_table.find_all('span')
            case_info['flags'] = [self.strip_whitespace(span.text) for span in spans]

        h3_element = main_content.find('h3', align='center')
        if h3_element:
            h3_text = h3_element.decode_contents().replace('<br>', '').split('<br/>\n')
            if 'U.S. District Court' in h3_text[0] or 'United States District Court' in h3_text[0]:
                court_and_office = h3_text[1].split('(')
                case_info['court_name'] = self.strip_whitespace(court_and_office[0])
                case_info['office'] = self.strip_whitespace(court_and_office[1].strip(')')) if len(
                    court_and_office) > 1 else ''
                if 'CIVIL DOCKET FOR CASE #' in h3_text[2]:
                    # Extract full docket number first
                    full_docket = self.strip_whitespace(h3_text[2].split(':', 1)[-1])
                    # Normalize to exactly 13 characters: N:NN-XX-NNNNN
                    case_info['docket_num'] = self._normalize_docket_to_13_chars(full_docket)

        case_table = h3_element.find_next('table')
        if case_table:
            rows = case_table.find_all('td', valign='top')
            for row in rows:
                case_details.extend(row.text.split('\n'))
        case_info['versus'] = case_details[0]
        case_details.pop(0)

        if len(case_details) >= 2:
            for detail in case_details:
                detail = detail.replace('\u00A0', ' ')
                if 'Assigned to:' in detail:
                    case_info['assigned_to'] = self.strip_whitespace(detail.split(':')[1])
                elif 'Referred to:' in detail:
                    case_info['referred_to'] = self.strip_whitespace(detail.split(':')[1])
                elif 'Case in other court:' in detail:
                    case_in_other_court = self.strip_whitespace(detail.split(':', 1)[1])
                    if len(case_in_other_court) != 0:
                        case_info['case_in_other_court'] = self.strip_whitespace(detail.split(':', 1)[1])
                elif 'Date Filed:' in detail:
                    case_info['date_filed'] = self.strip_whitespace(detail.split(':')[1])
                elif 'Jury Demand:' in detail:
                    case_info['jury_demand'] = self.strip_whitespace(detail.split(':')[1])
                elif 'Nature of Suit:' in detail:
                    case_info['nos'] = self.strip_whitespace(detail.split(':')[1])
                elif 'Jurisdiction:' in detail:
                    case_info['jurisdiction'] = self.strip_whitespace(detail.split(':')[1])
                elif 'Demand:' in detail:
                    case_info['demand'] = self.strip_whitespace(detail.split(':')[1])
                elif 'Lead case:' in detail:
                    case_info['lead_case'] = detail.split(':', 1)[1].strip() if ':' in detail else None
                elif 'Cause:' in detail:
                    case_info['cause'] = detail.split(':', 1)[1].strip() if ':' in detail else None

        attorney_and_parties_table = case_table.find_next('table')
        while attorney_and_parties_table:
            first_row = attorney_and_parties_table.find('tr')
            while first_row:
                row_text = first_row.text
                if 'Plaintiff' in row_text:
                    first_row = first_row.find_next('tr')
                    if first_row:
                        if len(first_row.find_all('td')) == 0:
                            first_row = first_row.find_next('tr')
                            continue
                        tds = first_row.find_all('td')
                        if len(tds) < 3:
                            first_row = first_row.find_next('tr')
                            continue
                        plaintiff_td, _, attorney_td = tds
                        if not plaintiff_td.find('b'):
                            first_row = first_row.find_next('tr')
                            continue
                        plaintiff = {'name': self.strip_whitespace(plaintiff_td.find('b').text), 'attorneys': []}
                        attorneys = attorney_td.text.split('\n')
                        attorneys = [re.sub(r'\s+', ' ', item.strip()) for item in attorneys if
                                     item.strip() and not item.startswith('United Sta')]

                        attorney_info = []
                        try:
                            for attorney in attorneys:
                                if attorney == 'ATTORNEY TO BE NOTICED':
                                    if attorney_info:
                                        try:
                                            attorney_record = self.process_attorney_parts(attorney_info,
                                                                                          previous_attorneys)
                                            plaintiff['attorneys'].append(attorney_record)
                                        except Exception as e:
                                            print(f"Error processing attorney info: {str(e)}")
                                            print(f"Attorney info that caused error: {attorney_info}")
                                    attorney_info = []
                                else:
                                    attorney_info.append(attorney)

                            # Handle any remaining attorney info after the loop
                            if attorney_info:
                                try:
                                    attorney_record = self.process_attorney_parts(attorney_info, previous_attorneys)
                                    plaintiff['attorneys'].append(attorney_record)
                                except Exception as e:
                                    print(f"Error processing final attorney info: {str(e)}")
                                    print(f"Final attorney info that caused error: {attorney_info}")

                        except Exception as e:
                            print(f"Error in attorney processing loop: {str(e)}")
                            print(f"Attorneys list that caused error: {attorneys}")
                        plaintiffs.append(plaintiff)

                elif 'Defendant' in row_text:
                    first_row = first_row.find_next('tr')
                    while first_row:
                        row_text = first_row.text.strip()
                        if 'Defendant' in row_text:
                            first_row = first_row.find_next('tr')
                            continue
                        if not row_text:
                            first_row = first_row.find_next('tr')
                            continue
                        defendant_td = first_row.find('td')
                        if defendant_td and defendant_td.find('b') and defendant_td.find('b').text.strip():
                            defendants.append(self.strip_whitespace(defendant_td.find('b').text))
                        first_row = first_row.find_next('tr')

                first_row = first_row.find_next('tr') if first_row else None

            attorney_and_parties_table = attorney_and_parties_table.find_next('table')

        # Find and extract 'Case in other court' using more specific logic
        case_in_other_court_table = self.soup.find('td', string=re.compile(r'Case\s+in\s+other\s+court:'))
        if case_in_other_court_table:
            next_td = case_in_other_court_table.find_next('td')
            if next_td:
                case_info['case_in_other_court'] = self.strip_whitespace(next_td.text)

        result = {
            'case_info': case_info,
            'plaintiffs': plaintiffs,
            'defendants': defendants
        }
        return result

    def is_removal(self):
        """Checks if the case is a removal case based on the docket text."""

        # Try to find the table with the most specific attributes first
        table = self.soup.find('table', attrs={'align': 'center', 'width': '99%', 'border': '1'})
        if not table:
            table = self.soup.find('table', attrs={'align': 'center'})
            if not table:
                table = self.soup.find('table')
                if not table:
                    return False

        # Find all rows in the table
        rows = table.find_all('tr')

        # Try to find the header row
        header_row = None
        for row in rows:
            if 'Date Filed' in row.get_text() and 'Docket Text' in row.get_text():
                header_row = row
                break

        if not header_row:
            return False

        # Find the first row after the header row
        content_row = header_row.find_next_sibling('tr')
        if not content_row:
            return False

        # Extract date and docket text
        cells = content_row.find_all('td')
        if len(cells) < 3:
            print(f"Not enough cells in content row. Found {len(cells)} cells.")
            return False

        date = cells[0].text.strip()
        docket_text = cells[2].text.strip()

        removal_pattern = r'^(?:notice\s+of\s+removal|notice\s+removal|petition\s+for\s+removal)'
        if re.search(removal_pattern, docket_text, re.IGNORECASE):
            try:
                parsed_date = datetime.strptime(date, '%m/%d/%Y')
                result = {'removal_date': parsed_date.strftime('%m/%d/%Y')}
                return result
            except ValueError:
                print(f"Could not parse date: {date}")

        return False

    def get_initial_filing_date(self):
        """Gets the initial filing date of the case from the docket text."""

        # Try to find the table with the most specific attributes first
        table = self.soup.find('table', attrs={'align': 'center', 'width': '99%', 'border': '1'})
        if not table:
            table = self.soup.find('table', attrs={'align': 'center'})
            if not table:
                table = self.soup.find('table')
                if not table:
                    return False

        # Find all rows in the table
        rows = table.find_all('tr')

        # Try to find the header row
        header_row = None
        for row in rows:
            if 'Date Filed' in row.get_text() and 'Docket Text' in row.get_text():
                header_row = row
                break

        if not header_row:
            return False

        # Find the first row after the header row
        content_row = header_row.find_next_sibling('tr')
        if not content_row:
            return False

        # Extract date and docket text
        cells = content_row.find_all('td')
        if len(cells) < 3:
            print(f"Not enough cells in content row. Found {len(cells)} cells.")
            return False

        date = cells[0].text.strip()
        docket_text = cells[2].text.strip()

        try:
            parsed_date = datetime.strptime(date, '%m/%d/%Y')
            result = {'initial_filing_date': parsed_date.strftime('%m/%d/%Y')}
            return result
        except ValueError:
            print(f"Could not parse date: {date}")

        return False

    def process_url(self, url: str):
        """Fetches HTML from a URL, parses it, and prints the results."""
        try:
            # Fetch the HTML from the URL
            response = requests.get(url)
            response.raise_for_status()  # Raise an error if the request failed

            # Parse the HTML content
            self.html = response.text
            self.soup = BeautifulSoup(self.html, 'html.parser')

            # Parse and display the results
            parsed_result = self.parse()
            pprint(parsed_result)

        except requests.RequestException as e:
            print(f"Error fetching the URL: {e}")
        except Exception as e:
            print(f"An error occurred while processing the URL: {traceback.format_exc()}")

    def parse_html(self, html_content: str) -> Dict:
        """
        Parse HTML content and return case information.
        
        Args:
            html_content: HTML content string to parse
        
        Returns:
            Dict containing parsed case information
        """
        self.html = html_content
        self.soup = BeautifulSoup(html_content, 'html.parser')
        return self.parse()
