import json
import logging
import os
from time import sleep
from typing import Dict, Optional, TYPE_CHECKING

import requests

from src.core.html.html_case_parser import HTMLCaseParser

if TYPE_CHECKING:
    from src.repositories.pacer_repository import PacerRepository as Pacer<PERSON>anager
    from src.infrastructure.storage.s3_async import S3AsyncStorage as S3Manager

try:
    # Try new migration helper first
    from src.migration import create_manager_replacement
    _using_new_architecture = True
except ImportError:
    _using_new_architecture = False
    if not TYPE_CHECKING:
        from src.repositories.pacer_repository import PacerRepository as PacerManager
        from src.infrastructure.storage.s3_async import S3AsyncStorage as S3Manager


class HTMLDataUpdater:
    """Updates JSON data with information extracted from PACER HTML."""

    def __init__(self, config: Dict, s3_manager: 'S3Manager', pacer_db: 'PacerManager'):
        self.config = config
        self.logger = logging.getLogger(__name__)  # Use __name__ for logger
        self.s3_manager = s3_manager
        self.pacer_db = pacer_db
        self.html_parser = HTMLCaseParser()

    def update_with_pacer_html(self, json_path: str, data: Dict) -> None:
        """Update JSON data with PACER HTML information."""
        try:
            # Ensure 'new_filename' which is used for constructing S3 paths is present
            if 'new_filename' not in data:
                if 'original_filename' in data:
                    data['new_filename'] = data['original_filename']
                else:
                    # Fallback: derive from json_path if other fields are missing
                    base_name = os.path.basename(json_path)
                    data['new_filename'] = base_name.replace('.json', '')
                    self.logger.info(
                        f"Derived 'new_filename': {data['new_filename']} from json_path for HTML processing.")

            pdf_path = json_path.replace('.json', '.pdf')  # For logging/checking presence
            if not os.path.exists(pdf_path):
                self.logger.warning(f"No local PDF found at {pdf_path} while preparing for HTML update.")
                if not data.get('s3_link'):  # s3_link for the PDF document itself
                    self.logger.error(
                        f"No local PDF and no s3_link (for PDF) found for {json_path}. HTML processing might be less effective if it relies on PDF context not available.")
                else:
                    self.logger.info(f"No local PDF, but s3_link for PDF exists: {data.get('s3_link')}")

            # --- Determine the CDN URL for the HTML content ---
            final_cdn_url = None
            s3_html_in_data = data.get('s3_html')  # This might be a full URL, an S3 key, or absent

            if s3_html_in_data and isinstance(s3_html_in_data, str):
                if s3_html_in_data.startswith('http'):
                    final_cdn_url = s3_html_in_data
                    self.logger.info(f"Using pre-existing full HTML URL from data: {final_cdn_url}")
                elif s3_html_in_data.startswith('/'):  # Assumed to be an S3 key like /YYYYMMDD/html/file.html
                    final_cdn_url = f"https://cdn.lexgenius.ai{s3_html_in_data}"
                    self.logger.info(f"Constructed HTML URL from s3_html key in data: {final_cdn_url}")
                # else: s3_html_in_data is present but not a usable URL or key, proceed to construct/verify

            if not final_cdn_url:
                # Construct a candidate URL based on the json_path's naming convention
                candidate_cdn_url_from_json_path = self._convert_url_to_cdn_format(json_path)
                self.logger.info(
                    f"Constructed candidate HTML URL based on json_path: {candidate_cdn_url_from_json_path}")

                # Try to verify/refine this candidate URL by checking S3 objects
                url_parts = candidate_cdn_url_from_json_path.split('/')
                if len(url_parts) >= 5:  # Expect schema, host, date_dir, 'html', filename.html
                    s3_date_dir = url_parts[-3]  # e.g., YYYYMMDD
                    s3_html_dir_path = f"{s3_date_dir}/html/"  # Prefix for S3 listing

                    # The target filename we expect, based on new_filename or json_path
                    expected_html_filename = data.get('new_filename',
                                                      os.path.basename(json_path).replace('.json', '')) + ".html"

                    self.logger.info(
                        f"Attempting to verify/find HTML file on S3 with listing prefix: '{s3_html_dir_path}' and expected filename: '{expected_html_filename}'")

                    try:
                        # list_existing_files returns S3 keys relative to bucket root
                        matching_s3_keys = self.s3_manager.list_existing_files(prefix=s3_html_dir_path)
                        found_s3_key = None

                        if matching_s3_keys:
                            # Search for the expected_html_filename within the listed keys
                            for key in matching_s3_keys:
                                if os.path.basename(key) == expected_html_filename:
                                    found_s3_key = key
                                    self.logger.info(f"Found exact match on S3: {key}")
                                    break
                            if not found_s3_key and matching_s3_keys:  # If no exact match, consider close ones (e.g. case difference)
                                self.logger.warning(
                                    f"No exact filename match for '{expected_html_filename}' in S3 listing. Checking for case-insensitive or similar.")
                                # Simple case-insensitive check on basename
                                for key in matching_s3_keys:
                                    if os.path.basename(key).lower() == expected_html_filename.lower():
                                        found_s3_key = key
                                        self.logger.info(f"Found case-insensitive S3 match: {key}")
                                        break
                                if not found_s3_key:
                                    self.logger.warning(
                                        f"No exact or case-insensitive match. Will use the first found file if any: {matching_s3_keys[0]}")
                                    # found_s3_key = matching_s3_keys[0] # Or decide to use candidate_cdn_url_from_json_path

                        if found_s3_key:
                            final_cdn_url = f"https://cdn.lexgenius.ai/{found_s3_key}"
                            self.logger.info(f"Refined/Verified HTML URL via S3 list: {final_cdn_url}")
                        else:
                            self.logger.warning(
                                f"No suitable HTML file found in S3 listing for prefix '{s3_html_dir_path}'. Using originally constructed URL: {candidate_cdn_url_from_json_path}")
                            final_cdn_url = candidate_cdn_url_from_json_path
                    except Exception as s3_list_err:
                        self.logger.error(
                            f"Error listing S3 files for prefix '{s3_html_dir_path}': {s3_list_err}. Falling back to constructed URL.")
                        final_cdn_url = candidate_cdn_url_from_json_path
                else:
                    self.logger.warning(
                        f"Could not parse candidate_cdn_url_from_json_path '{candidate_cdn_url_from_json_path}' for S3 verification. Using it directly.")
                    final_cdn_url = candidate_cdn_url_from_json_path

            if not final_cdn_url:
                self.logger.error(
                    f"CRITICAL: Failed to determine a valid CDN URL for HTML processing for {os.path.basename(json_path)}. Skipping HTML update.")
                data['s3_html_error'] = "CDN URL determination failed"
                data['s3_html'] = None
                # Save data with error and return, as further steps rely on HTML
                self._save_json(json_path, data)
                return

            data['s3_html'] = final_cdn_url  # Store the finalized CDN URL

            # --- Fetch and Parse HTML ---
            try:
                case_info = self._extract_case_info_from_html(final_cdn_url)  # Uses the determined final_cdn_url
                self.logger.debug(f"Extracted case info from {final_cdn_url}: {json.dumps(case_info, indent=2)}")
            except requests.exceptions.HTTPError as e:
                if e.response.status_code == 403 or e.response.status_code == 404:
                    self.logger.warning(
                        f"CDN access forbidden or file not found for {final_cdn_url} (Status: {e.response.status_code}). Continuing without HTML data.")
                    data['s3_html_error'] = f"CDN Error {e.response.status_code}"
                    # Still save JSON, but HTML specific fields won't be populated from this attempt.
                    self._save_json(json_path, data)
                    return  # Stop further HTML processing for this file
                else:
                    self.logger.error(f"HTTPError fetching HTML from {final_cdn_url}: {e}", exc_info=True)
                    raise  # Re-raise other HTTP errors
            except Exception as extract_err:  # Catch other errors from _extract_case_info_from_html
                self.logger.error(f"Error extracting case info from HTML ({final_cdn_url}): {extract_err}",
                                  exc_info=True)
                data['s3_html_error'] = f"Extraction Error: {extract_err}"
                self._save_json(json_path, data)
                return

            # --- Update data with extracted info ---
            if case_info.get('attorney'):  # 'attorney' key is set by _extract_case_info_from_html
                data['attorney'] = case_info['attorney']
            data['lead_case'] = case_info.get('lead_case')
            data['case_in_other_court'] = case_info.get('case_in_other_court')
            if case_info.get('is_removal'): data['is_removal'] = case_info.get('is_removal')
            if case_info.get('removal_date'): data['removal_date'] = case_info.get('removal_date')
            if case_info.get('initial_filing_date'): data['date_filed'] = case_info.get(
                'initial_filing_date')  # Ensure 'date_filed' is updated

            if data.get('case_in_other_court'):
                self._process_transfer_info(data)

            # _update_json_with_pacer_html re-parses. Could be optimized to pass parsed_content.
            # For now, let it re-fetch and re-parse if it has additional logic.
            self._update_json_with_pacer_html(json_path, data)  # data['s3_html'] is now the final_cdn_url

            self._save_json(json_path, data)
            self.logger.info(
                f"Successfully updated and saved JSON with PACER HTML data for {os.path.basename(json_path)}")

        except Exception as e:
            self.logger.error(f"Error in update_with_pacer_html for {os.path.basename(json_path)}: {str(e)}",
                              exc_info=True)
            # Attempt to save error state to data if possible
            if isinstance(data, dict):
                data['html_update_error'] = str(e)
                self._save_json(json_path, data)

    @staticmethod
    def _convert_url_to_cdn_format(file_url: str) -> str:
        """Convert a file URL to CDN format."""
        parts = file_url.split('/')
        date_part = parts[-3]
        filename = parts[-1].replace('.json', '.html')
        return f"https://cdn.lexgenius.ai/{date_part}/html/{filename}"

    def _extract_case_info_from_html(self, url: str, max_retries: int = 3, retry_delay: int = 1) -> Dict:
        """Extract case information from HTML with retry mechanism."""
        for attempt in range(max_retries):
            try:
                response = requests.get(url, timeout=10)
                response.raise_for_status()
                html_content = response.text
                sleep(retry_delay)

                parser = HTMLCaseParser(html_content)  # This will still work as before
                parsed_content = parser.parse()
                case_info = parsed_content.get('case_info', {})

                # Handle empty plaintiffs list safely
                plaintiffs = parsed_content.get('plaintiffs', [])
                if plaintiffs and isinstance(plaintiffs[0], dict):
                    attorneys = plaintiffs[0].get('attorneys', [])
                else:
                    attorneys = []
                case_info['attorney'] = attorneys

                # Check if this is a removal case
                is_removal = parser.is_removal()
                if is_removal:
                    case_info['is_removal'] = True
                    case_info['removal_date'] = is_removal.get('removal_date')
                else:
                    case_info['is_removal'] = False

                # Get initial filing date
                filing_info = parser.get_initial_filing_date()
                if filing_info:
                    case_info['initial_filing_date'] = filing_info.get('initial_filing_date')

                return case_info

            except requests.exceptions.RequestException as e:
                if attempt == max_retries - 1:
                    raise
                self.logger.warning(f"Attempt {attempt + 1} failed: {str(e)}. Retrying...")
                sleep(retry_delay * (attempt + 1))
        return {'attorney': [], 'lead_case': None, 'case_in_other_court': None}

    @staticmethod
    def check_and_convert_s3_link(data: Dict) -> None:
        """Convert s3_link format to s3_html format if needed."""
        if 'S3Link' in data:
            data['s3_link'] = data['S3Link']

        if 's3_link' in data and data['s3_link']:
            s3_link = data['s3_link']
            if not s3_link.startswith('http'):
                # Add https:// prefix if missing
                s3_link = f"https://{s3_link}"

            # Extract the path portion after the domain
            path_parts = s3_link.split('/', 3)
            if len(path_parts) >= 4:
                # Store just the path part in s3_html
                data['s3_html'] = path_parts[3]
            else:
                data['s3_html'] = None
        else:
            data['s3_html'] = None

    def _process_transfer_info(self, data: Dict) -> None:
        """
        Process transfer information from case_in_other_court field.
        """
        try:
            # Split case information into court name and docket number
            case_info = data.get('case_in_other_court', '')
            if ',' in case_info:
                transferor_docket_num = case_info.split(',')[1].strip()
                transferor_court_name = case_info.split(',')[0].strip()

                data['transferor_court_docket_num'] = transferor_docket_num
                data['transferor_court'] = transferor_court_name
                self.logger.info(f"Processed transfer info: {transferor_court_name}, {transferor_docket_num}")
        except Exception as e:
            self.logger.error(f"Error processing transfer info: {str(e)}")

    def _update_json_with_pacer_html(self, json_path: str, data: Dict) -> None:
        """Update JSON data with information from PACER HTML."""
        # This method assumes data['s3_html'] contains the correct, full CDN URL.
        try:
            url = data.get('s3_html')
            if not url or not isinstance(url, str) or not url.startswith('http'):
                self.logger.warning(
                    f"Invalid or missing s3_html URL in data for {os.path.basename(json_path)}: '{url}'. Skipping detailed parse.")
                return

            self.logger.debug(f"Fetching HTML for detailed parsing from: {url}")
            response = requests.get(url, timeout=10)  # Added timeout
            response.raise_for_status()

            # Use the instance's parser. Set new HTML content for it.
            self.html_parser.set_html(response.text)
            parsed_content = self.html_parser.parse()  # Call parse() on the instance

            # Extract ONLY the names from plaintiffs, nothing else
            if parsed_content.get('plaintiffs'):
                names = []
                for p_entry in parsed_content['plaintiffs']:  # Renamed p to p_entry
                    if isinstance(p_entry, dict):
                        name = p_entry.get('name')
                        if name and isinstance(name, str):
                            names.append(name.strip())

                # Remove any duplicates while preserving order
                seen = set()
                # Ensure data['plaintiffs'] is a list of strings as per your example output.
                # The 'attorney' list is usually more complex and handled by _extract_case_info_from_html.
                data['plaintiffs'] = [x for x in names if not (x in seen or seen.add(x))]

            # Ensure 'defendant' is a list of strings
            raw_defendants = parsed_content.get('defendants')
            if isinstance(raw_defendants, list):
                # Filter out any non-string or empty string items, ensuring clean list
                data['defendant'] = [str(d).strip() for d in raw_defendants if
                                     isinstance(d, (str, int, float)) and str(d).strip()]
            elif isinstance(raw_defendants, str) and raw_defendants.strip():
                data['defendant'] = [raw_defendants.strip()]  # Wrap single defendant in a list
            else:
                data['defendant'] = []  # Default to empty list if no valid defendants

            # The 'plaintiff' field (singular) historically might have held the first plaintiff.
            # If the goal is for data['plaintiff'] to be a list of strings identical to data['plaintiffs'], then:
            if 'plaintiffs' in data and isinstance(data['plaintiffs'], list):
                data['plaintiff'] = data['plaintiffs']  # Mirror 'plaintiffs' list
            else:  # Fallback if 'plaintiffs' is not a list (e.g. if parsing changed)
                data['plaintiff'] = []

            # Update case_info fields from the detailed parse if not already set by _extract_case_info_from_html
            # This helps consolidate where fields like 'date_filed' are updated from HTML.
            html_case_info = parsed_content.get('case_info', {})
            if html_case_info:
                data.setdefault('assigned_to', html_case_info.get('assigned_to'))
                data.setdefault('referred_to', html_case_info.get('referred_to'))
                # 'date_filed' should be updated here if HTML is the primary source for it
                # Note: _extract_case_info_from_html already sets 'date_filed' if 'initial_filing_date' is found
                # This ensures it's based on the first docket entry date if available.
                # If general 'date_filed' from header is preferred, can get it here:
                # if html_case_info.get('date_filed'): data['date_filed'] = html_case_info.get('date_filed')
                data.setdefault('jury_demand', html_case_info.get('jury_demand'))
                data.setdefault('nos', html_case_info.get('nos'))  # Nature of Suit
                data.setdefault('jurisdiction', html_case_info.get('jurisdiction'))
                data.setdefault('demand', html_case_info.get('demand'))
                data.setdefault('cause', html_case_info.get('cause'))
                data.setdefault('versus', html_case_info.get('versus'))  # Case Title/Versus

        except requests.exceptions.RequestException as req_ex:  # More specific exception
            self.logger.error(
                f"Request error in _update_json_with_pacer_html for {os.path.basename(json_path)} (URL: {url}): {req_ex}",
                exc_info=True)
            data['s3_html_error'] = f"Request Error: {req_ex}"
        except Exception as e:
            self.logger.error(
                f"Generic error in _update_json_with_pacer_html for {os.path.basename(json_path)}: {str(e)}",
                exc_info=True)
            data['s3_html_error'] = f"Parsing/Update Error: {e}"

    def _save_json(self, json_path: str, data: Dict) -> None:
        """
        Save the JSON data to a file.
        """
        try:
            # Create a copy of the data to avoid modifying the original
            filtered_data = data.copy()

            # Remove non-serializable objects
            if '_pdf_processor' in filtered_data:
                filtered_data.pop('_pdf_processor')

            # Filter out null values
            NULL_CONDITIONS = ['', 'NA', None, 'Pro Se', 'PRO SE', "None", []]
            filtered_data = {key: value for key, value in filtered_data.items() if value not in NULL_CONDITIONS}

            # Create parent directory if it doesn't exist
            os.makedirs(os.path.dirname(json_path), exist_ok=True)

            # Write the JSON file
            with open(json_path, 'w') as f:
                json.dump(filtered_data, f, indent=2)  # type: ignore[SupportedTypes]

            self.logger.debug(f"Successfully saved JSON to {json_path}")

        except Exception as e:
            self.logger.error(f"Error saving JSON to {json_path}: {str(e)}")
            self.logger.error("Stack trace:", exc_info=True)

    async def download_file_as_string(self, s3_url: str) -> Optional[str]:
        """
        Download a file from S3 and return its contents as a string.
        
        Args:
            s3_url: Full URL (e.g. https://cdn.lexgenius.ai/20250311/html/file.html)
            
        Returns:
            Optional[str]: The file contents as a string, or None if download fails
        """
        try:
            # Validate input
            if not isinstance(s3_url, str):
                self.logger.error(f"Invalid S3 URL type: {type(s3_url)}")
                return None

            if not s3_url:
                self.logger.error("Empty S3 URL provided")
                return None

            # Extract the path portion after cdn.lexgenius.ai/
            if "cdn.lexgenius.ai/" in s3_url:
                s3_key = s3_url.split("cdn.lexgenius.ai/")[1]
            else:
                s3_key = s3_url

            # Debug log the key being passed
            self.logger.debug(f"Extracted S3 key: {s3_key}")

            # Ensure we're not passing the logger by mistake
            if s3_key is self.logger:
                self.logger.error("Logger being passed as S3 key")
                return None

            # Use async method to avoid event loop conflicts
            content = await self.s3_manager.download_content(s3_key)
            if content is None:
                self.logger.error(f"Failed to download content from {s3_url}")
                return None

            # Convert bytes to string if needed
            if isinstance(content, bytes):
                return content.decode('utf-8')
            return content

        except Exception as e:
            self.logger.error(f"Error processing S3 URL {s3_url}: {str(e)}")
            return None
