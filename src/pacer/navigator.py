import logging
import os
import re
from datetime import datetime
from pathlib import Path
from typing import Optional, List

from playwright.async_api import Page, <PERSON><PERSON><PERSON> as Playwright<PERSON>rror, Locator

from .pacer_utils import dynamic_delay


class PacerNavigator:
    """Handles low-level browser interactions for PACER pages."""

    # Reduce default timeout slightly, let specific actions override if needed
    DEFAULT_TIMEOUT_MS = 25000
    # Default delay between actions
    DEFAULT_DELAY_S = 1.5

    def __init__(self, page: Page, delay: Optional[float] = None, timeout_ms: Optional[int] = None):
        if page.is_closed():
            raise ValueError("Cannot initialize PacerNavigator with a closed page.")
        self.page = page
        self.delay = delay if delay is not None else self.DEFAULT_DELAY_S
        self.timeout = timeout_ms if timeout_ms is not None else self.DEFAULT_TIMEOUT_MS
        self.logger = logging.getLogger(f"{__name__}.PacerNavigator")
        self._screenshot_dir = './screenshots'  # Default screenshot dir

    @property
    def is_ready(self) -> bool:
        """Check if the page is open and usable."""
        return not self.page.is_closed()

    async def _ensure_ready(self):
        """Raise an error if the page is not ready for interaction."""
        if not self.is_ready:
            self.logger.error("Page is closed. Cannot perform navigation action.")
            raise PlaywrightError("Page is closed")  # Use PlaywrightError or a custom one

    @dynamic_delay()
    async def goto(self, url: str, wait_until: str = 'domcontentloaded', timeout_override_ms: Optional[int] = None):
        await self._ensure_ready()
        nav_timeout = timeout_override_ms if timeout_override_ms is not None else self.timeout + 10000  # Longer for initial nav
        self.logger.info(f"Navigating to: {url} (Timeout: {nav_timeout}ms)")
        try:
            response = await self.page.goto(url, wait_until=wait_until, timeout=nav_timeout)
            if response and not response.ok:
                self.logger.warning(f"Navigation to {url} resulted in status {response.status}: {response.status_text}")
                # Optionally raise an error for non-2xx/3xx statuses if critical
                # raise PlaywrightError(f"Navigation failed with status {response.status}")
            self.logger.debug(f"Navigation successful to {self.page.url}")
            return response  # Return response object
        except PlaywrightError as e:
            self.logger.error(f"Failed navigating to {url}: {type(e).__name__} - {e}")
            await self.save_screenshot(f"nav_fail_{url.split('/')[-1].split('?')[0]}")  # Sanitize filename
            raise

    @dynamic_delay()
    async def click(self, selector: str, wait_for_nav: bool = False, force_click: bool = False,
                    timeout_override_ms: Optional[int] = None, **kwargs):
        await self._ensure_ready()
        click_timeout = timeout_override_ms if timeout_override_ms is not None else self.timeout
        self.logger.debug(
            f"Clicking selector: {selector} (WaitNav: {wait_for_nav}, Force: {force_click}, Timeout: {click_timeout}ms)")
        try:
            locator = self.page.locator(selector)
            # Ensure locator finds at least one element before proceeding
            if await locator.count() == 0:
                raise PlaywrightError(f"Locator '{selector}' did not find any elements.")
            # Wait for the first element matching the selector to be visible
            await locator.first.wait_for(state='visible', timeout=click_timeout)

            click_options = {'timeout': click_timeout, **kwargs}

            if wait_for_nav:
                # Use slightly longer timeout for navigation expectation
                nav_timeout = click_timeout + 15000
                self.logger.debug(f"Expecting navigation with timeout {nav_timeout}ms")
                async with self.page.expect_navigation(wait_until='domcontentloaded', timeout=nav_timeout):
                    if force_click:
                        await locator.first.dispatch_event('click', timeout=click_timeout)  # Try event dispatch first
                        # await locator.first.click(force=True, **click_options) # force=True bypasses checks
                    else:
                        await locator.first.click(**click_options)
                # Check URL after navigation
                if self.is_ready:  # Check page state again after potential nav
                    self.logger.debug(f"Clicked {selector} and navigated to {self.page.url}")
                else:
                    self.logger.warning(f"Clicked {selector}, expected navigation, but page closed.")

            else:
                if force_click:
                    await locator.first.dispatch_event('click', timeout=click_timeout)  # Try event dispatch first
                    # await locator.first.click(force=True, **click_options)
                else:
                    await locator.first.click(**click_options)
                await self.page.wait_for_timeout(500)  # Small delay after click without navigation
                self.logger.debug(f"Clicked {selector} (no navigation expected)")
        except PlaywrightError as e:
            self.logger.error(f"Failed to click {selector}: {type(e).__name__} - {e}")
            await self.save_screenshot(f"click_fail_{selector[:20]}")
            raise
        except Exception as e:  # Catch unexpected errors
            self.logger.error(f"Unexpected error clicking {selector}: {type(e).__name__} - {e}", exc_info=True)
            await self.save_screenshot(f"click_unexpected_fail_{selector[:20]}")
            raise

    @dynamic_delay()
    async def fill(self, selector: str, value: str, timeout_override_ms: Optional[int] = None, **kwargs):
        await self._ensure_ready()
        fill_timeout = timeout_override_ms if timeout_override_ms is not None else self.timeout
        # Avoid logging sensitive values like passwords
        log_value = "********" if "password" in selector.lower() else value[:50]  # Truncate long values
        self.logger.debug(f"Filling selector '{selector}' with value '{log_value}' (Timeout: {fill_timeout}ms)")
        try:
            locator = self.page.locator(selector)
            if await locator.count() == 0:
                raise PlaywrightError(f"Locator '{selector}' did not find any elements.")
            await locator.first.wait_for(state='visible', timeout=fill_timeout)
            await locator.first.fill(value, timeout=fill_timeout, **kwargs)
            self.logger.debug(f"Filled {selector}")
        except PlaywrightError as e:
            self.logger.error(f"Failed to fill {selector}: {type(e).__name__} - {e}")
            await self.save_screenshot(f"fill_fail_{selector[:20]}")
            raise
        except Exception as e:  # Catch unexpected errors
            self.logger.error(f"Unexpected error filling {selector}: {type(e).__name__} - {e}", exc_info=True)
            await self.save_screenshot(f"fill_unexpected_fail_{selector[:20]}")
            raise

    @dynamic_delay()
    async def select_option(self, selector: str, values: List[str], timeout_override_ms: Optional[int] = None,
                            **kwargs):
        await self._ensure_ready()
        select_timeout = timeout_override_ms if timeout_override_ms is not None else self.timeout
        self.logger.debug(f"Selecting options {values} for selector: {selector} (Timeout: {select_timeout}ms)")
        try:
            locator = self.page.locator(selector)
            if await locator.count() == 0:
                raise PlaywrightError(f"Locator '{selector}' did not find any elements.")
            await locator.first.wait_for(state='visible', timeout=select_timeout)

            # Select options by value attribute
            result = await locator.first.select_option(value=values, timeout=select_timeout, **kwargs)

            # Check which options were actually selected (Playwright returns the selected values)
            selected_options = result if result is not None else []
            if set(selected_options) == set(values):
                self.logger.debug(f"Selected all requested options for {selector}")
            else:
                missing = list(set(values) - set(selected_options))
                extra = list(set(selected_options) - set(values))  # Should be empty if select_option worked correctly
                if missing:
                    self.logger.warning(f"Could not select some options for {selector}. Missing: {missing}")
                if extra:  # This case indicates a potential issue or unexpected behavior
                    self.logger.warning(f"Selected unexpected extra options for {selector}: {extra}")

        except PlaywrightError as e:
            # Handle gracefully if some options don't exist, but log clearly
            if "options not found" in str(e).lower():
                self.logger.warning(
                    f"Some options not found for {selector} via direct select: {e}. Playwright might have selected available ones.")
                # You could add logic here to verify which ones *were* selected if needed
            else:
                self.logger.error(f"Failed to select options for {selector}: {type(e).__name__} - {e}")
                await self.save_screenshot(f"select_fail_{selector[:20]}")
                raise  # Re-raise other Playwright errors
        except Exception as e:  # Catch unexpected errors
            self.logger.error(f"Unexpected error selecting options for {selector}: {type(e).__name__} - {e}",
                              exc_info=True)
            await self.save_screenshot(f"select_unexpected_fail_{selector[:20]}")
            raise

    @dynamic_delay(default_delay=0.5)  # Shorter delay for checking
    async def check(self, selector: str, timeout_override_ms: Optional[int] = None, **kwargs):
        await self._ensure_ready()
        check_timeout = timeout_override_ms if timeout_override_ms is not None else self.timeout
        self.logger.debug(f"Ensuring checkbox '{selector}' is checked (Timeout: {check_timeout}ms)")
        try:
            locator = self.page.locator(selector)
            if await locator.count() == 0:
                raise PlaywrightError(f"Locator '{selector}' did not find any elements.")
            await locator.first.wait_for(state='visible', timeout=check_timeout)
            await locator.first.check(timeout=check_timeout, **kwargs)  # Ensures checked state idempotently
            self.logger.debug(f"Checked {selector}")
        except PlaywrightError as e:
            self.logger.error(f"Failed to check {selector}: {type(e).__name__} - {e}")
            await self.save_screenshot(f"check_fail_{selector[:20]}")
            raise
        except Exception as e:  # Catch unexpected errors
            self.logger.error(f"Unexpected error checking {selector}: {type(e).__name__} - {e}", exc_info=True)
            await self.save_screenshot(f"check_unexpected_fail_{selector[:20]}")
            raise

    # --- Getters (use shorter timeouts as they shouldn't block long) ---
    GETTER_TIMEOUT_MS = 5000

    async def get_text(self, selector: str, **kwargs) -> Optional[str]:
        await self._ensure_ready()
        try:
            locator = self.page.locator(selector)
            # Wait briefly for presence, not necessarily visibility
            await locator.first.wait_for(state='attached', timeout=self.GETTER_TIMEOUT_MS)
            return await locator.first.text_content(timeout=self.GETTER_TIMEOUT_MS, **kwargs)
        except PlaywrightError as e:
            self.logger.debug(f"Could not get text for {selector}: {type(e).__name__} - {e}")
            return None
        except Exception as e:
            self.logger.warning(f"Unexpected error in get_text for {selector}: {type(e).__name__} - {e}",
                                exc_info=False)
            return None

    async def get_attribute(self, selector: str, attribute: str, **kwargs) -> Optional[str]:
        await self._ensure_ready()
        try:
            locator = self.page.locator(selector)
            await locator.first.wait_for(state='attached', timeout=self.GETTER_TIMEOUT_MS)
            return await locator.first.get_attribute(attribute, timeout=self.GETTER_TIMEOUT_MS, **kwargs)
        except PlaywrightError as e:
            self.logger.debug(f"Could not get attribute '{attribute}' for {selector}: {type(e).__name__} - {e}")
            return None
        except Exception as e:
            self.logger.warning(f"Unexpected error in get_attribute for {selector}: {type(e).__name__} - {e}",
                                exc_info=False)
            return None

    async def get_inner_html(self, selector: str, **kwargs) -> Optional[str]:
        await self._ensure_ready()
        try:
            locator = self.page.locator(selector)
            await locator.first.wait_for(state='attached', timeout=self.GETTER_TIMEOUT_MS)
            return await locator.first.inner_html(timeout=self.GETTER_TIMEOUT_MS, **kwargs)
        except PlaywrightError as e:
            self.logger.debug(f"Could not get inner HTML for {selector}: {type(e).__name__} - {e}")
            return None
        except Exception as e:
            self.logger.warning(f"Unexpected error in get_inner_html for {selector}: {type(e).__name__} - {e}",
                                exc_info=False)
            return None

    async def locator(self, selector: str, **kwargs) -> Locator:
        """Provides direct access to locator for more complex checks/interactions."""
        await self._ensure_ready()
        return self.page.locator(selector, **kwargs)

    async def save_screenshot(self, filename_suffix: str):
        if not self.is_ready:
            self.logger.warning("Cannot save screenshot, page is closed.")
            return

        # Ensure screenshot directory exists
        try:
            Path(self._screenshot_dir).mkdir(parents=True, exist_ok=True)
        except Exception as e:
            self.logger.error(f"Failed to create screenshot directory {self._screenshot_dir}: {e}")
            return  # Cannot save if directory fails

        # Sanitize suffix: replace non-alphanumeric with underscore, limit length
        safe_suffix = re.sub(r'[^\w\-_\.]', '_', filename_suffix)
        safe_suffix = safe_suffix[:50]  # Limit length
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{timestamp}_{safe_suffix}.png"
        screenshot_path = os.path.join(self._screenshot_dir, filename)

        try:
            await self.page.screenshot(path=screenshot_path, full_page=True, timeout=15000)  # Add timeout
            self.logger.info(f"Screenshot saved: {screenshot_path}")
        except PlaywrightError as e:
            self.logger.error(f"Failed to save screenshot {screenshot_path}: {type(e).__name__} - {e}", exc_info=False)
        except Exception as e:
            self.logger.error(f"Unexpected error saving screenshot {screenshot_path}: {type(e).__name__} - {e}",
                              exc_info=True)

    def set_screenshot_dir(self, path: str):
        """Sets the directory for saving screenshots."""
        self._screenshot_dir = path
        self.logger.debug(f"Screenshot directory set to: {path}")

    async def __aenter__(self):
        # Could add setup logic here if needed, e.g., wait for ready state
        await self._ensure_ready()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        # No specific cleanup in navigator needed, page closure handled elsewhere
        pass
