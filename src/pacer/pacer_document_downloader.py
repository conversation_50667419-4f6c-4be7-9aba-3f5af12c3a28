import asyncio
import logging
import re
import shutil
import time
import uuid
from pathlib import Path
from typing import Dict, Any, Optional, List
import tempfile

from playwright.async_api import Locator, TimeoutError as PlaywrightTimeoutError, <PERSON>rror as PlaywrightError, \
    Download as PlaywrightDownload

from .pacer_utils import retry_async
from .file_manager import PacerFileManager
from .navigator import PacerNavigator
from src.infrastructure.storage.s3_async import S3AsyncStorage


class PacerDocumentDownloader:
    def __init__(self, navigator: PacerNavigator, file_manager: PacerFileManager,
                 s3_manager: S3AsyncStorage, stability_config: Dict[str, Any],
                 config: Dict[str, Any], iso_date: str, current_court_id: str,
                 gpt_interface: Optional[Any] = None,
                 logger: Optional[logging.Logger] = None):
        self.navigator = navigator
        self.file_manager = file_manager
        self.s3_manager = s3_manager
        self.stability_config = stability_config
        self.config = config
        self.iso_date = iso_date
        self.court_id = current_court_id
        self.gpt_interface = gpt_interface
        if logger:
            self.logger = logger
        else:
            # Fallback to a default logger for this component
            self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        self.logger.info(f"{__name__}.ReportHandler.{self.court_id}.{self.iso_date} initialized.")
        # This attribute is used by some sub-methods for detailed logging.
        # It will be set temporarily by methods that call these sub-methods.
        self._current_case_docket_num_for_logging: Optional[str] = None

    async def _click_element_if_exists(self, selector: str, description: str, timeout_ms: int = 5000) -> bool:
        try:
            locator_obj = await self.navigator.locator(selector)
            locator_instance = locator_obj.first
            await locator_instance.wait_for(state="visible", timeout=timeout_ms)
            await locator_instance.click(timeout=timeout_ms)
            self.logger.info(f"[{self.court_id}] Clicked '{description}' (Selector: {selector})")
            await self.navigator.page.wait_for_timeout(500)
            return True
        except (PlaywrightTimeoutError, PlaywrightError):
            self.logger.debug(
                f"[{self.court_id}] Elem '{description}' ({selector}) not found/visible or click failed in {timeout_ms}ms.")
            return False

    async def _deselect_checkboxes_except_first_one_async(self, current_case_docket_num_for_logging: str,
                                                          is_removal: bool) -> bool:
        log_prefix = f"[{self.court_id}][{current_case_docket_num_for_logging}] CheckboxLogic:"
        page = self.navigator.page
        self.logger.info(f"{log_prefix} Starting checkbox deselection logic.")

        selectors = [
            ("primary", "div#cmecfMainContent table input[type='checkbox'][name^='document_']"),
            ("fallback_attachment", "div#cmecfMainContent table input[type='checkbox'][name^='attachment_']"),
            ("general_doc", "input[type='checkbox'][name^='document_']"),
            ("general_att", "input[type='checkbox'][name^='attachment_']"),
        ]
        all_checkboxes_locators: List[Locator] = []
        for name, sel_str in selectors:
            self.logger.debug(f"{log_prefix} Finding checkboxes with {name} selector: {sel_str}")
            try:
                timeout = 5000 if name == "primary" else 2000
                await page.wait_for_selector(sel_str, state="attached", timeout=timeout)
                checkbox_locator_object = await self.navigator.locator(sel_str)
                all_checkboxes_locators = await checkbox_locator_object.all()
                if all_checkboxes_locators:
                    self.logger.info(
                        f"{log_prefix} Found {len(all_checkboxes_locators)} checkboxes using {name} selector.")
                    break
            except PlaywrightTimeoutError:
                self.logger.debug(f"{log_prefix} No checkboxes with {name} selector.")
            except PlaywrightError as e:
                self.logger.warning(f"{log_prefix} PlaywrightError with {name} selector: {e}")
            if all_checkboxes_locators: break

        if not all_checkboxes_locators:
            self.logger.info(f"{log_prefix} No document/attachment checkboxes found.")
            if is_removal:
                self.logger.info(f"{log_prefix} Is removal, trying click direct link '1'.")
                if await self._click_element_if_exists("div#cmecfMainContent table td a:text-is('1')",
                                                       "hyperlink '1' (removal, no checkboxes)", 3000):
                    self.logger.info(f"{log_prefix} Clicked direct link '1' for removal. Action taken.")
                    return True
                self.logger.warning(f"{log_prefix} No direct link '1' for removal. No checkbox action.")
                return False
            self.logger.info(f"{log_prefix} Not removal & no checkboxes. No action by this method.")
            return False

        try:
            self.logger.info(f"{log_prefix} Processing {len(all_checkboxes_locators)} checkboxes.")
            first_actionable_cb_processed = False
            for i, cb_loc in enumerate(all_checkboxes_locators):
                if not await cb_loc.is_visible(timeout=500) or not await cb_loc.is_enabled(timeout=500):
                    self.logger.debug(f"{log_prefix} Checkbox {i} not visible/enabled, skipping.")
                    continue

                is_checked = await cb_loc.is_checked()
                action_taken = False
                if not first_actionable_cb_processed:
                    if not is_checked:
                        await cb_loc.check(timeout=2000)
                        action_taken = True
                    first_actionable_cb_processed = True
                elif is_checked:  # Subsequent checkbox
                    await cb_loc.uncheck(timeout=2000)
                    action_taken = True
                if action_taken: await page.wait_for_timeout(100)

            if not first_actionable_cb_processed and all_checkboxes_locators:  # Re-ensure first is checked
                for cb_loc in all_checkboxes_locators:
                    if await cb_loc.is_visible(timeout=500) and await cb_loc.is_enabled(timeout=500):
                        if not await cb_loc.is_checked():
                            await cb_loc.check(timeout=2000)
                            await page.wait_for_timeout(100)
                        break
            await page.wait_for_timeout(200)
            self.logger.info(f"{log_prefix} Checkbox manipulation completed.")
            return True
        except PlaywrightError as e:
            self.logger.error(f"{log_prefix} Error during checkbox manipulation: {e}", exc_info=True)
            await self.navigator.save_screenshot(f"checkbox_error_{current_case_docket_num_for_logging}")
            return False

    async def _click_download_documents_if_exists_async(self) -> bool:
        # This method attempts to click "Download Documents" if it exists.
        log_prefix = f"[{self.court_id}][{self._current_case_docket_num_for_logging or 'GENERAL'}]"

        selectors = [
            "input[value='Download Documents'][type='button']",
            "input[value='Download Documents'][type='submit']"
        ]

        for idx, selector in enumerate(selectors):
            try:
                if self.navigator.page.is_closed():
                    self.logger.warning(
                        f"{log_prefix} Page closed before attempting to find 'Download Documents' (selector {idx + 1}). Button click skipped.")
                    return False

                locator_collection = await self.navigator.locator(selector)

                if self.navigator.page.is_closed():  # Re-check page status after await
                    self.logger.warning(
                        f"{log_prefix} Page closed after self.navigator.locator() for 'Download Documents' (selector {idx + 1}). Button click skipped.")
                    return False

                element_count = 0
                try:
                    element_count = await locator_collection.count()
                except PlaywrightError as e_count:
                    if "target page, context or browser has been closed" in str(e_count).lower() or \
                            "execution context was destroyed" in str(e_count).lower():
                        self.logger.error(  # Changed from WARNING to ERROR
                            f"{log_prefix} Page/context closed UNEXPECTEDLY during count for 'Download Documents' (selector {idx + 1}). This indicates instability. Selector failed. Error: {e_count}")
                        # Instead of returning True, continue to the next selector or let the function return False.
                        # This was a key reason for njd's long wait.
                        continue
                    else:
                        # For other errors during count, log and try next selector or fail.
                        self.logger.debug(
                            f"{log_prefix} Error during count for 'Download Documents' (selector {idx + 1}): {e_count}")
                        continue  # Try next selector

                if element_count > 0:
                    button_locator = locator_collection.first
                    try:
                        await button_locator.wait_for(state='visible', timeout=3000)  # Short timeout for visibility
                        self.logger.info(
                            f"{log_prefix} Found 'Download Documents' (Selector: {selector}). Attempting click with no_wait_after=True.")

                        # Use no_wait_after=True because this button's onclick causes a parent.location change
                        # to initiate the download. We don't want Playwright to time out waiting for this
                        # navigation to "settle" in the usual way.
                        await button_locator.click(timeout=10000, no_wait_after=True)

                        self.logger.info(
                            f"{log_prefix} Click (no_wait_after=True) dispatched for 'Download Documents' (Selector: {selector}). Assuming action initiated.")
                        # Brief pause to allow the browser to process the click and for the navigation (parent.location) to begin,
                        # which should lead to the 'download' event being fired.
                        # This pause is important because no_wait_after returns immediately.
                        await self.navigator.page.wait_for_timeout(1000)  # Give browser a moment to react
                        return True  # Assume click initiated the process successfully.
                    except PlaywrightError as e_click_direct:
                        # This error occurs if the click itself failed (e.g., element detached, not visible for click).
                        # It could also occur if the page closed/navigated extremely rapidly as a result of the click attempt.
                        self.logger.debug(
                            f"{log_prefix} Click (no_wait_after=True) on 'Download Documents' (Selector: {selector}) resulted in an error. Error: {e_click_direct}")

                        # If the error indicates the page closed, context was destroyed, or frame detached,
                        # it strongly implies the click DID have an effect (navigation/closure started).
                        if "target page, context or browser has been closed" in str(e_click_direct).lower() or \
                                "execution context was destroyed" in str(e_click_direct).lower() or \
                                "frame was detached" in str(e_click_direct).lower():
                            self.logger.warning(
                                f"{log_prefix} Page/context/frame changed during/after click (no_wait_after) on 'Download Documents' (Selector: {selector}). Assuming action was initiated. Error: {e_click_direct}")
                            return True
                            # For other errors (e.g. element genuinely not clickable before dispatch), continue to the next selector.
                else:
                    self.logger.debug(
                        f"{log_prefix} Elem 'Download Documents' (Selector: {selector}) not found by count.")

            except PlaywrightError as e_outer:  # Errors during the initial self.navigator.locator call or other setup within the loop for this selector
                if "navigation" in str(e_outer).lower() or \
                        "context was destroyed" in str(e_outer).lower() or \
                        "target page, context or browser has been closed" in str(e_outer).lower():
                    self.logger.warning(
                        f"{log_prefix} Navigation/closure while initially locating '{selector}' for 'Download Documents'. Error: {e_outer}")
                    # If page changed here, the action might have been preempted or implicitly successful.
                    return True  # Let download listener determine outcome
                self.logger.debug(
                    f"{log_prefix} PlaywrightError initially locating '{selector}' for 'Download Documents': {e_outer}")
            except Exception as e_unexp:  # Catch-all for unexpected issues for this selector attempt
                self.logger.error(
                    f"{log_prefix} Unexpected error with 'Download Documents' (selector {idx + 1}): {e_unexp}",
                    exc_info=True)

        self.logger.debug(
            f"{log_prefix} Button 'Download Documents' not successfully actioned with any provided selectors.")
        return False

    async def _find_and_click_view_document_async(self) -> bool:
        log_prefix = f"[{self.court_id}]"
        self.logger.debug(f"{log_prefix} Attempting 'View Document' link flow...")
        if not await self._click_element_if_exists("a:text-is('View Document')", "'View Document' link", 5000):
            self.logger.debug(f"{log_prefix} 'View Document' link not found/click failed.")
            return False
        self.logger.info(f"{log_prefix} Clicked 'View Document' link.")

        try:
            await self.navigator.page.wait_for_selector("text=/Go to Document/i", state="visible", timeout=10000)
            self.logger.info(f"{log_prefix} 'Go to Document' page detected.")
        except PlaywrightTimeoutError:  # Still counts as success of initial click for some flows
            self.logger.warning(f"{log_prefix} No 'Go to Document' text after 'View Document' click.")
            return True

        doc_num_input_obj = await self.navigator.locator("input[name='document_number']")
        try:
            await doc_num_input_obj.first.wait_for(state="visible", timeout=5000)
            await doc_num_input_obj.first.fill("1", timeout=3000)
            self.logger.info(f"{log_prefix} Filled 'document_number' with '1'.")
        except PlaywrightError as e:
            self.logger.error(f"{log_prefix} Failed to fill 'document_number': {e}")
            return False

        if not await self._click_element_if_exists("input[name='button1'][value*='Report']", "Run Report button", 5000):
            self.logger.error(f"{log_prefix} Failed to click 'Run Report' after filling doc number.")
            return False
        self.logger.info(f"{log_prefix} Clicked 'Run Report'.")
        return True

    async def _both_buttons_exist_async(self) -> bool:
        view_all_loc = await self.navigator.locator("input[value='View All'][onclick*='show_multidocs.pl']")
        dl_all_loc = await self.navigator.locator("input[value='Download All'][onclick*='zipit=1']")
        view_all_present = await view_all_loc.count() > 0
        dl_all_present = await dl_all_loc.count() > 0
        if view_all_present and dl_all_present:
            self.logger.debug(f"[{self.court_id}] Both 'View All' & 'Download All' buttons detected.")
            return True
        self.logger.debug(f"[{self.court_id}] View All: {view_all_present}, Download All: {dl_all_present}. Not both.")
        return False

    @retry_async(tries=2, delay=2, exceptions=(PlaywrightError,))
    async def click_complaint_link(self, case_details: Dict[str, Any]) -> bool:
        docket_num = case_details.get('docket_num', 'N/A')
        is_removal = case_details.get('is_removal', False)
        log_prefix = f"[{self.court_id}][{docket_num}]"
        self.logger.info(f"{log_prefix} Searching for complaint/doc link (Removal: {is_removal})...")
        page = self.navigator.page
        if not self.navigator.is_ready: return False

        try:
            table_locator = page.locator("table[width='99%'][border='1']")
            await table_locator.first.wait_for(state="visible", timeout=10000)
            all_row_locators = await table_locator.locator("tr:nth-child(n+2)").all()
            if not all_row_locators: return False

            target_link_locator: Optional[Locator] = None
            link_description = "Complaint/Document"

            if is_removal:
                self.logger.debug(f"{log_prefix} Handling removal case link finding.")
                if all_row_locators:
                    first_row = all_row_locators[0]
                    td_locators = first_row.locator("td")
                    if await td_locators.count() >= 3:
                        # Text still primarily from 3rd TD (index 2) for context/GPT
                        third_td_text = await td_locators.nth(2).text_content(timeout=5000) or ""
                        self.logger.debug(f"{log_prefix} Text for GPT (removal): {third_td_text[:100]}...")
                        
                        # Check if we already have a removal link number from review processing
                        link_num_gpt = case_details.get('removal_link_number')
                        
                        if link_num_gpt:
                            self.logger.info(f"{log_prefix} Using pre-defined removal link number from review process: {link_num_gpt}")
                        else:
                            # Call GPT to find the state removal link
                            if hasattr(self, 'gpt_interface') and self.gpt_interface:
                                try:
                                    gpt_result = self.gpt_interface.find_state_filing_link(third_td_text)
                                    link_num_gpt = gpt_result.get('number', 'NA')
                                    self.logger.info(f"{log_prefix} GPT returned link number: {link_num_gpt}")
                                    
                                    # If GPT returns 'NA', we should add to review list and skip download
                                    if link_num_gpt == 'NA':
                                        self.logger.info(f"{log_prefix} GPT returned 'NA' for removal case. Should be added to review list.")
                                        # Set a flag in case_details to indicate this needs review
                                        case_details['_gpt_removal_na'] = True
                                        case_details['_reason_review'] = "GPT could not identify state removal link"
                                        # Return False to indicate no link was clicked
                                        return False
                                except Exception as e:
                                    self.logger.error(f"{log_prefix} Error calling GPT for removal link: {e}")
                                    link_num_gpt = None
                            else:
                                self.logger.warning(f"{log_prefix} GPT interface not available for removal link detection")

                        # Targeting the 3rd TD (index 2) for the link IN REMOVAL CASES
                        link_td = td_locators.nth(2)
                        # Ensure link_num_gpt is a string before calling isdigit
                        if link_num_gpt and isinstance(link_num_gpt, str) and link_num_gpt.isdigit():
                            # Looking inside the 3rd TD for the link
                            target_link_locator = link_td.locator(f"a:text-is('{link_num_gpt}')").first
                            link_description = f"Removal Doc Link '{link_num_gpt}' (GPT in TD3)"
                        else:  # Fallback 1: Look for link '1'
                            # Looking inside the 3rd TD for link '1'
                            link_1_locator = link_td.locator("a:text-is('1')").first
                            if await link_1_locator.count() > 0:
                                target_link_locator = link_1_locator
                                link_description = "Removal Doc Link '1' (Fallback in TD3)"
                            else:  # Fallback 2: Check if *any* link exists in TD3 of first row
                                fallback_link_td3 = link_td.locator("a").first
                                if await fallback_link_td3.count() > 0:
                                    target_link_locator = fallback_link_td3
                                    link_text_fb = await fallback_link_td3.text_content(timeout=500) or "link"
                                    link_description = f"First Link '{link_text_fb}' in TD3 (Fallback)"
                                else:  # Fallback 3: Search rows for 'notice of removal', check TD3
                                    self.logger.debug(
                                        f"{log_prefix} Fallback: Searching rows for 'notice of removal' text, checking TD3.")
                                    for i, row_l in enumerate(all_row_locators[:5]):
                                        row_text = await row_l.text_content(timeout=1000) or ""
                                        if "notice of removal" in row_text.lower():
                                            # Looking inside the 3rd TD (index 2) of the matching row
                                            link_in_td = row_l.locator("td").nth(2).locator("a").first
                                            if await link_in_td.count() > 0:
                                                link_text = await link_in_td.text_content(timeout=500) or ""
                                                target_link_locator = link_in_td
                                                link_description = f"Notice of Removal Link '{link_text}' in TD3"
                                                break

            if not target_link_locator:  # Standard complaint logic (NOT removal or if removal link finding failed)
                self.logger.debug(f"{log_prefix} Handling standard complaint link finding (using TD index 1).")
                for i, row_l in enumerate(all_row_locators):
                    # Text from 3rd TD (index 2)
                    docket_text = await row_l.locator("td").nth(2).text_content(timeout=1000) or ""
                    docket_text_lower = docket_text.lower().strip()
                    # Standard exclusion logic remains the same
                    if not docket_text_lower or 'incomplete' in docket_text_lower or \
                            'stricken - complaint' in docket_text_lower or \
                            docket_text_lower.startswith('civil cover sheet') or \
                            ('complaint' in docket_text_lower and (
                                    'e-file' in docket_text_lower or 'efile' in docket_text_lower)) or \
                            ('amended complaint' in docket_text_lower and i > 0):
                        continue
                    if 'complaint' in docket_text_lower:
                        # Still looking inside the 2nd TD (index 1) for standard complaints
                        link_locator = row_l.locator("td").nth(1).locator("a").first
                        if await link_locator.count() > 0:
                            link_text = await link_locator.text_content(timeout=500) or "Link"
                            target_link_locator = link_locator
                            link_description = f"Complaint Link '{link_text}' in TD2"
                            break

            if target_link_locator:
                self.logger.info(f"{log_prefix} Attempting to click {link_description}...")
                await target_link_locator.wait_for(state="visible", timeout=10000)
                await target_link_locator.click()
                self.logger.info(f"{log_prefix} Successfully clicked {link_description}.")
                return True

            self.logger.warning(f"{log_prefix} No suitable complaint/document link found.")
            await self.navigator.save_screenshot(f"click_complaint_not_found_{docket_num}")
            return False
        except PlaywrightError as e:
            self.logger.error(f"{log_prefix} Error clicking complaint link: {e}", exc_info=True)
            await self.navigator.save_screenshot(f"click_complaint_error_{docket_num}")
            return False

    async def _wait_for_file_stabilization(self, file_path: Path, docket_num_for_log: str) -> bool:
        log_prefix = f"[{self.court_id}][{docket_num_for_log}] Stabilization:"
        self.logger.debug(f"{log_prefix} Checking stabilization for: {file_path.name}")
        timeout_s = self.stability_config.get('timeout_s', 45)
        check_interval_s = self.stability_config.get('check_interval_s', 0.5)
        required_duration_s = self.stability_config.get('required_duration_s', 2.0)

        if not await asyncio.to_thread(file_path.exists): return False
        last_size, start_time = -1, time.monotonic()
        stable_start_time = start_time
        zero_checks, max_zero_checks = 0, int((required_duration_s + 1) / check_interval_s)

        while (time.monotonic() - start_time) < timeout_s:
            try:
                if not await asyncio.to_thread(file_path.exists): return False
                current_size = (await asyncio.to_thread(file_path.stat)).st_size
            except FileNotFoundError:
                return False
            except Exception as e:
                self.logger.error(f"{log_prefix} Error getting size for {file_path.name}: {e}")
                await asyncio.sleep(check_interval_s)
                continue

            if current_size == 0:
                zero_checks += 1 if last_size <= 0 else 1  # if becomes 0, reset counter effectively
                if zero_checks > max_zero_checks: return False  # Persistently zero
            else:
                zero_checks = 0

            if current_size == last_size:
                if current_size > 0 and (time.monotonic() - stable_start_time) >= required_duration_s:
                    self.logger.info(f"{log_prefix} File size {current_size}b stabilized for {file_path.name}")
                    return True
            else:  # Size changed
                last_size, stable_start_time = current_size, time.monotonic()
                if current_size > 0 and last_size == 0: zero_checks = 0
            await asyncio.sleep(check_interval_s)

        # Timeout
        if last_size > 0:  # Has content, but unstable
            self.logger.warning(f"{log_prefix} Timeout for {file_path.name}. Size: {last_size}. Proceeding.")
            return True
        self.logger.warning(f"{log_prefix} Timeout for {file_path.name}. Size {last_size}. Failed.")
        return False

    async def _trigger_download_action(self, docket_num_for_log: str, is_removal: bool) -> bool:
        page = self.navigator.page
        log_prefix = f"[{self.court_id}][{docket_num_for_log}] TriggerAction:"
        self.logger.info(f"{log_prefix} Initiating download trigger...")
        await page.wait_for_timeout(self.config.get('trigger_action_initial_delay_ms', 1000))

        self._current_case_docket_num_for_logging = docket_num_for_log
        download_initiated_by_specific_action = False  # Flag to track if a download-specific click occurred

        checkbox_action_performed = await self._deselect_checkboxes_except_first_one_async(docket_num_for_log,
                                                                                           is_removal)

        if checkbox_action_performed:
            # This case means _deselect_checkboxes_except_first_one_async clicked a direct link for removal
            if is_removal and not await page.locator("input[type='checkbox'][name^='document_']").first.is_visible(
                    timeout=100):
                self.logger.info(
                    f"{log_prefix} Removal link likely clicked directly by checkbox logic. Download initiated.")
                self._current_case_docket_num_for_logging = None
                return True  # Direct link click is a download trigger

            # Try "Download Selected" first after checkbox manipulation
            clicked_dl_selected = await self._click_element_if_exists(
                "input#download_button[value='Download Selected'], input[type='submit'][value='Download Selected']",
                "Download Selected button (post-checkbox)", 3000)
            if clicked_dl_selected:
                self.logger.info(f"{log_prefix} Clicked 'Download Selected'. This is a download trigger.")
                download_initiated_by_specific_action = True

            # Then try "Download Documents"
            # _click_download_documents_if_exists_async returns True if it thinks it actioned something
            if await self._click_download_documents_if_exists_async():
                self.logger.info(
                    f"{log_prefix} Successfully actioned 'Download Documents'. This is a download trigger.")
                download_initiated_by_specific_action = True

            if download_initiated_by_specific_action:
                self.logger.info(f"{log_prefix} Download explicitly triggered after checkbox action.")
                self._current_case_docket_num_for_logging = None
                return True

            # If only checkbox action was performed but no explicit download button was clicked
            self.logger.warning(
                f"{log_prefix} Checkbox action performed, but no subsequent 'Download Selected' or 'Download Documents' button was successfully clicked. Not considering this a definitive download trigger yet.")
            # download_initiated_by_specific_action remains False
        else:  # No checkbox action performed
            self.logger.debug(
                f"{log_prefix} No checkbox action performed or it didn't lead to a direct link click for removal.")

        # Try other direct download triggers if no download was initiated above
        if await self._click_element_if_exists(
                "input[type='submit'][value='View Document'], input[type='button'][value='View Document']",
                "View Document input button", 5000):
            self.logger.info(f"{log_prefix} Clicked 'View Document' input button. This might lead to a download/PDF.")
            self._current_case_docket_num_for_logging = None
            return True  # Assume this can lead to a downloadable page or direct download

        if await self._find_and_click_view_document_async():
            self.logger.info(
                f"{log_prefix} 'Find and click View Document' link flow succeeded. This might lead to a download/PDF.")
            self._current_case_docket_num_for_logging = None
            return True  # Assume this can lead to a downloadable page or direct download

        if await self._both_buttons_exist_async():
            self.logger.info(f"{log_prefix} 'View All' & 'Download All' buttons exist (VADA buttons).")
            if await self._click_element_if_exists("input[value='Download All'][onclick*='zipit=1']",
                                                   "Download All button (VADA)", 3000):
                self.logger.info(f"{log_prefix} Clicked 'Download All' (VADA). This is a download trigger.")
                self._current_case_docket_num_for_logging = None
                return True
            if await self._click_element_if_exists("input[value='View All'][onclick*='show_multidocs.pl']",
                                                   "View All button (VADA)", 3000):
                self.logger.info(f"{log_prefix} Clicked 'View All' (VADA). This might lead to a page with downloads.")
                self._current_case_docket_num_for_logging = None
                return True  # Assume this can lead to downloadable content

        # Fallback buttons - these are more likely to be direct download triggers
        fallback_buttons_known_to_download = [
            ("input[value='Download Documents'][type='button'], input[value='Download Documents'][type='submit']",
             # Already tried if checkboxes ran
             "Download Documents (fallback)"),
            ("input#download_button[value='Download Selected'], input[type='submit'][value='Download Selected']",
             # Already tried if checkboxes ran
             "Download Selected (fallback)"),
            ("div#cmecfMainContent table td a:text-is('1')", "hyperlink '1' (fallback - often direct PDF)")
        ]
        for selector, desc in fallback_buttons_known_to_download:
            if await self._click_element_if_exists(selector, desc, 3000):
                self.logger.info(f"{log_prefix} Clicked '{desc}' from fallback list. This is a download trigger.")
                self._current_case_docket_num_for_logging = None
                return True

        # If we reach here, no definitive download-initiating action was confirmed.
        self.logger.error(
            f"{log_prefix} No known download trigger action successfully confirmed a download initiation.")
        if not page.is_closed():  # Save screenshot only if page is still open
            await self.navigator.save_screenshot(f"trigger_action_NO_DOWNLOAD_CONFIRMED_{docket_num_for_log}")
        self._current_case_docket_num_for_logging = None
        return False

    async def _upload_document_to_s3(self, file_path_str: str, case_details: Dict[str, Any]) -> Optional[str]:
        # `current_case_docket_num_for_logging` would be passed or use case_details for docket_num
        docket_num_log = case_details.get('docket_num', 'N/A_UPLOAD')
        log_prefix = f"[{self.court_id}][{docket_num_log}]"
        file_path = Path(file_path_str)
        if not file_path.exists():
            self.logger.error(f"{log_prefix} S3 Upload: File not found {file_path}")
            return None

        s3_key = f"{self.iso_date}/dockets/{file_path.name}"
        self.logger.info(f"{log_prefix} Uploading {file_path.name} to S3: {s3_key}")
        try:
            # Call async method directly without asyncio.to_thread
            upload_result = await self.s3_manager.upload_file(str(file_path), s3_key,
                                                             force_upload=False)

            # Ensure 'success' is strictly boolean.
            success: bool
            if isinstance(upload_result, tuple):
                if len(upload_result) > 0 and upload_result[0] is True:
                    success = True
                else:  # Tuple, but first element is not True (could be False, None, or empty tuple)
                    success = False
            elif upload_result is True:  # Non-tuple, explicitly True
                success = True
            else:  # Non-tuple, not explicitly True (could be False or None)
                success = False

            if success:
                cdn_base = self.config.get('cdn_base_url', 'https://cdn.lexgenius.ai')
                cdn_link = f"{cdn_base.rstrip('/')}/{s3_key}"
                self.logger.info(f"{log_prefix} Doc uploaded to S3. CDN: {cdn_link}")
                return cdn_link
            else:
                err_msg = upload_result[1] if isinstance(upload_result, tuple) and len(upload_result) > 1 else "Unknown"
                self.logger.error(f"{log_prefix} S3 upload failed for {s3_key}. Reason: {err_msg}")
                return None
        except Exception as e:
            self.logger.error(f"{log_prefix} Error uploading to S3 {s3_key}: {e}", exc_info=True)
            return None

    async def trigger_and_finalize_download(self, case_details: Dict[str, Any]) -> bool | None:
        page = self.navigator.page
        base_filename = case_details.get('base_filename', 'unknown_base')
        docket_num = case_details.get('docket_num', 'N/A_DOWNLOAD')
        is_removal = case_details.get('is_removal', False)
        log_prefix = f"[{self.court_id}][{docket_num}]"
        self.logger.info(f"{log_prefix} Download sequence for {base_filename} (is_removal: {is_removal})")

        # Check if browser download path is provided via config (from orchestrator)
        # CRITICAL WARNING: DO NOT MODIFY THIS PATH VALIDATION LOGIC!
        # This ensures downloaded files are placed in the correct directory and prevents
        # files from being lost in temp directories during parallel processing.
        context_download_path = self.config.get('context_download_path')
        if context_download_path:
            # Use the browser's download directory to ensure files are downloaded where we expect them
            staging_root = Path(context_download_path)
            self.logger.info(f"{log_prefix} Using browser download path as staging root: {staging_root}")
        else:
            self.logger.error(
                f"{log_prefix} CRITICAL: 'context_download_path' not found in configuration for PacerDocumentDownloader. "
                f"Cannot determine where to find downloaded files. Aborting download."
            )
            return False

        attempt_uuid = uuid.uuid4().hex[:8]  # Unique ID for this download attempt's staging
        temp_dl_dir = staging_root / f"dl_stage_{self.court_id}_{docket_num.replace(':', '_').replace('/', '_')}_{attempt_uuid}"

        try:
            await asyncio.to_thread(temp_dl_dir.mkdir, parents=True, exist_ok=True)
            self.logger.debug(f"{log_prefix} Created temp download staging dir: {temp_dl_dir}")
        except Exception as e:
            self.logger.error(f"{log_prefix} Mkdir failed for {temp_dl_dir}: {e}")
            return False

        # This list will hold the path to the successfully saved file from the event handler
        downloaded_file_path_holder: List[Optional[Path]] = [None]
        # This event is set AFTER download.save_as() in the event handler completes or fails
        download_save_operation_complete_event = asyncio.Event()

        async def on_download_event(download: PlaywrightDownload):
            self.logger.info(
                f"{log_prefix} PW 'download' event FIRED. Suggested filename: {download.suggested_filename}, URL: {download.url}")

            # Sanitize filename and create a unique temporary save path
            original_fn = download.suggested_filename or f"unknown_dl_file_{uuid.uuid4().hex[:6]}"
            safe_original_fn = re.sub(r'[^\w\-\._]', '_', original_fn)
            if len(safe_original_fn) > 150: safe_original_fn = safe_original_fn[-150:]

            temp_save_target = temp_dl_dir / safe_original_fn

            try:
                self.logger.info(
                    f"{log_prefix} Attempting to save download from event to: {temp_save_target}. This will block until file is saved or fails.")
                # CRITICAL: save_as() blocks until the download is complete or errors.
                await download.save_as(temp_save_target)

                # After save_as completes, check file integrity
                if await asyncio.to_thread(temp_save_target.exists) and \
                        (await asyncio.to_thread(temp_save_target.stat)).st_size > 0:
                    self.logger.info(
                        f"{log_prefix} Download file saved successfully by event handler to: {temp_save_target}")
                    downloaded_file_path_holder[0] = temp_save_target
                else:
                    self.logger.error(
                        f"{log_prefix} download.save_as() completed but file {temp_save_target} is missing or zero-size.")
                    downloaded_file_path_holder[0] = None  # Ensure it's None if save failed

                # Check Playwright's own failure indicator for the download object
                failure_reason = await download.failure()
                if failure_reason:
                    self.logger.error(f"{log_prefix} Playwright Download object reported failure: {failure_reason}")
                    downloaded_file_path_holder[0] = None  # Override if Playwright says it failed

            except PlaywrightError as e_save:
                self.logger.error(f"{log_prefix} PlaywrightError during download.save_as() in event handler: {e_save}")
                downloaded_file_path_holder[0] = None
            except Exception as e_unexp_save:
                self.logger.error(
                    f"{log_prefix} Unexpected error during download.save_as() in event handler: {e_unexp_save}",
                    exc_info=True)
                downloaded_file_path_holder[0] = None
            finally:
                # This event signals that the save_as attempt (and thus the download event handling) is complete.
                self.logger.debug(
                    f"{log_prefix} 'download' event processing (including save_as) finished. Setting download_save_operation_complete_event.")
                download_save_operation_complete_event.set()

                # Attach listener ONCE before any trigger action

        page.on("download", on_download_event)
        self.logger.debug(f"{log_prefix} PW 'download' listener attached.")

        final_path_str: Optional[str] = None
        download_trigger_succeeded = False
        current_downloaded_file: Optional[Path] = None

        try:
            if page.is_closed():
                self.logger.error(f"{log_prefix} Page is already closed before _trigger_download_action. Aborting.")
                raise Exception("Page closed before download trigger.")

            download_trigger_succeeded = await self._trigger_download_action(docket_num, is_removal)

            if download_trigger_succeeded:
                self.logger.info(
                    f"{log_prefix} Trigger action reported success or context change. Now waiting for download file save operation to complete...")
                try:
                    # VERY IMPORTANT: This timeout must be long enough for the largest expected file to download AND be saved by save_as().
                    download_completion_timeout_s = self.config.get('download_save_as_completion_timeout_s',
                                                                    600)  # Default 10 minutes
                    self.logger.debug(
                        f"{log_prefix} Waiting for download_save_operation_complete_event with timeout: {download_completion_timeout_s}s")
                    await asyncio.wait_for(download_save_operation_complete_event.wait(),
                                           timeout=download_completion_timeout_s)
                    self.logger.info(f"{log_prefix} download_save_operation_complete_event was set (or timed out).")
                    current_downloaded_file = downloaded_file_path_holder[0]  # Get path from event handler
                except asyncio.TimeoutError:
                    self.logger.error(
                        f"{log_prefix} CRITICAL TIMEOUT ({download_completion_timeout_s}s) waiting for download_save_operation_complete_event. The file save operation via event handler severely stalled or failed to signal completion.")
                    current_downloaded_file = None
            else:
                self.logger.warning(
                    f"{log_prefix} Download trigger action itself failed or reported no plausible action taken. Not waiting for download save event.")

            if current_downloaded_file and await asyncio.to_thread(current_downloaded_file.exists):
                self.logger.info(
                    f"{log_prefix} File successfully obtained and saved via download event: {current_downloaded_file}")
                case_details['original_filename'] = Path(current_downloaded_file.name).stem
            else:
                self.logger.info(
                    f"{log_prefix} No file from download event, or save failed. Checking for inline PDF as fallback...")
                if page.is_closed():
                    self.logger.warning(
                        f"{log_prefix} Page is closed, cannot check for inline PDF. Download has failed if no event file received.")
                    raise Exception(
                        "Page closed and no file from download event; inline PDF check skipped, download failed.")

                await page.wait_for_timeout(self.config.get('inline_pdf_check_delay_ms', 3500))
                current_url = page.url
                is_pdf_page = False  # Initialize
                if ".pdf" in current_url.lower():  # Check URL first
                    is_pdf_page = True
                else:  # If URL doesn't indicate PDF, then check for embed/object/iframe elements
                    try:
                        if not page.is_closed():
                            pdf_elements_selector = "embed[type='application/pdf'], object[type='application/pdf'], iframe[src*='.pdf' i]"
                            # Wait for at least one matching element to be attached (or visible) within 7 seconds
                            await page.wait_for_selector(pdf_elements_selector, state="attached", timeout=7000)
                            # If the above doesn't throw TimeoutError, an element is attached.
                            # Check count for parity with original logic, though presence is often enough.
                            if await page.locator(pdf_elements_selector).count() > 0:
                                is_pdf_page = True
                    except PlaywrightTimeoutError:
                        self.logger.debug(
                            f"{log_prefix} Timeout waiting for inline PDF elements (embed, object, iframe).")
                    except PlaywrightError as e_inline_check:
                        self.logger.warning(f"{log_prefix} Error checking for inline PDF elements: {e_inline_check}")

                if is_pdf_page:
                    self.logger.info(
                        f"{log_prefix} Inline PDF suspected on page: {current_url}. Attempting page.pdf().")
                    try:
                        await page.keyboard.press("Escape", timeout=500)
                    except Exception:
                        pass
                    try:
                        await page.locator('body').first.focus(timeout=1000)  # Try to ensure page focus
                    except Exception:
                        pass
                    await page.wait_for_timeout(700)

                    original_fn_inline = f"{base_filename}.pdf"
                    if "show_temp.pl" in current_url:
                        match = re.search(r"file=([^&]+)", current_url, re.IGNORECASE)
                        if match and Path(match.group(1)).name.lower().endswith(".pdf"): original_fn_inline = Path(
                            match.group(1)).name
                    elif Path(current_url.split('?')[0]).suffix.lower() == '.pdf':
                        original_fn_inline = Path(current_url.split('?')[0]).name

                    case_details['original_filename'] = Path(original_fn_inline).stem

                    pdf_target = temp_dl_dir / f"inline_pdf_{uuid.uuid4().hex[:6]}_{Path(original_fn_inline).name}"
                    try:
                        # Increased timeout for page.pdf as it can be slow for large inline documents
                        pdf_bytes = await page.pdf(format="Letter", print_background=True, timeout=90000)  # 90 seconds
                        if pdf_bytes and len(pdf_bytes) > 100:
                            with open(pdf_target, 'wb') as f:
                                f.write(pdf_bytes)
                            self.logger.info(f"{log_prefix} Inline PDF saved via page.pdf() to: {pdf_target}")
                            current_downloaded_file = pdf_target
                        else:
                            self.logger.error(
                                f"{log_prefix} page.pdf() returned empty or very small data. Size: {len(pdf_bytes) if pdf_bytes else 'None'}")
                    except PlaywrightError as e_pdf_save:
                        self.logger.error(f"{log_prefix} page.pdf() save failed: {e_pdf_save}")
                    except Exception as e_pdf_generic:
                        self.logger.error(f"{log_prefix} Generic error during page.pdf() or write: {e_pdf_generic}",
                                          exc_info=True)
                else:
                    self.logger.warning(f"{log_prefix} Not an obvious inline PDF page. URL: {current_url}")

            if not current_downloaded_file or not await asyncio.to_thread(current_downloaded_file.exists):
                self.logger.error(f"{log_prefix} No valid file obtained after all attempts (event or inline PDF).")
                raise Exception("No valid file obtained from download event or inline PDF.")

            if not await self._wait_for_file_stabilization(current_downloaded_file, docket_num):
                self.logger.error(
                    f"{log_prefix} File stabilization failed for {current_downloaded_file.name} or it's zero-byte.")
                raise Exception(f"File stabilization failed for {current_downloaded_file.name} or zero-byte")

            # Ensure original_filename for finalize_download is the actual name of the downloaded file
            original_filename_for_finalize = case_details.get('original_filename',
                                                              Path(current_downloaded_file.name).stem)
            # Add extension back if it was removed, or use the full name from the downloaded file
            if Path(current_downloaded_file.name).suffix:
                original_filename_for_finalize = Path(current_downloaded_file.name).name
            else:  # Should not happen if original_fn was derived correctly
                original_filename_for_finalize = f"{original_filename_for_finalize}{Path(current_downloaded_file.name).suffix or '.unknown'}"

            final_path_str = await self.file_manager.finalize_download(
                str(current_downloaded_file), self.iso_date, base_filename,
                original_filename_for_finalize,
                self.config.get('zip_pdf_downloads', True)
            )

            if final_path_str:
                case_details['new_filename'] = Path(final_path_str).stem
                self.logger.info(f"{log_prefix} Download sequence successful. Final file at: {final_path_str}")
            else:
                raise Exception("PacerFileManager.finalize_download failed to produce a final path.")

        except Exception as e:
            self.logger.error(f"{log_prefix} Overall download sequence error for {base_filename}: {e}", exc_info=True)
            if self.navigator and self.navigator.is_ready and not page.is_closed():
                try:
                    await self.navigator.save_screenshot(f"download_SEQ_ERROR_{base_filename}_{attempt_uuid}")
                except PlaywrightError as ss_err:
                    self.logger.warning(f"{log_prefix} Screenshot failed after sequence error: {ss_err}")
            final_path_str = None
        finally:
            if page and not page.is_closed():
                try:
                    page.remove_listener("download", on_download_event)
                    self.logger.debug(f"{log_prefix} PW 'download' listener removed.")
                except Exception:
                    pass

            if await asyncio.to_thread(temp_dl_dir.exists):
                try:
                    await asyncio.to_thread(shutil.rmtree, temp_dl_dir)
                    self.logger.debug(f"{log_prefix} Cleaned up temp download staging dir: {temp_dl_dir}")
                except Exception as e_rm:
                    self.logger.warning(f"{log_prefix} Failed to cleanup temp staging dir {temp_dl_dir}: {e_rm}")

        return bool(final_path_str)
