# src/lib/receipt_logger.py

import asyncio
import json
import logging
import os
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional
import re
import fasteners
import logging

class ReceiptLogger:
    """Handles logging transaction receipt data to a JSON Lines file with process locking."""

    def __init__(self, base_data_dir: Path, iso_date: str, logger: Optional[logging.Logger] = None):
        self.base_data_dir = base_data_dir
        self.iso_date = iso_date
        self.log_dir = self.base_data_dir / self.iso_date / 'logs'
        self.log_dir.mkdir(parents=True, exist_ok=True) # Ensure log directory exists

        self.log_file_path = self.log_dir / f"transaction_receipts_{iso_date}.jsonl"
        self.lock_path = self.log_file_path.with_suffix('.jsonl.lock')

        # Use inter-process lock for process-safe file appending
        # Timeout is necessary in case a process dies while holding the lock
        self.process_lock = fasteners.InterProcessLock(str(self.lock_path), timeout=30)

        if logger:
            self.logger = logger
        else:
            # Fallback to a default logger for this component
            self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        self.logger.info(f"{self.__class__.__name__}.{iso_date} initialized.")

    async def log_receipt(self, receipt_data: Dict[str, Any]):
        """Appends a receipt record to the JSON Lines file."""
        if not isinstance(receipt_data, dict):
            self.logger.error(f"Invalid receipt_data format provided: {type(receipt_data)}")
            return

        # Add metadata to the receipt data
        record_to_log = receipt_data.copy()
        record_to_log.update({
            '_log_timestamp_utc': datetime.utcnow().isoformat(), # Use UTC timestamp
            '_process_id': os.getpid(),
            '_thread_id': asyncio.current_task().get_name() if asyncio.current_task() else "main_thread",
        })

        # Ensure all values are JSON serializable (e.g., dates as strings) - json.dump with default=str handles this
        json_record = json.dumps(record_to_log, default=str)

        try:
            # Acquire process lock before writing to the file
            # Use asyncio.to_thread to run the blocking lock acquisition and file I/O
            lock_acquired = await asyncio.to_thread(self.process_lock.acquire, blocking=True, timeout=15)
            if not lock_acquired:
                 self.logger.error(f"Timeout acquiring process lock for {self.log_file_path}. Cannot log receipt.")
                 return # Failed to acquire lock

            def append_to_file_sync():
                # Append the JSON string followed by a newline
                with open(self.log_file_path, 'a') as f:
                    f.write(json_record + '\n')
                self.logger.debug(f"Logged receipt to {self.log_file_path}")

            await asyncio.to_thread(append_to_file_sync)

        except fasteners.exceptions.InterProcessLockTimeout:
            self.logger.error(f"Timeout acquiring process lock for {self.log_file_path}.")
        except Exception as e:
            self.logger.error(f"Error writing receipt data to {self.log_file_path}: {e}", exc_info=True)
        finally:
            # Release process lock if it was acquired
            if 'lock_acquired' in locals() and lock_acquired:
                try:
                    await asyncio.to_thread(self.process_lock.release)
                except Exception as release_err:
                    self.logger.error(f"Error releasing process lock for {self.log_file_path}: {release_err}")


# Helper function to extract data from the HTML table
def extract_receipt_data_from_html(html_content: str) -> Optional[Dict[str, Any]]:
    """
    Parses HTML content to find the transaction receipt table and extract data.
    Uses simple string searching/regex for robustness across minor HTML variations.
    """
    # Use a very broad pattern to find the table or its key content markers
    receipt_pattern = re.compile(
        r'<table.*?border="1".*?bgcolor="white".*?width="400".*?>.*?PACER Service Center.*?Transaction Receipt.*?<\/table>',
        re.DOTALL | re.IGNORECASE
    )

    match = receipt_pattern.search(html_content)
    if not match:
        return None # No receipt table found

    receipt_html = match.group(0) # The full table HTML

    data = {}

    # Extract Date/Time (might be outside the main label/value rows)
    date_match = re.search(r'>\s*(\d{2}/\d{2}/\d{4}\s+\d{2}:\d{2}:\d{2})\s*<', receipt_html, re.IGNORECASE)
    if date_match:
        data['TimestampRaw'] = date_match.group(1).strip()
        try:
            # Attempt to parse into ISO format for consistency, but keep raw
            dt_obj = datetime.strptime(data['TimestampRaw'], '%m/%d/%Y %H:%M:%S')
            data['TimestampISO'] = dt_obj.isoformat()
        except ValueError:
             pass # Parsing failed, keep only raw

    # Extract labelled fields using regex
    fields_to_extract = {
        'PACER Login': 'PacerLogin',
        'Client Code': 'ClientCode',
        'Description': 'Description',
        'Search Criteria': 'SearchCriteria',
        'Billable Pages': 'BillablePages',
        'Cost': 'Cost',
    }

    for label, key in fields_to_extract.items():
        # Look for the label followed by </td> and then the value in the next td
        # This regex is sensitive to cell structure but handles whitespace variations
        label_pattern = re.compile(
            rf'<th[^>]*?>\s*<font[^>]*?>\s*{re.escape(label)}\s*</font>\s*</th>\s*<td[^>]*?>\s*<font[^>]*?>\s*(.*?)\s*</font>\s*</td>',
            re.DOTALL | re.IGNORECASE
        )
        value_match = label_pattern.search(receipt_html)
        if value_match:
            value = value_match.group(1).strip()
            data[key] = value
        else:
            # Fallback: look for label in a td and value in the next td
             label_pattern_td = re.compile(
                rf'<td[^>]*?>\s*<font[^>]*?>\s*{re.escape(label)}\s*</font>\s*</td>\s*<td[^>]*?>\s*<font[^>]*?>\s*(.*?)\s*</font>\s*</td>',
                re.DOTALL | re.IGNORECASE
            )
             value_match_td = label_pattern_td.search(receipt_html)
             if value_match_td:
                 value = value_match_td.group(1).strip()
                 data[key] = value

    # Clean Cost and Billable Pages (convert to float/int if possible)
    try:
        data['CostValue'] = float(data.get('Cost', '0').replace('$', '').strip())
    except (ValueError, TypeError):
        data['CostValue'] = 0.0

    try:
        data['BillablePagesValue'] = int(data.get('BillablePages', '0').strip())
    except (ValueError, TypeError):
        data['BillablePagesValue'] = 0

    if data:
        # Basic check: must have a description, cost, and pages
        if data.get('Description') and data.get('Cost') and data.get('BillablePages'):
            return data
        else:
            # Log incomplete receipt found?
            # logging.getLogger(__name__).warning(f"Found potential receipt but missing key fields: {data}")
            return None # Don't return incomplete data

    return None # Should be caught by if data: check, but safety