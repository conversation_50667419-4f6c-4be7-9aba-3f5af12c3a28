import logging
import json
import os
import re
from datetime import datetime
from typing import List, Dict, Any, Optional

from playwright.async_api import (<PERSON>rro<PERSON> as PlaywrightError,
                                  TimeoutError as PlaywrightTimeoutError)

from .navigator import Pacer<PERSON>avigator
from .pacer_utils import retry_async
from src.utils.json_safety import safe_json_write


class ReportHandler:
    """Navigates to and configures PACER reports."""

    def __init__(self, navigator: PacerNavigator, court_id: str, from_date_str: str, to_date_str: str,
                 logger: logging.Logger):
        self.navigator: PacerNavigator = navigator
        self.court_id: str = court_id
        self.from_date_str: str = from_date_str  # Expected mm/dd/yy format
        self.to_date_str: str = to_date_str  # Expected mm/dd/yy format
        # self.logger: logging.Logger = logging.getLogger(f"{__name__}.ReportHandler.{court_id}")
        if logger:
            self.logger = logger
        else:
            # Fallback to a default logger for this component
            self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        self.logger.info(f"{__name__}.ReportHandler.{court_id} initialized.")

        # --- Define ALL selectors and constants as INSTANCE attributes ---
        # These names now EXACTLY match the class-level constants from your provided file snippet.

        self.REPORTS_MENU_SELECTOR: str = "a:text('Reports')"
        self.CIVIL_CASES_LINK_SELECTOR: str = "a:text('Civil Cases')"
        self.CIVIL_LINK_SELECTOR: str = "a:text('Civil')"
        self.CASES_FILED_REGEX_SELECTOR: str = "a:text-matches('/^Case(s)? Filed/i')"

        # This composite list should be built from the individual selectors above if you intend to iterate
        # For example, if REPORT_LINK_SELECTORS was meant to be a collection of the above for iteration:
        self.REPORT_LINK_SELECTORS: list[str] = [
            self.CIVIL_CASES_LINK_SELECTOR,
            self.CIVIL_LINK_SELECTOR,
            self.CASES_FILED_REGEX_SELECTOR,
            # Add any other selectors that belong in this iterative list
        ]

        self.DATE_INPUT_SELECTOR: str = "input[name*='date'], input[name*='filed']"
        # Adding the specific date selectors that were missed for direct use in _fill_date_fields
        self.DATE_FROM_PRIMARY_SELECTOR: str = "input[name='filed_from']"
        self.DATE_TO_PRIMARY_SELECTOR: str = "input[name='filed_to']"
        self.DATE_FROM_ALT_SELECTOR: str = "input[name='date_from']"
        self.DATE_TO_ALT_SELECTOR: str = "input[name='date_to']"

        self.NATURE_OF_SUIT_SELECTOR: str = "select[name='nature_of_suit']"
        self.CASE_TYPE_SELECTOR: str = "select[name='case_type']"
        self.CASE_FLAGS_SELECTOR: str = "select[name='case_flags']"
        self.CLOSED_CASES_SELECTOR: str = "input[name='closed_cases']"

        self.RUN_REPORT_BUTTON_SELECTORS: list[str] = ["[name='button1']"]

        self.RESULTS_TABLE_SELECTOR: str = "#cmecfMainContent table:has(a[href*='DktRpt.pl'])"
        self.RESULTS_TABLE_DATA_ROW_SELECTOR: str = self.RESULTS_TABLE_SELECTOR + " tbody tr:nth-child(n+2)"
        self.NO_CASES_FOUND_SELECTOR: str = "text=/no cases found|Total number of cases reported: 0|Sorry - no data for the chosen selection criteria/i"

        # Adding the XPATH selector that was missed
        self.XPATH_DATA_ROW_WITH_LINK_SELECTOR: str = (
            "//div[@id='cmecfMainContent']//tr["
            ".//a[contains(@href, 'DktRpt.pl') or contains(@href, 'iquery.pl')]"
            "]"
        )

        # Configuration Data (previously class-level, now instance attributes)
        self.COURT_CASE_TYPES: dict[str, list[str]] = {
            'nced': ['cv', 'MDL'], 'ohnd': ['cv', 'sf', 'dp'],
            'ilnd': ['cv', 'ad'], 'ilsd': ['cv', 'pq'],
            'insd': ['cv', 'ml'], 'nyed': ['cv', 'al', 'bi'],
            'gand': ['cv']
            # Ensure this list is complete based on your original class definition
        }
        self.DEFAULT_CASE_TYPES: list[str] = ['cv']

        self.COURT_CASE_FLAGS: dict[str, list[str]] = {
            'ared': ['MDL-2949'], 'azd': ['MULTI_DISTRICT'],
            'ilnd': ['MDL 3060', 'MDL 3026', 'MDL 3037', 'MDL 3060', 'MDL 3079'],
            'scd': ['MDL'], 'njd': ['MDL2738', 'MDL2789', 'MDL2875', 'MDL2921', 'MDL2973', 'MDL3080'],
            'flsd': ['MDL'], 'insd': ['MDL'], 'laed': ['TAXOTERE', 'TAXOTERE EYE', 'XARELTO'],
            'mad': ['MDL', 'MDL3029', 'MDL2768'], 'mdd': ['MDL', '2775'],
            'mnd': ['ACETAMINOPHEN', 'BAIR', 'HIPIMPLANT'], 'moed': ['MDL2820'],
            'ncwd': ['ASBESTOS', 'MDL-3036', 'MDL'], 'nhd': ['MDL', 'MDL-2753'],
            'nyed': ['MDL3044'], 'nysd': ['MDL'], 'ohnd': ['MDL2197', 'MDL3092'],
            'ohsd': ['MDL'], 'paed': ['MDL-3094', 'MDL-2848'],
            'pawd': ['MDL', 'MDL-MASTER', 'MDL3014'], 'txn': ['MDL2244']
            # Ensure this list is complete based on your original class definition
        }
        self.NATURE_OF_SUIT_OPTIONS: list[str] = ['365', '367', '368', '360', '240', '245', '470']
        # --- End of instance attribute selector definitions ---

    @retry_async(tries=3, delay=3, exceptions=(PlaywrightTimeoutError, PlaywrightError))
    async def navigate_to_report_menu(self):
        """Clicks the main 'Reports' menu item."""
        self.logger.info("Navigating to Reports menu...")
        await self.navigator.click(self.REPORTS_MENU_SELECTOR)
        await self.navigator.page.wait_for_timeout(1000)

    @retry_async(tries=3, delay=3, exceptions=(PlaywrightTimeoutError, PlaywrightError))
    async def navigate_to_case_filed_report(self):
        """Navigates to the 'Cases Filed' or 'Civil Cases' report page.
        Handles courts where 'Reports' click navigates to an intermediate page.
        Applies specific YUI menu clearing for ilcd, wvnd, ned, miwd.
        """
        self.logger.info(f"Navigating to Case Filed/Civil report for court {self.court_id}...")

        # Step 1: Click the main "Reports" menu item.
        self.logger.info(
            f"Clicking main 'Reports' menu item for {self.court_id} (expecting navigation for some UIs)...")
        try:
            async with self.navigator.page.expect_navigation(wait_until="domcontentloaded", timeout=25000):
                await self.navigator.click(self.REPORTS_MENU_SELECTOR, wait_for_nav=False, timeout_override_ms=15000)
            self.logger.info(
                f"Clicked '{self.REPORTS_MENU_SELECTOR}'. Current page after 'Reports' click: {self.navigator.page.url}")
        except PlaywrightError as e:
            self.logger.error(f"Failed to click '{self.REPORTS_MENU_SELECTOR}' or navigate: {e}", exc_info=True)
            if self.navigator.page and not self.navigator.page.is_closed():
                await self.navigator.save_screenshot(f"reports_menu_click_nav_fail_{self.court_id}")
            raise

        if not self.navigator.page or self.navigator.page.is_closed():
            self.logger.error(
                f"Page closed or invalid after clicking '{self.REPORTS_MENU_SELECTOR}' for {self.court_id}.")
            raise PlaywrightError(f"Page invalid after '{self.REPORTS_MENU_SELECTOR}' click for {self.court_id}")

        # Step 2: Conditional YUI menu clearing for specific courts.
        courts_needing_yui_clear = ['ilcd', 'wvnd', 'ned', 'miwd']
        if self.court_id.lower() in courts_needing_yui_clear:
            self.logger.info(f"Applying YUI menu clearing logic for court {self.court_id} on {self.navigator.page.url}")
            try:
                await self.navigator.page.keyboard.press("Escape")
                self.logger.info(f"Pressed Escape key for {self.court_id}.")
                await self.navigator.page.wait_for_timeout(500)

                body_locator = self.navigator.page.locator("body").first
                if await body_locator.count() > 0:
                    await body_locator.click(timeout=3000, no_wait_after=True, force=True)
                    self.logger.info(
                        f"Clicked body to attempt to remove focus from any active menu for {self.court_id}.")
                    await self.navigator.page.wait_for_timeout(300)
                else:
                    self.logger.debug(f"Body locator not found for focus click for {self.court_id}.")
            except PlaywrightError as e_clear:
                self.logger.warning(
                    f"Non-critical error attempting to clear YUI menus for {self.court_id}: {type(e_clear).__name__} - {str(e_clear).splitlines()[0]}")
            except Exception as e_unexp_clear:
                self.logger.warning(
                    f"Unexpected non-critical error during menu clear for {self.court_id}: {type(e_unexp_clear).__name__} - {str(e_unexp_clear)}")
        else:
            self.logger.info(f"Skipping YUI menu clearing logic for court {self.court_id} (not in specific list).")

        # Step 3: Iterate through scoped selectors to find the correct link on the reports landing page.
        # These selectors target links within div#cmecfMainContent.
        scoped_report_page_link_selectors = [
            f"div#cmecfMainContent a[href='/cgi-bin/CaseFiled-Rpt.pl']:text-matches('/Civil Cases/i')",
            f"div#cmecfMainContent a[href='/cgi-bin/CaseFiled-Rpt.pl']:text-matches('/^Civil Case(s)? Filed/i')",
            f"div#cmecfMainContent a:text-matches('/Civil Cases/i')",
            f"div#cmecfMainContent {self.CIVIL_CASES_LINK_SELECTOR}",
            f"div#cmecfMainContent {self.CIVIL_LINK_SELECTOR}",
            f"div#cmecfMainContent {self.CASES_FILED_REGEX_SELECTOR}",
        ]

        clicked_successfully = False
        for selector_index, selector_value in enumerate(scoped_report_page_link_selectors):
            self.logger.debug(
                f"On reports landing page, attempting SCOPED link selector #{selector_index + 1}: '{selector_value}' for {self.court_id}")

            if not self.navigator.page or self.navigator.page.is_closed():
                self.logger.warning(
                    f"Page became closed before attempting selector '{selector_value}' on reports landing page for {self.court_id}.")
                break

            try:
                link_locator_collection = await self.navigator.locator(selector_value)
                if await link_locator_collection.count() > 0:
                    link_locator_instance = link_locator_collection.first

                    self.logger.debug(
                        f"Waiting for link '{selector_value}' to be visible on reports landing page for {self.court_id}...")
                    try:
                        await link_locator_instance.wait_for(state='visible', timeout=12000)
                    except PlaywrightTimeoutError:
                        self.logger.debug(
                            f"Link '{selector_value}' not visible on reports landing page for {self.court_id}. Trying next.")
                        continue

                    self.logger.info(
                        f"Link '{selector_value}' is visible on reports landing page for {self.court_id}. Clicking (expecting navigation).")

                    async with self.navigator.page.expect_navigation(wait_until="domcontentloaded", timeout=30000):
                        await link_locator_instance.click(timeout=20000)

                    self.logger.info(
                        f"Successfully clicked and navigated from reports landing page using selector '{selector_value}' for {self.court_id}. New URL: {self.navigator.page.url}")
                    clicked_successfully = True
                    break
                else:
                    self.logger.debug(
                        f"Scoped report link selector '{selector_value}' not found on reports landing page for {self.court_id}.")

            except PlaywrightError as e:
                self.logger.warning(
                    f"Attempt on reports landing page with SCOPED selector '{selector_value}' for {self.court_id} failed: {type(e).__name__} - {str(e).splitlines()[0]}. Trying next selector.")
                if self.navigator.page and not self.navigator.page.is_closed():
                    await self.navigator.save_screenshot(
                        f"report_landing_scoped_link_click_error_{self.court_id}_{selector_index}")

        if not clicked_successfully:
            page_content_for_debug = "Page closed or content unavailable"
            current_url_for_debug = "Page closed or URL unavailable"
            if self.navigator.page and not self.navigator.page.is_closed():
                page_content_for_debug = await self.navigator.page.content()
                current_url_for_debug = self.navigator.page.url
                await self.navigator.save_screenshot(f"navigate_report_fail_all_scoped_selectors_{self.court_id}")
            self.logger.error(
                f"Failed to click any report link on the reports landing page for {self.court_id} after trying SCOPED selectors: {scoped_report_page_link_selectors}. Current page URL: {current_url_for_debug}. Page content snippet: {page_content_for_debug[:500]}")
            raise PlaywrightError(
                f"Failed to navigate from reports landing page for {self.court_id} (all scoped selectors failed).")

        # Step 4: Verification (now expecting to be on the actual report configuration form)
        if not self.navigator.page or self.navigator.page.is_closed():
            self.logger.error(f"Page is unexpectedly closed before final report page verification for {self.court_id}.")
            raise PlaywrightError(f"Page closed before final report verification for {self.court_id}")

        try:
            date_input_locator_collection = await self.navigator.locator(self.DATE_INPUT_SELECTOR)
            await date_input_locator_collection.first.wait_for(state='visible', timeout=15000)
            self.logger.info(
                f"Successfully navigated to and verified report configuration page for {self.court_id}: {self.navigator.page.url}")
        except PlaywrightError as e_verify:
            page_url_for_error = "UNKNOWN_URL (page closed)"
            page_content_for_error = "Page content unavailable"
            if self.navigator.page and not self.navigator.page.is_closed():
                page_url_for_error = self.navigator.page.url
                page_content_for_error = (await self.navigator.page.content())[:500]
                await self.navigator.save_screenshot(f"navigate_report_final_verify_fail_{self.court_id}")
            self.logger.error(
                f"Final verification of report page failed for {self.court_id}: Target element ('{self.DATE_INPUT_SELECTOR}') not found on {page_url_for_error}. Content: {page_content_for_error}. Error: {e_verify}",
                exc_info=True)
            raise

    def _get_court_case_type_options(self) -> List[str]:
        """Returns court-specific case type options for the report query."""
        return self.COURT_CASE_TYPES.get(self.court_id, self.DEFAULT_CASE_TYPES)

    async def _select_case_type(self):
        """Selects case type options if the dropdown exists and options are available."""
        desired_options = self._get_court_case_type_options()  # This calls your existing method
        if not desired_options:
            self.logger.debug(f"No specific case types configured for court {self.court_id}.")
            return

        try:
            # Corrected: await the result of self.navigator.locator()
            select_locator_obj = await self.navigator.locator(self.CASE_TYPE_SELECTOR)
            if await select_locator_obj.count() == 0:
                self.logger.debug(
                    f"Case type select element '{self.CASE_TYPE_SELECTOR}' not found for {self.court_id}. Skipping.")
                return

            # select_locator_obj is now the Locator object for the <select> element
            option_elements = await select_locator_obj.locator("option").all()
            available_option_values = []
            for opt_element in option_elements:
                val = await opt_element.get_attribute("value")
                if val is not None:
                    available_option_values.append(val)

            self.logger.debug(
                f"For {self.court_id}, desired case types: {desired_options}, available on page: {available_option_values}")

            options_to_actually_select = [opt for opt in desired_options if opt in available_option_values]

            if not options_to_actually_select:
                self.logger.warning(
                    f"None of the desired case types {desired_options} are available in dropdown '{self.CASE_TYPE_SELECTOR}' for {self.court_id}. "
                    f"Available options: {available_option_values}. Skipping case type selection."
                )
                return

            if set(options_to_actually_select) != set(desired_options):
                missing_options = list(set(desired_options) - set(options_to_actually_select))
                self.logger.warning(
                    f"Some desired case types for {self.court_id} are not available: {missing_options}. "
                    f"Will attempt to select: {options_to_actually_select}"
                )

            # navigator.select_option takes the selector string, not the Locator object directly for this action.
            self.logger.info(
                f"Attempting to select case types for {self.court_id} in '{self.CASE_TYPE_SELECTOR}': {options_to_actually_select}")
            await self.navigator.select_option(self.CASE_TYPE_SELECTOR, options_to_actually_select)
            self.logger.info(
                f"Successfully initiated selection for case types: {options_to_actually_select} for {self.court_id}")

        except PlaywrightError as e:
            self.logger.warning(
                f"Non-critical PlaywrightError during case type selection for {self.court_id} using selector '{self.CASE_TYPE_SELECTOR}': {e}",
                exc_info=True
            )
        except Exception as e:
            self.logger.error(
                f"Unexpected error selecting case types for {self.court_id} using selector '{self.CASE_TYPE_SELECTOR}': {e}",
                exc_info=True)

    def _get_court_case_flag_options(self) -> List[str]:
        """Returns court-specific case flag options."""
        return self.COURT_CASE_FLAGS.get(self.court_id, [])

    async def _select_case_flags(self):
        """Selects case flag options if the dropdown exists and options are defined."""
        flag_options = self._get_court_case_flag_options()  # This calls your existing method
        if not flag_options:
            self.logger.debug(f"No case flags defined for {self.court_id}.")
            return
        try:
            # Corrected: await the result of self.navigator.locator()
            flags_locator_obj = await self.navigator.locator(self.CASE_FLAGS_SELECTOR)
            if await flags_locator_obj.count() > 0:
                option_elements = await flags_locator_obj.locator("option").all()
                available_flag_values = []
                for opt_element in option_elements:
                    val = await opt_element.get_attribute("value")
                    if val is not None:
                        available_flag_values.append(val)

                self.logger.debug(
                    f"For {self.court_id}, desired case flags: {flag_options}, available on page: {available_flag_values}")

                flags_to_actually_select = [opt for opt in flag_options if opt in available_flag_values]

                if not flags_to_actually_select:
                    self.logger.warning(
                        f"None of the desired case flags {flag_options} are available in dropdown '{self.CASE_FLAGS_SELECTOR}' for {self.court_id}. "
                        f"Available options: {available_flag_values}. Skipping case flag selection."
                    )
                    return

                if set(flags_to_actually_select) != set(flag_options):
                    missing_flags = list(set(flag_options) - set(flags_to_actually_select))
                    self.logger.warning(
                        f"Some desired case flags for {self.court_id} are not available: {missing_flags}. "
                        f"Will attempt to select: {flags_to_actually_select}"
                    )

                self.logger.info(
                    f"Attempting to select case flags for {self.court_id} in '{self.CASE_FLAGS_SELECTOR}': {flags_to_actually_select}")
                await self.navigator.select_option(self.CASE_FLAGS_SELECTOR, flags_to_actually_select)
                self.logger.info(
                    f"Successfully initiated selection for case flags: {flags_to_actually_select} for {self.court_id}")
            else:
                self.logger.debug(
                    f"Case flags dropdown '{self.CASE_FLAGS_SELECTOR}' not found for {self.court_id}. Skipping.")
        except PlaywrightError as e:
            self.logger.warning(
                f"Non-critical error selecting case flags for {self.court_id} using '{self.CASE_FLAGS_SELECTOR}': {e}",
                exc_info=True)
        except Exception as e:
            self.logger.error(
                f"Unexpected error selecting case flags for {self.court_id} using '{self.CASE_FLAGS_SELECTOR}': {e}",
                exc_info=True)

    async def _check_include_closed_cases(self):
        """Ensures 'Include closed cases' is checked if present and court_id is 'gand'."""
        try:
            if self.court_id != 'gand':  # Your original condition
                self.logger.debug(f"Skipping 'Include closed cases' check for {self.court_id} (not gand).")
                return

            # Corrected: await the result of self.navigator.locator()
            closed_cases_locator_obj = await self.navigator.locator(self.CLOSED_CASES_SELECTOR)
            if await closed_cases_locator_obj.count() > 0:
                # PacerNavigator.check takes the selector string
                await self.navigator.check(self.CLOSED_CASES_SELECTOR)
                self.logger.debug(
                    f"Ensured 'Include closed cases' ('{self.CLOSED_CASES_SELECTOR}') is checked for {self.court_id}.")
            else:
                self.logger.debug(
                    f"'Include closed cases' checkbox ('{self.CLOSED_CASES_SELECTOR}') not found for {self.court_id}.")
        except PlaywrightError as e:
            self.logger.warning(
                f"Could not check 'closed_cases' checkbox ('{self.CLOSED_CASES_SELECTOR}') for {self.court_id}: {e}",
                exc_info=True)
        except Exception as e:
            self.logger.error(
                f"Unexpected error checking 'closed_cases' ('{self.CLOSED_CASES_SELECTOR}') for {self.court_id}: {e}",
                exc_info=True)

    @retry_async(tries=2, delay=3, exceptions=(PlaywrightTimeoutError, PlaywrightError))
    async def configure_case_filed_report(self):
        """Fills the date range and selects options on the report form."""
        self.logger.info(
            f"Configuring Case Filed report for {self.court_id} ({self.from_date_str} - {self.to_date_str})...")
        await self._fill_date_fields()  # This calls the corrected _fill_date_fields
        try:
            # Corrected: await the result of self.navigator.locator()
            nos_locator_obj = await self.navigator.locator(self.NATURE_OF_SUIT_SELECTOR)
            if await nos_locator_obj.count() > 0:
                # self.NATURE_OF_SUIT_OPTIONS should be defined in __init__
                if self.NATURE_OF_SUIT_OPTIONS:  # Check if list is not empty
                    await self.navigator.select_option(self.NATURE_OF_SUIT_SELECTOR, self.NATURE_OF_SUIT_OPTIONS)
                    self.logger.info(
                        f"Selected Nature of Suit options for '{self.NATURE_OF_SUIT_SELECTOR}': {self.NATURE_OF_SUIT_OPTIONS}")
                else:
                    self.logger.debug(
                        f"Nature of Suit selector '{self.NATURE_OF_SUIT_SELECTOR}' found, but no NATURE_OF_SUIT_OPTIONS defined to select.")
            else:
                self.logger.debug(
                    f"Nature of Suit selector '{self.NATURE_OF_SUIT_SELECTOR}' not found. Skipping NOS selection.")
        except PlaywrightError as e:
            self.logger.warning(
                f"Non-critical error selecting Nature of Suit using '{self.NATURE_OF_SUIT_SELECTOR}': {e}",
                exc_info=True)

        await self._select_case_type()  # Calls corrected helper
        await self._select_case_flags()  # Calls corrected helper
        await self._check_include_closed_cases()  # Calls corrected helper
        self.logger.info(f"Report configuration complete for {self.court_id}.")

    async def _fill_date_fields(self):
        """Fills the appropriate date fields."""
        # Using specific date selectors defined in __init__
        date_field_pairs_selectors = [
            (self.DATE_FROM_PRIMARY_SELECTOR, self.DATE_TO_PRIMARY_SELECTOR),
            (self.DATE_FROM_ALT_SELECTOR, self.DATE_TO_ALT_SELECTOR)
        ]
        filled = False
        for from_selector, to_selector in date_field_pairs_selectors:
            try:
                # Corrected: await the result of self.navigator.locator() for each part
                from_locator_obj = await self.navigator.locator(from_selector)
                to_locator_obj = await self.navigator.locator(to_selector)

                if await from_locator_obj.count() > 0 and await to_locator_obj.count() > 0:
                    await self.navigator.fill(from_selector, self.from_date_str)
                    await self.navigator.fill(to_selector, self.to_date_str)
                    self.logger.info(f"Filled date fields using selectors '{from_selector}' and '{to_selector}'.")
                    filled = True
                    break  # Exit loop once a pair is successfully filled
            except PlaywrightError as e:
                self.logger.debug(
                    f"Date field pair using selectors '{from_selector}'/'{to_selector}' not found or failed to fill: {e}")

        if not filled:
            self.logger.error("Failed to find and fill any known date field pairs.")
            if self.navigator.page and not self.navigator.page.is_closed():  # Check page state
                await self.navigator.save_screenshot("fill_date_fail_all_pairs")
            raise PlaywrightError("Could not fill date fields on report after trying defined pairs.")

    async def run_report(self) -> bool:
        """
        Runs the configured report by clicking the 'Run Report' button and handling navigation.
        Then calls _verify_report_content to check the results.
        Returns True if the report has cases, False if no cases or an error preventing result determination.
        """
        self.logger.info("Attempting to run the report...")
        page = self.navigator.page

        # Check if this court has ignore_download entries and fill document numbering fields if needed
        try:
            from .docket_processor import DocketProcessor
            ignore_download_config = DocketProcessor.load_ignore_download_config()
            if DocketProcessor.check_ignore_download_static(self.court_id, ignore_download_config):
                self.logger.info(f"Court {self.court_id} has ignore_download entries. Filling document numbering fields with '1'")
                
                # Fill documents_numbered_from_ field
                try:
                    await page.fill('input[name="documents_numbered_from_"]', '1')
                    self.logger.debug(f"Filled documents_numbered_from_ with '1'")
                except Exception as e:
                    self.logger.debug(f"Could not fill documents_numbered_from_ field: {e}")
                
                # Fill documents_numbered_to_ field  
                try:
                    await page.fill('input[name="documents_numbered_to_"]', '1')
                    self.logger.debug(f"Filled documents_numbered_to_ with '1'")
                except Exception as e:
                    self.logger.debug(f"Could not fill documents_numbered_to_ field: {e}")
                    
        except Exception as e:
            self.logger.warning(f"Error in ignore_download document numbering logic: {e}")

        # self.RUN_REPORT_BUTTON_SELECTORS should be a list defined in __init__
        if not self.RUN_REPORT_BUTTON_SELECTORS:
            self.logger.error("No RUN_REPORT_BUTTON_SELECTORS defined in ReportHandler.__init__.")
            raise ValueError("RUN_REPORT_BUTTON_SELECTORS is not defined or empty.")

        target_selector = self.RUN_REPORT_BUTTON_SELECTORS[0]  # Using the first one as per original logic
        self.logger.debug(f"Locating 'Run Report' button using primary selector: {target_selector}")

        try:
            # Corrected: await the result of self.navigator.locator()
            button_locator_collection = await self.navigator.locator(target_selector)
            if await button_locator_collection.count() == 0:
                self.logger.error(f"Run Report button not found with primary selector: {target_selector}")
                # Optionally, you could iterate through self.RUN_REPORT_BUTTON_SELECTORS here if it's a list of fallbacks
                raise PlaywrightError(f"Run Report button not found: {target_selector}")

            button_locator_instance = button_locator_collection.first
            await button_locator_instance.wait_for(state='visible', timeout=15000)
            if not await button_locator_instance.is_enabled(timeout=5000):
                self.logger.error(f"Run Report button '{target_selector}' found but is not enabled.")
                raise PlaywrightError(f"Run Report button '{target_selector}' not enabled.")

            self.logger.info(f"Button '{target_selector}' is visible and enabled. Clicking...")

            async with page.expect_navigation(wait_until="domcontentloaded", timeout=self.navigator.timeout + 30000):
                await button_locator_instance.click(timeout=15000)

            self.logger.info(f"Successfully clicked 'Run Report' ({target_selector}). New URL: {page.url}")

        except PlaywrightError as e_click_nav:
            self.logger.error(
                f"Critical PlaywrightError during 'Run Report' button click or navigation using '{target_selector}': {e_click_nav}",
                exc_info=True)
            if self.navigator.page and not self.navigator.page.is_closed():  # Check page state
                await self.navigator.save_screenshot(f"run_report_click_nav_critical_error_{self.court_id}")
            raise

        self.logger.info("Report run initiated, proceeding to verify content...")
        # Example: Using a fixed delay. If config was available, it would be self.config.get(...)
        report_load_delay_ms = 2000
        await page.wait_for_timeout(report_load_delay_ms)

        has_cases = await self._verify_report_content()  # Calls corrected helper

        # If report has cases, extract and save the report list
        if has_cases:
            await self._extract_and_save_report_list()

        return has_cases

    async def _verify_report_content(self) -> bool:
        """
        Verifies the content of the report results page.
        Checks for "no cases found" message or presence of data rows in the results table.
        Returns True if data rows are present, False otherwise.
        """
        page = self.navigator.page
        log_prefix = f"[{self.court_id}] VerifyReport:"
        self.logger.debug(f"{log_prefix} Verifying report content on URL: {page.url}...")

        # 1. Check for "no cases found" message
        try:
            # Corrected: await the result of self.navigator.locator()
            no_cases_locator_obj = await self.navigator.locator(self.NO_CASES_FOUND_SELECTOR)
            # Iterate through all elements matching the "no cases" selector
            # Using .all() which is fine, or just check visibility of .first if only one is expected
            all_no_cases_elements = await no_cases_locator_obj.all()  # Gets a list of Locator objects
            for el_locator in all_no_cases_elements:
                if await el_locator.is_visible(timeout=7000):  # Check visibility of each potential element
                    no_cases_text_found = await el_locator.text_content()
                    self.logger.info(f"{log_prefix} 'No cases found' message detected: '{no_cases_text_found}'.")
                    return False
        except PlaywrightError:  # TimeoutError if no element becomes visible within timeout
            self.logger.debug(
                f"{log_prefix} No 'no cases found' message detected quickly via '{self.NO_CASES_FOUND_SELECTOR}', or error during check. Proceeding to check for data rows.")

        # 2. If no "no cases" message, look for actual data rows using XPath.
        row_detection_timeout_ms = max(self.navigator.timeout, 60000)
        # self.XPATH_DATA_ROW_WITH_LINK_SELECTOR should be defined in __init__

        self.logger.debug(
            f"{log_prefix} Waiting for at least one data row matching XPath: '{self.XPATH_DATA_ROW_WITH_LINK_SELECTOR}' with timeout {row_detection_timeout_ms}ms")

        try:
            # Corrected: await the result of self.navigator.locator()
            data_rows_locator_obj = await self.navigator.locator(self.XPATH_DATA_ROW_WITH_LINK_SELECTOR)
            await data_rows_locator_obj.first.wait_for(state="visible", timeout=row_detection_timeout_ms)

            count = await data_rows_locator_obj.count()

            if count > 0:
                self.logger.info(
                    f"{log_prefix} Found {count} data row(s) with docket links using XPath '{self.XPATH_DATA_ROW_WITH_LINK_SELECTOR}'. Cases present.")
                return True
            else:
                self.logger.warning(
                    f"{log_prefix} XPath '{self.XPATH_DATA_ROW_WITH_LINK_SELECTOR}' matched a row initially (wait_for succeeded), but subsequent count is 0. This is unexpected. Assuming no cases to be safe.")
                if self.navigator.page and not self.navigator.page.is_closed():
                    await self.navigator.save_screenshot(f"verify_report_xpath_row_count_issue_{self.court_id}")
                return False

        except PlaywrightTimeoutError:
            self.logger.error(
                f"{log_prefix} Timeout ({row_detection_timeout_ms}ms) waiting for data rows via XPath ('{self.XPATH_DATA_ROW_WITH_LINK_SELECTOR}') AND no 'no cases found' message was seen. Assuming no usable report results.")
            if self.navigator.page and not self.navigator.page.is_closed():
                await self.navigator.save_screenshot(f"verify_report_data_rows_xpath_timeout_{self.court_id}")
            return False
        except PlaywrightError as e_row_check:
            self.logger.error(
                f"{log_prefix} PlaywrightError while checking for data rows via XPath ('{self.XPATH_DATA_ROW_WITH_LINK_SELECTOR}'): {e_row_check}",
                exc_info=True)
            if self.navigator.page and not self.navigator.page.is_closed():
                await self.navigator.save_screenshot(f"verify_report_data_rows_xpath_error_{self.court_id}")
            return False

    async def _extract_and_save_report_list(self) -> None:
        """
        Extracts case data from the report results table and saves it as a JSON file.
        Saves to data/YYYYMMDD/logs/docket_report_list_{court_id}.json
        """
        page = self.navigator.page
        log_prefix = f"[{self.court_id}] ExtractReportList:"
        self.logger.info(f"{log_prefix} Extracting report list data...")

        try:
            # Get all data rows using the same XPath selector used in verification
            data_rows_locator_obj = await self.navigator.locator(self.XPATH_DATA_ROW_WITH_LINK_SELECTOR)
            row_count = await data_rows_locator_obj.count()

            if row_count == 0:
                self.logger.warning(f"{log_prefix} No data rows found to extract.")
                return

            report_list = []

            # Extract data from each row
            for i in range(row_count):
                row_locator = data_rows_locator_obj.nth(i)
                try:
                    row_data = await self._extract_single_row_data(row_locator, i + 1, log_prefix)
                    if row_data:
                        report_list.append(row_data)
                except Exception as e:
                    self.logger.warning(f"{log_prefix} Error extracting data from row {i + 1}: {e}")
                    continue

            if report_list:
                await self._save_report_list_to_json(report_list, log_prefix)
                self.logger.info(f"{log_prefix} Successfully extracted and saved {len(report_list)} cases.")
            else:
                self.logger.warning(f"{log_prefix} No valid case data extracted from report.")

        except Exception as e:
            self.logger.error(f"{log_prefix} Error extracting report list: {e}", exc_info=True)
            if self.navigator.page and not self.navigator.page.is_closed():
                await self.navigator.save_screenshot(f"extract_report_list_error_{self.court_id}")

    async def _extract_single_row_data(self, row_locator, row_num: int, log_prefix: str) -> Optional[Dict[str, Any]]:
        """
        Extracts data from a single report table row.
        Returns a dictionary with case information or None if extraction fails.
        """
        try:
            await row_locator.wait_for(state='attached', timeout=5000)
            cell_locators = await row_locator.locator("td").all()

            if len(cell_locators) < 2:
                self.logger.debug(f"{log_prefix} Row {row_num}: Found fewer than 2 cells. Skipping row.")
                return None

            # Extract docket link and number
            docket_link_loc = cell_locators[0].locator("a").first
            if await docket_link_loc.count() == 0:
                self.logger.debug(f"{log_prefix} Row {row_num}: Could not find docket link element. Skipping.")
                return None

            docket_text = await docket_link_loc.text_content(timeout=3000)
            docket_href = await docket_link_loc.get_attribute("href", timeout=3000)

            # Extract versus information
            versus_loc = cell_locators[0].locator("b").first
            versus_text = await versus_loc.text_content(timeout=3000) if await versus_loc.count() > 0 else "Unknown Versus"

            # Extract filing date from second cell
            date_cell_text = await cell_locators[1].text_content(timeout=3000)
            
            # Extract just the date part from text like "Case filed: MM/DD/YYYY"
            filing_date_cleaned = ""
            if date_cell_text:
                date_cell_cleaned = date_cell_text.strip()
                # Use regex to extract date pattern, similar to orchestrator logic
                date_match = re.search(r"(?:Filed:?\s*|Case\s+filed:?\s*)?(\d{1,2}/\d{1,2}/\d{2,4})", date_cell_cleaned, re.IGNORECASE)
                if date_match:
                    filing_date_cleaned = date_match.group(1)
                else:
                    # Fallback: if no pattern match, use original text (for backward compatibility)
                    filing_date_cleaned = date_cell_cleaned

            # Initialize case data
            case_data = {
                'court_id': self.court_id,
                'docket_num': docket_text.strip() if docket_text else "",
                'versus': versus_text.strip() if versus_text else "",
                'filing_date': filing_date_cleaned,
                'docket_link': docket_href.strip() if docket_href else "",
                'extracted_at': datetime.now().isoformat(),
                'source': 'PACER Case Report'
            }

            # Extract additional details from 4th cell if available (Cause, NOS, Case Flags)
            if len(cell_locators) >= 4:
                details_cell_loc = cell_locators[3]
                try:
                    inner_text = await details_cell_loc.evaluate("el => el.innerText") or ""
                    lines = inner_text.splitlines()

                    for line in lines:
                        line_clean = line.strip()
                        if line_clean.lower().startswith('cause:'):
                            case_data['cause'] = line_clean[len('cause:'):].strip()
                        elif line_clean.lower().startswith('nos:'):
                            case_data['nos'] = line_clean[len('nos:'):].strip()
                        elif line_clean.lower().startswith('case flags:'):
                            case_data['case_flags'] = line_clean[len('case flags:'):].strip()

                except Exception as e_detail:
                    self.logger.debug(f"{log_prefix} Row {row_num}: Error extracting details from cell 4: {e_detail}")

            return case_data

        except Exception as e:
            self.logger.warning(f"{log_prefix} Row {row_num}: Error extracting row data: {e}")
            return None

    async def _save_report_list_to_json(self, report_list: List[Dict[str, Any]], log_prefix: str) -> None:
        """
        Saves the report list to a JSON file in the logs directory.
        File path: data/YYYYMMDD/logs/docket_report_list_{court_id}.json
        """
        try:
            # Determine the date for the directory structure
            # Use today's date in YYYYMMDD format
            today_date = datetime.now().strftime('%Y%m%d')

            # Create the logs directory path
            logs_dir = os.path.join('data', today_date, 'logs')
            os.makedirs(logs_dir, exist_ok=True)

            # Create the filename
            filename = f"docket_report_list_{self.court_id}.json"
            file_path = os.path.join(logs_dir, filename)

            # Prepare the data structure to save
            report_data = {
                'metadata': {
                    'court_id': self.court_id,
                    'generated_at': datetime.now().isoformat(),
                    'date_range': f"{self.from_date_str} to {self.to_date_str}",
                    'total_cases': len(report_list),
                    'source': 'PACER Case Filed Report'
                },
                'cases': report_list
            }

            # Save using safe JSON write
            success = safe_json_write(file_path, report_data, indent=2)

            if success:
                self.logger.info(f"{log_prefix} Successfully saved report list to: {file_path}")
            else:
                self.logger.error(f"{log_prefix} Failed to save report list to: {file_path}")

        except Exception as e:
            self.logger.error(f"{log_prefix} Error saving report list to JSON: {e}", exc_info=True)

    async def __aenter__(self):
        if not self.navigator.is_ready:
            await self.navigator._ensure_ready()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        pass
