"""
PACER (Public Access to Court Electronic Records) client and processing modules.
"""
import traceback # Add this import
from typing import TYPE_CHECKING

# The following imports are only for type checking / IDE linting support
# They aren't executed at runtime but help PyCharm understand our lazy loading pattern
if TYPE_CHECKING:  # pragma: no cover
    # noqa comments tell linters to ignore these imports
    from .orchestrator import PacerOrchestrator, load_config_local  # noqa: F401
    from .navigator import PacerNavigator  # noqa: F401
    from .file_manager import PacerFileManager  # noqa: F401
    from .docket_processor import DocketProcessor  # noqa: F401
    from .report_handler import ReportHandler  # noqa: F401
    from .browser_service import BrowserService  # noqa: F401
    from .case_relevance_engine import CaseRelevanceEngine  # noqa: F401
    from .case_transfer_handler import CaseTransferHandler  # noqa: F401
    from .pacer_document_downloader import PacerDocumentDownloader  # noqa: F401
    from .pacer_utils import get_court_ids_async  # noqa: F401

# Import components from the pacer directory
# Add or remove imports based on the actual modules in your src/lib/pacer directory

# Use runtime importing to prevent circular references
__all__ = [
    'PacerOrchestrator',
    'PacerNavigator',
    'PacerAuthenticator',
    'PacerFileManager',
    'DocketProcessor',
    'ReportHandler',
    'BrowserService',
    'CaseRelevanceEngine',
    'CaseTransferHandler',
    'PacerDocumentDownloader',
    'get_court_ids_async',
    'load_config_local'
]

# Early import for essential components to avoid circular references in orchestrator.py
from .authenticator import PacerAuthenticator

# Import components when the module is used, not on import
def __getattr__(name):
    """Lazily import src.lib.pacer components when they're accessed."""
    if name in __all__:
        original_exception = None
        try:
            if name == "PacerOrchestrator":
                from .orchestrator import PacerOrchestrator
                return PacerOrchestrator
            elif name == "PacerNavigator":
                from .navigator import PacerNavigator
                return PacerNavigator
            elif name == "PacerAuthenticator":
                # Already imported at module level
                return PacerAuthenticator
            elif name == "PacerFileManager":
                from .file_manager import PacerFileManager
                return PacerFileManager
            elif name == "DocketProcessor": # This is one of the components you're interested in
                from .docket_processor import DocketProcessor
                return DocketProcessor
            elif name == "ReportHandler":
                from .report_handler import ReportHandler
                return ReportHandler
            elif name == "BrowserService":
                from .browser_service import BrowserService
                return BrowserService
            elif name == "CaseRelevanceEngine":
                from .case_relevance_engine import CaseRelevanceEngine
                return CaseRelevanceEngine
            elif name == "CaseTransferHandler":
                from .case_transfer_handler import CaseTransferHandler
                return CaseTransferHandler
            elif name == "PacerDocumentDownloader":
                from .pacer_document_downloader import PacerDocumentDownloader
                return PacerDocumentDownloader
            elif name == "get_court_ids_async":
                from .pacer_utils import get_court_ids_async
                return get_court_ids_async
            elif name == "load_config_local":
                from .orchestrator import load_config_local
                return load_config_local
        except ImportError as e:
            original_exception = e
            print(f"ERROR: Failed to import PACER component '{name}' due to an internal ImportError.")
            print("Original ImportError traceback:")
            traceback.print_exc() # This will print the full traceback of the ImportError

        if original_exception:
            # Raise an AttributeError that includes the original error, making it clearer
            raise AttributeError(
                f"Module '{__name__}' has no attribute '{name}'. "
                f"Failed to lazily import due to: {original_exception}"
            ) from original_exception

    # If the attribute is not in __all__ or if import failed without catching ImportError above
    raise AttributeError(f"Module '{__name__}' has no attribute '{name}' (not listed in __all__ or unexpected error).")
