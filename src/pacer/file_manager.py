import asyncio
import json
import logging
import os
import re
import zipfile
from datetime import date as DateType, timedelta
from pathlib import Path
from typing import Dict, Any, Optional

import fasteners


class PacerFileManager:
    """Handles file system operations for docket files (JSON, PDF, ZIP) with locking.
    
    CRITICAL WARNING: DO NOT MODIFY THE ARTIFACT VERIFICATION METHODS WITHOUT UNDERSTANDING
    THE IMPACT ON FAILED DOWNLOAD REPROCESSING. These methods are essential for:
    - check_if_artifact_exists_by_pattern()
    - check_if_artifact_exists_last_7_days_by_pattern() 
    - check_if_artifact_exists_across_dates_by_pattern()
    
    These methods ONLY check for PDF/ZIP files and ignore JSON content to allow
    reprocessing of failed downloads. Modifying this logic will break reprocessing.
    """

    # Use slightly longer lock timeouts for file operations
    LOCK_TIMEOUT = 20  # seconds

    def __init__(self, base_data_dir: str):
        self.base_data_dir = Path(base_data_dir)  # Use pathlib.Path
        self.logger = logging.getLogger(f"{__name__}.PacerFileManager")
        self.async_lock = asyncio.Lock()  # Async lock for operations within this instance/process
        self._filename_cache = {}  # Cache for filename lists by date

    async def _get_cached_filenames(self, iso_date: str) -> list[str]:
        """Get cached filename list for a date or load if not cached."""
        if iso_date not in self._filename_cache:
            date_dir = self.base_data_dir / iso_date / 'dockets'
            if not await asyncio.to_thread(date_dir.exists):
                self._filename_cache[iso_date] = []
            else:
                try:
                    files = await asyncio.to_thread(list, date_dir.glob('*.json'))
                    # Store just the lowercased stems for fast pattern matching
                    self._filename_cache[iso_date] = [f.stem.lower() for f in files]
                except Exception as e:
                    self.logger.warning(f"Error loading filenames for {iso_date}: {e}")
                    self._filename_cache[iso_date] = []
        return self._filename_cache[iso_date]

    async def preload_filename_cache_for_7_days(self, end_date_obj: DateType) -> None:
        """Preload filename cache for the past 7 days to optimize batch operations."""
        tasks = []
        for i in range(7):
            check_date = end_date_obj - timedelta(days=i)
            iso_date = check_date.strftime('%Y%m%d')
            tasks.append(self._get_cached_filenames(iso_date))
        
        # Load all 7 days concurrently
        await asyncio.gather(*tasks)
        self.logger.debug(f"Preloaded filename cache for 7 days ending {end_date_obj.strftime('%Y%m%d')}")

    def _get_paths(self, iso_date: str, base_filename: str) -> Dict[str, Path]:
        """Generates standard paths for docket files using pathlib."""
        date_dir = self.base_data_dir / iso_date
        docket_dir = date_dir / 'dockets'
        html_dir = date_dir / 'html'
        temp_dir = docket_dir / 'temp'  # Define temp directory consistently
        lock_dir = docket_dir / '.locks'  # Centralized lock directory

        # Ensure lock directory exists
        lock_dir.mkdir(parents=True, exist_ok=True)

        return {
            "date_dir": date_dir,
            "docket_dir": docket_dir,
            "html_dir": html_dir,
            "temp_dir": temp_dir,
            "json": docket_dir / f"{base_filename}.json",
            "pdf": docket_dir / f"{base_filename}.pdf",
            "zip": docket_dir / f"{base_filename}.zip",
            "html": html_dir / f"{base_filename}.html",
            "lock": lock_dir / f"{base_filename}.lock"  # Per-file lock in central dir
        }

    async def check_if_downloaded(self, iso_date: str, base_filename: str) -> bool:
        """Checks if JSON and PDF/ZIP exist for a given docket."""
        paths = self._get_paths(iso_date, base_filename)
        # Run sync os.path.exists in thread pool
        json_exists = await asyncio.to_thread(paths["json"].exists)
        pdf_exists = await asyncio.to_thread(paths["pdf"].exists)
        zip_exists = await asyncio.to_thread(paths["zip"].exists)
        exists = json_exists and (pdf_exists or zip_exists)
        if exists:
            self.logger.debug(f"Files found for {base_filename} in {iso_date}")
        return exists

    async def check_if_downloaded_by_pattern(self, iso_date: str, court_id: str, docket_pattern: str) -> bool:
        """Optimized: Checks if files exist where filename STARTS WITH case-insensitive pattern {court_id}_NN_NNNNN."""
        # Use cached filename list instead of directory iteration
        cached_filenames = await self._get_cached_filenames(iso_date)
        if not cached_filenames:
            return False
            
        # Pattern to match: filename starts with {court_id}_{docket_pattern}
        # Example: flnd_25_00770_CLARK_et_al_v_PFIZER_INC_et_al.json starts with flnd_25_00770
        start_pattern = f"{court_id.lower()}_{docket_pattern.lower()}"
        
        # Use list comprehension for fast pattern matching
        matching_files = [f for f in cached_filenames if f.startswith(start_pattern)]
        
        if not matching_files:
            self.logger.debug(f"No files found matching pattern {court_id}_{docket_pattern} in {iso_date}")
            return False
        
        date_dir = self.base_data_dir / iso_date / 'dockets'
        
        # Check each matching file for PDF/ZIP or skip reasons
        for base_name in matching_files:
            try:
                pdf_path = date_dir / f"{base_name}.pdf"
                zip_path = date_dir / f"{base_name}.zip"
                
                pdf_exists = await asyncio.to_thread(pdf_path.exists)
                zip_exists = await asyncio.to_thread(zip_path.exists)
                
                if pdf_exists or zip_exists:
                    self.logger.debug(f"Found matching files for pattern {court_id}_{docket_pattern}: {base_name}")
                    return True
                
                # If no PDF/ZIP, check JSON for skip reasons
                json_path = date_dir / f"{base_name}.json"
                try:
                    json_content = await asyncio.to_thread(json_path.read_text)
                    case_data = json.loads(json_content)
                    
                    # Check for skip reasons
                    skip_reasons = [
                        case_data.get('_reason_review'),
                        case_data.get('_processing_notes'),
                        case_data.get('transferred_in'),
                        case_data.get('s3_link')
                    ]
                    
                    # Hardcoded exception for njd court: skip if PDF source not found error
                    if court_id.lower() == 'njd':
                        processing_error = case_data.get('processing_error', '')
                        if 'PDF source not found/acquirable' in processing_error:
                            self.logger.info(f"Found njd case with PDF source error, skipping: {base_name}")
                            return True
                    
                    if any(reason for reason in skip_reasons if reason):
                        self.logger.info(f"Found case with skip reason for pattern {court_id}_{docket_pattern}: {base_name}")
                        return True
                        
                except Exception as e:
                    self.logger.warning(f"Error reading JSON {json_path}: {e}")
                    
            except Exception as e:
                self.logger.warning(f"Error checking file {base_name}: {e}")
        
        self.logger.debug(f"No valid files found for pattern {court_id}_{docket_pattern} in {iso_date}")
        return False

    async def check_if_downloaded_across_dates(self, start_date_obj: DateType, end_date_obj: DateType,
                                               base_filename: str) -> bool:
        """Checks if files exist for a given docket across a date range."""
        current_date = start_date_obj
        while current_date <= end_date_obj:
            iso_date = current_date.strftime('%Y%m%d')
            if await self.check_if_downloaded(iso_date, base_filename):
                # No need to log here, check_if_downloaded already does
                return True
            current_date += timedelta(days=1)
        self.logger.debug(f"Files for {base_filename} not found between {start_date_obj} and {end_date_obj}")
        return False

    async def check_if_downloaded_across_dates_by_pattern(self, start_date_obj: DateType, end_date_obj: DateType,
                                                          court_id: str, docket_pattern: str) -> bool:
        """Checks if files exist matching pattern across a date range."""
        current_date = start_date_obj
        while current_date <= end_date_obj:
            iso_date = current_date.strftime('%Y%m%d')
            if await self.check_if_downloaded_by_pattern(iso_date, court_id, docket_pattern):
                return True
            current_date += timedelta(days=1)
        self.logger.debug(f"Files for pattern {court_id}_{docket_pattern} not found between {start_date_obj} and {end_date_obj}")
        return False

    async def check_if_downloaded_last_7_days_by_pattern(self, end_date_obj: DateType, court_id: str, docket_pattern: str) -> bool:
        """Optimized: Checks if files exist matching pattern in the last 7 days from end_date (inclusive).
        
        Args:
            end_date_obj: The ending date to look back from (inclusive)
            court_id: Court identifier (e.g., 'nysd', 'cand')
            docket_pattern: Docket pattern to match (e.g., '25_08592')
            
        Returns:
            True if matching files found in the last 7 days, False otherwise
        """
        # Preload cache for all 7 days for optimal performance
        await self.preload_filename_cache_for_7_days(end_date_obj)
        
        # Calculate 7-day range: end_date back to (end_date - 6 days) = 7 total days
        start_date_obj = end_date_obj - timedelta(days=6)
        
        self.logger.debug(f"Checking last 7 days for pattern {court_id}_{docket_pattern}: {start_date_obj.strftime('%Y-%m-%d')} to {end_date_obj.strftime('%Y-%m-%d')}")
        
        return await self.check_if_downloaded_across_dates_by_pattern(start_date_obj, end_date_obj, court_id, docket_pattern)

    async def check_if_artifact_exists_by_pattern(self, iso_date: str, court_id: str, docket_pattern: str) -> bool:
        """Optimized: Checks if a final artifact (PDF/ZIP) exists where filename STARTS WITH the pattern."""
        cached_filenames = await self._get_cached_filenames(iso_date)
        if not cached_filenames:
            return False

        start_pattern = f"{court_id.lower()}_{docket_pattern.lower()}"
        matching_files = [f for f in cached_filenames if f.startswith(start_pattern)]

        if not matching_files:
            return False

        date_dir = self.base_data_dir / iso_date / 'dockets'

        for base_name in matching_files:
            pdf_path = date_dir / f"{base_name}.pdf"
            zip_path = date_dir / f"{base_name}.zip"

            pdf_exists = await asyncio.to_thread(pdf_path.exists)
            if pdf_exists:
                self.logger.debug(f"Found PDF artifact for pattern {start_pattern}: {pdf_path.name}")
                return True

            zip_exists = await asyncio.to_thread(zip_path.exists)
            if zip_exists:
                self.logger.debug(f"Found ZIP artifact for pattern {start_pattern}: {zip_path.name}")
                return True

        return False

    async def check_if_artifact_exists_across_dates_by_pattern(self, start_date_obj: DateType, end_date_obj: DateType,
                                                                 court_id: str, docket_pattern: str) -> bool:
        """Checks if a final artifact (PDF/ZIP) exists matching pattern across a date range."""
        current_date = start_date_obj
        while current_date <= end_date_obj:
            iso_date = current_date.strftime('%Y%m%d')
            if await self.check_if_artifact_exists_by_pattern(iso_date, court_id, docket_pattern):
                return True
            current_date += timedelta(days=1)
        self.logger.debug(f"Artifact for pattern {court_id}_{docket_pattern} not found between {start_date_obj} and {end_date_obj}")
        return False

    async def check_if_artifact_exists_last_7_days_by_pattern(self, end_date_obj: DateType, court_id: str, docket_pattern: str) -> bool:
        """Optimized: Checks if a final artifact (PDF/ZIP) exists matching pattern in the last 7 days."""
        await self.preload_filename_cache_for_7_days(end_date_obj)
        start_date_obj = end_date_obj - timedelta(days=6)
        self.logger.debug(f"Checking last 7 days for ARTIFACT with pattern {court_id}_{docket_pattern}: {start_date_obj.strftime('%Y-%m-%d')} to {end_date_obj.strftime('%Y-%m-%d')}")
        return await self.check_if_artifact_exists_across_dates_by_pattern(start_date_obj, end_date_obj, court_id, docket_pattern)

    async def save_json(self, iso_date: str, base_filename: str, case_details: Dict[str, Any]):
        """Saves case details to JSON with inter-process and async locking."""
        paths = self._get_paths(iso_date, base_filename)
        json_path = paths["json"]
        lock_path = paths["lock"]
        self.logger.debug(f"Attempting to save JSON: {json_path}")

        process_lock = fasteners.InterProcessLock(str(lock_path))  # Fasteners needs string path
        acquired_process_lock = False

        try:
            # Acquire locks (async within process, inter-process for file)
            async with self.async_lock:  # Ensure only one async task in this process accesses the file via fasteners
                acquired_process_lock = await asyncio.to_thread(process_lock.acquire, blocking=True,
                                                                timeout=self.LOCK_TIMEOUT)
                if not acquired_process_lock:
                    # Should not happen with blocking=True unless timeout occurs before acquisition
                    raise TimeoutError(f"Failed to acquire process lock for {json_path} within {self.LOCK_TIMEOUT}s")

                # --- Perform write operation ---
                def write_json_sync():
                    # Ensure directory exists just before writing
                    json_path.parent.mkdir(parents=True, exist_ok=True)
                    # Write atomically: write to temp file then rename
                    temp_json_path = json_path.with_suffix(".json.tmp")
                    try:
                        with open(temp_json_path, 'w') as f:
                            json.dump(case_details, f, indent=4,
                                      default=str)  # Use default=str for non-serializable types
                        # Atomic rename/replace
                        os.replace(temp_json_path, json_path)
                    except Exception as write_err:
                        # Clean up temp file on error during write/replace
                        if temp_json_path.exists():
                            try:
                                temp_json_path.unlink()
                            except OSError:
                                pass
                        raise write_err  # Re-raise the caught exception

                await asyncio.to_thread(write_json_sync)
                # --- End write operation ---

                self.logger.info(f"JSON saved successfully: {json_path.name}")

        except (TimeoutError, fasteners.exceptions.InterProcessLockTimeout) as lock_e:
            self.logger.error(
                f"Timeout ({self.LOCK_TIMEOUT}s) acquiring process lock for JSON {json_path.name}: {lock_e}")
            # Optionally raise a specific exception or handle as needed
        except Exception as e:
            self.logger.error(f"Error saving JSON {json_path.name}: {e}", exc_info=True)
            # Optionally re-raise or handle
        finally:
            if acquired_process_lock:
                try:
                    await asyncio.to_thread(process_lock.release)
                except Exception as release_err:
                    self.logger.error(f"Error releasing process lock for {json_path.name}: {release_err}")

    async def finalize_download(
            self,
            temp_path_str: str,
            iso_date: str,
            base_filename: str,
            original_downloaded_filename: str,  # This parameter MUST be here
            zip_pdf: bool = True  # Default is True, can be overridden
    ) -> Optional[str]:
        """
        Processes a temporary downloaded file:
        - Determines its type (PDF or ZIP) from original_downloaded_filename.
        - If original_downloaded_filename lacks a clear extension, attempts to infer from temp_path_str.
        - If it's a PDF and zip_pdf is True, zips it.
        - If it's already a ZIP, uses it directly.
        - Renames/moves the final file (either the new ZIP or the original ZIP)
          to the target dockets directory as {base_filename}.zip (if zipping/is zip)
          or {base_filename}.pdf (if PDF and not zipping).
        - Returns the string path to the final file in the dockets directory, or None on failure.
        """
        self.logger.debug(
            f"Finalize_download_DEBUG called with: temp_path_str='{temp_path_str}', "
            f"original_downloaded_filename='{original_downloaded_filename}', zip_pdf={zip_pdf}"
        )
        temp_path = Path(temp_path_str)
        if not await asyncio.to_thread(temp_path.exists):
            self.logger.error(f"Temporary file {temp_path} does not exist for finalization.")
            return None
        self.logger.debug(
            f"Finalize_download_DEBUG: temp_path='{str(temp_path)}', temp_path.name='{temp_path.name}', "
            f"temp_path.suffix='{temp_path.suffix}'"
        )

        paths = self._get_paths(iso_date, base_filename)
        lock_path = paths["lock"]
        docket_dir = paths["docket_dir"]

        final_zip_target_path = paths["zip"]
        final_pdf_target_path = paths["pdf"]

        process_lock = fasteners.InterProcessLock(str(lock_path))
        acquired_process_lock = False

        original_fn_lower = original_downloaded_filename.lower()
        downloaded_file_is_pdf = original_fn_lower.endswith('.pdf')
        downloaded_file_is_zip = original_fn_lower.endswith('.zip')
        self.logger.debug(
            f"Finalize_download_DEBUG: Initial type check based on original_downloaded_filename "
            f"('{original_downloaded_filename}'): is_pdf={downloaded_file_is_pdf}, is_zip={downloaded_file_is_zip}"
        )

        if not downloaded_file_is_pdf and not downloaded_file_is_zip:
            self.logger.info(  # Changed to INFO for better visibility during debugging
                f"Finalize_download_INFO: Original_downloaded_filename '{original_downloaded_filename}' "
                f"lacks a .pdf/.zip extension. Attempting to infer type from temp file: {temp_path.name}"
            )
            temp_file_suffix_lower = temp_path.suffix.lower()
            self.logger.debug(
                f"Finalize_download_DEBUG: Inference: temp_file_suffix_lower='{temp_file_suffix_lower}' (from temp_path.name='{temp_path.name}')")
            if temp_file_suffix_lower == '.pdf':
                self.logger.info(
                    f"Finalize_download_INFO: Inference: Matched .pdf. Setting downloaded_file_is_pdf=True.")
                downloaded_file_is_pdf = True
                downloaded_file_is_zip = False  # Ensure mutually exclusive
            elif temp_file_suffix_lower == '.zip':
                self.logger.info(
                    f"Finalize_download_INFO: Inference: Matched .zip. Setting downloaded_file_is_zip=True.")
                downloaded_file_is_zip = True
                downloaded_file_is_pdf = False  # Ensure mutually exclusive
            else:
                # Changed to INFO for better visibility if this path is hit
                self.logger.info(
                    f"Finalize_download_INFO: Inference: Could not infer type. Temp file {temp_path.name} has an unrecognized suffix "
                    f"'{temp_file_suffix_lower}'. Proceeding with initial determination for "
                    f"'{original_downloaded_filename}'."
                )

        self.logger.debug(
            f"Finalize_download_DEBUG: After inference attempt: downloaded_file_is_pdf={downloaded_file_is_pdf}, "
            f"downloaded_file_is_zip={downloaded_file_is_zip}"
        )

        try:
            async with self.async_lock:
                acquired_process_lock = await asyncio.to_thread(process_lock.acquire, blocking=True,
                                                                timeout=self.LOCK_TIMEOUT)
                if not acquired_process_lock:
                    raise TimeoutError(
                        f"Failed to acquire process lock for finalizing {base_filename} within {self.LOCK_TIMEOUT}s")

                await asyncio.to_thread(docket_dir.mkdir, parents=True, exist_ok=True)

                if downloaded_file_is_pdf and zip_pdf:
                    self.logger.info(f"Zipping PDF {temp_path.name} to {final_zip_target_path.name}...")

                    def zip_pdf_sync():
                        try:
                            final_zip_target_path.parent.mkdir(parents=True, exist_ok=True)
                            with zipfile.ZipFile(final_zip_target_path, 'w', zipfile.ZIP_DEFLATED) as zf:
                                zf.write(temp_path, arcname=f"{base_filename}.pdf")  # Name inside zip
                            self.logger.info(f"Successfully zipped {temp_path.name} to {final_zip_target_path}")
                        except Exception as e_zip:
                            self.logger.error(f"Failed to zip {temp_path.name} to {final_zip_target_path}: {e_zip}",
                                              exc_info=True)
                            if final_zip_target_path.exists():
                                try:
                                    final_zip_target_path.unlink()
                                except OSError:
                                    pass
                            raise

                    await asyncio.to_thread(zip_pdf_sync)
                    if await asyncio.to_thread(temp_path.exists):  # Cleanup original temp PDF
                        await asyncio.to_thread(temp_path.unlink)
                    return str(final_zip_target_path)

                elif downloaded_file_is_zip:
                    self.logger.info(
                        f"Downloaded file {temp_path.name} (determined as ZIP) is being moved to {final_zip_target_path.name}.")
                    await asyncio.to_thread(os.replace, temp_path, final_zip_target_path)
                    self.logger.info(f"Successfully moved/renamed {temp_path.name} to {final_zip_target_path}")
                    return str(final_zip_target_path)

                elif downloaded_file_is_pdf and not zip_pdf:
                    self.logger.info(
                        f"Zipping disabled for PDF. Moving {temp_path.name} to {final_pdf_target_path.name}.")
                    await asyncio.to_thread(os.replace, temp_path, final_pdf_target_path)
                    self.logger.info(f"PDF finalized to {final_pdf_target_path}")
                    return str(final_pdf_target_path)

                else:
                    self.logger.error(
                        f"Downloaded file '{original_downloaded_filename}' (temp file: {temp_path.name}, suffix: '{temp_path.suffix}') "
                        f"has an unsupported/unexpected extension or type determination failed. Cannot process."
                    )
                    if await asyncio.to_thread(temp_path.exists): await asyncio.to_thread(temp_path.unlink)
                    return None

        except (TimeoutError, fasteners.exceptions.InterProcessLockTimeout) as lock_e:
            self.logger.error(f"Timeout ({self.LOCK_TIMEOUT}s) acquiring lock for finalizing {base_filename}: {lock_e}")
            if await asyncio.to_thread(temp_path.exists):
                try:
                    await asyncio.to_thread(temp_path.unlink)
                except OSError:
                    pass
            return None
        except Exception as e:
            self.logger.error(f"Error finalizing download for {base_filename}: {e}", exc_info=True)
            if await asyncio.to_thread(temp_path.exists):
                try:
                    await asyncio.to_thread(temp_path.unlink)
                except OSError:
                    pass
            return None
        finally:
            if acquired_process_lock:
                try:
                    await asyncio.to_thread(process_lock.release)
                except Exception as release_err:
                    self.logger.error(f"Error releasing process lock for finalizing {base_filename}: {release_err}")

    async def check_if_json_exists(self, iso_date: str, base_filename: str) -> bool:
        """Checks if only the JSON file exists for a given docket."""
        paths = self._get_paths(iso_date, base_filename)
        json_exists = await asyncio.to_thread(paths["json"].exists)
        if json_exists:
            self.logger.debug(f"JSON file found for {base_filename} in {iso_date}")
        return json_exists

    async def load_json_if_exists(self, iso_date: str, base_filename: str) -> Optional[Dict[str, Any]]:
        """Loads JSON data from a file if it exists, otherwise returns None."""
        paths = self._get_paths(iso_date, base_filename)
        json_path = paths["json"]

        if not await asyncio.to_thread(json_path.exists):
            return None

        # Lock acquire/release around file read
        lock_path = paths["lock"]
        process_lock = fasteners.InterProcessLock(str(lock_path))
        acquired_process_lock = False

        try:
            async with self.async_lock:  # In-process async lock
                acquired_process_lock = await asyncio.to_thread(process_lock.acquire, blocking=True,
                                                                timeout=self.LOCK_TIMEOUT)
                if not acquired_process_lock:
                    self.logger.warning(f"Timeout acquiring lock to read JSON {json_path.name}")
                    return None  # Could not acquire lock

                def read_json_sync():
                    try:
                        with open(json_path, 'r') as f:
                            return json.load(f)
                    except json.JSONDecodeError:
                        self.logger.warning(f"Corrupted JSON file found at {json_path}. Cannot load.")
                        return None  # Indicates corruption
                    except Exception as e_read:
                        self.logger.error(f"Error reading JSON file {json_path}: {e_read}")
                        return None  # General read error

                data = await asyncio.to_thread(read_json_sync)
                return data
        except Exception as e:
            self.logger.error(f"Outer error loading JSON {json_path.name}: {e}", exc_info=True)
            return None
        finally:
            if acquired_process_lock:
                try:
                    await asyncio.to_thread(process_lock.release)
                except Exception as release_err:
                    self.logger.error(f"Error releasing read lock for JSON {json_path.name}: {release_err}")

    @staticmethod  # If it doesn't rely on self
    def create_base_filename_static(court_id: str, docket_num: str, versus: str) -> str:
        """Creates a sanitized base filename. Static version for use outside instance."""
        # Clean docket number: Extract year and 5-digit docket number only
        # Format: N:YY-XX-NNNNN-... -> YY_NNNNN
        docket_parts = docket_num.split(':')[-1].split('-')
        if len(docket_parts) >= 3:
            year_part = docket_parts[0]  # YY
            # Skip the alphabetical part (cv, sf, etc.) and get the 5-digit number
            five_digit_part = docket_parts[2]  # Should be NNNNN
            # Extract exactly 5 digits, drop everything after
            num_match = re.search(r'(\d{5})', five_digit_part)
            if num_match:
                docket_num_cleaned = f"{year_part}_{num_match.group(1)}"
            else:
                # Fallback: use the part as-is if no 5-digit match
                docket_num_cleaned = f"{year_part}_{five_digit_part}"
        else:
            # Fallback for unexpected formats
            docket_num_cleaned = re.sub(r'[^\w-]', '_', docket_num)

        versus_cleaned = re.sub(r'[^\w\s-]', '', versus)
        versus_cleaned = re.sub(r'\s+', '_', versus_cleaned).strip('_-')
        max_versus_len = 60
        if len(versus_cleaned) > max_versus_len:
            versus_cleaned = versus_cleaned[:max_versus_len]

        base = f"{court_id}_{docket_num_cleaned}_{versus_cleaned}"
        base = re.sub(r'[^\w_-]+', '', base)
        base = re.sub(r'_+', '_', base)
        base = re.sub(r'-+', '-', base)
        return base.strip('_').strip('-')

    async def append_to_log_file(self, iso_date: str, log_filename: str, item_to_append: Dict[str, Any]):
        """Appends an item to a JSON log file in the specified iso_date's log directory."""
        log_dir = self.base_data_dir / iso_date / 'logs'
        log_dir.mkdir(parents=True, exist_ok=True)

        file_path = log_dir / log_filename
        lock_path = log_dir / f".{log_filename}.lock"  # Unique lock per log file

        self.logger.info(f"Attempting to append to log file: {file_path} with item: {item_to_append}")

        process_lock = fasteners.InterProcessLock(str(lock_path))
        acquired_process_lock = False

        try:
            async with self.async_lock:  # In-process async lock
                acquired_process_lock = await asyncio.to_thread(process_lock.acquire, blocking=True, timeout=15)
                if not acquired_process_lock:
                    raise TimeoutError(f"Timeout acquiring process lock for {file_path}")

                def read_append_write_json_sync():
                    existing_data = []
                    if file_path.exists() and file_path.stat().st_size > 0:
                        try:
                            with open(file_path, 'r') as f:
                                content = f.read()
                                if content.strip():  # Ensure content is not just whitespace
                                    existing_data = json.loads(content)
                                else:  # File exists but is empty or whitespace only
                                    existing_data = []
                            if not isinstance(existing_data, list):
                                self.logger.warning(
                                    f"File {file_path} content is not a list. Initializing as new list.")
                                existing_data = []
                        except json.JSONDecodeError:
                            self.logger.warning(f"Corrupted JSON {file_path}. Initializing as new list.")
                            existing_data = []
                        except Exception as read_err:
                            self.logger.error(f"Error reading {file_path}: {read_err}. Initializing as new list.")
                            existing_data = []

                    existing_data.append(item_to_append)

                    temp_file_path = file_path.with_suffix(f"{file_path.suffix}.tmp")  # Ensure .tmp is added correctly
                    try:
                        with open(temp_file_path, 'w') as f:
                            json.dump(existing_data, f, indent=4, default=str)
                        os.replace(temp_file_path, file_path)
                    except Exception as write_err:
                        if temp_file_path.exists():
                            try:
                                temp_file_path.unlink()
                            except OSError:
                                pass  # Log if necessary
                        raise write_err

                await asyncio.to_thread(read_append_write_json_sync)
                self.logger.debug(f"Successfully appended to {file_path}")

        except TimeoutError as lock_e:
            self.logger.error(f"Timeout acquiring lock for log file {file_path}: {lock_e}")
        except Exception as e:
            self.logger.error(f"Error appending to log file {file_path}: {e}", exc_info=True)
        finally:
            if acquired_process_lock:
                try:
                    await asyncio.to_thread(process_lock.release)
                except Exception as release_err:
                    self.logger.error(f"Error releasing process lock for {file_path}: {release_err}")
