import logging
from typing import Optional

from playwright.async_api import (
    TimeoutError as PlaywrightTimeoutError,
    <PERSON>rro<PERSON> as Play<PERSON><PERSON>rror
)

from .pacer_utils import retry_async
from .navigator import PacerNavigator


class PacerAuthenticator:
    """Handles PACER login."""

    LOGIN_URL = 'https://pacer.login.uscourts.gov/csologin/login.jsf'
    USERNAME_SELECTOR = "#loginForm\\:loginName"
    PASSWORD_SELECTOR = "#loginForm\\:password"
    LOGIN_BUTTON_SELECTOR = "#loginForm\\:fbtnLogin"
    LOGGED_IN_INDICATOR_SELECTOR = "span.psc-login-panel-header.psc-pacer-blue-fg"
    LOGGED_IN_TEXT = 'Logged in as'
    LOGIN_SUCCESS_INDICATOR_SELECTOR = "#logoutForm\\:courtId"  # Element expected after successful login

    def __init__(self, navigator: <PERSON><PERSON><PERSON><PERSON>gator, username: str, password: str, logger: Optional[logging.Logger] = None):
        self.navigator = navigator
        self.username = username
        self.password = password  # Be mindful of logging this
        if logger:
            self.logger = logger
        else:
            # Fallback to a default logger for this component
            self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        self.logger.info(f"{self.__class__.__name__} initialized.")

    async def _is_already_logged_in(self) -> bool:
        """Checks if the user session is already active."""
        logged_in_indicator_text = await self.navigator.get_text(self.LOGGED_IN_INDICATOR_SELECTOR)
        if logged_in_indicator_text and self.LOGGED_IN_TEXT in logged_in_indicator_text:
            logged_in_user = logged_in_indicator_text.split(self.LOGGED_IN_TEXT)[-1].strip()
            self.logger.info(f"Already logged in as '{logged_in_user}'")
            return True
        return False

    @retry_async(tries=3, delay=5, exceptions=(PlaywrightTimeoutError, PlaywrightError))
    async def login(self) -> bool:
        """Performs the login process."""
        await self.navigator.goto(self.LOGIN_URL)

        if await self._is_already_logged_in():
            return True

        self.logger.info("Performing PACER login...")
        await self.navigator.fill(self.USERNAME_SELECTOR, self.username)
        await self.navigator.fill(self.PASSWORD_SELECTOR, self.password)
        # Click login button and expect navigation
        await self.navigator.click(self.LOGIN_BUTTON_SELECTOR, wait_for_nav=True)

        # Verify login success by checking for an element on the landing page after login
        try:
            # --- START FIX: Await the async locator method ---
            success_indicator = await self.navigator.locator(self.LOGIN_SUCCESS_INDICATOR_SELECTOR)
            # --- END FIX ---
            await success_indicator.wait_for(state="visible", timeout=self.navigator.timeout)
            self.logger.info("Login successful.")
            return True
        except PlaywrightError as e:  # Catch specific verification errors
            self.logger.error(
                f"Login failed: Did not find expected element '{self.LOGIN_SUCCESS_INDICATOR_SELECTOR}' after submit. Error: {e}")
            await self.navigator.save_screenshot("login_fail_verification")
            return False

    async def __aenter__(self):
        # Login automatically when entering the context
        if not await self.login():
            raise ConnectionError("Failed to authenticate with PACER.")
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        # Could implement logout if needed, but typically not necessary for scripts
        self.logger.debug("PacerAuthenticator context exited.")
        pass
