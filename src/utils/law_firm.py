import re


class LawFirmNameHandler:
    # Define overrides as a class attribute for easy modification
    _NAME_OVERRIDES = {
        # Specific Overrides (like lowercase mctlaw)
        "mctlaw": "mctlaw",
        "allconsumer.com": "AllConsumer.com",
        "legal settlement for children": "Saddle Rock Legal Group",
        "chj law firm": "CHJ Law Firm",
        "jms law": "JMS Law Ltd",
        "keller postman injury law firm": "Keller Postman LLC",
        "keller postman injury law": "Keller Postman LLC",
        "levin papantonio law firm": "Levin Papantonio Proctor Buchanan O'Brien Barr & Mougey PA",
        "levin papantonio rafferty law firm": "Levin Papantonio Proctor Buchanan O'Brien Barr & Mougey PA",
        "saiontz & kirk, p.a. - youhavealawyer.com": "Saiontz & Kirk PA",
        "suboxone film lawsuit: allied law": "Justpoint",
        "potter handy, llp law firm": "Potter Handy LLP",
        "nicu injury help": "The Sentinel Group",
        "joseph a. gregorio, a professional law firm": "Joseph Gregorio Law",
        "scott+scott llp": "<PERSON> + <PERSON> Attorneys at Law LLP",
        "scott+scott attorneys at law llp": "<PERSON> + Scott Attorneys at Law LLP",
        "shield justice watch": "Shield Legal",
        "afff exposure attorneys": "TruLaw",
        "frantz law group, aplc": "Frantz Law Group APLC",
        "gibbs mura alg": "Gibbs Mura ALG",
        "bd & j pc": "BD&J PC",  # Note: BD&J might also be handled by special_cases below
        "bd&j": "BD&J",
        "bd&j pc": "BD&J PC",
        "tseg": "TSEG",
        "arns law": "The Arns Law Firm",
        "ls5 legal": "LS5 Legal",
        "mlk law group": "MLK Law Group",
        "nib": "NIB",
        "mb legal": "MB Legal",
        "1-800-law-firm": "1-800-LAW-FIRM",
        "djc law": "DJC Law",
        "kjt law group":"KJT Law Group",
        "select-justice":"Select Justice",
        "gibbs law group": "Gibbs Mura ALG",
        "chj law firm pc": "CHJ Law Firm PC", # <-- Fix
        "camg":"CAMG",
        "ask llp": "ASK LLP",
        "ava law group": "AVA Law Group",
        "cohenmalad llp": "CohenMalad LLP",
        "simmonbs hanly conroy": "Simmons Hanly Conroy",
        "nigh goldenberg raso & vaughn": "Nigh Goldenberg Raso & Vaughn PLLC",
        "peiffer wolf carr kane conway & wise": "Peiffer Wolf Carr Kane Conway & Wise LLP",
        "morgan and morgan": "Morgan & Morgan",
        "morgan & morgan - complex litigation group": "Morgan & Morgan",
        "ijh law": "IJH Law"
    }

    @staticmethod
    def capitalize_law_firm_names(name: str) -> str:
        """
        Capitalizes law firm names according to specific legal industry rules.
        Handles multiple firms separated by ';', acronyms (LLC, PA, PC, DC),
        prefixes (Mc, Mac, O'), minor words, punctuation, and consolidated overrides.
        """
        if not isinstance(name, str) or not name.strip():
            return ""

        input_name_stripped = name.strip()
        input_name_lower = input_name_stripped.lower()

        # --- Check FULL NAME OVERRIDES first ---
        if input_name_lower in LawFirmNameHandler._NAME_OVERRIDES:
            # If the entire input matches an override key, return the override value directly
            return LawFirmNameHandler._NAME_OVERRIDES[input_name_lower]

        # --- If no full override, proceed with segment/word processing ---

        # --- Rule Definitions ---
        acronyms = {
            "LLP", "LLC", "PA", "PC", "PLLC", "APLC", "LPA", "LP", "PLLP",
            "PLC", "DBA", "SC", "APC", "LLLP", "PSC", "TSEG", "NIB", "ALG",
            "EKSM", "ARS", "LS5", "MLK", "MB"
        }
        force_uppercase = {'DFW', 'LA', 'CR', 'NS', 'PR', 'HME', 'DRS', 'MDR', 'SMT', 'YDC', 'NYC', 'DV', 'DC', 'PLL'}
        minor_words = {
            "a", "an", "and", "as", "at", "but", "by", "for", "in", "of",
            "on", "or", "the", "to", "vs", "v", "with", "&"
        }
        special_cases = {"BD&J"}  # Specific patterns handled as units

        # --- Processing Logic ---
        final_result = []
        segments = re.split(r'(;)', input_name_stripped)  # Use the original stripped name

        for segment in segments:
            if not segment or segment == ';':
                final_result.append(segment)
                continue

            # Check if the whole segment (lowercase) matches an override AFTER splitting by ;
            # This handles cases where an override might apply to one part of a multi-firm string
            segment_lower = segment.strip().lower()
            if segment_lower in LawFirmNameHandler._NAME_OVERRIDES:
                final_result.append(LawFirmNameHandler._NAME_OVERRIDES[segment_lower])
                continue  # Skip word-by-word processing for this segment

            # --- Process segment word by word if no full segment override ---
            parts = re.split(r'(\s+|\+|&|-)', segment)
            capitalized_segment_parts = []
            is_first_word_in_segment = True

            for i, part in enumerate(parts):
                if not part or part.isspace():
                    capitalized_segment_parts.append(part)
                    continue
                if part in ['+', '&', '-']:
                    capitalized_segment_parts.append(part)
                    is_first_word_in_segment = True  # Reset for next word after separator
                    continue

                word_lower = part.lower()
                word_cleaned_for_check = re.sub(r"[.,]", "", part).upper()

                # Check individual word override (e.g., if 'mctlaw' appears mid-name)
                # Less common, but included for completeness
                if word_lower in LawFirmNameHandler._NAME_OVERRIDES:
                    capitalized_segment_parts.append(LawFirmNameHandler._NAME_OVERRIDES[word_lower])

                # --- Apply standard rules if no word override ---
                # (Rules 1-6: Special Cases, Forced Upper, Acronyms, Prefixes, Minor, Default)
                elif part.upper() in special_cases:
                    capitalized_segment_parts.append(part.upper())
                elif word_cleaned_for_check in force_uppercase:
                    capitalized_segment_parts.append(word_cleaned_for_check)
                elif word_cleaned_for_check in acronyms:
                    capitalized_segment_parts.append(word_cleaned_for_check)
                elif word_lower.startswith("mc") and len(part) > 2:
                    capitalized_segment_parts.append("Mc" + part[2:].capitalize())
                elif word_lower.startswith("mac") and len(part) > 3:
                    capitalized_segment_parts.append("Mac" + part[3:].capitalize())
                elif word_lower.startswith("o'") and len(part) > 2:
                    capitalized_segment_parts.append("O'" + part[2:].capitalize())
                elif word_lower in minor_words and not is_first_word_in_segment:
                    is_last_word = True
                    for next_part in parts[i + 1:]:
                        if next_part and not next_part.isspace() and next_part not in ['+', '&']:
                            is_last_word = False
                            break
                    capitalized_segment_parts.append(part.capitalize() if is_last_word else word_lower)
                else:
                    if len(part) > 1 and not part.islower() and not part.isupper():
                        capitalized_segment_parts.append(part)
                    elif len(part) == 1 and part.isupper():
                        capitalized_segment_parts.append(part)
                    elif len(part) == 2 and part[0].isalpha() and part[1] == '.':
                        capitalized_segment_parts.append(part[0].upper() + '.')
                    else:
                        word_to_cap = re.sub(r"[.,]$", "", part)
                        capitalized_segment_parts.append(word_to_cap.capitalize())

                is_first_word_in_segment = False

            final_result.extend(capitalized_segment_parts)

        # --- Final Join and Cleanup ---
        result_string = "".join(final_result)
        result_string = re.sub(r'\s+', ' ', result_string).strip()
        result_string = re.sub(r'\s*;\s*', ' ; ', result_string).strip()
        result_string = re.sub(r'\s*\+\s*', ' + ', result_string).strip()
        result_string = re.sub(r'\s*&\s*', ' & ', result_string).strip()
        result_string = re.sub(r'\s+', ' ', result_string).strip()

        return result_string

    @staticmethod
    def _general_capitalization_logic(words):
        """Applies general capitalization rules (Mc, Mac, O', LLP, PC, DFW etc.)."""
        processed_words = []
        # Define sets for efficient lookup
        lowercase_words = {'and', 'of', 'the', '&', 'a', 'an', 'at', 'by', 'for', 'in', 'on', 'to', 'up'}
        # Acronyms that should be fully uppercase WITHOUT punctuation
        acronyms = {'LLC', 'PLLC', 'PC', 'PA', 'LLP', 'LPA', 'LP', 'PLLP', 'PLC', 'DBA', 'SC', 'APC', 'LLLP'}
        # Words to force fully uppercase
        force_uppercase = {'DFW', 'LA', 'CR', 'NS', 'PR', 'HME', 'DRS', 'MDR', 'SMT', 'YDC', 'NYC',
                           'PA', 'EKSM', 'ARS', 'TS5', 'LS5', 'MLK', 'NIB', 'MB', 'TSEG', 'BD&J'}

        for i, word in enumerate(words):
            # Clean the word for checks (remove punctuation, uppercase)
            word_cleaned_no_punct = re.sub(r"[.,]", "", word)
            word_check_upper = word_cleaned_no_punct.upper()
            word_lower = word.lower()  # Use original word for case checks

            # 1. Check Forced Uppercase (Highest priority after cleaning)
            if word_check_upper in force_uppercase:
                processed_words.append(word_check_upper)
            # 2. Check Acronyms (Second priority)
            elif word_check_upper in acronyms:
                processed_words.append(word_check_upper)
            # 3. Handle 'Mc', 'Mac', 'O'' prefixes
            elif word_lower.startswith("mc") and len(word) > 2:
                processed_words.append("Mc" + word[2:].capitalize())
            elif word_lower.startswith("mac") and len(word) > 3:
                processed_words.append("Mac" + word[3:].capitalize())
            elif word_lower.startswith("o'") and len(word) > 2:
                processed_words.append("O'" + word[2:].capitalize())
            # 4. Handle lowercase words (but not the first word)
            elif word_lower in lowercase_words and i != 0:
                # Preserve original case if it's just '&'
                processed_words.append('&' if word == '&' else word_lower)
            # 5. Default: Capitalize the word (preserve internal caps like DiCello)
            # Remove trailing comma/period *before* capitalizing if it's not part of Mc/Mac/O'
            else:
                # Preserve existing mixed-case like 'DiCello' or single uppercase letters like 'A'
                if len(word) > 1 and not word.islower() and not word.isupper():  # Mixed case like DiCello
                    processed_words.append(word)
                elif len(word) == 1 and word.isupper():  # Single Upper like A.
                    processed_words.append(word)
                else:  # All lower or all upper (non-acronym)
                    # Clean trailing punctuation before capitalizing
                    word_to_capitalize = word.rstrip('.,')
                    processed_words.append(word_to_capitalize.capitalize())

        # Join words and clean up spacing
        result_string = ' '.join(processed_words)
        result_string = re.sub(r'\s+', ' ', result_string).strip()  # Collapse multiple spaces
        result_string = re.sub(r'\s*&\s*', ' & ', result_string).strip()  # Fix spacing around ampersand
        result_string = re.sub(r'\s*\+\s*', ' + ', result_string).strip()  # Fix spacing around plus signs
        return result_string