"""
Date utility module for standardized date operations across the application.
Consolidates date parsing, formatting, and manipulation functions.
"""

from datetime import datetime, timedelta, date as date_type
from typing import Optional, Union, List, Any, Tuple

import pandas as pd
import logging
import re

logger = logging.getLogger(__name__)

# Common date formats used in the application
FORMAT_ISO = "%Y%m%d"           # 20250516
FORMAT_US_SHORT = "%m/%d/%y"    # 05/16/25
FORMAT_US_LONG = "%m/%d/%Y"     # 05/16/2025
FORMAT_ISO_DASH = "%Y-%m-%d"    # 2025-05-16
FORMAT_DB = "%Y-%m-%d %H:%M:%S" # 2025-05-16 14:30:00

# List of formats to try when parsing dates with flexible format
COMMON_FORMATS = [
    FORMAT_ISO,
    FORMAT_US_SHORT,
    FORMAT_US_LONG,
    FORMAT_ISO_DASH,
    # Add more formats as needed
    "%b %d, %Y",    # May 16, 2025
    "%d %b %Y",     # 16 May 2025
    "%B %d, %Y",    # May 16, 2025
    "%Y/%m/%d",     # 2025/05/16
]

class DateUtils:
    """Utility class for date operations."""

    @staticmethod
    def parse_date(date_value: Any, default_format: str = None) -> Optional[datetime]:
        """
        Parse a date string or object into a datetime object using flexible format detection.
        
        Args:
            date_value: The date string or object to parse
            default_format: Optional format to try first
            
        Returns:
            datetime object or None if parsing fails
        """
        if not date_value:
            return None

        # If already a datetime, return it
        if isinstance(date_value, datetime):
            return date_value
        
        # If a date object, convert to datetime
        if isinstance(date_value, date_type):
            return datetime.combine(date_value, datetime.min.time())

        # Handle number types (assume timestamp)
        if isinstance(date_value, (int, float)):
            try:
                return datetime.fromtimestamp(date_value)
            except (ValueError, OSError, TypeError):
                return None

        # Convert to string for parsing
        date_str = str(date_value).strip()
        
        # Try the default format first if provided
        if default_format:
            try:
                return datetime.strptime(date_str, default_format)
            except ValueError:
                pass  # Continue to other formats
        
        # Try pandas for flexible parsing
        try:
            return pd.to_datetime(date_str).to_pydatetime()
        except (ValueError, TypeError):
            pass
            
        # Try each of our common formats
        for fmt in COMMON_FORMATS:
            try:
                return datetime.strptime(date_str, fmt)
            except ValueError:
                continue
                
        # Try regex patterns for special cases
        try:
            # Match patterns like May 16, 2025
            month_pattern = r'([A-Za-z]+)[.\s,]*(\d{1,2})[.\s,]*(\d{4})'
            match = re.match(month_pattern, date_str)
            if match:
                month, day, year = match.groups()
                month_map = {
                    'jan': 1, 'feb': 2, 'mar': 3, 'apr': 4, 'may': 5, 'jun': 6,
                    'jul': 7, 'aug': 8, 'sep': 9, 'oct': 10, 'nov': 11, 'dec': 12
                }
                month_num = month_map.get(month[:3].lower())
                if month_num:
                    return datetime(int(year), month_num, int(day))
        except Exception:
            pass
            
        # Unable to parse
        logger.debug(f"Unable to parse date: {date_value}")
        return None

    @staticmethod
    def format_date(date_value: Any, output_format: str = FORMAT_ISO, default_input_format: str = None) -> Optional[str]:
        """
        Convert a date value to a formatted string.
        
        Args:
            date_value: Date value to format (string, datetime, timestamp, etc.)
            output_format: Desired output format
            default_input_format: Optional format to try first when parsing strings
            
        Returns:
            Formatted date string or None if parsing fails
        """
        date_obj = DateUtils.parse_date(date_value, default_input_format)
        if date_obj is None:
            return None
            
        try:
            return date_obj.strftime(output_format)
        except Exception as e:
            logger.error(f"Error formatting date {date_value} to {output_format}: {e}")
            return None

    @staticmethod
    def date_to_iso(date_value: Any) -> Optional[str]:
        """
        Convert any date value to ISO format (YYYYMMDD).
        
        Args:
            date_value: Date to convert
            
        Returns:
            ISO formatted date or None if conversion fails
        """
        return DateUtils.format_date(date_value, FORMAT_ISO)

    @staticmethod
    def date_to_us_format(date_value: Any, long_year: bool = False) -> Optional[str]:
        """
        Convert any date value to US format (MM/DD/YY or MM/DD/YYYY).
        
        Args:
            date_value: Date to convert
            long_year: Whether to use 4-digit year (True) or 2-digit year (False)
            
        Returns:
            US formatted date or None if conversion fails
        """
        fmt = FORMAT_US_LONG if long_year else FORMAT_US_SHORT
        return DateUtils.format_date(date_value, fmt)

    @staticmethod
    def get_date_before(date_value: Any, days: int) -> Optional[str]:
        """
        Get the date N days before the given date, in ISO format.
        
        Args:
            date_value: Reference date
            days: Number of days to go back
            
        Returns:
            ISO formatted date or None if conversion fails
        """
        date_obj = DateUtils.parse_date(date_value)
        if date_obj is None:
            date_obj = datetime.now()
            
        try:
            result_date = date_obj - timedelta(days=days)
            return result_date.strftime(FORMAT_ISO)
        except Exception as e:
            logger.error(f"Error calculating date {days} days before {date_value}: {e}")
            return None
            
    @staticmethod
    def get_date_before_n_days(days: int, date_string: str) -> str:
        """
        Calculates date N days before a given date string in YYYYMMDD format.
        
        Args:
            days: Number of days to go back
            date_string: Reference date in YYYYMMDD format
            
        Returns:
            Date string in YYYYMMDD format
            
        Raises:
            ValueError: If date_string is not in YYYYMMDD format
        """
        if not isinstance(date_string, str) or len(date_string) != 8:
            raise ValueError("Date string must be in YYYYMMDD format")
        input_date = datetime.strptime(date_string, "%Y%m%d")
        date_before = input_date - timedelta(days=days)
        return date_before.strftime("%Y%m%d")

    @staticmethod
    def get_date_after(date_value: Any, days: int) -> Optional[str]:
        """
        Get the date N days after the given date, in ISO format.
        
        Args:
            date_value: Reference date
            days: Number of days to go forward
            
        Returns:
            ISO formatted date or None if conversion fails
        """
        date_obj = DateUtils.parse_date(date_value)
        if date_obj is None:
            date_obj = datetime.now()
            
        try:
            result_date = date_obj + timedelta(days=days)
            return result_date.strftime(FORMAT_ISO)
        except Exception as e:
            logger.error(f"Error calculating date {days} days after {date_value}: {e}")
            return None

    @staticmethod
    def convert_unix_to_date_str(unix_timestamp: Optional[Union[int, float]], fmt: str = FORMAT_ISO) -> Optional[str]:
        """
        Convert Unix timestamp (seconds) to a specified string format.
        
        Args:
            unix_timestamp: Unix timestamp in seconds
            fmt: Output date format
            
        Returns:
            Formatted date string or None if conversion fails
        """
        if unix_timestamp is None or not isinstance(unix_timestamp, (int, float)) or unix_timestamp <= 0:
            return None
        try:
            return datetime.fromtimestamp(unix_timestamp).strftime(fmt)
        except (ValueError, OSError, TypeError) as e:
            logger.warning(f"Could not convert invalid timestamp: {unix_timestamp}. Error: {e}")
            return None

    @staticmethod
    def generate_date_range(start_date: Any, end_date: Any, format: str = FORMAT_US_SHORT) -> List[str]:
        """
        Generate a list of dates between start_date and end_date inclusive.
        
        Args:
            start_date: Start date (any format)
            end_date: End date (any format)
            format: Output format for the dates
            
        Returns:
            List of formatted dates
        """
        start_dt = DateUtils.parse_date(start_date)
        end_dt = DateUtils.parse_date(end_date)
        
        if not start_dt or not end_dt:
            logger.error(f"Invalid date range: {start_date} to {end_date}")
            return []
            
        if start_dt > end_dt:
            start_dt, end_dt = end_dt, start_dt
            
        dates = []
        current_dt = start_dt
        while current_dt <= end_dt:
            dates.append(current_dt.strftime(format))
            current_dt += timedelta(days=1)
            
        return dates

    @staticmethod
    def is_valid_date(date_value: Any) -> bool:
        """
        Check if a value can be parsed as a valid date.
        
        Args:
            date_value: Value to check
            
        Returns:
            True if valid date, False otherwise
        """
        return DateUtils.parse_date(date_value) is not None

    @staticmethod
    def ensure_iso_format(date_value: Any) -> Optional[str]:
        """
        Convert date to YYYYMMDD format if needed.
        
        Args:
            date_value: Date to convert
            
        Returns:
            ISO formatted date or original value if conversion fails
        """
        if not date_value:
            return None

        # If already in YYYYMMDD format, return as is
        if isinstance(date_value, str) and len(date_value) == 8 and date_value.isdigit():
            return date_value

        # Try to parse and convert
        result = DateUtils.date_to_iso(date_value)
        
        # Return original if conversion fails
        return result if result else date_value
    
    @staticmethod
    def current_date(format: str = FORMAT_ISO) -> str:
        """
        Get the current date in the specified format.
        
        Args:
            format: Output date format
            
        Returns:
            Current date in specified format
        """
        return datetime.now().strftime(format)
    
    @staticmethod
    def parse_date_with_fallback(date_value: Any, formats_to_try: List[str]) -> Tuple[Optional[datetime], Optional[str]]:
        """
        Parse a date with multiple format attempts, return both datetime and which format worked.
        
        Args:
            date_value: Date to parse
            formats_to_try: List of format strings to try
            
        Returns:
            Tuple of (datetime object or None, format that worked or None)
        """
        if not date_value:
            return None, None
            
        date_str = str(date_value).strip()
        
        for fmt in formats_to_try:
            try:
                date_obj = datetime.strptime(date_str, fmt)
                return date_obj, fmt
            except ValueError:
                continue
                
        return None, None