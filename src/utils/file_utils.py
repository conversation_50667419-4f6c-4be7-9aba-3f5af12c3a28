"""File and directory utilities."""
import logging
import os
import zipfile
import time
from typing import Optional


def ensure_dir_exists(dir_path: str, logger: Optional[logging.Logger] = None):
    """
    Ensures that a directory exists, creating it if necessary.
    Logs the creation action if a logger is provided.
    """
    if not os.path.exists(dir_path):
        try:
            os.makedirs(dir_path, exist_ok=True)
            if logger:
                logger.debug(f"Created directory: {dir_path}")
        except OSError as e:
            if logger:
                logger.error(f"Failed to create directory {dir_path}: {e}", exc_info=True)
            raise  # Re-raise the exception as directory creation failure might be critical
    elif not os.path.isdir(dir_path):
        error_msg = f"Path exists but is not a directory: {dir_path}"
        if logger:
            logger.error(error_msg)
        raise NotADirectoryError(error_msg)


def try_remove(file_path: str, logger: logging.Logger, file_description: str = "file"):
    """
    Attempts to remove a file, logging success or failure. Handles None or non-string paths.
    """
    # Added check for None or non-string file_path
    if not file_path or not isinstance(file_path, str):
        logger.debug(f"Skipping removal: Invalid file path provided ({file_path}) for {file_description}.")
        return False

    try:
        if os.path.exists(file_path):
            if os.path.isfile(file_path) or os.path.islink(file_path):
                os.remove(file_path)
                logger.debug(f"Successfully removed {file_description}: {os.path.basename(file_path)}")
                return True
            else:
                logger.warning(f"Skipping removal: Path exists but is not a file or link ({file_description}): {file_path}")
                return False
        else:
            # It's okay if the file doesn't exist when we try to remove it
            logger.debug(f"Skipping removal: File does not exist ({file_description}): {os.path.basename(file_path)}")
            return False  # Return False as no removal occurred, but not an error state.
    except OSError as e:
        logger.warning(f"Failed to remove {file_description} '{os.path.basename(file_path)}': {e}")
        return False
    except Exception as e:
        logger.error(f"Unexpected error removing {file_description} '{os.path.basename(file_path)}': {e}", exc_info=True)
        return False


def extract_from_zip(zip_path: str, target_pdf_path: str, logger: logging.Logger) -> bool:
    """
    Extracts the first found valid PDF file from a zip archive directly to the target_pdf_path.

    Args:
        zip_path: The path to the zip file.
        target_pdf_path: The full desired path for the extracted PDF file.
        logger: The logger instance.

    Returns:
        True if a PDF was successfully extracted to target_pdf_path, False otherwise.
    """
    if not zip_path or not os.path.exists(zip_path):
        logger.warning(f"Zip file not found or path is invalid: {zip_path}")
        return False
    if not target_pdf_path:
        logger.error("Target PDF path for extraction cannot be empty.")
        return False

    pdf_member_name = None
    zip_filename_base = os.path.basename(zip_path)
    target_filename_base = os.path.basename(target_pdf_path)
    extract_to_dir = os.path.dirname(target_pdf_path)

    try:
        ensure_dir_exists(extract_to_dir, logger)  # Ensure extraction target dir exists

        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            # Find the first suitable PDF file in the archive
            pdf_members = []
            for member in zip_ref.infolist():
                # Skip directories and potentially harmful filenames
                if not member.is_dir() and member.filename.lower().endswith('.pdf'):
                    # Basic check to avoid path traversal
                    if '..' not in member.filename and not os.path.isabs(member.filename):
                        pdf_members.append(member.filename)

            if not pdf_members:
                logger.warning(f"No suitable PDF file found inside zip archive: {zip_filename_base}")
                return False

            # Prefer a PDF named '1.pdf' if multiple exist, otherwise take the first one found
            if '1.pdf' in [os.path.basename(m) for m in pdf_members]:
                # Find the full member name that corresponds to '1.pdf'
                for member_name in pdf_members:
                    if os.path.basename(member_name) == '1.pdf':
                        pdf_member_name = member_name
                        break
            else:
                pdf_member_name = pdf_members[0]  # Take the first valid one found

            if pdf_member_name:
                logger.info(f"Extracting '{pdf_member_name}' from '{zip_filename_base}' directly to target path '{target_filename_base}'")

                # Ensure target does not exist before extraction to avoid writing over existing file
                if os.path.exists(target_pdf_path):
                    logger.warning(f"Target PDF path already exists, removing before extraction: {target_filename_base}")
                    if not try_remove(target_pdf_path, logger, "pre-existing target PDF"):
                        logger.error(f"Failed to remove existing target file {target_filename_base}. Aborting extraction.")
                        return False

                # Extract the specific file's content and write it DIRECTLY to the target path
                pdf_content = zip_ref.read(pdf_member_name)
                with open(target_pdf_path, 'wb') as outfile:
                    outfile.write(pdf_content)
                    outfile.flush()  # Ensure buffer is written
                    try:
                        os.fsync(outfile.fileno())  # Try to force write to disk
                    except OSError as fsync_err:
                        logger.warning(f"Could not fsync after writing {target_filename_base}: {fsync_err}")

                # Verify extraction by checking existence and size
                # Add a tiny delay before checking, just in case of filesystem lag
                time.sleep(0.1)
                if os.path.exists(target_pdf_path) and os.path.getsize(target_pdf_path) > 100:  # Check size > 100 bytes
                    logger.debug(f"Successfully extracted PDF content to target: {target_filename_base} ({os.path.getsize(target_pdf_path)} bytes)")
                    return True
                else:
                    size = os.path.getsize(target_pdf_path) if os.path.exists(target_pdf_path) else -1
                    logger.error(f"Extraction verification failed for {target_filename_base}. Exists={os.path.exists(target_pdf_path)}, Size={size}. Original zip: {zip_filename_base}")
                    # Clean up failed/empty extraction attempt
                    try_remove(target_pdf_path, logger, "failed/empty extraction target")
                    return False
            else:
                # This case should be covered by the initial check for pdf_members
                logger.error(f"Internal logic error: No pdf_member_name selected despite finding members in {zip_filename_base}")
                return False

    except zipfile.BadZipFile as e:
        logger.error(f"Invalid or corrupted zip file '{zip_filename_base}': {e}")
        return False
    except Exception as e:
        logger.error(f"Unexpected error extracting PDF from '{zip_filename_base}' to '{target_filename_base}': {e}", exc_info=True)
        return False