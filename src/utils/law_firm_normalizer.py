import json
import os
import re
from typing import Dict, Optional, Set

from src.utils.law_firm import LawFirmNameHandler


class LawFirmNormalizer:
    """
    Handles normalization of law firm names by applying standardized mappings and formatting rules.
    
    This class consolidates various name standardization approaches:
    1. Direct mapping from known variations to canonical names
    2. Capitalization and formatting according to legal industry standards
    3. Custom rules for specific patterns in law firm names
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize the normalizer with mapping data from configuration.
        
        Args:
            config_path: Path to the normalization mapping file. If None, uses default path.
        """
        self.name_handler = LawFirmNameHandler()
        
        # Default path to normalization config
        if config_path is None:
            base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            config_path = os.path.join(base_dir, "src", "config", "law_firms", "name_normalization.json")
        
        # Load normalization mappings
        try:
            with open(config_path, 'r') as f:
                # Filter out comment keys (starting with //)
                self.normalization_map = {
                    k: v for k, v in json.load(f).items()
                    if not k.strip().startswith("//")
                }
        except (FileNotFoundError, json.JSONDecodeError) as e:
            print(f"Warning: Could not load law firm normalization map: {e}")
            self.normalization_map = {}
        
        # Common suffixes to standardize (PC, LLC, etc.)
        self.common_suffixes = {
            "PC", "LLC", "LLP", "PA", "PLLC", "APLC", "LPA", 
            "LP", "PLLP", "PLC", "APC", "LLLP", "PSC"
        }
        
        # Patterns to clean before normalization
        self.cleaning_patterns = [
            (r',\s*(?:LLC|LLP|PC|PLLC|PA|P\.A\.|L\.P\.|Inc\.?)\s*$', ''),  # Remove trailing commas before entity types
            (r'\s+&\s+', ' & '),  # Standardize ampersands
            (r'\s+and\s+', ' & '),  # Convert 'and' to '&'
            (r'—', '-'),  # Convert em dashes to regular hyphens
            (r'\s{2,}', ' '),  # Collapse multiple spaces
        ]
    
    def normalize(self, name: str) -> str:
        """
        Normalize a law firm name using mapping tables and formatting rules.
        
        Args:
            name: Original law firm name
            
        Returns:
            Normalized law firm name
        """
        if not name or not isinstance(name, str):
            return ""
        
        # Clean the name first
        clean_name = self._clean_name(name)
        
        # Check direct mapping first (case-insensitive)
        for variant, canonical in self.normalization_map.items():
            if clean_name.lower() == variant.lower():
                return canonical
        
        # Apply capitalization rules
        formatted_name = self.name_handler.capitalize_law_firm_names(clean_name)
        
        # Check if the formatted name is in our mapping
        if formatted_name in self.normalization_map:
            return self.normalization_map[formatted_name]
        
        return formatted_name
    
    def _clean_name(self, name: str) -> str:
        """
        Clean a law firm name by removing unwanted characters and standardizing format.
        
        Args:
            name: Original name string
            
        Returns:
            Cleaned name string
        """
        # Basic cleaning
        cleaned = name.strip()
        
        # Apply regex patterns
        for pattern, replacement in self.cleaning_patterns:
            cleaned = re.sub(pattern, replacement, cleaned)
            
        return cleaned
    
    def build_normalization_map(self, variants: Dict[str, Set[str]]) -> Dict[str, str]:
        """
        Build a normalization mapping from variant lists.
        
        Args:
            variants: Dictionary mapping canonical names to sets of variant forms
            
        Returns:
            Dictionary mapping each variant to its canonical form
        """
        result = {}
        for canonical, variant_set in variants.items():
            for variant in variant_set:
                result[variant] = canonical
        return result
    
    def update_normalization_map(self, new_mappings: Dict[str, str], save: bool = True, config_path: Optional[str] = None) -> None:
        """
        Update the normalization mapping with new entries.
        
        Args:
            new_mappings: Dictionary of new variant->canonical mappings to add
            save: Whether to save the updated mapping to file
            config_path: Path to save to (uses instance default if None)
        """
        # Update in-memory map
        self.normalization_map.update(new_mappings)
        
        # Save to file if requested
        if save:
            if config_path is None:
                base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
                config_path = os.path.join(base_dir, "config", "law_firms", "name_normalization.json")
            
            try:
                with open(config_path, 'w') as f:
                    json.dump(self.normalization_map, f, indent=2, sort_keys=True)
                print(f"Saved updated normalization map to {config_path}")
            except Exception as e:
                print(f"Error saving normalization map: {e}")


# Singleton instance for easy import
_normalizer = None

def get_normalizer(config_path: Optional[str] = None) -> LawFirmNormalizer:
    """
    Get the singleton law firm normalizer instance.
    
    Args:
        config_path: Optional path to normalization config
        
    Returns:
        Shared LawFirmNormalizer instance
    """
    global _normalizer
    if _normalizer is None:
        _normalizer = LawFirmNormalizer(config_path)
    return _normalizer


def normalize_law_firm_name(name: str) -> str:
    """
    Normalize a law firm name using the singleton normalizer.
    
    Args:
        name: Law firm name to normalize
        
    Returns:
        Normalized law firm name
    """
    return get_normalizer().normalize(name)