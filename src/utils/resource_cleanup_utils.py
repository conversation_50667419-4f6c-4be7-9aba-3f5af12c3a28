"""Resource cleanup manager for graceful shutdown."""
import atexit
import logging
import os
import signal
import sys
import time
import traceback
from typing import Optional

class ResourceCleanupManager:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self._register_signal_handlers()
        self._register_atexit_handlers()
        
    def _register_signal_handlers(self):
        """Register signal handlers for graceful shutdown"""
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

    def _register_atexit_handlers(self):
        """Register cleanup handlers to run at exit"""
        atexit.register(self.cleanup_all_resources)

    def _signal_handler(self, signum, frame):
        """Handle termination signals"""
        self.logger.debug(f"Received signal {signum}, performing graceful shutdown")
        self.cleanup_all_resources()
        sys.exit(0)

    def cleanup_all_resources(self):
        """Main cleanup method that orchestrates all cleanup operations"""
        self.logger.debug("Starting comprehensive resource cleanup...")
        
        self._cleanup_resource_tracker()
        self._cleanup_multiprocessing_resources()
        self._cleanup_temp_files()
        
        if sys.platform == 'darwin':
            self._cleanup_macos_semaphores()
        
        self.logger.debug("Resource cleanup completed")

    def _cleanup_resource_tracker(self):
        """Clean up multiprocessing resource tracker"""
        try:
            import multiprocessing.resource_tracker as rt
            
            if hasattr(rt, '_resource_tracker') and rt._resource_tracker is not None:
                try:
                    rt._send('STOP')
                    time.sleep(0.5)
                except Exception as e:
                    self.logger.debug(f"Error stopping resource tracker: {e}")

            if hasattr(rt, 'unregister_all'):
                try:
                    rt.unregister_all()
                except Exception as e:
                    self.logger.debug(f"Error in unregister_all: {e}")
                    
        except Exception as e:
            self.logger.error(f"Error in resource tracker cleanup: {e}")

    def _cleanup_multiprocessing_resources(self):
        """Clean up standard multiprocessing resources"""
        try:
            import multiprocessing
            for process in multiprocessing.active_children():
                process.join(timeout=1.0)
                
            # Force garbage collection
            import gc
            gc.collect()
            
        except Exception as e:
            self.logger.error(f"Error cleaning up multiprocessing resources: {e}")

    def _cleanup_temp_files(self):
        """Clean up temporary files"""
        try:
            import tempfile
            temp_dir = tempfile.gettempdir()
            prefixes = ('marker_', 'pacer_', 'docket_', 'temp_')
            extensions = ('.pdf', '.png', '.json')
            
            for filename in os.listdir(temp_dir):
                if filename.startswith(prefixes) and filename.endswith(extensions):
                    try:
                        os.remove(os.path.join(temp_dir, filename))
                    except Exception as e:
                        self.logger.debug(f"Failed to remove temp file {filename}: {e}")
                        
        except Exception as e:
            self.logger.error(f"Error cleaning up temporary files: {e}")

    def _cleanup_macos_semaphores(self):
        """Clean up macOS system semaphores"""
        if sys.platform != 'darwin':
            return
            
        try:
            import subprocess
            result = subprocess.run(['ipcs', '-s'], capture_output=True, text=True)
            lines = result.stdout.splitlines()
            user_semaphores = []
            
            for line in lines:
                if os.environ.get('USER') in line:
                    parts = line.split()
                    if len(parts) >= 2:
                        user_semaphores.append(parts[1])
            
            for sem_id in user_semaphores:
                try:
                    subprocess.run(['ipcrm', '-s', sem_id], capture_output=True)
                except Exception:
                    pass
                    
            self.logger.debug(f"Cleaned up {len(user_semaphores)} system semaphores")
            
        except Exception as e:
            self.logger.error(f"Error cleaning up macOS semaphores: {e}")

# Global instance
cleanup_manager = ResourceCleanupManager()

# Convenience functions
def cleanup_all():
    cleanup_manager.cleanup_all_resources()

def register_cleanup():
    """Register cleanup handlers for the current process"""
    atexit.register(cleanup_all)
    signal.signal(signal.SIGINT, cleanup_manager._signal_handler)
    signal.signal(signal.SIGTERM, cleanup_manager._signal_handler)