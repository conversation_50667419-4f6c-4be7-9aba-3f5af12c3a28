#!/usr/bin/env python3
import logging
import os
import re
import subprocess
import tempfile
import urllib.parse
from datetime import datetime
from typing import Optional, Tuple, List, Union

import fitz  # PyMuPDF
import pandas as pd
import pytesseract
import requests
from pdf2image import convert_from_path, convert_from_bytes

from src.data_transformer.litigation_classifier import LitigationClassifier


class PDFExtractor:
    """A class for extracting and processing text from PDF documents.

    Can be initialized with either:
    - An S3/web URL containing a PDF (s3_link)
    - A local file path to a PDF (pdf_source)
    """

    PATTERNS = {
        'mdl_number': re.compile(
            r'(?:MDL No\.?\s*:?\s*|1:\d{2}-md-|mdl\sno\.?\s|mdl\s|Master Docket No\.?:?\s*\d:\d{2}-mn-)(?:\d{2}-)?(\d{4,5})(?=\s*(\(|\b))(?!.*-cv-)',
            re.IGNORECASE
        ),
        'document_number': [
            r'\s+[Dd]ocument\s+(\d+)\s+',
            r'Doc #:\s+(\d+)\s+',
            r'\s+[Ee]ntry\s+[Nn]umber\s+(\d+)\s+'
        ],
        'date_formats': [
            r'(Date:|Dated:|Date Filed\s+|SO ORDERED this\s+\d{1,2}\s+day\s+of\s+|)'
            r'(\d{1,2}\s+[A-Za-z]+\s+\d{4}|[A-Za-z]+\s+\d{1,2},\s+\d{4}|\d{1,2}/\d{1,2}/\d{2,4})',
            r'\bDated\s+this\s+(\d{1,2})(?:st|nd|rd|th)\s+day\s+of\s+([A-Za-z]+)\s+(\d{4})\b',
            r'\b([A-Za-z]+)\s+(\d{1,2}),\s+(\d{4})\b'
        ]
    }

    def __init__(self, config, pdf_source=None, s3_link=None):
        """Initialize the PDFExtractor with either a local PDF path or S3/web URL.

        Args:
            config: Configuration object
            pdf_source: Optional local file path to PDF
            s3_link: Optional S3/web URL to PDF
        """
        if not pdf_source and not s3_link:
            raise ValueError("Must provide either pdf_source or s3_link")

        # Initialize logger first
        self.logger = logging.getLogger(__name__)

        self.pdf_source = pdf_source
        self.pdf_content = None  # Will hold PDF bytes if downloaded from URL

        # Initialize litigation classifier
        self.litigation_classifier = LitigationClassifier()

        # Configure Tesseract after logger is initialized
        self._configure_tesseract()

        # Download PDF if URL provided
        if s3_link:
            self._download_pdf(s3_link)

    def _configure_tesseract(self):
        """Configure Tesseract paths and verify installation."""
        try:
            # Try to find Tesseract installation
            tesseract_cmd = subprocess.check_output(['which', 'tesseract']).decode().strip()
            if tesseract_cmd:
                pytesseract.pytesseract.tesseract_cmd = tesseract_cmd
                self.logger.info(f"Found Tesseract at: {tesseract_cmd}")

            # Get Tesseract data directory
            base_dir = os.path.dirname(os.path.dirname(tesseract_cmd))
            tessdata_dir = os.path.join(base_dir, 'share', 'tessdata')

            if os.path.exists(tessdata_dir):
                os.environ['TESSDATA_PREFIX'] = tessdata_dir
                self.logger.info(f"Set TESSDATA_PREFIX to: {tessdata_dir}")
            else:
                self.logger.warning(f"Tessdata directory not found at: {tessdata_dir}")

        except (subprocess.CalledProcessError, FileNotFoundError):
            error_msg = ("Tesseract is not installed. Please install it using: \n"
                         "brew install tesseract")
            self.logger.error(error_msg)
            raise RuntimeError(error_msg)

        # Verify Tesseract is working
        try:
            pytesseract.get_tesseract_version()
            self.logger.info("Tesseract configuration successful")
        except pytesseract.TesseractNotFoundError as e:
            error_msg = (f"Tesseract configuration failed: {str(e)}\n"
                         "Please ensure Tesseract is properly installed")
            self.logger.error(error_msg)
            raise RuntimeError(error_msg)

    def _download_pdf(self, url: str) -> None:
        """Download PDF from URL and store in memory."""
        if not self._is_valid_url(url):
            raise ValueError(f"Invalid URL provided: {url}")

        try:
            response = requests.get(url, stream=True)
            response.raise_for_status()
            self.pdf_content = response.content
            self.logger.info("Successfully downloaded PDF to memory")
        except Exception as e:
            self.logger.error(f"Error downloading PDF from {url}: {e}")
            raise

    @staticmethod
    def _is_valid_url(url: str) -> bool:
        """Check if the given string is a valid URL."""
        try:
            result = urllib.parse.urlparse(url)
            return all([result.scheme in ['http', 'https'], result.netloc])
        except ValueError:
            return False

    def _get_document(self) -> fitz.Document:
        """Get fitz document object from either memory content or local file."""
        try:
            if self.pdf_content is not None:
                return fitz.open(stream=self.pdf_content, filetype="pdf")
            elif self.pdf_source and os.path.exists(self.pdf_source):
                return fitz.open(self.pdf_source)
            else:
                raise ValueError("No valid PDF source available")
        except Exception as e:
            self.logger.error(f"Error opening PDF: {e}")
            raise

    def extract_text_with_pymupdf(self, first_page=None, last_page=None) -> Optional[str]:
        """Extract text from PDF using PyMuPDF."""
        try:
            document = self._get_document()
            all_text = []
            start_page = first_page - 1 if first_page else 0
            end_page = last_page if last_page else len(document)

            for page_number in range(start_page, end_page):
                page = document.load_page(page_number)
                self.logger.debug(f'Processing page {page_number}')
                text = page.get_text("text")
                if self.is_text_garbled(text):
                    self.logger.info(f"Text is garbled on page {page_number + 1}. Falling back to Tesseract.")
                    return None
                all_text.append(text)

            return "\n".join(all_text)
        except Exception as e:
            self.logger.error(f"Error extracting text with PyMuPDF: {e}")
            return None

    def extract_text_with_tesseract(self, first_page=None, last_page=None) -> Optional[str]:
        """Extract text from PDF using Tesseract OCR."""
        try:
            if self.pdf_content is not None:
                images = convert_from_bytes(
                    self.pdf_content,
                    first_page=first_page or 1,
                    last_page=last_page,
                    fmt='ppm',
                    grayscale=True,
                    size=(1700, None)
                )
            else:
                images = convert_from_path(
                    self.pdf_source,
                    first_page=first_page or 1,
                    last_page=last_page,
                    fmt='ppm',
                    grayscale=True,
                    size=(1700, None)
                )

            texts = []
            for image in images:
                text = pytesseract.image_to_string(image)
                if text.strip():
                    texts.append(text)

            return "\n".join(texts) if texts else None

        except Exception as e:
            self.logger.error(f"Error extracting text with Tesseract: {e}")
            return None

    def extract_text_by_pages(self, first_page=None, last_page=None) -> Optional[str]:
        """Extract text from specified page range, falling back to Tesseract if needed."""
        # Try PyMuPDF first
        text = self.extract_text_with_pymupdf(first_page=first_page, last_page=last_page)
        if text:
            return text

        # Fall back to Tesseract if PyMuPDF fails or returns garbled text
        self.logger.info("Falling back to Tesseract for text extraction")
        return self.extract_text_with_tesseract(first_page=first_page, last_page=last_page)

    def extract_last_three_pages(self) -> List[str]:
        """Extract text from the last three pages of the PDF."""
        try:
            document = self._get_document()
            total_pages = len(document)
            start_page = max(0, total_pages - 3)

            pages_text = []
            for page_number in range(start_page, total_pages):
                text = self.extract_text_by_pages(first_page=page_number + 1, last_page=page_number + 1)
                if text:
                    pages_text.append(text.strip())

            return pages_text
        except Exception as e:
            self.logger.error(f"Error extracting last three pages: {e}")
            return []

    def extract_signature_page(self, use_tesseract=False) -> str:
        """Extract the signature page from the PDF."""
        try:
            doc = self._get_document()
            total_pages = len(doc)
            start_page = max(0, total_pages - 5)

            pages_text = []
            for page_number in range(start_page, total_pages):
                if use_tesseract:
                    text = self.extract_text_with_tesseract(first_page=page_number + 1, last_page=page_number + 1)
                else:
                    page = doc.load_page(page_number)
                    text = page.get_text("text")
                    if self.is_text_garbled(text) or not text.strip():
                        text = self.extract_text_with_tesseract(first_page=page_number + 1, last_page=page_number + 1)

                pages_text.append(text.strip())

            if not pages_text:
                return ""

            last_page = pages_text[-1]
            if 'certificate of service' in last_page.lower():
                last_page = pages_text[-2] if len(pages_text) > 1 else last_page

            for i, page in enumerate(pages_text[:-1]):
                if any(indicator in page.lower() for indicator in ['respectfully submitted', 'signature block', '/s/']):
                    return page + " " + pages_text[i + 1]

            return last_page
        except Exception as e:
            self.logger.error(f"Error extracting signature page: {e}")
            return ""

    def identify_litigation(self, mdl_litigations, all_pages=None) -> Tuple[Optional[str], Optional[str]]:
        """Identify litigation type from the document."""
        first_page_text = self.get_first_page_text(all_pages)
        if not first_page_text:
            return None, None

        litigation_name, mdl_number = self.find_mdl_litigation(mdl_litigations, first_page_text)
        if litigation_name:
            return litigation_name, str(mdl_number)

        return self.identify_litigation_by_text(first_page_text)

    def find_mdl_litigation(self, mdl_litigations: pd.DataFrame, first_page_text: str) -> Tuple[
        Optional[str], Optional[str]]:
        """Find MDL litigation information from text."""
        for mdl_match in re.finditer(self.PATTERNS['mdl_number'], first_page_text):
            mdl_number = mdl_match.group(1).lstrip('0') if len(mdl_match.group(1)) > 4 else mdl_match.group(1)
            self.logger.debug(f'MDL Match: {mdl_number}')
            try:
                litigation_name = mdl_litigations.loc[mdl_litigations['mdl_num'] == mdl_number, 'litigation'].values[0]
                return litigation_name, str(mdl_number)
            except Exception as e:
                self.logger.error(e)
        return None, None

    def get_first_page_text(self, all_pages=None) -> Optional[str]:
        """Get text from the first page."""
        if all_pages:
            return all_pages[0] if isinstance(all_pages, list) else all_pages
        try:
            return self.extract_text_with_pymupdf(first_page=1, last_page=2)
        except Exception as e:
            self.logger.error(f"Error getting first page text: {e}")
            return None

    def identify_litigation_by_text(self, first_page_text: str) -> Tuple[Optional[str], Optional[str]]:
        """Identify litigation type based on text content."""
        return self.litigation_classifier.identify_litigation_by_text(first_page_text)

    @staticmethod
    def is_removal_case(first_page_text: str) -> bool:
        """Determine if the case is a removal case."""
        if "forma pauperis" in first_page_text:
            return True
        if "notice of removal" in first_page_text and 'asbestos' not in first_page_text:
            return True
        return False

    @staticmethod
    def extract_document_number(text: str) -> Optional[str]:
        """Extract document number from text."""
        match = (
                re.search(r'\s+[Dd]ocument\s+(\d+)\s+', text) or
                re.search(r'Doc #:\s+(\d+)\s+', text) or
                re.search(r'\s+[Ee]ntry\s+[Nn]umber\s+(\d+)\s+', text)
        )
        return match.group(1) if match else None

    def extract_filing_date(self) -> str:
        """Extract filing date from document."""
        try:
            all_text = self.extract_text_with_tesseract()
            if not all_text:
                return "NA"

            # Try each date pattern
            for pattern in self.PATTERNS['date_formats']:
                date_match = re.search(pattern, all_text, re.IGNORECASE)
                if date_match:
                    try:
                        if "day of" in pattern:
                            day, month, year = date_match.groups()[-3:]
                            date_str = f"{month} {day} {year}"
                            return datetime.strptime(date_str, '%B %d %Y').strftime('%m/%d/%Y')
                        else:
                            date_str = date_match.group(2).strip()
                            date_str = re.sub(r'\band\b|\n', '', date_str).strip()

                            if re.match(r'\d{1,2}\s+[A-Za-z]+\s+\d{4}', date_str):
                                return datetime.strptime(date_str, '%d %B %Y').strftime('%m/%d/%Y')
                            elif re.match(r'[A-Za-z]+\s+\d{1,2},\s+\d{4}', date_str):
                                return datetime.strptime(date_str, '%B %d, %Y').strftime('%m/%d/%Y')
                            else:
                                return pd.to_datetime(date_str).strftime('%m/%d/%Y')
                    except Exception as e:
                        self.logger.error(f"Error parsing date: {e}")
                        continue

            return "NA"
        except Exception as e:
            self.logger.error(f"Error extracting filing date: {e}")
            return "NA"

    def extract_text_with_tesseract_local(self, pages: Union[int, List[int], slice, None] = None) -> str:
        """Extract text from PDF using Tesseract with flexible page selection."""
        try:
            if self.pdf_content is not None:
                images = convert_from_bytes(self.pdf_content)
            else:
                images = convert_from_path(self.pdf_source)

            total_pages = len(images)

            if pages is None:
                selected_images = images
            elif isinstance(pages, slice):
                selected_images = images[pages]
            elif isinstance(pages, (int, list)):
                if isinstance(pages, int):
                    pages = [pages]
                selected_indices = [i if i >= 0 else total_pages + i for i in pages]
                selected_indices = [i for i in selected_indices if 0 <= i < total_pages]
                selected_images = [images[i] for i in selected_indices]
            else:
                raise ValueError("Invalid 'pages' parameter")

            return "\n".join([
                pytesseract.image_to_string(image)
                for image in selected_images
            ])
        except Exception as e:
            self.logger.error(f"Error processing PDF with Tesseract: {e}")
            return ""

    def extract_doc_num(self) -> str:
        """Extract document number using either PyMuPDF or Tesseract."""
        try:
            # Try PyMuPDF first
            text = self.extract_text_with_pymupdf(first_page=1, last_page=1)
            if text:
                doc_num = self.extract_document_number(text)
                if doc_num:
                    return doc_num

            # Fall back to Tesseract
            text = self.extract_text_with_tesseract(first_page=1, last_page=1)
            if text:
                doc_num = self.extract_document_number(text)
                if doc_num:
                    return doc_num

            return 'NA'
        except Exception as e:
            self.logger.error(f"Error extracting document number: {e}")
            return 'NA'

    def extract_text_from_pdf(self) -> str:
        """Extract all text from the PDF using PyMuPDF with Tesseract fallback."""
        try:
            # Try PyMuPDF first
            text = self.extract_text_with_pymupdf()
            if text and not self.is_text_garbled(text):
                return text

            # Fall back to Tesseract
            return self.extract_text_with_tesseract() or ""
        except Exception as e:
            self.logger.error(f"Error extracting text from PDF: {e}")
            return ""

    @staticmethod
    def is_text_garbled(text: str) -> bool:
        """Check if the text is garbled using various heuristics."""
        if not text.strip():
            return True

        # Check for hex characters
        hex_pattern = r'(\\x[0-9A-Fa-f]{2})'
        hex_matches = re.findall(hex_pattern, text)
        if len(hex_matches) > 0 and (sum(len(match) for match in hex_matches) / len(text)) > 0.3:
            return True

        # Check for non-printable characters
        non_printable_pattern = r'[\x00-\x1F\x7F-\x9F]'
        non_printable_matches = re.findall(non_printable_pattern, text)
        if len(non_printable_matches) > 0 and (sum(len(match) for match in non_printable_matches) / len(text)) > 0.3:
            return True

        # Check for consonant clusters
        consonant_pattern = r'[^AEIOUaeiou\s]{3,}'
        consonant_matches = re.findall(consonant_pattern, text)
        if len(consonant_matches) > 0 and (sum(len(match) for match in consonant_matches) / len(text)) > 0.3:
            return True

        return False

    def extract_text_from_page_range(self, start_page: int, end_page: int) -> str:
        """Extract text from a specific page range."""
        try:
            text = self.extract_text_with_pymupdf(first_page=start_page, last_page=end_page)
            if text and not self.is_text_garbled(text):
                return text

            return self.extract_text_with_tesseract(first_page=start_page, last_page=end_page) or ""
        except Exception as e:
            self.logger.error(f"Error extracting text from page range {start_page}-{end_page}: {e}")
            return ""

    def extract_text_from_image(self, image_path_or_url: str) -> str:
        """Extract text from an image (PNG or JPG) using Tesseract OCR.

        Args:
            image_path_or_url (str): Path to the image file or URL (supports PNG and JPG)

        Returns:
            str: Extracted text from the image, including garbled text if detected

        Raises:
            FileNotFoundError: If the image file doesn't exist locally
            ValueError: If the image path is invalid or empty
            RuntimeError: If there's an error processing the image
        """
        try:
            self.logger.debug(f"Starting extract_text_from_image for: {image_path_or_url}") # ADDED

            # Check if input is a URL
            if image_path_or_url.startswith('http'):
                # Download the image from the URL
                response = requests.get(image_path_or_url, stream=True)
                response.raise_for_status()

                # Save image to a temporary file
                with tempfile.NamedTemporaryFile(suffix=".jpg", delete=False) as temp_file:
                    for chunk in response.iter_content(1024):
                        temp_file.write(chunk)
                    image_path = temp_file.name
            else:
                image_path = image_path_or_url

            if not image_path:
                raise ValueError("Image path cannot be empty")

            if not os.path.exists(image_path):
                raise FileNotFoundError(f"Image file not found: {image_path}")

            # Verify file is a PNG or JPG
            if not (image_path.lower().endswith('.png') or image_path.lower().endswith(
                    '.jpg') or image_path.lower().endswith('.jpeg')):
                raise ValueError("Only PNG and JPG images are supported")

            self.logger.debug(f"Using Tesseract to extract text from image: {image_path}") # ADDED

            # Extract text using Tesseract
            start_time = datetime.now() # ADDED
            text = pytesseract.image_to_string(image_path)
            self.logger.debug(f"Tesseract processing time: {datetime.now() - start_time}") # ADDED

            # Return the text even if it's garbled
            return text.strip()

        except requests.RequestException as e:
            self.logger.error(f"Error downloading image from URL: {e}")
            return ""
        except pytesseract.TesseractError as e:
            self.logger.error(f"Tesseract error processing image {image_path_or_url}: {e}")
            return ""
        except Exception as e:
            self.logger.error(f"Error extracting text from image {image_path_or_url}: {e}")
            if isinstance(e, (FileNotFoundError, ValueError)):
                raise
            raise RuntimeError(f"Failed to process image: {str(e)}")
        finally:
            # Clean up temporary file if created
            if image_path != image_path_or_url and os.path.exists(image_path):
                try:
                    os.remove(image_path)
                except Exception as e:
                    self.logger.warning(f"Failed to remove temp file {image_path}: {e}")

    def cleanup(self):
        """Clean up resources used by the PDFExtractor.

        This method should be called when done using the PDFExtractor instance
        to properly close open files and clean up temporary resources.
        """
        try:
            # Clean up PyMuPDF document if it exists
            doc = getattr(self, '_doc', None)
            if doc:
                try:
                    doc.close()
                except Exception as e:
                    self.logger.error(f"Error closing PDF document: {e}")

            # Clear PDF content from memory
            self.pdf_content = None

            # Clean up temporary directory used by pdf2image if it exists
            temp_dir = tempfile.gettempdir()
            if temp_dir:
                try:
                    # Only remove files that match pdf2image's temporary file pattern
                    for filename in os.listdir(temp_dir):
                        if filename.startswith('pdf2image_'):
                            filepath = os.path.join(temp_dir, filename)
                            try:
                                os.remove(filepath)
                            except OSError as e:
                                self.logger.warning(f"Could not remove temporary file {filepath}: {e}")
                except Exception as e:
                    self.logger.error(f"Error cleaning temporary directory: {e}")

            self.logger.info("Cleanup completed successfully")

        except Exception as e:
            self.logger.error(f"Error during cleanup: {e}")

    def __enter__(self):
        """Context manager entry point.

        Returns:
            PDFExtractor: The PDFExtractor instance
        """
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit point.

        Ensures cleanup() is called when exiting the context manager block,
        even if an exception occurred.

        Args:
            exc_type: The type of the exception that occurred, if any
            exc_val: The instance of the exception that occurred, if any
            exc_tb: The traceback of the exception that occurred, if any
        """
        self.cleanup()
        # Don't suppress any exceptions that occurred
        return False

if __name__ == '__main__': # ADDED __main__ block to pdf_extractor.py
    import logging
    from src.config_models.loader import load_config

    logging.basicConfig(level=logging.DEBUG)  # Set logging level to DEBUG for testing

    config = load_config('scraper', {'date': '01/01/1970'})  # You might need a minimal config if PDFExtractor uses it
    extractor = PDFExtractor(config, pdf_source="https://cdn.lexgenius.ai/20250216/dockets/mnd_25_00604_Marler_v_3M_Company_et_al.pdf") # pdf_source is a placeholder, not used in image extraction test

    # Test with a sample image URL (replace with a URL of a sample JPG or PNG image)
    sample_image_url = "https://cdn.lexgenius.ai/adarchive/fb/2345498905823541/495838533545578.jpg" # Replace with a valid image URL for testing, e.g., a facebook ad image url
    # if "https://cdn.lexgenius.ai/adarchive/fb/2345498905823541/495838533545578.jpg" not in sample_image_url: # prevent accidental test without replacing placeholder
    try:
        extracted_text = extractor.extract_text_from_image(sample_image_url)
        logging.info(f"Extracted Text from URL:\n{extracted_text}")
    except Exception as e:
        logging.error(f"Error extracting text from URL: {e}")

    extractor.cleanup() # Cleanup after test
    print("PDFExtractor test completed.")