import ast
import logging
import re
import time
from datetime import datetime
from typing import Dict

# Direct async imports - no compatibility layer needed
from src.repositories.district_courts_repository import DistrictCourtsRepository
from src.repositories.pacer_repository import PacerRepository
from .mdl_processor import MDLProcessor


class TransferHandler:
    """Handles transfer-related logic for dockets."""
    NULL_CONDITIONS = ['', 'NA', None, 'Pro Se', 'PRO SE', "None", []]

    def __init__(self, config: Dict, pacer_db: PacerRepository,
                 district_court_db: DistrictCourtsRepository, mdl_processor: MDLProcessor):
        self.config = config
        self.logger = logging.getLogger(__name__)  # Use __name__ for logger
        # Ensure proper logging configuration for data_transformer modules
        self.logger.handlers = []
        self.logger.propagate = True
        self.pacer_db = pacer_db
        self.district_court_db = district_court_db
        self.mdl_processor = mdl_processor
        self.logger.debug("TransferHandler initialized. Will use MDLProcessor for MDL lookup.")

        # Initialize District Court lookup cache attributes
        self._district_court_lookup_cache = None
        self._district_court_lookup_loaded = False

    async def _create_mdl_lookup_async(self) -> Dict[str, str]:
        """
        Delegates MDL lookup (mapping MDL num to court ID) to MDLProcessor.
        
        Async version following the new async architecture pattern.
        """
        # self.logger.debug("Requesting MDL lookup from MDLProcessor...") # Reduce verbosity
        return await self.mdl_processor._create_mdl_lookup_async()
    
    def _create_mdl_lookup(self) -> Dict[str, str]:
        """
        Legacy sync method - deprecated.
        
        This method is deprecated and will be removed. Use _create_mdl_lookup_async() instead.
        """
        self.logger.warning("Using deprecated sync MDL lookup method. Please migrate to async version.")
        # Fallback to legacy sync implementation for backward compatibility
        return self.mdl_processor._create_mdl_lookup_legacy_sync()

    async def _create_district_court_lookup_async(self) -> Dict[str, str]:
        """Creates a lookup dictionary for district courts (name -> ID). Uses caching.
        
        Async version following the new async architecture pattern.
        """
        if self._district_court_lookup_loaded:
            return self._district_court_lookup_cache if self._district_court_lookup_cache is not None else {}
        if not self.district_court_db:
            self.logger.error("District court DB not available for court name lookup.")
            self._district_court_lookup_cache = {}
            self._district_court_lookup_loaded = True
            return {}

        self.logger.debug("Creating district court lookup dictionary (first time)...")
        lookup_dict = {}
        try:
            start_time = time.time()
            self.logger.info(f"Scanning DistrictCourts async repository for name lookup...")
            # Use the async repository pattern
            court_items = await self.district_court_db.scan_all()
            duration = time.time() - start_time
            self.logger.info(f"District Court async scan complete in {duration:.2f}s, found {len(court_items)} items.")

            if court_items:
                for court in court_items:
                    full_name = court.get('CourtName')
                    short_name = full_name.replace(' District Court', '') if full_name else None
                    court_id = court.get('CourtId')
                    if full_name and court_id: lookup_dict[full_name.strip()] = court_id
                    if short_name and court_id and short_name != full_name: lookup_dict[
                        short_name.strip()] = court_id
            else:
                self.logger.warning("District Court scan for name lookup returned no items.")
            self._district_court_lookup_cache = lookup_dict
            self.logger.debug(f"District court lookup created with {len(lookup_dict)} entries.")

        except Exception as e:
            self.logger.error(f"Error creating district court lookup: {str(e)}", exc_info=True)
            self._district_court_lookup_cache = {}  # Reset on error
        finally:
            self._district_court_lookup_loaded = True  # Mark as loaded (even if error)

        return self._district_court_lookup_cache if self._district_court_lookup_cache is not None else {}
    
    def _create_district_court_lookup(self) -> Dict[str, str]:
        """
        Legacy sync method - deprecated.
        
        This method is deprecated and will be removed. Use _create_district_court_lookup_async() instead.
        """
        self.logger.warning("Using deprecated sync district court lookup method. Please migrate to async version.")
        if self._district_court_lookup_loaded:
            return self._district_court_lookup_cache if self._district_court_lookup_cache is not None else {}
        if not self.district_court_db:
            self.logger.error("District court DB not available for court name lookup.")
            self._district_court_lookup_cache = {}
            self._district_court_lookup_loaded = True
            return {}

        self.logger.debug("Creating district court lookup dictionary (first time)...")
        lookup_dict = {}
        try:
            start_time = time.time()
            self.logger.info(f"Scanning DistrictCourts table for name lookup...")
            # TODO: Convert to async in Phase 2 - for now, create basic bridge method
            court_items = self._sync_scan_courts()
            duration = time.time() - start_time
            self.logger.info(f"District Court scan complete in {duration:.2f}s, found {len(court_items)} items.")

            if court_items:
                for court in court_items:
                    full_name = court.get('CourtName')
                    short_name = full_name.replace(' District Court', '') if full_name else None
                    court_id = court.get('CourtId')
                    if full_name and court_id: lookup_dict[full_name.strip()] = court_id  # Add strip
                    if short_name and court_id and short_name != full_name: lookup_dict[
                        short_name.strip()] = court_id  # Add strip
            else:
                self.logger.warning("District Court scan for name lookup returned no items.")
            self._district_court_lookup_cache = lookup_dict
            self.logger.debug(f"District court lookup created with {len(lookup_dict)} entries.")

        except Exception as e:
            self.logger.error(f"Error creating district court lookup: {str(e)}", exc_info=True)
            self._district_court_lookup_cache = {}  # Reset on error
        finally:
            self._district_court_lookup_loaded = True  # Mark as loaded (even if error)

        return self._district_court_lookup_cache if self._district_court_lookup_cache is not None else {}
    
    def _sync_scan_courts(self):
        """Temporary bridge method to handle repository differences.
        
        TODO: This will be properly converted to async in Phase 2.
        For now, this provides basic functionality to eliminate compatibility layer.
        """
        try:
            # Since we're in a sync context but have async repository,
            # we'll use a simpler approach for now
            # This is a temporary bridge until full async conversion
            import asyncio
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                return loop.run_until_complete(self.district_court_db.scan_all())
            finally:
                loop.close()
        except Exception as e:
            self.logger.error(f"Error in sync court scan bridge: {e}")
            return []

    async def process_transfers(self, data: Dict):
        """Process all transfer-related updates."""
        await self.process_transfer_conditions(data)
        self.update_s3_html_link_for_transferred_case(data)
        # Update transferor case with transferee information if this is a transferred case
        if data.get('transferor_court_id') and data.get('transferor_docket_num'):
            self._update_transferor_case_with_transferee_info(data)
        # self.update_mdl_if_transferred(data) # Keep commented - seems redundant/risky

    async def process_transfer_conditions(self, data: Dict):
        """Process transfer information and set relevant flags."""
        if not isinstance(data, dict):
            self.logger.warning("Invalid data passed to process_transfer_conditions.")
            return

        transferor_court_id = data.get('transferor_court_id')
        court_id = data.get('court_id')
        is_removal = data.get('is_removal', False)
        mdl_num_raw = data.get('mdl_num')
        docket_num = data.get('docket_num', 'NA')
        log_filename = data.get('new_filename', docket_num)  # Use new_filename for logging
        mdl_court_id = None
        mdl_num_str = None

        # Initialize/Ensure boolean flags exist
        data['transferred_in'] = bool(data.get('transferred_in', False))
        data['is_transferred'] = bool(data.get('is_transferred', False))
        data['pending_cto'] = bool(data.get('pending_cto', False))
        data['is_removal'] = bool(is_removal)  # Use initial value

        # Get MDL Court ID lookup
        mdl_lookup = await self._create_mdl_lookup_async()

        if mdl_num_raw not in self.NULL_CONDITIONS:
            mdl_num_str = str(mdl_num_raw).replace('.0', '')  # Clean potential float artifact
            mdl_court_id = mdl_lookup.get(mdl_num_str)

        self.logger.debug(f"Processing transfers for {log_filename}: Court={court_id}, MDL={mdl_num_str}, MDLCourt={mdl_court_id}, Transferor={transferor_court_id}")

        # Rule 1: Special MDL cases (often test/placeholder)
        if mdl_num_str in ['25', '9000']:
            data.update({'transferred_in': False, 'is_transferred': False, 'pending_cto': False})
            self.logger.debug(f"Applying special MDL rule for {log_filename}")
            return

        # Rule 2: Has MDL, NO transferor info, NOT in MDL court -> Pending CTO
        if mdl_num_str and not transferor_court_id and mdl_court_id and court_id != mdl_court_id:
            data.update({'transferred_in': False, 'is_transferred': False, 'pending_cto': True})
            self.logger.debug(f"Applying Pending CTO rule (Rule 2) for {log_filename}")
            return

        # Rule 3: Has MDL, NO transferor info, IS in MDL court -> Not transferred/pending
        if mdl_num_str and not transferor_court_id and mdl_court_id and court_id == mdl_court_id:
            data.update({'transferred_in': False, 'is_transferred': False, 'pending_cto': False})
            self.logger.debug(f"Applying In MDL Court rule (Rule 3, no transferor) for {log_filename}")
            return

        # Rule 4: Has MDL, IS in MDL court, HAS different valid transferor -> Transferred IN
        is_valid_transferor = isinstance(transferor_court_id, str) and 3 <= len(transferor_court_id) <= 4
        if mdl_num_str and mdl_court_id and court_id == mdl_court_id and is_valid_transferor and transferor_court_id != court_id:
            data.update({'transferred_in': True, 'is_transferred': True, 'pending_cto': False})
            self.logger.debug(f"Applying Transferred IN rule (Rule 4) for {log_filename}")
            return

        # Process 'case_in_other_court' field (often source court for removal/transfer)
        case_in_other_court_str = data.get('case_in_other_court')
        if isinstance(case_in_other_court_str, str) and case_in_other_court_str not in self.NULL_CONDITIONS:
            # self.logger.debug(f"Processing 'case_in_other_court': {case_in_other_court_str}") # Reduce verbosity
            try:
                parts = case_in_other_court_str.split(',', 1)  # Split only once
                if len(parts) == 2:
                    potential_transferor_name = parts[0].strip()
                    potential_transferor_docket = parts[1].strip()

                    updated_transferor_info = False
                    # Only update if fields are missing or different
                    if data.get('transferor_court_docket_num') != potential_transferor_docket:
                        data['transferor_court_docket_num'] = potential_transferor_docket
                        updated_transferor_info = True
                    if data.get('transferor_court_name') != potential_transferor_name:
                        data['transferor_court_name'] = potential_transferor_name
                        updated_transferor_info = True

                        court_lookup = await self._create_district_court_lookup_async()
                        # Try exact match first, then without "District Court"
                        found_court_id = court_lookup.get(potential_transferor_name)
                        if not found_court_id:
                            short_name = potential_transferor_name.replace(' District Court', '').strip()
                            found_court_id = court_lookup.get(short_name)

                        # Update transferor_court_id only if it's missing or different AND found
                        if found_court_id and data.get('transferor_court_id') != found_court_id:
                            # Check if the found ID is the same as the current court ID - this might indicate a removal case
                            if found_court_id == court_id:
                                self.logger.warning(
                                    f"'case_in_other_court' name '{potential_transferor_name}' resolves to current court_id '{court_id}' for {log_filename}. Ignoring transferor_court_id update.")
                            else:
                                data['transferor_court_id'] = found_court_id
                                updated_transferor_info = True
                                self.logger.debug(
                                    f"Set/Updated transferor_court_id = {found_court_id} from 'case_in_other_court' name lookup for {log_filename}.")
                                # Re-run logic is tempting but risky. Let subsequent runs handle it.
                        elif not found_court_id:
                            self.logger.warning(
                                f"Could not find Court ID for name from 'case_in_other_court': '{potential_transferor_name}' for {log_filename}.")
                    # Log if any transferor info was updated
                    # if updated_transferor_info:
                    #     self.logger.debug(f"Updated transferor details from 'case_in_other_court' for {log_filename}.")

                else:
                    self.logger.warning(
                        f"Could not parse 'case_in_other_court' into two parts: {case_in_other_court_str}")
            except Exception as e:
                self.logger.error(f"Error processing 'case_in_other_court' field for {log_filename}: {str(e)}",
                                  exc_info=True)

        # Log final state if flags might have changed
        self.logger.debug(f"No transfer rules applied - Final transfer flags for {log_filename}: In={data['transferred_in']}, Is={data['is_transferred']}, CTO={data['pending_cto']}, Removal={data['is_removal']}")

    @staticmethod
    def _update_data_from_transferor(data: Dict, transferor_data: Dict):
        """Update current docket data using info from the transferor docket (use cautiously)."""
        logger = logging.getLogger(__name__)
        log_filename = data.get('new_filename', data.get('docket_num', 'NA'))
        updated_count = 0

        # Fields to potentially update (target_field: source_field_in_transferor_data)
        # Be VERY selective about what to copy to avoid overwriting better/later info
        fields_to_consider = {
            'mdl_num': 'MdlNum',  # Update MDL num only if missing in current
            'law_firm': 'LawFirm',  # Update primary law firm string only if missing
            # 'legacy_law_firm2': 'LawFirm2', # Update legacy field if missing
            # Add other fields very cautiously
        }

        for target_field, source_field in fields_to_consider.items():
            source_value = transferor_data.get(source_field)
            # Update if target is missing/null AND source has a non-null value
            if data.get(
                    target_field) in TransferHandler.NULL_CONDITIONS and source_value not in TransferHandler.NULL_CONDITIONS:
                data[target_field] = source_value
                logger.debug(
                    f"Updated '{target_field}' for {log_filename} from transferor docket ({transferor_data.get('CourtId', 'NA')}:{transferor_data.get('DocketNum', 'NA')}).")
                updated_count += 1

        if updated_count == 0:
            logger.debug(f"No fields updated from transferor docket data for {log_filename}.")

    # update_mdl_if_transferred is commented out as requested/implied by previous state
    # def update_mdl_if_transferred(self, data: Dict): ...

    def _update_transferor_case_with_transferee_info(self, data: Dict):
        """Update the transferor case in the database with transferee information."""
        transferor_court_id = data.get('transferor_court_id')
        transferor_docket_num = data.get('transferor_docket_num')
        
        if not transferor_court_id or not transferor_docket_num:
            return
            
        # Get current case (transferee) information
        transferee_court_id = data.get('court_id')
        transferee_docket_num = data.get('docket_num')
        
        if not transferee_court_id or not transferee_docket_num:
            self.logger.warning(f"Missing transferee information, cannot update transferor case")
            return
            
        log_filename = data.get('new_filename', data.get('docket_num', 'NA'))
        
        try:
            # First, fetch the transferor case to get its primary key
            transferor_items = self.pacer_db.get_docket_items(transferor_court_id, transferor_docket_num)
            
            if not transferor_items or not isinstance(transferor_items, list) or len(transferor_items) == 0:
                self.logger.warning(
                    f"[TransferHandler] Transferor case {transferor_court_id}:{transferor_docket_num} not found in DB, cannot update with transferee info")
                return
                
            transferor_data = transferor_items[0]
            
            # Get the primary key fields needed for update
            filing_date = transferor_data.get('FilingDate')
            docket_num = transferor_data.get('DocketNum')
            
            if not filing_date or not docket_num:
                self.logger.error(
                    f"[TransferHandler] Missing primary key fields for transferor case {transferor_court_id}:{transferor_docket_num}")
                return
                
            # Prepare update payload
            update_payload = {
                'TransfereeDocketNum': transferee_docket_num,
                'TransfereeCourtId': transferee_court_id,
                'LastTransferUpdateTimestamp': datetime.now().isoformat()
            }
            
            # Key for update operation
            key = {
                'FilingDate': filing_date,
                'DocketNum': docket_num
            }
            
            self.logger.info(
                f"[TransferHandler] Updating transferor case {transferor_court_id}:{transferor_docket_num} "
                f"with transferee info {transferee_court_id}:{transferee_docket_num}")
            
            # Update the transferor case
            success = self.pacer_db.update_item(key, update_payload, consistent_read_verify=False)
            
            if success:
                self.logger.info(
                    f"[TransferHandler] Successfully updated transferor case with transferee information")
            else:
                self.logger.error(
                    f"[TransferHandler] Failed to update transferor case with transferee information")
                
        except Exception as e:
            self.logger.error(
                f"[TransferHandler] Exception updating transferor case {transferor_court_id}:{transferor_docket_num}: {e}",
                exc_info=True)

    def update_s3_html_link_for_transferred_case(self, data: Dict):
        """Update S3 PDF link from transferor if current link is missing/invalid."""
        current_s3_link = data.get('s3_link')
        log_filename = data.get('new_filename', data.get('docket_num', 'NA'))
        # Check if current link looks like a valid PDF CDN link
        is_current_link_valid = isinstance(current_s3_link,
                                           str) and 'cdn.lexgenius.ai' in current_s3_link and current_s3_link.lower().endswith(
            '.pdf')

        if is_current_link_valid:
            self.logger.debug(
                f"[TransferHandler/S3Link] Current s3_link for {log_filename} is already valid: {current_s3_link}. Skipping transferor check.")
            # Call check/convert just to ensure format consistency, even if deemed valid
            self._check_and_convert_s3_link(data)
            return  # No need to check transferor

        transferor_court_id = data.get('transferor_court_id')
        transferor_docket_num = data.get('transferor_docket_num')  # Use correct key

        if transferor_court_id and transferor_docket_num:
            # --- Added Log Point 1 ---
            self.logger.info(
                f"[TransferHandler/S3Link] Current s3_link invalid/missing for {log_filename}. Querying DB for transferor {transferor_court_id}:{transferor_docket_num}")
            transferor_dockets = None
            try:
                # Querying DB can be slow, do it only if necessary
                transferor_dockets = self.pacer_db.get_docket_items(transferor_court_id, transferor_docket_num)
                # --- Added Log Point 2 ---
                found_count = len(transferor_dockets) if isinstance(transferor_dockets, list) else 0
                self.logger.info(
                    f"[TransferHandler/S3Link] DB query for {transferor_court_id}:{transferor_docket_num} returned {found_count} items.")

            except Exception as e:
                # --- Added Log Point 5 (in except) ---
                self.logger.error(
                    f"[TransferHandler/S3Link] DB Error querying transferor {transferor_court_id}:{transferor_docket_num} for {log_filename}: {e}",
                    exc_info=True)  # Added exc_info
                return  # Stop if DB query fails

            if transferor_dockets and isinstance(transferor_dockets, list) and len(
                    transferor_dockets) > 0 and isinstance(transferor_dockets[0], dict):
                transferor_data = transferor_dockets[0]
                # Check both 's3_link' (lowercase) and 'S3Link' (camelcase)
                s3_link_from_transferor = transferor_data.get('s3_link', transferor_data.get('S3Link'))
                # --- Added Log Point 3 ---
                self.logger.info(
                    f"[TransferHandler/S3Link] Extracted link from transferor data: '{s3_link_from_transferor}' (type: {type(s3_link_from_transferor).__name__})")

                if s3_link_from_transferor and isinstance(s3_link_from_transferor, str):
                    # --- Added Log Point 4 ---
                    self.logger.info(
                        f"[TransferHandler/S3Link] Found valid S3 link string in transferor. Updating s3_link for {log_filename}.")
                    data['s3_link'] = s3_link_from_transferor  # Update current data
                    # Check and potentially convert the format after updating
                    link_validated = self._check_and_convert_s3_link(data)  # Check return value
                    if not link_validated:
                        self.logger.warning(
                            f"[TransferHandler/S3Link] Link from transferor '{s3_link_from_transferor}' failed validation/conversion. Link may remain invalid in data.")
                    else:
                        self.logger.info(
                            f"[TransferHandler/S3Link] Link from transferor validated/converted successfully. Final link in data: {data.get('s3_link')}")

                else:  # s3_link_from_transferor is None or not a string
                    self.logger.warning(
                        f"[TransferHandler/S3Link] No valid S3 link string found in transferor docket data for {transferor_court_id}:{transferor_docket_num}.")
            else:
                self.logger.warning(
                    f"[TransferHandler/S3Link] No transferor docket data found or format incorrect for {transferor_court_id}:{transferor_docket_num}.")
        else:  # No transferor_court_id or transferor_docket_num
            self.logger.debug(
                f"[TransferHandler/S3Link] No valid transferor info in {log_filename} to check for S3 link.")
        # else: self.logger.debug(f"No valid transferor info to check for S3 link for {log_filename}.") # Reduce verbosity

    def _check_and_convert_s3_link(self, data: Dict) -> bool:
        """Ensure S3 link uses the CDN format (https://cdn.lexgenius.ai/YYYYMMDD/dockets/...)."""
        s3_link = data.get('s3_link')
        log_filename = data.get('new_filename', data.get('docket_num', 'NA'))
        if not s3_link or not isinstance(s3_link, str): return False

        # Correct CDN format pattern
        cdn_pattern = r'https://cdn\.lexgenius\.ai/(\d{8}/dockets/.+\.pdf)'
        match = re.match(cdn_pattern, s3_link, re.IGNORECASE)  # Ignore case for .pdf
        if match:
            data['s3_link'] = s3_link.replace('\\', '/')  # Normalize path separators
            return True

        # Old S3 bucket format pattern
        s3_pattern = r'https://lexgenius\.s3\.us-west-2\.amazonaws\.com/(\d{8}/dockets/.+\.pdf)'
        s3_match = re.match(s3_pattern, s3_link, re.IGNORECASE)  # Ignore case for .pdf
        if s3_match:
            path = s3_match.group(1).replace('\\', '/')
            new_s3_link = f"https://cdn.lexgenius.ai/{path}"
            data['s3_link'] = new_s3_link
            self.logger.info(f"Converted S3 URL to CDN URL for {log_filename}: {new_s3_link}")
            return True

        # Check for HTML link - mark as invalid for PDF context
        if s3_link.lower().endswith('.html'):
            self.logger.warning(f"s3_link points to HTML, not PDF: {s3_link}. Clearing for {log_filename}.")
            data['s3_link'] = None
            return False

        self.logger.warning(f"Unknown or non-standard s3_link format for {log_filename}: {s3_link}")
        return False  # Not a recognized valid PDF link format

    def _convert_strings_to_literals(self, input_data):
        """Convert string representations to Python literals recursively (use with caution)."""
        # This is generally risky, prefer explicit parsing if possible.
        # Kept from original code, ensure it's only used where absolutely necessary.
        if isinstance(input_data, str):
            if (input_data.startswith('[') and input_data.endswith(']')) or \
                    (input_data.startswith('{') and input_data.endswith('}')):
                try:
                    return ast.literal_eval(input_data)
                except (ValueError, SyntaxError, TypeError):
                    # self.logger.debug(f"ast.literal_eval failed for string: '{input_data[:100]}...'") # Reduce verbosity
                    return input_data  # Return original string if eval fails
            else:
                return input_data
        elif isinstance(input_data, list):
            return [self._convert_strings_to_literals(item) for item in input_data]
        elif isinstance(input_data, dict):
            return {key: self._convert_strings_to_literals(value) for key, value in input_data.items()}
        else:
            return input_data
