import logging
from dataclasses import dataclass
from typing import Optional, Tuple, List


@dataclass
class LitigationRule:
    name: str
    mdl_number: str
    keywords: List[str]
    all_required: bool = False  # If True, all keywords must match


class LitigationClassifier:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        # Clear any handlers that might have been added specifically to this logger
        # and ensure it propagates to the root logger (which has RichHandler).
        self.logger.handlers = []
        self.logger.propagate = True
        # +++ End of added block +++
        self.rules = self._initialize_rules()

    @staticmethod
    def _initialize_rules() -> List[LitigationRule]:
        """Initialize all litigation classification rules."""
        return [
            LitigationRule(
                name="Insulin Pricing Litigation",
                mdl_number="3080",
                keywords=[
                    "insulin pricing",
                ]
            ),
            LitigationRule(
                name="Pfizer Oxbryta Product Liability Litigation",
                mdl_number="9010",
                keywords=["oxbryta", "sickle cell", "voxelotor"]
            ),
            LitigationRule(
                name="PowerSchool Holdings, Inc., and PowerSchool Group, LLC Customer Data Security Breach",
                mdl_number="3149",
                keywords=["powerschool"]
            ),
            LitigationRule(
                name="OpenAI, Inc., Copyright Infringement Litigation",
                mdl_number="3143",
                keywords=["openai", "training material"],
                all_required=True
            ),
            # LitigationRule(
            #     name="TikTok Minor Privacy Litigation",
            #     mdl_number="3144",
            #     keywords=["personally identifiable information", "pii", "tiktok"],
            #     all_required=True
            # ),
            LitigationRule(
                name="Asbestos Product Liability",
                mdl_number="NA",
                keywords=["mesothelioma"]
            ),
            LitigationRule(
                name="Paragard IUD Products Liability Litigation",
                mdl_number="2974",
                keywords=["paragard"],
                all_required=True
            ),
            LitigationRule(
                name="Valsartan, Losartan, and Irbesartan Products Liability Litigation",
                mdl_number="2875",
                keywords=["valsartan", "losartan", "irbesartan"],
                all_required=True
            ),
            LitigationRule(
                name="Depo-Provera (Depot Medroxyprogesterone Acetate) Products Liability Litigation",
                mdl_number="3140",
                keywords=["depo-provera", "provera"]
            ),
            LitigationRule(
                name="EXACTECH POLYETHYLENE ORTHOPEDIC PRODUCTS LIABILITY LITIGATION",
                mdl_number="3044",
                keywords=["exactech", "replacement", ("knee", "hip", "ankle", "opetrak")],
                all_required=True
            ),
            LitigationRule(
                name="Bair Hugger Forced air Warming Device Products Liability Litigation",
                mdl_number="2666",
                keywords=["bair hugger", "forced air warming"]
            ),
            LitigationRule(
                name="Snowflake Inc. Data Security Breach Litigation",
                mdl_number="3126",
                keywords=["snowflake"]
            ),
            LitigationRule(
                name="Zimmer M/L Taper Hip Prothesis or M/L Taper Hip Prothesis with Kinectiv Technology and Versys Femoral Head Products Liability Litigation",
                mdl_number="2859",
                keywords=["zimmer hip"]
            ),
            LitigationRule(
                name="Covidien Hernia Mesh Products Liability Litigation (No. II)",
                mdl_number="3029",
                keywords=["covidien", "hernia", " mesh"],
                all_required=True
            ),
            LitigationRule(
                name="Allergan USA & Lifecell Corporation Strattice Hernia Mesh Device",
                mdl_number="17705",
                keywords=["17705"]
            ),
            LitigationRule(
                name="Preterm Infant Nutrition Products Liability Litigation",
                mdl_number="3026",
                keywords=["necrotizing enterocolitis"]
            ),
            LitigationRule(
                name="Suboxone (Buprenorphine/Naloxone) Film Products Liability Litigation",
                mdl_number="3092",
                keywords=["suboxone"]
            ),
            LitigationRule(
                name="Uber Technologies, Passenger Sexual Assault Litigation ",
                mdl_number="3084",
                keywords=["uber", "passenger sexual assault"]
            ),
            LitigationRule(
                name="Aqueous Film-Forming Foams Products Liability Litigation",
                mdl_number="2873",
                keywords=[
                    ["firefighter", "pfas"],
                    ["aqueous", "film-forming"],
                    ["afff"],
                    ["pfas", "2873"]
                ],
                all_required=True
            ),
            LitigationRule(
                name="Church of Jesus Christ of Latter-Day Saints Sex Abuse Litigation",
                mdl_number="3150",
                keywords=["Latter-Day Saints"]
            ),
            LitigationRule(
                name="Paraquat Products Liability Litigation",
                mdl_number="3004",
                keywords=["syngenta", "paraquat"],
                all_required=True
            ),
            LitigationRule(
                name="Johnson & Johnson Talcum Powder Products Marketing, Sales Practices and Products Liability Litigation",
                mdl_number="2738",
                keywords=[
                    ["johnson & johnson"],  # Required keyword
                    ["baby powder", "talcum powder"]  # Either "baby powder" OR "talcum powder"
                ],
                all_required=True  # Set this to ensure all keyword conditions are met
            ),
            LitigationRule(
                name="Hair Relaxer Marketing, Sales Practices, And Products Liability Litigation",
                mdl_number="3060",
                keywords=[
                    "hair relaxer marketing",
                    "mdl 3060",
                    "hair relaxer"
                ]
            ),
            LitigationRule(
                name="Glucagon-like Peptide-1 Receptor Agonists (GLP-1 RAS) Products Liability Litigation",
                mdl_number="3094",
                keywords=[
                    ["novo nordisk", "semaglutide"],
                    ["wegovy"],
                    ["ozempic"],
                    ["eli lilly", "trulicity"]
                ]
            ),
            LitigationRule(
                name="Video Game Addiction Products Liability Litigation",
                mdl_number="3109",
                keywords=["video game addiction", "robolox", "fortnite", "minecraft", "gaming"]
            ),
            LitigationRule(
                name="Baby Food Products Liability Litigation",
                mdl_number="3101",
                keywords=[
                    "beech-nut",
                    "beech nut",
                    "gerber products"
                ]
            ),
            LitigationRule(
                name="Cook Medical, Inc., IVC Filters Marketing, Sales Practices and Products Liability Litigation",
                mdl_number="2570",
                keywords=[
                    "ivc filter",
                    "vena cava"
                ]
            ),
            LitigationRule(
                name="Gardisil Products Liability Litigation",
                mdl_number="3036",
                keywords=["gardisil"]
            ),
            LitigationRule(
                name="Camp Lejeune Water Litigation",
                mdl_number="25",
                keywords=[
                    "camp lejeune",
                    "camp lejeune justice act of 2022"
                ]
            ),
            LitigationRule(
                name="Roundup Products Liability Litigation",
                mdl_number="2741",
                keywords=[
                    "roundup",
                    "glyphosate"
                ]
            ),
            LitigationRule(
                name="Tepezza Marketing, Sales Practices, and Products Liability Litigation",
                mdl_number="3079",
                keywords=["tepezza"]
            ),
            LitigationRule(
                name="Future Motion, Inc. Products Liability Litigation",
                mdl_number="3087",
                keywords=["future motion", "https://onewheel.com/"],
                all_required=True
            ),
            LitigationRule(
                name="Zantac (Ranitidine) Products Liability Litigation",
                mdl_number="2924",
                keywords=["zantac"]
            ),
            LitigationRule(
                name="ANGIODYNAMICS INC. AND NAVILYST MEDICAL INC. PORT CATHETER PRODUCTS LIABILITY LITIGATION",
                mdl_number="3125",
                keywords=["angiodynamics"]
            ),
            LitigationRule(
                name="THE COOPER COMPANIES INC. IN VITRO FERTILIZATION GLOBAL CULTURE MEDIA PRODUCTS LIABILITY LITIGATION",
                mdl_number="3122",
                keywords=[
                    # "coopersurgical",
                    "culture media"
                ]
            ),
            LitigationRule(
                name="Pressure Cooker / Instant Pot Products Liability",
                mdl_number="9000",
                keywords=[
                    "pressure cooker",
                    "instant pot"
                ]
            ),
            LitigationRule(
                name="Social Media Adolescent Addiction/Personal Injury Products Liability Litigation",
                mdl_number="3047",
                keywords=[
                    "addiction",  # Required keyword
                    ["meta", "facebook", "instagram", "tiktok", "youtube", "snapchat", "social media"]  # At least one of these
                ],
                all_required=True
            ),
            LitigationRule(
                name="DePuy Orthopaedics, Inc., ASR Hip Implant Products Liability Litigation",
                mdl_number="2197",
                keywords=["depuy metal-on-metal hip replacement"]
            )
        ]

    @staticmethod
    def _check_keywords(text: str, keywords: List[str], all_required: bool = False) -> bool:
        """
        Check if keywords are present in the text.

        Args:
            text: The text to search in
            keywords: List of keywords or tuples of alternative keywords
            all_required: If True, all keywords must be present
        """
        text = text.lower()
        import re
        import string

        def normalize_text_for_matching(text_str):
            """Remove punctuation for better matching"""
            # Remove common punctuation that might interfere with matching
            translator = str.maketrans('', '', string.punctuation.replace('-', '').replace('_', ''))
            return text_str.translate(translator)

        def check_keyword_item(keyword_item):
            if isinstance(keyword_item, (list, tuple)):
                # For alternative keywords, any one should match
                return any(check_single_keyword(kw) for kw in keyword_item)
            return check_single_keyword(keyword_item)
            
        def check_single_keyword(keyword):
            keyword = keyword.lower()
            # First try exact word boundary match
            if re.search(r'\b' + re.escape(keyword) + r'\b', text):
                return True
            # Then try normalized text matching (ignoring punctuation)
            normalized_text = normalize_text_for_matching(text)
            normalized_keyword = normalize_text_for_matching(keyword)
            return re.search(r'\b' + re.escape(normalized_keyword) + r'\b', normalized_text) is not None

        if all_required:
            return all(check_keyword_item(k) for k in keywords)
        return any(check_keyword_item(k) for k in keywords)

    def identify_litigation_by_text(self, first_page_text: str) -> Tuple[Optional[str], Optional[str]]:
        """
        Identify litigation type and MDL number from text.

        Args:
            first_page_text: The text to analyze

        Returns:
            Tuple of (litigation name, MDL number) or (None, None) if no match
        """
        text = first_page_text.lower()

        for rule in self.rules:
            match_result = self._check_keywords(text, rule.keywords, rule.all_required)
            self.logger.debug(f"Rule '{rule.name}' match result: {match_result}")

            if match_result:
                # Add detailed logging for TikTok rule to debug the issue
                if rule.name == "TikTok Minor Privacy Litigation":
                    self.logger.info(f"TikTok rule matched with all_required={rule.all_required}")
                    for kw in rule.keywords:
                        if isinstance(kw, (list, tuple)):
                            for alt_kw in kw:
                                import re
                                match = re.search(r'\b' + re.escape(alt_kw.lower()) + r'\b', text)
                                self.logger.info(f"Keyword '{alt_kw}' match: {match is not None}")
                        else:
                            import re
                            match = re.search(r'\b' + re.escape(kw.lower()) + r'\b', text)
                            self.logger.info(f"Keyword '{kw}' match: {match is not None}")

                return rule.name, rule.mdl_number

        return None, None
