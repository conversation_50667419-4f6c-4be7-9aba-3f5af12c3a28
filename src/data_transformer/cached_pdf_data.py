import logging
from typing import Optional, List, Tuple
import pandas as pd
import re
from .litigation_classifier import LitigationClassifier


class CachedPdfData:
    """Cache for PDF data to avoid repeated processing (Simple version)."""

    # This implementation is basic. Consider LRU cache or more robust caching if needed.
    def __init__(self):
        self.full_text: Optional[str] = None
        self.pdf_path: Optional[str] = None
        self.s3_link: Optional[str] = None
        self.md_path: Optional[str] = None
        self._logger = logging.getLogger(__name__)  # Add logger

    def is_valid(self) -> bool:
        return bool(self.full_text)

    def matches_source(self, pdf_path: str = None, s3_link: str = None) -> bool:
        # Check if cache holds data matching either the local path or S3 link
        return (pdf_path and pdf_path == self.pdf_path) or \
            (s3_link and s3_link == self.s3_link)

    def update(self, full_text: str, pdf_path: str = None, s3_link: str = None, md_path: str = None) -> None:
        self.full_text = full_text
        self.pdf_path = pdf_path
        self.s3_link = s3_link
        self.md_path = md_path
        self._logger.debug(f"PDF Cache updated. Source: {pdf_path or s3_link or md_path}")

    def get_signature_pages(self) -> List[str]:
        """Placeholder for extracting signature pages."""
        # Requires actual implementation based on document structure analysis
        self._logger.warning("get_signature_pages is a placeholder.")
        return [self.full_text] if self.full_text else []

    def identify_litigation(self, mdl_litigations: pd.DataFrame) -> Tuple[Optional[str], Optional[str]]:
        """Identify litigation based on cached PDF text (requires LitigationClassifier)."""
        if not self.is_valid() or self.full_text is None: return None, None
        if mdl_litigations is None or mdl_litigations.empty:
            self._logger.warning("MDL litigation data not provided for identification.")
            return None, None  # Cannot identify by MDL number

        # 1. Search for MDL Number
        mdl_pattern = r'MDL\s*No\.?\s*(\d+)'
        try:
            for mdl_match in re.finditer(mdl_pattern, self.full_text):
                mdl_number_raw = mdl_match.group(1)
                # Clean MDL number robustly
                mdl_number = mdl_number_raw.lstrip('0') if len(
                    mdl_number_raw) > 1 else mdl_number_raw  # Avoid stripping '0'
                if mdl_number:
                    match = mdl_litigations[mdl_litigations['mdl_num'] == str(mdl_number)]
                    if not match.empty:
                        litigation_name = match['litigation'].iloc[0]  # Use iloc[0] for first match
                        self._logger.debug(f"Identified MDL {mdl_number} ({litigation_name}) from text.")
                        return litigation_name, str(mdl_number)
        except Exception as e:
            self._logger.error(f"Error searching for MDL number in text: {e}")

        # 2. Fallback: Use LitigationClassifier (if available)
        try:
            # Should be injected or available globally/via instance
            litigation_classifier = LitigationClassifier()
            result = litigation_classifier.identify_litigation_by_text(self.full_text)
            if result[0] or result[1]:  # If classifier found something
                self._logger.debug(f"Identified litigation via text classifier: {result}")
                return result
        except NameError:
            self._logger.warning("LitigationClassifier not available for text-based identification.")
        except Exception as e:
            self._logger.error(f"Error during text-based litigation identification: {e}")

        return None, None  # No identification found

