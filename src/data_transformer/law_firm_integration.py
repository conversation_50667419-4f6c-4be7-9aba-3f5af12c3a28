"""
Law firm integration and processing for docket data.

This module handles law firm processing integration extracted from docket_processor.py
as part of Phase 3.2 refactoring.
"""
import asyncio
import logging
from typing import Dict, List, Optional, Any


class LawFirmIntegration:
    """Handles law firm processing and integration for docket data."""
    
    NULL_CONDITIONS = ['', 'NA', None, 'Pro Se', 'PRO SE', "None", []]
    
    def __init__(self, law_firm_processor: Optional[Any] = None, 
                 config: Optional[Dict] = None, logger: Optional[logging.Logger] = None):
        self.law_firm_processor = law_firm_processor
        self.config = config or {}
        self.logger = logger or logging.getLogger(__name__)
        
        # Configure processing parameters
        self.max_concurrent_requests = self.config.get('max_concurrent_law_firm_requests', 5)
        self.timeout_seconds = self.config.get('law_firm_timeout_seconds', 30)
    
    async def process_law_firms(self, data: Dict) -> bool:
        """
        Process law firm information for the docket data.
        
        This is the main async entry point for law firm processing.
        
        Args:
            data: Docket data dictionary (modified in place)
            
        Returns:
            True if law firm processing was attempted, False otherwise
        """
        if not isinstance(data, dict):
            self.logger.error("Invalid data dictionary provided to process_law_firms")
            return False
        
        filename = data.get('new_filename', data.get('docket_num', 'unknown'))
        
        if not self.law_firm_processor:
            self.logger.warning(f"Law firm processor not available for {filename}")
            return False
        
        try:
            # Prepare data for law firm processing
            self._prepare_law_firm_data(data)
            
            # Extract attorney information for processing
            attorneys = self._extract_attorney_information(data)
            
            if not attorneys:
                self.logger.debug(f"No attorney information found for law firm processing: {filename}")
                return False
            
            # Process law firms asynchronously
            processed_firms = await self._process_law_firms_async(attorneys, filename)
            
            if processed_firms:
                # Update data with processed law firm information
                self._update_data_with_law_firms(data, processed_firms)
                
                # Validate and clean results
                self._validate_law_firm_results(data)
                
                self.logger.info(f"Law firm processing completed for {filename}. "
                               f"Processed {len(processed_firms)} firms.")
                return True
            else:
                self.logger.warning(f"Law firm processing returned no results for {filename}")
                return False
                
        except Exception as e:
            self.logger.error(f"Error during law firm processing for {filename}: {e}", exc_info=True)
            self._handle_law_firm_error(data, str(e))
            return False
    
    def _prepare_law_firm_data(self, data: Dict):
        """
        Prepare data dictionary for law firm processing.
        
        Args:
            data: Data dictionary to prepare (modified in place)
        """
        # Ensure law firm related fields exist
        data.setdefault('law_firm', [])
        data.setdefault('attorneys', [])
        data.setdefault('law_firm_processed', False)
        
        # Clean existing law firm data
        if not isinstance(data.get('law_firm'), list):
            if isinstance(data.get('law_firm'), str) and data['law_firm'].strip():
                data['law_firm'] = [data['law_firm'].strip()]
            else:
                data['law_firm'] = []
        
        if not isinstance(data.get('attorneys'), list):
            if isinstance(data.get('attorneys'), str) and data['attorneys'].strip():
                data['attorneys'] = [data['attorneys'].strip()]
            else:
                data['attorneys'] = []
    
    def _extract_attorney_information(self, data: Dict) -> List[Dict[str, Any]]:
        """
        Extract attorney information from data for law firm processing.
        
        Args:
            data: Data dictionary containing attorney information
            
        Returns:
            List of attorney information dictionaries
        """
        attorneys = []
        
        # Extract from various possible fields
        attorney_sources = [
            ('attorneys', 'attorney_name'),
            ('plaintiff_attorneys', 'attorney_name'),
            ('defendant_attorneys', 'attorney_name'),
            ('attorneys_gpt', 'attorney_name')
        ]
        
        for field_name, name_key in attorney_sources:
            field_data = data.get(field_name, [])
            
            if isinstance(field_data, list):
                for attorney in field_data:
                    if isinstance(attorney, dict):
                        # Attorney is already a dictionary
                        if attorney.get(name_key) and attorney[name_key] not in self.NULL_CONDITIONS:
                            attorneys.append(attorney)
                    elif isinstance(attorney, str) and attorney.strip():
                        # Attorney is a string, convert to dict
                        attorneys.append({name_key: attorney.strip()})
            elif isinstance(field_data, str) and field_data.strip():
                # Single attorney as string
                attorneys.append({name_key: field_data.strip()})
        
        # Deduplicate attorneys by name
        seen_names = set()
        unique_attorneys = []
        
        for attorney in attorneys:
            name = attorney.get('attorney_name', '').strip()
            if name and name not in seen_names and name not in self.NULL_CONDITIONS:
                unique_attorneys.append(attorney)
                seen_names.add(name)
        
        return unique_attorneys
    
    async def _process_law_firms_async(self, attorneys: List[Dict], filename: str) -> List[Dict[str, Any]]:
        """
        Process law firms asynchronously using the law firm processor.
        
        Args:
            attorneys: List of attorney information dictionaries
            filename: Filename for logging context
            
        Returns:
            List of processed law firm information
        """
        if not attorneys:
            return []
        
        try:
            # Create semaphore to limit concurrent requests
            semaphore = asyncio.Semaphore(self.max_concurrent_requests)
            
            async def process_single_attorney(attorney: Dict) -> Optional[Dict]:
                async with semaphore:
                    try:
                        # Use timeout for individual requests
                        return await asyncio.wait_for(
                            self._call_law_firm_processor(attorney),
                            timeout=self.timeout_seconds
                        )
                    except asyncio.TimeoutError:
                        self.logger.warning(f"Law firm processing timeout for attorney: {attorney.get('attorney_name')}")
                        return None
                    except Exception as e:
                        self.logger.error(f"Error processing attorney {attorney.get('attorney_name')}: {e}")
                        return None
            
            # Process all attorneys concurrently
            tasks = [process_single_attorney(attorney) for attorney in attorneys]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Filter successful results
            processed_firms = []
            for result in results:
                if isinstance(result, dict) and result:
                    processed_firms.append(result)
                elif isinstance(result, Exception):
                    self.logger.error(f"Law firm processing exception: {result}")
            
            self.logger.debug(f"Processed {len(processed_firms)} law firms from {len(attorneys)} attorneys for {filename}")
            return processed_firms
            
        except Exception as e:
            self.logger.error(f"Error in async law firm processing for {filename}: {e}")
            return []
    
    async def _call_law_firm_processor(self, attorney: Dict) -> Optional[Dict]:
        """
        Call the law firm processor for a single attorney.
        
        Args:
            attorney: Attorney information dictionary
            
        Returns:
            Processed law firm information or None
        """
        if not self.law_firm_processor:
            return None
        
        # Check for async method first
        if hasattr(self.law_firm_processor, 'process_attorney_async'):
            return await self.law_firm_processor.process_attorney_async(attorney)
        elif hasattr(self.law_firm_processor, 'process_attorney'):
            # Use sync method in thread
            return await asyncio.to_thread(self.law_firm_processor.process_attorney, attorney)
        else:
            self.logger.warning("Law firm processor has no compatible methods")
            return None
    
    def _update_data_with_law_firms(self, data: Dict, processed_firms: List[Dict]):
        """
        Update data dictionary with processed law firm information.
        
        Args:
            data: Data dictionary to update (modified in place)
            processed_firms: List of processed law firm information
        """
        if not processed_firms:
            return
        
        # Extract law firm names
        law_firm_names = []
        law_firm_details = []
        
        for firm in processed_firms:
            if isinstance(firm, dict):
                # Extract firm name
                firm_name = (firm.get('law_firm_name') or 
                           firm.get('firm_name') or 
                           firm.get('name'))
                
                if firm_name and firm_name not in self.NULL_CONDITIONS:
                    law_firm_names.append(firm_name)
                    law_firm_details.append(firm)
        
        # Update main law_firm field
        if law_firm_names:
            # Remove duplicates while preserving order
            unique_firms = []
            seen = set()
            for firm in law_firm_names:
                if firm not in seen:
                    unique_firms.append(firm)
                    seen.add(firm)
            
            data['law_firm'] = unique_firms
        
        # Store detailed law firm information
        if law_firm_details:
            data['law_firm_details'] = law_firm_details
        
        # Set processing metadata
        data['law_firm_processed'] = True
        data['law_firm_processing_timestamp'] = self._get_current_timestamp()
        data['law_firm_count'] = len(law_firm_names)
    
    def _validate_law_firm_results(self, data: Dict):
        """
        Validate and clean law firm processing results.
        
        Args:
            data: Data dictionary to validate (modified in place)
        """
        # Validate law_firm list
        law_firms = data.get('law_firm', [])
        if isinstance(law_firms, list):
            cleaned_firms = []
            for firm in law_firms:
                if isinstance(firm, str):
                    cleaned = firm.strip()
                    if cleaned and cleaned not in self.NULL_CONDITIONS:
                        cleaned_firms.append(cleaned)
            data['law_firm'] = cleaned_firms
        
        # Validate law_firm_details
        law_firm_details = data.get('law_firm_details', [])
        if isinstance(law_firm_details, list):
            validated_details = []
            for detail in law_firm_details:
                if isinstance(detail, dict) and detail:
                    validated_details.append(detail)
            data['law_firm_details'] = validated_details
    
    def _handle_law_firm_error(self, data: Dict, error_message: str):
        """
        Handle law firm processing errors.
        
        Args:
            data: Data dictionary to update with error info (modified in place)
            error_message: Error message to record
        """
        data['law_firm_processing_error'] = error_message
        data['law_firm_processed'] = False
        data['law_firm_processing_timestamp'] = self._get_current_timestamp()
        
        filename = data.get('new_filename', data.get('docket_num', 'unknown'))
        self.logger.error(f"Law firm processing failed for {filename}: {error_message}")
    
    def _get_current_timestamp(self) -> str:
        """Get current timestamp for metadata."""
        from datetime import datetime
        return datetime.now().isoformat()
    
    def validate_law_firm_integration(self, data: Dict) -> Dict[str, Any]:
        """
        Validate law firm integration results and return validation report.
        
        Args:
            data: Data dictionary to validate
            
        Returns:
            Validation report with status and details
        """
        report = {
            'status': 'success',
            'warnings': [],
            'errors': [],
            'processed_firms': 0,
            'processed_attorneys': 0
        }
        
        # Check if processing was completed
        if not data.get('law_firm_processed'):
            report['status'] = 'not_processed'
            return report
        
        # Check for processing errors
        if 'law_firm_processing_error' in data:
            report['status'] = 'error'
            report['errors'].append(f"Processing error: {data['law_firm_processing_error']}")
        
        # Count processed firms and attorneys
        law_firms = data.get('law_firm', [])
        if isinstance(law_firms, list):
            report['processed_firms'] = len(law_firms)
        
        attorneys = data.get('attorneys', [])
        if isinstance(attorneys, list):
            report['processed_attorneys'] = len(attorneys)
        
        # Validate data quality
        if report['processed_firms'] == 0 and report['processed_attorneys'] > 0:
            report['warnings'].append("Attorneys found but no law firms processed")
        
        law_firm_details = data.get('law_firm_details', [])
        if isinstance(law_firm_details, list):
            if len(law_firm_details) != report['processed_firms']:
                report['warnings'].append("Mismatch between law firm count and detailed information")
        
        # Set overall status
        if report['errors']:
            report['status'] = 'error'
        elif report['warnings']:
            report['status'] = 'warning'
        elif report['processed_firms'] == 0:
            report['status'] = 'no_firms'
        
        return report
    
    def get_law_firm_summary(self, data: Dict) -> str:
        """
        Get summary of law firm processing results.
        
        Args:
            data: Data dictionary to summarize
            
        Returns:
            Human-readable summary string
        """
        if not data.get('law_firm_processed'):
            return "Law firm processing not completed"
        
        if 'law_firm_processing_error' in data:
            return f"Law firm processing failed: {data['law_firm_processing_error']}"
        
        law_firms = data.get('law_firm', [])
        attorneys = data.get('attorneys', [])
        
        firm_count = len(law_firms) if isinstance(law_firms, list) else 0
        attorney_count = len(attorneys) if isinstance(attorneys, list) else 0
        
        summary_parts = []
        
        if firm_count > 0:
            summary_parts.append(f"{firm_count} law firm(s)")
        if attorney_count > 0:
            summary_parts.append(f"{attorney_count} attorney(s)")
        
        if summary_parts:
            result = f"Processed: {', '.join(summary_parts)}"
            
            # Add sample firm names if available
            if firm_count > 0 and isinstance(law_firms, list):
                sample_firms = law_firms[:2]  # Show first 2 firms
                firm_sample = ", ".join(sample_firms)
                if firm_count > 2:
                    firm_sample += f" (+{firm_count - 2} more)"
                result += f" [{firm_sample}]"
            
            return result
        else:
            return "Law firm processing completed but no firms identified"
    
    def get_processing_stats(self, data: Dict) -> Dict[str, Any]:
        """
        Get detailed processing statistics.
        
        Args:
            data: Data dictionary to analyze
            
        Returns:
            Dictionary with processing statistics
        """
        stats = {
            'processed': data.get('law_firm_processed', False),
            'timestamp': data.get('law_firm_processing_timestamp'),
            'firm_count': 0,
            'attorney_count': 0,
            'has_details': False,
            'has_error': 'law_firm_processing_error' in data
        }
        
        law_firms = data.get('law_firm', [])
        if isinstance(law_firms, list):
            stats['firm_count'] = len(law_firms)
        
        attorneys = data.get('attorneys', [])
        if isinstance(attorneys, list):
            stats['attorney_count'] = len(attorneys)
        
        law_firm_details = data.get('law_firm_details', [])
        if isinstance(law_firm_details, list) and law_firm_details:
            stats['has_details'] = True
        
        return stats