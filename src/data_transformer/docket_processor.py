"""
Refactored docket processor using modular components.

This class has been refactored in Phase 3.2 to use modular components for better
organization and maintainability.
"""
import asyncio
import copy
import logging
import os
import re
from datetime import datetime
from typing import Dict, Any, Optional

from src.data_transformer.court_data_processor import CourtDataProcessor
from src.data_transformer.docket_data_cleaner import DocketDataCleaner
from src.data_transformer.docket_html_processor import DocketHTMLProcessor
from src.data_transformer.docket_llm_engine import DocketLLMEngine
from src.data_transformer.docket_text_handler import DocketTextHandler
from src.data_transformer.law_firm_integration import LawFirmIntegration


class DocketProcessor:
    """
    Encapsulates the logic for processing the data of a single docket file.
    Handles data cleaning, LLM interactions, and updates to the docket dictionary.
    
    This class has been refactored in Phase 3.2 to use modular components for:
    - Data cleaning and formatting (DocketDataCleaner)
    - LLM extraction (DocketLLMEngine)
    - PDF text handling (DocketTextHandler)
    - HTML processing (DocketHTMLProcessor)
    - Court data processing (CourtDataProcessor)
    - Law firm integration (LawFirmIntegration)
    """
    NULL_CONDITIONS = ['', 'NA', None, 'Pro Se', 'PRO SE', "None", []]

    def __init__(self,
                 config: Dict,
                 llm_client: Any = None,
                 law_firm_processor: Any = None,
                 transfer_handler: Any = None,
                 html_data_updater: Any = None,
                 validator: Any = None,
                 file_handler: Any = None,
                 pdf_processor: Optional[Any] = None,
                 gpt_client: Optional[Any] = None,  # Alias for llm_client
                 **kwargs):
        self.config = config
        self.logger = logging.getLogger(__name__)
        # Ensure proper logging configuration for data_transformer modules
        self.logger.handlers = []
        self.logger.propagate = True
        
        # Accept both llm_client and gpt_client for backward compatibility
        self.llm_client = llm_client or gpt_client
        self.law_firm_processor = law_firm_processor
        self.transfer_handler = transfer_handler
        self.html_data_updater = html_data_updater
        self.validator = validator
        self.file_handler = file_handler
        self.pdf_processor = pdf_processor
        
        # Initialize modular components
        self.data_cleaner = DocketDataCleaner(logger=self.logger)
        self.llm_engine = DocketLLMEngine(llm_client=self.llm_client, logger=self.logger)
        self.text_handler = DocketTextHandler(
            file_handler=self.file_handler,
            pdf_processor=self.pdf_processor,
            logger=self.logger
        )
        self.html_processor = DocketHTMLProcessor(
            html_data_updater=self.html_data_updater,
            logger=self.logger
        )
        self.court_processor = CourtDataProcessor(
            config=self.config,
            logger=self.logger
        )
        self.law_firm_integration = LawFirmIntegration(
            law_firm_processor=self.law_firm_processor,
            config=self.config,
            logger=self.logger
        )

    def process_filing_date(self, data: Dict):
        """
        Process, format, and validate the filing date within the data dictionary.
        Delegates to DocketDataCleaner component.
        """
        self.data_cleaner.process_filing_date(data)

    def clean_and_flatten(self, data: Dict):
        """
        Removes duplicates from plaintiff/defendant lists and flattens 'case_info'.
        Delegates to DocketDataCleaner component.
        """
        self.data_cleaner.clean_and_flatten(data)

    async def run_llm_extraction(self, data: Dict, pdf_text: Optional[str]) -> bool:
        """
        Runs the configured LLM client's extraction process on the provided text.
        Delegates to DocketLLMEngine component.
        
        Args:
            data: The docket data dictionary (will be updated in-place).
            pdf_text: The text content extracted from the document.

        Returns:
            bool: True if extraction was attempted, False otherwise.
        """
        return await self.llm_engine.run_llm_extraction(data, pdf_text)

    def _clear_processing_errors(self, data: Dict):
        """
        Removes error-related keys from the data dictionary.
        Delegates to DocketDataCleaner component.
        """
        self.data_cleaner.clear_processing_errors(data)

    def _update_error_status(self, data: Optional[Dict], json_path: str, error_message: str):
        """
        Updates the data dictionary with error information and logs the error.
        Sets 'processing_error' and 'last_error_date'.
        """
        filename = os.path.basename(json_path) if json_path else "Unknown file"
        log_filename = data.get('new_filename',
                                filename) if data else filename  # Prioritize new_filename if available

        # Log the error first
        self.logger.error(f"Error processing {log_filename}: {error_message}")

        # Update the data dictionary if it's valid
        if isinstance(data, dict):
            data['processing_error'] = str(error_message)
            data['last_error_date'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            self.logger.debug(f"Updated error status in data dictionary for {log_filename}.")
        else:
            self.logger.warning(
                f"Could not update error status for {log_filename}: data is not a dictionary or is None.")

    async def _get_pdf_text(self, json_path: str, data: Dict) -> Optional[str]:
        """
        Extract text content from PDF associated with the docket.
        Delegates to DocketTextHandler component.
        
        Args:
            json_path: Path to the JSON file
            data: Docket data dictionary
            
        Returns:
            Extracted text content or None if unavailable
        """
        return await self.text_handler.get_pdf_text(json_path, data)

    async def process_s3_html(self, data: Dict, json_path: str) -> bool:
        """
        Process S3 HTML content and extract case information.
        Delegates to DocketHTMLProcessor component.
        
        Args:
            data: The docket data dictionary (will be updated in-place).
            json_path: The full path to the current JSON file being processed.

        Returns:
            bool: True if HTML was found and processed successfully, False otherwise.
        """
        return await self.html_processor.process_s3_html(data, json_path)

    async def process_court_info(self, data: Dict) -> bool:
        """
        Process court information including nature of suit, case flags, etc.
        Delegates to CourtDataProcessor component.
        
        Args:
            data: Docket data dictionary (modified in place)
            
        Returns:
            True if court info was processed successfully
        """
        nature_processed = await self.court_processor.process_nature_of_suit(data)
        flags_processed = await self.court_processor.process_case_flags(data)
        court_info_processed = await self.court_processor.process_court_info(data)
        
        return any([nature_processed, flags_processed, court_info_processed])

    async def process_law_firms_async(self, data: Dict) -> bool:
        """
        Process law firm information asynchronously.
        Delegates to LawFirmIntegration component.
        
        Args:
            data: Docket data dictionary (modified in place)
            
        Returns:
            True if law firm processing was completed successfully
        """
        return await self.law_firm_integration.process_law_firms(data)

    def process_sync(self, json_path: str, force_reprocess: bool = False) -> Optional[Dict]:
        """
        Synchronously processes a single docket file. Loads data, applies processing steps
        (HTML update, transfers, dates, cleaning), and saves the result.
        NOTE: This method is less efficient for batch processing. Prefer async methods.

        Args:
            json_path: Path to the JSON file.
            force_reprocess: If True, process even if validation suggests it's complete.

        Returns:
            A dictionary with processing status, or None if skipped or failed.
        """
        self.logger.warning(
            "Synchronous processing (process_sync) may not fully align with new async filename logic. Use with caution.")
        self.logger.info(f"Starting synchronous processing for {os.path.basename(json_path)}")

        # 1. Load Data
        original_data = self.file_handler.load_json(json_path)
        if original_data is None:
            self.logger.error(f"Failed to load JSON data from {json_path}. Skipping.")
            return None

        # Create a working copy
        data = copy.deepcopy(original_data)  # Use deepcopy for safety
        if '_source_json_path' not in data:  # Ensure source path is stored if missing
            data['_source_json_path'] = json_path

        # 2. Check if processing is needed
        is_complete, _ = self.validator.is_data_complete(data)
        has_error = 'processing_error' in data

        should_reprocess = force_reprocess or not is_complete or has_error
        if not should_reprocess:
            self.logger.info(
                f"Skipping already complete file {os.path.basename(json_path)} (no error, force_reprocess={force_reprocess}).")
            return None  # Indicate skipped

        # If reprocessing or incomplete or had error, clear previous errors
        self._clear_processing_errors(data)

        # 3. Apply Processing Steps (Synchronously)
        try:
            # Retrieve text (Synchronously - needs wrapper for async _get_pdf_text)
            pdf_text = None
            try:
                pdf_text = asyncio.run(self._get_pdf_text(json_path, data))  # Wrap the async call
            except Exception as get_text_err:
                self.logger.error(f"Error getting text sync: {get_text_err}")

            if pdf_text is None:
                self.logger.warning(
                    f"Could not retrieve text for {os.path.basename(json_path)} (sync). LLM may be skipped.")

            # Run LLM Extraction (Need sync version or wrapper)
            if pdf_text:
                try:
                    asyncio.run(self.run_llm_extraction(data, pdf_text))  # Wrap the async call
                except Exception as llm_err_sync:
                    self.logger.error(
                        f"Error running LLM (sync attempt) for {os.path.basename(json_path)}: {llm_err_sync}",
                        exc_info=True)
                    self._update_error_status(data, json_path, f"LLM sync error: {llm_err_sync}")
                    # Continue processing other steps

            # Process other steps (assuming these are already synchronous)
            # Pass json_path to process_s3_html (async call)
            try:
                asyncio.run(self.process_s3_html(data, json_path))
            except Exception as html_err_sync:
                self.logger.error(
                    f"Error processing S3 HTML (sync attempt) for {os.path.basename(json_path)}: {html_err_sync}",
                    exc_info=True)
                self._update_error_status(data, json_path, f"HTML sync error: {html_err_sync}")
            
            # Law firm processing (use sync wrapper for old law_firm_processor interface)
            try:
                self.law_firm_processor.process_law_firms(data)
            except Exception as law_firm_err:
                self.logger.error(f"Error in law firm processing: {law_firm_err}")
            
            asyncio.run(self.transfer_handler.process_transfers(data))
            self.process_filing_date(data)
            
            # Add MDL info (assuming mdl_processor.add_mdl_info is sync)
            if hasattr(self, 'mdl_processor') and self.mdl_processor:
                self.mdl_processor.add_mdl_info(data)  # This will populate title from mdl_num
                self.mdl_processor.calculate_afff_num_plaintiffs(data)
            
            self.clean_and_flatten(data)

            # Final cleaning
            if 'law_firm' in data and isinstance(data.get('law_firm'), str):
                data['law_firm'] = data['law_firm'].strip(' ;')

        except Exception as process_err:
            self.logger.error(
                f"Error during synchronous processing steps for {os.path.basename(json_path)}: {process_err}",
                exc_info=True)
            self._update_error_status(data, json_path, f"Sync processing step error: {process_err}")
            self.file_handler.save_json(json_path, data)  # Save with error
            return None  # Indicate failure

        # 4. Final Validation (Optional)
        lost_keys = [k for k in ['court_id', 'docket_num'] if k in original_data and k not in data]
        if lost_keys:
            self.logger.error(
                f"CRITICAL SAVE ERROR (sync): Processed data lost critical keys: {lost_keys} for {os.path.basename(json_path)}. NOT SAVING.")
            self._update_error_status(original_data, json_path, f"Save aborted (sync): Lost critical keys {lost_keys}")
            self.file_handler.save_json(json_path, original_data)  # Save original with error
            return None

        # 5. Save Updated Data (Filename handling is missing here compared to async)
        save_successful = self.file_handler.save_json(json_path, data)

        if save_successful:
            self.logger.info(f"Successfully processed and saved (sync) {os.path.basename(json_path)}")
            # Return the filename used for saving, which is the input path here
            return {'filename': os.path.basename(json_path), 'status': 'Processed'}
        else:
            self.logger.error(f"Failed to save updated JSON (sync) for {os.path.basename(json_path)}.")
            self._update_error_status(data, json_path, "Failed to save updated JSON (sync)")
            self.file_handler.save_json(json_path, data)  # Attempt save with error status
            return None  # Indicate failure

    # Component validation and summary methods
    def validate_processing_results(self, data: Dict) -> Dict[str, Any]:
        """
        Validate processing results from all components.
        
        Args:
            data: Processed data dictionary
            
        Returns:
            Comprehensive validation report
        """
        validation_report = {
            'overall_status': 'success',
            'component_results': {},
            'warnings': [],
            'errors': []
        }
        
        # Validate data cleaning results
        try:
            data_validation = self.data_cleaner.validate_required_fields(data)
            validation_report['component_results']['data_cleaner'] = {
                'status': 'success' if not data_validation else 'warning',
                'errors': data_validation
            }
            if data_validation:
                validation_report['warnings'].extend(data_validation)
        except Exception as e:
            validation_report['component_results']['data_cleaner'] = {
                'status': 'error',
                'error': str(e)
            }
            validation_report['errors'].append(f"Data cleaner validation failed: {e}")
        
        # Validate LLM results
        try:
            llm_validation = self.llm_engine.validate_llm_results(data)
            validation_report['component_results']['llm_engine'] = llm_validation
            if llm_validation['status'] == 'error':
                validation_report['errors'].extend(llm_validation['errors'])
            elif llm_validation['status'] == 'warning':
                validation_report['warnings'].extend(llm_validation['warnings'])
        except Exception as e:
            validation_report['component_results']['llm_engine'] = {
                'status': 'error',
                'error': str(e)
            }
            validation_report['errors'].append(f"LLM validation failed: {e}")
        
        # Validate court data
        try:
            court_validation = self.court_processor.validate_court_data(data)
            validation_report['component_results']['court_processor'] = court_validation
            if court_validation['status'] == 'error':
                validation_report['errors'].extend(court_validation['errors'])
            elif court_validation['status'] == 'warning':
                validation_report['warnings'].extend(court_validation['warnings'])
        except Exception as e:
            validation_report['component_results']['court_processor'] = {
                'status': 'error',
                'error': str(e)
            }
            validation_report['errors'].append(f"Court processor validation failed: {e}")
        
        # Validate law firm integration
        try:
            law_firm_validation = self.law_firm_integration.validate_law_firm_integration(data)
            validation_report['component_results']['law_firm_integration'] = law_firm_validation
            if law_firm_validation['status'] == 'error':
                validation_report['errors'].extend(law_firm_validation['errors'])
            elif law_firm_validation['status'] == 'warning':
                validation_report['warnings'].extend(law_firm_validation['warnings'])
        except Exception as e:
            validation_report['component_results']['law_firm_integration'] = {
                'status': 'error',
                'error': str(e)
            }
            validation_report['errors'].append(f"Law firm integration validation failed: {e}")
        
        # Set overall status
        if validation_report['errors']:
            validation_report['overall_status'] = 'error'
        elif validation_report['warnings']:
            validation_report['overall_status'] = 'warning'
        
        return validation_report

    def get_processing_summary(self, data: Dict) -> str:
        """
        Get a comprehensive summary of processing results.
        
        Args:
            data: Processed data dictionary
            
        Returns:
            Human-readable summary string
        """
        summary_parts = []
        
        # Get summaries from each component
        try:
            llm_summary = self.llm_engine.get_extraction_summary(data)
            if llm_summary != "LLM extraction not completed":
                summary_parts.append(f"LLM: {llm_summary}")
        except Exception:
            pass
        
        try:
            court_summary = self.court_processor.get_court_processing_summary(data)
            if court_summary != "No court data processed":
                summary_parts.append(f"Court: {court_summary}")
        except Exception:
            pass
        
        try:
            law_firm_summary = self.law_firm_integration.get_law_firm_summary(data)
            if law_firm_summary != "Law firm processing not completed":
                summary_parts.append(f"Law firms: {law_firm_summary}")
        except Exception:
            pass
        
        try:
            html_summary = self.html_processor.get_html_processing_summary(data)
            if html_summary != "HTML processed but no content extracted":
                summary_parts.append(f"HTML: {html_summary}")
        except Exception:
            pass
        
        if summary_parts:
            return f"Processing completed - {'; '.join(summary_parts)}"
        else:
            return "Processing completed but no significant content extracted"