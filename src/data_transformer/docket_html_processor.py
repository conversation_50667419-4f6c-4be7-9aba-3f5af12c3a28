"""
HTML processing for docket data.

This module handles HTML parsing and data extraction extracted from 
docket_processor.py as part of Phase 3.2 refactoring.
"""
import logging
import re
from typing import Dict, List, Optional, Any


class DocketHTMLProcessor:
    """Handles HTML parsing and data extraction for docket data."""
    
    NULL_CONDITIONS = ['', 'NA', None, 'Pro Se', 'PRO SE', "None", []]
    
    def __init__(self, html_data_updater: Any, logger: Optional[logging.Logger] = None):
        self.html_data_updater = html_data_updater
        self.logger = logger or logging.getLogger(__name__)
    
    async def process_s3_html(self, data: Dict, json_path: str) -> bool:
        """
        Process S3 HTML content and extract case information.
        
        Args:
            data: Docket data dictionary (modified in place)
            json_path: Path to JSON file for context
            
        Returns:
            True if HTML was processed successfully, False otherwise
        """
        if not isinstance(data, dict):
            self.logger.error("Invalid data dictionary provided to process_s3_html")
            return False
        
        filename = data.get('new_filename', data.get('docket_num', 'unknown'))
        
        try:
            # Get S3 HTML link
            s3_html_link = self._extract_s3_html_link(data)
            if not s3_html_link:
                self.logger.debug(f"No S3 HTML link found for {filename}")
                return False
            
            # Parse HTML content
            parsed_content = await self._parse_html_content(s3_html_link, filename)
            if not parsed_content:
                return False
            
            # Extract and update party information
            parties_updated = self._extract_party_information(data, parsed_content, filename)
            
            # Extract and update case information
            case_info_updated = self._extract_case_information(data, parsed_content, filename)
            
            # Log processing results
            if parties_updated or case_info_updated:
                self.logger.info(f"Updated docket data from HTML for {filename}")
                return True
            else:
                self.logger.debug(f"No updates made from HTML for {filename}")
                return False
                
        except Exception as e:
            self.logger.error(f"Error processing S3 HTML for {filename}: {e}", exc_info=True)
            return False

    def _extract_s3_html_link(self, data: Dict) -> Optional[str]:
        """
        Extract S3 HTML link from data.
        
        Args:
            data: Docket data dictionary
            
        Returns:
            S3 HTML link or None
        """
        # Check for HTML link in various possible fields
        html_link_fields = ['s3_html_link', 's3_link_html', 'html_link', 's3_link']
        
        for field in html_link_fields:
            link = data.get(field)
            if link and isinstance(link, str) and '.html' in link.lower():
                return link
        
        # Check if s3_link is HTML (not PDF)
        s3_link = data.get('s3_link')
        if s3_link and isinstance(s3_link, str):
            if s3_link.lower().endswith('.html'):
                return s3_link
            # Convert PDF link to HTML link
            elif s3_link.lower().endswith('.pdf'):
                html_link = s3_link.replace('.pdf', '.html')
                return html_link
        
        return None

    async def _parse_html_content(self, html_link: str, filename: str) -> Optional[Dict]:
        """
        Parse HTML content from the provided link.
        
        Args:
            html_link: URL to HTML content
            filename: Filename for logging context
            
        Returns:
            Parsed content dictionary or None
        """
        try:
            if not self.html_data_updater:
                self.logger.warning(f"HTML data updater not available for {filename}")
                return None
            
            # Use HTML data updater to parse content
            if hasattr(self.html_data_updater, 'parse_html_async'):
                parsed_content = await self.html_data_updater.parse_html_async(html_link)
            elif hasattr(self.html_data_updater, 'parse_html'):
                import asyncio
                parsed_content = await asyncio.to_thread(self.html_data_updater.parse_html, html_link)
            else:
                self.logger.error(f"HTML data updater missing parse methods for {filename}")
                return None
            
            if parsed_content and isinstance(parsed_content, dict):
                self.logger.debug(f"Successfully parsed HTML content for {filename}")
                return parsed_content
            else:
                self.logger.warning(f"HTML parsing returned empty/invalid content for {filename}")
                return None
                
        except Exception as e:
            self.logger.error(f"Error parsing HTML content for {filename}: {e}")
            return None

    def _extract_party_information(self, data: Dict, parsed_content: Dict, filename: str) -> bool:
        """
        Extract plaintiff and defendant information from parsed HTML.
        
        Args:
            data: Docket data dictionary (modified in place)
            parsed_content: Parsed HTML content
            filename: Filename for logging context
            
        Returns:
            True if party information was updated, False otherwise
        """
        updated_parties = False
        
        # Extract plaintiff names
        plaintiff_names = self._extract_plaintiff_names(parsed_content)
        if plaintiff_names:
            # Check if existing plaintiff data should be overwritten
            current_plaintiff = data.get('plaintiff', [])
            should_overwrite = not current_plaintiff or all(p in self.NULL_CONDITIONS for p in current_plaintiff)
            
            if should_overwrite:
                data['plaintiff'] = plaintiff_names
                updated_parties = True
                self.logger.debug(f"Updated 'plaintiff' from HTML: {plaintiff_names} for {filename}")
        
        # Extract defendant names
        defendant_names = self._extract_defendant_names(parsed_content)
        if defendant_names:
            # Check if existing defendant data should be overwritten
            current_defendant = data.get('defendant', [])
            current_defendants = data.get('defendants', [])
            
            should_overwrite_defendant = not current_defendant or all(d in self.NULL_CONDITIONS for d in current_defendant)
            should_overwrite_defendants = not current_defendants or all(d in self.NULL_CONDITIONS for d in current_defendants)
            
            if should_overwrite_defendant:
                data['defendant'] = defendant_names
                updated_parties = True
            if should_overwrite_defendants:
                data['defendants'] = defendant_names
                updated_parties = True
            
            if updated_parties:
                self.logger.debug(f"Updated defendant fields from HTML: {defendant_names} for {filename}")
        
        return updated_parties

    def _extract_plaintiff_names(self, parsed_content: Dict) -> List[str]:
        """
        Extract plaintiff names from parsed HTML content.
        
        Args:
            parsed_content: Parsed HTML content dictionary
            
        Returns:
            List of cleaned plaintiff names
        """
        plaintiff_names = []
        
        # Try different possible field names for plaintiffs
        plaintiff_fields = ['plaintiffs', 'plaintiff', 'plaintiff_names']
        
        for field in plaintiff_fields:
            raw_plaintiffs = parsed_content.get(field, [])
            
            if isinstance(raw_plaintiffs, list):
                seen_plaintiffs = set()
                for p in raw_plaintiffs:
                    if isinstance(p, dict):
                        # If plaintiffs are dict objects, extract name
                        name = p.get('name') or p.get('plaintiff_name') or str(p)
                    elif isinstance(p, str):
                        name = p
                    else:
                        continue
                    
                    cleaned_name = self._clean_party_name(name)
                    if cleaned_name and cleaned_name not in seen_plaintiffs:
                        plaintiff_names.append(cleaned_name)
                        seen_plaintiffs.add(cleaned_name)
            
            elif isinstance(raw_plaintiffs, str) and raw_plaintiffs.strip():
                cleaned_name = self._clean_party_name(raw_plaintiffs)
                if cleaned_name:
                    plaintiff_names.append(cleaned_name)
        
        return plaintiff_names

    def _extract_defendant_names(self, parsed_content: Dict) -> List[str]:
        """
        Extract defendant names from parsed HTML content.
        
        Args:
            parsed_content: Parsed HTML content dictionary
            
        Returns:
            List of cleaned defendant names
        """
        defendant_names = []
        
        # Try different possible field names for defendants
        defendant_fields = ['defendants', 'defendant', 'defendant_names']
        
        for field in defendant_fields:
            raw_defendants = parsed_content.get(field, [])
            
            if isinstance(raw_defendants, list):
                seen_defendants = set()
                for d in raw_defendants:
                    if isinstance(d, dict):
                        # If defendants are dict objects, extract name
                        name = d.get('name') or d.get('defendant_name') or str(d)
                    elif isinstance(d, str):
                        name = d
                    else:
                        continue
                    
                    cleaned_name = self._clean_party_name(name)
                    if cleaned_name and cleaned_name not in seen_defendants:
                        defendant_names.append(cleaned_name)
                        seen_defendants.add(cleaned_name)
            
            elif isinstance(raw_defendants, str) and raw_defendants.strip():
                cleaned_name = self._clean_party_name(raw_defendants)
                if cleaned_name:
                    defendant_names.append(cleaned_name)
        
        return defendant_names

    def _clean_party_name(self, name: str) -> Optional[str]:
        """
        Clean and validate a party name.
        
        Args:
            name: Raw party name
            
        Returns:
            Cleaned party name or None if invalid
        """
        if not isinstance(name, str):
            return None
        
        # Basic cleaning
        cleaned = name.strip()
        
        # Remove common prefixes/suffixes
        cleaned = re.sub(r'^(plaintiff|defendant):\s*', '', cleaned, flags=re.IGNORECASE)
        cleaned = re.sub(r'\s*\(.*?\)\s*$', '', cleaned)  # Remove trailing parentheses
        
        # Check for null conditions
        if not cleaned or cleaned in self.NULL_CONDITIONS:
            return None
        
        # Check for minimum length
        if len(cleaned) < 2:
            return None
        
        # Check for obviously invalid names
        invalid_patterns = [
            r'^[0-9\-\s]+$',  # Only numbers, dashes, spaces
            r'^[^a-zA-Z]*$',  # No letters at all
            r'^\W+$'          # Only special characters
        ]
        
        for pattern in invalid_patterns:
            if re.match(pattern, cleaned):
                return None
        
        return cleaned

    def _extract_case_information(self, data: Dict, parsed_content: Dict, filename: str) -> bool:
        """
        Extract case information from parsed HTML content.
        
        Args:
            data: Docket data dictionary (modified in place)
            parsed_content: Parsed HTML content
            filename: Filename for logging context
            
        Returns:
            True if case information was updated, False otherwise
        """
        updated_case_info = False
        
        # Fields to extract from parsed content
        case_info_fields = {
            'nature_of_suit': ['nature_of_suit', 'nature', 'cause'],
            'jurisdiction': ['jurisdiction', 'court_jurisdiction'],
            'case_type': ['case_type', 'type'],
            'judge': ['judge', 'assigned_judge', 'presiding_judge'],
            'case_flags': ['case_flags', 'flags', 'status_flags']
        }
        
        for target_field, source_fields in case_info_fields.items():
            for source_field in source_fields:
                if source_field in parsed_content:
                    value = parsed_content[source_field]
                    
                    # Only update if current field is missing/null
                    if target_field not in data or data[target_field] in self.NULL_CONDITIONS:
                        if value not in self.NULL_CONDITIONS:
                            data[target_field] = value
                            updated_case_info = True
                            self.logger.debug(f"Updated '{target_field}' from HTML for {filename}")
                    break
        
        # Merge any other relevant case_info fields
        if 'case_info' in parsed_content and isinstance(parsed_content['case_info'], dict):
            case_info = parsed_content['case_info']
            
            for key, value in case_info.items():
                # Avoid overwriting plaintiffs/defendants handled above
                if key not in ['plaintiffs', 'defendants', 'plaintiff', 'defendant']:
                    if key not in data or data[key] in self.NULL_CONDITIONS:
                        if value not in self.NULL_CONDITIONS:
                            data[key] = value
                            updated_case_info = True
        
        return updated_case_info

    def validate_html_extraction(self, data: Dict, original_data: Dict) -> Dict[str, Any]:
        """
        Validate HTML extraction results.
        
        Args:
            data: Updated data dictionary
            original_data: Original data before HTML processing
            
        Returns:
            Validation report
        """
        report = {
            'status': 'success',
            'updates': [],
            'warnings': [],
            'errors': []
        }
        
        # Check what was updated
        fields_to_check = ['plaintiff', 'defendant', 'defendants', 'nature_of_suit', 'jurisdiction', 'judge']
        
        for field in fields_to_check:
            original_value = original_data.get(field)
            updated_value = data.get(field)
            
            if original_value != updated_value:
                report['updates'].append(f"Updated {field}")
                
                # Validate the update
                if field in ['plaintiff', 'defendant', 'defendants']:
                    if isinstance(updated_value, list) and len(updated_value) == 0:
                        report['warnings'].append(f"HTML extraction resulted in empty {field} list")
                    elif isinstance(updated_value, list) and len(updated_value) > 50:
                        report['warnings'].append(f"Unusually large number of {field}s extracted: {len(updated_value)}")
        
        # Overall status
        if report['errors']:
            report['status'] = 'error'
        elif report['warnings']:
            report['status'] = 'warning'
        elif not report['updates']:
            report['status'] = 'no_changes'
        
        return report

    def get_html_processing_summary(self, data: Dict) -> str:
        """
        Get summary of HTML processing results.
        
        Args:
            data: Processed data dictionary
            
        Returns:
            Human-readable summary
        """
        summary_parts = []
        
        # Count extracted parties
        plaintiff_count = len(data.get('plaintiff', []))
        defendant_count = len(data.get('defendant', []))
        
        if plaintiff_count > 0:
            summary_parts.append(f"{plaintiff_count} plaintiff(s)")
        if defendant_count > 0:
            summary_parts.append(f"{defendant_count} defendant(s)")
        
        # Check for case information
        case_fields = ['nature_of_suit', 'jurisdiction', 'judge']
        extracted_case_fields = [f for f in case_fields if data.get(f) not in self.NULL_CONDITIONS]
        
        if extracted_case_fields:
            summary_parts.append(f"{len(extracted_case_fields)} case info fields")
        
        if summary_parts:
            return f"HTML extraction: {', '.join(summary_parts)}"
        else:
            return "HTML processed but no content extracted"