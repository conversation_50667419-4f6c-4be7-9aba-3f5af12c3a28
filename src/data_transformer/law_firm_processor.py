import json
import logging
import os
from typing import Dict, List, Set, Any, Tuple

from src.utils.law_firm_normalizer import normalize_law_firm_name


class LawFirmProcessor:
    """
    Processes attorney and law firm information from docket data,
    including extraction and formatting.
    """
    NULL_CONDITIONS = ['', 'NA', 'N/A', None, 'Pro Se', 'PRO SE', "None", []]

    def __init__(self, config: Dict = None):
        self.config = config or {}
        self.logger = logging.getLogger(__name__)  # Use __name__ for logger
        # Ensure proper logging configuration for data_transformer modules
        self.logger.handlers = []
        self.logger.propagate = True
        self.ACRONYMS = [  # Not actively used in cleaning, but kept for reference
            'PC', 'PSC', 'PLLC', 'LLC', 'PLC', 'LLP', 'PA', 'APLC', 'LP', 'Ltd', 'APC',
            'LPA', 'INC', 'INCORPORATED', 'PLLP', 'CO', 'CHARTERED', 'A LAW CORPORATION', 'ALC'
        ]

        # Get project_root from config
        project_root = self.config.get('project_root')
        if not project_root:
            # Fall back to directories.base_dir
            project_root = self.config.get('directories', {}).get('base_dir')
            if not project_root:
                self.logger.warning("No project_root found in config, using current directory")
                project_root = os.getcwd()

        self.attorney_lookup_path = os.path.join(project_root, "src", "config", "attorneys", "attorney_lookup.json")
        self.attorney_lookup = {}
        self._load_attorney_lookup()

    def _load_attorney_lookup(self):
        """Load attorney lookup from JSON file."""
        try:
            if os.path.exists(self.attorney_lookup_path):
                with open(self.attorney_lookup_path, 'r') as f:
                    lookup_data = json.load(f)
                    # Create a dictionary with attorney_name as key for quick lookup
                    self.attorney_lookup = {item["attorney_name"]: item["law_firm"]
                                            for item in lookup_data
                                            if "attorney_name" in item and "law_firm" in item}
                    self.logger.info(f"Loaded {len(self.attorney_lookup)} attorney-law firm mappings")
            else:
                self.logger.warning(f"Attorney lookup file not found: {self.attorney_lookup_path}")
        except Exception as e:
            self.logger.error(f"Error loading attorney lookup: {str(e)}")

    def _clean_law_firm_name(self, name: Any) -> str:
        """
        Cleans and normalizes a law firm name.
        
        First performs basic cleaning, then applies normalization.
        
        Args:
            name: Raw law firm name
            
        Returns:
            Normalized law firm name
        """
        if not isinstance(name, str): return ""

        # First do basic cleaning as before
        cleaned_name = name.replace(',', '').replace('.', '')
        cleaned_name = cleaned_name.strip()

        # Now apply normalization
        normalized_name = normalize_law_firm_name(cleaned_name)

        return normalized_name

    def _extract_and_format_law_firms(self, data: Dict[str, Any]):
        """
        Extracts unique law firms from 'attorneys_gpt' or 'attorney', populates 'law_firms' list
        and 'law_firm' string. Filters out null values from attorney dictionaries.
        If attorneys_gpt doesn't contain at least one item with a law firm key, processes attorney key instead.
        Also removes 'bar_info' and 'phone' keys if their value is "N/A" from attorneys_gpt items.
        """
        unique_firms_list: List[str] = []
        seen_firms: Set[str] = set()
        attorneys_gpt = data.get('attorneys_gpt')
        found_law_firm_in_attorneys_gpt = False

        # First try to process attorneys_gpt
        if isinstance(attorneys_gpt, list):
            self.logger.info(f"Processing attorneys_gpt with {len(attorneys_gpt)} attorneys: {attorneys_gpt}")
            # Convert string entries to dict format for consistency
            normalized_attorneys_gpt = []
            for attorney_info in attorneys_gpt:
                if isinstance(attorney_info, str):
                    # Convert string to dict format
                    normalized_attorneys_gpt.append({
                        "attorney_name": attorney_info.strip(),
                        "law_firm": None  # Will be populated later if needed
                    })
                    self.logger.debug(f"Converted string attorney to dict: {attorney_info}")
                elif isinstance(attorney_info, dict):
                    normalized_attorneys_gpt.append(attorney_info)
                else:
                    self.logger.warning(f"Item in attorneys_gpt is neither string nor dict: {attorney_info}")
                    continue
            
            # Update the data with normalized structure
            data['attorneys_gpt'] = normalized_attorneys_gpt
            attorneys_gpt = normalized_attorneys_gpt
            
            # Iterate through the normalized list
            for attorney_info in attorneys_gpt:  # attorneys_gpt is now a list of dicts
                if not isinstance(attorney_info, dict):
                    self.logger.warning(f"Item in attorneys_gpt is not a dict after normalization: {attorney_info}")
                    continue

                # --- START: Remove 'bar_info' and 'phone' if "N/A" ---
                if attorney_info.get('bar_info') == "N/A":
                    try:
                        del attorney_info['bar_info']
                        self.logger.debug(
                            f"Removed 'bar_info': 'N/A' for attorney: {attorney_info.get('attorney_name', 'Unknown')}")
                    except KeyError:
                        pass  # Should not happen if .get() found it

                if attorney_info.get('phone') == "N/A":
                    try:
                        del attorney_info['phone']
                        self.logger.debug(
                            f"Removed 'phone': 'N/A' for attorney: {attorney_info.get('attorney_name', 'Unknown')}")
                    except KeyError:
                        pass
                # --- END: Remove 'bar_info' and 'phone' if "N/A" ---

                # Check for law_firm and try to get law_firm from lookup if missing
                if not attorney_info.get('law_firm') and 'attorney_name' in attorney_info:
                    attorney_name = attorney_info['attorney_name']
                    if attorney_name in self.attorney_lookup:
                        law_firm_from_lookup = self.attorney_lookup[attorney_name]
                        attorney_info['law_firm'] = law_firm_from_lookup
                        self.logger.info(
                            f"Updated attorney {attorney_name} with law_firm from lookup: {law_firm_from_lookup}")

                # Now process for law firm extraction
                raw_firm_name = attorney_info.get('law_firm')
                if raw_firm_name and raw_firm_name not in self.NULL_CONDITIONS:
                    found_law_firm_in_attorneys_gpt = True
                    cleaned_firm_name = self._clean_law_firm_name(raw_firm_name)
                    if cleaned_firm_name and cleaned_firm_name not in seen_firms:
                        unique_firms_list.append(cleaned_firm_name)
                        seen_firms.add(cleaned_firm_name)
                        self.logger.debug(f"Added law firm from attorneys_gpt: {cleaned_firm_name}")

            if found_law_firm_in_attorneys_gpt:
                self.logger.info(
                    f"Found {len(unique_firms_list)} unique law firms in attorneys_gpt after N/A field cleanup.")
            else:
                # This case would be hit if attorneys_gpt was empty or all items lacked a valid law_firm after processing.
                self.logger.info(
                    "No valid law firms found in attorneys_gpt after N/A field cleanup. Will check 'attorney' key.")

        # If no law firm found in attorneys_gpt (i.e., found_law_firm_in_attorneys_gpt is still False)
        # or if attorneys_gpt itself was empty/None initially.
        if not found_law_firm_in_attorneys_gpt:
            # This log can be confusing if attorneys_gpt existed but had no law firms.
            # Let's refine the condition for this log.
            if not attorneys_gpt or len(attorneys_gpt) == 0:
                self.logger.info("attorneys_gpt is empty or not present. Processing 'attorney' key instead.")
            # else: (attorneys_gpt existed but no firms found in it) - covered by log above.

            attorney_data = data.get('attorney')
            if attorney_data is None:
                self.logger.debug("Key 'attorney' is None or not present.")  # Changed to debug
            elif isinstance(attorney_data, list):
                self.logger.debug(
                    f"'attorney' key is a list with {len(attorney_data)} items. Processing for law firms.")
                for attorney_item in attorney_data:
                    if isinstance(attorney_item, dict) and 'law_firm' in attorney_item:
                        law_firm_value = attorney_item.get('law_firm')
                        if law_firm_value and law_firm_value not in self.NULL_CONDITIONS:
                            cleaned_firm_name = self._clean_law_firm_name(law_firm_value)
                            if cleaned_firm_name and cleaned_firm_name not in seen_firms:
                                unique_firms_list.append(cleaned_firm_name)
                                seen_firms.add(cleaned_firm_name)
                                self.logger.debug(
                                    f"Added law firm from 'attorney' key (list item): {cleaned_firm_name}")
            elif isinstance(attorney_data, dict) and 'law_firm' in attorney_data:
                law_firm_value = attorney_data.get('law_firm')
                if law_firm_value and law_firm_value not in self.NULL_CONDITIONS:
                    cleaned_firm_name = self._clean_law_firm_name(law_firm_value)
                    if cleaned_firm_name and cleaned_firm_name not in seen_firms:
                        unique_firms_list.append(cleaned_firm_name)
                        seen_firms.add(cleaned_firm_name)
                        self.logger.debug(f"Added law firm from 'attorney' key (dict): {cleaned_firm_name}")
            else:
                self.logger.debug(f"'attorney' key is not a list or dict with expected law firm info.")

            if len(unique_firms_list) > 0 and not found_law_firm_in_attorneys_gpt:  # Check again to ensure it was from 'attorney' key
                self.logger.info(f"Extracted {len(unique_firms_list)} unique law firms from 'attorney' key.")

        # Check if this case was matched by ignore_download config
        if 'ignore_download' in data.get('_processing_notes', ''):
            # This case should have law firm overrides from ignore_download config
            # We need to re-apply them here since they may have been lost
            self.logger.info("Case was matched by ignore_download config, checking for law firm overrides")
            
            # Load ignore_download config to find the matching entry
            ignore_download_path = os.path.join(
                self.config.get('project_root', os.getcwd()),
                'src', 'config', 'pacer', 'ignore_download', 'ignore_download.json'
            )
            
            try:
                with open(ignore_download_path, 'r') as f:
                    ignore_download_config = json.load(f)
                    
                # Find matching entry
                court_id = data.get('court_id')
                attorney_list = data.get('attorney', [])
                
                for entry in ignore_download_config:
                    if entry.get('court_id') != court_id:
                        continue
                        
                    # Check attorney match
                    match_found = False
                    for attorney in attorney_list:
                        if isinstance(attorney, dict):
                            attorney_name = attorney.get('attorney_name', '').strip().upper()
                            law_firm = attorney.get('law_firm', '').strip().upper()
                            
                            entry_attorney = entry.get('attorney_name', '').strip().upper()
                            entry_law_firm = entry.get('law_firm', '').strip().upper()
                            
                            if (attorney_name == entry_attorney and law_firm == entry_law_firm):
                                match_found = True
                                break
                    
                    if match_found and 'report_law_firm' in entry:
                        # Apply the override
                        report_law_firm = entry['report_law_firm']
                        law_firms_list = [firm.strip() for firm in report_law_firm.split(' ; ') if firm.strip()]
                        data['law_firms'] = law_firms_list
                        data['law_firm'] = report_law_firm
                        self.logger.info(f"Applied ignore_download law firm override: '{report_law_firm}'")
                        return  # Exit early, we've set the values
                        
            except Exception as e:
                self.logger.error(f"Error loading ignore_download config: {e}")
        
        # Normal processing - update data dictionary with extracted law firms
        data['law_firms'] = unique_firms_list
        data['law_firm'] = " ; ".join(unique_firms_list)

        if unique_firms_list:
            self.logger.debug(f"Final generated law_firms list: {data['law_firms']}")
            self.logger.debug(f"Final generated law_firm string: '{data['law_firm']}'")
        else:
            self.logger.info("No law firms ultimately found to populate 'law_firms' or 'law_firm'.")

        # The attorneys_gpt list within 'data' is modified in-place if applicable.

    def extract_unique_attorneys(self, data: Dict) -> List[Tuple[str, str, str]]:
        """
        Extract unique attorney combinations (name, email, cleaned_law_firm)
        from attorneys_gpt or attorney key.
        NOTE: This now benefits from the "N/A" filtering done in
              _extract_and_format_law_firms if process_law_firms is called first.
              If called independently, consider adding the filtering here too.
        """
        unique_attorneys: Set[Tuple[str, str, str]] = set()
        found_valid_attorney_in_attorneys_gpt = False

        # First try attorneys_gpt
        attorneys_gpt = data.get(
            'attorneys_gpt')  # Assumes 'attorneys_gpt' might already be cleaned by the other method
        if isinstance(attorneys_gpt, list):
            for attorney in attorneys_gpt:
                if not isinstance(attorney, dict): continue
                # No need to filter "N/A" here again if _extract_and_format_law_firms runs first and updates data['attorneys_gpt']
                name = attorney.get('attorney_name', '').strip()
                email = attorney.get('email', '').strip()
                raw_firm_name = attorney.get('law_firm')
                cleaned_firm_name = self._clean_law_firm_name(raw_firm_name)

                # Skip if name, email, or cleaned firm name is missing/null
                if (not name or name in self.NULL_CONDITIONS or
                        not email or email in self.NULL_CONDITIONS or
                        not cleaned_firm_name or cleaned_firm_name in self.NULL_CONDITIONS):
                    continue  # Skip incomplete entries silently for brevity

                found_valid_attorney_in_attorneys_gpt = True
                attorney_tuple = (name, email, cleaned_firm_name)
                unique_attorneys.add(attorney_tuple)  # Set handles duplicates

        # If no valid attorney found in attorneys_gpt, try attorney key
        if not found_valid_attorney_in_attorneys_gpt:
            self.logger.info("No valid attorney found in attorneys_gpt, checking attorney key")
            attorney_data = data.get('attorney')

            if isinstance(attorney_data, list):
                for attorney in attorney_data:
                    if not isinstance(attorney, dict): continue
                    name = attorney.get('attorney_name', '').strip()
                    email = attorney.get('email', '').strip()
                    raw_firm_name = attorney.get('law_firm')
                    cleaned_firm_name = self._clean_law_firm_name(raw_firm_name)

                    # Skip if name, email, or cleaned firm name is missing/null
                    if (not name or name in self.NULL_CONDITIONS or
                            not email or email in self.NULL_CONDITIONS or
                            not cleaned_firm_name or cleaned_firm_name in self.NULL_CONDITIONS):
                        continue  # Skip incomplete entries silently for brevity

                    attorney_tuple = (name, email, cleaned_firm_name)
                    unique_attorneys.add(attorney_tuple)  # Set handles duplicates
                    self.logger.debug(f"Added attorney tuple from attorney key: {attorney_tuple}")
            elif isinstance(attorney_data, dict):
                name = attorney_data.get('attorney_name', '').strip()
                email = attorney_data.get('email', '').strip()
                raw_firm_name = attorney_data.get('law_firm')
                cleaned_firm_name = self._clean_law_firm_name(raw_firm_name)

                # Check if name, email, and cleaned firm name are all valid
                if (name and name not in self.NULL_CONDITIONS and
                        email and email not in self.NULL_CONDITIONS and
                        cleaned_firm_name and cleaned_firm_name not in self.NULL_CONDITIONS):
                    attorney_tuple = (name, email, cleaned_firm_name)
                    unique_attorneys.add(attorney_tuple)  # Set handles duplicates
                    self.logger.debug(f"Added attorney tuple from attorney key (dict): {attorney_tuple}")

        result_list = list(unique_attorneys)
        self.logger.debug(f"Extracted {len(result_list)} unique attorney (name, email, firm) tuples.")
        return result_list

    def process_law_firms(self, data: Dict):
        """
        Processes attorney and law firm info. Populates 'law_firms' (list) and
        'law_firm' (string). Removes deprecated keys.
        If attorneys_gpt doesn't contain at least one item with a law firm key, processes attorney key instead.
        """
        docket_num = data.get('docket_num', 'NA')
        log_filename = data.get('new_filename', docket_num)  # Use new_filename for logging if available
        self.logger.info(f"Starting law firm processing for: {log_filename}")

        # 1. Extract and format law firms list and string
        # This will check attorneys_gpt first, and if no law firm is found, it will check attorney key
        self._extract_and_format_law_firms(data)  # Populates data['law_firms'] and data['law_firm']

        # 2. Remove deprecated keys
        self.remove_deprecated_keys(data)  # This handles all deprecated keys

        # Log the results
        if data.get('law_firms') and len(data.get('law_firms', [])) > 0:
            self.logger.info(
                f"Found {len(data.get('law_firms', []))} law firms for {log_filename}: {data.get('law_firm')}")
        else:
            self.logger.warning(f"No law firms found for {log_filename} after processing")

        self.logger.info(f"Finished law firm processing for: {log_filename}")

    @staticmethod
    def remove_deprecated_keys(data: Dict) -> None:
        """Remove deprecated keys from the data dictionary."""
        # Handle potential transferor_docket_law_firm update safely
        law_firm2_val = data.get('law_firm2')
        if law_firm2_val not in LawFirmProcessor.NULL_CONDITIONS and data.get('transferred_in', False):
            data['transferor_docket_law_firm'] = law_firm2_val
        # elif 'transferor_docket_law_firm' not in data:  # Ensure field exists even if law_firm2 was null
        #     data['transferor_docket_law_firm'] = ''

        deprecated_keys = {
            'attorney_emails',
            'law_firm_emails',
            'matched_law_firms',
            'matched_law_firms_list',
            'attorney_emails_gpt',
            'law_firms_gpt',
            'law_firms_gpt_list',
            'merged_law_firms',
            'merged_law_firms_list',
            'law_firm_stats',
            'normalized_filename',  # This specific key might be handled differently now
            # Keep law_firm_stats removal? Yes.
            'law_firm2'  # Remove after potentially using its value
        }

        keys_removed_count = 0
        for key in deprecated_keys:
            if key in data:
                try:
                    del data[key]
                    keys_removed_count += 1
                except KeyError:
                    pass  # Should not happen after check

        # if keys_removed_count > 0:
        #     logging.debug(f"Removed {keys_removed_count} deprecated keys.")

# TODO: Update process_law_firm to handle attorney matching case insensitvely and properly parse out law_firm name.
