"""
Data processing engine for DataTransformer.

This module handles core data processing and enrichment logic extracted from transformer.py
as part of Phase 3 refactoring.
"""
import asyncio
import os
from datetime import datetime
from typing import Dict, Optional, Tuple

from .docket_processor import DocketProcessor
from .mdl_processor import MDLProcessor


class DataProcessingEngine:
    """Core data processing and enrichment engine."""
    
    def __init__(
        self,
        docket_processor: DocketProcessor,
        mdl_processor: MDLProcessor,
        llm_client,
        logger
    ):
        self.docket_processor = docket_processor
        self.mdl_processor = mdl_processor
        self.llm_client = llm_client
        self.logger = logger
    
    async def load_and_validate_initial_data(
        self, 
        json_path: str, 
        force_reprocess: bool
    ) -> Optional[Tuple[Dict, str, str]]:
        """
        Load and validate initial data from JSON file.
        
        Args:
            json_path: Path to JSON file
            force_reprocess: Whether to force reprocessing
            
        Returns:
            Tuple of (data, base_filename, current_dir) or None if validation fails
        """
        try:
            self.logger.debug(f"Loading data from {json_path}")
            
            # Load JSON data
            data = await self._load_json_data(json_path)
            if not data:
                self.logger.error(f"Failed to load data from {json_path}")
                return None
            
            # Extract file information
            base_filename = os.path.splitext(os.path.basename(json_path))[0]
            current_dir = os.path.dirname(json_path)
            
            # Validate data structure
            if not self._validate_data_structure(data, base_filename):
                return None
            
            # Check if reprocessing is needed
            if not force_reprocess and self._is_already_processed(data, base_filename):
                self.logger.debug(f"File {base_filename} already processed, skipping")
                return None
            
            self.logger.debug(f"Data loaded and validated for {base_filename}")
            return data, base_filename, current_dir
            
        except Exception as e:
            self.logger.error(f"Error loading/validating data from {json_path}: {e}")
            return None
    
    async def _load_json_data(self, json_path: str) -> Optional[Dict]:
        """Load JSON data from file."""
        try:
            import json
            
            def _sync_load_json():
                with open(json_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            
            return await asyncio.to_thread(_sync_load_json)
            
        except Exception as e:
            self.logger.error(f"Error reading JSON file {json_path}: {e}")
            return None
    
    def _validate_data_structure(self, data: Dict, base_filename: str) -> bool:
        """Validate that data has required structure."""
        if not isinstance(data, dict):
            self.logger.error(f"Data is not a dictionary for {base_filename}")
            return False
        
        # Check for essential fields
        required_fields = ['docket_num']  # Minimal requirement
        for field in required_fields:
            if field not in data:
                self.logger.warning(f"Missing required field '{field}' in {base_filename}")
                # Don't fail validation for missing fields, just warn
        
        return True
    
    def _is_already_processed(self, data: Dict, base_filename: str) -> bool:
        """Check if data indicates it's already been processed."""
        # Check for processing completion indicators
        processing_status = data.get('processing_status')
        if processing_status == 'completed':
            return True
        
        # Check for presence of key processed fields
        processed_indicators = ['new_filename', 'law_firm_normalized', 'mdl_info_added']
        processed_count = sum(1 for indicator in processed_indicators if data.get(indicator))
        
        # If most indicators are present, consider it processed
        if processed_count >= len(processed_indicators) // 2:
            self.logger.debug(f"File {base_filename} appears already processed ({processed_count}/{len(processed_indicators)} indicators)")
            return True
        
        return False
    
    async def enrich_data(
        self, 
        working_data: Dict, 
        pdf_text_content: Optional[str],
        current_json_path: str, 
        skip_case_info_llm: bool
    ) -> bool:
        """
        Run all data enrichment steps (LLM, HTML, Transfers, MDL, etc.).
        
        Args:
            working_data: Working data dictionary
            pdf_text_content: PDF text content for LLM processing
            current_json_path: Path to current JSON file
            skip_case_info_llm: Whether to skip LLM case info extraction
            
        Returns:
            True on success, False on critical failure
        """
        base_filename = os.path.splitext(os.path.basename(current_json_path))[0]
        self.logger.info(
            f"Starting enrichment for {base_filename}.json "
            f"(Skip LLM Case Info Fields={skip_case_info_llm})"
        )
        
        try:
            # LLM Extraction
            await self._run_llm_extraction(
                working_data, pdf_text_content, base_filename, 
                current_json_path, skip_case_info_llm
            )
            
            # Other Enrichment Steps
            await self._run_other_enrichment_steps(working_data, current_json_path, base_filename)
            
            self.logger.info(f"Data enrichment completed successfully for {base_filename}")
            return True
            
        except Exception as e:
            self.logger.error(f"Critical error during data enrichment for {base_filename}: {e}", exc_info=True)
            return False
    
    async def _run_llm_extraction(
        self,
        working_data: Dict,
        pdf_text_content: Optional[str],
        base_filename: str,
        current_json_path: str,
        skip_case_info_llm: bool
    ):
        """Run LLM extraction if conditions are met."""
        if pdf_text_content and self.llm_client and self.docket_processor:
            self.logger.info(f"Running LLM extraction via DocketProcessor for {base_filename}...")
            
            try:
                await self.docket_processor.run_llm_extraction(
                    working_data,
                    pdf_text_content
                )
                self.logger.info(f"LLM extraction completed for {base_filename}.")

                # If classifier determined LLM should be skipped for case info fields,
                # nullify them now so MDLProcessor can fill them from lookup.
                if skip_case_info_llm:
                    self.logger.debug(
                        f"Classifier identified MDL for {base_filename}. "
                        f"Nullifying LLM-extracted title/summary/allegations to prefer MDL lookup."
                    )
                    working_data['title'] = None
                    working_data['summary'] = None
                    working_data['allegations'] = None

            except Exception as llm_err:
                self.logger.error(f"LLM extraction error for {base_filename}: {llm_err}", exc_info=True)
                
                if hasattr(self.docket_processor, '_update_error_status'):
                    self.docket_processor._update_error_status(
                        working_data, current_json_path, f"LLM extraction err: {llm_err}"
                    )
                else:
                    working_data['processing_error'] = f"LLM extraction err: {llm_err}"
                    working_data['last_error_date'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        else:
            self.logger.debug(
                f"Skipping LLM extraction for {base_filename} "
                f"(Criteria not met: Text={bool(pdf_text_content)}, "
                f"Client={bool(self.llm_client)}, Processor={bool(self.docket_processor)})"
            )
    
    async def _run_other_enrichment_steps(
        self,
        working_data: Dict,
        current_json_path: str,
        base_filename: str
    ):
        """Run other enrichment steps (HTML, transfers, MDL, etc.)."""
        self.logger.debug(f"Running other enrichment steps for {base_filename}")
        
        try:
            # HTML processing
            if self.docket_processor:
                await self.docket_processor.process_s3_html(working_data, current_json_path)
                self.docket_processor.process_filing_date(working_data)
                self.docket_processor.process_nature_of_suit(working_data)
                self.docket_processor.process_case_flags(working_data)
                self.docket_processor.process_court_info(working_data)
                
                # Law firm processing
                await self.docket_processor.process_law_firms(working_data)
                
                # Transfer processing
                if hasattr(self.docket_processor, 'transfer_handler') and self.docket_processor.transfer_handler:
                    await self.docket_processor.transfer_handler.process_transfers(working_data)
                
                self.logger.debug(f"DocketProcessor enrichment completed for {base_filename}")

            # MDL processing
            if self.mdl_processor:
                await self.mdl_processor.add_mdl_info_async(working_data)
                self.mdl_processor.calculate_afff_num_plaintiffs(working_data)
                self.logger.debug(f"MDLProcessor enrichment completed for {base_filename}")

        except Exception as e:
            self.logger.error(f"Error in other enrichment steps for {base_filename}: {e}", exc_info=True)
            raise
    
    def clean_duplicate_fields(self, data: Dict):
        """
        Clean duplicate fields from data dictionary.
        
        Args:
            data: Data dictionary to clean
        """
        # Map of standardized field names to their possible duplicates
        field_mappings = {
            'court_id': ['CourtId', 'courtId'],
            'docket_num': ['DocketNum', 'docketNum'],
            'date_filed': ['DateFiled', 'dateFiled'],
            'case_title': ['CaseTitle', 'caseTitle'],
            'law_firm': ['LawFirm', 'lawFirm'],
            'mdl_num': ['MdlNum', 'mdlNum'],
            's3_link': ['S3Link', 's3Link']
        }
        
        cleaned_count = 0
        for standard_field, duplicate_fields in field_mappings.items():
            # Keep the standard field if it exists
            if standard_field in data:
                for dup_field in duplicate_fields:
                    if dup_field in data:
                        del data[dup_field]
                        cleaned_count += 1
                        self.logger.debug(f"Removed duplicate field '{dup_field}' (kept '{standard_field}')")
            else:
                # If standard field doesn't exist, promote the first duplicate found
                for dup_field in duplicate_fields:
                    if dup_field in data:
                        data[standard_field] = data[dup_field]
                        del data[dup_field]
                        cleaned_count += 1
                        self.logger.debug(f"Promoted '{dup_field}' to '{standard_field}'")
                        break
        
        if cleaned_count > 0:
            self.logger.debug(f"Cleaned {cleaned_count} duplicate fields from data")
    
    def extract_mdl_from_flags(self, data: Dict) -> Optional[str]:
        """
        Extract MDL number from case flags.
        
        Args:
            data: Data dictionary containing case flags
            
        Returns:
            MDL number if found, None otherwise
        """
        case_flags = data.get('case_flags', [])
        if not isinstance(case_flags, list):
            return None
        
        for flag in case_flags:
            if isinstance(flag, str) and 'MDL' in flag.upper():
                # Try to extract number from flag
                import re
                mdl_match = re.search(r'MDL[:\s]*(\d+)', flag, re.IGNORECASE)
                if mdl_match:
                    mdl_num = mdl_match.group(1)
                    self.logger.debug(f"Extracted MDL number {mdl_num} from case flag: {flag}")
                    return mdl_num
        
        return None