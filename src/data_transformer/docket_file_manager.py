import asyncio
import glob
import json
import logging
import os
from typing import Union, List, Set, Optional

from src.utils import try_remove


class DocketFileManager:
    def __init__(self, download_dir: str, file_handler):
        self.download_dir = download_dir
        self.file_handler = file_handler
        self.logger = logging.getLogger(__name__)  # Use __name__ for logger
        # Ensure proper logging configuration for data_transformer modules
        self.logger.handlers = []
        self.logger.propagate = True

    def process_zip_files(self):
        """Refactored: Process all zip files in the dockets directory."""
        docket_dir = os.path.join(self.download_dir, 'dockets')
        self.logger.debug(f"Processing zip files in directory: {docket_dir}")
        zip_filenames = glob.glob(os.path.join(docket_dir, '*.zip'))
        if zip_filenames:
            self.logger.info(f"Found {len(zip_filenames)} zip files. Extracting...")
            self.file_handler.process_all_zip_files()
        else:
            self.logger.info("No zip files to process.")

    async def _check_for_error_key(self, json_path: str) -> bool:
        """
        Helper method to asynchronously check if a JSON file contains the 'processing_error' key.
        Leverages FileHandler for loading if possible.
        """
        # --- This method remains unchanged ---
        try:
            # Assuming self.file_handler exists and has an async load method
            # If not, implement direct async file reading and JSON parsing here.
            data = await self.file_handler.load_json_async(json_path)
            return data is not None and 'processing_error' in data
        except FileNotFoundError:
            self.logger.warning(f"File not found while checking for error key: {json_path}")
            return False
        except json.JSONDecodeError:
            self.logger.warning(
                f"Invalid JSON structure in {os.path.basename(json_path)} while checking for error key.")
            return False
        except Exception as e:
            self.logger.warning(f"Error reading {os.path.basename(json_path)} to check for error key: {e}",
                                exc_info=False)  # Keep log concise
            return False

    async def get_filtered_json_files_async(self,
                                            files_to_process: Optional[Union[List[str], Set[str], bool]] = False,
                                            start_from_incomplete: bool = False) -> List[str]:
        """
        Identifies JSON files in the target directory based on filtering criteria.
        Includes recovery logic for '.bak' and '.tmp' files.

        Args:
            files_to_process:
                - List/Set[str]: Process only these specific files (full paths).
                - True: Process all standard files found/recovered (subject to start_from_incomplete).
                - False/None: Process standard files found/recovered (subject to start_from_incomplete).
            start_from_incomplete: If True, prioritizes selecting only files with a 'processing_error' key
                                   from the found/recovered files.

        Returns:
            List[str]: A list of absolute paths to the JSON files selected for processing.
        """
        target_dir = self.file_handler.get_target_docket_directory()
        if not await asyncio.to_thread(os.path.isdir, target_dir):
            self.logger.error(f"Target directory for filtering not found: {target_dir}")
            return []

        self.logger.info(f"Scanning for JSON files (including recovery) in: {target_dir}")
        self.logger.debug(
            f"Filtering params: files_to_process type={type(files_to_process).__name__}, start_from_incomplete={start_from_incomplete}")

        initial_candidates = []  # Files found initially (.json, .bak, .tmp)
        recovered_files_map = {}  # Track original names of recovered files base -> recovered_json_path

        # --- Step 1: Glob for all potentially relevant files ---
        try:
            def glob_sync_multiple(patterns):
                all_files = set()
                for pattern in patterns:
                    all_files.update(glob.glob(pattern))
                return list(all_files)

            glob_patterns = [
                os.path.join(target_dir, "*.json"),
                os.path.join(target_dir, "*.json.bak"),
                os.path.join(target_dir, "*.json.tmp")
            ]
            initial_candidates = await asyncio.to_thread(glob_sync_multiple, glob_patterns)
            self.logger.debug(f"Initial scan found {len(initial_candidates)} potential files (.json, .bak, .tmp).")

        except Exception as e:
            self.logger.error(f"Error during initial glob scan: {e}", exc_info=True)
            return []

        # --- Step 2: Perform Recovery ---
        # Group files by their base name (without .json/.bak/.tmp)
        files_by_base = {}
        for f_path in initial_candidates:
            # Robustly get base name: remove .json, then .bak or .tmp if present
            base_name = os.path.basename(f_path)
            if base_name.endswith(".json.bak"):
                base_name = base_name[:-len(".json.bak")]
            elif base_name.endswith(".json.tmp"):
                base_name = base_name[:-len(".json.tmp")]
            elif base_name.endswith(".json"):
                base_name = base_name[:-len(".json")]
            else:
                self.logger.warning(f"Skipping unexpected file during recovery grouping: {f_path}")
                continue  # Skip files not matching expected patterns

            if base_name not in files_by_base:
                files_by_base[base_name] = {}
            if f_path.endswith(".json"): files_by_base[base_name]['json'] = f_path
            if f_path.endswith(".json.bak"): files_by_base[base_name]['bak'] = f_path
            if f_path.endswith(".json.tmp"): files_by_base[base_name]['tmp'] = f_path

        # Process each group to recover if necessary
        recovered_or_existing_jsons = []
        for base_name, paths in files_by_base.items():
            target_json_path = os.path.join(target_dir, f"{base_name}.json")
            bak_path = paths.get('bak')
            tmp_path = paths.get('tmp')
            json_path = paths.get('json')

            if json_path:  # If .json exists, prioritize it
                recovered_or_existing_jsons.append(json_path)
                # Clean up potential leftover .bak or .tmp for this base
                if bak_path: await asyncio.to_thread(try_remove, bak_path, self.logger,
                                                     f"redundant backup for existing {base_name}.json")
                if tmp_path: await asyncio.to_thread(try_remove, tmp_path, self.logger,
                                                     f"redundant temp for existing {base_name}.json")
            elif bak_path:  # If no .json, but .bak exists, recover from .bak
                self.logger.info(
                    f"[RECOVERY] Found '{os.path.basename(bak_path)}', attempting recovery to '{os.path.basename(target_json_path)}'.")
                try:
                    await asyncio.to_thread(os.rename, bak_path, target_json_path)
                    recovered_or_existing_jsons.append(target_json_path)
                    recovered_files_map[base_name] = target_json_path
                    # Clean up potential leftover .tmp
                    if tmp_path: await asyncio.to_thread(try_remove, tmp_path, self.logger,
                                                         f"redundant temp after recovering {base_name}.json from bak")
                except OSError as e:
                    self.logger.error(f"Failed to recover from {os.path.basename(bak_path)}: {e}")
            elif tmp_path:  # If no .json or .bak, but .tmp exists, recover from .tmp
                self.logger.info(
                    f"[RECOVERY] Found '{os.path.basename(tmp_path)}', attempting recovery to '{os.path.basename(target_json_path)}'.")
                # Optional: Add validation for .tmp file (e.g., check size > 0) before rename
                try:
                    # Example validation (optional):
                    # tmp_size = await asyncio.to_thread(os.path.getsize, tmp_path)
                    # if tmp_size == 0:
                    #    self.logger.warning(f"Temporary file {tmp_path} is empty, removing instead of recovering.")
                    #    await asyncio.to_thread(try_remove, tmp_path, self.logger, "empty temp file")
                    #    continue # Skip to next base name
                    await asyncio.to_thread(os.rename, tmp_path, target_json_path)
                    recovered_or_existing_jsons.append(target_json_path)
                    recovered_files_map[base_name] = target_json_path
                except OSError as e:
                    self.logger.error(f"Failed to recover from {os.path.basename(tmp_path)}: {e}")
            # else: No .json, .bak, or .tmp found for this base name - nothing to do

        self.logger.info(f"Found/Recovered {len(recovered_or_existing_jsons)} total JSON files after recovery phase.")

        # --- Step 3: Apply Filtering Logic based on parameters ---
        candidate_files = []  # This will hold the files selected by the input parameters

        # --- Filtering Logic based on files_to_process and start_from_incomplete ---
        if isinstance(files_to_process, (list, set)):
            # Filter the recovered/existing list based on the specific input list
            self.logger.info(
                f"Filtering found/recovered files based on specific list of {len(files_to_process)} files.")
            requested_files_set = {os.path.normpath(f) for f in files_to_process}
            candidate_files = [f for f in recovered_or_existing_jsons if os.path.normpath(f) in requested_files_set]

            # Check for requested files that weren't found/recovered
            found_set = {os.path.normpath(f) for f in candidate_files}
            missing_requested = requested_files_set - found_set
            if missing_requested:
                self.logger.warning(f"Specified files not found or recovered: {list(missing_requested)}")

            # Note: start_from_incomplete is generally ignored when a specific list is given for selection
            if start_from_incomplete:
                self.logger.warning(
                    "`start_from_incomplete=True` ignored for file *selection* because a specific file list was provided.")

        elif start_from_incomplete:
            # Filter the recovered/existing list for those containing the error key
            self.logger.info("Filtering found/recovered files for incomplete ('processing_error' key).")
            check_tasks = {f: self._check_for_error_key(f) for f in recovered_or_existing_jsons}
            results = await asyncio.gather(*check_tasks.values())
            candidate_files = [f for f, has_error in zip(check_tasks.keys(), results) if has_error]
            self.logger.info(
                f"Found {len(candidate_files)} incomplete files out of {len(recovered_or_existing_jsons)} found/recovered JSONs.")

        else:  # files_to_process is True, False, or None - use all recovered/existing
            self.logger.info("Selecting all standard found/recovered JSON files.")
            candidate_files = recovered_or_existing_jsons

        # --- Final Filtering: Apply skip_files ---
        if not candidate_files:
            self.logger.info("No candidate files found after initial filtering and recovery.")
            return []

        skip_files_set = self.file_handler.get_skip_files()
        if skip_files_set:
            final_files = [f for f in candidate_files if os.path.realpath(f) not in skip_files_set]
            skipped_count = len(candidate_files) - len(final_files)
            if skipped_count > 0:
                self.logger.info(f"Excluded {skipped_count} files based on the skip list.")
        else:
            final_files = candidate_files

        self.logger.info(f"Final selection: Returning {len(final_files)} JSON files for processing.")
        return final_files

    # def batch_process_pdfs(self, json_files: List[str], config: Dict):
    #     """Refactored: Delegate the batch processing (wrapping existing batch_process_json_files)."""
    #     from .pdf_extractor2 import batch_process_json_files
    #     reprocess_md = config.get('reprocess_md', False)
    #     num_workers = config.get('num_workers', 4)
    #     self.logger.info(f"Batch processing {len(json_files)} PDFs with {num_workers} workers")
    #     stats = batch_process_json_files(
    #         json_files,
    #         config,
    #         num_workers=num_workers,
    #         reprocess_md=reprocess_md,
    #         batch_size=10
    #     )
    #     self.logger.info(f"Batch processing complete: {stats['successful']} successful, {stats['failed']} failed")
    #
    # async def batch_process_pdfs_async(self, json_files: List[str], config: Dict):
    #     """Asynchronous version of batch processing PDFs."""
    #     # Configure to use tqdm for progress tracking
    #     config['use_tqdm'] = True
    #
    #     # Import the async batch processor
    #     from .mistral_pdf_processor import batch_process_json_files_async
    #     reprocess_md = config.get('reprocess_md', False)
    #     num_workers = config.get('num_workers', 4)
    #     rate_limit = config.get('api_rate_limit', 2)
    #
    #     self.logger.info(
    #         f"Async batch processing {len(json_files)} PDFs with {num_workers} workers and API rate limit {rate_limit}"
    #     )
    #
    #     # Process files with progress tracking
    #     stats = await batch_process_json_files_async(
    #         json_files,
    #         config,
    #         num_workers=num_workers,
    #         reprocess_md=reprocess_md,
    #         batch_size=10,
    #         rate_limit=rate_limit
    #     )
    #
    #     return stats

    # TODO: Add pdf_extractor as backup.
