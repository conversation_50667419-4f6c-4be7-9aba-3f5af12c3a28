"""
Text and PDF handling for docket processing.

This module handles PDF text extraction and processing extracted from 
docket_processor.py as part of Phase 3.2 refactoring.
"""
import asyncio
import logging
import os
import tempfile
from typing import Dict, Optional, Any

import aiofiles
import requests

from src.utils import try_remove


class DocketTextHandler:
    """Handles PDF text extraction and processing for docket data."""
    
    NULL_CONDITIONS = ['', 'NA', None, 'Pro Se', 'PRO SE', "None", []]
    
    def __init__(self, file_handler: Any, pdf_processor: Optional[Any] = None, 
                 logger: Optional[logging.Logger] = None):
        self.file_handler = file_handler
        self.pdf_processor = pdf_processor
        self.logger = logger or logging.getLogger(__name__)
    
    async def get_pdf_text(self, json_path: str, data: Dict) -> Optional[str]:
        """
        Extract text content from PDF associated with the docket.
        
        Tries multiple sources in order:
        1. Local .md file (if exists)
        2. Local .pdf file (extract text)
        3. S3 PDF download and extract
        
        Args:
            json_path: Path to the JSON file
            data: Docket data dictionary
            
        Returns:
            Extracted text content or None if unavailable
        """
        if not json_path:
            self.logger.warning("No JSON path provided for PDF text extraction")
            return None
        
        base_path = os.path.splitext(json_path)[0]
        filename = data.get('new_filename', os.path.basename(base_path))
        
        # Strategy 1: Check for existing .md file
        md_text = await self._try_load_md_file(base_path, filename)
        if md_text:
            return md_text
        
        # Strategy 2: Check for local PDF and extract
        pdf_text = await self._try_extract_local_pdf(base_path, filename)
        if pdf_text:
            return pdf_text
        
        # Strategy 3: Download from S3 and extract
        s3_text = await self._try_extract_s3_pdf(data, filename)
        if s3_text:
            return s3_text
        
        self.logger.warning(f"No text content could be extracted for {filename}")
        return None

    async def _try_load_md_file(self, base_path: str, filename: str) -> Optional[str]:
        """
        Try to load existing .md file.
        
        Args:
            base_path: Base path without extension
            filename: Filename for logging
            
        Returns:
            MD file content or None
        """
        md_path = f"{base_path}.md"
        
        if not await asyncio.to_thread(os.path.exists, md_path):
            return None
        
        try:
            async with aiofiles.open(md_path, 'r', encoding='utf-8') as f:
                content = await f.read()
            
            if content and len(content.strip()) > 10:
                self.logger.debug(f"Loaded existing .md file for {filename}")
                return content.strip()
            else:
                self.logger.debug(f"MD file exists but is empty/minimal for {filename}")
                return None
                
        except Exception as e:
            self.logger.warning(f"Error reading .md file for {filename}: {e}")
            return None

    async def _try_extract_local_pdf(self, base_path: str, filename: str) -> Optional[str]:
        """
        Try to extract text from local PDF file.
        
        Args:
            base_path: Base path without extension
            filename: Filename for logging
            
        Returns:
            Extracted PDF text or None
        """
        pdf_path = f"{base_path}.pdf"
        
        if not await asyncio.to_thread(os.path.exists, pdf_path):
            self.logger.debug(f"No local PDF found for {filename}")
            return None
        
        try:
            # Use PDF processor if available
            if self.pdf_processor:
                text = await self._extract_with_pdf_processor(pdf_path, filename)
                if text:
                    # Save extracted text to .md file for future use
                    await self._save_extracted_text(f"{base_path}.md", text, filename)
                    return text
            
            # Fallback: Try other extraction methods
            text = await self._extract_with_fallback_methods(pdf_path, filename)
            if text:
                await self._save_extracted_text(f"{base_path}.md", text, filename)
                return text
            
            self.logger.warning(f"Could not extract text from local PDF for {filename}")
            return None
            
        except Exception as e:
            self.logger.error(f"Error extracting text from local PDF for {filename}: {e}")
            return None

    async def _try_extract_s3_pdf(self, data: Dict, filename: str) -> Optional[str]:
        """
        Try to download PDF from S3 and extract text.
        
        Args:
            data: Docket data dictionary containing S3 link
            filename: Filename for logging
            
        Returns:
            Extracted PDF text or None
        """
        s3_link = data.get('s3_link')
        if not s3_link or not isinstance(s3_link, str):
            return None
        
        # Only proceed if S3 link looks like a PDF
        if not s3_link.lower().endswith('.pdf'):
            self.logger.debug(f"S3 link doesn't appear to be a PDF for {filename}: {s3_link}")
            return None
        
        try:
            # Download PDF to temporary file
            temp_pdf_path = await self._download_s3_pdf(s3_link, filename)
            if not temp_pdf_path:
                return None
            
            try:
                # Extract text from downloaded PDF
                if self.pdf_processor:
                    text = await self._extract_with_pdf_processor(temp_pdf_path, filename)
                else:
                    text = await self._extract_with_fallback_methods(temp_pdf_path, filename)
                
                if text:
                    self.logger.info(f"Successfully extracted text from S3 PDF for {filename}")
                    return text
                else:
                    self.logger.warning(f"Could not extract text from downloaded S3 PDF for {filename}")
                    return None
                    
            finally:
                # Clean up temporary file
                await asyncio.to_thread(try_remove, temp_pdf_path)
                
        except Exception as e:
            self.logger.error(f"Error extracting text from S3 PDF for {filename}: {e}")
            return None

    async def _download_s3_pdf(self, s3_url: str, filename: str) -> Optional[str]:
        """
        Download PDF from S3 URL to temporary file.
        
        Args:
            s3_url: S3 URL to download from
            filename: Filename for logging
            
        Returns:
            Path to temporary PDF file or None
        """
        try:
            def download_sync(url: str, dest_path: str) -> bool:
                """Synchronous download function."""
                try:
                    response = requests.get(url, stream=True, timeout=30)
                    response.raise_for_status()
                    
                    with open(dest_path, 'wb') as f:
                        for chunk in response.iter_content(chunk_size=8192):
                            f.write(chunk)
                    
                    return os.path.exists(dest_path) and os.path.getsize(dest_path) > 0
                except Exception as e:
                    self.logger.error(f"Error downloading PDF: {e}")
                    return False
            
            # Create temporary file
            with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as temp_file:
                temp_path = temp_file.name
            
            # Download in thread to avoid blocking
            success = await asyncio.to_thread(download_sync, s3_url, temp_path)
            
            if success:
                self.logger.debug(f"Downloaded S3 PDF for {filename} to {temp_path}")
                return temp_path
            else:
                await asyncio.to_thread(try_remove, temp_path)
                return None
                
        except Exception as e:
            self.logger.error(f"Error downloading S3 PDF for {filename}: {e}")
            return None

    async def _extract_with_pdf_processor(self, pdf_path: str, filename: str) -> Optional[str]:
        """
        Extract text using the configured PDF processor.
        
        Args:
            pdf_path: Path to PDF file
            filename: Filename for logging
            
        Returns:
            Extracted text or None
        """
        try:
            if hasattr(self.pdf_processor, 'extract_text_async'):
                # Use async method if available
                text = await self.pdf_processor.extract_text_async(pdf_path)
            elif hasattr(self.pdf_processor, 'extract_text'):
                # Use sync method in thread
                text = await asyncio.to_thread(self.pdf_processor.extract_text, pdf_path)
            else:
                self.logger.warning(f"PDF processor doesn't have expected methods for {filename}")
                return None
            
            if text and len(text.strip()) > 10:
                self.logger.debug(f"Extracted {len(text)} characters using PDF processor for {filename}")
                return text.strip()
            else:
                self.logger.warning(f"PDF processor returned empty/minimal text for {filename}")
                return None
                
        except Exception as e:
            self.logger.error(f"Error using PDF processor for {filename}: {e}")
            return None

    async def _extract_with_fallback_methods(self, pdf_path: str, filename: str) -> Optional[str]:
        """
        Extract text using fallback methods when PDF processor is unavailable.
        
        Args:
            pdf_path: Path to PDF file
            filename: Filename for logging
            
        Returns:
            Extracted text or None
        """
        # Try PyMuPDF (fitz) if available
        try:
            import fitz  # PyMuPDF
            
            def extract_with_fitz(path: str) -> str:
                doc = fitz.open(path)
                text = ""
                for page in doc:
                    text += page.get_text()
                doc.close()
                return text
            
            text = await asyncio.to_thread(extract_with_fitz, pdf_path)
            if text and len(text.strip()) > 10:
                self.logger.debug(f"Extracted {len(text)} characters using PyMuPDF for {filename}")
                return text.strip()
                
        except ImportError:
            pass
        except Exception as e:
            self.logger.warning(f"Error using PyMuPDF for {filename}: {e}")
        
        # Try pdfplumber if available
        try:
            import pdfplumber
            
            def extract_with_pdfplumber(path: str) -> str:
                text = ""
                with pdfplumber.open(path) as pdf:
                    for page in pdf.pages:
                        page_text = page.extract_text()
                        if page_text:
                            text += page_text + "\n"
                return text
            
            text = await asyncio.to_thread(extract_with_pdfplumber, pdf_path)
            if text and len(text.strip()) > 10:
                self.logger.debug(f"Extracted {len(text)} characters using pdfplumber for {filename}")
                return text.strip()
                
        except ImportError:
            pass
        except Exception as e:
            self.logger.warning(f"Error using pdfplumber for {filename}: {e}")
        
        self.logger.warning(f"No working PDF extraction methods available for {filename}")
        return None

    async def _save_extracted_text(self, md_path: str, text: str, filename: str):
        """
        Save extracted text to .md file for future use.
        
        Args:
            md_path: Path to save MD file
            text: Text content to save
            filename: Filename for logging
        """
        try:
            async with aiofiles.open(md_path, 'w', encoding='utf-8') as f:
                await f.write(text)
            
            self.logger.debug(f"Saved extracted text to {md_path} for {filename}")
            
        except Exception as e:
            self.logger.warning(f"Could not save extracted text for {filename}: {e}")

    def validate_text_quality(self, text: str, filename: str = '') -> Dict[str, Any]:
        """
        Validate the quality of extracted text.
        
        Args:
            text: Extracted text to validate
            filename: Filename for logging context
            
        Returns:
            Validation report with quality metrics
        """
        if not text or not isinstance(text, str):
            return {
                'status': 'error',
                'message': 'No text provided',
                'metrics': {}
            }
        
        text = text.strip()
        
        metrics = {
            'length': len(text),
            'word_count': len(text.split()),
            'line_count': len(text.splitlines()),
            'avg_word_length': sum(len(word) for word in text.split()) / max(len(text.split()), 1)
        }
        
        # Quality assessment
        if metrics['length'] < 10:
            status = 'error'
            message = 'Text too short'
        elif metrics['word_count'] < 5:
            status = 'error' 
            message = 'Too few words'
        elif metrics['avg_word_length'] < 2:
            status = 'warning'
            message = 'Very short average word length - may be garbled'
        elif metrics['length'] > 100000:
            status = 'warning'
            message = 'Very long text - may include excessive metadata'
        else:
            status = 'success'
            message = 'Text quality appears good'
        
        return {
            'status': status,
            'message': message,
            'metrics': metrics
        }