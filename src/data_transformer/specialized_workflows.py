"""
Specialized workflows for DataTransformer.

This module handles specific processing modes and specialized workflows
extracted from transformer.py as part of Phase 3 refactoring.
"""
import asyncio
import os
from typing import Dict, List

from .file_handler import FileHandler


class SpecializedWorkflows:
    """Handles specialized processing workflows."""
    
    def __init__(
        self,
        file_handler: FileHandler,
        docket_processor,
        mdl_processor,
        logger
    ):
        self.file_handler = file_handler
        self.docket_processor = docket_processor
        self.mdl_processor = mdl_processor
        self.logger = logger
    
    async def run_law_firm_normalization_only(self, num_workers: int = 4) -> List[Dict]:
        """
        Run law firm normalization workflow only.
        
        Args:
            num_workers: Number of concurrent workers
            
        Returns:
            List of processing results
        """
        self.logger.info("Running Law Firm Normalization ONLY mode")
        
        # Validate number of workers
        if num_workers <= 0:
            self.logger.warning(f"num_workers was not set, defaulting to {num_workers} for normalization mode.")
            num_workers = 4
        
        # Get target directory and files
        target_docket_dir = self.file_handler.get_target_docket_directory()
        if not target_docket_dir:
            self.logger.error("Target docket directory not found. Cannot run normalization.")
            return []
        
        try:
            json_files = [
                os.path.join(target_docket_dir, f) 
                for f in await asyncio.to_thread(os.listdir, target_docket_dir)
                if f.endswith('.json')
            ]
        except OSError as e:
            self.logger.error(f"Error listing files in {target_docket_dir}: {e}")
            return []
        
        if not json_files:
            self.logger.warning("No JSON files found for law firm normalization.")
            return []
        
        self.logger.info(f"Found {len(json_files)} JSON files for law firm normalization.")
        
        async def normalize_single_file_law_firms(json_path: str) -> Dict:
            """Normalize law firms for a single file."""
            base_filename = os.path.splitext(os.path.basename(json_path))[0]
            
            try:
                # Load data
                data = self.file_handler.load_json(json_path)
                if not data:
                    return {
                        'filename': base_filename,
                        'status': 'error',
                        'message': 'Failed to load JSON data'
                    }
                
                # Run law firm processing
                if self.docket_processor:
                    await self.docket_processor.process_law_firms(data)
                    
                    # Save updated data
                    self.file_handler.save_json(json_path, data)
                    
                    return {
                        'filename': base_filename,
                        'status': 'success',
                        'message': 'Law firm normalization completed'
                    }
                else:
                    return {
                        'filename': base_filename,
                        'status': 'error',
                        'message': 'DocketProcessor not available'
                    }
                    
            except Exception as e:
                self.logger.error(f"Error normalizing law firms for {base_filename}: {e}")
                return {
                    'filename': base_filename,
                    'status': 'error',
                    'message': str(e)
                }
        
        # Process files with semaphore for concurrency control
        semaphore = asyncio.Semaphore(num_workers)
        
        async def process_with_semaphore(json_path: str) -> Dict:
            async with semaphore:
                return await normalize_single_file_law_firms(json_path)
        
        # Run processing
        self.logger.info(f"Starting law firm normalization with {num_workers} workers...")
        tasks = [process_with_semaphore(json_path) for json_path in json_files]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Process results
        processed_results = []
        success_count = 0
        error_count = 0
        
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                error_count += 1
                processed_results.append({
                    'filename': os.path.basename(json_files[i]),
                    'status': 'error',
                    'message': str(result)
                })
            elif isinstance(result, dict):
                processed_results.append(result)
                if result.get('status') == 'success':
                    success_count += 1
                else:
                    error_count += 1
        
        self.logger.info(
            f"Law firm normalization completed: {success_count} success, {error_count} errors"
        )
        
        return processed_results
    
    async def run_mdl_title_update_only(self, num_workers: int = 4) -> List[Dict]:
        """
        Run MDL title update workflow only.
        
        Args:
            num_workers: Number of concurrent workers
            
        Returns:
            List of processing results
        """
        self.logger.info("Running MDL Title Update ONLY mode")
        
        # Validate number of workers
        if num_workers <= 0:
            self.logger.warning(f"num_workers was not set, defaulting to {num_workers} for MDL update mode.")
            num_workers = 4
        
        # Get target directory and files
        target_docket_dir = self.file_handler.get_target_docket_directory()
        if not target_docket_dir:
            self.logger.error("Target docket directory not found. Cannot run MDL title update.")
            return []
        
        try:
            json_files = [
                os.path.join(target_docket_dir, f) 
                for f in await asyncio.to_thread(os.listdir, target_docket_dir)
                if f.endswith('.json')
            ]
        except OSError as e:
            self.logger.error(f"Error listing files in {target_docket_dir}: {e}")
            return []
        
        if not json_files:
            self.logger.warning("No JSON files found for MDL title update.")
            return []
        
        self.logger.info(f"Found {len(json_files)} JSON files for MDL title update.")
        
        async def update_single_file_mdl_title(json_path: str) -> Dict:
            """Update MDL title for a single file."""
            base_filename = os.path.splitext(os.path.basename(json_path))[0]
            
            try:
                # Load data
                data = self.file_handler.load_json(json_path)
                if not data:
                    return {
                        'filename': base_filename,
                        'status': 'error',
                        'message': 'Failed to load JSON data'
                    }
                
                # Run MDL processing
                if self.mdl_processor:
                    original_title = data.get('title')
                    original_allegations = data.get('allegations')
                    
                    # Add MDL info (includes title and allegations from lookup)
                    await self.mdl_processor.add_mdl_info_async(data)
                    
                    # Check if anything was updated
                    title_updated = data.get('title') != original_title
                    allegations_updated = data.get('allegations') != original_allegations
                    
                    if title_updated or allegations_updated:
                        # Save updated data
                        self.file_handler.save_json(json_path, data)
                        
                        update_details = []
                        if title_updated:
                            update_details.append('title')
                        if allegations_updated:
                            update_details.append('allegations')
                        
                        return {
                            'filename': base_filename,
                            'status': 'success',
                            'message': f'Updated: {", ".join(update_details)}',
                            'mdl_num': data.get('mdl_num'),
                            'title_updated': title_updated,
                            'allegations_updated': allegations_updated
                        }
                    else:
                        return {
                            'filename': base_filename,
                            'status': 'no_change',
                            'message': 'No MDL updates needed',
                            'mdl_num': data.get('mdl_num')
                        }
                else:
                    return {
                        'filename': base_filename,
                        'status': 'error',
                        'message': 'MDLProcessor not available'
                    }
                    
            except Exception as e:
                self.logger.error(f"Error updating MDL title for {base_filename}: {e}")
                return {
                    'filename': base_filename,
                    'status': 'error',
                    'message': str(e)
                }
        
        # Process files with semaphore for concurrency control
        semaphore = asyncio.Semaphore(num_workers)
        
        async def process_with_semaphore(json_path: str) -> Dict:
            async with semaphore:
                return await update_single_file_mdl_title(json_path)
        
        # Run processing
        self.logger.info(f"Starting MDL title update with {num_workers} workers...")
        tasks = [process_with_semaphore(json_path) for json_path in json_files]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Process results
        processed_results = []
        success_count = 0
        no_change_count = 0
        error_count = 0
        
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                error_count += 1
                processed_results.append({
                    'filename': os.path.basename(json_files[i]),
                    'status': 'error',
                    'message': str(result)
                })
            elif isinstance(result, dict):
                processed_results.append(result)
                status = result.get('status')
                if status == 'success':
                    success_count += 1
                elif status == 'no_change':
                    no_change_count += 1
                else:
                    error_count += 1
        
        self.logger.info(
            f"MDL title update completed: {success_count} updated, "
            f"{no_change_count} no changes, {error_count} errors"
        )
        
        return processed_results
    
    async def run_custom_workflow(self, workflow_name: str, **kwargs) -> List[Dict]:
        """
        Run a custom workflow by name.
        
        Args:
            workflow_name: Name of the workflow to run
            **kwargs: Additional arguments for the workflow
            
        Returns:
            List of processing results
        """
        self.logger.info(f"Running custom workflow: {workflow_name}")
        
        # Map workflow names to methods
        workflow_map = {
            'law_firm_normalization': self.run_law_firm_normalization_only,
            'mdl_title_update': self.run_mdl_title_update_only
        }
        
        if workflow_name not in workflow_map:
            self.logger.error(f"Unknown workflow: {workflow_name}")
            return []
        
        workflow_method = workflow_map[workflow_name]
        
        try:
            return await workflow_method(**kwargs)
        except Exception as e:
            self.logger.error(f"Error running workflow {workflow_name}: {e}")
            return []