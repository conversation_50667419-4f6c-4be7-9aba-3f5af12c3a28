import ast
import logging
from typing import Dict, Optional, Tuple, Any, List  # <--- ADDED Any and List here


class DocketValidator:
    """Validates docket data dictionary."""

    # Define fields that MUST exist and be non-empty/non-null for a record to be considered complete.
    # Adjust this list based on your absolute minimum requirements.
    CORE_REQUIRED_FIELDS = [
        'court_id',
        'docket_num',
        'date_filed',
        'plaintiff',
        'defendant',
        'new_filename',
        'attorneys_gpt',
        'plaintiffs_gpt',
        's3_link',
        'title',  # Usually essential
        'versus',  # Usually essential (or title fallback)
        # Add other fields that are CRITICAL for your downstream use
    ]

    # --- ADDED: Date validation requirement ---
    # Field that must exist and be a valid YYYYMMDD string
    REQUIRED_DATE_FIELDS = ['added_on']
    COMPLETION_MARKER_FIELD = 'added_on'

    def __init__(self):
        self.logger = logging.getLogger(__name__)  # Use __name__ for logger
        # Ensure proper logging configuration for data_transformer modules
        self.logger.handlers = []
        self.logger.propagate = True
        self.NULL_CONDITIONS = ['', 'NA', None, 'Pro Se', 'PRO SE', "None", []]  # Match FileHandler

    def validate_docket(self, data: Dict) -> bool:
        """
        DEPRECATED or simplified usage.
        This method previously just logged incompleteness but returned True.
        The primary logic is now in is_data_complete.
        If called, it should perhaps reflect the outcome of is_data_complete.
        """
        if not isinstance(data, dict):
            self.logger.error("Invalid data format: Expected dict for validation.")
            return False
        # log_filename = data.get('new_filename', data.get('docket_num', 'NA'))

        is_complete, _ = self.is_data_complete(data)
        # Maybe log if needed, but return the actual completeness status
        # self.logger.debug(f"validate_docket called for {log_filename}. Completeness: {is_complete}")
        return is_complete  # Return the result of the check

    @staticmethod
    def _is_valid_date_format(date_str: Any) -> bool:  # Now 'Any' is defined
        """Checks if a string is a valid YYYYMMDD date."""
        if not isinstance(date_str, str):
            return False
        if len(date_str) != 8:
            return False
        if not date_str.isdigit():
            return False
        # Basic check, doesn't validate month/day ranges, but ensures format
        return True

    def is_data_complete(self, data: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """
        Checks if the core required fields AND the completion marker field are present and valid.
        This determines if a file has been fully processed successfully in a previous run.

        Args:
            data: The docket data dictionary.

        Returns:
            A tuple: (bool indicating completeness, list of missing/invalid fields).
        """
        if not isinstance(data, dict):
            self.logger.warning("Validation check received non-dict data.")
            return False, ['Invalid data type']

        missing_or_invalid = []

        # 1. Check CORE_REQUIRED_FIELDS
        for field in self.CORE_REQUIRED_FIELDS:
            value = data.get(field)
            if value in self.NULL_CONDITIONS or value is None:  # Check presence and validity
                missing_or_invalid.append(field)
            # Add more specific checks if needed (e.g., date_filed format, s3_link structure)
            elif field == 's3_link' and (not isinstance(value, str) or not value.lower().endswith('.pdf')):
                missing_or_invalid.append(f"{field} (invalid format)")

        # 2. Check COMPLETION_MARKER_FIELD ('added_on')
        completion_marker_value = data.get(self.COMPLETION_MARKER_FIELD)
        if not self._is_valid_completion_date(completion_marker_value):
            missing_or_invalid.append(f"{self.COMPLETION_MARKER_FIELD} (missing or invalid format)")

        # 3. Check for Explicit Error Marker
        if 'processing_error' in data:
            # Technically, an errored file isn't "complete" in the desired sense
            # but we handle this separately in the calling logic (_load_and_validate).
            # We don't add it to missing_or_invalid here, as its presence signifies
            # *why* it might be missing required fields.
            pass

        is_complete = not missing_or_invalid
        # Logging is handled in the calling methods based on the return value

        return is_complete, missing_or_invalid

    @staticmethod
    def _is_s3_html_valid(s3_html: Optional[str]) -> bool:
        """Check if s3_html looks like an HTML link."""
        return isinstance(s3_html, str) and s3_html.lower().endswith('.html')  # Check lower case

    def _convert_strings_to_literals(self, input_data):
        """Recursive literal evaluation (use with caution)."""
        # Duplicates TransferHandler method - consider moving to utils
        if isinstance(input_data, str):
            if (input_data.startswith('[') and input_data.endswith(']')) or \
                    (input_data.startswith('{') and input_data.endswith('}')):
                try:
                    return ast.literal_eval(input_data)
                except (ValueError, SyntaxError, TypeError):
                    # self.logger.debug(f"ast.literal_eval failed: '{input_data[:100]}...'") # Reduce verbosity
                    return input_data
            else:
                return input_data
        elif isinstance(input_data, list):
            return [self._convert_strings_to_literals(item) for item in input_data]
        elif isinstance(input_data, dict):
            return {key: self._convert_strings_to_literals(value) for key, value in input_data.items()}
        else:
            return input_data

    @staticmethod
    def _is_valid_completion_date(date_str: Any) -> bool:
        """Checks if the 'added_on' value is a valid YYYYMMDD string."""
        if not isinstance(date_str, str):
            return False
        if len(date_str) != 8:
            return False
        if not date_str.isdigit():
            return False
        # Basic format check, doesn't validate month/day ranges
        try:
            # Optional: More robust check if needed
            # from datetime import datetime
            # datetime.strptime(date_str, '%Y%m%d')
            pass
        except ValueError:
            return False  # Invalid date components
        return True
