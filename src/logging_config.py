import logging
import os
import colorlog
import warnings

class DebugFilter(logging.Filter):
    def filter(self, record):
        # Allow DEBUG messages only from loggers in the allowed namespaces
        allowed_prefixes = ("src", "lib")
        if record.levelno == logging.DEBUG and not record.name.startswith(allowed_prefixes):
            return False
        return True
warnings.filterwarnings("ignore", category=RuntimeWarning)


def configure_logger(config):
    # Configure log directory and file based on configuration values
    iso_date = config.get('iso_date', 'default')
    log_dir = config.get('directories', {}).get(
        'log_dir',
        os.path.join(config.get('DATA_DIR', os.path.join(os.getcwd(), 'data')), f"{iso_date}/logs")
    )
    os.makedirs(log_dir, exist_ok=True)
    log_file = os.path.join(log_dir, 'app.log')
    data_transformer_log_file = os.path.join(log_dir, 'data_transformer.log')

    # Standard formatter including module and function information
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(module)s.%(funcName)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

    # Colored console formatter
    console_formatter = colorlog.ColoredFormatter(
        "%(asctime)s %(log_color)s%(levelname)-8s%(reset)s %(blue)s%(module)s.%(funcName)s - %(message)s",
        datefmt='%Y-%m-%d %H:%M:%S',
        log_colors={
            'DEBUG': 'cyan',
            'INFO': 'green',
            'WARNING': 'yellow',
            'ERROR': 'red',
            'CRITICAL': 'red,bg_white',
        }
    )

    # Get the root logger and clear any existing handlers, and set to WARNING
    root_logger = logging.getLogger()
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    root_logger.setLevel(logging.WARNING)

    # Configure the 'src' namespace logger to capture debug messages from our modules
    own_logger = logging.getLogger("src")
    own_logger.setLevel(logging.DEBUG)
    own_logger.propagate = False  # Prevent propagation to root logger

    # File handler setup for our own loggers (general src namespace)
    file_handler = logging.FileHandler(log_file)
    file_handler.setLevel(logging.DEBUG)
    file_handler.setFormatter(formatter)
    own_logger.addHandler(file_handler)
    file_handler.addFilter(DebugFilter())

    # Console handler setup for our own loggers
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.DEBUG)
    console_handler.setFormatter(console_formatter)
    own_logger.addHandler(console_handler)
    console_handler.addFilter(DebugFilter())

    # SPECIFIC SETUP FOR DATA_TRANSFORMER MODULES
    data_transformer_logger = logging.getLogger("src.data_transformer")
    data_transformer_logger.setLevel(logging.DEBUG)
    data_transformer_logger.propagate = False  # Don't propagate to parent to avoid duplicate logs
    
    # Data transformer specific file handler
    dt_file_handler = logging.FileHandler(data_transformer_log_file)
    dt_file_handler.setLevel(logging.DEBUG)
    dt_file_handler.setFormatter(formatter)
    data_transformer_logger.addHandler(dt_file_handler)
    
    # Data transformer console handler
    dt_console_handler = logging.StreamHandler()
    dt_console_handler.setLevel(logging.DEBUG)
    dt_console_handler.setFormatter(console_formatter)
    data_transformer_logger.addHandler(dt_console_handler)

    # Set external libraries to a higher log level - be more aggressive
    for lib in ['botocore', 'boto3', 'urllib3', 'botocore.auth', 'botocore.endpoint', 'botocore.regions', 'botocore.action']:
        logging.getLogger(lib).setLevel(logging.ERROR)
    
    # Also disable specific AWS loggers that are being noisy
    logging.getLogger('botocore.credentials').setLevel(logging.ERROR)
    logging.getLogger('botocore.httpsession').setLevel(logging.ERROR)
    logging.getLogger('botocore.awsrequest').setLevel(logging.ERROR)
    
    # Ensure our own loggers under the "src" namespace remain at DEBUG level
    logging.getLogger("src").setLevel(logging.DEBUG)
    
    print(f"Logging configured: data_transformer logs -> {data_transformer_log_file}")
