#!/usr/bin/env python3

import argparse  # Added argparse
import asyncio
import csv
import glob
import logging

# KILL ALL BOTO3/AWS LOGGING IMMEDIATELY AND COMPLETELY
logging.basicConfig(level=logging.WARNING)
for logger_name in ['boto3', 'botocore', 'botocore.auth', 'botocore.endpoint', 'botocore.regions', 'botocore.action', 'botocore.credentials', 'botocore.httpsession', 'botocore.awsrequest', 'botocore.parsers', 'botocore.retryhandler', 'urllib3', 'aioboto3', 'aiobotocore']:
    logger = logging.getLogger(logger_name)
    logger.setLevel(logging.CRITICAL)
    logger.disabled = True
    logger.propagate = False

# Now it's safe to import multiprocessing
import multiprocessing
import os
# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    logging.warning("python-dotenv not available, environment variables from .env file will not be loaded")
import subprocess
# Import our enhanced date utilities
import sys
import threading
import time as time_module  # Rename to avoid conflict
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Set, Any  # Added Any

sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from src.utils.date import DateUtils, FORMAT_ISO, FORMAT_US_SHORT

import psutil
import yaml  # Added yaml
from playwright.async_api import async_playwright
from rich.console import Console
from rich.logging import RichHandler

_csv_lock = threading.Lock()

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))  # Add parent directory to path

# Import cleanup library
from src.utils.cleanup_utils import cleanup_everything, patch_resource_tracker
from src.utils.resource_cleanup_utils import register_cleanup
from src.config_models.loader import load_config as load_config_new

# Create compatibility function for old load_config signature
def load_config(end_date=None, start_date=None, testing=False, use_proxy=True, headless=True, **kwargs):
    """Compatibility wrapper for old load_config signature."""
    # Create a params dict from the old signature
    params = {
        'date': end_date,
        'start_date': start_date,
        'testing': testing,
        'use_proxy': use_proxy,
        'headless': headless
    }
    params.update(kwargs)
    
    # For now, return a dict that mimics the old config structure
    # This is a transitional solution during the refactoring
    project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    data_dir = os.environ.get('LEXGENIUS_DATA_DIR', os.path.join(project_root, 'data'))
    
    config = {
        'date': end_date,
        'start_date': start_date,
        'iso_date': DateUtils.date_to_iso(end_date) if end_date else None,
        'testing': testing,
        'use_proxy': use_proxy,
        'headless': headless,
        'llm_provider': 'deepseek',
        'DATA_DIR': data_dir,
        'data_path': data_dir,
        'log_dir': 'logs',
        'project_root': project_root,
        'bucket_name': os.environ.get('S3_BUCKET_NAME', 'lexgenius'),
        # Fix environment variable names to match .env file
        'aws_access_key': os.environ.get('AWS_ACCESS_KEY', os.environ.get('AWS_ACCESS_KEY_ID', '')),
        'aws_secret_key': os.environ.get('AWS_SECRET_KEY', os.environ.get('AWS_SECRET_ACCESS_KEY', '')),
        'aws_region': (
            os.environ.get('AWS_REGION') or 
            os.environ.get('LEXGENIUS_AWS_REGION') or
            os.environ.get('REGION_NAME') or
            'us-west-2'
        ),
        # Add missing CloudFront distribution ID
        'cloudfront_distribution_id': os.environ.get('CLOUDFRONT_DISTRIBUTION_ID', ''),
        # Add OpenAI API key
        'openai_api_key': os.environ.get('OPENAI_API_KEY', '').strip().strip('"').strip("'"),
        # Add PACER credentials
        'username_prod': os.environ.get('PACER_USERNAME', ''),
        'password_prod': os.environ.get('PACER_PASSWORD', ''),
        # Check if PACER credentials are missing and add warning
        'pacer_warning': 'PACER_USERNAME and PACER_PASSWORD environment variables are required for scraping' if not os.environ.get('PACER_USERNAME') else None,
        'directories': {
            'base_dir': project_root,
            'data': data_dir
        }
    }
    config.update(kwargs)
    return config

# Try to import FacebookAdsOrchestrator, but provide a fallback if it fails
try:
    from fb_ads import FacebookAdsOrchestrator
except ImportError:
    FacebookAdsOrchestrator = None
    logging.warning("FacebookAdsOrchestrator not found or failed to import. FB Ads functionality disabled.")

# Import from the refactored pacer client module
# Assuming the refactored file is named pacer_client_refactored.py
# and is accessible via src.lib.pacer.client
# Adjust the import path if necessary based on your project structure
try:
    # Import only what we need directly from the modules
    from src.pacer.orchestrator import (
        PacerOrchestrator,
        process_single_docket_standalone
    )
except ImportError as e:
    logging.error(f"Failed to import Pacer components from src.lib.pacer.orchestrator: {e}")
    # Create dummy implementations
    print(f"Warning: Using dummy implementations for PacerOrchestrator due to import error: {e}")


    def _get_processed_logs_path(config: Dict, logger_for_path: Optional[logging.Logger] = None) -> Optional[Path]:
        """
        Constructs the full path to the processed_courts.log file.
        Returns None if essential config keys (DATA_DIR, iso_date) are missing or invalid.
        """
        current_logger = logger_for_path if logger_for_path else logging.getLogger(
            __name__ + "._get_processed_logs_path")

        data_dir_str = config.get("DATA_DIR")
        iso_date = config.get("iso_date")

        current_logger.debug(
            f"Inside _get_processed_logs_path: Initial DATA_DIR from config: '{data_dir_str}', iso_date: '{iso_date}'")

        if not data_dir_str:
            current_logger.error(
                "CRITICAL: 'DATA_DIR' is missing in the config for _get_processed_logs_path. Cannot determine log path.")
            return None

        data_dir = Path(data_dir_str)
        if not data_dir.is_absolute():
            current_logger.warning(
                f"DATA_DIR '{data_dir_str}' is not absolute. Resolving it relative to CWD '{Path.cwd()}'.")
            data_dir = data_dir.resolve()
            current_logger.info(f"Resolved DATA_DIR to absolute path: '{data_dir}'")

        if not iso_date:  # Handles None or empty string
            current_logger.error(
                "CRITICAL: 'iso_date' is missing or empty in the config for _get_processed_logs_path. Cannot determine log path.")
            # Fallback to a generic name or error out, depending on desired behavior.
            # For now, let's prevent file creation in an unexpected place by returning None.
            return None

        # Validate iso_date format (simple check for YYYYMMDD)
        if not (isinstance(iso_date, str) and len(iso_date) == 8 and iso_date.isdigit()):
            current_logger.error(
                f"CRITICAL: 'iso_date' ('{iso_date}') in config is not in YYYYMMDD format. Cannot determine log path.")
            return None

        try:
            # Construct the path
            log_path = data_dir / iso_date / "logs" / "processed_courts.log"
            current_logger.debug(f"_get_processed_logs_path: Constructed log_path: {log_path}")
            return log_path
        except TypeError as e:
            current_logger.error(
                f"CRITICAL: TypeError during path construction in _get_processed_logs_path (likely due to invalid iso_date or DATA_DIR type). Error: {e}. Config snapshot: DATA_DIR='{data_dir_str}', iso_date='{iso_date}'")
            return None
        except Exception as e_path:  # Catch any other unexpected error during Path object creation
            current_logger.error(
                f"CRITICAL: Unexpected error constructing Path object in _get_processed_logs_path. Error: {e_path}. Config snapshot: DATA_DIR='{data_dir_str}', iso_date='{iso_date}'",
                exc_info=True)
            return None


    def _append_processed_log_entry(config: Dict, court_stats: Dict[str, Any], logger: logging.Logger) -> None:
        """
        Appends a single court's processing statistics to the processed_courts.log file.
        Manages header creation if the file does not exist.
        """
        logger.info(f"--- _append_processed_log_entry called for CourtID: {court_stats.get('CourtID')} ---")
        logger.debug(
            f"Config passed to _append_processed_log_entry: DATA_DIR='{config.get('DATA_DIR')}', iso_date='{config.get('iso_date')}'")
        logger.debug(f"Court stats: {court_stats}")

        header = [
            "CourtID", "RowsAttempted", "RowsSuccessful", "RowsDownloaded",
            "RowsSkippedDate", "RowsSkippedExistsDbGsi", "RowsSkippedExistsLocalDateRange",
            "RowsSkippedIrrelevantLocalJsonToday", "RowsSkippedDownloadedLocalJsonToday",
            "RowsSkippedReportDataIrrelevant", "RowsSkippedOtherRelevance",
            "RowsSkippedHistoricalIrrelevant", "RowsSkippedNosDefendantCheck",
            "RowsSkippedMdCase", "RowsFailed",
            "RowsFailedExtractElements", "RowsFailedGetDocketNum"
        ]
        key_mapping = {
            "CourtID": "CourtID", "attempted": "RowsAttempted", "successful": "RowsSuccessful",
            "downloaded": "RowsDownloaded", "skipped_date": "RowsSkippedDate",
            "skipped_exists_db_gsi": "RowsSkippedExistsDbGsi",
            "skipped_exists_local_date_range": "RowsSkippedExistsLocalDateRange",
            "skipped_review_local_json_today": "RowsSkippedIrrelevantLocalJsonToday",
            "skipped_downloaded_local_json_today": "RowsSkippedDownloadedLocalJsonToday",
            "skipped_report_data_review": "RowsSkippedReportDataIrrelevant",
            "skipped_other_relevance": "RowsSkippedOtherRelevance",
            "skipped_historical_review": "RowsSkippedHistoricalIrrelevant",
            "skipped_nos_defendant_check": "RowsSkippedNosDefendantCheck",
            "skipped_md_case": "RowsSkippedMdCase", "failed": "RowsFailed",
            "failed_extract_elements": "RowsFailedExtractElements",
            "failed_get_docket_num": "RowsFailedGetDocketNum"
        }

        formatted_stats = {csv_key: court_stats.get(orc_key, 0) for orc_key, csv_key in key_mapping.items()}
        if "CourtID" not in formatted_stats or not formatted_stats["CourtID"]:  # Ensure CourtID is present
            formatted_stats["CourtID"] = court_stats.get("CourtID", "UNKNOWN_COURT_ID_IN_FORMAT")

        log_path = _get_processed_logs_path(config, logger)  # Pass logger for context
        if not log_path:
            logger.error("CRITICAL: Failed to get processed_courts.log path. Cannot append entry.")
            return

        logger.info(f"Attempting to write processed_courts.log entry to: {log_path}")

        try:
            log_path.parent.mkdir(parents=True, exist_ok=True)
            logger.debug(f"Ensured parent directory exists: {log_path.parent}")
        except Exception as e_mkdir:
            logger.error(
                f"CRITICAL: Failed to create parent directory {log_path.parent} for processed_courts.log: {e_mkdir}",
                exc_info=True)
            return

        with _csv_lock:
            file_exists = log_path.exists()
            logger.debug(f"File {log_path} exists: {file_exists}")
            try:
                with open(log_path, 'a', newline='', encoding='utf-8') as f:
                    writer = csv.DictWriter(f, fieldnames=header)
                    if not file_exists:
                        writer.writeheader()
                        logger.info(f"Created new processed_courts.log with header at {log_path}")

                    logger.info(f"Writing row to processed_courts.log: {formatted_stats}")
                    writer.writerow(formatted_stats)
                    logger.info(f"Appended processed log for {formatted_stats.get('CourtID', 'N/A')}")
            except IOError as e:
                logger.error(f"IOError appending to processed_courts.log at {log_path}: {e}", exc_info=True)
            except ValueError as e_val:
                logger.error(
                    f"ValueError writing CSV row to {log_path} (likely field mismatch): {e_val}. Row data: {formatted_stats}",
                    exc_info=True)
            except Exception as e_write:
                logger.error(f"Unexpected error writing to processed_courts.log at {log_path}: {e_write}",
                             exc_info=True)


    class PacerOrchestrator:
        def __init__(self, *args, **kwargs):
            pass

        async def process_courts(self, *args, **kwargs):
            logging.error("Dummy PacerOrchestrator.process_courts called - real implementation not available")
            return []


    async def process_single_docket_standalone(*args, **kwargs):
        logging.error("Dummy process_single_docket_standalone called - real implementation not available")
        return {}

# Import the updated DataTransformer
from src.reports import ReportsOrchestrator
from src.infrastructure.storage.s3_async import S3AsyncStorage
from src.data_transformer import DataTransformer, Uploader, FileHandler
# Remove duplicate import that's causing namespace collision
# from src.lib.pacer import PacerOrchestrator, get_court_ids_async
import json
from pathlib import Path

# --- END OF IMPORTS ---

# Initialize project paths based on current file location (don't rely on config yet)
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))  # Go up from src/ to project root
from src.pacer.config import DISTRICT_COURTS_PATH

# Initialize Rich console
console = Console()


# main.py

async def get_court_ids_async() -> List[str]:
    """
    Asynchronously loads court IDs from the specified JSON file, cleans them
    to the expected format (e.g., 'cacd', 'nysd'), and returns a unique, sorted list.
    Handles variations like 'cacd1' or 'cacdc'.
    """
    logger = logging.getLogger(__name__ + ".get_court_ids_async")
    courts_file = Path(DISTRICT_COURTS_PATH)

    try:
        logger.debug(f"Attempting to read district courts file: {courts_file}")

        if not await asyncio.to_thread(courts_file.exists):
            logger.error(f"District courts file not found at {courts_file}")
            raise FileNotFoundError(f"District courts file not found at {courts_file}")

        # Read file content asynchronously using asyncio.to_thread
        def read_sync():
            with open(courts_file, 'r') as f:
                return f.read()

        file_content = await asyncio.to_thread(read_sync)

        # Parse JSON (CPU-bound, okay in async context)
        district_courts_data = json.loads(file_content)
        if not isinstance(district_courts_data, list):
            logger.error(f"Invalid format in district courts file: Expected a list, got {type(district_courts_data)}.")
            raise ValueError("Invalid format in district courts file: Expected a list.")

        # Extract and clean IDs robustly
        ids = set()  # Use a set for automatic deduplication
        for court in district_courts_data:
            court_id_raw = court.get('court_id')

            if isinstance(court_id_raw, str) and court_id_raw.strip():
                cleaned_id = court_id_raw.strip().lower()  # Work with lowercase

                # Remove trailing digit if present
                if cleaned_id and cleaned_id[-1].isdigit():
                    cleaned_id = cleaned_id[:-1]

                # Remove trailing 'c' if present (handles cases like 'akdc')
                if cleaned_id and cleaned_id.endswith('c'):
                    cleaned_id = cleaned_id[:-1]

                # Final check: Ensure it's not empty and looks like a court ID (e.g., at least 3 chars, common pattern)
                # Basic check - can be refined if needed
                if cleaned_id and len(cleaned_id) >= 3 and cleaned_id.isalnum():
                    ids.add(cleaned_id)
                    # logger.debug(f"Extracted and cleaned court ID: {court_id_raw} -> {cleaned_id}") # Keep debug noise low maybe
                else:
                    logger.debug(
                        f"Skipping invalid or empty court_id after cleaning: '{court_id_raw}' -> '{cleaned_id}'")
            else:
                logger.debug(f"Skipping invalid or non-string court_id entry: {court_id_raw}")

        unique_ids_list = sorted(list(ids))  # Convert set to sorted list
        logger.info(f"Loaded {len(unique_ids_list)} unique court IDs.")  # This should now be > 0
        if not unique_ids_list:
            logger.warning("get_court_ids_async: Loaded 0 unique court IDs after filtering/cleaning.")
        # +++ Add logging to see the actual list being returned +++
        if unique_ids_list:
            logger.debug(f"Returning court IDs (first 10): {unique_ids_list[:10]}")
        # +++ End logging +++
        return unique_ids_list

    except FileNotFoundError as e:
        logger.error(f"Error loading court IDs: {e}")
        raise  # Re-raise specific error
    except json.JSONDecodeError as e:
        logger.error(f"Error decoding JSON from district courts file {courts_file}: {e}")
        raise ValueError(f"Error decoding JSON from {courts_file}") from e
    except Exception as e:
        logger.error(f"Unexpected error loading court IDs: {e}", exc_info=True)
        raise RuntimeError("Unexpected error loading court IDs") from e


def setup_rich_logging(config):
    """Configure Rich logging with both console and file output"""
    log_dir = config.get('directories', {}).get(
        'log_dir',
        os.path.join(config.get('DATA_DIR', os.path.join(os.getcwd(), 'data')), f"{config['iso_date']}/logs")
    )
    os.makedirs(log_dir, exist_ok=True)
    log_file = os.path.join(log_dir, 'app.log')

    # Create a standard formatter for file logging
    file_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(module)s.%(funcName)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

    # Remove any existing handlers from root logger
    root_logger = logging.getLogger()
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    root_logger.setLevel(logging.DEBUG)

    # File handler
    file_handler = logging.FileHandler(log_file)
    file_handler.setFormatter(file_formatter)
    file_handler.setLevel(logging.DEBUG)
    root_logger.addHandler(file_handler)

    # Use Rich console handler
    console_handler = RichHandler(
        rich_tracebacks=True,
        console=console,
        show_time=True,
        show_path=True
    )
    # Respect LEXGENIUS_LOG_LEVEL environment variable
    log_level = getattr(logging, os.environ.get('LEXGENIUS_LOG_LEVEL', 'DEBUG').upper(), logging.DEBUG)
    console_handler.setLevel(log_level)
    root_logger.addHandler(console_handler)

    # Set external libraries to ERROR level to eliminate debug noise
    for lib_name in ['botocore', 'boto3', 'urllib3', 'playwright', 'botocore.auth', 'botocore.endpoint', 'botocore.regions', 'botocore.action']:
        logging.getLogger(lib_name).setLevel(logging.ERROR)
    
    # Disable specific noisy AWS loggers
    for aws_logger in ['botocore.credentials', 'botocore.httpsession', 'botocore.awsrequest', 'botocore.parsers', 'botocore.retryhandler']:
        logging.getLogger(aws_logger).setLevel(logging.ERROR)

    # Configure separate log files for specific components
    components_to_log_separately = {
        "pacer": "src.pacer",
        "reports": "src.reports",
        "data_transformer": "src.data_transformer",
        "logging_config_module": "src.logging_config"
    }

    for component_file_basename, logger_name in components_to_log_separately.items():
        component_log_file_path = os.path.join(log_dir, f"{component_file_basename}.log")

        component_file_handler = logging.FileHandler(component_log_file_path)
        component_file_handler.setFormatter(file_formatter)
        component_file_handler.setLevel(logging.DEBUG)

        component_logger = logging.getLogger(logger_name)
        for handler in component_logger.handlers[:]:  # Clear existing handlers
            component_logger.removeHandler(handler)
        component_logger.addHandler(component_file_handler)
        component_logger.setLevel(logging.DEBUG)  # Ensure it's set to DEBUG

        if logger_name in ["src.pacer", "src.data_transformer"]:  # MODIFICATION HERE
            component_logger.propagate = True
            logging.info(
                f"Log propagation ENABLED for '{logger_name}' to root (console). Check both console and {component_log_file_path}")
        else:
            component_logger.propagate = False

        logging.info(f"Configured separate log file for '{logger_name}' at: {component_log_file_path}")


def monitor_process():
    while True:
        process = psutil.Process()
        print(f"CPU Usage: {process.cpu_percent()}%")
        print(f"Memory Usage: {process.memory_info().rss / 1024 / 1024:.2f} MB")
        time_module.sleep(5)


class DateProcessor:
    """
    Processes files for specific dates, utilizing enhanced date utilities.
    This class handles date range generation, file discovery, and processing orchestration.
    """

    def __init__(self, params: Dict):
        self.params = params
        # Use PROJECT_ROOT variable which is already defined at the top of the file
        self.base_path = params.get('base_data_path', os.path.join(PROJECT_ROOT, "data"))
        self.logger = logging.getLogger(__name__)
        # self.logger.propagate = False # Let root handler manage output

    def _get_json_files(self, dir_path: str) -> List[str]:
        """
        Get all JSON files in the directory.

        Args:
            dir_path (str): Path to the directory to check

        Returns:
            List[str]: List of paths to JSON files
        """
        try:
            json_files = glob.glob(os.path.join(dir_path, "*.json"))
            if json_files:
                self.logger.info(f"Found {len(json_files)} JSON files in {dir_path}")
            else:
                self.logger.info(f"No JSON files found in {dir_path}")
            return json_files
        except Exception as e:
            self.logger.error(f"Error getting JSON files from {dir_path}: {str(e)}")
            return []

    def _convert_date_format(self, date: str) -> str:
        """
        Convert date from MM/DD/YY to YYYYMMDD format for directory paths.

        Args:
            date (str): Date in MM/DD/YY format

        Returns:
            str: Date in YYYYMMDD format
        """
        try:
            # Use DateUtils for more robust date conversion
            iso_date = DateUtils.format_date(date, FORMAT_ISO, FORMAT_US_SHORT)
            if not iso_date:
                raise ValueError(f"Could not convert date '{date}' to ISO format")
            return iso_date
        except Exception as e:
            self.logger.error(f"Error converting date format: {e}")
            raise

    def _get_directory_path(self, date: str) -> str:
        """
        Get the full directory path for a given date.

        Args:
            date (str): Date in MM/DD/YY format

        Returns:
            str: Full directory path for the date
        """
        try:
            formatted_date = self._convert_date_format(date)
            # Use the derived 'iso_date' path structure
            return os.path.join(self.base_path, formatted_date, "dockets")
        except Exception as e:
            self.logger.error(f"Error creating directory path for date {date}: {e}")
            raise

    def generate_dates(self, start_date: str, end_date: str) -> List[str]:
        """
        Generate a list of dates between start_date and end_date inclusive.

        Args:
            start_date (str): Start date in MM/DD/YY format
            end_date (str): End date in MM/DD/YY format

        Returns:
            List[str]: List of dates in MM/DD/YY format
        """
        try:
            # Use DateUtils for more robust date range generation
            return DateUtils.generate_date_range(start_date, end_date, FORMAT_US_SHORT)
        except ValueError as e:
            self.logger.error(f"Error generating dates: {e}")
            raise ValueError(f"Invalid date format. Please use MM/DD/YY format. Error: {e}")

    def _check_files_for_date(self, dir_path: str) -> List[Tuple[str, str]]:
        """
        Check for matching JSON and PDF files in the directory.
        Returns list of tuples containing paths to matching JSON and PDF files.
        """
        matching_files = []
        json_files = glob.glob(os.path.join(dir_path, "*.json"))

        for json_file in json_files:
            root_name = os.path.splitext(os.path.basename(json_file))[0]
            pdf_file = os.path.join(dir_path, f"{root_name}.pdf")

            if os.path.exists(pdf_file):
                matching_files.append((json_file, pdf_file))
            else:
                self.logger.warning(f"Missing PDF file for JSON: {json_file}")

        return matching_files

    def process_single_date(self, date: str) -> bool:
        """
        Process all JSON files for a single date.

        Args:
            date (str): Date string in MM/DD/YY format

        Returns:
            bool: True if processing was attempted, False if no files found or other error.
        """
        self.logger.info(f"Starting process_single_date for: {date}")
        try:
            # Get directory path for the date
            dir_path = self._get_directory_path(date)

            # Check if directory exists
            if not os.path.exists(dir_path):
                self.logger.info(f"No directory found for date {date}: {dir_path}")
                return False

            # --- Determine which files to process ---
            reprocess_param = self.params.get('reprocess_files')
            files_to_process_param = None

            if isinstance(reprocess_param, (list, set)):
                # Filter the provided list to only those relevant for *this specific date*
                filtered_list = [
                    p for p in reprocess_param
                    if os.path.normpath(os.path.dirname(p)) == os.path.normpath(dir_path)
                ]
                if not filtered_list:
                    self.logger.info(f"Provided reprocess_files list has no files for date {date} in {dir_path}.")
                    return False  # Treat as skip if specific list is empty for this date
                files_to_process_param = filtered_list
                self.logger.info(
                    f"Will process {len(files_to_process_param)} specific files from list for date {date}.")
            elif reprocess_param is True:
                files_to_process_param = True
                self.logger.info(f"Will reprocess all applicable files for date {date} (reprocess_files=True).")
            else:
                files_to_process_param = False
                self.logger.info(
                    f"Standard processing triggered for date {date} (reprocess_files not specified or False).")

            # Create updated parameters for this batch, including the filtered file list
            updated_params = self.params.copy()
            updated_params['date'] = date  # Set the specific date being processed
            # Pass the determined files_to_process_param down, which might be List, True, or False
            updated_params['reprocess_files'] = files_to_process_param

            # Keep original start/end date if they exist in params (might be used by scraper even in date loop)
            if 'start_date' in self.params: updated_params['start_date'] = self.params['start_date']
            if 'end_date' in self.params: updated_params['end_date'] = self.params['end_date']

            # If weekly report is requested and running a report_generator task,
            # ensure we calculate the 7-day start date from the current date
            if updated_params.get('weekly', False) and updated_params.get('report_generator', False):
                try:
                    # Use DateUtils to get date 7 days before current date
                    seven_days_prior = DateUtils.get_date_before(date, 7)
                    # Convert to MM/DD/YY format for params
                    start_date_formatted = DateUtils.format_date(seven_days_prior, FORMAT_US_SHORT)

                    if start_date_formatted:
                        # Set the calculated start date for weekly reports
                        updated_params['start_date'] = start_date_formatted
                        self.logger.info(
                            f"Weekly report requested for {date}. Using date range: {updated_params['start_date']} to {date}")
                    else:
                        self.logger.error(f"Failed to calculate start date for weekly report using date: {date}")
                except Exception as e:
                    self.logger.error(f"Error calculating weekly date range: {e}")
                    # Keep the existing start_date if any

            # --- Instantiate MainProcessor with updated params for this date ---
            try:
                config_for_date = load_config(
                    end_date=date,  # Use current date for iso_date generation
                    start_date=updated_params.get('start_date'),
                    testing=updated_params.get('testing', False),
                    use_proxy=updated_params.get('use_proxy', True),
                    headless=updated_params.get('headless', True)
                )
                config_for_date['llm_provider'] = updated_params.get('llm_provider',
                                                                     config_for_date.get('llm_provider', 'deepseek'))
                config_for_date['DATA_DIR'] = self.base_path  # Use the base path from DateProcessor

            except Exception as config_err:
                self.logger.error(f"Error loading configuration for date {date}: {config_err}", exc_info=True)
                return False

            self.logger.info(
                f"Processing for date {date} using config with iso_date: {config_for_date.get('iso_date')}")
            setup_rich_logging(config_for_date)  # Re-setup logging for the correct date's log file

            # Check if there's actually any work to do based on flags
            needs_processing = (updated_params.get('post_process') or
                                updated_params.get('start_from_incomplete') or
                                isinstance(updated_params.get('reprocess_files'), (list, set)) or
                                updated_params.get('reprocess_files') is True)
            needs_upload = updated_params.get('upload')
            needs_scraping = updated_params.get('scraper') or updated_params.get(
                'docket_num')  # Check if scraping is triggered
            needs_weekly_report = updated_params.get('report_generator') and updated_params.get('weekly', False)

            # Determine if any action is requested
            action_requested = needs_processing or needs_upload or needs_scraping or \
                               updated_params.get('fb_ads') or updated_params.get('report_generator')

            if not action_requested:
                self.logger.info(
                    f"No processing, upload, scraping, ads, or report flags set for date {date}. Skipping MainProcessor run.")
                return True  # Considered successful as no work needed

            # --- Execute MainProcessor ---
            try:
                processor = MainProcessor(updated_params, config=config_for_date)
                # Run in a new event loop for isolation if needed, but maybe not necessary here
                # await processor.run() # Direct await if DateProcessor is run in async context
                # If DateProcessor is synchronous, need to manage the loop
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    loop.run_until_complete(processor.run())
                finally:
                    loop.close()
                    asyncio.set_event_loop(None)

                self.logger.info(f"Completed processing run for date {date}")
                return True

            except Exception as e:
                self.logger.error(f"Error running MainProcessor for date {date}: {str(e)}", exc_info=True)
                return False  # Indicate failure

        except Exception as e:
            self.logger.error(f"Unexpected error in process_single_date for {date}: {str(e)}", exc_info=True)
            return False

    def check_and_process(self, start_date: str, end_date: str) -> Dict[str, List[str]]:
        """
        Process all dates between start_date and end_date inclusive.
        Returns a dictionary with results for each date.
        """
        results = {
            'successful_dates': [],
            'failed_dates': [],
            'skipped_dates': []  # Dates where directory/files weren't found or no work needed
        }

        try:
            dates = self.generate_dates(start_date, end_date)
        except ValueError as e:
            self.logger.error(f"Failed to generate dates: {e}")
            return results

        total_dates = len(dates)
        self.logger.info(f"Starting batch processing of {total_dates} dates from {start_date} to {end_date}")

        for index, date in enumerate(dates, 1):
            self.logger.info(f"--- Processing Date {index}/{total_dates}: {date} ---")

            try:
                success = self.process_single_date(date)
                if success:
                    # Check if the run was truly successful or just skipped
                    # Need more granular return from process_single_date or check logs?
                    # For now, assume True means success/skip
                    results['successful_dates'].append(date)  # Could be success or skip
                    self.logger.info(f"Completed run (or skipped) for date {date}.")
                else:
                    # False generally indicates an error occurred during the run
                    results['failed_dates'].append(date)
                    self.logger.warning(f"Processing run for date {date} resulted in failure.")

            except Exception as e:
                # This catches errors *calling* process_single_date
                self.logger.error(f"FATAL: Unexpected error initiating processing for date {date}: {str(e)}",
                                  exc_info=True)
                results['failed_dates'].append(date)
                # Decide if to continue or stop batch on fatal error
                continue  # Continue to the next date

        # Log summary
        self.logger.info("\n=== Batch Processing Summary ===")
        self.logger.info(f"Completed/Skipped Runs: {len(results['successful_dates'])} dates")
        # self.logger.info(f"Skipped (No work/dir): {len(results['skipped_dates'])} dates") # Distinction needs work
        self.logger.info(f"Failed Runs (Errors): {len(results['failed_dates'])} dates")
        self.logger.info("================================")

        return results


class PlaywrightSetup:
    # --- PlaywrightSetup code remains unchanged ---
    @staticmethod
    async def check_browser_installation():
        """Check if browsers are installed for Playwright."""
        logger = logging.getLogger("PlaywrightSetup")
        logger.info("Checking Playwright browser installation...")

        try:
            # Run playwright install command to ensure browsers are available
            result = subprocess.run(
                [sys.executable, "-m", "playwright", "install", "chromium"],  # Use sys.executable
                capture_output=True,
                text=True,
                check=False  # Don't raise exception on non-zero exit
            )

            if result.returncode == 0:
                logger.info("Playwright browsers check command executed successfully (might not mean installed).")
                # Try a more definitive check
                return await PlaywrightSetup.run_test(silent=True)
            else:
                logger.warning(f"Playwright browser install check command failed. Attempting install...")
                # Try installing default browsers
                install_result = subprocess.run(
                    [sys.executable, "-m", "playwright", "install"],  # Use sys.executable
                    capture_output=True,
                    text=True,
                    check=False
                )
                if install_result.returncode == 0:
                    logger.info("Playwright install command successful.")
                    return await PlaywrightSetup.run_test(silent=True)
                else:
                    logger.error(
                        f"Playwright browser INSTALLATION failed:\nSTDOUT: {install_result.stdout}\nSTDERR: {install_result.stderr}")
                    return False
        except FileNotFoundError:
            logger.error(f"Could not run Playwright command. Is '{sys.executable}' in PATH and Playwright installed?")
            return False
        except Exception as e:
            logger.error(f"Error checking/installing Playwright: {str(e)}", exc_info=True)
            return False

    @staticmethod
    async def run_test(silent=False):
        """Test Playwright browser launch."""
        logger = logging.getLogger("PlaywrightSetup")
        if not silent: logger.info("Testing Playwright browser launch...")
        pw = None
        browser = None
        try:
            pw = await async_playwright().start()
            browser = await pw.chromium.launch()
            await browser.close()
            await pw.stop()
            if not silent: logger.info(f"Playwright browser launch test successful.")
            return True
        except Exception as e:
            if not silent: logger.error(f"Failed to launch Playwright browser: {str(e)}", exc_info=True)
            # Ensure cleanup even on failure
            if browser and not browser.is_closed(): await browser.close()
            if pw: await pw.stop()
            return False

    @staticmethod
    async def reset_playwright():
        """Ensure Playwright is properly configured."""
        logger = logging.getLogger("PlaywrightSetup")

        # First check if browsers are installed
        installed = await PlaywrightSetup.check_browser_installation()
        if not installed:
            logger.error(
                "Playwright check/installation failed. Please install manually ('python -m playwright install') or check environment.")
            sys.exit(1)  # Exit if setup fails

        # Test browser launch again after potential install
        success = await PlaywrightSetup.run_test()
        if not success:
            logger.error("Failed to verify Playwright setup after installation attempt.")
            sys.exit(1)

        logger.info("Playwright setup verified successfully")




class ScraperManager:
    def __init__(self, config: Dict):
        self.config = config
        self.logger = logging.getLogger(__name__)

    @staticmethod
    def _process_court_wrapper(args: Tuple[str, Dict, Optional[str], Optional[str]]) -> Tuple[
        str, bool, Dict[str, Any]]:
        """
        Spawn-safe worker: runs one court's scraping task using PacerOrchestrator.
        Accepts start and end dates for the orchestrator.
        """
        court_id, cfg, start_date_str, end_date_str = args  # Unpack arguments

        # --- Worker-Specific Logging Setup ---
        # Derive log directory from config passed to worker
        iso_date_worker = cfg.get("iso_date", "unknown_date")  # Get iso_date from config
        log_dir_base = cfg.get("directories", {}).get(
            "log_dir_base",  # Allow specifying a base dir for worker logs
            os.path.join(cfg.get("DATA_DIR", "./data"), iso_date_worker, "logs", "workers"),
        )
        os.makedirs(log_dir_base, exist_ok=True)
        log_file = os.path.join(log_dir_base, f"worker_{court_id}_{os.getpid()}.log")

        # Setup basic file logging for the worker
        worker_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        worker_file_handler = logging.FileHandler(log_file)
        worker_file_handler.setFormatter(worker_formatter)

        # Configure the root logger for this worker process
        # Avoid interfering with parent's RichHandler
        worker_root_logger = logging.getLogger()
        # Remove handlers inherited from parent if any (important for spawn)
        for handler in worker_root_logger.handlers[:]:
            worker_root_logger.removeHandler(handler)
        worker_root_logger.addHandler(worker_file_handler)
        worker_root_logger.setLevel(logging.INFO)  # Set desired level for worker logs

        # Set library levels for worker
        for lib in ['botocore', 'boto3', 'urllib3', 'playwright']:
            logging.getLogger(lib).setLevel(logging.WARNING)

        logger = logging.getLogger(f"Worker-{court_id}")
        logger.info(f"Worker started for court {court_id}. Logging to: {log_file}")
        logger.info(f"Worker using config with iso_date: {iso_date_worker}")
        logger.info(f"Worker processing for start: {start_date_str}, end: {end_date_str}")

        async def _run_orchestrator_for_court():
            """Async function to run the orchestrator for a single court."""
            try:
                async with PacerOrchestrator(cfg) as orchestrator:
                    # Async context manager properly initializes repositories
                    failed_courts_internal, processed_stats_list_internal = await orchestrator.process_courts(
                        court_ids=[court_id],  # Input to PacerOrchestrator is always a list
                        start_date_str=start_date_str,
                        end_date_str=end_date_str
                    )
                    success_status = court_id not in failed_courts_internal
                    # Return the single stats dict for this court, if available, otherwise empty dict
                    return success_status, processed_stats_list_internal[0] if processed_stats_list_internal else {}
            except Exception as e:
                logger.error(f"Exception during orchestrator run for {court_id}: {e}", exc_info=True)
                return False, {}  # Return failure status and empty stats on exception

        try:
            success, processed_stats = asyncio.run(_run_orchestrator_for_court())
            if success:
                logger.info(f"Worker finished successfully for court {court_id}")
            else:
                logger.warning(f"Worker finished for court {court_id}, but processing reported failure.")
            return court_id, success, processed_stats
        except Exception as e:
            logger.error(f"Worker failed unexpectedly for court {court_id}: {e}", exc_info=True)
            return court_id, False, {}  # Return empty dict on worker crash

    async def run_scraper(self, court_ids: List[str]) -> None:
        """
        Decide sequential vs parallel execution.
        Fetch court list with async helper if caller passed [].
        """
        # Make a copy to avoid modifying the list passed from params
        target_court_ids = list(court_ids)

        if not target_court_ids and not self.config.get("process_single_court"):  # Check original param here
            self.logger.info("Fetching full court list (process_single_court was empty)...")
            try:
                target_court_ids = await get_court_ids_async()
            except Exception as e:
                self.logger.error(f"Could not fetch court IDs: {e}")
                return

        if not target_court_ids:
            self.logger.warning("No courts specified or fetched to scrape.")
            return

        # --- Execution Mode ---
        if self.config.get("run_parallel", False):
            self.logger.info("Running scraper in PARALLEL mode.")
            await self._run_parallel_processing(target_court_ids)
        else:
            self.logger.info("Running scraper in SEQUENTIAL mode.")
            await self._run_sequential_processing(target_court_ids)

    async def _run_parallel_processing(self, court_ids: List[str], max_workers: Optional[int] = None) -> None:
        """Process courts in parallel using multiprocessing."""
        if not court_ids:
            self.logger.warning("No court IDs provided for parallel scraping.")
            return

        if not max_workers:
            max_workers = self.config.get("scraper_workers", multiprocessing.cpu_count())
        self.logger.info(f"Parallel scraping starting with {max_workers} workers for {len(court_ids)} courts")

        # Pass start/end dates from config to worker
        start_date_str = self.config.get("start_date")
        end_date_str = self.config.get("date")  # Use 'date' as end_date for the run

        # Log the config that ScraperManager is using for _append_processed_log_entry
        self.logger.info(
            f"ScraperManager (parallel) using self.config for DATA_DIR: '{self.config.get('DATA_DIR')}', iso_date: '{self.config.get('iso_date')}'")

        ctx = multiprocessing.get_context("spawn")
        with ctx.Pool(processes=max_workers) as pool:
            worker_args = [(cid, self.config, start_date_str, end_date_str) for cid in court_ids]

            results_iter = pool.imap_unordered(
                ScraperManager._process_court_wrapper,
                worker_args,
            )

            failed_courts: List[str] = []
            successful_courts: List[str] = []
            processed_count = 0
            start_time = time_module.monotonic()

            for court_id, success, processed_stats in results_iter:
                processed_count += 1
                elapsed_time = time_module.monotonic() - start_time
                avg_time = elapsed_time / processed_count if processed_count > 0 else 0
                remaining = len(court_ids) - processed_count
                eta = avg_time * remaining if avg_time > 0 else 0

                status_msg = f"Processed: {processed_count}/{len(court_ids)} | Failed: {len(failed_courts)} | Avg: {avg_time:.2f}s/court | ETA: {timedelta(seconds=int(eta))}"
                console.print(status_msg, end="\r")

                if success:
                    successful_courts.append(court_id)
                else:
                    failed_courts.append(court_id)

                if processed_stats:
                    self.logger.debug(
                        f"ScraperManager (parallel): About to call _append_processed_log_entry for court {court_id}.")
                    self.logger.debug(
                        f"ScraperManager (parallel): self.config DATA_DIR: '{self.config.get('DATA_DIR')}', iso_date: '{self.config.get('iso_date')}'")
                    self.logger.debug(f"ScraperManager (parallel): processed_stats for {court_id}: {processed_stats}")
                    _append_processed_log_entry(self.config, processed_stats, self.logger)
                else:
                    self.logger.warning(
                        f"No detailed processing stats returned for court {court_id} in parallel run. Skipping CSV log entry.")

            console.print()  # Newline after loop finishes
            self.logger.info("Parallel scraping finished.")
            if failed_courts:
                self.logger.warning(f"{len(failed_courts)} courts failed processing: {failed_courts}")
            else:
                self.logger.info("All courts processed successfully in parallel run.")

    async def _run_sequential_processing(self, court_ids: List[str]) -> None:
        """Process courts sequentially using PacerOrchestrator."""
        if not court_ids:
            self.logger.warning("No court IDs provided for sequential processing.")
            return

        self.logger.info(f"Initializing PacerOrchestrator for sequential run of {len(court_ids)} courts.")
        # Log the config that ScraperManager is using for _append_processed_log_entry
        self.logger.info(
            f"ScraperManager (sequential) using self.config for DATA_DIR: '{self.config.get('DATA_DIR')}', iso_date: '{self.config.get('iso_date')}'")

        # Instantiate orchestrator ONCE with the config
        try:
            async with PacerOrchestrator(self.config) as orchestrator:
                # MODIFIED: Get two lists back from PacerOrchestrator.process_courts
                failed_courts_seq, processed_stats_list_seq = await orchestrator.process_courts(
                    court_ids=court_ids,
                    start_date_str=self.config.get('start_date'),
                    end_date_str=self.config.get('date')  # Use 'date' as end_date
                )
        except Exception as init_err:
            self.logger.error(f"Failed to initialize PacerOrchestrator for sequential run: {init_err}", exc_info=True)
            return

        # NEW: Append all collected stats sequentially in the main process
        if processed_stats_list_seq:
            self.logger.info(f"Appending {len(processed_stats_list_seq)} processed stats from sequential run.")
            for stats in processed_stats_list_seq:
                # Added diagnostic logging
                court_id_from_stats = stats.get("CourtID", "UNKNOWN_IN_SEQ_LOOP")
                self.logger.debug(
                    f"ScraperManager (sequential): About to call _append_processed_log_entry for court {court_id_from_stats}.")
                self.logger.debug(
                    f"ScraperManager (sequential): self.config DATA_DIR: '{self.config.get('DATA_DIR')}', iso_date: '{self.config.get('iso_date')}'")
                self.logger.debug(f"ScraperManager (sequential): stats for {court_id_from_stats}: {stats}")
                _append_processed_log_entry(self.config, stats, self.logger)
        else:
            self.logger.info("No processing stats returned from sequential run.")

        if failed_courts_seq:
            self.logger.warning(
                f"Sequential processing failed for {len(failed_courts_seq)} courts: {failed_courts_seq}")
        else:
            self.logger.info("All courts processed successfully in sequential run.")

    async def run_single_docket(self, court_id: str, docket: str, html_only: bool = False) -> None:
        """ Process a single docket using the standalone function. """
        self.logger.info(
            f"Processing single docket {docket} for court {court_id} (HTML only={html_only}) using standalone function.")
        try:
            # Assuming process_single_docket_standalone also returns processed stats
            # or it's handled internally in the PacerOrchestrator's internal logging for standalone.
            # For now, let's assume if it produces a log, it will be handled by the orchestrator itself.
            await process_single_docket_standalone(
                config=self.config,
                court_id=court_id,
                docket_num=docket,
                html_only=html_only
            )
            self.logger.info(f"Single docket processing complete for {court_id}/{docket}.")
        except Exception as e:
            self.logger.error(f"Error processing single docket {court_id}/{docket}: {e}", exc_info=True)
            # Decide if error should be re-raised


class MainProcessor:
    # --- MainProcessor code remains unchanged ---
    def __init__(self, params: Dict, config: Dict = None):
        self.params = params  # Store the dictionary passed from main
        self.logger = logging.getLogger(__name__)  # Initialize logger first

        if config is None:
            # Load config using provided params, especially 'date' for iso_date
            self.config = load_config(
                end_date=params.get('date'),  # primary date for iso_date
                start_date=params.get('start_date'),  # for scraper range if needed
                testing=params.get('testing', False),
                use_proxy=params.get('use_proxy', True),
                headless=params.get('headless', True)
            )
        else:
            # Use pre-loaded config (e.g., from DateProcessor)
            self.config = config

        # Ensure start_date from params is preserved in config if not already set
        if params.get('start_date') and not self.config.get('start_date'):
            self.config['start_date'] = params.get('start_date')
        elif params.get('start_date') and self.config.get('start_date') != params.get('start_date'):
            self.config['start_date'] = params.get('start_date')  # Use params value

        # Ensure LLM provider is set, defaulting if necessary
        if 'llm_provider' not in self.config:
            self.config['llm_provider'] = self.params.get('llm_provider', 'deepseek')

        if 'force_openrouter_paid' in params:
            self.config['force_openrouter_paid'] = params['force_openrouter_paid']
            logging.getLogger(__name__).debug(
                f"Propagated force_openrouter_paid={self.config['force_openrouter_paid']} to config")

        if 'normalize_law_firm_names' in params:  # Ensure this gets into self.config
            self.config['normalize_law_firm_names'] = params['normalize_law_firm_names']
            logging.getLogger(__name__).debug(
                f"Propagated normalize_law_firm_names={self.config['normalize_law_firm_names']} to config")

        if 'reprocess_md' in params:  # Ensure this gets into self.config
            self.config['reprocess_md'] = params['reprocess_md']
            logging.getLogger(__name__).debug(f"Propagated reprocess_md={self.config['reprocess_md']} to config")

        if 'reprocess_mdl_num' in params:  # Ensure this gets into self.config
            self.config['reprocess_mdl_num'] = params['reprocess_mdl_num']
            logging.getLogger(__name__).debug(
                f"Propagated reprocess_mdl_num={self.config['reprocess_mdl_num']} to config")

        if 'use_docket_report_log' in params:  # Ensure this gets into self.config
            self.config['use_docket_report_log'] = params['use_docket_report_log']
            logging.getLogger(__name__).debug(
                f"Propagated use_docket_report_log={self.config['use_docket_report_log']} to config")

        # Propagate FB ads table names from params to config
        fb_table_keys = ['fb_ad_archive_table_name', 'fb_image_hash_table_name', 'law_firms_table_name']
        for key in fb_table_keys:
            if key in params:
                self.config[key] = params[key]
                logging.getLogger(__name__).debug(f"Propagated {key}={self.config[key]} to config")
        
        # Also propagate nested dynamodb table names
        if 'dynamodb' in params:
            if 'dynamodb' not in self.config:
                self.config['dynamodb'] = {}
            for key in fb_table_keys:
                if key in params['dynamodb']:
                    self.config['dynamodb'][key] = params['dynamodb'][key]
                    logging.getLogger(__name__).debug(f"Propagated dynamodb.{key}={self.config['dynamodb'][key]} to config")

        self.date = params.get('date')  # MM/DD/YY format from params
        self.start_date = params.get('start_date')  # Optional MM/DD/YY
        self.end_date = params.get('end_date')  # Optional MM/DD/YY
        self.iso_date = self.config.get('iso_date')  # YYYYMMDD format from config

        # Initialize S3Manager
        self.bucket_name = self.config.get('bucket_name', self.config.get('aws_s3', {}).get('bucket_name'))
        aws_access_key = self.config.get('aws_access_key')
        aws_secret_key = self.config.get('aws_secret_key')
        
        if not self.bucket_name or not aws_access_key or not aws_secret_key:
            logging.warning("S3 Bucket Name or AWS credentials not configured. S3 operations will be skipped.")
            self.s3_manager = None
        else:
            self.s3_manager = S3AsyncStorage(
                bucket_name=self.bucket_name,
                aws_access_key=aws_access_key,
                aws_secret_key=aws_secret_key,
                aws_region=self.config.get('aws_region', 
                    os.environ.get('AWS_REGION') or 
                    os.environ.get('LEXGENIUS_AWS_REGION') or
                    os.environ.get('REGION_NAME') or
                    'us-west-2')
            )

        self.file_handler = FileHandler(self.config, self.s3_manager)

        self.logger = logging.getLogger(__name__)
        self._setup_logging()

        self.processor: Optional[DataTransformer] = None
        self.uploader: Optional[Uploader] = None

    def _setup_logging(self) -> None:
        """Set up detailed logging configuration."""
        self.logger.info("=== MainProcessor Initialization ===")
        self.logger.info(f"Target Date (MM/DD/YY): {self.date}")
        self.logger.info(f"ISO Date (YYYYMMDD): {self.iso_date}")
        self.logger.info(f"Scraper Start Date: {self.start_date}")
        self.logger.info(f"Scraper End Date: {self.end_date}")
        self.logger.info(f"S3 Bucket: {self.bucket_name or 'Not Configured'}")
        self.logger.info(f"LLM Provider: {self.config.get('llm_provider')}")

        # Log important configuration parameters from self.params
        self.logger.debug("Run Parameters (from self.params):")
        loggable_params = ['scraper', 'headless', 'reset_chrome', 'docket_num',
                           'process_single_court', 'html_only', 'run_parallel',
                           'post_process', 'reprocess_files', 'start_from_incomplete',
                           'upload', 'upload_types', 'force_upload',  # Added force_upload
                           'fb_ads', 'report_generator', 'skip_ads',
                           'reprocess_md', 'num_workers']
        for key in loggable_params:
            # Log the value stored in the instance's self.params
            if key in self.params: self.logger.debug(f"  Param '{key}': {self.params[key]}")

        self.logger.debug("Derived Configuration:")
        loggable_config = ['testing', 'use_proxy', 'headless', 'DATA_DIR', 'log_dir', 'iso_date']
        for key in loggable_config:
            if key in self.config: self.logger.debug(f"  Config '{key}': {self.config.get(key)}")

    async def run(self) -> None:
        start_time = time_module.time()
        self.logger.info(f"=== Starting MainProcessor.run for ISO Date: {self.iso_date} ===")

        try:
            # Apply patches and setup cleanup handlers if not done globally
            # apply_patches() # Usually done once at entry point
            # setup_cleanup_handlers() # Usually done once at entry point

            # Chrome reset if needed
            if self.params.get('reset_chrome'):
                self.logger.info("Resetting Playwright...")
                await PlaywrightSetup.reset_playwright()

            # Scraping process
            if self.params.get('scraper') or self.params.get('docket_num'):
                await self._handle_scraping()  # Now uses PacerOrchestrator directly

            # Post-processing and Uploading Phase
            if any([self.params.get('post_process'),
                    self.params.get('reprocess_files'),
                    self.params.get('start_from_incomplete'),
                    self.params.get('upload')]):
                await self._handle_post_processing_and_upload()  # Use the combined handler

            # Additional processing
            if self.params.get('fb_ads'):
                if FacebookAdsOrchestrator:
                    self.logger.info("Starting Facebook ads processing")
                    await self.run_fb_ads()
                else:
                    self.logger.warning("Skipping Facebook ads processing - library not available.")

            # Report Generation handled in main() or DateProcessor loop

            execution_time = time_module.time() - start_time
            self.logger.info(
                f"=== MainProcessor.run completed for {self.iso_date}. Execution time: {execution_time:.2f} seconds ===")

        except Exception as e:
            self.logger.error(f"Critical error in MainProcessor.run for {self.iso_date}: {str(e)}", exc_info=True)
            raise
        finally:
            # Consider if any specific cleanup is needed here per run
            pass

    async def _handle_scraping(self) -> None:
        """Handle the scraping process based on parameters using PacerOrchestrator."""
        self.logger.info("=== Starting Scraping Process (Using PacerOrchestrator) ===")
        
        # Check for PACER credentials before proceeding
        if not self.config.get('username_prod') or not self.config.get('password_prod'):
            self.logger.error(
                "PACER credentials missing! Please set PACER_USERNAME and PACER_PASSWORD environment variables."
            )
            self.logger.error(
                "Example: export PACER_USERNAME='your_username' && export PACER_PASSWORD='your_password'"
            )
            return

        court_ids_param = self.params.get('process_single_court', [])
        docket_num_param = self.params.get('docket_num')
        html_only_param = self.params.get('html_only', False)
        docket_list_input = self.params.get('docket_list_for_orchestrator')  # Get the new list

        try:
            async with PacerOrchestrator(self.config) as orchestrator:
                # --- Mode 1: List of specific dockets from multiple_courts YAML key ---
                if docket_list_input and isinstance(docket_list_input, list):
                    self.logger.info(
                        f"Processing specific list of {len(docket_list_input)} dockets from 'multiple_courts' YAML.")
                    await orchestrator.process_courts(
                        court_ids=[],  # Not used when docket_list_input is provided
                        start_date_str=self.params.get('start_date'),
                        end_date_str=self.params.get('date'),  # 'date' is the run date
                        docket_list_input=docket_list_input
                    )
                    self.logger.info("Specific docket list processing complete.")

                # --- Mode 2: Single Docket Processing (old style) ---
                elif docket_num_param and court_ids_param:
                    if isinstance(court_ids_param, list) and len(court_ids_param) > 0:
                        target_court = court_ids_param[0]
                        if len(court_ids_param) > 1:
                            self.logger.warning(
                                f"Multiple courts provided ({court_ids_param}) but processing single docket ({docket_num_param}) for the first court only: {target_court}")
                        self.logger.info(
                            f"Processing single docket - Court: {target_court}, Docket: {docket_num_param}, HTML Only: {html_only_param}")
                        await process_single_docket_standalone(self.config, target_court, docket_num_param, html_only_param)
                        self.logger.info("Single docket processing complete.")
                    else:
                        self.logger.error(
                            f"Cannot process single docket {docket_num_param}: 'process_single_court' list is empty or not a list.")

                # --- Mode 3: Report Scraping (Multi-Court) ---
                elif self.params.get('scraper'):  # General scraper flag, no specific dockets
                    target_courts_for_reports = court_ids_param  # This list is filtered in main()
                    if not target_courts_for_reports:
                        self.logger.warning(
                            "Scraper enabled but the list of courts to process for reports is empty. No courts will be scraped.")
                    else:
                        self.logger.info(
                            f"Running PacerOrchestrator for report scraping on {len(target_courts_for_reports)} courts.")
                        await orchestrator.process_courts(
                            court_ids=target_courts_for_reports,
                            start_date_str=self.params.get('start_date'),
                            end_date_str=self.params.get('date')
                        )
                        self.logger.info("PacerOrchestrator multi-court report scraping complete.")
                else:
                    self.logger.info(
                        "Scraping skipped (no specific dockets from 'multiple_courts', not single docket mode, and scraper flag not set for report mode).")
        except Exception as e:
            self.logger.error(f"Error during scraping process: {e}", exc_info=True)
            raise

    async def _handle_post_processing_and_upload(self) -> None:
        """
        Handles the post-processing and upload workflows based on flags.
        Processing runs first if needed, then upload runs if requested,
        using the results from the processing step to select files for upload.
        """
        self.logger.info("--- Entering Post-Processing & Upload Handling ---")

        # The 'post_process' flag from YAML (via self.params) indicates if general post-processing should run.
        run_general_post_process_flag = self.params.get('post_process', True)  # Default to True if not specified

        self.logger.debug(f"Flags: General post_process (from params)={run_general_post_process_flag}, "
                          f"reprocess_files type: {type(self.params.get('reprocess_files')).__name__}, "
                          f"start_from_incomplete={self.params.get('start_from_incomplete')}, "
                          f"normalize_law_firm_names (from config): {self.config.get('normalize_law_firm_names')}, "
                          f"upload={self.params.get('upload')}")

        # DataTransformer reads these from its config, ensure they are set in self.config by __init__
        # self.config['reprocess_md'] = self.params.get('reprocess_md', False)
        # self.config['force_openrouter_paid'] = self.params.get('force_openrouter_paid', False)
        # self.config['normalize_law_firm_names'] = self.params.get('normalize_law_firm_names', False)
        # Already handled in __init__ to ensure self.config is up-to-date.

        # --- Determine if DataTransformer needs to be run ---
        reprocess_arg = self.params.get('reprocess_files', False)
        start_incomplete_arg = self.params.get('start_from_incomplete', False)

        # Standard conditions that trigger DataTransformer for full processing or reprocessing
        standard_processing_triggered = (
                run_general_post_process_flag or  # If YAML explicitly says post_process: True
                isinstance(reprocess_arg, (list, set)) or  # Explicit list of files to reprocess
                reprocess_arg is True or  # Explicitly reprocess all
                start_incomplete_arg  # Start from incomplete files
        )

        # Check for cleanup operations that should run regardless of post_process setting
        normalize_law_firm_names_config_flag = self.config.get('normalize_law_firm_names', False)
        reprocess_mdl_num_config_flag = self.config.get('reprocess_mdl_num', False)
        
        # These cleanup operations run whenever their flags are True
        cleanup_operations_requested = normalize_law_firm_names_config_flag or reprocess_mdl_num_config_flag

        # DataTransformer should run if standard processing is triggered OR
        # if any cleanup operations are requested
        should_run_data_transformer = standard_processing_triggered or cleanup_operations_requested

        processing_summary: Optional[List[Dict]] = None

        # --- Step 1: Run DataTransformer if needed ---
        if should_run_data_transformer:
            self.logger.info(">>> Workflow Step: DataTransformer run is needed. <<<")
            if cleanup_operations_requested:
                cleanup_modes = []
                if normalize_law_firm_names_config_flag:
                    cleanup_modes.append("Law Firm Normalization")
                if reprocess_mdl_num_config_flag:
                    cleanup_modes.append("MDL Title Update")
                self.logger.info(f"DataTransformer will run cleanup operations: {', '.join(cleanup_modes)}")
                if standard_processing_triggered:
                    self.logger.info("Standard processing will also run after cleanup operations.")
            elif standard_processing_triggered:
                self.logger.info("DataTransformer will run in 'Standard Processing/Reprocessing' mode.")

            try:
                if not self.processor:
                    self.logger.info("Initializing DataTransformer for workflow...")
                    # self.config should be fully populated by __init__ with all relevant flags
                    self.processor = DataTransformer(self.config, self.logger)
                    self.logger.info("DataTransformer initialized.")

                num_workers = self.params.get('num_workers', (os.cpu_count() or 1) * 2)

                self.logger.info("DataTransformer.start() Invocation Parameters:")
                self.logger.info(f"  Workers: {num_workers}")
                if isinstance(reprocess_arg, (list, set)):
                    self.logger.info(f"  Reprocess Files Arg: List/Set with {len(reprocess_arg)} items")
                    self.logger.debug(f"  Reprocess Files List (first 5): {list(reprocess_arg)[:5]}")
                else:
                    self.logger.info(f"  Reprocess Files Arg: {reprocess_arg} (type: {type(reprocess_arg).__name__})")
                self.logger.info(f"  Start From Incomplete Arg: {start_incomplete_arg}")
                self.logger.info(f"  Skip Files Arg: {self.params.get('skip_files', [])}")
                # CRITICAL: Pass the general post_process flag from params here.
                # DataTransformer.start() uses this to decide between full processing and normalization-only mode.
                self.logger.info(f"  post_process (to DataTransformer.start): {run_general_post_process_flag}")

                processing_summary = await self.processor.start(
                    upload=False,
                    reprocess_files=reprocess_arg,
                    start_from_incomplete=start_incomplete_arg,
                    skip_files=self.params.get('skip_files', []),
                    num_workers=num_workers,
                    post_process=run_general_post_process_flag  # Pass the flag from params
                )

                if processing_summary is None:
                    self.logger.error("DataTransformer.start returned None, indicating a potential error.")
                elif not processing_summary:
                    self.logger.warning("DataTransformer.start completed but returned no summary (empty list).")
                else:
                    self.logger.info(f"DataTransformer.start completed. Reported {len(processing_summary)} outcomes.")
                    self.logger.debug(f"Processing summary (first 2): {processing_summary[:2]}")

            except Exception as proc_err:
                self.logger.error(f"DataTransformer workflow encountered an unhandled error: {proc_err}", exc_info=True)
                processing_summary = None
        else:
            self.logger.info(">>> Workflow Step: DataTransformer run Skipped (no relevant flags/conditions met) <<<")
            processing_summary = []

        # --- Step 2: Run Granular Upload Workflows ---
        upload_json = self.params.get('upload_json_to_dynamodb', False)
        upload_pdfs = self.params.get('upload_pdfs_to_s3', False)
        upload_reports = self.params.get('upload_reports_to_s3', False)
        
        if upload_json or upload_pdfs:
            self.logger.info(">>> Workflow Step: Data Upload Requested <<<")
            if processing_summary is None:
                self.logger.warning("Skipping data upload: Preceding DataTransformer step failed or returned None.")
            elif not processing_summary and should_run_data_transformer:  # If DT was supposed to run but found nothing
                self.logger.warning(
                    "Skipping data upload: Preceding DataTransformer step found no files to process/summarize.")
            else:
                # Build upload targets based on flags
                upload_targets = set()
                if upload_json:
                    upload_targets.add('dynamodb')
                if upload_pdfs:
                    upload_targets.add('s3')
                    
                force_s3_upload = upload_pdfs  # Force S3 upload if PDFs requested
                self.logger.info(f"Upload Params: JSON to DynamoDB={upload_json}, PDFs to S3={upload_pdfs}, Targets={upload_targets}")

                try:
                    if not self.file_handler:
                        self.logger.error("FileHandler not initialized. Cannot proceed with upload.")
                        return

                    await self.run_upload_workflow(
                        processed_summary=processing_summary,  # Pass summary (None, [], or list of dicts)
                        upload_types=upload_targets,
                        force_s3_upload=force_s3_upload
                    )
                    self.logger.info("Data upload workflow completed.")
                except Exception as upload_err:
                    self.logger.error(f"Data upload workflow failed: {upload_err}", exc_info=True)
        else:
            self.logger.info(">>> Workflow Step: Data Upload Skipped (both upload_json_to_dynamodb and upload_pdfs_to_s3 are False) <<<")

        self.logger.info("--- Exiting Post-Processing & Upload Handling ---")

    async def run_upload_workflow(
            self,
            processed_summary: Optional[List[Dict]],  # Can be None (failure), [] (skipped), or list
            upload_types: Set[str],
            force_s3_upload: bool
    ):
        """
        Handles the upload process based on configuration and processed files summary.

        Args:
            processed_summary: A list of dictionaries summarizing successfully processed files,
                               an empty list if processing was skipped, or None if processing failed.
            upload_types: A set specifying the target services (e.g., {'s3', 'dynamodb'}).
            force_s3_upload: Flag to force S3 upload even if the file exists.
        """
        self.logger.info("=== Starting Upload Workflow ===")
        self.logger.info(f"Upload Target Types: {upload_types}")
        self.logger.info(f"Force S3 Upload Flag: {force_s3_upload}")

        # --- Get list of files to upload ---
        json_files_to_upload = []
        if processed_summary is None:
            self.logger.warning(
                "Upload workflow received None for processed_summary (processing failed). Skipping upload.")
            return {'uploaded': [], 'skipped': [], 'failed': ['Processing step failed']}
        elif processed_summary:  # Non-empty list means processing ran successfully
            self.logger.info(f"Upload workflow using summary of {len(processed_summary)} processed files.")
            target_docket_dir = self.file_handler.get_target_docket_directory()
            if not target_docket_dir:
                self.logger.error("Cannot determine target docket directory for upload. Aborting upload.")
                return {'uploaded': [], 'skipped': [], 'failed': ['Target directory unknown']}

            for item in processed_summary:
                final_filename = item.get('filename')  # Filename from DataTransformer result
                if final_filename and isinstance(final_filename, str) and final_filename.endswith('.json'):
                    full_path = os.path.join(target_docket_dir, final_filename)
                    if await asyncio.to_thread(os.path.exists, full_path):
                        json_files_to_upload.append(full_path)
                    else:
                        self.logger.warning(f"File reported in summary not found for upload: {full_path}")
                else:
                    self.logger.warning(f"Processed summary item missing valid 'filename': {item}")

            if not json_files_to_upload:
                self.logger.warning(
                    "Processed summary provided, but no valid/existing JSON files extracted for upload.")
                return {'uploaded': [], 'skipped': [], 'failed': ['No valid files from summary']}
        else:  # Empty list [] means processing was skipped or found nothing to process
            self.logger.warning(
                "No processed summary provided (processing skipped or no files found). Scanning directory for all JSON files (may include unprocessed/errored files).")
            # Use file_handler.get_json_files() which applies basic filtering
            json_files_to_upload = self.file_handler.get_json_files()
            if not json_files_to_upload:
                self.logger.warning("No JSON files found in the target directory for upload.")
                return {'uploaded': [], 'skipped': [], 'failed': ['No files found in directory']}

        self.logger.info(f"Identified {len(json_files_to_upload)} JSON files for potential upload.")
        # Log first few files for debugging
        log_limit = 5
        for i, fpath in enumerate(json_files_to_upload):
            if i < log_limit:
                self.logger.debug(f"  - {os.path.basename(fpath)}")
            elif i == log_limit:
                self.logger.debug(f"  ... and {len(json_files_to_upload) - log_limit} more.");
                break

        # --- Initialize Uploader ---
        if not self.uploader:
            self.logger.info("Initializing Uploader for upload workflow...")
            # Need PacerManager for Uploader init
            # Should PacerManager be initialized here or in MainProcessor.__init__?
            # Let's assume it needs to be initialized if not already done.
            # It's better to initialize components in __init__ if they are always needed.
            # Re-check __init__ - PacerManager is NOT initialized there. Let's add it.
            # For now, initialize lazily if DataTransformer wasn't run.

            pacer_db_instance = None
            s3_manager_instance = self.s3_manager  # Get from self

            if self.processor:  # If DataTransformer ran, use its components
                pacer_db_instance = self.processor.pacer_db
                # s3_manager_instance = self.processor.s3_manager # Already have self.s3_manager
            else:  # Initialize PacerDB if needed
                self.logger.warning("DataTransformer didn't run. Lazily initializing PacerRepository for Uploader.")
                try:
                    # Use new repository pattern directly for better performance
                    from src.infrastructure.storage.dynamodb_async import AsyncDynamoDBStorage
                    from src.repositories.pacer_repository import PacerRepository
                    
                    # Create a simple config object with required attributes
                    class SimpleConfig:
                        def __init__(self, config_dict):
                            for k, v in config_dict.items():
                                setattr(self, k, v)
                    
                    config_obj = SimpleConfig(self.config)
                    storage = AsyncDynamoDBStorage(config_obj)
                    pacer_db_instance = PacerRepository(storage)
                except ImportError:
                    # Fallback to old import if migration helper not available
                    try:
                        from lib.pacer_manager import PacerManager  # Local import
                        pacer_db_instance = PacerManager(self.config)
                    except ImportError:
                        self.logger.error("Failed to import PacerManager. Cannot initialize Uploader for DynamoDB.")
                        # Can still proceed if only S3 upload is needed
                except Exception as db_init_err:
                    self.logger.error(f"Failed to initialize PacerManager: {db_init_err}", exc_info=True)

            # Check if required components are available based on upload_types
            can_upload = True
            if 's3' in upload_types and not s3_manager_instance:
                self.logger.error("Cannot initialize Uploader: S3 target specified but S3Manager is not available.")
                can_upload = False
            if 'dynamodb' in upload_types and not pacer_db_instance:
                self.logger.error(
                    "Cannot initialize Uploader: DynamoDB target specified but PacerManager is not available.")
                can_upload = False

            if not can_upload:
                self.logger.error("Aborting upload workflow due to missing components.")
                return {'uploaded': [], 'skipped': [], 'failed': ['Uploader component init failed']}

        # --- Perform Batch Upload with proper context management ---
        # Use context managers to ensure proper cleanup of storage instances
        upload_results = {'uploaded': [], 'skipped': [], 'failed': []}
        
        if 's3' in upload_types and s3_manager_instance:
            async with s3_manager_instance as s3_mgr:
                if 'dynamodb' in upload_types and pacer_db_instance and hasattr(pacer_db_instance, 'storage'):
                    async with pacer_db_instance.storage as db_storage:
                        # Both S3 and DynamoDB uploads
                        uploader = Uploader(
                            config=self.config,
                            s3_manager=s3_mgr,
                            pacer_db=pacer_db_instance,
                            file_handler=self.file_handler
                        )
                        upload_results = await uploader.upload_batch_to_aws_async(
                            json_paths=json_files_to_upload,
                            upload_types=upload_types,
                            force_s3_upload=force_s3_upload
                        )
                else:
                    # S3 only
                    uploader = Uploader(
                        config=self.config,
                        s3_manager=s3_mgr,
                        pacer_db=None,
                        file_handler=self.file_handler
                    )
                    upload_results = await uploader.upload_batch_to_aws_async(
                        json_paths=json_files_to_upload,
                        upload_types=upload_types,
                        force_s3_upload=force_s3_upload
                    )
        elif 'dynamodb' in upload_types and pacer_db_instance and hasattr(pacer_db_instance, 'storage'):
            async with pacer_db_instance.storage as db_storage:
                # DynamoDB only
                uploader = Uploader(
                    config=self.config,
                    s3_manager=None,
                    pacer_db=pacer_db_instance,
                    file_handler=self.file_handler
                )
                upload_results = await uploader.upload_batch_to_aws_async(
                    json_paths=json_files_to_upload,
                    upload_types=upload_types,
                    force_s3_upload=force_s3_upload
                )
        else:
            self.logger.error("No valid storage instances for upload")
            upload_results = {'uploaded': [], 'skipped': [], 'failed': ['No valid storage instances']}

        self.logger.info("=== Upload Workflow Finished ===")
        return upload_results  # Uploader now returns its own summary

    async def run_fb_ads(self) -> None:
        # --- Facebook Ads logic ---
        self.logger.info("=== Starting Facebook Ads Processing ===")
        if not FacebookAdsOrchestrator:
            self.logger.warning("FacebookAdsOrchestrator not available. Skipping FB Ads.")
            return
        try:
            # FB config could be in either self.config or self.params
            fb_config = self.config.get('facebook_ads') or self.params.get('facebook_ads', {})
            self.logger.debug(f"FB Config found: {fb_config}")
            self.logger.debug(f"Config keys: {list(self.config.keys())}")
            self.logger.debug(f"Params keys: {list(self.params.keys())}")
            
            if not fb_config.get('enabled', self.params.get('fb_ads', False)):
                self.logger.info("Facebook ads processing is disabled in config/params.")
                return

            required_fb_keys = ['app_id', 'app_secret', 'access_token', 'ad_account_id']
            missing_keys = [k for k in required_fb_keys if not fb_config.get(k)]
            if missing_keys:
                self.logger.error(
                    f"Facebook Ads enabled but missing required credentials {missing_keys} in config. Found: {list(fb_config.keys())}")
                return

            # Create merged config for FB ads processing
            merged_config = self.config.copy()
            # Add FB ads specific processing flags from params
            fb_processing_flags = [
                'defer_image_processing', 'image_queue_dir', 'image_queue',
                'headless', 'use_proxy', 'mobile_proxy', 'render_html', 'airplane_mode',
                'max_ad_pages', 'api_retries', 'api_backoff_base', 'payload_retries',
                'oxylabs_num_proxies', 'proxy_ban_duration', 'max_proxy_failures',
                'rotate_proxy_between_firms', 'rotate_proxy_per_page'
            ]
            for flag in fb_processing_flags:
                if flag in self.params:
                    merged_config[flag] = self.params[flag]
            
            # Instantiate the orchestrator with aiohttp session
            import aiohttp
            async with aiohttp.ClientSession() as session:
                fb_orchestrator = FacebookAdsOrchestrator(merged_config, session)
                # Run the fb ads processing
                await fb_orchestrator.run()
            self.logger.info("Facebook ads processing completed")
        except Exception as e:
            self.logger.error(f"Error in Facebook ads processing: {str(e)}", exc_info=True)

    async def run_report_generator(self) -> None:
        # --- Report Generator logic ---
        self.logger.info("=== Starting Report Generation ===")
        try:
            iso_date_for_report = self.config.get('iso_date')
            if not iso_date_for_report:
                self.logger.error("ISO date missing in config. Cannot generate report accurately.")
                iso_date_for_report = datetime.now().strftime('%Y%m%d')
                self.logger.warning(f"Using current date {iso_date_for_report} for report due to missing config value.")

            # Check if weekly report is requested
            weekly_report = self.params.get('weekly', False)
            skip_ads_param = self.params.get('skip_ads', False)
            skip_invalidate_param = self.params.get('skip_invalidate', False)

            if weekly_report:
                self.logger.info("Generating WEEKLY report (7-day period)")
                from src.reports.orchestrator_weekly import WeeklyReportOrchestrator
                reports = WeeklyReportOrchestrator(self.config)
                await reports.generate_report()
                self.logger.info("Weekly report generation completed")
            else:
                self.logger.info("Generating standard DAILY report")
                reports = ReportsOrchestrator(self.config, logger=self.logger, iso_date=iso_date_for_report)
                self.logger.info(f"Report Generation Skip Ads Param: {skip_ads_param}")
                # Assuming ReportOrchestrator.start is synchronous
                # Run synchronous function in a thread
                await asyncio.to_thread(reports.run, skip_ads=skip_ads_param, skip_invalidate=skip_invalidate_param)
                self.logger.info("Daily report generation completed")

        except ImportError as ie:
            self.logger.warning(f"Report Orchestrator import failed: {ie}. Skipping Report Generation.")
        except Exception as e:
            self.logger.error(f"Error in report generation: {str(e)}", exc_info=True)

    async def process_cases(self, cases: List[Tuple[str, str]]) -> None:
        # --- Process Cases logic ---
        self.logger.info(f"=== Processing {len(cases)} Specific Cases ===")

        for court_id, docket_num in cases:
            self.logger.info(f"--- Processing Case: Court={court_id}, Docket={docket_num} ---")
            case_start_time = time_module.time()
            try:
                # Create specific parameters for this single case run
                case_params = self.params.copy()  # Start with base params
                case_params.update({
                    'process_single_court': [court_id],  # Target the specific court
                    'docket_num': docket_num,  # Target the specific docket
                    'scraper': True,  # Force scraper on for single case processing
                    'html_only': self.params.get('html_only', False),  # Respect html_only flag
                    # Inherit post-process/upload flags? Or disable for single case?
                    # Let's disable them by default for single case focus.
                    'post_process': self.params.get('post_process_single', False),  # New flag? Default False
                    'upload': self.params.get('upload_single', False),  # New flag? Default False
                    'reprocess_files': None,  # Don't reprocess others
                    'start_from_incomplete': False,  # Don't start incomplete
                })
                # Ensure config reflects the potential need for scraping (headless, proxy)
                # Use the main config, assuming it's suitable
                case_config = self.config.copy()

                # Instantiate a new MainProcessor for this case? Recommended for isolation.
                case_processor = MainProcessor(case_params, case_config)
                await case_processor.run()

                case_duration = time_module.time() - case_start_time
                self.logger.info(f"--- Completed processing case {court_id}/{docket_num} in {case_duration:.2f}s ---")

            except Exception as e:
                case_duration = time_module.time() - case_start_time
                self.logger.error(f"Error processing case {court_id}/{docket_num} after {case_duration:.2f}s: {str(e)}",
                                  exc_info=True)
                # Continue to the next case


def load_failed_downloads(base_data_dir, iso_date):
    """
    Load failed downloads from docket files in data/YYYYMMDD/dockets directory.
    Returns a list of dockets with 'Download failed.' in _processing_notes.
    Excludes cases with 'Matched ignore_download config.' to avoid reprocessing intentionally ignored cases.
    """
    logger = logging.getLogger(__name__)
    failed_downloads = []
    excluded_ignore_download_count = 0

    dockets_directory = Path(base_data_dir) / iso_date / "dockets"

    if not dockets_directory.exists():
        logger.warning(f"Dockets directory not found: {dockets_directory}")
        return failed_downloads

    logger.info(f"Scanning for failed downloads in: {dockets_directory}")

    json_files = list(dockets_directory.glob("*.json"))
    if not json_files:
        logger.warning(f"No JSON files found in {dockets_directory}")
        return failed_downloads

    logger.info(f"Checking {len(json_files)} docket files for failed downloads...")

    for json_file in json_files:
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # Check if this file has "Download failed." in _processing_notes
            processing_notes = data.get('_processing_notes', '')
            if 'Download failed.' in processing_notes:
                # Exclude ignore_download cases from failed processing
                if 'Matched ignore_download config.' in processing_notes:
                    logger.debug(f"Excluding ignore_download case from failed processing: {json_file.name}")
                    excluded_ignore_download_count += 1
                    continue
                    
                court_id = data.get('court_id')
                docket_num = data.get('docket_num')

                if court_id and docket_num:
                    failed_downloads.append({
                        'court_id': court_id,
                        'docket_num': docket_num
                    })
                    logger.debug(f"Found failed download: {court_id}/{docket_num}")
                else:
                    logger.warning(f"File {json_file.name} has 'Download failed.' but missing court_id or docket_num")

        except json.JSONDecodeError:
            logger.error(f"Error reading JSON from {json_file.name}. Skipping...")
        except Exception as e:
            logger.error(f"Unexpected error reading {json_file.name}: {str(e)}")

    logger.info(f"Found {len(failed_downloads)} failed downloads. Excluded {excluded_ignore_download_count} ignore_download cases.")
    return failed_downloads


async def process_case_batch(custom_params):
    """
    Process a batch of cases using the provided custom parameters.
    (Tailored for DateProcessor)
    """
    logger = logging.getLogger(__name__)
    config_for_batch = None
    try:
        # Config loading happens here based on the specific date from DateProcessor
        config_for_batch = load_config(
            end_date=custom_params.get('date'),  # Essential for iso_date
            start_date=custom_params.get('start_date'),
            testing=custom_params.get('testing', False),
            use_proxy=custom_params.get('use_proxy', True),
            headless=custom_params.get('headless', True)
        )
        config_for_batch['llm_provider'] = custom_params.get('llm_provider',
                                                             config_for_batch.get('llm_provider', 'deepseek'))
        config_for_batch['DATA_DIR'] = custom_params.get('base_data_path',
                                                         os.environ.get('DATA_DIR', './data'))  # Ensure DATA_DIR

        # Setup logging specifically for this batch run if needed
        setup_rich_logging(config_for_batch)
        logger.info(
            f"Processing cases for date {custom_params.get('date')} with derived iso_date {config_for_batch.get('iso_date')}")

        # Create processor and run directly since we're already in an async context (called by DateProcessor loop)
        processor = MainProcessor(custom_params, config_for_batch)
        await processor.run()
    except Exception as e:
        logger.error(f"Error in process_case_batch for date {custom_params.get('date')}: {e}", exc_info=True)
        raise  # Re-raise to signal failure to the caller (e.g., DateProcessor)


async def main():
    """
    Main entry point for the application. Sets parameters and runs the main processor or orchestrator.
    Ensures processing/upload happens before report generation if both are requested.
    """
    base_data_dir = os.environ.get('LEXGENIUS_DATA_DIR', os.path.join(PROJECT_ROOT, "data"))
    os.environ['DATA_DIR'] = base_data_dir

    params = {
        "date": None, 'start_date': None, 'end_date': None,
        'llm_provider': 'deepseek',
        'scraper': False, 'headless': True, 'reset_chrome': False,
        'docket_num': None, 'process_single_court': [],
        'docket_list_for_orchestrator': None,
        'skip_courts': [], 'start_after_court': None, 'start_at_court': None,
        'html_only': False, 'run_parallel': False,
        'post_process': False, 'reprocess_files': False, 'start_from_incomplete': False,
        'skip_files': [], 'reprocess_md': False, 'force_openrouter_paid': False,
        'upload': False, 'upload_types': ['s3', 'dynamodb'], 'force_upload': False,
        'num_workers': (os.cpu_count() or 1) * 2,
        'fb_ads': False,
        'report_generator': False, 'skip_ads': False, 'skip_invalidate': False, 'weekly': False,
        'process_review_cases': False,  # Process cases that need review
        'process_review_cases_legacy': False,  # DEPRECATED: Use process_review_cases instead
        'reprocess_failed': False,  # New parameter for reprocessing failed downloads
    }

    cli_parser = argparse.ArgumentParser(description="LexGenius Processing Script")
    cli_parser.add_argument("--params", help="Path to YAML configuration file to override default parameters.")
    args = cli_parser.parse_args()

    if args.params:
        try:
            with open(args.params, 'r') as f:
                yaml_config = yaml.safe_load(f)
                if yaml_config:
                    logging.info(f"Loading parameters from YAML file: {args.params}")
                    # Update params with all YAML values first
                    params.update(yaml_config)

                    # Now, specifically handle logic for 'multiple_courts', 'process_review_cases', and 'reprocess_failed'
                    # which might override or set 'docket_list_for_orchestrator'

                    # Handle process_review_cases and reprocess_failed flags
                    docket_list_from_flags = []

                    # Get iso_date for path construction (needed for both flags)
                    temp_config_for_iso_date = None
                    iso_date = None
                    if params.get('process_review_cases') or params.get('process_review_cases_legacy') or params.get('reprocess_failed'):
                        temp_config_for_iso_date = load_config(
                            end_date=params.get('date'),
                            start_date=params.get('start_date')
                        )
                        iso_date = temp_config_for_iso_date.get('iso_date')
                        if not iso_date:
                            logging.error(
                                "Cannot determine iso_date for loading dockets. 'date' parameter might be missing or invalid.")

                    # Handle process_review_cases (new) or process_review_cases_legacy (legacy)
                    if params.get('process_review_cases') or params.get('process_review_cases_legacy'):
                        flag_name = 'process_review_cases' if params.get('process_review_cases') else 'process_review_cases_legacy'
                        logging.info(
                            f"{flag_name} is True. Attempting to load dockets from review_cases_final.json.")
                        if iso_date:
                            # Try new filename first, then legacy filename
                            review_cases_path = Path(base_data_dir) / iso_date / "logs" / "review_cases_final.json"
                            legacy_cases_path = Path(base_data_dir) / iso_date / "logs" / "review_cases_final.json"

                            cases_path = review_cases_path if review_cases_path.exists() else legacy_cases_path

                            if cases_path.exists():
                                try:
                                    with open(cases_path, 'r') as icf:
                                        review_dockets_data = json.load(icf)
                                    if isinstance(review_dockets_data, list) and all(
                                            isinstance(item, dict) and 'court_id' in item and 'docket_num' in item
                                            for item in review_dockets_data
                                    ):
                                        # Filter out ignore_download cases from review processing
                                        try:
                                            from src.pacer.docket_processor import DocketProcessor
                                            ignore_download_config = DocketProcessor.load_ignore_download_config()
                                            filtered_review_dockets = []
                                            excluded_count = 0
                                            
                                            for item in review_dockets_data:
                                                court_id = item.get('court_id')
                                                if court_id and DocketProcessor.check_ignore_download_static(court_id, ignore_download_config):
                                                    logging.debug(f"Excluding ignore_download case from review processing: {court_id}/{item.get('docket_num')}")
                                                    excluded_count += 1
                                                else:
                                                    filtered_review_dockets.append(item)
                                            
                                            docket_list_from_flags.extend(filtered_review_dockets)
                                            logging.info(f"Loaded {len(filtered_review_dockets)} dockets from {cases_path}. Excluded {excluded_count} ignore_download cases.")
                                            
                                        except Exception as e:
                                            logging.warning(f"Error filtering ignore_download cases from review list: {e}. Using unfiltered list.")
                                            docket_list_from_flags.extend(review_dockets_data)
                                            logging.info(f"Loaded {len(review_dockets_data)} dockets from {cases_path}.")
                                    else:
                                        logging.warning(
                                            f"File {cases_path} does not contain a valid list of docket objects. Skipping.")
                                except json.JSONDecodeError:
                                    logging.error(f"Error decoding JSON from {cases_path}. Skipping.")
                                except Exception as e_load_review:
                                    logging.error(f"Error loading {cases_path}: {e_load_review}")
                            else:
                                logging.warning(
                                    f"review_cases_final.json not found at {review_cases_path}. No dockets loaded for {flag_name} mode.")

                    if params.get('reprocess_failed'):
                        logging.info(
                            "reprocess_failed is True. Attempting to load failed downloads from docket files.")
                        if iso_date:
                            try:
                                failed_downloads_data = load_failed_downloads(base_data_dir, iso_date)
                                if failed_downloads_data:
                                    docket_list_from_flags.extend(failed_downloads_data)
                                    logging.info(
                                        f"Loaded {len(failed_downloads_data)} failed downloads for reprocessing.")

                                    # Save failed downloads to logs directory for reference
                                    logs_directory = Path(base_data_dir) / iso_date / "logs"
                                    logs_directory.mkdir(parents=True, exist_ok=True)
                                    failed_downloads_path = logs_directory / "failed_downloads.json"
                                    with open(failed_downloads_path, 'w') as f:
                                        json.dump(failed_downloads_data, f, indent=4)
                                    logging.info(f"Saved failed downloads list to {failed_downloads_path}")
                                else:
                                    logging.info("No failed downloads found for reprocessing.")
                            except Exception as e_load_failed:
                                logging.error(f"Error loading failed downloads: {e_load_failed}")

                    # Set the combined docket list if any flags were processed
                    if docket_list_from_flags:
                        params['docket_list_for_orchestrator'] = docket_list_from_flags
                        logging.info(
                            f"Combined docket list from flags: {len(docket_list_from_flags)} total dockets.")
                        # Clear other docket-related params to avoid conflict
                        params['process_single_court'] = []
                        params['docket_num'] = None
                        if 'multiple_courts' in yaml_config:  # If user also specified multiple_courts, log a warning
                            logging.warning(
                                "process_review_cases, process_review_cases_legacy, or reprocess_failed is True, overriding 'multiple_courts' from YAML.")

                    # If not processing special flags (docket_list_from_flags is empty),
                    # then check for 'multiple_courts' from YAML.
                    elif 'multiple_courts' in yaml_config: # Check if key exists in original YAML
                        mc_data = yaml_config.get('multiple_courts')
                        valid_mc_dockets = []
                        if isinstance(mc_data, list):
                            valid_mc_dockets = [
                                item for item in mc_data
                                if isinstance(item, dict) and 'court_id' in item and 'docket_num' in item
                            ]

                        if valid_mc_dockets: # Only if 'multiple_courts' provided valid, non-empty dockets
                            params['docket_list_for_orchestrator'] = valid_mc_dockets
                            logging.info(
                                f"Loaded 'docket_list_for_orchestrator' from YAML key 'multiple_courts': {len(valid_mc_dockets)} items.")
                            # Clear other specific docket/court params as multiple_courts takes precedence
                            params['process_single_court'] = []
                            params['docket_num'] = None
                            logging.debug(
                                "Clearing 'process_single_court' and 'docket_num' as 'multiple_courts' is used.")
                        elif mc_data is not None: # If mc_data was provided (e.g., empty list [], or malformed non-empty list) but yielded no valid_mc_dockets
                            logging.info(
                                f"'multiple_courts' key in YAML provided data ('{str(mc_data)[:100]}...'), but it was empty or contained only invalid/malformed entries. "
                                "It will not populate 'docket_list_for_orchestrator' or clear other court/docket params."
                            )
                        # If mc_data was None (key not present or value null in YAML), this block does nothing further,
                        # leaving params['docket_list_for_orchestrator'] as None and not clearing other params.
                else:
                    logging.warning(f"YAML config file {args.params} is empty.")
        except FileNotFoundError:
            logging.error(f"Error: YAML config file not found at {args.params}")
            sys.exit(1)
        except yaml.YAMLError as e:
            logging.error(f"Error parsing YAML config file {args.params}: {e}")
            sys.exit(1)
        except Exception as e:
            logging.error(f"Unexpected error loading YAML config file {args.params}: {e}")
            sys.exit(1)

    logging.info(
        f"Final parameters: {json.dumps(params, indent=2, default=str)}")

    # --- Load Configuration (Initial Setup, this one sets up logging properly) ---
    try:
        initial_config = load_config(
            end_date=params.get('date'),
            start_date=params.get('start_date'),
            testing=params.get('testing', False),
            use_proxy=params.get('use_proxy', False),
            headless=params.get('headless', True)
        )
        initial_config['DATA_DIR'] = base_data_dir
        initial_config['data_path'] = base_data_dir
        initial_config['log_dir'] = initial_config.get('directories', {}).get(
            'log_dir',
            os.path.join(base_data_dir, initial_config.get('iso_date', 'unknown_date'), 'logs')
        )

    except Exception as config_err:
        logging.basicConfig(level=logging.ERROR)
        logging.error(f"CRITICAL: Failed to load initial configuration: {config_err}", exc_info=True)
        sys.exit(1)

    setup_rich_logging(initial_config)
    logger = logging.getLogger(__name__)
    logger.info(f"Initial logging setup complete. Log directory: {initial_config.get('log_dir')}")
    logger.info(f"Base data directory: {base_data_dir}")

    # --- Apply Court Filtering Logic for Report Scraping Mode ---
    # This filtering applies IF 'scraper' is true, AND
    # NEITHER 'docket_list_for_orchestrator' is set (by multiple_courts, process_review_cases, or process_review_cases_legacy)
    # NOR 'docket_num' (for single specific docket) is set
    # AND 'process_single_court' is initially empty (meaning all courts are intended for report scraping).
    if params.get('scraper') and \
            not params.get('docket_list_for_orchestrator') and \
            not params.get('docket_num') and \
            not params.get('process_single_court'):  # Check if initially empty, not after potential YAML update
        logger.info(
            "Scraper enabled for report mode (no specific dockets/courts defined in params). Fetching/filtering full court list...")
        try:
            all_courts = await get_court_ids_async()
            if not all_courts:
                logger.warning("Could not retrieve court IDs for report scraping. Scraping will target no courts.")
                params['process_single_court'] = []
            else:
                logger.info(f"Retrieved {len(all_courts)} court IDs for potential report scraping.")
                filtered_courts = sorted(all_courts)
                skip_list = params.get('skip_courts', [])
                if skip_list:
                    filtered_courts = [c for c in filtered_courts if c not in skip_list]
                    logger.info(
                        f"Applied skip_courts filter. Remaining for report scraping: {len(filtered_courts)} courts.")
                start_after = params.get('start_after_court')
                start_at = params.get('start_at_court')
                start_index = 0
                if start_at and start_after:
                    logger.warning(
                        f"Both 'start_at_court' ({start_at}) and 'start_after_court' ({start_after}) are set. Prioritizing 'start_at_court'.")
                    start_after = None
                if start_at:
                    try:
                        start_index = next(i for i, c in enumerate(filtered_courts) if c >= start_at)
                    except StopIteration:
                        start_index = len(filtered_courts)
                elif start_after:
                    try:
                        start_index = next(i for i, c in enumerate(filtered_courts) if c > start_after)
                    except StopIteration:
                        start_index = len(filtered_courts)

                params['process_single_court'] = filtered_courts[start_index:]
                if not params['process_single_court']:
                    logger.warning("Report scraping: Filtering resulted in an empty list of courts.")
                else:
                    logger.info(
                        f"Report scraping: Will target {len(params['process_single_court'])} courts after filtering.")
        except Exception as filter_err:
            logger.error(f"Error during court ID fetching/filtering for report scraping: {filter_err}", exc_info=True)
            params['process_single_court'] = []

    # --- Decide Run Mode (Single Date vs Date Range) ---
    process_start_date = params.get('start_date')
    process_end_date = params.get('end_date')
    single_date_run = params.get('date')
    run_in_date_range_mode = False

    if process_start_date and process_end_date:
        try:
            start_dt = DateUtils.parse_date(process_start_date, FORMAT_US_SHORT)
            end_dt = DateUtils.parse_date(process_end_date, FORMAT_US_SHORT)
            if start_dt and end_dt:
                if start_dt < end_dt:
                    run_in_date_range_mode = True
                elif start_dt == end_dt:
                    params['date'] = process_start_date
                    single_date_run = process_start_date
                else:
                    logger.error(f"Invalid date range: Start date {process_start_date} > end date {process_end_date}.")
                    sys.exit(1)
            else:
                logger.error(
                    f"Could not parse date range: start='{process_start_date}', end='{process_end_date}'. Use MM/DD/YY.")
                sys.exit(1)
        except ValueError:  # Should be caught by DateUtils.parse_date returning None
            logger.error(f"Invalid date format for range. Use MM/DD/YY.")
            sys.exit(1)
    elif not single_date_run:
        logger.error("No target date ('date') or valid date range ('start_date'/'end_date') specified.")
        sys.exit(1)

    # --- Initialize and Run ---
    if run_in_date_range_mode:
        logger.warning(
            "Running in Date Range mode. Report Generation (ReportOrchestrator) will be SKIPPED within the loop.")
        date_processor_params = params.copy()
        date_processor_params['base_data_path'] = base_data_dir
        date_processor_params['report_generator'] = False  # Disable report gen inside loop
        date_processor = DateProcessor(date_processor_params)
        try:
            results = date_processor.check_and_process(process_start_date, process_end_date)
            logger.info(f"Date range processing finished. Results: {results}")
        except Exception as e:
            logger.critical(f"Unhandled error during DateProcessor run: {str(e)}", exc_info=True)
            sys.exit(1)
    else:  # Single date run
        target_date = single_date_run
        logger.info(f"Preparing for single date run: {target_date}")
        try:
            # This load_config is the one used for the actual MainProcessor/ReportOrchestrator run
            final_config = load_config(
                end_date=target_date, start_date=params.get('start_date'),
                testing=params.get('testing', False), use_proxy=params.get('use_proxy', False),
                headless=params.get('headless', True)
            )
            final_config['DATA_DIR'] = base_data_dir
            final_config['data_path'] = base_data_dir
            final_config['log_dir'] = initial_config.get('log_dir')  # Use log_dir from first config
            final_config['llm_provider'] = params.get('llm_provider', final_config.get('llm_provider', 'deepseek'))
            if 'force_openrouter_paid' in params:
                final_config['force_openrouter_paid'] = params['force_openrouter_paid']
            # Re-setup logging with the final config for the *single date* to ensure logs go to correct YYYYMMDD folder
            setup_rich_logging(final_config)
            logger.info(f"Logging re-configured for target date {target_date}. Log dir: {final_config.get('log_dir')}")
        except Exception as config_err:
            logger.critical(f"Failed to load final config for {target_date}: {config_err}", exc_info=True)
            sys.exit(1)

        run_main_processor = any([
            params.get('scraper'),  # This is the master flag for any scraping
            # params.get('docket_num'), # Already covered by scraper
            # params.get('docket_list_for_orchestrator'), # Already covered by scraper
            params.get('post_process'), isinstance(params.get('reprocess_files'), (list, set)),
            params.get('reprocess_files') is True, params.get('start_from_incomplete'),
            params.get('upload'), params.get('fb_ads')
        ])
        run_report_orchestrator = params.get('report_generator')
        # Report might depend on processing/upload if they modify data used by reports
        processing_upload_needed_for_report = any([
            params.get('post_process'), isinstance(params.get('reprocess_files'), (list, set)),
            params.get('reprocess_files') is True, params.get('start_from_incomplete'), params.get('upload')
        ])
        report_depends_on_main_processor = run_report_orchestrator and processing_upload_needed_for_report
        main_processor_success = True  # Assume success unless MainProcessor fails

        if run_main_processor:
            logger.info("--- Running MainProcessor Tasks ---")
            processor = MainProcessor(params, final_config)
            try:
                await processor.run()
                logger.info("MainProcessor run completed.")
            except Exception as e:
                logger.critical(f"Error during MainProcessor run: {str(e)}", exc_info=True)
                main_processor_success = False
                if report_depends_on_main_processor:
                    logger.error("MainProcessor failed; dependent report generation aborted.")

        if run_report_orchestrator:
            if not report_depends_on_main_processor or main_processor_success:
                logger.info(f"--- Running Report Orchestrator ---")
                try:
                    # Check if weekly report is requested
                    weekly_report = params.get('weekly', False)
                    skip_ads_param = params.get('skip_ads', False)
                    skip_invalidate_param = params.get('skip_invalidate', False)

                    if weekly_report:
                        logger.info("Generating WEEKLY report (7-day period)")
                        from src.reports.orchestrator_weekly import WeeklyReportOrchestrator
                        reports = WeeklyReportOrchestrator(final_config)
                        await reports.generate_report(
                            skip_ads=skip_ads_param, 
                            skip_invalidate=skip_invalidate_param
                        )
                        logger.info("Weekly report generation completed")
                    else:
                        logger.info("Generating standard DAILY report")
                        # ReportOrchestrator now uses the final_config which corresponds to the target_date
                        report_orchestrator = ReportsOrchestrator(final_config)
                        await report_orchestrator.run(
                            skip_ads=skip_ads_param,
                            skip_invalidate=skip_invalidate_param
                        )
                        logger.info("Daily report generation completed")
                    
                    logger.info(f"Report Orchestrator run completed for {target_date}.")
                except Exception as e:
                    logger.critical(f"Error during ReportOrchestrator: {str(e)}", exc_info=True)
            elif report_depends_on_main_processor and not main_processor_success:
                logger.error("Skipping Report Orchestrator due to MainProcessor failure.")

        if not run_main_processor and not run_report_orchestrator:
            logger.info("No actions enabled. Processing skipped for this date.")

    logger.info("Main function execution finished.")
    
    # Explicit cleanup for any remaining aiohttp sessions or resources
    await cleanup_async_resources()


async def cleanup_async_resources():
    """Cleanup any remaining async resources to prevent unclosed aiohttp sessions."""
    import logging
    cleanup_logger = logging.getLogger(__name__)
    
    try:
        import gc
        import aiohttp
        import asyncio
        
        # Check for any open aiohttp sessions and warn
        open_sessions = []
        for obj in gc.get_objects():
            if isinstance(obj, aiohttp.ClientSession) and not obj.closed:
                open_sessions.append(obj)
        
        if open_sessions:
            cleanup_logger.warning(f"Found {len(open_sessions)} unclosed aiohttp sessions - attempting to close them")
            for session in open_sessions:
                try:
                    await session.close()
                except Exception as e:
                    cleanup_logger.warning(f"Error closing session: {e}")
        
        # Force garbage collection to clean up any dangling references
        gc.collect()
        
        # Wait a brief moment for any pending cleanup
        await asyncio.sleep(0.1)
        
        cleanup_logger.info("Async resources cleanup completed")
    except Exception as e:
        cleanup_logger.warning(f"Error during async cleanup: {e}")


def safe_run_main():
    """Safely run the main async function with cleanup."""
    loop = None
    exit_code = 0
    main_task = None

    try:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        main_task = loop.create_task(main(), name="MainApplicationTask")
        loop.run_until_complete(asyncio.wait_for(main_task, timeout=7200))  # 2 hour timeout

    except KeyboardInterrupt:
        print("\nProcess interrupted by user (KeyboardInterrupt)")
        logging.warning("Process interrupted by user.")
        exit_code = 130
        if main_task and not main_task.done():
            main_task.cancel()
    except asyncio.TimeoutError:
        print("\nProcess timed out.")
        logging.error("Main execution timed out.")
        exit_code = 1
        if main_task and not main_task.done():
            main_task.cancel()
    except SystemExit as e:
        print(f"Exiting program (Code: {e.code}).")
        logging.warning(f"Exiting program (Code: {e.code}).")
        exit_code = e.code if isinstance(e.code, int) else 1
    except Exception as e:
        logging.critical(f"Critical error in safe_run_main execution: {str(e)}", exc_info=True)
        exit_code = 1
        if main_task and not main_task.done():
            main_task.cancel()
    finally:
        logging.info("Entering final cleanup phase...")
        if loop:
            try:
                if main_task and (main_task.done() or main_task.cancelled()):
                    if main_task.cancelled():
                        logging.info("Main task was cancelled. Attempting to gather results/exceptions from it.")
                    try:
                        # This ensures any exception from the main_task (if not already retrieved) is raised/logged
                        loop.run_until_complete(main_task)
                    except asyncio.CancelledError:
                        logging.info("Main task successfully processed cancellation.")
                    except Exception as e_main_task_final:
                        logging.error(f"Exception from awaiting main_task in finally: {e_main_task_final}",
                                      exc_info=True)

                logging.info("Gathering all remaining tasks after main task completion/cancellation...")
                pending_tasks = [task for task in asyncio.all_tasks(loop=loop) if not task.done()]

                if pending_tasks:
                    logging.info(f"Found {len(pending_tasks)} pending tasks. Attempting to gather them with a timeout.")
                    try:
                        # Give more time for pending tasks to complete their own cleanup.
                        loop.run_until_complete(asyncio.wait(pending_tasks, timeout=20.0))
                    except asyncio.TimeoutError:
                        logging.warning("Timeout waiting for pending tasks to complete. Proceeding to cancel.")

                    final_pending_tasks = [task for task in asyncio.all_tasks(loop=loop) if not task.done()]
                    if final_pending_tasks:
                        logging.info(f"Cancelling {len(final_pending_tasks)} stubborn tasks...")
                        for task in final_pending_tasks:
                            if not task.done() and not task.cancelling():  # Check if not already cancelling
                                task.cancel()
                        # Wait for cancellations to be processed
                        loop.run_until_complete(asyncio.gather(*final_pending_tasks, return_exceptions=True))
                        logging.info("Stubborn tasks processed for cancellation.")
                else:
                    logging.info("No other pending tasks found after main task.")

                # Grace period for final callbacks BEFORE stopping the loop
                if loop.is_running():
                    logging.info(
                        "Loop is running. Sleeping for a short moment for final Playwright/network callbacks...")
                    try:
                        # Increased sleep duration
                        loop.run_until_complete(asyncio.sleep(1.0))
                        logging.info("Short grace sleep completed.")
                    except RuntimeError as e_sleep_runtime:
                        logging.debug(
                            f"Could not run final grace sleep, loop might have been stopped: {e_sleep_runtime}")
                    except Exception as e_final_sleep:
                        logging.error(f"Error during final grace sleep: {e_final_sleep}", exc_info=True)

                # Explicitly run garbage collection before stopping/closing the loop.
                if not loop.is_closed():
                    import gc
                    logging.info("Explicitly running garbage collection (first pass)...")
                    gc.collect()

                    # Give the loop a chance to process tasks scheduled by __del__ methods
                    try:
                        loop.run_until_complete(asyncio.sleep(0.1))  # Short yield
                    except RuntimeError:  # Loop might be closed by a __del__ method
                        pass

                    logging.info("Explicitly running garbage collection (second pass)...")
                    gc.collect()
                    logging.info("Garbage collection finished.")

                if loop.is_running():
                    logging.info("Stopping event loop...")
                    loop.stop()

            except Exception as e_task_cleanup:
                logging.error(f"Error during asyncio task cleanup: {e_task_cleanup}", exc_info=True)
            finally:
                if not loop.is_closed():
                    logging.info("Closing event loop...")
                    loop.close()
                    logging.info("Event loop closed.")
                else:
                    logging.info("Event loop was already closed.")

        asyncio.set_event_loop(None)
        logging.info("Current thread's event loop policy reset.")

        logging.info("Running synchronous cleanup_everything...")
        if 'cleanup_everything' in globals() and callable(cleanup_everything):
            try:
                cleanup_everything()
                logging.info("cleanup_everything completed.")
            except Exception as e_sync_cleanup:
                logging.error(f"Error in synchronous cleanup_everything: {e_sync_cleanup}", exc_info=True)
        else:
            logging.warning("cleanup_everything function not found, skipping synchronous cleanup call.")

        logging.info(f"safe_run_main finished with exit_code: {exit_code}")
        return exit_code


if __name__ == "__main__":
    # Setup debugging if enabled
    debug_enabled = os.environ.get('ENABLE_DEBUG', '').lower() in ('true', '1', 'yes')
    # if debug_enabled:
    #     try:
    #         import pydevd_pycharm
    #
    #         pydevd_pycharm.settrace(
    #             host='127.0.0.1',
    #             port=12345,
    #             stdoutToServer=False,  # Keep terminal output intact
    #             stderrToServer=False,
    #             suspend=False  # Don't suspend immediately
    #         )
    #         print("PyCharm debugger connected")
    #     except Exception as e:
    #         print(f"Failed to connect to debugger: {e}")

    exit_code = 0
    print(f'Starting main.py with Python {sys.version}')
    try:
        # Set start method appropriately for cross-platform compatibility
        if multiprocessing.get_start_method(allow_none=True) is None or sys.platform == 'win32':
            multiprocessing.set_start_method('spawn', force=True)
            print("Set multiprocessing start method to 'spawn'.")

        # Ensure cleanup utils are available before calling them
        if 'patch_resource_tracker' in globals() and callable(patch_resource_tracker):
            patch_resource_tracker()  # Apply patches early
        else:
            print("WARNING: patch_resource_tracker function not found.", file=sys.stderr)

        if 'register_cleanup' in globals() and callable(register_cleanup):
            register_cleanup()  # Setup signal and atexit handlers
        else:
            print("WARNING: register_cleanup function not found.", file=sys.stderr)

        # Call safe_run_main and store its exit code
        exit_code = safe_run_main()

    except Exception as e:
        # Catch errors during setup (logging might not be fully configured yet)
        print(f"FATAL SETUP ERROR: {str(e)}", file=sys.stderr)
        # Use basic logging if available, otherwise just print
        if logging.getLogger().handlers:  # Check if handlers are configured
            logging.critical(f"FATAL SETUP ERROR: {str(e)}", exc_info=True)
        exit_code = 1  # Exit with error
    finally:
        # The cleanup_resources call is now reliably handled by safe_run_main's finally block
        # and the atexit handler. Removing the redundant call here.
        print("Main script exit.")
        sys.exit(exit_code)  # Ensure script exits with correct code
