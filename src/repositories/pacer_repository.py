"""
PACER Repository - Data Access Layer
Handles all DynamoDB operations for PACER data
"""
from typing import Dict, List, Any, Optional, Union, Tuple
from datetime import datetime
from boto3.dynamodb.conditions import Key, Attr
from botocore.exceptions import ClientError
import logging
import pandas as pd

from src.infrastructure.storage.dynamodb_async import AsyncDynamoDBStorage as DynamoDBStorage


class PacerRepository:
    """Repository for PACER data access operations"""
    
    def __init__(self, storage: DynamoDBStorage):
        self.storage = storage
        self.table_name = 'Pacer'
        self.logger = logging.getLogger(__name__)
    
    def _map_fields_to_dynamodb(self, record: Dict[str, Any]) -> Dict[str, Any]:
        """Map snake_case field names to DynamoDB PascalCase field names"""
        # Comprehensive field mapping from snake_case to PascalCase
        field_mapping = {
            # Core identification fields
            'court_id': 'CourtId',
            'docket_num': 'DocketNum',
            'filing_date': 'FilingDate',
            'date_filed': 'DateFiled',
            
            # Date fields
            'added_date': 'AddedDate',
            'added_date_iso': 'AddedDateIso', 
            'added_on': 'AddedOn',
            
            # Case information
            'versus': 'Versus',
            'cause': 'Cause',
            'cause_from_report': 'CauseFromReport',
            'nos': 'Nos',
            'nos_from_report': 'NosFromReport',
            'source_page': 'SourcePage',
            'jurisdiction': 'Jurisdiction',
            'jury_demand': 'JuryDemand',
            'office': 'Office',
            'assigned_to': 'AssignedTo',
            'referred_to': 'ReferredTo',
            
            # Parties and attorneys
            'plaintiff': 'Plaintiff',
            'plaintiffs': 'Plaintiffs',
            'defendant': 'Defendant',
            'defendants': 'Defendants',
            'attorney': 'Attorney',
            'attorneys': 'Attorneys',
            'plaintiffs_gpt': 'PlaintiffsGpt',
            'attorneys_gpt': 'AttorneysGpt',
            
            # Law firm information
            'law_firm': 'LawFirm',
            'law_firms': 'LawFirms',
            
            # File and processing information
            'base_filename': 'BaseFilename',
            'new_filename': 'NewFilename',
            'original_filename': 'OriginalFilename',
            'is_downloaded': 'IsDownloaded',
            's3_html': 'S3Html',
            's3_link': 'S3Link',
            
            # Transfer information
            'is_removal': 'IsRemoval',
            'is_transferred': 'IsTransferred',
            'transferred_in': 'TransferredIn',
            'pending_cto': 'PendingCto',
            'transferee_court_id': 'TransfereeCourtId',
            'transferee_docket_num': 'TransfereeDocketNum',
            'transferor_court_id': 'TransferorCourtId',
            'transferor_docket_num': 'TransferorDocketNum',
            'transferor_docket_law_firm': 'TransferorDocketLawFirm',
            
            # MDL information
            'mdl_num': 'MdlNum',
            'mdl_cat': 'MdlCat',
            'lead_case': 'LeadCase',
            
            # Case content
            'title': 'Title',
            'summary': 'Summary',
            'allegations': 'Allegations',
            'flags': 'Flags',
            'reason_relevant': 'ReasonRelevant',
            
            # Processing metadata
            'html_only': 'HtmlOnly',
            'processing_notes': 'ProcessingNotes',
            '_processing_notes': 'ProcessingNotes',
        }
        
        # Boolean fields that need explicit conversion
        boolean_fields = {
            'IsRemoval', 'IsTransferred', 'TransferredIn', 'PendingCto', 'IsDownloaded', 'HtmlOnly'
        }
        
        mapped_record = {}
        for key, value in record.items():
            # Skip None values and internal fields starting with '_'
            if value is None or (key.startswith('_') and key not in field_mapping):
                continue
                
            # Use mapped field name if available, otherwise use original key
            mapped_key = field_mapping.get(key, key)
            
            # Ensure boolean fields are properly converted to boolean
            if mapped_key in boolean_fields:
                mapped_record[mapped_key] = bool(value)
            else:
                mapped_record[mapped_key] = value
            
        return mapped_record

    async def add_or_update_record(self, record: Dict[str, Any]) -> bool:
        """Add or update a PACER record with proper field mapping"""
        try:
            # Map fields to DynamoDB PascalCase format
            mapped_record = self._map_fields_to_dynamodb(record)
            
            # Ensure required fields are present
            if 'FilingDate' not in mapped_record or 'DocketNum' not in mapped_record:
                self.logger.error(f"Missing required fields (FilingDate or DocketNum) in record: {list(mapped_record.keys())}")
                return False
                
            await self.storage.put_item(self.table_name, mapped_record)
            return True
        except ClientError as e:
            error_code = e.response.get('Error', {}).get('Code', 'Unknown')
            if error_code == 'ProvisionedThroughputExceededException':
                self.logger.error(f"DynamoDB throughput exceeded after all retries for record: {mapped_record.get('DocketNum', 'unknown')}")
            else:
                self.logger.error(f"Error adding/updating record: {e}")
            return False
        except Exception as e:
            self.logger.error(f"Unexpected error adding/updating record: {e}", exc_info=True)
            return False
    
    async def get_by_filing_date_and_docket(
        self, filing_date: str, docket_num: str
    ) -> Optional[Dict[str, Any]]:
        """Get a record by filing date and docket number"""
        key = {'FilingDate': filing_date, 'DocketNum': docket_num}
        return await self.storage.get_item(self.table_name, key)
    
    async def query_by_filing_date(self, filing_date: str, projection_expression: Optional[str] = None) -> List[Dict[str, Any]]:
        """Query records by filing date with optional projection"""
        key_condition = Key('FilingDate').eq(filing_date)
        return await self.storage.query(
            self.table_name, 
            key_condition, 
            projection_expression=projection_expression
        )
    
    async def query_by_court_and_docket(
        self, court_id: str, docket_num: str
    ) -> List[Dict[str, Any]]:
        """Query records by court ID and docket number"""
        # Use GSI if available
        index_name = 'CourtId-DocketNum-index'
        key_condition = Key('CourtId').eq(court_id) & Key('DocketNum').eq(docket_num)
        
        try:
            return await self.storage.query(
                self.table_name,
                key_condition,
                index_name=index_name
            )
        except ClientError:
            # Fallback to scan if GSI doesn't exist
            filter_expression = (
                Attr('CourtId').eq(court_id) & 
                Attr('DocketNum').eq(docket_num)
            )
            return await self.storage.scan(
                self.table_name, 
                filter_expression=filter_expression
            )
    
    # Alias for backward compatibility
    query_pacer_by_court_id_and_docket_num = query_by_court_and_docket
    
    async def query_by_transferee_docket(
        self, court_id: str, docket_num: str
    ) -> List[Dict[str, Any]]:
        """Query by transferee court and docket"""
        index_name = 'TransfereeCourtIdDocketNum-index'
        key_condition = (
            Key('TransfereeCourtId').eq(court_id) & 
            Key('TransfereeDocketNum').eq(docket_num)
        )
        return await self.storage.query(
            self.table_name,
            key_condition,
            index_name=index_name
        )
    
    async def check_docket_exists(
        self, court_id: str, docket_num: str
    ) -> bool:
        """Check if a docket exists"""
        results = await self.query_by_court_and_docket(court_id, docket_num)
        return len(results) > 0
    
    async def docket_exists(
        self,
        filing_date: str,
        docket_num: str,
        court_id: Optional[str] = None
    ) -> Union[bool, List[Dict[str, Any]]]:
        """Check if a docket exists - backward compatibility method
        
        Returns the list of found items (empty list if not found) to match legacy behavior
        """
        if court_id:
            # Use GSI to query by court ID and docket number
            results = await self.query_by_court_and_docket(court_id, docket_num)
            return results
        else:
            # Use primary key if we have filing date and docket number
            key = {'FilingDate': filing_date, 'DocketNum': docket_num}
            item = await self.storage.get_item(self.table_name, key)
            return [item] if item else []
    
    async def get_docket_items(
        self, court_id: str, docket_num: str
    ) -> List[Dict[str, Any]]:
        """Get all items for a specific docket"""
        # Query main table
        main_results = await self.query_by_court_and_docket(court_id, docket_num)
        
        # Query as transferee
        transferee_results = await self.query_by_transferee_docket(court_id, docket_num)
        
        # Combine and deduplicate
        all_results = main_results + transferee_results
        seen = set()
        unique_results = []
        
        for item in all_results:
            key = (item.get('FilingDate'), item.get('DocketNum'))
            if key not in seen:
                seen.add(key)
                unique_results.append(item)
        
        return unique_results
    
    async def update_added_on_date(
        self, current_added_on: str, new_added_on: str
    ) -> int:
        """Update AddedOn date for all matching records"""
        # First, find all records with the current AddedOn date
        filter_expression = Attr('AddedOn').eq(current_added_on)
        items = await self.storage.scan(
            self.table_name,
            filter_expression=filter_expression
        )
        
        # Update each item
        update_count = 0
        for item in items:
            key = {
                'FilingDate': item['FilingDate'],
                'DocketNum': item['DocketNum']
            }
            update_expression = 'SET AddedOn = :new_date'
            expression_values = {':new_date': new_added_on}
            
            await self.storage.update_item(
                self.table_name,
                key,
                update_expression,
                expression_values
            )
            update_count += 1
        
        return update_count
    
    async def scan_all(self) -> List[Dict[str, Any]]:
        """Scan all records in the table"""
        return await self.storage.scan(self.table_name, None)
    
    async def query_by_date_range(
        self, start_date: str, end_date: str
    ) -> List[Dict[str, Any]]:
        """Query records within a date range"""
        # Since FilingDate is the partition key, we need to scan with filter
        filter_expression = (
            Attr('FilingDate').gte(start_date) & 
            Attr('FilingDate').lte(end_date)
        )
        return await self.storage.scan(
            self.table_name,
            filter_expression=filter_expression
        )
    
    async def query_by_mdl_and_date_range(
        self, mdl_num: str, start_date: str, end_date: str
    ) -> List[Dict[str, Any]]:
        """Query records by MDL number within a date range using GSI"""
        from datetime import datetime, timedelta
        
        results = []
        current_date = datetime.strptime(start_date, '%Y%m%d')
        end_date_obj = datetime.strptime(end_date, '%Y%m%d')
        
        index_name = 'MdlNum-FilingDate-index'
        
        while current_date <= end_date_obj:
            date_str = current_date.strftime('%Y%m%d')
            
            key_condition = Key('MdlNum').eq(mdl_num) & Key('FilingDate').eq(date_str)
            
            try:
                daily_records = await self.storage.query(
                    self.table_name,
                    key_condition,
                    index_name=index_name
                )
                results.extend(daily_records)
            except Exception as e:
                self.logger.warning(f"Failed to query MdlNum {mdl_num} for date {date_str}: {e}")
            
            current_date += timedelta(days=1)
        
        return results
    
    async def query_by_added_on_range(
        self, start_date: str, end_date: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """Query records by AddedOn date range"""
        if end_date:
            filter_expression = (
                Attr('AddedOn').gte(start_date) & 
                Attr('AddedOn').lte(end_date)
            )
        else:
            filter_expression = Attr('AddedOn').eq(start_date)
        
        return await self.storage.scan(
            self.table_name,
            filter_expression=filter_expression
        )
    
    def get_records_by_added_on(self, start_date: str, end_date: str) -> List[Dict[str, Any]]:
        """
        Synchronous method for backward compatibility with original data loader.
        Creates its own async storage context to avoid event loop conflicts.
        """
        import asyncio
        
        async def query_with_own_storage():
            from src.infrastructure.storage.dynamodb_async import AsyncDynamoDBStorage
            
            # Create thread-local async storage with same config
            storage_config = self.storage.config if hasattr(self.storage, 'config') else {}
            thread_storage = AsyncDynamoDBStorage(storage_config)
            
            async with thread_storage:
                # Create temporary repository with thread storage
                thread_repo = PacerRepository(thread_storage)
                return await thread_repo.query_by_added_on_range(start_date, end_date)
        
        try:
            return asyncio.run(query_with_own_storage())
        except Exception as e:
            self.logger.error(f"Error in get_records_by_added_on: {e}")
            return []
    
    async def query_by_mdl_num(self, mdl_num: str) -> List[Dict[str, Any]]:
        """Query records by MDL number"""
        index_name = 'MdlNum-FilingDate-index'
        key_condition = Key('MdlNum').eq(mdl_num)
        return await self.storage.query(
            self.table_name,
            key_condition,
            index_name=index_name
        )
    
    async def get_mdl_summary(self, date_str: str) -> pd.DataFrame:
        """
        Query the past 30 days of filings from the given date, group by MDL number,
        and return a sorted DataFrame with the total count for each MDL.
        MATCHES ORIGINAL PACER MANAGER LOGIC EXACTLY - includes deduplication and plaintiff weighting.

        :param date_str: The date to start querying from, in YYYYMMDD format
        :return: DataFrame with columns MdlNum and Total, sorted by Total in descending order
        """
        try:
            from datetime import datetime, timedelta
            
            # Convert input date to datetime
            end_date = datetime.strptime(date_str, '%Y%m%d')
            start_date = end_date - timedelta(days=29)  # 30 days including the end date

            items = []
            current_date = end_date

            # Query for each day in the 30-day range with ONLY the 3 fields we need
            while current_date >= start_date:
                filing_date = current_date.strftime('%Y%m%d')
                
                try:
                    # Query by FilingDate with projection to get ONLY: MdlNum, Versus, NumPlaintiffs
                    daily_records = await self.query_by_filing_date(
                        filing_date, 
                        projection_expression='MdlNum, Versus, NumPlaintiffs'
                    )
                    items.extend(daily_records)
                except Exception as e:
                    self.logger.warning(f"Failed to query FilingDate {filing_date}: {e}")
                
                current_date -= timedelta(days=1)

            self.logger.info(f"Total records retrieved for MDL summary (30 days): {len(items)}")
            
            if not items:
                self.logger.warning(f"No data found for MDL summary in 30-day range ending {date_str}")
                return pd.DataFrame(columns=['MdlNum', 'Total'])

            # Convert to DataFrame for processing
            df = pd.DataFrame(items)
            
            if 'MdlNum' not in df.columns:
                self.logger.warning("No MdlNum column found in results")
                return pd.DataFrame(columns=['MdlNum', 'Total'])
            
            # Filter out empty/null MDL numbers
            df_filtered = df[df['MdlNum'].notna() & (df['MdlNum'] != '') & (df['MdlNum'] != 'nan')]
            
            if df_filtered.empty:
                self.logger.warning("No records with valid MDL numbers")
                return pd.DataFrame(columns=['MdlNum', 'Total'])

            # CRITICAL STEP 1: Remove duplicates by MdlNum + Versus combination (same case appearing multiple times)
            if 'Versus' in df_filtered.columns:
                df_deduped = df_filtered.drop_duplicates(subset=['MdlNum', 'Versus'], keep='first')
            else:
                self.logger.warning("No Versus column for deduplication, using all records")
                df_deduped = df_filtered

            # CRITICAL STEP 2: Apply plaintiff weighting (NumPlaintiffs if available, else 1)
            if 'NumPlaintiffs' in df_deduped.columns:
                # Convert NumPlaintiffs to numeric, use 1 if null/invalid
                df_deduped = df_deduped.copy()
                df_deduped['NumPlaintiffs'] = pd.to_numeric(df_deduped['NumPlaintiffs'], errors='coerce').fillna(1).astype(int)
                # Use NumPlaintiffs as weight
                df_deduped['Weight'] = df_deduped['NumPlaintiffs']
            else:
                self.logger.warning("No NumPlaintiffs column, using weight of 1 for all records")
                df_deduped = df_deduped.copy()
                df_deduped['Weight'] = 1

            # CRITICAL STEP 3: Group by MDL and sum the weighted counts
            mdl_summary = df_deduped.groupby('MdlNum')['Weight'].sum().reset_index()
            mdl_summary.columns = ['MdlNum', 'Total']
            mdl_summary = mdl_summary.sort_values('Total', ascending=False).reset_index(drop=True)
            
            self.logger.info(f"Generated MDL summary for 30-day range ending {date_str}: {len(mdl_summary)} MDLs")
            
            return mdl_summary
            
        except Exception as e:
            self.logger.error(f"Error generating MDL summary for 30-day range ending {date_str}: {e}", exc_info=True)
            return pd.DataFrame(columns=['MdlNum', 'Total'])
    
    async def query_by_law_firm(self, law_firm: str, filing_date: Optional[str] = None) -> List[Dict[str, Any]]:
        """Query records by law firm and optionally by filing date"""
        index_name = 'LawFirm-FilingDate-index'
        
        if filing_date:
            # Query with both law firm and filing date
            key_condition = Key('LawFirm').eq(law_firm) & Key('FilingDate').eq(filing_date)
        else:
            # Query just by law firm
            key_condition = Key('LawFirm').eq(law_firm)
            
        return await self.storage.query(
            self.table_name,
            key_condition,
            index_name=index_name
        )
    
    async def update_transfer_info(
        self,
        filing_date: str,
        docket_num: str,
        transfer_info: Dict[str, Any]
    ) -> bool:
        """Update transfer information for a record"""
        key = {'FilingDate': filing_date, 'DocketNum': docket_num}
        
        # Build update expression
        update_parts = []
        expression_values = {}
        
        for field, value in transfer_info.items():
            placeholder = f":{field.lower()}"
            update_parts.append(f"{field} = {placeholder}")
            expression_values[placeholder] = value
        
        update_expression = "SET " + ", ".join(update_parts)
        
        try:
            await self.storage.update_item(
                self.table_name,
                key,
                update_expression,
                expression_values
            )
            return True
        except ClientError as e:
            self.logger.error(f"Error updating transfer info: {e}")
            return False
    
    async def update_item(
        self,
        key: Dict[str, Any],
        update_data: Dict[str, Any],
        consistent_read_verify: bool = False
    ) -> bool:
        """Update an item with given data - backward compatibility method"""
        # Delegate to update_transfer_info for now
        filing_date = key.get('FilingDate')
        docket_num = key.get('DocketNum')
        
        if not filing_date or not docket_num:
            self.logger.error("Missing required key fields for update")
            return False
        
        return await self.update_transfer_info(filing_date, docket_num, update_data)
    
    async def get_recent_filings(self, days: int = 7) -> List[Dict[str, Any]]:
        """Get recent filings from the last N days"""
        from datetime import datetime, timedelta
        
        results = []
        end_date = datetime.now()
        
        # Query each day individually
        for i in range(days):
            date = end_date - timedelta(days=i)
            date_str = date.strftime('%Y%m%d')
            
            # Query by filing date
            day_results = await self.query_by_filing_date(date_str)
            results.extend(day_results)
        
        return results
    
    async def query_transfer_info_async(
        self, court_id: str, docket_num: str
    ) -> Optional[Dict[str, Any]]:
        """Query transfer information for a specific court and docket
        
        Returns the first matching record that contains S3 link information,
        which is what the transformer expects for PDF lookup.
        
        Args:
            court_id: Court identifier
            docket_num: Docket number
            
        Returns:
            Single record dict with S3 link information, or None if not found
        """
        try:
            # Query records for this court and docket
            records = await self.query_by_court_and_docket(court_id, docket_num)
            
            if not records:
                return None
            
            # Look for a record with S3 link information
            for record in records:
                # Check for S3 link fields that the transformer looks for
                s3_link = record.get('S3Link') or record.get('s3_link')
                if s3_link and isinstance(s3_link, str) and s3_link.lower().endswith('.pdf'):
                    return record
            
            # If no record with S3 link found, return the first record
            # This maintains compatibility with the original behavior
            return records[0]
            
        except Exception as e:
            self.logger.error(f"Error querying transfer info for court={court_id}, docket={docket_num}: {e}")
            return None
    
    def query_transfer_info(
        self, court_id: str, docket_num: str
    ) -> Optional[Dict[str, Any]]:
        """Synchronous wrapper for query_transfer_info_async
        
        This method exists for backward compatibility with legacy code that expects
        synchronous database operations. It creates an event loop if none exists
        and executes the async method.
        
        Args:
            court_id: Court identifier
            docket_num: Docket number
            
        Returns:
            Single record dict with S3 link information, or None if not found
        """
        import asyncio
        
        try:
            # Try to get current event loop
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # If loop is running, we need to create a new thread
                import concurrent.futures
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(asyncio.run, self.query_transfer_info_async(court_id, docket_num))
                    return future.result()
            else:
                # Loop exists but not running, we can use it
                return loop.run_until_complete(self.query_transfer_info_async(court_id, docket_num))
        except RuntimeError:
            # No event loop exists, create one
            return asyncio.run(self.query_transfer_info_async(court_id, docket_num))
        except Exception as e:
            self.logger.error(f"Error in synchronous query_transfer_info wrapper: {e}")
            return None