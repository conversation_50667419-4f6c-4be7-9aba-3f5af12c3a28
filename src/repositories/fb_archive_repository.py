"""
Facebook Ad Archive Repository - Data Access Layer
"""
from typing import Dict, List, Any, Optional, Tuple, Set
from boto3.dynamodb.conditions import Key
from botocore.exceptions import ClientError
import logging

from src.infrastructure.storage.dynamodb_async import AsyncDynamoDBStorage


class FBArchiveRepository:
    """Repository for Facebook Ad Archive data access"""
    
    DEFAULT_TABLE_NAME = "FBAdArchive"
    
    def __init__(self, storage: AsyncDynamoDBStorage):
        self.storage = storage
        self.logger = logging.getLogger(__name__)
        self.table_name = self.DEFAULT_TABLE_NAME
    
    async def add_or_update_record(self, record: Dict[str, Any]) -> bool:
        """
        Add or update a single record
        
        Args:
            record: Record data with AdArchiveID and StartDate
            
        Returns:
            True if successful
        """
        try:
            await self.storage.put_item(self.table_name, record)
            return True
        except ClientError as e:
            self.logger.error(f"Failed to add/update record: {e}")
            return False
    
    def _validate_record(self, record: Dict[str, Any]) -> List[str]:
        """Validate record data and return list of issues"""
        issues = []
        
        # Check required fields
        if not record.get('AdArchiveID'):
            issues.append("Missing AdArchiveID")
        if not record.get('StartDate'):
            issues.append("Missing StartDate")
            
        # Check data types
        for field, value in record.items():
            if value is None:
                continue
                
            # Check for problematic values
            if isinstance(value, str) and len(value) > 10000:
                issues.append(f"Field {field} is too long ({len(value)} chars)")
                
            # Check for invalid characters that might cause DynamoDB issues
            if isinstance(value, str) and '\x00' in value:
                issues.append(f"Field {field} contains null characters")
                
        return issues

    async def put_item(self, record: Dict[str, Any]) -> None:
        """
        Put a single item to the table (compatible with orchestrator expectations)
        
        Args:
            record: Record data with AdArchiveID and StartDate
        """
        ad_archive_id = record.get('AdArchiveID', 'Unknown')
        start_date = record.get('StartDate', 'Unknown')
        
        # Validate record data
        validation_issues = self._validate_record(record)
        if validation_issues:
            self.logger.error(f"Record validation failed for {ad_archive_id}: {validation_issues}")
            # Continue with save attempt but log the issues
        
        try:
            self.logger.debug(f"Attempting to save FB ad item: AdArchiveID={ad_archive_id}, StartDate={start_date}")
            
            # Log record size for debugging
            record_size = len(str(record))
            if record_size > 50000:  # DynamoDB item size limit is 400KB
                self.logger.warning(f"Large record for {ad_archive_id}: {record_size} characters")
            
            await self.storage.put_item(self.table_name, record)
            self.logger.debug(f"Successfully saved FB ad item: AdArchiveID={ad_archive_id}")
        except ClientError as e:
            error_code = e.response['Error']['Code']
            error_message = e.response['Error']['Message']
            self.logger.error(f"DynamoDB ClientError saving ad {ad_archive_id}: Code={error_code}, Message={error_message}")
            
            # Log specific error details
            if error_code == 'ValidationException':
                self.logger.error(f"DynamoDB validation failed - check data types and field names")
            elif error_code == 'ConditionalCheckFailedException':
                self.logger.error(f"Conditional check failed - item may already exist with different conditions")
            elif error_code == 'ItemSizeTooLargeException':
                self.logger.error(f"Item size too large - record size: {len(str(record))} characters")
                
            self.logger.error(f"Failed record sample: {str(record)[:500]}...")  # Log first 500 chars
            raise
        except Exception as e:
            self.logger.error(f"Unexpected error saving ad {ad_archive_id}: {str(e)}")
            self.logger.error(f"Failed record sample: {str(record)[:500]}...")
            raise
    
    async def get_item(self, key: Dict[str, str]) -> Optional[Dict[str, Any]]:
        """
        Get a single item by primary key (compatible with orchestrator expectations)
        
        Args:
            key: Primary key with AdArchiveID and StartDate
            
        Returns:
            Item if found, None otherwise
        """
        return await self.storage.get_item(self.table_name, key)
    
    async def batch_add_or_update_records(self, records: List[Dict[str, Any]], overwrite: bool = True) -> Tuple[int, int]:
        """
        Batch add or update records (compatible with original interface)
        
        Args:
            records: List of records to save
            overwrite: Whether to overwrite existing records
            
        Returns:
            Tuple of (success_count, failure_count)
        """
        if not records:
            self.logger.info("No records to save in batch operation")
            return 0, 0
        
        self.logger.info(f"Starting batch operation for {len(records)} records (overwrite={overwrite})")
        success_count = 0
        failure_count = 0
        failed_records = []
        
        for i, record in enumerate(records):
            ad_archive_id = record.get('AdArchiveID', 'Unknown')
            try:
                await self.put_item(record)
                success_count += 1
                
                # Log progress every 50 items
                if (i + 1) % 50 == 0:
                    self.logger.info(f"Batch progress: {i + 1}/{len(records)} processed, {success_count} successful, {failure_count} failed")
                    
            except Exception as e:
                self.logger.error(f"Failed to save record {ad_archive_id} (index {i}): {str(e)}")
                failure_count += 1
                failed_records.append({
                    'ad_archive_id': ad_archive_id,
                    'index': i,
                    'error': str(e)
                })
        
        self.logger.info(f"Batch operation completed: {success_count} successful, {failure_count} failed out of {len(records)} total")
        
        if failed_records:
            self.logger.error(f"Failed records summary: {failed_records[:5]}")  # Log first 5 failures
            if len(failed_records) > 5:
                self.logger.error(f"... and {len(failed_records) - 5} more failures")
        
        return success_count, failure_count
    
    async def get_by_key(self, ad_archive_id: str, start_date: str) -> Optional[Dict[str, Any]]:
        """Get record by primary key"""
        key = {
            'AdArchiveID': ad_archive_id,
            'StartDate': start_date
        }
        return await self.storage.get_item(self.table_name, key)
    
    async def query_by_start_date(self, start_date: str) -> List[Dict[str, Any]]:
        """Query records by start date using GSI"""
        import asyncio
        self.logger.info(f"[FB_REPO] Querying StartDate-index for date: {start_date}")
        print(f"[FB_REPO] DEBUG: Querying with start_date={start_date}, type={type(start_date)}")
        try:
            # Add timeout to prevent hanging
            result = await asyncio.wait_for(
                self.storage.query(
                    table_name=self.table_name,
                    key_condition=Key('StartDate').eq(start_date),
                    index_name='StartDate-index'
                ),
                timeout=30.0  # 30 second timeout
            )
            self.logger.info(f"[FB_REPO] Query for {start_date} returned {len(result)} items")
            return result
        except asyncio.TimeoutError:
            self.logger.error(f"[FB_REPO] Query timeout for start date {start_date} after 30 seconds")
            return []
        except ClientError as e:
            self.logger.error(f"[FB_REPO] Failed to query by start date {start_date}: {e}")
            return []
        except Exception as e:
            self.logger.error(f"[FB_REPO] Unexpected error querying start date {start_date}: {e}")
            return []
    
    async def query_by_archive_id(self, ad_archive_id: str) -> List[Dict[str, Any]]:
        """Query all records for an archive ID"""
        try:
            return await self.storage.query(
                table_name=self.table_name,
                key_condition=Key('AdArchiveID').eq(ad_archive_id)
            )
        except ClientError as e:
            self.logger.error(f"Failed to query by archive ID: {e}")
            return []
    
    async def query_by_page_id(self, page_id: str) -> List[Dict[str, Any]]:
        """Query records by page ID using GSI"""
        try:
            return await self.storage.query(
                table_name=self.table_name,
                key_condition=Key('PageID').eq(page_id),
                index_name='PageID-StartDate-index'
            )
        except ClientError as e:
            self.logger.error(f"Failed to query by page ID: {e}")
            return []
    
    async def query_by_page_id_and_date(
        self, page_id: str, last_updated: str
    ) -> List[Dict[str, Any]]:
        """Query records by page ID and last updated date"""
        try:
            return await self.storage.query(
                table_name=self.table_name,
                key_condition=Key('PageID').eq(page_id) & Key('LastUpdated').eq(last_updated),
                index_name='PageID-LastUpdated-index'
            )
        except ClientError as e:
            self.logger.error(f"Failed to query by page ID and date: {e}")
            return []
    
    async def query_by_last_updated(self, last_updated: str) -> List[Dict[str, Any]]:
        """Query records by last updated date"""
        try:
            return await self.storage.query(
                table_name=self.table_name,
                key_condition=Key('LastUpdated').eq(last_updated),
                index_name='LastUpdated-index'
            )
        except ClientError as e:
            self.logger.error(f"Failed to query by last updated: {e}")
            return []
    
    async def query_by_last_updated_range(
        self, start_date: str, end_date: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """Query records by LastUpdated date range"""
        from boto3.dynamodb.conditions import Attr
        
        if end_date:
            filter_expression = (
                Attr('LastUpdated').gte(start_date) & 
                Attr('LastUpdated').lte(end_date)
            )
        else:
            filter_expression = Attr('LastUpdated').eq(start_date)
        
        return await self.storage.scan(
            self.table_name,
            filter_expression=filter_expression
        )
    
    async def batch_get_records(self, keys: List[Dict[str, str]]) -> List[Dict[str, Any]]:
        """
        Batch get multiple records
        
        Args:
            keys: List of primary keys (AdArchiveID, StartDate)
            
        Returns:
            List of records
        """
        if not keys:
            return []
        
        try:
            # DynamoDB batch_get_item can handle up to 100 items
            results = []
            for i in range(0, len(keys), 100):
                batch_keys = keys[i:i + 100]
                
                table = await self.storage.get_table(self.table_name)
                response = await self.storage.dynamodb.batch_get_item(
                    RequestItems={
                        self.table_name: {
                            'Keys': batch_keys,
                            'ConsistentRead': True
                        }
                    }
                )
                
                items = response.get('Responses', {}).get(self.table_name, [])
                results.extend(items)
                
                # Handle unprocessed keys
                unprocessed = response.get('UnprocessedKeys', {}).get(self.table_name, {}).get('Keys', [])
                if unprocessed:
                    self.logger.warning(f"{len(unprocessed)} keys were not processed")
            
            return results
            
        except ClientError as e:
            self.logger.error(f"Batch get failed: {e}")
            return []
    
    async def batch_write_records(self, records: List[Dict[str, Any]]) -> int:
        """
        Batch write multiple records
        
        Args:
            records: List of records to write
            
        Returns:
            Number of records successfully written
        """
        if not records:
            return 0
        
        success_count = 0
        try:
            table = await self.storage.get_table(self.table_name)
            
            # Process in batches of 25 (DynamoDB limit)
            for i in range(0, len(records), 25):
                batch = records[i:i + 25]
                
                async with table.batch_writer() as batch_writer:
                    for record in batch:
                        await batch_writer.put_item(Item=record)
                        success_count += 1
            
            return success_count
            
        except ClientError as e:
            self.logger.error(f"Batch write failed: {e}")
            return success_count
    
    async def delete_record(self, ad_archive_id: str, start_date: str) -> bool:
        """Delete a single record"""
        try:
            table = await self.storage.get_table(self.table_name)
            await table.delete_item(
                Key={
                    'AdArchiveID': ad_archive_id,
                    'StartDate': start_date
                }
            )
            return True
        except ClientError as e:
            self.logger.error(f"Failed to delete record: {e}")
            return False
    
    async def batch_delete_records(self, keys: List[Dict[str, str]]) -> int:
        """
        Batch delete multiple records
        
        Args:
            keys: List of primary keys to delete
            
        Returns:
            Number of records deleted
        """
        if not keys:
            return 0
        
        deleted_count = 0
        try:
            table = await self.storage.get_table(self.table_name)
            
            # Process in batches of 25
            for i in range(0, len(keys), 25):
                batch_keys = keys[i:i + 25]
                
                async with table.batch_writer() as batch_writer:
                    for key in batch_keys:
                        await batch_writer.delete_item(Key=key)
                        deleted_count += 1
            
            return deleted_count
            
        except ClientError as e:
            self.logger.error(f"Batch delete failed: {e}")
            return deleted_count
    
    async def update_attributes(
        self, 
        ad_archive_id: str, 
        start_date: str,
        updates: Dict[str, Any]
    ) -> bool:
        """
        Update specific attributes of a record
        
        Args:
            ad_archive_id: Primary key part 1
            start_date: Primary key part 2
            updates: Dictionary of attributes to update
            
        Returns:
            True if successful
        """
        if not updates:
            return True
        
        try:
            # Use dict interface for backward compatibility
            await self.storage.update_item(
                self.table_name,
                {'AdArchiveID': ad_archive_id, 'StartDate': start_date},
                updates  # Pass dict directly
            )
            
            return True
            
        except ClientError as e:
            self.logger.error(f"Failed to update attributes: {e}")
            return False
    
    async def remove_attributes(
        self,
        ad_archive_id: str,
        start_date: str,
        attributes: List[str]
    ) -> bool:
        """
        Remove specific attributes from a record
        
        Args:
            ad_archive_id: Primary key part 1
            start_date: Primary key part 2
            attributes: List of attribute names to remove
            
        Returns:
            True if successful
        """
        if not attributes:
            return True
        
        try:
            # Build remove expression
            expression_names = {}
            remove_parts = []
            
            for idx, attr in enumerate(attributes):
                attr_name = f"#attr{idx}"
                remove_parts.append(attr_name)
                expression_names[attr_name] = attr
            
            update_expression = "REMOVE " + ", ".join(remove_parts)
            
            table = await self.storage.get_table(self.table_name)
            await table.update_item(
                Key={'AdArchiveID': ad_archive_id, 'StartDate': start_date},
                UpdateExpression=update_expression,
                ExpressionAttributeNames=expression_names
            )
            
            return True
            
        except ClientError as e:
            self.logger.error(f"Failed to remove attributes: {e}")
            return False
    
    async def scan_all(
        self,
        filter_expression: Optional[Any] = None
    ) -> List[Dict[str, Any]]:
        """
        Scan entire table with optional filter
        
        Args:
            filter_expression: Optional filter expression
            
        Returns:
            List of all matching records
        """
        return await self.storage.scan(self.table_name, filter_expression)
    
    async def query_ad_archive_by_date(self, start_date: str, end_date: str) -> List[Dict[str, Any]]:
        """
        Query ads by date range (compatibility method for ad processor).
        This method queries each date individually since DynamoDB GSI only supports exact matches.
        
        Args:
            start_date: Start date in YYYYMMDD format
            end_date: End date in YYYYMMDD format
            
        Returns:
            List of ad records within the date range
        """
        from datetime import datetime, timedelta
        
        all_items = []
        current_date = datetime.strptime(start_date, '%Y%m%d')
        end_date_dt = datetime.strptime(end_date, '%Y%m%d')
        
        self.logger.info(f"Querying FB ads from {start_date} to {end_date}")
        
        while current_date <= end_date_dt:
            date_str = current_date.strftime('%Y%m%d')
            try:
                items = await self.query_by_start_date(date_str)
                all_items.extend(items)
            except Exception as e:
                self.logger.error(f"Error querying ads for date {date_str}: {e}")
            
            current_date += timedelta(days=1)
        
        self.logger.info(f"Total ads found in date range {start_date}-{end_date}: {len(all_items)}")
        return all_items

    async def get_unique_values(self, attribute: str) -> Set[str]:
        """
        Get unique values for a specific attribute
        
        Args:
            attribute: Attribute name
            
        Returns:
            Set of unique values
        """
        items = await self.scan_all()
        unique_values = set()
        
        for item in items:
            value = item.get(attribute)
            if value:
                unique_values.add(str(value))
        
        return unique_values