#!/usr/bin/env python3
"""
Configuration adapter for backward compatibility.

This module provides compatibility for legacy code that expects the old config loading interface.
During the migration to new architecture, this adapter provides the old `load_config` function
and PROJECT_ROOT constant.
"""

import os
import logging
from datetime import datetime
from typing import Dict, Any, Optional

# Load environment variables if available
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    pass

# PROJECT_ROOT constant used by legacy scripts
PROJECT_ROOT = os.getenv('PROJECT_ROOT', os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

# Set up logger
logger = logging.getLogger(__name__)


def load_config(
    end_date: Optional[str] = None,
    start_date: Optional[str] = None,
    testing: bool = False,
    use_proxy: bool = True,
    headless: bool = True,
    **kwargs
) -> Dict[str, Any]:
    """
    Legacy configuration loader for backward compatibility.
    
    This function provides a simplified configuration object that matches
    the old interface expected by legacy components.
    
    Args:
        end_date: Target date in various formats
        start_date: Start date (optional)
        testing: Testing mode flag
        use_proxy: Whether to use proxy
        headless: Whether to run in headless mode
        **kwargs: Additional configuration parameters
        
    Returns:
        Dict containing configuration values compatible with legacy code
    """
    logger.warning(
        "Using legacy config_adapter.load_config(). "
        "Consider migrating to src.config_models.loader.load_config() for new code."
    )
    
    # Parse date if provided
    target_date = None
    if end_date:
        try:
            # Try various date formats that legacy code might use
            if '/' in end_date:
                # MM/DD/YY or MM/DD/YYYY format
                target_date = datetime.strptime(end_date, '%m/%d/%y' if len(end_date.split('/')[2]) == 2 else '%m/%d/%Y')
            elif len(end_date) == 8:
                # YYYYMMDD format
                target_date = datetime.strptime(end_date, '%Y%m%d')
            else:
                # ISO format
                target_date = datetime.fromisoformat(end_date)
        except (ValueError, TypeError) as e:
            logger.warning(f"Could not parse date '{end_date}': {e}")
            target_date = datetime.now()
    else:
        target_date = datetime.now()
    
    # Create legacy-compatible configuration object
    config = {
        # Basic configuration
        'date': target_date.strftime('%m/%d/%y') if target_date else None,
        'testing': testing,
        'headless': headless,
        'use_proxy': use_proxy,
        
        # AWS Configuration from environment variables
        'aws_access_key_id': os.getenv('AWS_ACCESS_KEY_ID'),
        'aws_secret_access_key': os.getenv('AWS_SECRET_ACCESS_KEY'),
        'aws_region': (
            os.getenv('AWS_REGION') or 
            os.getenv('LEXGENIUS_AWS_REGION') or
            os.getenv('REGION_NAME') or
            'us-west-2'  # Default to us-west-2 to match project default
        ),
        
        # DynamoDB Configuration
        'dynamodb_table': os.getenv('DYNAMODB_TABLE', 'PacerDockets'),
        'fb_ads_table': os.getenv('FB_ADS_TABLE', 'FBAdArchive'),
        'law_firms_table': os.getenv('LAW_FIRMS_TABLE', 'LawFirms'),
        'district_courts_table': os.getenv('DISTRICT_COURTS_TABLE', 'DistrictCourts'),
        
        # S3 Configuration
        's3_bucket': os.getenv('S3_BUCKET', 'lexgenius-dockets'),
        'cloudfront_distribution_id': os.getenv('CLOUDFRONT_DISTRIBUTION_ID'),
        
        # PACER Configuration
        'pacer_username': os.getenv('PACER_USERNAME'),
        'pacer_password': os.getenv('PACER_PASSWORD'),
        
        # AI Service Configuration
        'openai_api_key': os.getenv('OPENAI_API_KEY'),
        'deepseek_api_key': os.getenv('DEEPSEEK_API_KEY'),
        'deepseek_base_url': os.getenv('DEEPSEEK_BASE_URL', 'https://api.deepseek.com'),
        
        # Mistral Configuration
        'mistral_api_key': os.getenv('MISTRAL_API_KEY'),
        'mistral_base_url': os.getenv('MISTRAL_BASE_URL', 'https://api.mistral.ai'),
        
        # Email Configuration
        'mailgun_api_key': os.getenv('MAILGUN_API_KEY'),
        'mailgun_domain': os.getenv('MAILGUN_DOMAIN'),
        
        # Project paths
        'project_root': PROJECT_ROOT,
        'data_root': os.getenv('LEXGENIUS_DATA_DIR', os.path.join(PROJECT_ROOT, 'data')),
        
        # Processing flags
        'scraper': kwargs.get('scraper', True),
        'post_process': kwargs.get('post_process', True),
        'upload': kwargs.get('upload', True),
        'report_generator': kwargs.get('report_generator', True),
        'fb_ads': kwargs.get('fb_ads', True),
        
        # Performance settings
        'num_workers': kwargs.get('num_workers', 4),
        'run_parallel': kwargs.get('run_parallel', True),
        'parallel': kwargs.get('parallel', True),
        
        # Process control
        'process_single_court': kwargs.get('process_single_court'),
        'process_single_file': kwargs.get('process_single_file'),
    }
    
    # Add any additional kwargs to the config
    config.update(kwargs)
    
    return config


def get_project_root() -> str:
    """Get the project root directory."""
    return PROJECT_ROOT


def get_config_path(config_name: str) -> str:
    """Get the path to a configuration file."""
    return os.path.join(PROJECT_ROOT, 'src', 'config', config_name)


def get_data_path(*args) -> str:
    """Get a path within the data directory."""
    data_root = os.getenv('LEXGENIUS_DATA_DIR', os.path.join(PROJECT_ROOT, 'data'))
    return os.path.join(data_root, *args)