"""
Law Firms Query Service - Business Logic
"""
from typing import List, Dict, Any, Optional, Tuple
import logging
from datetime import datetime

from src.repositories.law_firms_repository import LawFirmsRepository
from src.utils.dynamodb_utils import DynamoDBUtils


class LawFirmsQueryService:
    """Service for Law Firms queries and business logic"""
    
    def __init__(self, repository: LawFirmsRepository):
        self.repository = repository
        self.logger = logging.getLogger(__name__)
    
    async def add_or_update_firm(self, record: Dict[str, Any]) -> bool:
        """
        Add or update a law firm with data normalization
        
        Args:
            record: Law firm data
            
        Returns:
            True if successful
        """
        # Handle Facebook page_id mapping
        if 'page_id' in record and 'id' not in record:
            record['id'] = record['page_id']
            self.logger.debug(f"Mapped 'page_id' to 'id': {record['id']}")
        
        # Convert snake_case keys to PascalCase, and handle common fields
        pascal_record = {}
        for key, value in record.items():
            if '_' in key:
                # Convert snake_case to PascalCase
                pascal_key = DynamoDBUtils.snake_to_pascal_case(key)
            elif key.lower() in ['id', 'name']:
                # Common fields that should be capitalized
                pascal_key = key.capitalize()
            else:
                # Keep existing casing
                pascal_key = key
            pascal_record[pascal_key] = value
        record = pascal_record
        
        # Handle PageId to Id mapping if needed
        if 'PageId' in record and 'Id' not in record:
            record['Id'] = record['PageId']
            self.logger.debug(f"Mapped 'PageId' to 'Id': {record['Id']}")
        
        # Ensure ID field is properly named for DynamoDB
        if 'Id' in record and 'ID' not in record:
            record['ID'] = record.pop('Id')
        
        # Sanitize for DynamoDB
        record = DynamoDBUtils.sanitize_for_dynamodb(record)
        
        # Validate required fields
        if 'ID' not in record or 'Name' not in record:
            self.logger.error(
                f"Missing required fields. Found: {list(record.keys())}"
            )
            return False
        
        # Ensure string keys
        record['ID'] = str(record['ID'])
        record['Name'] = str(record['Name'])
        
        # Check if record exists
        existing = await self.repository.get_by_key(record['ID'], record['Name'])
        
        if existing:
            # Update only changed fields
            updates = {k: v for k, v in record.items() 
                      if k not in ['ID', 'Name'] and v != existing.get(k)}
            
            if updates:
                self.logger.info(
                    f"Updating {record['ID']}/{record['Name']} "
                    f"with {len(updates)} changes"
                )
                return await self.repository.update_attributes(
                    record['ID'], record['Name'], updates
                )
            else:
                self.logger.info(
                    f"No changes for {record['ID']}/{record['Name']}"
                )
                return True
        else:
            # Insert new record
            self.logger.info(
                f"Adding new firm {record['ID']}/{record['Name']}"
            )
            return await self.repository.add_or_update_record(record)
    
    async def search_firms(self, search_term: str) -> List[Dict[str, Any]]:
        """
        Search law firms by name or page alias
        
        Args:
            search_term: Term to search for
            
        Returns:
            List of matching firms
        """
        # Search in both name and alias
        name_results = await self.repository.search_by_name(search_term)
        alias_results = await self.repository.search_by_page_alias(search_term)
        
        # Combine and deduplicate results
        all_results = name_results + alias_results
        
        # Deduplicate by (ID, Name) key
        seen = set()
        unique_results = []
        
        for record in all_results:
            key = (record.get('ID'), record.get('Name'))
            if key not in seen and key[0] and key[1]:
                seen.add(key)
                unique_results.append(record)
        
        # Sort by name
        unique_results.sort(key=lambda x: x.get('Name', '').lower())
        
        self.logger.info(
            f"Found {len(unique_results)} unique firms matching '{search_term}'"
        )
        
        return unique_results
    
    async def get_firm_by_id(self, firm_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a specific firm by ID
        
        Args:
            firm_id: The firm ID
            
        Returns:
            First matching firm record or None
        """
        results = await self.repository.get_by_id(firm_id)
        
        if results:
            # Return the first result (should typically be only one)
            return results[0]
        
        return None
    
    async def update_all_firms_last_updated(
        self,
        new_date: Optional[str] = None
    ) -> Tuple[int, int]:
        """
        Update LastUpdated field for all firms
        
        Args:
            new_date: Date to set (defaults to today)
            
        Returns:
            Tuple of (success_count, total_count)
        """
        if not new_date:
            new_date = datetime.now().strftime('%Y-%m-%d')
        
        self.logger.info(f"Updating all firms with LastUpdated = {new_date}")
        
        # Get all records
        all_firms = await self.repository.scan_all()
        total_count = len(all_firms)
        
        # Update LastUpdated
        success_count = await self.repository.batch_update_last_updated(new_date)
        
        self.logger.info(
            f"Updated {success_count}/{total_count} firms successfully"
        )
        
        return success_count, total_count
    
    async def get_firms_statistics(self) -> Dict[str, Any]:
        """Get statistics about law firms"""
        all_firms = await self.repository.scan_all()
        
        # Calculate statistics
        stats = {
            'total_firms': len(all_firms),
            'firms_with_page_id': 0,
            'firms_with_alias': 0,
            'firms_with_categories': 0,
            'firms_with_website': 0,
            'firms_by_state': {},
            'recently_updated': 0
        }
        
        for firm in all_firms:
            # Count various attributes
            if firm.get('PageId'):
                stats['firms_with_page_id'] += 1
            
            if firm.get('PageAlias'):
                stats['firms_with_alias'] += 1
            
            if firm.get('Categories'):
                stats['firms_with_categories'] += 1
            
            if firm.get('Website'):
                stats['firms_with_website'] += 1
            
            # Count by state
            state = firm.get('State', 'Unknown')
            stats['firms_by_state'][state] = stats['firms_by_state'].get(state, 0) + 1
            
            # Check if recently updated (within last 30 days)
            last_updated = firm.get('LastUpdated')
            if last_updated:
                try:
                    update_date = datetime.strptime(last_updated, '%Y-%m-%d')
                    days_ago = (datetime.now() - update_date).days
                    if days_ago <= 30:
                        stats['recently_updated'] += 1
                except:
                    pass
        
        return stats
    
    async def export_firms_data(
        self,
        fields: Optional[List[str]] = None
    ) -> List[Dict[str, Any]]:
        """
        Export firms data with specific fields
        
        Args:
            fields: List of fields to include (None for all)
            
        Returns:
            List of firm records
        """
        if fields:
            # Create projection expression
            projection = ','.join(fields)
            records = await self.repository.scan_all(
                projection_expression=projection
            )
        else:
            records = await self.repository.scan_all()
        
        # Sort by name for consistent export
        records.sort(key=lambda x: x.get('Name', '').lower())
        
        return records