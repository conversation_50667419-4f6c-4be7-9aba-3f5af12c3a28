"""
Prompt Management Service - Handles prompt loading and formatting
"""
from typing import Dict, Optional
import json
from pathlib import Path
import logging


class PromptManager:
    """Service for managing AI prompts"""
    
    def __init__(self, prompts_dir: Path):
        self.prompts_dir = prompts_dir
        self.logger = logging.getLogger(__name__)
        self._prompts_cache: Dict[str, str] = {}
        self._load_prompts()
    
    def get_prompt(self, prompt_key: str) -> str:
        """
        Get prompt by key
        
        Args:
            prompt_key: Key for the prompt (e.g., 'classify_ad_system')
            
        Returns:
            Prompt content
            
        Raises:
            ValueError: If prompt not found
        """
        if prompt_key in self._prompts_cache:
            return self._prompts_cache[prompt_key]
        
        # Try to load from file if not in cache
        prompt_path = self._find_prompt_file(prompt_key)
        if prompt_path and prompt_path.exists():
            content = prompt_path.read_text(encoding='utf-8')
            self._prompts_cache[prompt_key] = content
            return content
        
        raise ValueError(f"Prompt not found: {prompt_key}")
    
    def reload_prompts(self) -> None:
        """Reload all prompts from disk"""
        self._prompts_cache.clear()
        self._load_prompts()
        self.logger.info(f"Reloaded {len(self._prompts_cache)} prompts")
    
    def list_prompts(self) -> Dict[str, str]:
        """List all available prompts with their first 100 chars"""
        return {
            key: content[:100] + "..." if len(content) > 100 else content
            for key, content in self._prompts_cache.items()
        }
    
    def update_prompt(self, prompt_key: str, content: str) -> None:
        """
        Update prompt content
        
        Args:
            prompt_key: Key for the prompt
            content: New prompt content
        """
        self._prompts_cache[prompt_key] = content
        
        # Find appropriate file path
        prompt_path = self._find_prompt_file(prompt_key)
        if prompt_path:
            prompt_path.write_text(content, encoding='utf-8')
            self.logger.info(f"Updated prompt: {prompt_key}")
        else:
            # Create new file
            category = self._determine_category(prompt_key)
            new_path = self.prompts_dir / category / f"{prompt_key}.md"
            new_path.parent.mkdir(parents=True, exist_ok=True)
            new_path.write_text(content, encoding='utf-8')
            self.logger.info(f"Created new prompt: {prompt_key}")
    
    def format_prompt(self, template: str, **kwargs) -> str:
        """
        Format prompt template with variables
        
        Args:
            template: Prompt template with {variable} placeholders
            **kwargs: Variables to substitute
            
        Returns:
            Formatted prompt
        """
        try:
            return template.format(**kwargs)
        except KeyError as e:
            self.logger.error(f"Missing variable in prompt template: {e}")
            raise ValueError(f"Missing required variable: {e}")
    
    def _load_prompts(self) -> None:
        """Load all prompts from the prompts directory"""
        if not self.prompts_dir.exists():
            self.logger.warning(f"Prompts directory not found: {self.prompts_dir}")
            return
        
        # Load .md files
        for prompt_file in self.prompts_dir.rglob("*.md"):
            relative_path = prompt_file.relative_to(self.prompts_dir)
            # Convert path to key: fb_ads/classify_system.md -> classify_system
            key = relative_path.stem
            
            try:
                content = prompt_file.read_text(encoding='utf-8')
                self._prompts_cache[key] = content
            except Exception as e:
                self.logger.error(f"Error loading prompt {prompt_file}: {e}")
        
        # Load prompts.json if exists
        prompts_json = self.prompts_dir / "prompts.json"
        if prompts_json.exists():
            try:
                with open(prompts_json, 'r', encoding='utf-8') as f:
                    json_prompts = json.load(f)
                    for key, content in json_prompts.items():
                        self._prompts_cache[key] = content
            except Exception as e:
                self.logger.error(f"Error loading prompts.json: {e}")
        
        self.logger.info(f"Loaded {len(self._prompts_cache)} prompts")
    
    def _find_prompt_file(self, prompt_key: str) -> Optional[Path]:
        """Find prompt file by key"""
        # Direct match
        for ext in ['.md', '.txt']:
            direct_path = self.prompts_dir / f"{prompt_key}{ext}"
            if direct_path.exists():
                return direct_path
        
        # Search in subdirectories
        for prompt_file in self.prompts_dir.rglob(f"{prompt_key}.*"):
            if prompt_file.suffix in ['.md', '.txt']:
                return prompt_file
        
        return None
    
    def _determine_category(self, prompt_key: str) -> str:
        """Determine category for a prompt based on its key"""
        categories = {
            'classify': 'fb_ads',
            'extract': 'post_processor',
            'attorney': 'post_processor',
            'summary': 'post_processor',
            'signature': 'post_processor',
            'fb': 'fb_ads',
            'pacer': 'pacer',
            'court': 'pacer'
        }
        
        for keyword, category in categories.items():
            if keyword in prompt_key.lower():
                return category
        
        return 'general'  # Default category


class PromptTemplates:
    """Common prompt templates"""
    
    CLASSIFICATION_TEMPLATE = """
Title: {title}
Body: {body}
Link: {link}
Image Text: {image_text}
Summary: {summary}

Please classify this advertisement according to the specified criteria.
"""
    
    EXTRACTION_TEMPLATE = """
Document Type: {doc_type}
Content Length: {content_length} characters

Content:
{content}

Please extract the requested information from this document.
"""
    
    SUMMARY_TEMPLATE = """
Text Length: {text_length} characters
Summary Type: {summary_type}

Text:
{text}

Please provide a {summary_type} summary of this text.
"""
    
    @staticmethod
    def format_classification(title: str, body: str, link: str, 
                            image_text: str = "", summary: str = "") -> str:
        """Format classification prompt"""
        return PromptTemplates.CLASSIFICATION_TEMPLATE.format(
            title=title,
            body=body,
            link=link,
            image_text=image_text,
            summary=summary
        )
    
    @staticmethod
    def format_extraction(content: str, doc_type: str = "legal document") -> str:
        """Format extraction prompt"""
        return PromptTemplates.EXTRACTION_TEMPLATE.format(
            doc_type=doc_type,
            content_length=len(content),
            content=content
        )
    
    @staticmethod
    def format_summary(text: str, summary_type: str = "concise") -> str:
        """Format summary prompt"""
        return PromptTemplates.SUMMARY_TEMPLATE.format(
            text_length=len(text),
            summary_type=summary_type,
            text=text
        )