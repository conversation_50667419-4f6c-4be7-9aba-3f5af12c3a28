"""
Batch Processing Service - Handles batch AI operations
"""
from typing import Dict, List, Any, Optional, Callable
import asyncio
import logging
from datetime import datetime
from collections import defaultdict

from src.infrastructure.external.deepseek_client import DeepSeekClient


class BatchProcessor:
    """Service for batch processing with AI models"""
    
    def __init__(self, client: DeepSeekClient):
        self.client = client
        self.logger = logging.getLogger(__name__)
        self.stats = defaultdict(int)
    
    async def process_batch(
        self,
        items: List[Dict[str, Any]],
        process_func: Callable,
        batch_size: int = 10,
        concurrency: int = 5,
        progress_callback: Optional[Callable] = None
    ) -> List[Dict[str, Any]]:
        """
        Process items in batches with concurrency control
        
        Args:
            items: List of items to process
            process_func: Async function to process each item
            batch_size: Number of items per batch
            concurrency: Maximum concurrent operations
            progress_callback: Optional callback for progress updates
            
        Returns:
            List of processed items
        """
        self.stats.clear()
        self.stats['total'] = len(items)
        self.stats['start_time'] = datetime.now()
        
        # Create batches
        batches = [items[i:i + batch_size] for i in range(0, len(items), batch_size)]
        self.logger.info(f"Processing {len(items)} items in {len(batches)} batches")
        
        # Process batches with semaphore
        semaphore = asyncio.Semaphore(concurrency)
        results = []
        
        async def process_batch_with_semaphore(batch, batch_idx):
            async with semaphore:
                batch_results = []
                for item in batch:
                    try:
                        result = await process_func(item)
                        batch_results.append(result)
                        self.stats['processed'] += 1
                        
                        if progress_callback:
                            await progress_callback(
                                processed=self.stats['processed'],
                                total=self.stats['total'],
                                current_item=item
                            )
                    except Exception as e:
                        self.logger.error(f"Error processing item: {e}")
                        self.stats['errors'] += 1
                        batch_results.append({
                            **item,
                            'error': str(e),
                            'status': 'failed'
                        })
                
                return batch_results
        
        # Process all batches
        tasks = [
            process_batch_with_semaphore(batch, idx) 
            for idx, batch in enumerate(batches)
        ]
        
        batch_results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Flatten results
        for batch_result in batch_results:
            if isinstance(batch_result, Exception):
                self.logger.error(f"Batch processing error: {batch_result}")
                self.stats['batch_errors'] += 1
            else:
                results.extend(batch_result)
        
        # Calculate final stats
        self.stats['end_time'] = datetime.now()
        self.stats['duration'] = (self.stats['end_time'] - self.stats['start_time']).total_seconds()
        self.stats['success_rate'] = (self.stats['processed'] - self.stats['errors']) / self.stats['total']
        
        self._log_stats()
        return results
    
    async def process_with_retry(
        self,
        items: List[Dict[str, Any]],
        process_func: Callable,
        max_retries: int = 3,
        retry_delay: float = 1.0,
        **kwargs
    ) -> List[Dict[str, Any]]:
        """
        Process items with automatic retry on failure
        
        Args:
            items: List of items to process
            process_func: Async function to process each item
            max_retries: Maximum number of retries per item
            retry_delay: Delay between retries in seconds
            **kwargs: Additional arguments for process_batch
            
        Returns:
            List of processed items
        """
        async def process_with_retry_logic(item):
            for attempt in range(max_retries + 1):
                try:
                    return await process_func(item)
                except Exception as e:
                    if attempt < max_retries:
                        self.logger.warning(
                            f"Retry {attempt + 1}/{max_retries} for item after error: {e}"
                        )
                        await asyncio.sleep(retry_delay * (attempt + 1))
                    else:
                        self.logger.error(f"Failed after {max_retries} retries: {e}")
                        raise
        
        return await self.process_batch(items, process_with_retry_logic, **kwargs)
    
    async def process_with_rate_limit(
        self,
        items: List[Dict[str, Any]],
        process_func: Callable,
        requests_per_minute: int = 60,
        **kwargs
    ) -> List[Dict[str, Any]]:
        """
        Process items with rate limiting
        
        Args:
            items: List of items to process
            process_func: Async function to process each item
            requests_per_minute: Maximum requests per minute
            **kwargs: Additional arguments for process_batch
            
        Returns:
            List of processed items
        """
        # Calculate delay between requests
        delay = 60.0 / requests_per_minute
        
        async def rate_limited_process(item):
            result = await process_func(item)
            await asyncio.sleep(delay)
            return result
        
        # Adjust concurrency based on rate limit
        max_concurrency = min(kwargs.get('concurrency', 5), requests_per_minute // 10)
        kwargs['concurrency'] = max_concurrency
        
        return await self.process_batch(items, rate_limited_process, **kwargs)
    
    async def chunk_and_process(
        self,
        text: str,
        chunk_size: int,
        process_func: Callable,
        combine_func: Callable,
        overlap: int = 0
    ) -> Dict[str, Any]:
        """
        Chunk text and process each chunk, then combine results
        
        Args:
            text: Text to chunk and process
            chunk_size: Size of each chunk in characters
            process_func: Function to process each chunk
            combine_func: Function to combine chunk results
            overlap: Number of characters to overlap between chunks
            
        Returns:
            Combined result
        """
        # Create chunks with optional overlap
        chunks = []
        start = 0
        
        while start < len(text):
            end = start + chunk_size
            chunk = text[start:end]
            chunks.append({
                'text': chunk,
                'start': start,
                'end': min(end, len(text))
            })
            start = end - overlap if overlap > 0 else end
        
        self.logger.info(f"Created {len(chunks)} chunks from {len(text)} characters")
        
        # Process chunks
        chunk_results = await self.process_batch(
            chunks,
            lambda chunk: process_func(chunk['text']),
            batch_size=5
        )
        
        # Combine results
        return combine_func(chunk_results)
    
    def get_stats(self) -> Dict[str, Any]:
        """Get processing statistics"""
        return dict(self.stats)
    
    def _log_stats(self):
        """Log processing statistics"""
        self.logger.info(
            f"Batch processing complete: "
            f"{self.stats['processed']}/{self.stats['total']} processed, "
            f"{self.stats['errors']} errors, "
            f"{self.stats['duration']:.2f}s duration, "
            f"{self.stats['success_rate']:.2%} success rate"
        )
    
    async def estimate_cost(
        self,
        items: List[Dict[str, Any]],
        estimate_func: Callable
    ) -> Dict[str, float]:
        """
        Estimate processing cost for items
        
        Args:
            items: List of items to process
            estimate_func: Function to estimate tokens/cost per item
            
        Returns:
            Cost estimation details
        """
        total_input_tokens = 0
        total_output_tokens = 0
        
        for item in items:
            tokens = await estimate_func(item)
            total_input_tokens += tokens.get('input_tokens', 0)
            total_output_tokens += tokens.get('output_tokens', 0)
        
        # Use client's cost calculation
        estimated_cost = self.client.calculate_cost(
            total_input_tokens,
            total_output_tokens
        )
        
        return {
            'total_items': len(items),
            'total_input_tokens': total_input_tokens,
            'total_output_tokens': total_output_tokens,
            'estimated_cost': estimated_cost,
            'cost_per_item': estimated_cost / len(items) if items else 0
        }