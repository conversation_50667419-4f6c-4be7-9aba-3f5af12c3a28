"""
District Courts Query Service - Business Logic
"""
from typing import List, Dict, Any, Optional
import logging

from src.repositories.district_courts_repository import DistrictCourtsRepository
from src.utils.dynamodb_utils import DynamoDBUtils


class DistrictCourtsQueryService:
    """Service for District Courts queries and business logic"""
    
    def __init__(self, repository: DistrictCourtsRepository):
        self.repository = repository
        self.logger = logging.getLogger(__name__)
    
    async def add_or_update_court(self, record: Dict[str, Any]) -> bool:
        """
        Add or update a court record with data sanitization
        
        Args:
            record: Court record data
            
        Returns:
            True if successful
        """
        # Convert snake_case keys to PascalCase
        pascal_record = {}
        for key, value in record.items():
            if '_' in key:
                # Convert snake_case to PascalCase
                pascal_key = DynamoDBUtils.snake_to_pascal_case(key)
            else:
                # Keep existing casing
                pascal_key = key
            pascal_record[pascal_key] = value
        
        # Sanitize for DynamoDB
        record = DynamoDBUtils.sanitize_for_dynamodb(pascal_record)
        
        # Validate required fields
        if 'CourtId' not in record or 'MdlNum' not in record:
            self.logger.error("Missing required fields: CourtId or MdlNum")
            return False
        
        # Check if record exists
        existing = await self.repository.get_by_key(
            record['CourtId'], record['MdlNum']
        )
        
        if existing:
            # Update only changed fields
            updates = {k: v for k, v in record.items() 
                      if k not in ['CourtId', 'MdlNum'] and v != existing.get(k)}
            
            if updates:
                self.logger.info(
                    f"Updating {record['CourtId']}/{record['MdlNum']} "
                    f"with {len(updates)} changes"
                )
                return await self.repository.update_attributes(
                    record['CourtId'], record['MdlNum'], updates
                )
            else:
                self.logger.info(
                    f"No changes for {record['CourtId']}/{record['MdlNum']}"
                )
                return True
        else:
            # Insert new record
            self.logger.info(
                f"Adding new record {record['CourtId']}/{record['MdlNum']}"
            )
            return await self.repository.add_or_update_record(record)
    
    async def get_court_by_transferee(
        self,
        transferee_court_id: str,
        transferee_docket_num: str
    ) -> List[Dict[str, Any]]:
        """Get courts by transferee information"""
        results = await self.repository.query_by_transferee_court_and_docket(
            transferee_court_id, transferee_docket_num
        )
        
        if results:
            self.logger.info(
                f"Found {len(results)} courts for transferee "
                f"{transferee_court_id}/{transferee_docket_num}"
            )
        else:
            self.logger.info(
                f"No courts found for transferee "
                f"{transferee_court_id}/{transferee_docket_num}"
            )
        
        return results
    
    async def get_courts_by_mdl(self, mdl_num: str) -> List[Dict[str, Any]]:
        """Get all courts for a specific MDL"""
        all_courts = await self.repository.scan_all()
        
        # Filter by MDL number
        mdl_courts = [
            court for court in all_courts
            if court.get('MdlNum') == mdl_num
        ]
        
        return sorted(mdl_courts, key=lambda x: x.get('CourtId', ''))
    
    async def get_court_statistics(self) -> Dict[str, Any]:
        """Get statistics about district courts"""
        all_courts = await self.repository.scan_all()
        
        # Calculate statistics
        mdl_counts = {}
        transferee_counts = {}
        
        for court in all_courts:
            # Count by MDL
            mdl = court.get('MdlNum', 'Unknown')
            mdl_counts[mdl] = mdl_counts.get(mdl, 0) + 1
            
            # Count by transferee court
            transferee = court.get('TransfereeCourtId')
            if transferee:
                transferee_counts[transferee] = transferee_counts.get(transferee, 0) + 1
        
        return {
            'total_courts': len(all_courts),
            'unique_mdls': len(mdl_counts),
            'mdl_distribution': mdl_counts,
            'transferee_distribution': transferee_counts,
            'courts_with_transfers': len([c for c in all_courts if c.get('TransfereeCourtId')])
        }
    
    async def find_transfer_chains(self) -> List[Dict[str, Any]]:
        """Find chains of court transfers"""
        all_courts = await self.repository.scan_all()
        
        # Build transfer map
        transfer_chains = []
        
        for court in all_courts:
            if court.get('TransfereeCourtId'):
                chain = {
                    'original': {
                        'court_id': court.get('CourtId'),
                        'docket_num': court.get('DocketNum'),
                        'mdl_num': court.get('MdlNum')
                    },
                    'transferee': {
                        'court_id': court.get('TransfereeCourtId'),
                        'docket_num': court.get('TransfereeDocketNum')
                    }
                }
                transfer_chains.append(chain)
        
        return transfer_chains