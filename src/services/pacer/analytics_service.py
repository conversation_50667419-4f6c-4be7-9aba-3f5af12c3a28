"""
PACER Analytics Service - Statistics and Reporting
"""
import logging
from collections import defaultdict
from typing import List, Dict, Tuple, Optional

import pandas as pd

from src.repositories.pacer_repository import PacerRepository


class PacerAnalyticsService:
    """Service for PACER analytics, statistics, and reporting"""

    def __init__(self, repository: PacerRepository):
        self.repository = repository
        self.logger = logging.getLogger(__name__)

    async def get_mdl_count_in_date_range(
            self, mdl_num: str, start_date: str, end_date: str
    ) -> int:
        """Get count of MDL filings in date range"""
        records = await self.repository.query_by_mdl_and_date_range(
            mdl_num, start_date, end_date
        )
        return len(records)

    async def get_filings_by_court(
            self, mdl_num: str, start_date: str, end_date: str
    ) -> pd.DataFrame:
        """Get filing counts by court for an MDL"""
        records = await self.repository.query_by_mdl_and_date_range(
            mdl_num, start_date, end_date
        )

        # Count by court
        court_counts = defaultdict(int)
        for record in records:
            court_id = record.get('CourtId', 'Unknown')
            court_counts[court_id] += 1

        # Convert to DataFrame
        data = [
            {'CourtId': court, 'Count': count}
            for court, count in court_counts.items()
        ]

        df = pd.DataFrame(data)
        return df.sort_values('Count', ascending=False)

    async def summarize_filings_by_law_firm_for_mdl(
            self, mdl_num: str, start_date: str, end_date: str
    ) -> Tuple[pd.DataFrame, int]:
        """Summarize filings by law firm for an MDL"""
        records = await self.repository.query_by_mdl_and_date_range(
            mdl_num, start_date, end_date
        )

        # Extract law firms
        law_firm_counts = defaultdict(int)
        total_filings = len(records)

        for record in records:
            # Check various law firm fields
            law_firms = set()

            # Add from different fields
            for field in ['LawFirm', 'LawFirms', 'PlaintiffAttorneys']:
                value = record.get(field)
                if isinstance(value, str) and value:
                    law_firms.add(value)
                elif isinstance(value, list):
                    law_firms.update(str(f) for f in value if f)

            # Count each firm once per filing
            for firm in law_firms:
                law_firm_counts[firm] += 1

        # Convert to DataFrame
        data = [
            {'LawFirm': firm, 'FilingCount': count}
            for firm, count in law_firm_counts.items()
        ]

        df = pd.DataFrame(data)
        df = df.sort_values('FilingCount', ascending=False)

        return df, total_filings

    async def get_pacer_records_df(self) -> pd.DataFrame:
        """Get all PACER records as a DataFrame"""
        records = await self.repository.scan_all()
        return pd.DataFrame(records)

    async def count_identical_court_docket_combinations(
            self) -> Dict[Tuple[str, str], int]:
        """Count identical court/docket combinations"""
        records = await self.repository.scan_all()

        combinations = defaultdict(int)
        for record in records:
            key = (record.get('CourtId'), record.get('DocketNum'))
            combinations[key] += 1

        # Return only duplicates
        return {k: v for k, v in combinations.items() if v > 1}

    async def get_dockets_for_bubble(
            self, start_date: str, end_date: str, court_id: Optional[str] = None
    ) -> pd.DataFrame:
        """Get dockets formatted for Bubble.io integration"""
        records = await self.repository.query_by_date_range(start_date, end_date)

        # Filter by court if specified
        if court_id:
            records = [r for r in records if r.get('CourtId') == court_id]

        # Format for Bubble
        bubble_data = []
        for record in records:
            bubble_data.append({
                'filing_date': record.get('FilingDate'),
                'docket_num': record.get('DocketNum'),
                'court_id': record.get('CourtId'),
                'title': record.get('Title', ''),
                'mdl_num': record.get('MdlNum', ''),
                'plaintiff': record.get('Plaintiff', ''),
                'defendant': record.get('Defendant', ''),
                'law_firm': record.get('LawFirm', ''),
                'added_on': record.get('AddedOn', ''),
                'pdf_count': record.get('PdfCount', 0)
            })

        return pd.DataFrame(bubble_data)

    async def get_mdl_summary(self, date_str: str) -> pd.DataFrame:
        """Get MDL summary statistics for a specific date"""
        records = await self.repository.query_by_filing_date(date_str)

        # Group by MDL
        mdl_groups = defaultdict(list)
        for record in records:
            mdl_num = record.get('MdlNum', 'Unknown')
            mdl_groups[mdl_num].append(record)

        # Calculate summaries
        summaries = []
        for mdl_num, mdl_records in mdl_groups.items():
            # Count by court
            court_counts = defaultdict(int)
            total_pdfs = 0

            for record in mdl_records:
                court_counts[record.get('CourtId', 'Unknown')] += 1
                total_pdfs += record.get('PdfCount', 0)

            summaries.append({
                'MDL': mdl_num,
                'TotalFilings': len(mdl_records),
                'UniqueCourtCount': len(court_counts),
                'TotalPDFs': total_pdfs,
                'TopCourt': max(court_counts.items(), key=lambda x: x[1])[0] if court_counts else 'N/A',
                'FilingDate': date_str
            })

        return pd.DataFrame(summaries).sort_values('TotalFilings', ascending=False)

    async def get_mdl_summary2(self, date_str: str) -> pd.DataFrame:
        """Alternative MDL summary with more details"""
        records = await self.repository.query_by_filing_date(date_str)

        # Group by MDL and calculate detailed stats
        mdl_stats = defaultdict(lambda: {
            'filings': 0,
            'courts': set(),
            'law_firms': set(),
            'plaintiffs': set(),
            'defendants': set(),
            'pdfs': 0
        })

        for record in records:
            mdl_num = record.get('MdlNum', 'Unknown')
            stats = mdl_stats[mdl_num]

            stats['filings'] += 1
            stats['courts'].add(record.get('CourtId', 'Unknown'))
            stats['pdfs'] += record.get('PdfCount', 0)

            # Add law firms
            for field in ['LawFirm', 'LawFirms', 'PlaintiffAttorneys']:
                value = record.get(field)
                if isinstance(value, str) and value:
                    stats['law_firms'].add(value)
                elif isinstance(value, list):
                    stats['law_firms'].update(str(f) for f in value if f)

            # Add parties
            if record.get('Plaintiff'):
                stats['plaintiffs'].add(record['Plaintiff'])
            if record.get('Defendant'):
                stats['defendants'].add(record['Defendant'])

        # Convert to DataFrame format
        summary_data = []
        for mdl_num, stats in mdl_stats.items():
            summary_data.append({
                'MDL': mdl_num,
                'Filings': stats['filings'],
                'UniqueCourts': len(stats['courts']),
                'UniqueLawFirms': len(stats['law_firms']),
                'UniquePlaintiffs': len(stats['plaintiffs']),
                'UniqueDefendants': len(stats['defendants']),
                'TotalPDFs': stats['pdfs'],
                'AvgPDFsPerFiling': stats['pdfs'] / stats['filings'] if stats['filings'] > 0 else 0
            })

        return pd.DataFrame(summary_data).sort_values('Filings', ascending=False)

    async def get_unique_law_firms(self) -> List[str]:
        """Extract all unique law firm names from the database"""
        records = await self.repository.scan_all()

        unique_firms = set()

        for record in records:
            # Check all law firm related fields
            law_firm_fields = [
                'LawFirm', 'LawFirms', 'PlaintiffAttorneys',
                'DefendantAttorneys', 'Attorneys'
            ]

            for field in law_firm_fields:
                value = record.get(field)
                if isinstance(value, str) and value:
                    unique_firms.add(value.strip())
                elif isinstance(value, list):
                    for firm in value:
                        if isinstance(firm, str) and firm:
                            unique_firms.add(firm.strip())

        # Sort and return
        return sorted(list(unique_firms))

    async def scan_to_dataframe(self) -> pd.DataFrame:
        """Scan entire table and return as DataFrame"""
        records = await self.repository.scan_all()
        return pd.DataFrame(records)
