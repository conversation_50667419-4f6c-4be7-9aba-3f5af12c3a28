"""
PACER Query Service - Business Logic for Complex Queries
"""
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
import logging

from src.repositories.pacer_repository import PacerRepository
from src.utils.date import DateUtils


class PacerQueryService:
    """Service for complex PACER queries and business logic"""
    
    def __init__(self, repository: PacerRepository):
        self.repository = repository
        self.logger = logging.getLogger(__name__)
    
    async def search_by_title(self, search_string: str) -> List[Dict[str, Any]]:
        """Search PACER records by title"""
        # Get all records and filter by title
        all_records = await self.repository.scan_all()
        
        search_lower = search_string.lower()
        results = []
        
        for record in all_records:
            title = record.get('Title', '').lower()
            if search_lower in title:
                results.append(record)
        
        return sorted(results, key=lambda x: x.get('FilingDate', ''), reverse=True)
    
    async def search_by_law_firm(self, law_firm: str, filing_date: Optional[str] = None) -> List[Dict[str, Any]]:
        """Search records by law firm name and optionally by filing date"""
        # Use repository method if available
        if filing_date and hasattr(self.repository, 'query_by_law_firm'):
            return await self.repository.query_by_law_firm(law_firm, filing_date)
        
        # Fallback to scanning
        all_records = await self.repository.scan_all()
        
        search_lower = law_firm.lower()
        results = []
        
        for record in all_records:
            # Check filing date if provided
            if filing_date and record.get('FilingDate') != filing_date:
                continue
                
            # Check in various law firm fields
            law_firm_fields = [
                'LawFirm', 'LawFirms', 'PlaintiffAttorneys', 
                'DefendantAttorneys', 'Attorneys'
            ]
            
            for field in law_firm_fields:
                field_value = record.get(field, '')
                if isinstance(field_value, str) and search_lower in field_value.lower():
                    results.append(record)
                    break
                elif isinstance(field_value, list):
                    for firm in field_value:
                        if search_lower in str(firm).lower():
                            results.append(record)
                            break
        
        return results
    
    async def search_by_law_firm_with_totals(
        self, law_firm: str
    ) -> Dict[str, Any]:
        """Search by law firm and calculate totals"""
        results = await self.search_by_law_firm(law_firm)
        
        # Calculate totals by MDL
        mdl_counts = {}
        for record in results:
            mdl_num = record.get('MdlNum', 'Unknown')
            mdl_counts[mdl_num] = mdl_counts.get(mdl_num, 0) + 1
        
        return {
            'results': results,
            'total_count': len(results),
            'mdl_breakdown': mdl_counts
        }
    
    async def search_by_defendant(self, defendant: str) -> List[Dict[str, Any]]:
        """Search records by defendant name"""
        all_records = await self.repository.scan_all()
        
        search_lower = defendant.lower()
        results = []
        
        for record in all_records:
            # Check defendant fields
            defendant_fields = ['Defendant', 'Defendants', 'Versus']
            
            for field in defendant_fields:
                field_value = record.get(field, '')
                if isinstance(field_value, str) and search_lower in field_value.lower():
                    results.append(record)
                    break
                elif isinstance(field_value, list):
                    for def_name in field_value:
                        if search_lower in str(def_name).lower():
                            results.append(record)
                            break
        
        return results
    
    async def search_by_versus(self, versus: str) -> List[Dict[str, Any]]:
        """Search records by versus field"""
        all_records = await self.repository.scan_all()
        
        search_lower = versus.lower()
        results = []
        
        for record in all_records:
            versus_value = record.get('Versus', '')
            if search_lower in versus_value.lower():
                results.append(record)
        
        return results
    
    async def search_by_summary(self, summary: str) -> List[Dict[str, Any]]:
        """Search records by summary field"""
        all_records = await self.repository.scan_all()
        
        search_lower = summary.lower()
        results = []
        
        for record in all_records:
            summary_value = record.get('Summary', '')
            if search_lower in summary_value.lower():
                results.append(record)
        
        return results
    
    async def get_records_by_added_on(
        self, start_date: str, end_date: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """Get records by AddedOn date range"""
        return await self.repository.query_by_added_on_range(start_date, end_date)
    
    async def query_by_date_range(
        self, start_date: str, end_date: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """Query records by filing date range"""
        if not end_date:
            end_date = start_date
        
        return await self.repository.query_by_date_range(start_date, end_date)
    
    async def query_by_mdl_num(
        self, mdl_num: str, 
        start_date: Optional[str] = None, 
        end_date: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """Query records by MDL number with optional date range"""
        if start_date and end_date:
            return await self.repository.query_by_mdl_and_date_range(
                mdl_num, start_date, end_date
            )
        
        # No date range - scan all and filter by MDL
        all_records = await self.repository.scan_all()
        results = [r for r in all_records if r.get('MdlNum') == mdl_num]
        
        return sorted(results, key=lambda x: x.get('FilingDate', ''), reverse=True)
    
    async def get_mdl_dockets_by_date_range(
        self, mdl_num: str, start_date: str, end_date: str
    ) -> List[Dict[str, Any]]:
        """Get MDL dockets within date range"""
        return await self.repository.query_by_mdl_and_date_range(
            mdl_num, start_date, end_date
        )
    
    async def check_docket_status(
        self, court_id: str, docket_num: str, filing_date: str
    ) -> Tuple[bool, Optional[Dict[str, Any]], Optional[str]]:
        """
        Check docket status and return detailed information
        Returns: (exists, record, status_message)
        """
        # First try to get the exact record
        record = await self.repository.get_by_filing_date_and_docket(filing_date, docket_num)
        
        if record:
            # Verify court_id matches if provided
            if record.get('CourtId') == court_id:
                return True, record, "Record exists"
            else:
                return False, None, "Record not found"
        else:
            return False, None, "Record not found"
    
    async def query_transfer_info(
        self, court_id: str, docket_num: str
    ) -> Dict[str, Any]:
        """Get comprehensive transfer information for a docket"""
        result = {
            'original_docket': None,
            'transferee_dockets': [],
            'transfer_history': []
        }
        
        # Check as original docket
        original_records = await self.repository.query_by_court_and_docket(
            court_id, docket_num
        )
        
        if original_records:
            result['original_docket'] = original_records[0]
            
            # Look for any transferee information
            for record in original_records:
                if record.get('TransfereeCourtId'):
                    result['transfer_history'].append({
                        'from': f"{court_id}:{docket_num}",
                        'to': f"{record['TransfereeCourtId']}:{record.get('TransfereeDocketNum', 'Unknown')}",
                        'date': record.get('FilingDate')
                    })
        
        # Check as transferee docket
        transferee_records = await self.repository.query_by_transferee_docket(
            court_id, docket_num
        )
        
        if transferee_records:
            result['transferee_dockets'] = transferee_records
            
            for record in transferee_records:
                result['transfer_history'].append({
                    'from': f"{record.get('CourtId', 'Unknown')}:{record.get('DocketNum', 'Unknown')}",
                    'to': f"{court_id}:{docket_num}",
                    'date': record.get('FilingDate')
                })
        
        return result
    
    async def scan_for_duplicates(self) -> List[Dict[str, Any]]:
        """Scan for duplicate records in the database"""
        all_records = await self.repository.scan_all()
        
        # Group by court_id and docket_num
        docket_groups = {}
        for record in all_records:
            key = (record.get('CourtId'), record.get('DocketNum'))
            if key not in docket_groups:
                docket_groups[key] = []
            docket_groups[key].append(record)
        
        # Find duplicates
        duplicates = []
        for key, records in docket_groups.items():
            if len(records) > 1:
                # Sort by filing date
                records.sort(key=lambda x: x.get('FilingDate', ''))
                duplicates.append({
                    'court_id': key[0],
                    'docket_num': key[1],
                    'count': len(records),
                    'filing_dates': [r.get('FilingDate') for r in records],
                    'records': records
                })
        
        return duplicates
    
    async def search_and_update_record(
        self, search_string: str, new_mdl_num: str, new_title: str
    ) -> int:
        """Search for records and update their MDL number and title"""
        results = await self.search_by_title(search_string)
        
        update_count = 0
        for record in results:
            # Update the record
            record['MdlNum'] = new_mdl_num
            record['Title'] = new_title
            
            await self.repository.add_or_update_record(record)
            update_count += 1
        
        return update_count
    
    async def get_mdl_summary(self, mdl_num: str) -> Dict[str, Any]:
        """Get summary statistics for an MDL"""
        records = await self.repository.query_by_mdl_num(mdl_num)
        
        if not records:
            return {
                'mdl_num': mdl_num,
                'total_cases': 0,
                'unique_courts': 0,
                'unique_law_firms': 0,
                'courts': {},
                'law_firms': {},
                'status': 'No records found'
            }
        
        # Calculate statistics
        court_counts = {}
        law_firm_counts = {}
        
        for record in records:
            # Count courts
            court_id = record.get('CourtId', 'Unknown')
            court_counts[court_id] = court_counts.get(court_id, 0) + 1
            
            # Count law firms
            law_firm = record.get('LawFirm', 'Unknown')
            if law_firm:
                law_firm_counts[law_firm] = law_firm_counts.get(law_firm, 0) + 1
        
        return {
            'mdl_num': mdl_num,
            'total_cases': len(records),
            'unique_courts': len(court_counts),
            'unique_law_firms': len(law_firm_counts),
            'courts': court_counts,
            'law_firms': law_firm_counts,
            'status': 'Active' if records else 'Inactive'
        }
    
    async def find_transfers(
        self, court_id: Optional[str] = None, mdl_num: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """Find transfer records with optional filtering"""
        all_records = await self.repository.scan_all()
        
        transfers = []
        for record in all_records:
            # Check if record has transfer information
            if record.get('TransfereeCourtId') or record.get('TransferorCourtId'):
                # Apply filters if provided
                if court_id and record.get('CourtId') != court_id:
                    continue
                if mdl_num and record.get('MdlNum') != mdl_num:
                    continue
                
                transfers.append(record)  # Return the full record
        
        return transfers
    
    async def get_filings_by_date_range(
        self, start_date: str, end_date: str
    ) -> List[Dict[str, Any]]:
        """Get all filings within a date range"""
        from datetime import datetime, timedelta
        
        results = []
        
        # Parse dates
        start = datetime.strptime(start_date, '%Y%m%d')
        end = datetime.strptime(end_date, '%Y%m%d')
        
        # Query each day
        current = start
        while current <= end:
            date_str = current.strftime('%Y%m%d')
            day_results = await self.repository.query_by_filing_date(date_str)
            results.extend(day_results)
            current += timedelta(days=1)
        
        return results
    
    async def search_complex(
        self,
        criteria: Optional[Dict[str, Any]] = None,
        title: Optional[str] = None,
        law_firm: Optional[str] = None,
        mdl_num: Optional[str] = None,
        court_id: Optional[str] = None,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """Complex search with multiple optional criteria"""
        # Extract from criteria dict if provided
        if criteria:
            title = criteria.get('title', title)
            law_firm = criteria.get('law_firm', law_firm)
            mdl_num = criteria.get('mdl_num', mdl_num)
            court_id = criteria.get('court_id', court_id)
            start_date = criteria.get('start_date', start_date)
            end_date = criteria.get('end_date', end_date)
        
        # Start with all records
        all_records = await self.repository.scan_all()
        results = all_records
        
        # Apply filters
        if title:
            title_lower = title.lower()
            results = [r for r in results if title_lower in r.get('Title', '').lower()]
        
        if law_firm:
            firm_lower = law_firm.lower()
            filtered = []
            for r in results:
                # Check multiple law firm fields
                if any(firm_lower in str(r.get(field, '')).lower() 
                      for field in ['LawFirm', 'LawFirms', 'PlaintiffAttorneys']):
                    filtered.append(r)
            results = filtered
        
        if mdl_num:
            results = [r for r in results if r.get('MdlNum') == mdl_num]
        
        if court_id:
            results = [r for r in results if r.get('CourtId') == court_id]
        
        if start_date:
            results = [r for r in results if r.get('FilingDate', '') >= start_date]
        
        if end_date:
            results = [r for r in results if r.get('FilingDate', '') <= end_date]
        
        return sorted(results, key=lambda x: x.get('FilingDate', ''), reverse=True)
    
    async def aggregate_by_court(self, records: Optional[List[Dict[str, Any]]] = None) -> Dict[str, int]:
        """Aggregate filings by court"""
        if records is None:
            records = await self.repository.scan_all()
        
        court_counts = {}
        
        for record in records:
            court_id = record.get('CourtId', 'Unknown')
            court_counts[court_id] = court_counts.get(court_id, 0) + 1
        
        return court_counts