"""
PACER Export Service - CSV and Data Export Logic
"""
from typing import List, Dict, Any, Optional
import csv
import pandas as pd
from datetime import datetime
from rich.console import Console
from rich.table import Table
import logging


class PacerExportService:
    """Service for exporting PACER data in various formats"""
    
    def __init__(self):
        self.console = Console()
        self.logger = logging.getLogger(__name__)
    
    def export_to_csv(
        self, 
        items: List[Dict[str, Any]], 
        filename: str, 
        fields: Optional[List[str]] = None
    ) -> None:
        """Export items to CSV file"""
        if not items:
            self.logger.warning("No items to export")
            return
        
        # Determine fields if not specified
        if not fields:
            # Get all unique fields from all items
            all_fields = set()
            for item in items:
                all_fields.update(item.keys())
            fields = sorted(list(all_fields))
        
        # Write CSV
        try:
            with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=fields)
                writer.writeheader()
                
                for item in items:
                    # Only write fields that exist in the item
                    row = {field: item.get(field, '') for field in fields}
                    writer.writerow(row)
            
            self.logger.info(f"Exported {len(items)} items to {filename}")
            self.console.print(f"[green]Successfully exported {len(items)} items to {filename}[/green]")
            
        except Exception as e:
            self.logger.error(f"Error exporting to CSV: {e}")
            self.console.print(f"[red]Error exporting to CSV: {e}[/red]")
    
    def export_to_excel(
        self,
        items: List[Dict[str, Any]],
        filename: str,
        sheet_name: str = "PACER Data"
    ) -> None:
        """Export items to Excel file"""
        if not items:
            self.logger.warning("No items to export")
            return
        
        try:
            df = pd.DataFrame(items)
            df.to_excel(filename, sheet_name=sheet_name, index=False)
            
            self.logger.info(f"Exported {len(items)} items to {filename}")
            self.console.print(f"[green]Successfully exported {len(items)} items to {filename}[/green]")
            
        except Exception as e:
            self.logger.error(f"Error exporting to Excel: {e}")
            self.console.print(f"[red]Error exporting to Excel: {e}[/red]")
    
    def display_results(
        self,
        items: List[Dict[str, Any]],
        output_file: Optional[str] = None,
        output_fields: Optional[List[str]] = None
    ) -> None:
        """Display results in console and optionally export to file"""
        if not items:
            self.console.print("[yellow]No results found.[/yellow]")
            return
        
        # Display in console
        self.console.print(f"\n[bold]Found {len(items)} results:[/bold]")
        
        # Create rich table for display
        table = Table(show_header=True, header_style="bold magenta")
        
        # Determine display fields
        if output_fields:
            display_fields = output_fields
        else:
            # Default fields for display
            display_fields = [
                'FilingDate', 'CourtId', 'DocketNum', 
                'Title', 'MdlNum', 'Plaintiff', 'Defendant'
            ]
            # Only include fields that exist in at least one item
            display_fields = [
                f for f in display_fields 
                if any(f in item for item in items)
            ]
        
        # Add columns
        for field in display_fields:
            table.add_column(field)
        
        # Add rows (limit to first 20 for console display)
        for item in items[:20]:
            row = []
            for field in display_fields:
                value = item.get(field, '')
                # Truncate long values for display
                if isinstance(value, str) and len(value) > 50:
                    value = value[:47] + "..."
                row.append(str(value))
            table.add_row(*row)
        
        self.console.print(table)
        
        if len(items) > 20:
            self.console.print(f"\n[yellow]... and {len(items) - 20} more results[/yellow]")
        
        # Export to file if requested
        if output_file:
            if output_file.endswith('.csv'):
                self.export_to_csv(items, output_file, output_fields)
            elif output_file.endswith('.xlsx'):
                self.export_to_excel(items, output_file)
            else:
                self.console.print(f"[red]Unsupported file format: {output_file}[/red]")
    
    def format_for_bubble(
        self,
        items: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """Format items for Bubble.io integration"""
        bubble_formatted = []
        
        for item in items:
            # Convert to Bubble-friendly format
            bubble_item = {
                'filing_date': item.get('FilingDate', ''),
                'docket_num': item.get('DocketNum', ''),
                'court_id': item.get('CourtId', ''),
                'title': item.get('Title', ''),
                'mdl_num': item.get('MdlNum', ''),
                'plaintiff': item.get('Plaintiff', ''),
                'defendant': item.get('Defendant', ''),
                'law_firm': item.get('LawFirm', ''),
                'added_on': item.get('AddedOn', ''),
                'pdf_count': item.get('PdfCount', 0),
                'transferee_court_id': item.get('TransfereeCourtId', ''),
                'transferee_docket_num': item.get('TransfereeDocketNum', ''),
                'summary': item.get('Summary', ''),
                'versus': item.get('Versus', '')
            }
            
            # Clean empty strings and None values
            bubble_item = {
                k: v for k, v in bubble_item.items() 
                if v is not None and v != ''
            }
            
            bubble_formatted.append(bubble_item)
        
        return bubble_formatted
    
    def generate_summary_report(
        self,
        items: List[Dict[str, Any]],
        group_by: str = 'MdlNum'
    ) -> pd.DataFrame:
        """Generate a summary report grouped by specified field"""
        if not items:
            return pd.DataFrame()
        
        df = pd.DataFrame(items)
        
        # Group and aggregate
        summary = df.groupby(group_by).agg({
            'FilingDate': 'count',  # Count of filings
            'CourtId': 'nunique',   # Unique courts
            'PdfCount': 'sum'       # Total PDFs
        }).rename(columns={
            'FilingDate': 'TotalFilings',
            'CourtId': 'UniqueCourts',
            'PdfCount': 'TotalPDFs'
        })
        
        # Sort by total filings
        summary = summary.sort_values('TotalFilings', ascending=False)
        
        return summary