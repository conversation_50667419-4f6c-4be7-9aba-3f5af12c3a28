"""
Facebook Ad Archive Query Service - Business Logic for Complex Queries
"""
from typing import Dict, List, Any, Optional, Set, Tuple
from datetime import datetime
import logging
from collections import defaultdict

from src.repositories.fb_archive_repository import FBArchiveRepository


class FBArchiveQueryService:
    """Service for complex Facebook Ad Archive queries"""
    
    def __init__(self, repository: FBArchiveRepository):
        self.repository = repository
        self.logger = logging.getLogger(__name__)
    
    async def get_ads_by_archive_ids(
        self, 
        archive_ids: List[str]
    ) -> List[Dict[str, Any]]:
        """
        Get ads by multiple archive IDs
        
        Args:
            archive_ids: List of AdArchiveIDs
            
        Returns:
            List of ad records
        """
        if not archive_ids:
            return []
        
        # First, get all records for each archive ID to find full keys
        all_keys = []
        for ad_id in archive_ids:
            records = await self.repository.query_by_archive_id(ad_id)
            for record in records:
                if record.get('StartDate'):
                    all_keys.append({
                        'AdArchiveID': ad_id,
                        'StartDate': record['StartDate']
                    })
        
        # Deduplicate keys
        unique_keys = []
        seen = set()
        for key in all_keys:
            key_tuple = (key['AdArchiveID'], key['StartDate'])
            if key_tuple not in seen:
                seen.add(key_tuple)
                unique_keys.append(key)
        
        # Batch get the records
        return await self.repository.batch_get_records(unique_keys)
    
    async def get_ads_by_date_range(
        self,
        start_date: str,
        end_date: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Get ads within a date range
        
        Args:
            start_date: Start date (YYYYMMDD)
            end_date: End date (YYYYMMDD), defaults to start_date
            
        Returns:
            List of ad records
        """
        if not end_date:
            end_date = start_date
        
        # Generate date range
        dates = self._generate_date_range(start_date, end_date)
        
        # Query for each date in parallel for better performance
        import asyncio
        
        # Log the dates we're about to query
        import logging
        logger = logging.getLogger(__name__)
        logger.info(f"[FB_QUERY] Preparing to query {len(dates)} dates: {dates}")
        print(f"[FB_QUERY] DEBUG: About to query {len(dates)} dates: {dates}")
        
        # Create tasks for parallel queries with logging
        async def query_with_logging(date):
            self.logger.debug(f"Starting query for date: {date}")
            try:
                result = await self.repository.query_by_start_date(date)
                self.logger.info(f"Completed query for date {date}: {len(result)} records")
                return result
            except Exception as e:
                self.logger.error(f"Failed to query date {date}: {e}")
                raise
        
        tasks = [query_with_logging(date) for date in dates]
        
        # Execute all queries in parallel
        self.logger.info(f"Executing {len(dates)} parallel queries...")
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Collect all records, handling any errors
        all_records = []
        successful_dates = 0
        print(f"[FB_QUERY] DEBUG: Processing {len(results)} results")
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                print(f"[FB_QUERY] DEBUG: Date {dates[i]} failed with error: {result}")
                self.logger.error(f"Error for date {dates[i]}: {result}")
            else:
                print(f"[FB_QUERY] DEBUG: Date {dates[i]} succeeded with {len(result)} records")
                all_records.extend(result)
                successful_dates += 1
        
        self.logger.info(f"Query complete: Retrieved {len(all_records)} total records from {successful_dates}/{len(dates)} dates")
        return all_records
    
    async def get_ads_by_page_id_grouped(
        self,
        page_id: str
    ) -> Dict[str, List[Dict[str, Any]]]:
        """
        Get ads for a page ID grouped by law firm
        
        Args:
            page_id: Facebook Page ID
            
        Returns:
            Dictionary with law firms as keys and ads as values
        """
        records = await self.repository.query_by_page_id(page_id)
        
        # Group by law firm
        grouped = defaultdict(list)
        for record in records:
            law_firm = record.get('LawFirm', 'Unknown')
            grouped[law_firm].append(record)
        
        return dict(grouped)
    
    async def get_unique_archive_ids_by_page(
        self,
        page_id: str
    ) -> List[str]:
        """
        Get unique AdArchiveIDs for a page
        
        Args:
            page_id: Facebook Page ID
            
        Returns:
            List of unique AdArchiveIDs
        """
        records = await self.repository.query_by_page_id(page_id)
        unique_ids = set()
        
        for record in records:
            ad_id = record.get('AdArchiveID')
            if ad_id:
                unique_ids.add(ad_id)
        
        return sorted(list(unique_ids))
    
    async def find_ads_with_summary_issues(self) -> Tuple[List[Dict], List[Dict]]:
        """
        Find ads with invalid summaries
        
        Returns:
            Tuple of (clean_ads, ads_with_issues)
        """
        # Define problematic terms
        unwanted_terms = [
            'accident', 'medical malpractice', 'birth injury', 'been injured',
            'fighting for justice', 'trabajo', 'misdiagnosis', 'damn good ad',
            'insurance company', 'nursing home', 'compensación laboral',
            'wet floors', 'social security', 'tbi', 'hospital injury',
            'medical negligence', 'billions won', 'honor our heroes',
            'slip & fall', 'halloween', 'driving a lemon', 'dog bite',
            'cruise', 'injured', 'injury', 'injuries', 'facing legal charges',
            'lemon law', 'car accident', 'auto accident', 'truck accident',
            'motorcycle accident', 'drug charge', 'expand our referral',
            'personal injury', 'slip and fall', 'workers compensation',
            'accidents', 'hurricane', 'billions for clients', 'workers comp',
            'work injury', 'workplace injury', "workers' comp", 'Big Blue Nation',
            "let's work together", 'hispanic heritage', 'latino business expo',
            'veteran disability', 'veterans disability', 'four tickets',
            'Kroger Field', 'dui charge', 'got a lemon', "workers' compensation",
            'los veteranos', 'think big', 'think big wins', 'unless we win',
            'nursing homes', 'enter to win', 'awareness month', 'halloween',
            'make it a #farahfight', 'legal whisperer'
        ]
        
        # Scan all records
        all_records = await self.repository.scan_all()
        
        clean_ads = []
        ads_with_issues = []
        
        for record in all_records:
            summary = record.get('Summary', '')
            body = record.get('Body', '').lower() if record.get('Body') else ''
            
            # Check if summary is invalid
            if not summary or summary == 'NA' or len(summary) < 3:
                # Check for unwanted terms in body
                has_unwanted = any(term.lower() in body for term in unwanted_terms)
                
                ad_info = {
                    'AdArchiveID': record.get('AdArchiveID'),
                    'Body': record.get('Body'),
                    'Title': record.get('Title'),
                    'LinkDescription': record.get('LinkDescription'),
                    'Summary': summary
                }
                
                if has_unwanted:
                    ads_with_issues.append(ad_info)
                else:
                    clean_ads.append(ad_info)
        
        return clean_ads, ads_with_issues
    
    async def search_ads(
        self,
        search_term: str,
        fields: List[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Search ads by term in specified fields
        
        Args:
            search_term: Term to search for
            fields: List of fields to search in (default: Title, Body, LinkDescription)
            
        Returns:
            List of matching records
        """
        if fields is None:
            fields = ['Title', 'Body', 'LinkDescription']
        
        search_term_lower = search_term.lower()
        all_records = await self.repository.scan_all()
        
        matching_records = []
        for record in all_records:
            for field in fields:
                value = record.get(field, '')
                if value and search_term_lower in str(value).lower():
                    matching_records.append(record)
                    break
        
        return matching_records
    
    async def get_statistics_by_date(
        self,
        date: str
    ) -> Dict[str, Any]:
        """
        Get statistics for ads on a specific date
        
        Args:
            date: Date in YYYYMMDD format
            
        Returns:
            Statistics dictionary
        """
        records = await self.repository.query_by_last_updated(date)
        
        stats = {
            'total_ads': len(records),
            'unique_pages': len(set(r.get('PageID', '') for r in records)),
            'unique_law_firms': len(set(r.get('LawFirm', '') for r in records)),
            'active_ads': sum(1 for r in records if r.get('IsActive')),
            'inactive_ads': sum(1 for r in records if not r.get('IsActive')),
            'ads_by_law_firm': defaultdict(int),
            'ads_by_page': defaultdict(int)
        }
        
        for record in records:
            law_firm = record.get('LawFirm', 'Unknown')
            page_id = record.get('PageID', 'Unknown')
            stats['ads_by_law_firm'][law_firm] += 1
            stats['ads_by_page'][page_id] += 1
        
        # Convert defaultdicts to regular dicts
        stats['ads_by_law_firm'] = dict(stats['ads_by_law_firm'])
        stats['ads_by_page'] = dict(stats['ads_by_page'])
        
        return stats
    
    async def find_duplicate_ads(self) -> Dict[str, List[Dict[str, Any]]]:
        """
        Find duplicate ads based on content
        
        Returns:
            Dictionary with duplicate groups
        """
        all_records = await self.repository.scan_all()
        
        # Group by content hash
        content_groups = defaultdict(list)
        
        for record in all_records:
            # Create content hash from key fields
            content_key = self._create_content_hash(record)
            if content_key:
                content_groups[content_key].append({
                    'AdArchiveID': record.get('AdArchiveID'),
                    'StartDate': record.get('StartDate'),
                    'LawFirm': record.get('LawFirm'),
                    'PageID': record.get('PageID')
                })
        
        # Filter to only duplicates
        duplicates = {
            key: ads for key, ads in content_groups.items()
            if len(ads) > 1
        }
        
        return duplicates
    
    def _generate_date_range(self, start_date: str, end_date: str) -> List[str]:
        """Generate list of dates between start and end"""
        try:
            from datetime import timedelta
            
            start = datetime.strptime(start_date, '%Y%m%d')
            end = datetime.strptime(end_date, '%Y%m%d')
            
            dates = []
            current = start
            while current <= end:
                dates.append(current.strftime('%Y%m%d'))
                current = current + timedelta(days=1)
            
            return dates
        except ValueError as e:
            self.logger.error(f"Invalid date format: {e}")
            return []
    
    def _create_content_hash(self, record: Dict[str, Any]) -> Optional[str]:
        """Create a hash from record content for duplicate detection"""
        title = record.get('Title', '')
        body = record.get('Body', '')
        link_desc = record.get('LinkDescription', '')
        
        if not any([title, body, link_desc]):
            return None
        
        # Simple hash based on content
        content = f"{title}|{body}|{link_desc}"
        return str(hash(content))