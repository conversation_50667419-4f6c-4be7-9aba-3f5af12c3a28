"""
Report generation configuration.
"""

from pydantic import BaseModel, Field, field_validator, EmailStr
from typing import Optional, List, Dict, Any, Literal
from datetime import time
from pathlib import Path
from .base import WorkflowConfig


class ReportConfig(WorkflowConfig):
    """Report generation configuration"""
    # Core settings
    report_generator: bool = Field(True, description="Enable report generation")
    report_type: Literal["daily", "weekly", "monthly", "custom"] = Field(
        "daily",
        description="Type of report to generate"
    )
    
    model_config = {
        **WorkflowConfig.model_config,
        "extra": "allow"  # Allow extra fields during migration
    }
    
    # Email settings
    send_email: bool = Field(True, description="Send report via email")
    email_test_mode: bool = Field(False, description="Send to test recipients only")
    test_recipients: List[EmailStr] = Field(default_factory=list, description="Test email recipients")
    
    # Report content
    include_sections: List[str] = Field(
        default_factory=lambda: ["summary", "campaigns", "litigation", "adspy", "news"],
        description="Report sections to include"
    )
    exclude_sections: List[str] = Field(default_factory=list, description="Sections to exclude")
    
    # Generation settings
    max_items_per_section: int = Field(50, ge=1, le=500, description="Maximum items per section")
    include_images: bool = Field(True, description="Include images in report")
    include_charts: bool = Field(True, description="Include charts and visualizations")
    
    # Template settings
    template_name: str = Field("weekly_report_template.html", description="Template file to use")
    custom_css_path: Optional[Path] = Field(None, description="Path to custom CSS file")
    
    # Schedule settings (for automated reports)
    scheduled: bool = Field(False, description="Enable scheduled generation")
    schedule_time: Optional[time] = Field(None, description="Time to generate report")
    schedule_days: List[int] = Field(
        default_factory=lambda: [1, 2, 3, 4, 5],  # Monday-Friday
        description="Days of week to generate (1=Monday, 7=Sunday)"
    )
    
    @field_validator('include_sections', 'exclude_sections')
    @classmethod
    def validate_sections(cls, v):
        """Ensure section names are valid"""
        valid_sections = {
            "summary", "campaigns", "litigation", "adspy", "news",
            "afff_stats", "announcements", "upcoming_hearings",
            "detailed_filings", "special_reports"
        }
        for section in v:
            if section not in valid_sections:
                raise ValueError(f"Invalid section: {section}. Valid sections: {valid_sections}")
        return v
    
    @field_validator('schedule_days')
    @classmethod
    def validate_schedule_days(cls, v):
        """Ensure schedule days are valid"""
        for day in v:
            if day not in range(1, 8):
                raise ValueError(f"Invalid day {day}. Must be 1-7 (Monday-Sunday)")
        return sorted(set(v))


class EmailConfig(BaseModel):
    """Email configuration for reports"""
    # SMTP settings
    smtp_host: str = Field(..., description="SMTP server host")
    smtp_port: int = Field(587, ge=1, le=65535, description="SMTP server port")
    smtp_username: str = Field(..., description="SMTP username")
    smtp_password: str = Field(..., description="SMTP password")
    smtp_use_tls: bool = Field(True, description="Use TLS encryption")
    
    # Email settings
    from_email: EmailStr = Field(..., description="From email address")
    from_name: str = Field("LexGenius Reports", description="From display name")
    reply_to: Optional[EmailStr] = Field(None, description="Reply-to email address")
    
    # Recipients
    default_recipients: List[EmailStr] = Field(default_factory=list, description="Default recipients")
    cc_recipients: List[EmailStr] = Field(default_factory=list, description="CC recipients")
    bcc_recipients: List[EmailStr] = Field(default_factory=list, description="BCC recipients")
    
    # Rate limiting
    max_emails_per_minute: int = Field(10, ge=1, le=100, description="Maximum emails per minute")
    batch_delay_seconds: int = Field(5, ge=1, le=60, description="Delay between email batches")
    
    model_config = {
        "env_file": ".env",
        "env_prefix": "LEXGENIUS_EMAIL_"
    }


class SectionConfig(BaseModel):
    """Configuration for individual report sections"""
    # Section metadata
    name: str = Field(..., description="Section name")
    title: str = Field(..., description="Section display title")
    enabled: bool = Field(True, description="Whether section is enabled")
    order: int = Field(0, ge=0, description="Display order (lower = earlier)")
    
    # Content settings
    max_items: int = Field(25, ge=1, le=100, description="Maximum items to show")
    days_back: int = Field(7, ge=1, le=90, description="Days of data to include")
    
    # Filtering
    include_filters: Dict[str, Any] = Field(default_factory=dict, description="Inclusion filters")
    exclude_filters: Dict[str, Any] = Field(default_factory=dict, description="Exclusion filters")
    
    # Display options
    show_images: bool = Field(True, description="Show images in this section")
    show_charts: bool = Field(True, description="Show charts in this section")
    collapsed_by_default: bool = Field(False, description="Start section collapsed")
    
    # Custom template
    custom_template: Optional[str] = Field(None, description="Custom template for this section")


class ContentFilters(BaseModel):
    """Content filtering configuration"""
    # Campaign filters
    campaign_min_ads: int = Field(5, ge=1, description="Minimum ads for campaign inclusion")
    campaign_min_spend: float = Field(1000.0, ge=0.0, description="Minimum spend for inclusion")
    campaign_exclude_patterns: List[str] = Field(
        default_factory=list,
        description="Regex patterns to exclude campaigns"
    )
    
    # Litigation filters
    litigation_min_filings: int = Field(3, ge=1, description="Minimum filings for inclusion")
    litigation_include_mdls: List[str] = Field(default_factory=list, description="MDLs to include")
    litigation_exclude_mdls: List[str] = Field(default_factory=list, description="MDLs to exclude")
    
    # Law firm filters
    firm_min_activity: int = Field(2, ge=1, description="Minimum activity for firm inclusion")
    firm_exclude_list: List[str] = Field(default_factory=list, description="Firms to exclude")
    
    # Content quality
    min_text_length: int = Field(50, ge=10, description="Minimum text length for content")
    require_images: bool = Field(False, description="Only include items with images")
    
    # Spam detection
    spam_keywords: List[str] = Field(
        default_factory=lambda: ["test", "demo", "sample"],
        description="Keywords indicating spam content"
    )
    duplicate_threshold: float = Field(0.9, ge=0.0, le=1.0, description="Similarity threshold for duplicates")