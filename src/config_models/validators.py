"""
Custom validators for configuration models.
"""

import re
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional
from pydantic import field_validator


def validate_date_format(date_str: str) -> datetime:
    """Validate and parse date in multiple formats"""
    formats = [
        "%m/%d/%y",    # MM/DD/YY
        "%m/%d/%Y",    # MM/DD/YYYY
        "%Y-%m-%d",    # YYYY-MM-DD
        "%m-%d-%Y",    # MM-DD-YYYY
        "%m-%d-%y",    # MM-DD-YY
    ]
    
    for fmt in formats:
        try:
            parsed_date = datetime.strptime(date_str, fmt)
            # Convert 2-digit years to 4-digit
            if parsed_date.year < 100:
                parsed_date = parsed_date.replace(year=parsed_date.year + 2000)
            return parsed_date
        except ValueError:
            continue
    
    raise ValueError(f"Unable to parse date '{date_str}'. Expected formats: {', '.join(formats)}")


def validate_court_id(court_id: str) -> str:
    """Validate court ID format"""
    court_id = court_id.strip().upper()
    
    # Valid format: 2-4 letter state code + 'd' for district
    # Examples: NYD, NYSD, CAD, CACD
    pattern = r"^[A-Z]{2,4}D$"
    
    if not re.match(pattern, court_id):
        raise ValueError(
            f"Invalid court ID '{court_id}'. "
            f"Expected format: state code + 'd' (e.g., 'NYD', 'CACD')"
        )
    
    return court_id


def validate_mdl_number(mdl_num: str) -> str:
    """Validate MDL number format"""
    mdl_num = mdl_num.strip()
    
    # MDL numbers are typically 4 digits, sometimes with leading zeros
    if mdl_num.isdigit() and len(mdl_num) <= 4:
        return mdl_num.zfill(4)  # Pad with zeros if needed
    
    # Also accept format "MDL-2741" or "MDL 2741"
    match = re.match(r"^MDL[-\s]?(\d{1,4})$", mdl_num, re.IGNORECASE)
    if match:
        return match.group(1).zfill(4)
    
    raise ValueError(
        f"Invalid MDL number '{mdl_num}'. "
        f"Expected format: 4-digit number or 'MDL-XXXX'"
    )


def validate_email_list(emails: List[str]) -> List[str]:
    """Validate list of email addresses"""
    validated = []
    email_pattern = r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
    
    for email in emails:
        email = email.strip()
        if not re.match(email_pattern, email):
            raise ValueError(f"Invalid email address: {email}")
        validated.append(email.lower())
    
    return list(set(validated))  # Remove duplicates


def validate_file_path(path: Path, must_exist: bool = False) -> Path:
    """Validate file path"""
    path = Path(path).expanduser().resolve()
    
    if must_exist and not path.exists():
        raise ValueError(f"Path does not exist: {path}")
    
    # Ensure parent directory exists for new files
    if not must_exist:
        path.parent.mkdir(parents=True, exist_ok=True)
    
    return path


def validate_json_path(path: Path) -> Path:
    """Validate JSON file path"""
    path = validate_file_path(path)
    
    if not path.suffix == '.json':
        raise ValueError(f"Expected .json file, got: {path}")
    
    return path


def validate_yaml_path(path: Path) -> Path:
    """Validate YAML file path"""
    path = validate_file_path(path)
    
    if path.suffix not in ['.yml', '.yaml']:
        raise ValueError(f"Expected .yml or .yaml file, got: {path}")
    
    return path


def validate_percentage(value: float, field_name: str) -> float:
    """Validate percentage value (0-100)"""
    if not 0 <= value <= 100:
        raise ValueError(f"{field_name} must be between 0 and 100, got {value}")
    return value


def validate_port(port: int) -> int:
    """Validate network port number"""
    if not 1 <= port <= 65535:
        raise ValueError(f"Port must be between 1 and 65535, got {port}")
    return port


def validate_timeout(timeout: int, min_val: int = 1, max_val: int = 3600) -> int:
    """Validate timeout value in seconds"""
    if not min_val <= timeout <= max_val:
        raise ValueError(
            f"Timeout must be between {min_val} and {max_val} seconds, got {timeout}"
        )
    return timeout


def validate_batch_size(size: int, max_size: int = 1000) -> int:
    """Validate batch size"""
    if not 1 <= size <= max_size:
        raise ValueError(f"Batch size must be between 1 and {max_size}, got {size}")
    return size


def validate_regex_pattern(pattern: str) -> str:
    """Validate regex pattern"""
    try:
        re.compile(pattern)
        return pattern
    except re.error as e:
        raise ValueError(f"Invalid regex pattern '{pattern}': {e}")


def validate_cron_expression(expr: str) -> str:
    """Validate cron expression format"""
    # Simple validation for cron format
    parts = expr.strip().split()
    if len(parts) != 5:
        raise ValueError(
            f"Invalid cron expression '{expr}'. "
            f"Expected 5 fields: minute hour day month weekday"
        )
    return expr


def validate_law_firm_name(name: str) -> str:
    """Validate and normalize law firm name"""
    name = name.strip()
    
    if not name:
        raise ValueError("Law firm name cannot be empty")
    
    # Remove excessive whitespace
    name = ' '.join(name.split())
    
    # Check for minimum length
    if len(name) < 3:
        raise ValueError(f"Law firm name too short: '{name}'")
    
    return name


def validate_weights(weights: Dict[str, float]) -> Dict[str, float]:
    """Validate that weights sum to 1.0"""
    total = sum(weights.values())
    
    if abs(total - 1.0) > 0.001:
        raise ValueError(
            f"Weights must sum to 1.0, got {total}. "
            f"Weights: {weights}"
        )
    
    for name, weight in weights.items():
        if not 0 <= weight <= 1:
            raise ValueError(f"Weight '{name}' must be between 0 and 1, got {weight}")
    
    return weights


# Note: For Pydantic v2, validators need to be applied directly to fields
# These validator functions can be used with @field_validator decorator in models