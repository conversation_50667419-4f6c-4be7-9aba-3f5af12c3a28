"""
Facebook ads processing configuration.
"""

from pydantic import BaseModel, Field, field_validator
from typing import Optional, List, Dict, Any, Literal
from pathlib import Path
from .base import WorkflowConfig


class FBAdConfig(WorkflowConfig):
    """Facebook ads processing configuration"""
    # Core settings
    fb_ads: bool = Field(True, description="Enable Facebook ads processing")
    process_images: bool = Field(True, description="Enable image processing")
    enable_ocr: bool = Field(True, description="Enable OCR for images")
    enable_classification: bool = Field(True, description="Enable ad classification")
    
    model_config = {
        **WorkflowConfig.model_config,
        "extra": "allow"  # Allow extra fields during migration
    }
    
    # Processing options
    batch_size: int = Field(100, ge=1, le=1000, description="Batch size for processing")
    max_workers: int = Field(4, ge=1, le=16, description="Maximum parallel workers")
    
    # Image processing
    image_timeout_seconds: int = Field(30, ge=5, le=300, description="Image processing timeout")
    max_image_size_mb: int = Field(10, ge=1, le=50, description="Maximum image size in MB")
    enable_phash: bool = Field(True, description="Enable perceptual hashing for deduplication")
    phash_threshold: int = Field(10, ge=0, le=30, description="PHash similarity threshold")
    
    # Classification settings
    classification_mode: Literal["rules", "embeddings", "hybrid"] = Field(
        "hybrid",
        description="Classification mode to use"
    )
    embedding_model: str = Field("sentence-transformers/all-mpnet-base-v2", description="Embedding model to use")
    classification_threshold: float = Field(0.7, ge=0.0, le=1.0, description="Classification confidence threshold")
    
    # Deferred processing
    enable_deferred_processing: bool = Field(True, description="Enable deferred image processing")
    deferred_queue_max_size: int = Field(10000, ge=100, le=100000, description="Maximum deferred queue size")
    deferred_batch_size: int = Field(50, ge=1, le=500, description="Batch size for deferred processing")
    
    # Caching
    enable_cache: bool = Field(True, description="Enable caching")
    cache_ttl_hours: int = Field(24, ge=1, le=168, description="Cache TTL in hours")
    cache_max_size_mb: int = Field(500, ge=10, le=5000, description="Maximum cache size in MB")
    
    # Rate limiting
    rate_limit_enabled: bool = Field(True, description="Enable rate limiting")
    requests_per_minute: int = Field(60, ge=1, le=1000, description="API requests per minute")
    
    @field_validator('classification_threshold')
    @classmethod
    def validate_threshold(cls, v, info):
        """Ensure threshold is appropriate for classification mode"""
        mode = info.data.get('classification_mode', 'hybrid')
        if mode == 'rules' and v != 1.0:
            raise ValueError("Rules-only mode should have threshold=1.0")
        return v


class ImageProcessingConfig(BaseModel):
    """Image processing specific configuration"""
    # OCR settings
    ocr_provider: Literal["mistral", "ollama", "tesseract"] = Field("mistral", description="OCR provider to use")
    ocr_confidence_threshold: float = Field(0.8, ge=0.0, le=1.0, description="OCR confidence threshold")
    ocr_language: str = Field("eng", description="OCR language")
    
    # Image preprocessing
    enable_preprocessing: bool = Field(True, description="Enable image preprocessing")
    resize_max_width: int = Field(1920, ge=100, le=4000, description="Maximum width for resizing")
    resize_max_height: int = Field(1080, ge=100, le=4000, description="Maximum height for resizing")
    jpeg_quality: int = Field(85, ge=50, le=100, description="JPEG compression quality")
    
    # PHash settings
    phash_hash_size: int = Field(8, ge=4, le=16, description="PHash size")
    phash_highfreq_factor: int = Field(4, ge=1, le=8, description="PHash high frequency factor")
    
    # Batch processing
    batch_timeout_seconds: int = Field(300, ge=30, le=3600, description="Batch processing timeout")
    max_retry_attempts: int = Field(3, ge=0, le=10, description="Maximum retry attempts")


class ClassificationConfig(BaseModel):
    """Ad classification configuration"""
    # Campaign mapping
    campaign_config_path: Path = Field(
        Path("src/config/fb_ad_categorizer/campaign_config.json"),
        description="Path to campaign configuration"
    )
    company_name_mapping_path: Path = Field(
        Path("src/config/fb_ad_categorizer/company_name_mapping.json"),
        description="Path to company name mappings"
    )
    
    # Skip terms
    campaign_skip_terms: List[str] = Field(
        default_factory=list,
        description="Terms to skip in campaign classification"
    )
    embedding_stop_terms: List[str] = Field(
        default_factory=list,
        description="Stop terms for embedding generation"
    )
    
    # Classification rules
    min_rule_confidence: float = Field(0.8, ge=0.0, le=1.0, description="Minimum rule confidence")
    min_embedding_similarity: float = Field(0.7, ge=0.0, le=1.0, description="Minimum embedding similarity")
    
    # Hybrid mode weights
    rule_weight: float = Field(0.6, ge=0.0, le=1.0, description="Weight for rule-based classification")
    embedding_weight: float = Field(0.4, ge=0.0, le=1.0, description="Weight for embedding-based classification")
    
    @field_validator('rule_weight', 'embedding_weight')
    @classmethod
    def validate_weights(cls, v, info):
        """Ensure weights sum to 1.0"""
        rule_w = info.data.get('rule_weight', 0.6)
        embed_w = info.data.get('embedding_weight', 0.4)
        if v in [rule_w, embed_w]:
            if v == rule_w:
                rule_w = v
            else:
                embed_w = v
        if abs(rule_w + embed_w - 1.0) > 0.001:
            raise ValueError(f"Weights must sum to 1.0, got {rule_w + embed_w}")
        return v


class DeferredProcessingConfig(BaseModel):
    """Deferred processing queue configuration"""
    # Queue settings
    queue_type: Literal["sqlite", "redis", "dynamodb"] = Field("sqlite", description="Queue backend type")
    queue_path: Path = Field(Path("sqlite/deferred_queue.db"), description="Path to queue database")
    
    # Processing intervals
    process_interval_minutes: int = Field(15, ge=1, le=1440, description="Processing interval in minutes")
    max_items_per_run: int = Field(100, ge=1, le=1000, description="Maximum items to process per run")
    
    # Retry logic
    max_retries: int = Field(3, ge=0, le=10, description="Maximum retry attempts")
    retry_delay_minutes: int = Field(60, ge=1, le=1440, description="Delay between retries")
    
    # Priority settings
    enable_priority_queue: bool = Field(False, description="Enable priority-based processing")
    priority_levels: List[str] = Field(
        default_factory=lambda: ["high", "medium", "low"],
        description="Priority levels"
    )


# Note: Model rebuild will be handled by the config loader after all imports