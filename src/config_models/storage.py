"""
Storage and infrastructure configuration.
"""

from pydantic import BaseModel, Field, field_validator
from pydantic_settings import BaseSettings
from pydantic import HttpUrl
from typing import Optional, List, Dict, Any, Literal
from pathlib import Path


class StorageConfig(BaseSettings):
    """Storage configuration for AWS and databases"""
    # AWS settings
    aws_region: str = Field("us-west-2", description="AWS region")
    aws_access_key_id: Optional[str] = Field(None, description="AWS access key ID")
    aws_secret_access_key: Optional[str] = Field(None, description="AWS secret access key")
    aws_session_token: Optional[str] = Field(None, description="AWS session token")
    
    # S3 settings
    s3_bucket_name: str = Field("lexgenius-dockets", description="S3 bucket name")
    s3_endpoint_url: Optional[HttpUrl] = Field(None, description="Custom S3 endpoint URL")
    s3_use_ssl: bool = Field(True, description="Use SSL for S3 connections")
    s3_verify_ssl: bool = Field(True, description="Verify SSL certificates")
    
    # DynamoDB settings
    dynamodb_endpoint_url: Optional[HttpUrl] = Field(None, description="DynamoDB endpoint URL")
    dynamodb_table_prefix: str = Field("", description="Prefix for DynamoDB tables (empty for production)")
    dynamodb_default_read_capacity: int = Field(1, ge=1, le=40000, description="Default read capacity units")
    dynamodb_default_write_capacity: int = Field(1, ge=1, le=40000, description="Default write capacity units")
    dynamodb_use_on_demand: bool = Field(False, description="Use on-demand billing mode")
    
    # Auto-scaling defaults
    dynamodb_enable_autoscaling: bool = Field(True, description="Enable auto-scaling by default")
    dynamodb_min_read_capacity: int = Field(1, ge=1, description="Default minimum read capacity")
    dynamodb_max_read_capacity: int = Field(15, ge=1, description="Default maximum read capacity")
    dynamodb_min_write_capacity: int = Field(1, ge=1, description="Default minimum write capacity")
    dynamodb_max_write_capacity: int = Field(15, ge=1, description="Default maximum write capacity")
    dynamodb_target_utilization: float = Field(70.0, ge=20.0, le=90.0, description="Target utilization percentage")
    
    # Local storage
    local_data_dir: Path = Field(Path("data"), description="Local data directory")
    temp_dir: Path = Field(Path("/tmp/lexgenius"), description="Temporary file directory")
    cache_dir: Path = Field(Path("cache"), description="Cache directory")
    
    # Connection pooling
    max_pool_connections: int = Field(50, ge=1, le=1000, description="Maximum connection pool size")
    connection_timeout: int = Field(30, ge=5, le=300, description="Connection timeout in seconds")
    read_timeout: int = Field(60, ge=5, le=600, description="Read timeout in seconds")
    
    @field_validator('local_data_dir', 'temp_dir', 'cache_dir', mode='before')
    @classmethod
    def ensure_paths_exist(cls, v):
        """Ensure directories exist"""
        path = Path(v)
        path.mkdir(parents=True, exist_ok=True)
        return path
    
    @field_validator('dynamodb_endpoint_url')
    @classmethod
    def validate_dynamodb_endpoint(cls, v, info):
        """Set local DynamoDB endpoint for development"""
        if v is None and info.data.get('environment') == 'development':
            return HttpUrl("http://localhost:8000")
        return v
    
    model_config = {
        "env_file": ".env",
        "env_prefix": "LEXGENIUS_",
        "case_sensitive": False,
        "extra": "allow",  # Allow extra fields during migration
        "env_file_encoding": "utf-8"
    }
    
    @field_validator('aws_access_key_id', mode='before')
    @classmethod
    def get_aws_access_key(cls, v):
        """Get AWS access key from various environment variables"""
        if v:
            return v
        import os
        return (os.getenv('LEXGENIUS_AWS_ACCESS_KEY_ID') or 
                os.getenv('AWS_ACCESS_KEY_ID') or 
                os.getenv('AWS_ACCESS_KEY'))
    
    @field_validator('aws_secret_access_key', mode='before')
    @classmethod
    def get_aws_secret_key(cls, v):
        """Get AWS secret key from various environment variables"""
        if v:
            return v
        import os
        return (os.getenv('LEXGENIUS_AWS_SECRET_ACCESS_KEY') or 
                os.getenv('AWS_SECRET_ACCESS_KEY') or 
                os.getenv('AWS_SECRET_KEY'))
    
    @field_validator('aws_region', mode='before')
    @classmethod
    def get_aws_region(cls, v):
        """Get AWS region from various environment variables"""
        if v:
            return v
        import os
        return (os.getenv('LEXGENIUS_AWS_REGION') or 
                os.getenv('AWS_REGION') or 
                os.getenv('AWS_DEFAULT_REGION') or
                os.getenv('REGION_NAME') or
                'us-west-2')
    
    @field_validator('s3_bucket_name', mode='before')
    @classmethod
    def get_bucket_name(cls, v):
        """Get bucket name from various environment variables"""
        if v:
            return v
        # Try different environment variable names
        import os
        return (os.getenv('LEXGENIUS_S3_BUCKET_NAME') or 
                os.getenv('BUCKET_NAME') or 
                os.getenv('S3_BUCKET_NAME') or
                'lexgenius-dockets')


class DynamoDBTableConfig(BaseModel):
    """Configuration for individual DynamoDB table"""
    table_name: str = Field(..., description="DynamoDB table name")
    read_capacity_units: int = Field(1, ge=1, le=40000, description="Base read capacity units")
    write_capacity_units: int = Field(1, ge=1, le=40000, description="Base write capacity units")
    
    # Auto-scaling configuration
    enable_autoscaling: bool = Field(True, description="Enable auto-scaling")
    min_read_capacity: int = Field(1, ge=1, le=40000, description="Minimum read capacity for auto-scaling")
    max_read_capacity: int = Field(15, ge=1, le=40000, description="Maximum read capacity for auto-scaling")
    min_write_capacity: int = Field(1, ge=1, le=40000, description="Minimum write capacity for auto-scaling")
    max_write_capacity: int = Field(15, ge=1, le=40000, description="Maximum write capacity for auto-scaling")
    target_read_utilization: float = Field(70.0, ge=20.0, le=90.0, description="Target read utilization percentage")
    target_write_utilization: float = Field(70.0, ge=20.0, le=90.0, description="Target write utilization percentage")


class DatabaseConfig(BaseModel):
    """Database table configurations"""
    # Table configurations - matching actual AWS setup
    tables: Dict[str, DynamoDBTableConfig] = Field(
        default_factory=lambda: {
            "Pacer": DynamoDBTableConfig(
                table_name="Pacer",
                read_capacity_units=8,  # Higher base capacity
                write_capacity_units=1,
                max_read_capacity=25    # Higher max capacity
            ),
            "FBAdArchive": DynamoDBTableConfig(
                table_name="FBAdArchive",
                read_capacity_units=1,
                write_capacity_units=1
            ),
            "FBArchive": DynamoDBTableConfig(
                table_name="FBArchive", 
                read_capacity_units=1,
                write_capacity_units=1
            ),
            "LawFirms": DynamoDBTableConfig(
                table_name="LawFirms",
                read_capacity_units=1,
                write_capacity_units=1
            ),
            "FBImageHash": DynamoDBTableConfig(
                table_name="FBImageHash",
                read_capacity_units=1,
                write_capacity_units=1
            ),
            "JPMLData": DynamoDBTableConfig(
                table_name="JPMLData",
                read_capacity_units=1,
                write_capacity_units=1
            ),
            "DistrictCourts": DynamoDBTableConfig(
                table_name="DistrictCourts",
                read_capacity_units=1,
                write_capacity_units=1
            ),
            "Attorneys": DynamoDBTableConfig(
                table_name="Attorneys",
                read_capacity_units=1,
                write_capacity_units=1
            ),
            "EmailMap": DynamoDBTableConfig(
                table_name="EmailMap",
                read_capacity_units=1,
                write_capacity_units=1
            ),
            "AdCategoryMap": DynamoDBTableConfig(
                table_name="AdCategoryMap",
                read_capacity_units=1,
                write_capacity_units=1
            ),
            "LawFirmMap": DynamoDBTableConfig(
                table_name="LawFirmMap",
                read_capacity_units=1,
                write_capacity_units=1
            )
        },
        description="DynamoDB table configurations"
    )
    
    # Legacy table name fields for backward compatibility
    @property
    def pacer_table(self) -> str:
        return self.tables.get("Pacer", DynamoDBTableConfig(table_name="Pacer")).table_name
    
    @property
    def fb_archive_table(self) -> str:
        return self.tables.get("FBAdArchive", DynamoDBTableConfig(table_name="FBAdArchive")).table_name
    
    @property
    def law_firms_table(self) -> str:
        return self.tables.get("LawFirms", DynamoDBTableConfig(table_name="LawFirms")).table_name
    
    @property
    def jpml_data_table(self) -> str:
        return self.tables.get("JPMLData", DynamoDBTableConfig(table_name="JPMLData")).table_name
    
    @property
    def district_courts_table(self) -> str:
        return self.tables.get("DistrictCourts", DynamoDBTableConfig(table_name="DistrictCourts")).table_name
    
    @property
    def attorneys_table(self) -> str:
        return self.tables.get("Attorneys", DynamoDBTableConfig(table_name="Attorneys")).table_name
    
    @property
    def email_map_table(self) -> str:
        return self.tables.get("EmailMap", DynamoDBTableConfig(table_name="EmailMap")).table_name
    
    # Index names
    use_global_secondary_indexes: bool = Field(True, description="Use GSI for queries")
    gsi_projection_type: Literal["ALL", "KEYS_ONLY", "INCLUDE"] = Field(
        "ALL",
        description="GSI projection type"
    )
    
    # Query settings
    query_page_size: int = Field(100, ge=1, le=1000, description="Items per query page")
    scan_page_size: int = Field(50, ge=1, le=1000, description="Items per scan page")
    batch_write_size: int = Field(25, ge=1, le=25, description="Items per batch write")
    
    # Retry configuration
    max_retries: int = Field(3, ge=0, le=10, description="Maximum retry attempts")
    retry_backoff_base: float = Field(2.0, ge=1.0, le=10.0, description="Exponential backoff base")
    retry_backoff_max: float = Field(60.0, ge=1.0, le=300.0, description="Maximum backoff time")


class CacheConfig(BaseModel):
    """Caching configuration"""
    # Cache backend
    cache_backend: Literal["memory", "redis", "sqlite", "file"] = Field(
        "sqlite",
        description="Cache backend to use"
    )
    
    # Redis settings (if using Redis)
    redis_host: str = Field("localhost", description="Redis host")
    redis_port: int = Field(6379, ge=1, le=65535, description="Redis port")
    redis_password: Optional[str] = Field(None, description="Redis password")
    redis_db: int = Field(0, ge=0, le=15, description="Redis database number")
    
    # SQLite settings (if using SQLite)
    sqlite_path: Path = Field(Path("sqlite/cache.db"), description="SQLite cache database path")
    
    # File cache settings (if using file cache)
    file_cache_dir: Path = Field(Path("cache/files"), description="File cache directory")
    
    # Cache behavior
    default_ttl_seconds: int = Field(3600, ge=60, le=86400, description="Default TTL in seconds")
    max_cache_size_mb: int = Field(1000, ge=10, le=10000, description="Maximum cache size in MB")
    eviction_policy: Literal["lru", "lfu", "ttl"] = Field("lru", description="Cache eviction policy")
    
    # Cache key prefixes
    embedding_cache_prefix: str = Field("emb:", description="Prefix for embedding cache keys")
    api_cache_prefix: str = Field("api:", description="Prefix for API response cache keys")
    image_cache_prefix: str = Field("img:", description="Prefix for image cache keys")
    
    @field_validator('sqlite_path', 'file_cache_dir', mode='before')
    @classmethod
    def ensure_cache_dirs_exist(cls, v):
        """Ensure cache directories exist"""
        path = Path(v)
        path.parent.mkdir(parents=True, exist_ok=True)
        return path


class PathConfig(BaseModel):
    """Path configuration for data organization"""
    # Data directory structure
    data_root: Path = Field(Path("data"), description="Root data directory")
    date_format: str = Field("%Y/%m/%d", description="Date-based directory format")
    
    # Court data paths
    court_data_pattern: str = Field(
        "{date}/{court_id}",
        description="Pattern for court data directories"
    )
    docket_file_pattern: str = Field(
        "{docket_number}_{date}.json",
        description="Pattern for docket files"
    )
    
    # Facebook data paths
    fb_data_pattern: str = Field(
        "fb_ads/{date}",
        description="Pattern for Facebook ad data"
    )
    fb_image_pattern: str = Field(
        "images/{ad_id}_{hash}.{ext}",
        description="Pattern for Facebook ad images"
    )
    
    # Report paths
    report_output_dir: Path = Field(
        Path("reports/output"),
        description="Report output directory"
    )
    report_archive_dir: Path = Field(
        Path("reports/archive"),
        description="Report archive directory"
    )
    
    # Processing paths
    processing_queue_dir: Path = Field(
        Path("processing/queue"),
        description="Processing queue directory"
    )
    processing_complete_dir: Path = Field(
        Path("processing/complete"),
        description="Completed processing directory"
    )
    
    def get_court_data_path(self, date: str, court_id: str) -> Path:
        """Get path for court data"""
        return self.data_root / self.court_data_pattern.format(date=date, court_id=court_id)
    
    def get_fb_data_path(self, date: str) -> Path:
        """Get path for Facebook data"""
        return self.data_root / self.fb_data_pattern.format(date=date)