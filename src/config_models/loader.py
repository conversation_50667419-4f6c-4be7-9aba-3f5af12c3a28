"""
Configuration loader that supports both old YAML format and new Pydantic models.
Provides backward compatibility during migration.
"""

import json
import yaml
import warnings
from pathlib import Path
from typing import Dict, Any, Optional, Type, TypeVar, Union
from datetime import datetime

from .base import BaseConfig, WorkflowConfig
from .features import FeatureFlags
from .scraper import ScraperConfig
from .fb_ads import FBAdConfig
from .reports import ReportConfig
from .storage import StorageConfig, DatabaseConfig, CacheConfig, PathConfig

# Rebuild models after FeatureFlags is defined
WorkflowConfig.model_rebuild()
ScraperConfig.model_rebuild()

T = TypeVar('T', bound=BaseConfig)


class ConfigLoader:
    """Loads configuration from various sources with backward compatibility"""
    
    # Mapping of old config names to new Pydantic models
    CONFIG_MAPPINGS = {
        'scraper': ScraperConfig,
        'transform': ScraperConfig,  # Transform uses same config
        'report': ReportConfig,
        'weekly_report': ReportConfig,
        'fb_ads': FBAdConfig,
    }
    
    def __init__(self, config_dir: Path = Path("config")):
        self.config_dir = Path(config_dir)
        self._cache: Dict[str, Any] = {}
        self._feature_flags: Optional[FeatureFlags] = None
        self._storage_config: Optional[StorageConfig] = None
    
    def load_workflow_config(
        self, 
        config_name: str, 
        params: Optional[Dict[str, Any]] = None
    ) -> WorkflowConfig:
        """Load workflow configuration by name"""
        config_class = self.CONFIG_MAPPINGS.get(config_name, WorkflowConfig)
        
        # Try new config location first
        new_config_path = self.config_dir / "workflows" / f"{config_name}.yml"
        if new_config_path.exists():
            return self._load_new_config(new_config_path, config_class, params)
        
        # Fall back to old config location
        old_config_path = self.config_dir / f"{config_name}.yml"
        if old_config_path.exists():
            warnings.warn(
                f"Loading config from deprecated location: {old_config_path}. "
                f"Please move to {new_config_path}",
                DeprecationWarning
            )
            return self._load_old_config(old_config_path, config_class, params)
        
        raise FileNotFoundError(f"Configuration not found for: {config_name}")
    
    def load_feature_flags(self, force_reload: bool = False) -> FeatureFlags:
        """Load feature flags configuration"""
        if self._feature_flags is not None and not force_reload:
            return self._feature_flags
        
        # Try new location
        new_path = Path("src/config_models/features.json")
        if new_path.exists():
            with open(new_path) as f:
                data = json.load(f)
            self._feature_flags = FeatureFlags(**data)
            return self._feature_flags
        
        # Try old location
        old_path = Path("src/config/feature_flags.json")
        if old_path.exists():
            with open(old_path) as f:
                data = json.load(f)
            self._feature_flags = FeatureFlags(**data)
            return self._feature_flags
        
        # Return defaults
        self._feature_flags = FeatureFlags()
        return self._feature_flags
    
    def load_storage_config(self, force_reload: bool = False) -> StorageConfig:
        """Load storage configuration"""
        if self._storage_config is not None and not force_reload:
            return self._storage_config
        
        # Storage config comes from environment variables
        self._storage_config = StorageConfig()
        return self._storage_config
    
    def load_static_data(self, data_type: str) -> Dict[str, Any]:
        """Load static data files (JSON)"""
        cache_key = f"static_{data_type}"
        if cache_key in self._cache:
            return self._cache[cache_key]
        
        # Map data types to file paths
        data_paths = {
            'district_courts': 'courts/district_courts.json',
            'mdl_lookup': 'mdl/mdl_lookup.json',
            'attorney_lookup': 'attorneys/attorney_lookup.json',
            'company_mapping': 'fb_ads/company_name_mapping.json',
            'campaign_config': 'fb_ad_categorizer/campaign_config.json',
            'law_firm_normalization': 'law_firms/name_normalization.json',
        }
        
        if data_type not in data_paths:
            raise ValueError(f"Unknown data type: {data_type}")
        
        # Try new location first
        new_path = self.config_dir / "data" / data_paths[data_type]
        if new_path.exists():
            with open(new_path) as f:
                data = json.load(f)
                self._cache[cache_key] = data
                return data
        
        # Fall back to old location
        old_path = Path("src/config") / data_paths[data_type]
        if old_path.exists():
            with open(old_path) as f:
                data = json.load(f)
                self._cache[cache_key] = data
                return data
        
        raise FileNotFoundError(f"Static data file not found for: {data_type}")
    
    def _load_new_config(
        self, 
        path: Path, 
        config_class: Type[T], 
        params: Optional[Dict[str, Any]] = None
    ) -> T:
        """Load configuration in new format"""
        with open(path) as f:
            data = yaml.safe_load(f)
        
        # Add workflow name if not present
        if 'name' not in data:
            # Extract name from filename (e.g., scrape.yml -> scraper)
            data['name'] = path.stem
        
        # Merge with params if provided
        if params:
            data.update(params)
        
        # Rebuild model to resolve forward references before instantiation
        try:
            config_class.model_rebuild()
        except Exception:
            # If rebuild fails, continue anyway - might be resolved later
            pass
        
        return config_class(**data)
    
    def _load_old_config(
        self, 
        path: Path, 
        config_class: Type[T], 
        params: Optional[Dict[str, Any]] = None
    ) -> T:
        """Load configuration in old format and convert"""
        with open(path) as f:
            data = yaml.safe_load(f)
        
        # Convert old format to new format
        converted = self._convert_old_to_new(data, config_class)
        
        # Merge with params if provided
        if params:
            converted.update(params)
        
        # Rebuild model to resolve forward references before instantiation
        try:
            config_class.model_rebuild()
        except Exception:
            # If rebuild fails, continue anyway - might be resolved later
            pass
        
        return config_class(**converted)
    
    def _convert_old_to_new(self, old_data: Dict[str, Any], config_class: Type[T]) -> Dict[str, Any]:
        """Convert old configuration format to new format"""
        # Handle date conversion
        if 'date' in old_data and isinstance(old_data['date'], str):
            # Old format uses MM/DD/YY
            old_data['date'] = self._parse_old_date(old_data['date'])
        
        # Map old field names to new ones
        field_mappings = {
            'run_parallel': 'parallel',
            'num_workers': 'num_workers',
            'process_single_court': 'process_single_court',
            'process_single_file': 'process_single_file',
        }
        
        converted = {}
        for old_key, new_key in field_mappings.items():
            if old_key in old_data:
                converted[new_key] = old_data[old_key]
        
        # Copy over fields that haven't changed
        for key in ['scraper', 'post_process', 'upload', 'report_generator', 
                    'fb_ads', 'headless', 'name', 'enabled']:
            if key in old_data:
                converted[key] = old_data[key]
        
        # Add workflow name if not present
        if 'name' not in converted:
            converted['name'] = config_class.__name__.replace('Config', '').lower()
        
        # Copy date field from old data
        if 'date' in old_data:
            converted['date'] = old_data['date']
        
        return converted
    
    def _parse_old_date(self, date_str: str) -> datetime:
        """Parse date in old MM/DD/YY format"""
        if '/' in date_str:
            parts = date_str.split('/')
            if len(parts) == 3 and len(parts[2]) == 2:
                # Convert YY to YYYY
                parts[2] = f"20{parts[2]}"
                date_str = '/'.join(parts)
            return datetime.strptime(date_str, "%m/%d/%Y")
        return datetime.strptime(date_str, "%Y-%m-%d")
    
    def migrate_config_file(self, old_path: Path, new_path: Path) -> None:
        """Migrate a configuration file to the new location"""
        # Load old config
        with open(old_path) as f:
            if old_path.suffix == '.json':
                data = json.load(f)
            else:
                data = yaml.safe_load(f)
        
        # Ensure new directory exists
        new_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Write to new location
        with open(new_path, 'w') as f:
            if new_path.suffix == '.json':
                json.dump(data, f, indent=2)
            else:
                yaml.dump(data, f, default_flow_style=False)
        
        print(f"Migrated {old_path} → {new_path}")


# Global config loader instance
config_loader = ConfigLoader()


def load_config(
    config_name: str, 
    params: Optional[Dict[str, Any]] = None
) -> WorkflowConfig:
    """Convenience function to load configuration"""
    return config_loader.load_workflow_config(config_name, params)


def load_feature_flags() -> FeatureFlags:
    """Convenience function to load feature flags"""
    return config_loader.load_feature_flags()


def load_storage_config() -> StorageConfig:
    """Convenience function to load storage configuration"""
    return config_loader.load_storage_config()


def load_static_data(data_type: str) -> Dict[str, Any]:
    """Convenience function to load static data"""
    return config_loader.load_static_data(data_type)