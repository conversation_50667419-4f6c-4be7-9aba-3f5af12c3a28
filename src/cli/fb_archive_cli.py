"""
Facebook Ad Archive CLI - Command Line Interface
"""
import asyncio
import arg<PERSON><PERSON>
import json
from typing import Dict, Any, List
from pathlib import Path
import pandas as pd

from src.infrastructure.storage.dynamodb_async import AsyncDynamoDBStorage
from src.repositories.fb_archive_repository import FBArchiveRepository
from src.services.fb_archive.query_service import FBArchiveQueryService
from src.services.fb_archive.update_service import FBArchiveUpdateService
from src.services.fb_archive.delete_service import FBArchiveDeleteService
from src.services.fb_archive.data_conversion_service import FBArchiveDataConversionService


class FBArchiveCLI:
    """Command line interface for FB Archive operations"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.storage = None
        self.repository = None
        self.query_service = None
        self.update_service = None
        self.delete_service = None
        self.conversion_service = None
    
    async def __aenter__(self):
        """Initialize async resources"""
        self.storage = await AsyncDynamoDBStorage(self.config).__aenter__()
        self.repository = FBArchiveRepository(self.storage)
        self.query_service = FBArchiveQueryService(self.repository)
        self.update_service = FBArchiveUpdateService(self.repository, self.config)
        self.delete_service = FBArchiveDeleteService(self.repository)
        self.conversion_service = FBArchiveDataConversionService(self.repository)
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Cleanup async resources"""
        if self.storage:
            await self.storage.__aexit__(exc_type, exc_val, exc_tb)
    
    async def query_by_page_id(self, page_id: str):
        """Query ads by page ID"""
        print(f"\nQuerying ads for PageID: {page_id}")
        
        # Get grouped results
        grouped = await self.query_service.get_ads_by_page_id_grouped(page_id)
        
        for law_firm, ads in grouped.items():
            print(f"\n{law_firm}: {len(ads)} ads")
            for ad in ads[:5]:  # Show first 5
                print(f"  - {ad.get('AdArchiveID')} ({ad.get('StartDate')})")
            if len(ads) > 5:
                print(f"  ... and {len(ads) - 5} more")
    
    async def query_by_date(self, date: str):
        """Query ads by date"""
        print(f"\nQuerying ads for date: {date}")
        
        stats = await self.query_service.get_statistics_by_date(date)
        
        print(f"Total ads: {stats['total_ads']}")
        print(f"Unique pages: {stats['unique_pages']}")
        print(f"Unique law firms: {stats['unique_law_firms']}")
        print(f"Active ads: {stats['active_ads']}")
        print(f"Inactive ads: {stats['inactive_ads']}")
        
        print("\nTop law firms:")
        sorted_firms = sorted(stats['ads_by_law_firm'].items(), 
                            key=lambda x: x[1], reverse=True)
        for firm, count in sorted_firms[:10]:
            print(f"  {firm}: {count} ads")
    
    async def update_law_firm(self, page_id: str):
        """Interactive law firm update"""
        # Get current records
        records = await self.repository.query_by_page_id(page_id)
        
        if not records:
            print(f"No records found for PageID: {page_id}")
            return
        
        # Show current law firms
        law_firms = {}
        for record in records:
            firm = record.get('LawFirm', 'Unknown')
            law_firms[firm] = law_firms.get(firm, 0) + 1
        
        print(f"\nCurrent law firms for PageID {page_id}:")
        for firm, count in law_firms.items():
            print(f"  {firm}: {count} records")
        
        # Get update parameters
        current_name = input("\nEnter current law firm name to update (or press Enter for all): ").strip()
        new_name = input("Enter new law firm name: ").strip()
        
        if not new_name:
            print("No new name provided, cancelling")
            return
        
        # Confirm
        target_count = law_firms.get(current_name, sum(law_firms.values())) if current_name else sum(law_firms.values())
        confirm = input(f"\nUpdate {target_count} records to '{new_name}'? (y/n): ")
        
        if confirm.lower() != 'y':
            print("Update cancelled")
            return
        
        # Perform update
        updated = await self.update_service.update_law_firm_name(
            page_id, new_name, current_name if current_name else None
        )
        
        print(f"Updated {updated} records")
    
    async def cleanup_cdn(self):
        """Clean up CDN attributes"""
        print("\nScanning for CDN attributes...")
        
        cleaned, attributes = await self.update_service.cleanup_cdn_attributes()
        
        print(f"\nCleaned {cleaned} records")
        print(f"Removed attributes: {', '.join(attributes)}")
    
    async def delete_by_page(self, page_id: str):
        """Delete records by page ID"""
        def confirm_batch(batch_info, batch_num, total_batches):
            print(f"\nBatch {batch_num}/{total_batches}:")
            for info in batch_info[:5]:
                print(f"  {info}")
            if len(batch_info) > 5:
                print(f"  ... and {len(batch_info) - 5} more")
            
            response = input("Delete this batch? (y/n/q): ").lower()
            return response == 'y'
        
        deleted, archive_ids = await self.delete_service.delete_by_page_id(
            page_id, confirm_callback=confirm_batch
        )
        
        print(f"\nDeleted {deleted} records")
        
        # Save archive IDs
        if archive_ids:
            with open('deleted_archive_ids.txt', 'w') as f:
                for ad_id in archive_ids:
                    f.write(f"{ad_id}\n")
            print(f"Saved {len(archive_ids)} archive IDs to deleted_archive_ids.txt")
    
    async def export_data(self, output_file: str, filters: Dict[str, Any] = None):
        """Export data to CSV"""
        print(f"\nExporting data to {output_file}...")
        
        records = await self.conversion_service.export_to_dataframe_format(filters)
        
        df = pd.DataFrame(records)
        df.to_csv(output_file, index=False)
        
        print(f"Exported {len(records)} records to {output_file}")
    
    async def find_duplicates(self):
        """Find duplicate ads"""
        print("\nSearching for duplicate ads...")
        
        duplicates = await self.query_service.find_duplicate_ads()
        
        print(f"Found {len(duplicates)} groups of duplicates")
        
        for idx, (content_hash, ads) in enumerate(duplicates.items()):
            if idx >= 10:  # Show first 10
                print(f"\n... and {len(duplicates) - 10} more groups")
                break
            
            print(f"\nDuplicate group {idx + 1} ({len(ads)} ads):")
            for ad in ads:
                print(f"  - {ad['AdArchiveID']} ({ad['LawFirm']})")
    
    async def fix_dates(self):
        """Fix date format issues"""
        print("\nFixing date format issues...")
        
        fixed, failed = await self.conversion_service.fix_date_formats()
        
        print(f"Fixed: {fixed} records")
        print(f"Failed: {failed} records")


async def main():
    """Main CLI entry point"""
    parser = argparse.ArgumentParser(description='Facebook Ad Archive CLI')
    parser.add_argument('--config', type=str, help='Config file path')
    
    subparsers = parser.add_subparsers(dest='command', help='Commands')
    
    # Query commands
    query_parser = subparsers.add_parser('query', help='Query ads')
    query_parser.add_argument('--page-id', type=str, help='Query by page ID')
    query_parser.add_argument('--date', type=str, help='Query by date (YYYYMMDD)')
    query_parser.add_argument('--archive-ids', nargs='+', help='Query by archive IDs')
    
    # Update commands
    update_parser = subparsers.add_parser('update', help='Update records')
    update_parser.add_argument('--law-firm', type=str, help='Update law firm for page ID')
    update_parser.add_argument('--cleanup-cdn', action='store_true', help='Clean up CDN attributes')
    update_parser.add_argument('--fix-dates', action='store_true', help='Fix date formats')
    
    # Delete commands
    delete_parser = subparsers.add_parser('delete', help='Delete records')
    delete_parser.add_argument('--page-id', type=str, help='Delete by page ID')
    delete_parser.add_argument('--date', type=str, help='Delete by date')
    
    # Export command
    export_parser = subparsers.add_parser('export', help='Export data')
    export_parser.add_argument('output', type=str, help='Output file path')
    export_parser.add_argument('--page-id', type=str, help='Filter by page ID')
    export_parser.add_argument('--date', type=str, help='Filter by date')
    
    # Utility commands
    util_parser = subparsers.add_parser('util', help='Utility commands')
    util_parser.add_argument('--find-duplicates', action='store_true', help='Find duplicate ads')
    
    args = parser.parse_args()
    
    # Load config
    config = {}
    if args.config:
        config_path = Path(args.config)
        if config_path.exists():
            with open(config_path) as f:
                config = json.load(f)
    
    # Execute command
    async with FBArchiveCLI(config) as cli:
        if args.command == 'query':
            if args.page_id:
                await cli.query_by_page_id(args.page_id)
            elif args.date:
                await cli.query_by_date(args.date)
            elif args.archive_ids:
                ads = await cli.query_service.get_ads_by_archive_ids(args.archive_ids)
                print(f"Found {len(ads)} ads")
                for ad in ads:
                    print(f"  {ad.get('AdArchiveID')} - {ad.get('LawFirm')} ({ad.get('StartDate')})")
        
        elif args.command == 'update':
            if args.law_firm:
                await cli.update_law_firm(args.law_firm)
            elif args.cleanup_cdn:
                await cli.cleanup_cdn()
            elif args.fix_dates:
                await cli.fix_dates()
        
        elif args.command == 'delete':
            if args.page_id:
                await cli.delete_by_page(args.page_id)
            elif args.date:
                deleted = await cli.delete_service.delete_by_last_updated(args.date)
                print(f"Deleted {deleted} records")
        
        elif args.command == 'export':
            filters = {}
            if args.page_id:
                filters['page_id'] = args.page_id
            if args.date:
                filters['last_updated'] = args.date
            await cli.export_data(args.output, filters)
        
        elif args.command == 'util':
            if args.find_duplicates:
                await cli.find_duplicates()


if __name__ == '__main__':
    asyncio.run(main())