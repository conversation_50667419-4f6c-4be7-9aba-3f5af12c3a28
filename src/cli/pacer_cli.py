"""
PACER CLI - Command Line Interface for PACER operations
"""
import argparse
import asyncio
import logging
from typing import Optional

from src.config_models.loader import load_config
from src.infrastructure.storage.dynamodb_async import AsyncDynamoDBStorage
from src.repositories.pacer_repository import PacerRepository
from src.services.pacer.query_service import PacerQueryService
from src.services.pacer.analytics_service import PacerAnalyticsService
from src.services.pacer.export_service import PacerExportService
from src.services.pacer.interactive_service import PacerInteractiveService


async def main():
    """Main CLI entry point"""
    # Parse arguments
    parser = argparse.ArgumentParser(description="Query the PACER database.")
    
    # Query options
    parser.add_argument("--docket-num", help="Docket number to query.")
    parser.add_argument("--court-id", help="Court ID to query.")
    parser.add_argument("--mdl-num", help="MDL number to query.")
    parser.add_argument("--filing-date", help="Filing date (YYYYMMDD) to query.")
    parser.add_argument("--start-date", help="Start date (YYYYMMDD) for date range queries.")
    parser.add_argument("--end-date", help="End date (YYYYMMDD) for date range queries.")
    parser.add_argument("--defendant", help="Search for string in defendant or defendants fields.")
    parser.add_argument("--versus", help="Search for string in versus field.")
    parser.add_argument("--summary", help="Search for string in summary field.")
    parser.add_argument("--law-firm", help="Search for law firm name.")
    parser.add_argument("--title", help="Search for string in title field.")
    
    # Output options
    parser.add_argument("--output", help="Output results to CSV file (e.g., results.csv).")
    parser.add_argument("--output-fields", help="Comma-separated list of fields to include in output.")
    
    # Interactive options
    parser.add_argument("--interactive-mdl", action="store_true", 
                       help="Interactive MDL query and update mode.")
    parser.add_argument("--interactive-title", action="store_true",
                       help="Interactive title search and update mode.")
    parser.add_argument("--interactive-law-firm", action="store_true",
                       help="Interactive law firm search mode.")
    parser.add_argument("--scan-duplicates", action="store_true",
                       help="Scan for duplicate records.")
    
    # Analytics options
    parser.add_argument("--mdl-summary", help="Generate MDL summary for date (YYYYMMDD).")
    parser.add_argument("--extract-law-firms", action="store_true",
                       help="Extract all unique law firm names from the database.")
    
    # Configuration options
    parser.add_argument("--local", action="store_true", help="Use local DynamoDB.")
    parser.add_argument("--local-port", type=int, default=8000, help="Local DynamoDB port.")
    parser.add_argument("--config", help="Path to configuration file.")
    
    args = parser.parse_args()
    
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    logging.getLogger('boto3').setLevel(logging.WARNING)
    logging.getLogger('botocore').setLevel(logging.WARNING)
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    
    # Load configuration
    config_path = args.config or 'config/scrape.yml'
    config = load_config(config_path)
    
    # Override with CLI arguments
    if args.local:
        config.use_local = True
        config.local_port = args.local_port
    
    # Initialize services
    async with AsyncDynamoDBStorage(config) as storage:
        repository = PacerRepository(storage)
        query_service = PacerQueryService(repository)
        analytics_service = PacerAnalyticsService(repository)
        export_service = PacerExportService()
        interactive_service = PacerInteractiveService(query_service)
        
        # Parse output fields
        output_fields = None
        if args.output_fields:
            output_fields = [f.strip() for f in args.output_fields.split(',')]
        
        # Handle different query modes
        results = None
        
        # Interactive modes
        if args.interactive_mdl:
            await interactive_service.interactive_mdl_query_and_update()
            return
        elif args.interactive_title:
            await interactive_service.interactive_title_search_and_update()
            return
        elif args.interactive_law_firm:
            await interactive_service.interactive_law_firm_search()
            return
        elif args.scan_duplicates:
            await interactive_service.interactive_duplicate_scan()
            return
        
        # Analytics modes
        elif args.mdl_summary:
            df = await analytics_service.get_mdl_summary(args.mdl_summary)
            print(df)
            if args.output:
                df.to_csv(args.output, index=False)
            return
        elif args.extract_law_firms:
            law_firms = await analytics_service.get_unique_law_firms()
            print(f"Found {len(law_firms)} unique law firms")
            if args.output:
                with open(args.output, 'w') as f:
                    for firm in law_firms:
                        f.write(f"{firm}\n")
            else:
                for firm in law_firms[:20]:
                    print(f"  - {firm}")
                if len(law_firms) > 20:
                    print(f"  ... and {len(law_firms) - 20} more")
            return
        
        # Query modes
        elif args.docket_num and args.court_id:
            results = await repository.query_by_court_and_docket(
                args.court_id, args.docket_num
            )
        elif args.filing_date:
            results = await repository.query_by_filing_date(args.filing_date)
        elif args.mdl_num:
            results = await query_service.query_by_mdl_num(
                args.mdl_num, args.start_date, args.end_date
            )
        elif args.start_date:
            results = await query_service.query_by_date_range(
                args.start_date, args.end_date
            )
        elif args.title:
            results = await query_service.search_by_title(args.title)
        elif args.law_firm:
            results = await query_service.search_by_law_firm(args.law_firm)
        elif args.defendant:
            results = await query_service.search_by_defendant(args.defendant)
        elif args.versus:
            results = await query_service.search_by_versus(args.versus)
        elif args.summary:
            results = await query_service.search_by_summary(args.summary)
        else:
            parser.print_help()
            return
        
        # Display or export results
        if results is not None:
            export_service.display_results(results, args.output, output_fields)


if __name__ == "__main__":
    asyncio.run(main())