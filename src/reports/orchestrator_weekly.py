"""
Clean Weekly Report Orchestrator without compatibility layers.
Refactored version that eliminates all migration helpers and feature flags.
"""
import logging
import os
from datetime import datetime, timedelta
from typing import Dict, Any
import asyncio

import pandas as pd

# Direct async repository imports - no compatibility layers
from src.infrastructure.storage.dynamodb_async import AsyncDynamoDBStorage
from src.repositories.fb_archive_repository import FBArchiveRepository
from src.repositories.pacer_repository import PacerRepository
from src.repositories.law_firms_repository import LawFirmsRepository

from src.utils.law_firm import LawFirmNameHandler
from src.utils.law_firm_normalizer import get_normalizer
from .ad_df_processor import AdDataFrameProcessor
from .ad_page_generator import AdPageGenerator
from .cache_invalidator import CacheInvalidator
from .config import ReportConfig
from .data_loader import DynamicDataLoader, StaticDataLoader
from .publisher import ReportPublisher
from .renderer import ReportRenderer
from .processor import ReportDataProcessor
from src.infrastructure.storage.s3_async import S3AsyncStorage


class WeeklyReportOrchestrator:
    """
    Clean async Weekly Report Orchestrator using repositories directly.
    No migration helpers or compatibility layers.
    """

    def __init__(self, config_dict: Dict[str, Any]):
        self.logger = logging.getLogger(f"{__name__}.WeeklyReportOrchestrator")
        self.config = ReportConfig(config_dict, is_weekly_report=True)
        self.law_firm_handler = LawFirmNameHandler()

        # Store start and end dates
        self.end_date_iso = self.config.iso_date
        self.start_date_iso = self._calculate_start_date(self.end_date_iso)
        
        self.logger.info(f"Weekly report period: {self.start_date_iso} to {self.end_date_iso}")

        # Initialize storage and repositories (will be set up in __aenter__)
        self._storage = None
        self._pacer_repo = None
        self._fb_archive_repo = None
        self._law_firms_repo = None
        self._s3_manager = None
        
        # Components will be initialized in __aenter__
        self._static_data_loader = None
        self._ad_processor = None
        self._dynamic_data_loader = None
        self._data_processor = None
        self._renderer = None
        self._ad_page_generator = None
        self._publisher = None
        self._cache_invalidator = None

    async def __aenter__(self):
        """Async context manager entry - initialize all components."""
        # Create async storage and repositories
        storage_config = {
            'aws_region': self.config.config_dict.get('aws_region', 
                os.environ.get('AWS_REGION') or 
                os.environ.get('LEXGENIUS_AWS_REGION') or
                'us-west-2'
            ),
            'fb_ad_archive_table_name': 'FBAdArchive',
            'pacer_table_name': 'Pacer',
            'law_firms_table_name': 'LawFirms'
        }
        
        self._storage = AsyncDynamoDBStorage(storage_config)
        await self._storage.__aenter__()
        
        # Create repositories with shared storage (performance improvement!)
        self._pacer_repo = PacerRepository(self._storage)
        self._fb_archive_repo = FBArchiveRepository(self._storage)
        self._law_firms_repo = LawFirmsRepository(self._storage)
        
        # Initialize S3 if credentials are available
        aws_access_key = self.config.config_dict.get('aws_access_key')
        aws_secret_key = self.config.config_dict.get('aws_secret_key')
        if aws_access_key and aws_secret_key:
            self._s3_manager = S3AsyncStorage(
                aws_access_key_id=aws_access_key,
                aws_secret_access_key=aws_secret_key,
                region_name=storage_config['aws_region'],
                bucket_name=self.config.bucket_name
            )
            await self._s3_manager._initialize()
        
        # Initialize components with refactored classes
        self._static_data_loader = StaticDataLoader()
        
        self._ad_processor = AdDataFrameProcessor(
            fb_ad_db=self._fb_archive_repo,
            logger=self.logger,
            iso_date=self.config.iso_date,
            days_back=7
        )
        
        self._dynamic_data_loader = DynamicDataLoader(
            report_config=self.config,
            pacer_repository=self._pacer_repo,
            ad_processor=self._ad_processor,
            law_firm_handler=self.law_firm_handler
        )
        
        self._data_processor = ReportDataProcessor(
            report_config=self.config,
            pacer_repository=self._pacer_repo
        )
        
        self._renderer = ReportRenderer(
            report_config=self.config,
            static_data=self._static_data_loader,
            report_processor=self._data_processor
        )
        
        if self._s3_manager:
            self._ad_page_generator = AdPageGenerator(
                report_config=self.config,
                renderer=self._renderer,
                s3_manager=self._s3_manager
            )
            self._publisher = ReportPublisher(
                report_config=self.config,
                s3_manager=self._s3_manager
            )
        
        self._cache_invalidator = CacheInvalidator(self.config)
        
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit - cleanup resources."""
        if self._storage:
            await self._storage.__aexit__(exc_type, exc_val, exc_tb)

    def _calculate_start_date(self, end_date_iso: str) -> str:
        """Calculate the start date (7 days before end date)"""
        try:
            end_date = datetime.strptime(end_date_iso, "%Y%m%d")
            start_date = end_date - timedelta(days=7)
            return start_date.strftime("%Y%m%d")
        except ValueError:
            self.logger.error(f"Invalid ISO date format: {end_date_iso}")
            # Fallback to 7 days ago from today
            return (datetime.now() - timedelta(days=7)).strftime("%Y%m%d")

    async def _aggregate_docket_data_async(self) -> pd.DataFrame:
        """Aggregates docket data for the whole 7-day period using async methods."""
        self.logger.info(f"Aggregating docket data from {self.start_date_iso} to {self.end_date_iso}")

        # Use async data loader for each day to ensure consistent processing
        all_daily_dfs = []
        current_date = datetime.strptime(self.start_date_iso, "%Y%m%d")
        end_date = datetime.strptime(self.end_date_iso, "%Y%m%d")

        while current_date <= end_date:
            iso_date = current_date.strftime("%Y%m%d")
            self.logger.info(f"Loading dockets for date: {iso_date}")

            try:
                # Create a temporary config for this specific date
                date_obj = datetime.strptime(iso_date, "%Y%m%d")
                date_str = date_obj.strftime("%m/%d/%y")

                daily_config = {
                    'date': date_str,
                    'project_root': getattr(self.config, 'project_root', None),
                    'bucket_name': getattr(self.config, 'bucket_name', 'lexgenius-dockets'),
                    'show_litigation': True,
                    'show_filings': True,
                    'show_summary': True,
                    'show_afff_stats': True,
                    'show_upcoming_hearings': False,
                    'show_special_reports': False,
                    'show_news': False,
                    'show_announcements': False,
                    'show_adspy': True
                }

                # Use refactored DynamicDataLoader with async methods
                temp_config = ReportConfig(daily_config)
                temp_loader = DynamicDataLoader(
                    report_config=temp_config,
                    pacer_repository=self._pacer_repo,
                    ad_processor=self._ad_processor,
                    law_firm_handler=self.law_firm_handler
                )

                # Use async method
                daily_df = await temp_loader.load_docket_df_async(is_weekly=False)
                if daily_df is not None and not daily_df.empty:
                    all_daily_dfs.append(daily_df)
                    self.logger.info(f"Added {len(daily_df)} processed docket items from {iso_date}")
                else:
                    self.logger.info(f"No docket items found for {iso_date}")

            except Exception as e:
                self.logger.error(f"Error loading dockets for {iso_date}: {e}", exc_info=True)

            current_date += timedelta(days=1)

        if not all_daily_dfs:
            self.logger.warning("No docket items found for the weekly period.")
            return pd.DataFrame()

        # Combine all daily DataFrames
        try:
            combined_df = pd.concat(all_daily_dfs, ignore_index=True)
            self.logger.info(
                f"Successfully combined {len(combined_df)} docket entries from {len(all_daily_dfs)} days for weekly report.")
            return combined_df

        except Exception as e:
            self.logger.error(f"Error combining daily docket data: {e}", exc_info=True)
            return pd.DataFrame()

    async def _aggregate_ad_data_async(self) -> pd.DataFrame:
        """Aggregates ad data for the whole 7-day period using async methods."""
        self.logger.info(f"Aggregating ad data from {self.start_date_iso} to {self.end_date_iso}")

        try:
            # Use async method for ad loading
            ad_df = await self._dynamic_data_loader.load_ad_df_async()

            if ad_df is None or ad_df.empty:
                self.logger.warning("Ad DataFrame is empty or failed to load.")
                return pd.DataFrame()

            # Filter for ads within the weekly date range based on StartDate
            if 'start_date' in ad_df.columns:
                ad_df['start_date_dt'] = pd.to_datetime(ad_df['start_date'], format='%m/%d/%y', errors='coerce')
                start_date = datetime.strptime(self.start_date_iso, "%Y%m%d")

                # For weekly reports, include any ad that has StartDate >= 7 days ago
                filtered_df = ad_df[ad_df['start_date_dt'] >= start_date]

                self.logger.info(
                    f"Weekly AdSpy: Filtered from {len(ad_df)} to {len(filtered_df)} ads with StartDate >= {self.start_date_iso}")
                ad_df = filtered_df

                # Drop the temporary datetime column, keep original formatted start_date
                ad_df = ad_df.drop(columns=['start_date_dt'])

            return ad_df

        except Exception as e:
            self.logger.error(f"Error aggregating ad data: {e}", exc_info=True)
            return pd.DataFrame()

    async def _load_and_process_weekly_data_async(self) -> Dict[str, Any]:
        """Loads and processes all data needed for the weekly report using async methods."""
        self.logger.info("--- Loading and Processing Data for Weekly Report ---")
        report_data = {}

        # Load Dynamic Data using async methods
        try:
            self.logger.info("Aggregating weekly docket data...")
            report_data['docket_df'] = await self._aggregate_docket_data_async()

            self.logger.info("Aggregating weekly ad data...")
            report_data['ad_df'] = await self._aggregate_ad_data_async()
            self.logger.info(f"Weekly ad aggregation completed: {len(report_data['ad_df'])} ads in final ad_df")

        except Exception as load_err:
            self.logger.error(f"Error during weekly data loading: {load_err}", exc_info=True)
            if 'docket_df' not in report_data: 
                report_data['docket_df'] = pd.DataFrame()
            if 'ad_df' not in report_data: 
                report_data['ad_df'] = pd.DataFrame()

        # Calculate AFFF Stats using async method
        if self.config.show_afff_stats:
            report_data['afff_stats'] = await self._data_processor.calculate_afff_stats_async(report_data['docket_df'])
        else:
            report_data['afff_stats'] = None

        # Static data loading (remains synchronous as it's file-based)
        if self.config.show_upcoming_hearings:
            report_data['upcoming_hearings'] = self._static_data_loader.load_upcoming_hearings()
        if self.config.show_special_reports:
            report_data['special_reports'] = self._static_data_loader.load_special_reports()
        if self.config.show_news:
            report_data['news_items'] = self._static_data_loader.load_news()
        if self.config.show_announcements:
            report_data['announcements'] = self._static_data_loader.load_announcements()
        
        report_data['general_sponsorships'] = self._static_data_loader.load_general_sponsorships()

        # Data processing using async methods
        if self.config.show_litigation:
            grouped_litigation, title_totals = await self._data_processor.group_filings_by_litigation_async(report_data['docket_df'])
            report_data['grouped_litigation'] = grouped_litigation
            report_data['title_totals'] = title_totals
        else:
            report_data['grouped_litigation'], report_data['title_totals'] = {}, pd.DataFrame()

        if self.config.show_filings:
            report_data['detailed_filings'] = await self._data_processor.group_filings_by_firm_async(report_data['docket_df'])
        else:
            report_data['detailed_filings'] = {}

        self.logger.info(f"Weekly report: show_adspy = {self.config.show_adspy}")
        if self.config.show_adspy:
            self.logger.info(f"Processing {len(report_data['ad_df'])} ads for AdSpy section")
            report_data['grouped_ads'] = await self._data_processor.filter_ad_summaries_async(report_data['ad_df'])
            self.logger.info(f"After filtering: {len(report_data['grouped_ads'])} law firms with ads")
        else:
            self.logger.info("AdSpy is disabled - setting grouped_ads to empty dict")
            report_data['grouped_ads'] = {}

        # Get MDL Summary Data using async method
        if self.config.show_summary:
            chart_labels, chart_data = await self._data_processor.get_mdl_summary_data_async(
                afff_stats=report_data.get('afff_stats')
            )
            report_data['chart_labels'], report_data['chart_data'] = chart_labels, chart_data
        else:
            report_data['chart_labels'], report_data['chart_data'] = [], []

        self.logger.info(f"--- Finished Loading and Processing Weekly Data ---")
        return report_data

    async def generate_report_async(self, skip_ads: bool = False, skip_invalidate: bool = False) -> bool:
        """Generates the weekly report using async methods."""
        self.logger.info(f"Generating weekly report for period {self.start_date_iso} to {self.end_date_iso}")

        try:
            # Load and process the data
            report_data = await self._load_and_process_weekly_data_async()

            # Generate both web and email versions of the report
            web_success = await self._generate_single_report_async(report_data, is_web=True)
            email_success = await self._generate_single_report_async(report_data, is_web=False)

            overall_success = web_success and email_success

            # Ad page generation if configured and successful so far
            if self.config.show_adspy and overall_success and not skip_ads and self._ad_page_generator:
                self.logger.info("--- Generating Ad HTML Pages for Weekly Report ---")
                try:
                    ad_df_to_use = report_data.get('ad_df')
                    if ad_df_to_use is not None and not ad_df_to_use.empty:
                        await self._ad_page_generator.generate_and_upload_ad_pages(ad_df_to_use)
                    else:
                        self.logger.warning("Skipping ad page generation as Ad DataFrame is empty.")
                except Exception as e:
                    self.logger.error(f"ERROR during weekly ad page generation: {e}", exc_info=True)
            elif skip_ads:
                self.logger.info("--- Skipping Ad Page Generation (skip_ads=True) ---")

            # Cache invalidation if configured and successful so far
            if overall_success and not skip_invalidate:
                self.logger.info("--- Invalidating CloudFront Cache for Weekly Report ---")
                try:
                    await self._cache_invalidator.invalidate_cache()
                except Exception as e:
                    self.logger.error(f"ERROR during CloudFront invalidation for weekly report: {e}", exc_info=True)
            elif skip_invalidate:
                self.logger.info("--- Skipping CloudFront Cache Invalidation (skip_invalidate=True) ---")

            if overall_success:
                self.logger.info(f"Weekly report generation completed successfully.")
            else:
                self.logger.error(f"Weekly report generation completed with errors.")

            return overall_success

        except Exception as e:
            self.logger.error(f"Error generating weekly report: {e}", exc_info=True)
            return False

    async def _generate_single_report_async(self, report_data: Dict[str, Any], is_web: bool) -> bool:
        """Generates a single format (web or email) of the weekly report."""
        format_type = "web" if is_web else "email"
        self.logger.info(f"--- Generating weekly {format_type} report ---")

        try:
            # Rendering (run in thread since it's CPU-bound)
            if is_web:
                html_content = await asyncio.to_thread(
                    self._renderer.render_report, report_data, is_web=True, is_weekly=True
                )
            else:
                html_content = await asyncio.to_thread(
                    self._renderer.render_email_report, report_data, is_weekly=True
                )

            if "<h1>Error Generating Report</h1>" in html_content:
                self.logger.error(f"Detected rendering error in weekly {format_type} report output.")
                return False

            # Publishing using async method
            if self._publisher:
                generation_successful = await self._publisher.publish_report_async(html_content, is_web, is_weekly=True)
                if not generation_successful:
                    self.logger.error(f"Failed to publish weekly {format_type} report.")
                    return False
            else:
                self.logger.warning(f"No publisher available - skipping publication of weekly {format_type} report.")
                return False

            self.logger.info(f"Successfully generated weekly {format_type} report.")
            return True

        except Exception as e:
            self.logger.error(f"Error generating weekly {format_type} report: {e}", exc_info=True)
            return False

    # Backward compatibility - keep sync method during transition
    async def generate_report(self, skip_ads: bool = False, skip_invalidate: bool = False) -> bool:
        """Sync wrapper for backward compatibility. Use generate_report_async instead."""
        self.logger.warning("Using compatibility wrapper. Consider migrating to generate_report_async.")
        return await self.generate_report_async(skip_ads, skip_invalidate)