import asyncio
from typing import List, Di<PERSON>, <PERSON><PERSON>, Optional
import re
import pandas as pd
import logging
import json
import os
from datetime import datetime
from .config import ReportConfig
from .renderer import ReportRenderer
from src.infrastructure.storage.s3_async import S3AsyncStorage

class AdPageGenerator:
    """Generates individual HTML pages for ads and uploads them."""
    def __init__(self, report_config: ReportConfig, renderer: ReportRenderer, s3_manager: S3AsyncStorage):
        self.config = report_config
        self.renderer = renderer # Needs Jinja env
        self.s3_manager = s3_manager
        self.logger = logging.getLogger(f"{__name__}.AdPageGenerator")

    # --- Utility Methods (static or instance methods as appropriate) ---
    @staticmethod
    def _filter_curly_braces(text):
        return re.sub(r'\{\{.*?\}\}', '', text) if isinstance(text, str) else text

    @staticmethod
    def _check_and_format(value):
        if pd.isna(value): return ''
        value_str = str(value).strip()
        return '' if not value_str or value_str.lower() in {'nan', 'na', 'none', '<na>'} else value_str

    def _safe_process_platform(self, value) -> List[str]:
        # Uses _check_and_format and _filter_curly_braces
        # (Logic copied from original)
        if isinstance(value, list):
            cleaned_list = []
            for item in value:
                item_checked = self._check_and_format(item)
                if item_checked:
                    filtered_item = self._filter_curly_braces(item_checked)
                    if filtered_item: cleaned_list.append(filtered_item)
            return cleaned_list
        elif isinstance(value, str):
            value_str_checked = self._check_and_format(value)
            if not value_str_checked: return []
            # ... rest of string parsing logic from original ...
            return [self._filter_curly_braces(value_str_checked)] # Simplified example
        # ... handle other types / None ...
        return []

    @staticmethod
    def _get_image_uri(row_dict: Dict) -> str:
        # Uses _check_and_format
        img_url = AdPageGenerator._check_and_format(row_dict.get('original_image_url'))
        if img_url: return img_url
        vid_thumb_url = AdPageGenerator._check_and_format(row_dict.get('video_preview_image_url'))
        if vid_thumb_url: return vid_thumb_url
        return ''

    @staticmethod
    def _format_text_ad(value): # Renamed to avoid conflict
        # Copied from original docket loader - maybe centralize text formatting?
        if not isinstance(value, str): return ''
        value = value.strip()
        value = re.sub(r'\r\n', '\n', value)
        value = re.sub(r'\n{3,}', '\n\n', value)
        return value

    async def _create_s3_folders(self) -> bool:
        """Ensures necessary S3 folders for the report date exist."""
        if not self.config.iso_date:
            self.logger.error("Cannot create S3 folders: iso_date not set.")
            return False
        self.logger.info(f'Ensuring S3 folders exist for date: {self.config.iso_date}')
        base_folder = f'{self.config.iso_date}/'
        ads_folder = f'{self.config.iso_date}/ads/'
        try:
            created_base = await self.s3_manager.create_folder(base_folder)
            created_ads = await self.s3_manager.create_folder(ads_folder)
            if created_base is not None and created_ads is not None:
                self.logger.info(f"Confirmed S3 folders exist: {base_folder}, {ads_folder}")
                return True
            else:
                self.logger.warning(f"Failed to create/confirm S3 folders. Base: {created_base}, Ads: {created_ads}")
                return False
        except Exception as e:
            self.logger.error(f"Exception creating S3 folders for {self.config.iso_date}: {e}", exc_info=True)
            return False

    async def _upload_ad_task(self, ad_html: str, ad_archive_id: str, retry_count: int = 3) -> Tuple[str, bool, Optional[str]]:
        """Task for uploading a single ad HTML to S3 with retry logic.
        
        Returns:
            Tuple of (ad_archive_id, success, error_message)
        """
        if not self.config.iso_date: 
            return ad_archive_id, False, "Missing iso_date in config"
            
        s3_path = f"{self.config.iso_date}/ads/{ad_archive_id}.html"
        last_error = None
        
        for attempt in range(retry_count):
            try:
                # Add exponential backoff for retries
                if attempt > 0:
                    await asyncio.sleep(2 ** attempt)
                    self.logger.info(f"Retry attempt {attempt + 1}/{retry_count} for ad {ad_archive_id}")
                
                # Use the async method directly
                success = await self.s3_manager.upload_content(
                    content=ad_html,
                    object_key=s3_path,
                    content_type='text/html',
                    overwrite=True
                )
                
                if success:
                    if attempt > 0:
                        self.logger.info(f"Successfully uploaded ad {ad_archive_id} on attempt {attempt + 1}")
                    return ad_archive_id, True, None
                else:
                    last_error = "S3 upload returned False"
                    self.logger.warning(f"S3 upload task failed for ad {ad_archive_id} to {s3_path} (attempt {attempt + 1}/{retry_count})")
                    
            except asyncio.TimeoutError as timeout_err:
                last_error = f"Timeout error: {str(timeout_err)}"
                self.logger.error(f"Timeout during S3 upload for ad {ad_archive_id} (attempt {attempt + 1}/{retry_count}): {timeout_err}")
            except Exception as upload_err:
                last_error = f"{type(upload_err).__name__}: {str(upload_err)}"
                self.logger.error(f"Exception during S3 upload task for ad {ad_archive_id} to {s3_path} (attempt {attempt + 1}/{retry_count}): {upload_err}", exc_info=True)
        
        # All retries failed
        self.logger.error(f"Failed to upload ad {ad_archive_id} after {retry_count} attempts. Last error: {last_error}")
        return ad_archive_id, False, last_error

    async def generate_and_upload_ad_pages(self, ad_df: pd.DataFrame):
        """Generates and uploads individual ad HTML pages."""
        if ad_df is None or ad_df.empty:
            self.logger.warning("Cannot generate ad pages: Ad DataFrame is empty.")
            return
        if not self.config.iso_date:
             self.logger.error("Cannot generate ad pages: iso_date is not set.")
             return
        if not self.s3_manager:
            self.logger.error("Cannot generate ad pages: S3 manager is None.")
            return
        if not self.s3_manager._client:
            self.logger.error("Cannot generate ad pages: S3 client not initialized. Call await s3_manager._initialize() first.")
            return
        self.logger.info("S3 manager and client confirmed ready for ad page generation.")

        self.logger.info(f'Generating {len(ad_df)} ad HTML pages.')
        if not await self._create_s3_folders():
            self.logger.error("Failed to create necessary S3 folders. Aborting ad page generation.")
            return

        try:
            template = self.renderer.env.get_template('view_ad_v2.html')
        except Exception as e:
            self.logger.error(f"Failed to load ad template 'view_ad_v2.html': {e}", exc_info=True)
            return

        # Prepare data and generate HTML content
        ad_htmls_to_upload: List[Tuple[str, str]] = []
        required_cols = ['ad_archive_id', 'ad_creative_id', 'page_id', 'law_firm', # ... add all from original
                         'body', 'link_url', 'publisher_platform']
        missing_cols = [col for col in required_cols if col not in ad_df.columns]
        if missing_cols:
            self.logger.error(f"Cannot generate ad pages: Missing columns in ad_df: {missing_cols}")
            return

        for index, row in ad_df.iterrows():
            try:
                row_dict = row.to_dict() # Work with dict for easier .get() access
                ad_archive_id = self._check_and_format(row_dict.get('ad_archive_id'))
                ad_creative_id = self._check_and_format(row_dict.get('ad_creative_id'))
                if not ad_archive_id or not ad_creative_id:
                    self.logger.warning(f"Skipping ad row {index}: Missing ID.")
                    continue

                # Image URI Logic (original - check S3 existence)
                ad_img_s3_key = f"adarchive/fb/{ad_archive_id}/{ad_creative_id}.jpg"
                if await self.s3_manager.file_exists(ad_img_s3_key):
                    image_uri = f"{self.config.s3_prod_url}/{ad_img_s3_key}"
                else:
                    fallback_s3_key = "assets/images/images/ad_unavailable.jpg" # Check path
                    image_uri = f"{self.config.s3_prod_url}/{fallback_s3_key}"
                    self.logger.debug(f"Ad image {ad_img_s3_key} not found. Using fallback.")

                # Logo URI Logic (original)
                page_id = self._check_and_format(row_dict.get('page_id'))
                logo_s3_key = f"assets/images/law-firm-logos/{page_id}.jpg" if page_id else "assets/images/law-firm-logos/default.jpg" # Check paths
                logo_uri = f"{self.config.s3_prod_url}/{logo_s3_key}"

                # Prepare context data for the ad template
                ad_context_data = {
                    'page_name': self._check_and_format(row_dict.get('law_firm', 'Unknown Page')),
                    'logo_uri': logo_uri,
                    'image_uri': image_uri,
                    'caption': self._filter_curly_braces(self._check_and_format(row_dict.get('caption'))).upper(),
                    'title': self._filter_curly_braces(self._check_and_format(row_dict.get('title'))),
                    'link_description': self._filter_curly_braces(self._check_and_format(row_dict.get('link_description'))),
                    'cta_text': self._filter_curly_braces(self._check_and_format(row_dict.get('cta_text'))),
                    'is_active': "Active" if row_dict.get("is_active", False) else "Inactive",
                    'start_date': self._check_and_format(row_dict.get('start_date', '')),
                    'end_date': self._check_and_format(row_dict.get('end_date', '')),
                    'body': self._format_text_ad(self._filter_curly_braces(self._check_and_format(row_dict.get('body')))),
                    'link_url': self._filter_curly_braces(self._check_and_format(row_dict.get('link_url'))),
                    'publisher_platform': self._safe_process_platform(row_dict.get('publisher_platform')),
                    'landing_page': (self._check_and_format(row_dict.get('link_url')) or '').split('?')[0],
                    'ad_archive_id': ad_archive_id
                }
                ad_html = template.render(data=ad_context_data)
                ad_htmls_to_upload.append((ad_html, ad_archive_id))

            except Exception as e:
                current_ad_id = row.get('ad_archive_id', 'N/A')
                self.logger.error(f"Error processing ad row {index} (ID: {current_ad_id}): {e}", exc_info=True)
                continue

        self.logger.info(f'Finished generating {len(ad_htmls_to_upload)} ad HTML contents.')

        # Upload in parallel
        if ad_htmls_to_upload:
            num_ads = len(ad_htmls_to_upload)
            max_workers = min(self.config.s3_upload_workers, num_ads) if num_ads > 0 else 1
            self.logger.info(f'Uploading {num_ads} ad HTMLs to S3 using up to {max_workers} workers.')
            success_count, fail_count = 0, 0
            
            # Create tasks for all uploads
            tasks = []
            for html, ad_id in ad_htmls_to_upload:
                tasks.append(self._upload_ad_task(html, ad_id))
            
            # Execute all tasks concurrently
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Process results and track failures
            failed_ads = []
            for result in results:
                if isinstance(result, Exception):
                    self.logger.error(f'Error in upload task: {result}')
                    fail_count += 1
                else:
                    ad_id, success, error_msg = result
                    if success: 
                        success_count += 1
                    else: 
                        fail_count += 1
                        failed_ads.append({
                            'ad_id': ad_id,
                            'error': error_msg or 'Unknown error'
                        })
            
            self.logger.info(f'Finished uploading ad HTMLs. Successful: {success_count}, Failed: {fail_count}.')
            
            # Log details of failed uploads
            if failed_ads:
                self.logger.error(f"Failed to upload {len(failed_ads)} ad pages:")
                for failed in failed_ads[:10]:  # Log first 10 failures
                    self.logger.error(f"  - Ad {failed['ad_id']}: {failed['error']}")
                if len(failed_ads) > 10:
                    self.logger.error(f"  ... and {len(failed_ads) - 10} more failures")
                
                # Save failed ad IDs to a file for recovery
                self._save_failed_uploads(failed_ads)
        else:
            self.logger.warning("No ad HTML pages were generated or prepared for upload.")

    async def generate_ad_pages_only(self, ad_df: pd.DataFrame):
        """Generates individual ad HTML pages locally without uploading to S3."""
        if ad_df is None or ad_df.empty:
            self.logger.warning("Cannot generate ad pages: Ad DataFrame is empty.")
            return
        if not self.config.iso_date:
             self.logger.error("Cannot generate ad pages: iso_date is not set.")
             return

        self.logger.info(f'Generating {len(ad_df)} ad HTML pages locally (no upload).')

        try:
            template = self.renderer.env.get_template('view_ad_v2.html')
        except Exception as e:
            self.logger.error(f"Failed to load ad template 'view_ad_v2.html': {e}", exc_info=True)
            return

        # Create local directory for ad pages
        local_ads_dir = os.path.join(self.config.download_dir, 'ads')
        os.makedirs(local_ads_dir, exist_ok=True)

        # Prepare data and generate HTML content
        generated_count = 0
        required_cols = ['ad_archive_id', 'ad_creative_id', 'page_id', 'law_firm',
                         'body', 'link_url', 'publisher_platform']
        missing_cols = [col for col in required_cols if col not in ad_df.columns]
        if missing_cols:
            self.logger.error(f"Cannot generate ad pages: Missing columns in ad_df: {missing_cols}")
            return

        for index, row in ad_df.iterrows():
            try:
                row_dict = row.to_dict()
                ad_archive_id = self._check_and_format(row_dict.get('ad_archive_id'))
                ad_creative_id = self._check_and_format(row_dict.get('ad_creative_id'))
                if not ad_archive_id or not ad_creative_id:
                    self.logger.warning(f"Skipping ad row {index}: Missing ID.")
                    continue

                # Use fallback images for local generation
                image_uri = f"{self.config.s3_prod_url}/assets/images/images/ad_unavailable.jpg"
                
                page_id = self._check_and_format(row_dict.get('page_id'))
                logo_s3_key = f"assets/images/law-firm-logos/{page_id}.jpg" if page_id else "assets/images/law-firm-logos/default.jpg"
                logo_uri = f"{self.config.s3_prod_url}/{logo_s3_key}"

                # Prepare context data for the ad template
                ad_context_data = {
                    'page_name': self._check_and_format(row_dict.get('law_firm', 'Unknown Page')),
                    'logo_uri': logo_uri,
                    'image_uri': image_uri,
                    'caption': self._filter_curly_braces(self._check_and_format(row_dict.get('caption'))).upper(),
                    'title': self._filter_curly_braces(self._check_and_format(row_dict.get('title'))),
                    'link_description': self._filter_curly_braces(self._check_and_format(row_dict.get('link_description'))),
                    'cta_text': self._filter_curly_braces(self._check_and_format(row_dict.get('cta_text'))),
                    'is_active': "Active" if row_dict.get("is_active", False) else "Inactive",
                    'start_date': self._check_and_format(row_dict.get('start_date', '')),
                    'end_date': self._check_and_format(row_dict.get('end_date', '')),
                    'body': self._format_text_ad(self._filter_curly_braces(self._check_and_format(row_dict.get('body')))),
                    'link_url': self._filter_curly_braces(self._check_and_format(row_dict.get('link_url'))),
                    'publisher_platform': self._safe_process_platform(row_dict.get('publisher_platform')),
                    'landing_page': (self._check_and_format(row_dict.get('link_url')) or '').split('?')[0],
                    'ad_archive_id': ad_archive_id
                }
                
                ad_html = template.render(data=ad_context_data)
                
                # Save to local file
                local_file_path = os.path.join(local_ads_dir, f"{ad_archive_id}.html")
                with open(local_file_path, 'w', encoding='utf-8') as f:
                    f.write(ad_html)
                
                generated_count += 1

            except Exception as e:
                current_ad_id = row.get('ad_archive_id', 'N/A')
                self.logger.error(f"Error processing ad row {index} (ID: {current_ad_id}): {e}", exc_info=True)
                continue

        self.logger.info(f'Generated {generated_count} ad HTML pages locally in {local_ads_dir}.')
    
    def _save_failed_uploads(self, failed_ads: List[Dict[str, str]]) -> None:
        """Save failed ad uploads to a JSON file for recovery/debugging."""
        if not self.config.iso_date or not failed_ads:
            return
            
        try:
            # Create directory if it doesn't exist
            failed_dir = os.path.join(self.config.download_dir, 'failed_ad_uploads')
            os.makedirs(failed_dir, exist_ok=True)
            
            # Create filename with timestamp
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"failed_ads_{self.config.iso_date}_{timestamp}.json"
            filepath = os.path.join(failed_dir, filename)
            
            # Save failed ad info
            failed_data = {
                'report_date': self.config.iso_date,
                'timestamp': timestamp,
                'total_failures': len(failed_ads),
                'failed_ads': failed_ads
            }
            
            with open(filepath, 'w') as f:
                json.dump(failed_data, f, indent=2)
                
            self.logger.info(f"Saved {len(failed_ads)} failed ad uploads to: {filepath}")
        except Exception as e:
            self.logger.error(f"Failed to save failed uploads list: {e}", exc_info=True)
    
    async def validate_ad_pages(self, ad_ids: List[str]) -> Dict[str, bool]:
        """Validate that ad HTML pages exist on S3.
        
        Args:
            ad_ids: List of ad archive IDs to check
            
        Returns:
            Dict mapping ad_id to exists status
        """
        if not self.config.iso_date or not ad_ids:
            return {}
            
        validation_results = {}
        
        # Check in batches to avoid overwhelming S3
        batch_size = 50
        for i in range(0, len(ad_ids), batch_size):
            batch = ad_ids[i:i + batch_size]
            
            # Create tasks for checking existence
            tasks = []
            for ad_id in batch:
                s3_path = f"{self.config.iso_date}/ads/{ad_id}.html"
                tasks.append(self._check_ad_exists(ad_id, s3_path))
            
            # Execute batch concurrently
            results = await asyncio.gather(*tasks)
            
            # Store results
            for ad_id, exists in results:
                validation_results[ad_id] = exists
        
        # Log summary
        total = len(ad_ids)
        existing = sum(1 for exists in validation_results.values() if exists)
        missing = total - existing
        
        self.logger.info(f"Ad page validation: {existing}/{total} exist, {missing} missing")
        
        if missing > 0:
            missing_ids = [ad_id for ad_id, exists in validation_results.items() if not exists]
            self.logger.warning(f"Missing ad pages: {missing_ids[:10]}{'...' if len(missing_ids) > 10 else ''}")
            
        return validation_results
    
    async def _check_ad_exists(self, ad_id: str, s3_path: str) -> Tuple[str, bool]:
        """Check if a single ad page exists on S3."""
        try:
            exists = await self.s3_manager.file_exists(s3_path)
            return ad_id, exists
        except Exception as e:
            self.logger.error(f"Error checking existence of ad {ad_id}: {e}")
            return ad_id, False