import logging
import os
import re
import json
from typing import Dict, Any, Optional, List

import pandas as pd
from jinja2 import Environment, FileSystemLoader, select_autoescape
from css_inline import inline

from .config import ReportConfig
from .data_loader import StaticDataLoader
from .processor import ReportDataProcessor


class ReportRenderer:
    """Handles Jinja setup, template rendering, and email-specific processing."""

    def __init__(self, report_config: ReportConfig, static_data: StaticDataLoader,
                 report_processor: ReportDataProcessor):
        self.config = report_config
        self.static_data = static_data  # Needed for sponsor lookup during render
        self.report_processor = report_processor  # Needed for allegations lookup
        self.logger = logging.getLogger(f"{__name__}.ReportRenderer")
        self.template_path = self._setup_template_path()
        self.env = self._setup_jinja_env()
        # Store loaded data needed by template helpers
        self.litigation_sponsorships = self.static_data.load_litigation_sponsorships()
        # Cache section configuration to avoid reloading
        self._section_config_cache = None
        # self._docket_df_for_summary: pd.DataFrame = pd.DataFrame() # To be set before rendering

    def _setup_template_path(self) -> str:
        # Use the correct path to templates in src/assets/templates
        template_root = os.path.abspath(os.path.join(
            os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'src', 'assets', 'templates'
        ))
        base_template_file = os.path.join(template_root, 'base.html')
        if not os.path.exists(base_template_file):
            raise FileNotFoundError(f"Base template file not found: {base_template_file}")
        self.logger.info(f"Jinja template root path: {template_root}")
        return template_root

    def _setup_jinja_env(self) -> Environment:
        env = Environment(
            loader=FileSystemLoader(self.template_path),
            autoescape=select_autoescape(['html', 'xml'])
        )
        env.filters['clean_id'] = self._clean_id
        env.filters['convert_newlines'] = self._convert_newlines
        # Make helper methods available in templates
        env.globals['get_litigation_sponsor'] = self._get_litigation_sponsor_for_template
        env.globals['get_allegations_and_causes'] = self._get_allegations_and_causes_for_template  # Wrapper
        return env

    @staticmethod
    def _clean_id(value: Any) -> str:
        """Creates a safe HTML ID from a string."""
        s = str(value).strip()
        s = re.sub(r'^\W+|\W+$', '', s)
        s = re.sub(r'\W+', '_', s)
        return s.lower()

    @staticmethod
    def _convert_newlines(value: Any) -> Any:
        """Converts escaped newlines \\n to actual newlines \n."""
        return value.replace('\\n', '\n') if isinstance(value, str) else value

    def _get_litigation_sponsor_for_template(self, mdl_num_str: str, is_web_context: bool) -> Optional[Dict[str, Any]]:
        """Helper function accessible in Jinja to safely get sponsor data."""
        if not mdl_num_str or not isinstance(mdl_num_str, str): return None
        cleaned_mdl_num = mdl_num_str.strip().removesuffix('.0')
        sponsor_data = self.litigation_sponsorships.get(cleaned_mdl_num)

        if not sponsor_data or not sponsor_data.get("display", False):
            # self.logger.debug(f"Sponsor for MDL {cleaned_mdl_num} not found or display is false.")
            return None

        ad_copy_key = 'web_ad_copy' if is_web_context else 'email_ad_copy'
        fallback_copy_key = 'email_ad_copy' if is_web_context else 'web_ad_copy'
        ad_copy = sponsor_data.get(ad_copy_key) or sponsor_data.get(fallback_copy_key)

        return {
            "logo_svg_link": sponsor_data.get("logo_svg_link"),
            "logo_background_color": sponsor_data.get("logo_background_color"),  # Check if used
            "background_color": sponsor_data.get("background_color"),
            "ad_copy": ad_copy or "",
            "cta_link": sponsor_data.get("cta_link")
        }

    def _get_allegations_and_causes_for_template(self, docket_df_from_context: pd.DataFrame, title: str,
                                                 mdl_num: Any) -> str:
        """ Wrapper for Jinja to call the processor's method. Needs the docket_df passed explicitly. """
        if docket_df_from_context.empty:
            self.logger.warning(f"Cannot get allegations for '{title}': docket_df_from_context is empty.")
            return "Summary unavailable (data missing)."
        # Call the processor's method, passing the explicitly provided docket_df
        return self.report_processor.get_allegations_and_causes(title, mdl_num, docket_df_from_context)

    def _load_section_config(self) -> List[Dict[str, Any]]:
        """ Loads the section configuration from the config file with caching. """
        # Return cached config if available
        if self._section_config_cache is not None:
            return self._section_config_cache
            
        # Construct path to section config file
        config_path = os.path.abspath(os.path.join(
            os.path.dirname(os.path.dirname(__file__)), 
            'config', 'reports', 'section_config.json'
        ))
        
        try:
            with open(config_path, 'r') as f:
                config_data = json.load(f)
                self.logger.info(f"Loaded section configuration from {config_path}")
                self._section_config_cache = config_data.get('sections', [])
                return self._section_config_cache
        except FileNotFoundError:
            self.logger.error(f"Section config file not found at {config_path}")
            raise
        except json.JSONDecodeError as e:
            self.logger.error(f"Invalid JSON in section config file: {e}")
            raise
        except Exception as e:
            self.logger.error(f"Error loading section config: {e}")
            raise

    def reload_section_config(self) -> None:
        """ Forces a reload of the section configuration by clearing the cache. """
        self._section_config_cache = None
        self.logger.info("Section configuration cache cleared, will reload on next access")

    def _prepare_context(self, report_data: Dict[str, Any], is_web: bool, is_weekly: bool) -> Dict[str, Any]:
        """Builds the context dictionary for template rendering."""
        self.logger.info(
            f"Preparing context for {'weekly' if is_weekly else 'daily'} {'web' if is_web else 'email'} report.")
        self.logger.debug(f"--- START _prepare_context ---")  # ADDED LOG
        self.logger.debug(f"Incoming report_data keys: {list(report_data.keys())}")  # ADDED LOG

        # Get the correct docket_df for this specific report (daily or weekly)
        docket_df = report_data.get('docket_df', pd.DataFrame())
        if docket_df.empty:
            self.logger.warning("Docket DF is empty in report_data during context preparation.")

        report_title_date = self.config.get_formatted_report_date(for_summary=False)
        if is_weekly:
            summary_date_formatted = self.config.get_formatted_weekly_summary_date()
        else:
            summary_date_formatted = self.config.get_formatted_report_date(for_summary=True)

        # Calculate total adjusted torts (original logic)
        num_torts_adjusted = 0
        if not docket_df.empty and 'mdl_num' in docket_df.columns and 'num_plaintiffs' in docket_df.columns:
            is_target_mdl = (docket_df['mdl_num'].isin(['2873', '3092']) & (docket_df['num_plaintiffs'] > 0))
            target_mdl_plaintiff_count = docket_df.loc[is_target_mdl, 'num_plaintiffs'].sum()
            other_filing_count = (~is_target_mdl).sum()
            num_torts_adjusted = int(target_mdl_plaintiff_count + other_filing_count)
        else:
            self.logger.warning("Could not calculate adjusted tort count, docket_df missing or incomplete.")

        # Calculate final ad count (original logic)
        num_ads_final = 0
        grouped_ads_filtered = report_data.get('grouped_ads', {})
        if grouped_ads_filtered and isinstance(grouped_ads_filtered, dict):  # Check it's the expected dict
            num_ads_final = sum(len(df) for df in grouped_ads_filtered.values() if isinstance(df, pd.DataFrame))
        elif grouped_ads_filtered:
            self.logger.warning(
                f"Unexpected type for grouped_ads: {type(grouped_ads_filtered)}. Cannot calculate ad count.")

        # --- Ensure AFFF adjustment IS NOT happening here ---
        # (Code block previously here was removed as per prior instructions)

        # Get the potentially correct labels/data directly from report_data
        # *** ADDED DETAILED LOGGING HERE ***
        retrieved_chart_labels = report_data.get('chart_labels')
        retrieved_chart_data = report_data.get('chart_data')
        self.logger.debug(
            f"Retrieved 'chart_labels' from report_data: Type={type(retrieved_chart_labels)}, Value={retrieved_chart_labels}")
        self.logger.debug(
            f"Retrieved 'chart_data' from report_data: Type={type(retrieved_chart_data)}, Value={retrieved_chart_data}")

        # Use default empty lists if retrieval failed or returned None
        chart_labels = retrieved_chart_labels if retrieved_chart_labels is not None else []
        chart_data = retrieved_chart_data if retrieved_chart_data is not None else []
        self.logger.debug(f"Final 'chart_labels' being used for context: {chart_labels}")
        self.logger.debug(f"Final 'chart_data' being used for context: {chart_data}")
        # *** END ADDED LOGGING ***

        # Filter sections based on flags
        all_sections = self._load_section_config()
        active_sections = [
            sec for sec in all_sections
            if getattr(self.config, sec['show_flag_attr'], False)
        ]
        self.logger.info(f"Active sections for rendering: {[s['id'] for s in active_sections]}")

        # Log data being passed to templates BEFORE creating context
        grouped_litigation = report_data.get('grouped_litigation', {})
        detailed_filings = report_data.get('detailed_filings', {})
        title_totals = report_data.get('title_totals', pd.DataFrame())
        afff_stats = report_data.get('afff_stats')
        
        self.logger.warning(f"🔥 RENDERER: grouped_litigation keys: {list(grouped_litigation.keys())}")
        self.logger.warning(f"🔥 RENDERER: detailed_filings keys: {list(detailed_filings.keys())}")
        self.logger.warning(f"🔥 RENDERER: title_totals shape: {title_totals.shape if hasattr(title_totals, 'shape') else 'Not DataFrame'}")
        self.logger.warning(f"🔥 RENDERER: afff_stats type: {type(afff_stats)}")
        
        if grouped_litigation:
            sample_key = list(grouped_litigation.keys())[0]
            self.logger.warning(f"🔥 RENDERER: Sample grouped_litigation '{sample_key}': {grouped_litigation[sample_key]}")
        if detailed_filings:
            sample_firm = list(detailed_filings.keys())[0]
            self.logger.warning(f"🔥 RENDERER: Sample detailed_filings '{sample_firm}': {list(detailed_filings[sample_firm].keys())}")

        context = {
            # Base Info
            'report_date': report_title_date,
            'summary_date': summary_date_formatted,
            'is_web': is_web,
            'is_weekly_report': is_weekly,
            'iso_date': self.config.iso_date,
            's3_prod': self.config.s3_prod_url,
            'config_obj': self.config,

            # Calculated Summaries
            'num_torts': num_torts_adjusted,
            'num_ads': num_ads_final,

            # --- Pass Data Directly ---
            # Use the final chart labels and data variables confirmed above
            'chart_labels': chart_labels,
            'chart_data': chart_data,
            
            # Main report data
            'grouped': grouped_litigation,
            'detailed_filings_grouped': detailed_filings,
            'grouped_ads': report_data.get('grouped_ads', {}),
            'title_totals': title_totals,
            'afff_stats': afff_stats,

            # Static/Optional Data
            'upcoming_hearings': report_data.get('upcoming_hearings', []),
            'special_reports': report_data.get('special_reports', []),
            'news_items': report_data.get('news_items', []),
            'announcements': report_data.get('announcements', []),
            'calendly_link': self.config.calendly_link,

            # Sponsorship Data
            'sponsorships': report_data.get('general_sponsorships', {}),
            'litigation_sponsorships': self.litigation_sponsorships,

            # --- Pass Necessary Objects/Data for Helpers ---
            'docket_df': docket_df,
            'report_processor': self.report_processor,

            # Control/Rendering Helpers
            'active_sections': active_sections,
            'context': None  # Will be set below
        }
        context['context'] = context

        self.logger.debug(f"--- Context Prepared ---")  # ADDED LOG
        self.logger.debug(f"Context Check - Chart Labels key: {context.get('chart_labels')}")  # ADDED LOG
        self.logger.debug(f"Context Check - Chart Data key: {context.get('chart_data')}")  # ADDED LOG
        self.logger.debug(f"--- END _prepare_context ---")  # ADDED LOG

        return context

    def render_report(self, report_data: Dict[str, Any], is_web: bool, is_weekly: bool) -> str:
        """Renders the report HTML using the base template."""
        context = self._prepare_context(report_data, is_web, is_weekly)
        self.logger.critical(
            f"VERIFY ACTIVE SECTIONS (EMAIL): IDs = {[s['id'] for s in context['active_sections']] if 'active_sections' in context else 'Not Found'}")
        template_name = 'base.html'
        try:
            template = self.env.get_template(template_name)
            self.logger.info(f"Rendering template '{template_name}'...")
            html_output = template.render(context)
            self.logger.info(f"Successfully rendered template '{template_name}'.")
            return html_output
        except Exception as e:
            self.logger.error(f"Error loading or rendering template '{template_name}': {e}", exc_info=True)
            # Provide a basic error HTML for easier debugging
            import traceback
            error_html = f"<html><body><h1>Error Generating Report</h1>"
            error_html += f"<p>Template: {template_name}</p>"
            error_html += f"<p>Error: {e}</p>"
            error_html += f"<h2>Context Keys:</h2><pre>{list(context.keys())}</pre>"
            error_html += f"<h2>Traceback:</h2><pre>{traceback.format_exc()}</pre>"
            error_html += "</body></html>"
            return error_html

    def _preprocess_html_for_email(self, html_content: str) -> str:
        """Applies pre-processing steps for email client compatibility."""
        if not html_content: return html_content
        self.logger.info("Pre-processing HTML for email compatibility")

        # --- CSS var() replacements and fixes (original logic) ---
        css_replacements = [
            (r'var\(--fa-style-family, "Font Awesome 6 Free"\)', '"Font Awesome 6 Free"'),
            (r'var\(--fa-style, 900\)', '900'),
            (r'var\(--fa-display, inline-block\)', 'inline-block'),
            # Add all other replacements from the original code here...
            (r'padding:var\(--fa-border-padding,.2em .25em .15em\)', 'padding: 0.2em 0.25em 0.15em'),
            (r'linear-gradient\(135deg, #115197 0%, #1E88E5 100%\)', '#115197'),
            (r'}\s+/\* Rule for state toggled by JS click \*/', '}/* Rule for state toggled by JS click */'),
        ]
        processed_html = html_content
        for pattern, replacement in css_replacements:
            processed_html = re.sub(pattern, replacement, processed_html, flags=re.IGNORECASE)  # Added flags

        # --- Add email-specific CSS and FontAwesome fixes (original logic) ---
        email_specific_css = """
        <style type="text/css">
            /* Email-specific overrides */
            body, table, td, a { font-family: Arial, sans-serif; }
            .gradient-header, .email-header { background-color: #115197 !important; background-image: none !important; }
            /* FontAwesome compatibility fixes */
            .fas, .fa, .far, .fal, .fab { display: inline-block; font-style: normal; font-variant: normal; text-rendering: auto; line-height: 1; font-family: "Font Awesome 6 Free"; font-weight: 900; /* Added weight */ }
            /* Add specific icon content rules: .fa-chevron-right:before { content: "\\f054"; } etc. */
        </style>
        """
        # Insert before </head>
        processed_html = re.sub(r'(</head>)', f'{email_specific_css}\\1', processed_html, flags=re.IGNORECASE)

        self.logger.info("HTML pre-processing for email complete.")
        return processed_html

    def render_email_report(self, report_data: Dict[str, Any], is_weekly: bool) -> str:
        """
        Renders the report, removes HTML comments, and applies email-specific
        CSS inlining using the css-inline library.
        """
        self.logger.info(f"Rendering and processing {'weekly' if is_weekly else 'daily'} email report.")

        # 1. Render base HTML
        html_output = self.render_report(report_data, is_web=False, is_weekly=is_weekly)
        if not html_output or "Error Generating Report" in html_output:
             self.logger.error("Initial rendering failed or produced error HTML. Skipping further processing.")
             return html_output # Return error html directly

        # 2. Pre-process for email client issues (like FontAwesome, CSS vars)
        # Assumes _preprocess_html_for_email method exists and is unchanged
        processed_html = self._preprocess_html_for_email(html_output)

        # 3. Remove HTML comments
        self.logger.info("Removing HTML comments...")
        # Use re.DOTALL to match comments spanning multiple lines
        html_without_comments = re.sub(r'<!--.*?-->', '', processed_html, flags=re.DOTALL)
        self.logger.info("HTML comments removed.")

        # 4. Inline CSS using css-inline library
        try:
            self.logger.info("Inlining CSS using css-inline library...")
            # keep_style_tags=True preserves style tags, which can be a fallback for some email clients
            # similar to the original Premailer's primary attempt.
            inlined_html = inline(html_without_comments, keep_style_tags=False)
            self.logger.info("CSS inlining complete using css-inline.")
            return inlined_html
        except Exception as e:
            self.logger.error(f"CSS inlining using css-inline failed: {e}", exc_info=True)
            # Return the pre-processed, comment-removed HTML as the best effort fallback
            self.logger.warning("Returning HTML without CSS inlining due to error.")
            return html_without_comments
