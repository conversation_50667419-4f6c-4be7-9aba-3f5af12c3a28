#!/usr/bin/env python3
"""
Recent Ads Processor <PERSON>le

Encapsulates the logic for loading recent ads from DynamoDB and categorizing them
using the rule-based categorizer. Can be imported and used in other scripts.
"""

import pandas as pd
import boto3
import json
import re
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Optional, Union
from boto3.dynamodb.conditions import Attr

from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TimeElapsedColumn, MofNCompleteColumn

console = Console()


class SimplifiedRuleCategorizer:
    """Simplified rule categorizer without external dependencies"""

    def __init__(self, config_dir: Optional[Union[str, Path]] = None):
        self.campaigns = []
        self.skip_terms = []
        self.config_dir = None
        self._load_configurations(config_dir)

    def _find_config_dir(self, config_dir: Optional[Union[str, Path]] = None) -> Path:
        """Find the configuration directory"""
        if config_dir:
            config_path = Path(config_dir)
            if config_path.exists():
                return config_path

        # Search in standard locations
        search_paths = [
            Path.cwd() / "config",
            Path(__file__).parent.parent.parent.parent / "config",
            Path.cwd() / "src" / "config" / "fb_ad_categorizer",
        ]

        for path in search_paths:
            if path.exists() and (path / "campaign_config.json").exists():
                return path

        raise FileNotFoundError("Could not find configuration directory with campaign_config.json")

    def _load_configurations(self, config_dir: Optional[Union[str, Path]] = None):
        """Load campaign rules and skip terms from configuration files"""
        try:
            self.config_dir = self._find_config_dir(config_dir)

            # Load campaign configuration
            campaign_config_file = self.config_dir / "campaign_config.json"
            with open(campaign_config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)

            # Handle both formats: direct array or dict with 'campaigns' key
            if isinstance(config_data, list):
                self.campaigns = config_data
            else:
                self.campaigns = config_data.get('campaigns', [])
            console.print(f"[green]✓[/green] Loaded {len(self.campaigns)} campaign rules")

            # Load skip terms
            skip_terms_file = self.config_dir / "campaign_skip_terms.json"
            if skip_terms_file.exists():
                with open(skip_terms_file, 'r', encoding='utf-8') as f:
                    skip_data = json.load(f)

                # Handle both formats: direct array or dict with 'skip_terms' key
                if isinstance(skip_data, list):
                    self.skip_terms = skip_data
                else:
                    self.skip_terms = skip_data.get('skip_terms', [])
                console.print(f"[green]✓[/green] Loaded {len(self.skip_terms)} skip terms")
            else:
                console.print(f"[yellow]Warning: Skip terms file not found[/yellow]")
                self.skip_terms = []

        except Exception as e:
            console.print(f"[red]Error loading configurations: {e}[/red]")
            raise

    def extract_text_fields(self, fb_ad: Dict) -> str:
        """Extract and combine text fields from Facebook ad"""
        text_parts = []
        fields_to_extract = ['Summary', 'Title', 'Body', 'PageName', 'LinkDescription']

        for field in fields_to_extract:
            value = fb_ad.get(field, '')
            if value and isinstance(value, str):
                text_parts.append(value.strip())

        combined_text = ' '.join(text_parts)
        combined_text = re.sub(r'\{\{[^}]+\}\}', '', combined_text)
        combined_text = re.sub(r'\[\[[^\]]+\]\]', '', combined_text)
        combined_text = re.sub(r'<[^>]+>', '', combined_text)
        combined_text = re.sub(r'\s+', ' ', combined_text).strip()

        return combined_text

    def normalize_text(self, text: str) -> str:
        """Normalize text for consistent word boundary matching"""
        if not isinstance(text, str):
            return ""
        text = text.lower()
        text = re.sub(r'[^\w\s]', ' ', text)
        text = re.sub(r'\s+', ' ', text).strip()
        return text

    def word_match(self, term: str, text: str) -> bool:
        """Check if term matches as a whole word/phrase in text"""
        if not term or not text:
            return False
        pattern = r'\b' + re.escape(term) + r'\b'
        return bool(re.search(pattern, text))

    def matches_campaign(self, combined_text: str, campaign: Dict) -> bool:
        """Check if ad text matches a campaign's rules"""
        normalized_text = self.normalize_text(combined_text)

        # Check exclude terms first
        excludes = [self.normalize_text(term) for term in campaign.get('exclude', [])]
        if any(self.word_match(exclude, normalized_text) for exclude in excludes if exclude):
            return False

        # Check triggers
        triggers = [self.normalize_text(term) for term in campaign.get('triggers', [])]
        trigger_match = any(self.word_match(trigger, normalized_text) for trigger in triggers if trigger)

        if not trigger_match:
            return False

        # Check include terms
        includes = [self.normalize_text(term) for term in campaign.get('include', [])]
        if includes:
            return any(self.word_match(include, normalized_text) for include in includes if include)

        return True

    def is_skip_term(self, combined_text: str) -> bool:
        """Check if ad contains skip terms"""
        if not self.skip_terms:
            return False

        normalized_text = self.normalize_text(combined_text)

        for term in self.skip_terms:
            normalized_term = self.normalize_text(term)
            if normalized_term and self.word_match(normalized_term, normalized_text):
                return True

        return False

    def categorize(self, fb_ad: Dict) -> Dict:
        """Categorize a Facebook ad using rules-based classification"""
        categorized_ad = fb_ad.copy()
        combined_text = self.extract_text_fields(fb_ad)

        if not combined_text or not combined_text.strip():
            categorized_ad['category'] = 'Other'
            return categorized_ad

        # Try to match against campaign rules FIRST
        for campaign in self.campaigns:
            if self.matches_campaign(combined_text, campaign):
                category = campaign.get('LitigationName', 'Unknown')
                categorized_ad['category'] = category
                return categorized_ad

        # If no campaign matches, check skip terms
        if self.is_skip_term(combined_text):
            categorized_ad['category'] = 'General'
            return categorized_ad

        # No campaign rules AND no skip terms matched
        categorized_ad['category'] = 'Other'
        return categorized_ad

    def __call__(self, fb_ad: Dict) -> Dict:
        return self.categorize(fb_ad)


class RecentAdsProcessor:
    """Encapsulated logic for processing recent Facebook ads"""

    def __init__(self, start_date: str = "20250401"):
        self.start_date = start_date
        self.categorizer = None
        self.dynamodb = None
        self.table = None

        self._initialize_categorizer()
        self._initialize_database()

    def _initialize_categorizer(self):
        """Initialize the RuleCategorizer"""
        console.print("[cyan]Initializing RuleCategorizer...[/cyan]")
        try:
            self.categorizer = SimplifiedRuleCategorizer()
        except Exception as e:
            console.print(f"[red]Error initializing categorizer: {e}[/red]")
            raise

    def _initialize_database(self):
        """Initialize DynamoDB connection"""
        console.print("[cyan]Initializing database connection...[/cyan]")
        try:
            # Try local DynamoDB first
            self.dynamodb = boto3.resource(
                'dynamodb',
                endpoint_url='http://localhost:8000',
                region_name=(
                    os.environ.get('AWS_REGION') or 
                    os.environ.get('LEXGENIUS_AWS_REGION') or
                    os.environ.get('REGION_NAME') or
                    'us-west-2'
                ),
                aws_access_key_id='dummy',
                aws_secret_access_key='dummy'
            )
            self.table = self.dynamodb.Table('FBAdArchive')

            # Test connection
            self.table.table_status
            console.print("[green]✓[/green] Connected to local DynamoDB")

        except Exception as e:
            console.print(f"[yellow]Local DynamoDB not available, trying AWS: {e}[/yellow]")
            try:
                # Fallback to AWS DynamoDB
                # Get region from environment or use default
                region = (
                    os.environ.get('AWS_REGION') or 
                    os.environ.get('LEXGENIUS_AWS_REGION') or
                    os.environ.get('REGION_NAME') or
                    'us-west-2'
                )
                self.dynamodb = boto3.resource('dynamodb', region_name=region)
                self.table = self.dynamodb.Table('FBAdArchive')
                console.print("[green]✓[/green] Connected to AWS DynamoDB")
            except Exception as aws_e:
                console.print(f"[red]Error connecting to DynamoDB: {aws_e}[/red]")
                raise

    def load_recent_ads(self) -> List[Dict]:
        """Load all ads from the specified start date to present"""
        console.print(f"\n[cyan]Loading ads from {self.start_date} to present...[/cyan]")

        all_ads = []

        try:
            start_date_obj = datetime.strptime(self.start_date, "%Y%m%d").date()

            with Progress(
                    SpinnerColumn(),
                    TextColumn("[progress.description]{task.description}"),
                    BarColumn(),
                    MofNCompleteColumn(),
                    TimeElapsedColumn(),
                    console=console
            ) as progress:
                task = progress.add_task("Scanning DynamoDB for recent ads", total=None)

                last_evaluated_key = None
                processed_count = 0

                while True:
                    scan_kwargs = {
                        'Limit': 1000,
                        'FilterExpression': Attr('StartDate').gte(self.start_date)
                    }

                    if last_evaluated_key:
                        scan_kwargs['ExclusiveStartKey'] = last_evaluated_key

                    response = self.table.scan(**scan_kwargs)
                    items = response.get('Items', [])

                    # Add all items since they already match the date filter
                    all_ads.extend(items)

                    processed_count += len(items)
                    progress.update(task, advance=len(items), total=processed_count)

                    last_evaluated_key = response.get('LastEvaluatedKey')
                    if not last_evaluated_key:
                        break

            console.print(f"[green]✓[/green] Found {len(all_ads):,} ads from {self.start_date} onwards")

            # Debug: Show some sample dates if we found no ads
            if len(all_ads) == 0:
                console.print(
                    f"[yellow]Debug: Scanned {processed_count} total ads but none matched date filter >= {self.start_date}[/yellow]")
                self._debug_database_dates()

        except Exception as e:
            console.print(f"[red]Error loading ads: {e}[/red]")
            raise

        return all_ads

    def _debug_database_dates(self):
        """Debug helper to examine database date formats"""
        # Sample a few ads to see their date format
        sample_response = self.table.scan(Limit=10)
        sample_items = sample_response.get('Items', [])
        console.print(f"[yellow]Sample of {len(sample_items)} ads found in database:[/yellow]")

        # Check what date fields actually exist
        date_fields = set()
        for item in sample_items:
            for key in item.keys():
                if any(date_word in key.lower() for date_word in ['date', 'time', 'created', 'start', 'end']):
                    date_fields.add(key)

        console.print(f"[yellow]Date-related fields found: {sorted(date_fields)}[/yellow]")

        for item in sample_items[:3]:
            ad_id = item.get('id', item.get('AdArchiveID', 'Unknown'))
            console.print(f"  Ad {ad_id}:")

            # Show all date-related fields
            for field in sorted(date_fields):
                value = item.get(field, 'Not found')
                console.print(f"    {field}: '{value}'")
            console.print("    ---")

    def categorize_ads(self, ads: List[Dict]) -> List[Dict]:
        """Categorize all ads using RuleCategorizer"""
        console.print(f"\n[cyan]Categorizing {len(ads):,} ads...[/cyan]")

        categorized_ads = []

        with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                BarColumn(),
                TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
                TimeElapsedColumn(),
                console=console
        ) as progress:
            task = progress.add_task("Categorizing ads with rules", total=len(ads))

            for ad in ads:
                try:
                    categorized_ad = self.categorizer(ad)
                    categorized_ads.append(categorized_ad)
                except Exception as e:
                    console.print(
                        f"[yellow]Warning: Error categorizing ad {ad.get('AdArchiveID', 'Unknown')}: {e}[/yellow]")
                    ad_copy = ad.copy()
                    ad_copy['category'] = 'Error'
                    categorized_ads.append(ad_copy)

                progress.update(task, advance=1)

        console.print(f"[green]✓[/green] Categorized {len(categorized_ads):,} ads")
        return categorized_ads

    def save_to_csv(self, categorized_ads: List[Dict], output_file: str = None) -> str:
        """Save categorized ads to CSV"""
        if output_file is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_file = f"categorized_ads_{self.start_date}_to_present_{timestamp}.csv"

        console.print(f"\n[cyan]Saving results to CSV: {output_file}[/cyan]")

        csv_data = []
        for ad in categorized_ads:
            csv_row = {
                'AdArchiveID': ad.get('AdArchiveID', ''),
                'Title': ad.get('Title', ''),
                'Body': ad.get('Body', ''),
                'Summary': ad.get('Summary', ''),
                'Category': ad.get('category', 'Uncategorized')
            }
            csv_data.append(csv_row)

        df = pd.DataFrame(csv_data)

        try:
            df.to_csv(output_file, index=False, encoding='utf-8')
            console.print(f"[green]✓[/green] Saved {len(df):,} ads to {output_file}")

            self._show_category_summary(df)
            return output_file

        except Exception as e:
            console.print(f"[red]Error saving CSV: {e}[/red]")
            raise

    def _show_category_summary(self, df: pd.DataFrame):
        """Show summary table of category distribution"""
        from rich.table import Table

        console.print("\n[bold]Category Distribution:[/bold]")

        category_counts = df['Category'].value_counts()
        total_ads = len(df)

        table = Table(show_header=True, header_style="bold magenta")
        table.add_column("Category", style="cyan", width=30)
        table.add_column("Count", justify="right", style="green", width=10)
        table.add_column("Percentage", justify="right", style="yellow", width=12)

        for category, count in category_counts.head(20).items():
            percentage = (count / total_ads) * 100
            table.add_row(
                category[:27] + "..." if len(category) > 30 else category,
                f"{count:,}",
                f"{percentage:.1f}%"
            )

        console.print(table)

        if len(category_counts) > 20:
            console.print(f"[dim]... and {len(category_counts) - 20} more categories[/dim]")

    def run(self, output_file: str = None) -> str:
        """Run the complete categorization process"""
        ads = self.load_recent_ads()

        if not ads:
            console.print("[yellow]No ads found for the specified date range[/yellow]")
            return None

        categorized_ads = self.categorize_ads(ads)
        saved_file = self.save_to_csv(categorized_ads, output_file)
        return saved_file