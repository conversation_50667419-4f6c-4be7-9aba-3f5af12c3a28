from datetime import datetime, timedelta
import holidays
import logging
import os
from typing import Dict, Any, Optional


class ReportConfig:
    """Handles configuration loading, flags, and date utilities."""
    def __init__(self, config_dict: Dict[str, Any], is_weekly_report: bool = False):
        self.config_dict = config_dict
        self.logger = logging.getLogger(f"{__name__}.ReportConfig")
        self.is_weekly_report = is_weekly_report

        # Core settings
        self.report_date_str: str = config_dict['date'] # Raw date string from config
        self.bucket_name: str = config_dict['bucket_name']
        
        # Use project_root to determine the base_data_path 
        project_root = config_dict.get('project_root')
        if not project_root:
            # Fall back to directories.base_dir if present
            project_root = config_dict.get('directories', {}).get('base_dir')
            if not project_root:
                # Use current directory as last resort
                project_root = os.getcwd()
                self.logger.warning(f"No project_root found in config, using current directory: {project_root}")
        
        self.base_data_path: str = os.path.join(project_root, 'data/')
        self.s3_prod_url: str = "https://cdn.lexgenius.ai"
        self.mdl_chart_top_n: int = config_dict.get('mdl_chart_top_n', 10)
        self.s3_upload_workers: int = config_dict.get('s3_upload_workers', 10)
        self.cloudfront_distribution_id: Optional[str] = config_dict.get('cloudfront_distribution_id')
        self.calendly_link: str = "https://calendly.com/lexgenius/30min"

        # Load Control Flags from config file, with config_dict overrides
        import json
        config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'src', 'config', 'reports', 'core_config.json')
        try:
            with open(config_path) as f:
                flags_config = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            self.logger.warning(f"Could not load flags config from {config_path}, using defaults")
            flags_config = {}
            
        # Use config_dict values if available, otherwise fall back to file config, then defaults
        self.show_upcoming_hearings: bool = config_dict.get('show_upcoming_hearings', flags_config.get('show_upcoming_hearings', False))
        self.show_calendly: bool = config_dict.get('show_calendly', flags_config.get('show_calendly', False))
        self.show_afff_stats: bool = config_dict.get('show_afff_stats', flags_config.get('show_afff_stats', True))
        self.show_special_reports: bool = config_dict.get('show_special_reports', flags_config.get('show_special_reports', False))
        self.show_news: bool = config_dict.get('show_news', flags_config.get('show_news', False))
        self.show_announcements: bool = config_dict.get('show_announcements', flags_config.get('show_announcements', False))
        self.show_summary: bool = config_dict.get('show_summary', flags_config.get('show_summary', True))
        self.show_litigation: bool = config_dict.get('show_litigation', flags_config.get('show_litigation', True))
        self.show_filings: bool = config_dict.get('show_filings', flags_config.get('show_filings', True))
        self.show_adspy: bool = config_dict.get('show_adspy', flags_config.get('show_adspy', True))
        self.minimal_email_mode: bool = config_dict.get('minimal_email_mode', flags_config.get('minimal_email_mode', False))

        # Facebook Ads Configuration
        fb_ads_config = config_dict.get('fb_ads', {})
        self.fb_ads_generate_pages: bool = fb_ads_config.get('generate_ad_pages', True)
        self.fb_ads_upload_pages: bool = fb_ads_config.get('upload_ad_pages', True)
        self.fb_ads_use_cache: bool = fb_ads_config.get('use_cache', True)
        self.fb_ads_force_regenerate: bool = fb_ads_config.get('force_regenerate', False)
        self.fb_ads_cache_ttl_hours: int = fb_ads_config.get('cache_ttl_hours', 24)

        # Derived Date Info
        self.iso_date: Optional[str] = self._format_iso_date(self.report_date_str)
        self.us_holidays = holidays.US()
        self.is_sunday: bool = False
        self.seven_days_ago: Optional[str] = None
        self.download_dir: Optional[str] = None

        if self.iso_date:
            date_obj = datetime.strptime(self.iso_date, "%Y%m%d")
            self.is_sunday = date_obj.weekday() == 6
            self.seven_days_ago = (date_obj - timedelta(days=7)).strftime("%Y%m%d")
            self.download_dir = self._get_download_dir()
        else:
            self.logger.error("Could not parse report date, ISO date is None.")
            raise ValueError(f"Could not parse report date: {self.report_date_str}")

        if self.download_dir is None:
             raise ValueError("Download directory could not be determined or created")

        self.logger.info(f"Configuration initialized for date: {self.report_date_str} (ISO: {self.iso_date})")
        self.logger.info(f"Download directory: {self.download_dir}")

    def _format_iso_date(self, date_str: str) -> Optional[str]:
        """Attempts to parse date string with multiple formats."""
        for fmt in ["%m/%d/%y", "%m-%d/%y", "%m-%d-%y", "%Y%m%d"]:
            try:
                return datetime.strptime(date_str, fmt).strftime("%Y%m%d")
            except ValueError:
                pass
        self.logger.error(f"Error formatting date: {date_str} - Unknown format")
        return None

    def _get_download_dir(self) -> Optional[str]:
        """Determines and creates the download directory based on ISO date."""
        if not self.iso_date:
            self.logger.error("Cannot determine download directory: iso_date is not set.")
            return None

        download_dir = os.path.join(self.base_data_path, self.iso_date)
        self.logger.info(f"Target download directory: {download_dir}")
        try:
            os.makedirs(download_dir, exist_ok=True)
            os.makedirs(os.path.join(download_dir, 'reports'), exist_ok=True)
            return download_dir
        except OSError as e:
            self.logger.error(f"Failed to create directory {download_dir}: {e}")
            return None

    @staticmethod
    def add_ordinal_suffix(day: int) -> str:
        """Adds st, nd, rd, th suffix to a day number."""
        if 11 <= day <= 13:
            return str(day) + "th"
        suffixes = {1: 'st', 2: 'nd', 3: 'rd'}
        return str(day) + suffixes.get(day % 10, "th")

    def _next_business_day(self, date: datetime) -> datetime:
        """Finds the next business day, skipping weekends and holidays."""
        one_day = timedelta(days=1)
        next_day = date + one_day
        while next_day.weekday() >= 5 or next_day in self.us_holidays:
            next_day += one_day
        return next_day

    def get_formatted_report_date(self, for_summary: bool = False) -> str:
        """Formats the report date string (e.g., 'September 26th, 2024')."""
        if not self.iso_date:
            return "Date Not Specified"
        try:
            base_date = datetime.strptime(self.iso_date, '%Y%m%d')
            report_date_obj = base_date if for_summary else self._next_business_day(base_date)
            month = report_date_obj.strftime('%B')
            year = report_date_obj.strftime('%Y')
            day_with_suffix = self.add_ordinal_suffix(report_date_obj.day)
            return f"{month} {day_with_suffix}, {year}"
        except ValueError:
            self.logger.error(f"Could not parse iso_date '{self.iso_date}' for formatting report date.")
            return "Invalid Date"

    def get_formatted_weekly_summary_date(self) -> str:
        """Formats the date range string for the weekly summary."""
        if not self.iso_date or not self.seven_days_ago:
            return f"Weekly Data (Dates Missing)"
        try:
            start_dt = datetime.strptime(self.seven_days_ago, '%Y%m%d')
            end_dt = datetime.strptime(self.iso_date, '%Y%m%d')
            start_day_suffix = self.add_ordinal_suffix(start_dt.day)
            end_day_suffix = self.add_ordinal_suffix(end_dt.day)
            return f"{start_dt.strftime('%B')} {start_day_suffix} to {end_dt.strftime('%B')} {end_day_suffix}, {end_dt.year}"
        except (ValueError, TypeError) as e:
            self.logger.error(f"Error formatting weekly summary date range: {e}")
            return f"Data from {self.seven_days_ago} to {self.iso_date}"