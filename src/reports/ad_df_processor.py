# ad_df_processor.py

import json
import logging
import re
from pathlib import Path
from typing import Dict, <PERSON><PERSON>, List, Optional

import pandas as pd

from src.utils.date import DateUtils
# Ensure utility imports are correct
from src.utils.law_firm import LawFirmNameHandler

try:
    from src.fb_ads.vector_clusterer import VectorClusterer
except ImportError:
    VectorClusterer = None  # Fallback if not found, will cause error if used
    logging.getLogger(__name__).error("VectorClusterer class not found. Campaign tagging will fail.")


class AdDataFrameProcessor:
    def __init__(self, fb_ad_db, logger: logging.Logger, iso_date: str,
                 vector_clusterer: Optional[VectorClusterer] = None, days_back: int = 14):  # Add days_back parameter
        self.fb_ad_db = fb_ad_db
        self.logger = logger
        self.iso_date = iso_date
        self.vector_clusterer = vector_clusterer  # Store the instance
        if self.vector_clusterer is None:
            self.logger.warning(
                "VectorClusterer instance not provided to AdDataFrameProcessor. Rule-based campaign tagging will be skipped.")

        try:
            self.start_date = DateUtils.get_date_before_n_days(days_back, iso_date)
            self.logger.info(
                f"AdDataFrameProcessor initialized with {days_back} days lookback period: {self.start_date} to {iso_date}")
        except ValueError as e:
            self.logger.error(f"Invalid iso_date format '{iso_date}'. Cannot calculate start_date. Error: {e}")
            raise ValueError(f"Invalid iso_date format for AdDataFrameProcessor: {iso_date}") from e

    @staticmethod
    def _clean_ad_firm_name_final(name: str) -> str:
        """
        Applies final cleaning to ad law firm names:
        - Removes commas.
        - Removes periods EXCEPT for specific cases like initials (e.g., "S.")
          and predefined exceptions (e.g., "AllConsumer.com").
        Assumes capitalization has already been applied.
        """
        if not isinstance(name, str):
            return ""

        # --- Exceptions where periods should be kept exactly ---
        exceptions_keep_periods = {"AllConsumer.com", "ClassAction.org", "AllVeteran.com"}
        if name in exceptions_keep_periods:
            return name  # Return the exact exception string

        # 1. Remove all commas unconditionally
        name_no_commas = name.replace(',', '')

        # 2. Process parts to handle periods conditionally
        parts = name_no_commas.split()  # Split by whitespace
        cleaned_parts = []
        # Regex to identify a single uppercase letter followed by a period (potential initial)
        initial_pattern = re.compile(r"^[A-Z]\.$")

        for part in \
                parts:
            # Check if the part matches the initial pattern (e.g., "S.", "T.")
            if initial_pattern.match(part):
                cleaned_parts.append(part)  # Keep the initial with its period
            else:
                # For all other parts, remove any periods
                cleaned_parts.append(part.replace('.', ''))

        # 3. Join parts back with single spaces
        cleaned_name = ' '.join(cleaned_parts)
        # Final cleanup for any potential double spaces introduced if parts were empty
        cleaned_name = re.sub(r'\s+', ' ', cleaned_name).strip()

        return cleaned_name

    # --- Add a helper for detailed logging during apply ---
    def _log_and_capitalize(self, name):
        """Wraps the capitalization call with logging for debugging."""
        original_name = name
        try:
            name_str = str(name).strip() if pd.notna(name) and name is not None else ""
            if not name_str:
                return ""

            # Handle semicolon-separated law firms
            if ';' in name_str:
                from src.utils.law_firm_normalizer import normalize_law_firm_name
                firms = [firm.strip() for firm in name_str.split(';')]
                normalized_firms = []
                for firm in firms:
                    if firm:
                        capitalized_firm = LawFirmNameHandler.capitalize_law_firm_names(firm)
                        normalized_firm = normalize_law_firm_name(capitalized_firm)
                        normalized_firms.append(normalized_firm)
                result = ' ; '.join(normalized_firms)
                return result
            else:
                # Single firm - use existing logic
                from src.utils.law_firm_normalizer import normalize_law_firm_name
                capitalized_name = LawFirmNameHandler.capitalize_law_firm_names(name_str)
                normalized_name = normalize_law_firm_name(capitalized_name)
                return normalized_name
        except Exception as e:
            self.logger.error(f"Error during law firm capitalization for Input='{original_name}': {e}", exc_info=True)
            return original_name

    async def load_ad_df_async(self) -> pd.DataFrame:
        """
        Loads, preprocesses, applies capitalization, final cleaning,
        and (temporarily for testing) rule-based campaign tagging to the Ad DataFrame.
        """
        self.logger.info("Starting Ad DataFrame load process...")

        # Step 1: Query database
        df = await self._query_and_create_dataframe(self.start_date)
        if df.empty:
            self.logger.info("Ad DataFrame is empty after query.")
            return df

        # Step 2: Preprocess (column renaming, date handling, basic type conversion)
        df = self._preprocess_dataframe(df, self.start_date)
        self.logger.info(f"Ad DataFrame shape after preprocess: {df.shape}")
        if df.empty:
            self.logger.warning("Ad DataFrame became empty after preprocessing.")
            return df

        # Step 3: Apply consolidated capitalization logic if 'law_firm' column exists
        if 'law_firm' in df.columns:
            self.logger.info("Applying consolidated law firm name capitalization to Ad DataFrame...")
            try:
                law_firm_col_str = df['law_firm'].astype(str)
                sample_before = law_firm_col_str.dropna().unique()[:20]
            except Exception as log_err:
                self.logger.warning(f"Could not log pre-capitalization samples: {log_err}")
            df['law_firm'] = df['law_firm'].fillna('').astype(str).apply(self._log_and_capitalize)
            self.logger.info("Finished applying consolidated capitalization for Ad DataFrame.")

            self.logger.info("Applying final cleaning (commas, conditional periods) to Ad DataFrame law firm names...")
            try:
                df['law_firm'] = df['law_firm'].astype(str).apply(AdDataFrameProcessor._clean_ad_firm_name_final)
                sample_after_clean = df['law_firm'].astype(str).dropna().unique()[:20]
            except Exception as clean_err:
                self.logger.error(f"Error during final cleaning of law firm names: {clean_err}")
            self.logger.info("Finished final cleaning.")

            try:
                sample_after = df['law_firm'].astype(str).dropna().unique()[:20]
            except Exception as log_err:
                self.logger.warning(f"Could not log post-transformation samples: {log_err}")
        else:
            self.logger.warning("Ad DataFrame missing 'law_firm' column, cannot apply capitalization or cleaning.")

        # Step X: (TEMPORARY) Apply rule-based campaign tagging using VectorClusterer
        if self.vector_clusterer:
            self.logger.info("Temporarily applying rule-based campaign tagging using VectorClusterer...")

            campaigns_assigned = []
            summaries_updated_by_rules = []

            def apply_rules(row_series: pd.Series) -> pd.Series:
                # Convert row to dict with snake_case keys as expected by get_category_for_ad_data
                # The DataFrame columns should already be snake_cased by _preprocess_dataframe
                ad_dict_for_rules = row_series.to_dict()

                original_summary = ad_dict_for_rules.get('summary')

                # get_category_for_ad_data modifies ad_dict_for_rules in-place
                self.vector_clusterer.get_category_for_ad_data(ad_dict_for_rules)

                # Update the row series with the new 'campaign' and potentially modified 'summary'
                row_series['campaign'] = ad_dict_for_rules.get('campaign', 'Other (Rule Error)')

                # Log if summary was changed by rules
                new_summary = ad_dict_for_rules.get('summary')
                if new_summary != original_summary:
                    summaries_updated_by_rules.append({
                        'ad_archive_id': row_series.get('ad_archive_id', 'N/A'),
                        'original_summary': original_summary,
                        'new_summary': new_summary,
                        'campaign': row_series['campaign']
                    })
                row_series['summary'] = new_summary

                campaigns_assigned.append(row_series['campaign'])
                return row_series

            df = df.apply(apply_rules, axis=1)

            # Log summary of campaign tagging
            if campaigns_assigned:
                from collections import Counter
                campaign_counts = Counter(campaigns_assigned)
                self.logger.info(f"Rule-based campaign tagging complete. Counts: {dict(campaign_counts)}")
                if summaries_updated_by_rules:
                    self.logger.info(
                        f"{len(summaries_updated_by_rules)} summaries were updated by rule-based campaign logic.")
                    # For debugging, log a few examples of summary updates
                    for i, update_info in enumerate(summaries_updated_by_rules[:3]):
                        self.logger.debug(
                            f"SummaryUpdateEx{i + 1}: AdID {update_info['ad_archive_id']}, "
                            f"Original: '{str(update_info['original_summary'])[:30]}...', "
                            f"New: '{str(update_info['new_summary'])[:30]}...' (Campaign: {update_info['campaign']})"
                        )

            self.logger.info("Finished temporary rule-based campaign tagging.")
        else:
            self.logger.warning(
                "VectorClusterer not provided. Skipping temporary rule-based campaign tagging. 'campaign' column will not be added by this step.")
            if 'campaign' not in df.columns:  # Ensure column exists if not added
                df['campaign'] = 'N/A (No Rules Engine)'

        # Step 5: Filter out ads with summaries marked as invalid/skipped
        # This filtering happens *after* rule-based summary updates for consistency.
        df = self._filter_invalid_summaries(df)
        self.logger.info(f"Ad DataFrame shape after filtering invalid summaries: {df.shape}")
        if df.empty:
            self.logger.warning("Ad DataFrame became empty after filtering invalid summaries.")
            return df

        self.logger.info("Finished Ad DataFrame load process.")
        return df

    def _filter_invalid_summaries(self, df: pd.DataFrame) -> pd.DataFrame:
        """Filters out entries with invalid or missing summaries."""
        if 'summary' not in df.columns:
            self.logger.warning("No 'summary' column found, skipping summary filtering.")
            return df
        df['summary'] = df['summary'].fillna('').astype(str)
        invalid_markers = {'na', 'skipped', 'n/a', ''}  # summary generation failed is handled by rules now
        # Check against specific "Summary generation failed" only if it wasn't replaced by a rule.
        # If a rule replaced it, it's now a valid campaign name.
        # This logic simplifies: if summary is one of these markers now, it means no rule replaced it.
        valid_summary_mask = ~df['summary'].str.lower().isin(invalid_markers)

        # Additionally, if VectorClusterer might set summary to "Summary generation failed" AND a rule matches,
        # we need to be careful. The current `get_category_for_ad_data` replaces these.
        # So, we only need to check if the current summary value is literally "Summary generation failed"
        # if it implies that no rule matched *and* AI summary also failed.
        # However, the rule for `get_category_for_ad_data` is to replace "Summary generation failed" with the rule name.
        # So, if 'summary' still IS "Summary generation failed", it means no rule matched it for replacement.
        valid_summary_mask &= ~(df['summary'].str.lower() == 'summary generation failed')

        filtered_df = df[valid_summary_mask].copy()
        removed_count = len(df) - len(filtered_df)
        if removed_count > 0:
            self.logger.info(
                f"Removed {removed_count} entries with invalid, missing, or 'Summary generation failed' summaries (after rule application).")
        return filtered_df

    # Method moved to DateUtils class in src.lib.utils.date

    async def _query_and_create_dataframe(self, start_date: str) -> pd.DataFrame:
        """Queries the ad archive and returns a DataFrame."""
        self.logger.info(f"Starting ad archive query from {start_date} to {self.iso_date} (14 days)")
        import time
        query_start = time.time()
        try:
            self.logger.info(f"[AD_DF] About to call fb_ad_db.query_ad_archive_by_date")
            print(f"[AD_DF] DEBUG: Calling fb_ad_db.query_ad_archive_by_date({start_date}, {self.iso_date})")
            items = await self.fb_ad_db.query_ad_archive_by_date(start_date, self.iso_date)
            print(f"[AD_DF] DEBUG: Call returned {len(items)} items")
            query_time = time.time() - query_start
            self.logger.info(f"Query completed in {query_time:.2f} seconds. Retrieved {len(items)} ad items.")
            if not items: return pd.DataFrame()
            return pd.DataFrame(items)
        except Exception as e:
            query_time = time.time() - query_start
            self.logger.error(f"Error after {query_time:.2f} seconds querying or creating DataFrame: {e}", exc_info=True)
            return pd.DataFrame()

    def _preprocess_dataframe(self, df: pd.DataFrame, start_date: str) -> pd.DataFrame:
        """Handles column renaming, date conversion, and basic type setting."""
        if df.empty: return df
        col_order, col_names_mapping = self._get_column_definitions()
        missing_original_cols = [col for col in col_order if col not in df.columns]
        rename_map_existing = {k: v for k, v in col_names_mapping.items() if k in df.columns}
        if missing_original_cols:
            self.logger.warning(f"Original Ad DB columns missing: {missing_original_cols}. Renaming only existing.")
        df.rename(columns=rename_map_existing, inplace=True)
        target_cols = list(col_names_mapping.values())
        for target_col in target_cols:
            if target_col not in df.columns:
                if target_col == 'is_active':
                    df[target_col] = False
                elif target_col in ['start_date', 'end_date']:
                    df[target_col] = ''
                else:
                    df[target_col] = ''
        try:
            if 'start_date' in df.columns: df['start_date'] = df['start_date'].astype(str)
            if 'end_date' in df.columns: df['end_date'] = df['end_date'].astype(str)
            start_date_dt_filter = pd.to_datetime(start_date, format='%Y%m%d', errors='coerce')

            # Try multiple date formats for more robust parsing
            df['start_date_dt'] = pd.to_datetime(df['start_date'], format='%Y%m%d', errors='coerce')
            # If that fails, try auto-parsing for other common formats
            if df['start_date_dt'].isna().all():
                df['start_date_dt'] = pd.to_datetime(df['start_date'], errors='coerce')

            df['end_date_dt'] = pd.to_datetime(df['end_date'], format='%Y%m%d', errors='coerce')
            # If that fails, try auto-parsing for other common formats
            if df['end_date_dt'].isna().all():
                df['end_date_dt'] = pd.to_datetime(df['end_date'], errors='coerce')

            if pd.notna(start_date_dt_filter):
                valid_date_mask = (df['start_date_dt'] >= start_date_dt_filter) | (
                            df['end_date_dt'] >= start_date_dt_filter)
                df = df[valid_date_mask].copy()
            df['start_date'] = df['start_date_dt'].dt.strftime('%m/%d/%y').fillna('')
            df['end_date'] = df['end_date_dt'].dt.strftime('%m/%d/%y').fillna('')
            df.drop(columns=['start_date_dt', 'end_date_dt'], inplace=True)
        except Exception as date_err:
            self.logger.error(f"Error processing ad date columns: {date_err}.", exc_info=True)
            if 'start_date' not in df.columns:
                df['start_date'] = ''
            else:
                df['start_date'] = df['start_date'].fillna('').astype(str)
            if 'end_date' not in df.columns:
                df['end_date'] = ''
            else:
                df['end_date'] = df['end_date'].fillna('').astype(str)
        for col in ['caption', 'body', 'title', 'law_firm', 'summary', 'page_name']:
            if col in df.columns: df[col] = df[col].fillna('').astype(str)
        return df

    # --- Optional helper methods for further cleaning/deduplication ---
    # These might be run *after* capitalization in load_ad_df if needed

    def _filter_and_clean_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """Applies unwanted content filtering and deduplication."""
        original_len = len(df)
        df = self._sort_and_drop_duplicates(df)
        removed_count = original_len - len(df)
        return df

    @staticmethod
    def _remove_duplicate_entries(df: pd.DataFrame) -> pd.DataFrame:
        """Removes duplicate ads based on ad_archive_id."""
        subset = ['ad_archive_id']
        original_len = len(df)
        if all(c in df.columns for c in subset):
            df = df.drop_duplicates(subset=subset, keep='first')
        removed_count = original_len - len(df)
        if removed_count > 0: logging.debug(f"Removed {removed_count} duplicate entries by ad_archive_id.")
        return df

    @staticmethod
    def _load_unwanted_terms() -> List[str]:
        """Loads unwanted terms from configuration file."""
        # Simplified path, assumes config is findable relative to a project root or via PYTHONPATH
        config_path = Path(__file__).resolve().parents[2] / 'config' / 'reports' / 'content_filters.json'
        try:
            with open(config_path, 'r') as f:
                config = json.load(f)
            return config.get('unwanted_terms', [])
        except Exception as e:
            logging.error(f"Error loading unwanted terms: {e}"); return []

    @staticmethod
    def _filter_out_unwanted_content(df: pd.DataFrame) -> pd.DataFrame:
        """Filters ads based on keywords in the summary."""
        unwanted_terms = AdDataFrameProcessor._load_unwanted_terms()
        if 'summary' in df.columns and unwanted_terms:
            pattern = '|'.join(unwanted_terms)
            original_len = len(df)
            df['summary_str'] = df['summary'].fillna('').astype(str)
            mask = ~df['summary_str'].str.contains(pattern, case=False, na=False)
            df = df[mask].copy().drop(columns=['summary_str'])
            removed_count = original_len - len(df)
            if removed_count > 0: logging.debug(f"Filtered {removed_count} ads by unwanted summary terms.")
        return df

    def _sort_and_drop_duplicates(self, df: pd.DataFrame) -> pd.DataFrame:
        """Sorts by date and drops duplicates based on law_firm and summary."""
        if 'start_date' in df.columns and 'end_date' in df.columns:
            try:
                df['start_date_dt'] = pd.to_datetime(df['start_date'], format='%m/%d/%y', errors='coerce')
                df['end_date_dt'] = pd.to_datetime(df['end_date'], format='%m/%d/%y', errors='coerce')
                df = df.sort_values(by=['start_date_dt', 'end_date_dt'], ascending=[True, False], na_position='last')
                df = df.drop(columns=['start_date_dt', 'end_date_dt'])
            except Exception as sort_err:
                self.logger.warning(f"Could not sort by date: {sort_err}.")
        subset_final = ['law_firm', 'summary']
        if all(c in df.columns for c in subset_final):
            original_len = len(df)
            df['summary'] = df['summary'].fillna('').astype(str)
            df = df.drop_duplicates(subset=subset_final, keep='first')
            removed_count = original_len - len(df)
        else:
            self.logger.warning(
                f"Cannot drop final duplicates, missing: {[c for c in subset_final if c not in df.columns]}")
        return df

    @staticmethod
    def _get_column_definitions() -> Tuple[List[str], Dict[str, str]]:
        """Returns the expected original column names and their target mapping."""
        col_order = [
            'AdArchiveID', 'AdCreativeId', 'IsActive', 'LawFirm', 'PageName', 'PageID', 'StartDate', 'EndDate',
            'Summary', 'CtaText', 'Title', 'Body', 'LinkDescription', 'Caption', 'LinkUrl', 'PublisherPlatform',
            'OriginalImageUrl', 'ResizedImageUrl', 'VideoPreviewImageUrl', 'VideoHdUrl', 'VideoSdUrl'
        ]
        col_names_mapping = {
            'AdArchiveID': 'ad_archive_id', 'AdCreativeId': 'ad_creative_id', 'IsActive': 'is_active',
            'LawFirm': 'law_firm', 'PageName': 'page_name', 'PageID': 'page_id',
            'StartDate': 'start_date', 'EndDate': 'end_date', 'Summary': 'summary',
            'CtaText': 'cta_text', 'Title': 'title', 'Body': 'body',
            'LinkDescription': 'link_description', 'Caption': 'caption', 'LinkUrl': 'link_url',
            'PublisherPlatform': 'publisher_platform', 'OriginalImageUrl': 'original_image_url',
            'ResizedImageUrl': 'resized_image_url', 'VideoPreviewImageUrl': 'video_preview_image_url',
            'VideoHdUrl': 'video_hd_url', 'VideoSdUrl': 'video_sd_url'
        }
        return col_order, col_names_mapping

    def load_ad_df(self) -> pd.DataFrame:
        """
        Synchronous method for backward compatibility with original data loader.
        The original data loader calls this exact method name.
        """
        import asyncio
        import concurrent.futures
        
        try:
            # Check if we're already in an event loop
            try:
                loop = asyncio.get_running_loop()
                # We're in a loop, need to run in a thread
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(
                        lambda: asyncio.run(self.load_ad_df_async())
                    )
                    return future.result()
            except RuntimeError:
                # No event loop running, safe to use asyncio.run
                return asyncio.run(self.load_ad_df_async())
        except Exception as e:
            self.logger.error(f"Error in sync load_ad_df wrapper: {e}")
            return pd.DataFrame()
