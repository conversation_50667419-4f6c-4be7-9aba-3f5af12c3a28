"""
Clean Data Loader without migration helpers or compatibility layers.
"""
import json
import logging
import os
import re
from datetime import datetime, timed<PERSON>ta
from typing import List, Dict, Any, Optional
import asyncio

import pandas as pd

from src.utils.law_firm import LawFirmNameHandler
from src.utils.law_firm_normalizer import normalize_law_firm_name

# Direct repository imports - no compatibility layers
from src.repositories.pacer_repository import PacerRepository
from .ad_df_processor import AdDataFrameProcessor
from .config import ReportConfig


class StaticDataLoader:
    """Loads static or infrequently changing data used in reports."""

    def __init__(self):
        self.logger = logging.getLogger(f"{__name__}.StaticDataLoader")
        # Base path for config files - now in src/config/reports
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
        self.config_path = os.path.abspath(os.path.join(
            project_root, 'src', 'config', 'reports'
        ))
        # Also store the general data path for non-report configs
        self.data_path = os.path.abspath(os.path.join(
            project_root, 'src', 'config'
        ))
        self.logger.debug(f"Config path for static data: {self.config_path}")

    def _load_json_config(self, filename: str, default_value: Any = None) -> Any:
        """Helper method to load JSON configuration files."""
        file_path = os.path.join(self.config_path, filename)
        try:
            if not os.path.exists(file_path):
                self.logger.warning(f"Config file not found: {file_path}. Using default value.")
                return default_value

            with open(file_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
                # Most config files have a 'data' key that contains the actual data
                # Handle both dict and list formats
                if isinstance(config_data, dict):
                    return config_data.get('data', config_data)
                else:
                    return config_data
        except (json.JSONDecodeError, IOError) as e:
            self.logger.error(f"Error loading config file {filename}: {e}")
            return default_value

    def load_attorney_df(self) -> pd.DataFrame:
        """Loads the static attorney-to-firm lookup table from attorney_lookup.json."""
        self.logger.debug("Loading attorney lookup data from JSON file.")
        try:
            # Use the data_path to access attorneys config
            attorney_lookup_path = os.path.join(
                self.data_path, 'attorneys', 'attorney_lookup.json'
            )
            self.logger.debug(f"Loading attorney lookup from: {attorney_lookup_path}")
            with open(attorney_lookup_path, 'r') as f:
                attorney_data = json.load(f)
                # Convert to DataFrame with consistent column names
                data = {
                    'Attorney': [item['attorney_name'] for item in attorney_data],
                    'Law Firm': [item['law_firm'] for item in attorney_data]
                }
                return pd.DataFrame(data)
        except Exception as e:
            self.logger.error(f"Failed to load attorney lookup: {str(e)}")
            return pd.DataFrame(columns=['Attorney', 'Law Firm'])

    def load_litigation_sponsorships(self) -> Dict[str, Dict[str, Any]]:
        """Loads the mapping between MDL numbers and their specific sponsors."""
        self.logger.info("Loading litigation-specific sponsorships from config.")
        return self._load_json_config('litigation_sponsorships.json', {})

    def load_general_sponsorships(self) -> Dict[str, Dict[str, Any]]:
        """Loads general sponsorship data including header and numbered slots."""
        self.logger.debug("Loading general sponsorships from config.")
        return self._load_json_config('general_sponsorships.json', {})

    def load_upcoming_hearings(self) -> List[Dict[str, Any]]:
        """Loads upcoming hearings and adds formatted date parts."""
        self.logger.info("Loading upcoming hearings data from config.")
        raw_hearings_data = self._load_json_config('upcoming_hearings.json', [])
        formatted_hearings = []

        for hearing in raw_hearings_data:
            hearing_date_str = hearing.get('date')
            title = hearing.get('title', 'Untitled Hearing')
            try:
                if not hearing_date_str:
                    raise ValueError("Missing date string")
                hearing_date = datetime.strptime(hearing_date_str, '%m/%d/%Y')
                hearing['date_day'] = hearing_date.strftime('%d')
                hearing['date_year'] = hearing_date.strftime('%Y')
                formatted_hearings.append(hearing)
            except (ValueError, KeyError, TypeError) as e:
                hearing['date_month_short'] = '???'
                hearing['date_day'] = '??'
                hearing['date_year'] = '????'
                hearing['formatted_date'] = hearing_date_str or 'No Date Provided'
                formatted_hearings.append(hearing)

        def sort_key(h):
            try:
                return datetime.strptime(h['date'], '%m/%d/%Y') if h.get('date') else datetime.max
            except (ValueError, TypeError):
                return datetime.max

        formatted_hearings.sort(key=sort_key)
        return formatted_hearings

    def load_announcements(self) -> List[Dict[str, str]]:
        """Loads predefined announcements for display."""
        self.logger.info("Loading announcements from config.")
        return self._load_json_config('announcements.json', [])

    def load_news(self) -> List[Dict[str, Any]]:
        """Loads recent news items (JPML, CMOs) with separate date/title."""
        self.logger.info("Loading news items from config.")
        default_news = [
            {"date": "04/03/25",
             "title": "IN RE: TikTok, Inc., Minor Privacy Litigation",
             "link": "https://www.jpml.uscourts.gov/sites/jpml/files/MDL-3144-Transfer_Order-3-25.pdf"
             }
        ]
        return self._load_json_config('news.json', default_news)

    def load_mdl_lookup(self) -> pd.DataFrame:
        """Load MDL lookup data for title enhancement."""
        self.logger.debug("Loading MDL lookup data for title enhancement.")
        mdl_lookup_path = os.path.abspath(os.path.join(
            self.data_path, 'mdl', 'mdl_lookup.json'
        ))
        
        try:
            if not os.path.exists(mdl_lookup_path):
                self.logger.warning(f"MDL lookup file not found at {mdl_lookup_path}. Title enhancement will be limited.")
                return pd.DataFrame()
                
            with open(mdl_lookup_path, 'r', encoding='utf-8') as f:
                mdl_data = json.load(f)
                
            if not mdl_data:
                self.logger.warning("MDL lookup file is empty.")
                return pd.DataFrame()
                
            df = pd.DataFrame(mdl_data)
            if 'mdl_num' in df.columns:
                # Clean and normalize MDL numbers
                df['mdl_num'] = df['mdl_num'].astype(str).str.strip().str.removesuffix('.0')
                self.logger.info(f"Loaded {len(df)} MDL entries for title enhancement.")
                return df
            else:
                self.logger.warning("MDL lookup file missing 'mdl_num' column.")
                return pd.DataFrame()
                
        except Exception as e:
            self.logger.error(f"Failed to load MDL lookup file: {e}", exc_info=True)
            return pd.DataFrame()

    def load_special_reports(self) -> List[Dict[str, Any]]:
        """Load special reports data."""
        self.logger.info("Loading special reports from config.")
        return self._load_json_config('special_reports.json', [])


class DynamicDataLoader:
    """
    Clean async data loader using repositories directly.
    No migration helpers or compatibility layers.
    """

    def __init__(self, report_config: ReportConfig, pacer_repository: PacerRepository, 
                 ad_processor: AdDataFrameProcessor, law_firm_handler: LawFirmNameHandler):
        self.config = report_config
        self.pacer_repo = pacer_repository  # Direct repository, no manager wrapper
        self.ad_processor = ad_processor
        self.law_firm_handler = law_firm_handler
        self.logger = logging.getLogger(f"{__name__}.DynamicDataLoader")
        self._cached_docket_df: Optional[pd.DataFrame] = None
        self._cached_weekly_docket_df: Optional[pd.DataFrame] = None
        
        # Load MDL lookup data for title enhancement
        static_loader = StaticDataLoader()
        self.mdl_lookup_df = static_loader.load_mdl_lookup()
        if not self.mdl_lookup_df.empty:
            self.logger.info(f"Loaded {len(self.mdl_lookup_df)} MDL entries for title enhancement.")
        else:
            self.logger.warning("No MDL lookup data available. Titles will not be enhanced.")

    @staticmethod
    def _format_text(value: Any) -> str:
        """Cleans text: trims whitespace, collapses multiple newlines."""
        if not isinstance(value, str): 
            return ''
        value = value.strip()
        value = re.sub(r'\r\n', '\n', value)
        value = re.sub(r'\n{3,}', '\n\n', value)
        return value
    
    def _enhance_titles_with_mdl_lookup(self, df: pd.DataFrame) -> pd.DataFrame:
        """Enhance case titles using MDL lookup data."""
        if self.mdl_lookup_df.empty:
            self.logger.debug("No MDL lookup data available for title enhancement.")
            return df
            
        if 'mdl_num' not in df.columns or 'title' not in df.columns:
            self.logger.warning("DataFrame missing required columns for title enhancement.")
            return df
            
        enhanced_count = 0
        total_with_mdl = 0
        
        for idx, row in df.iterrows():
            mdl_num = str(row['mdl_num']).strip()
            
            # Skip if no MDL number
            if not mdl_num or mdl_num in ['', 'nan', 'None', 'NA']:
                continue
                
            total_with_mdl += 1
            
            # Camp Lejeune MDL 25 lookup - use '25' directly as it exists in mdl_lookup.json
            
            # Look up in MDL data
            match = self.mdl_lookup_df[self.mdl_lookup_df['mdl_num'] == mdl_num]
            if not match.empty:
                litigation_title = match.iloc[0].get('litigation', '')
                if litigation_title and litigation_title.strip():
                    # Use the litigation name from lookup
                    df.at[idx, 'title'] = litigation_title.strip()
                    enhanced_count += 1
        
        if enhanced_count > 0:
            self.logger.info(f"Enhanced {enhanced_count} titles out of {total_with_mdl} records with MDL numbers.")
        else:
            self.logger.warning(f"No titles enhanced despite {total_with_mdl} records having MDL numbers.")
            
        return df

    async def load_docket_df_async(self, is_weekly: bool = False) -> pd.DataFrame:
        """Loads PACER docket data asynchronously using repository."""
        cache_attr = '_cached_weekly_docket_df' if is_weekly else '_cached_docket_df'
        if getattr(self, cache_attr) is not None:
            self.logger.info(f"Using cached {'weekly' if is_weekly else 'daily'} docket data.")
            return getattr(self, cache_attr).copy()

        if not self.config.iso_date:
            self.logger.error("Cannot load dockets: iso_date is not set.")
            return pd.DataFrame()

        start_date = self.config.seven_days_ago if is_weekly else self.config.iso_date
        end_date = self.config.iso_date
        date_range_str = f"{start_date} to {end_date}" if is_weekly else self.config.iso_date

        if not start_date:
            self.logger.error(
                f"Cannot load dockets: Start date is missing for {'weekly' if is_weekly else 'daily'} range.")
            return pd.DataFrame()

        self.logger.info(f'Loading PACER dockets for {date_range_str}')
        items = []
        
        try:
            if is_weekly:
                # For weekly reports, query by FilingDate for each day in the range
                self.logger.info(f"Weekly report: querying by FilingDate for each day from {start_date} to {end_date}")
                
                # Convert dates to datetime objects
                start_dt = datetime.strptime(start_date, '%Y%m%d')
                end_dt = datetime.strptime(end_date, '%Y%m%d')
                
                # Query each day individually using async repository methods
                current_dt = start_dt
                while current_dt <= end_dt:
                    date_str = current_dt.strftime('%Y%m%d')
                    try:
                        daily_items = await self.pacer_repo.query_by_filing_date(date_str)
                        items.extend(daily_items)
                    except Exception as day_error:
                        self.logger.warning(f"Error querying FilingDate {date_str}: {day_error}")
                    current_dt += timedelta(days=1)
                
                self.logger.info(f"Weekly FilingDate query returned {len(items)} total items.")
            else:
                # For daily reports, use async repository method
                items = await self.pacer_repo.query_by_added_on_range(start_date, end_date)
                self.logger.info(f"Daily AddedOn query returned {len(items)} items.")
                
        except Exception as e:
            self.logger.error(f"Error querying Pacer repository for {date_range_str}: {e}", exc_info=True)
            items = []

        if not items:
            self.logger.warning(f"No docket items found for {date_range_str}.")
            # Define schema for empty DataFrame
            cols = ['court_id', 'docket_num', 'filing_date', 'law_firm', 'title', 'versus', 'num_plaintiffs', 's3_link',
                    'mdl_num', 'allegations', 'claims', 'is_removed', 'added_on', 'pending_cto', 'transferred_in',
                    'versus_sort']
            empty_df = pd.DataFrame(columns=cols)
            # Set correct dtypes for empty df
            for col in ['is_removed', 'pending_cto', 'transferred_in']: 
                empty_df[col] = empty_df[col].astype(bool)
            empty_df['num_plaintiffs'] = empty_df['num_plaintiffs'].astype(int)
            empty_df['s3_link'] = empty_df['s3_link'].astype(str)
            empty_df[['law_firm', 'title', 'versus', 'mdl_num', 'allegations', 'claims', 'versus_sort']] = empty_df[
                ['law_firm', 'title', 'versus', 'mdl_num', 'allegations', 'claims', 'versus_sort']].astype(str)

            setattr(self, cache_attr, empty_df.copy())
            return empty_df

        # Create DataFrame and process
        try:
            df = pd.DataFrame(items)
        except Exception as df_err:
            self.logger.error(f"CRITICAL ERROR creating DataFrame from DynamoDB items: {df_err}", exc_info=True)
            self.logger.error(f"First few items that caused error (if available): {items[:3] if items else 'No items'}")
            return pd.DataFrame()

        # Data processing (same as original but cleaner)
        df = await self._process_docket_dataframe(df)
        
        # Cache the processed dataframe
        setattr(self, cache_attr, df.copy())
        self.logger.info(f"Loaded and processed {len(df)} docket entries for {date_range_str}.")
        return df

    async def _process_docket_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """Process the docket dataframe with field mapping and cleaning."""
        # Delegate to sync method to avoid duplication
        return await asyncio.to_thread(self._process_docket_dataframe_sync, df)
    
    def _process_docket_dataframe_sync(self, df: pd.DataFrame) -> pd.DataFrame:
        """Synchronous dataframe processing to match original implementation."""
        # Field mapping from DynamoDB to expected format
        col_mapping = {
            'CourtId': 'court_id', 'DocketNum': 'docket_num', 'FilingDate': 'filing_date',
            'LawFirm': 'law_firm', 'Title': 'title', 'Versus': 'versus', 'NumPlaintiffs': 'num_plaintiffs',
            'S3Link': 's3_link', 'MdlNum': 'mdl_num', 'Allegations': 'allegations', 'Claims': 'claims',
            'IsRemoved': 'is_removed', 'AddedOn': 'added_on', 'PendingCto': 'pending_cto',
            'TransferredIn': 'transferred_in'
        }

        # Rename columns
        cols_to_rename = {k: v for k, v in col_mapping.items() if k in df.columns and k != v}
        if cols_to_rename:
            df.rename(columns=cols_to_rename, inplace=True)

        # Ensure all target columns exist
        for col_py in col_mapping.values():
            if col_py not in df.columns:
                self.logger.warning(f"Column '{col_py}' missing after rename. Setting default.")
                if col_py in ['is_removed', 'pending_cto', 'transferred_in']:
                    df[col_py] = False
                elif col_py == 'num_plaintiffs':
                    df[col_py] = 0
                elif col_py == 's3_link':
                    df[col_py] = ''
                else:
                    df[col_py] = None

        # Data cleaning and type conversion
        df['s3_link'] = df['s3_link'].fillna('').astype(str)
        null_strings_pattern = r'^(nan|none|null|na|<na>)$'
        df['s3_link'] = df['s3_link'].str.replace(null_strings_pattern, '', case=False, regex=True).str.strip()

        df['law_firm'] = df['law_firm'].fillna("Unknown Firm").astype(str)
        df['title'] = df['title'].fillna("Unknown Title").astype(str).str.strip()
        df['versus'] = df['versus'].fillna("Unknown Defendant").astype(str)
        df['versus_sort'] = df['versus'].str.lower()
        df['mdl_num'] = df['mdl_num'].fillna('').astype(str).str.strip().replace('nan', '', regex=False).replace('None', '', regex=False).replace('NA', '', regex=False)

        # Boolean column handling
        for col in ['is_removed', 'pending_cto', 'transferred_in']:
            df[col] = df[col].apply(
                lambda x: x if isinstance(x, bool) else (str(x).lower() in ['true', '1', 'yes', 't'] if pd.notna(x) else False)
            ).astype(bool)

        df['num_plaintiffs'] = pd.to_numeric(df['num_plaintiffs'], errors='coerce').fillna(0).astype(int)
        df['allegations'] = df['allegations'].fillna('').astype(str).apply(self._format_text)
        df['claims'] = df['claims'].fillna('').astype(str).apply(self._format_text)

        # Title normalization
        in_re_pattern = re.compile(r'^\s*in\s+re[:\s]+\s*', re.IGNORECASE)
        df['title'] = df['title'].apply(lambda x: in_re_pattern.sub('', x).strip())
        
        # Title enhancement using MDL lookup
        self.logger.info("Applying title enhancement with MDL lookup...")
        df = self._enhance_titles_with_mdl_lookup(df)
        self.logger.info("Finished title enhancement.")

        # Law firm processing using normalizer
        self.logger.info("Applying law firm name normalization...")
        
        def normalize_firm_field(name_str):
            """Handle both single firms and semicolon-separated firms"""
            if ';' in name_str:
                firms = [firm.strip() for firm in name_str.split(';')]
                normalized_firms = []
                for firm in firms:
                    if firm:
                        capitalized_firm = self.law_firm_handler.capitalize_law_firm_names(firm)
                        normalized_firm = normalize_law_firm_name(capitalized_firm)
                        normalized_firms.append(normalized_firm)
                return ' ; '.join(normalized_firms)
            else:
                capitalized_name = self.law_firm_handler.capitalize_law_firm_names(name_str)
                return normalize_law_firm_name(capitalized_name)
        
        df['law_firm'] = df['law_firm'].apply(normalize_firm_field)
        self.logger.info("Finished law firm name normalization.")

        return df

    async def load_ad_df_async(self) -> pd.DataFrame:
        """Loads the ad data asynchronously using the AdDataFrameProcessor."""
        self.logger.info("Loading Ad DataFrame asynchronously...")
        
        # Call the ad processor's async method directly
        ad_df = await self.ad_processor.load_ad_df_async()
        
        if ad_df is None or ad_df.empty:
            self.logger.warning("Ad DataFrame is empty or failed to load.")
            return pd.DataFrame()
        
        # Apply consistent law firm normalization
        if 'law_firm' in ad_df.columns:
            self.logger.info("Applying law firm name normalization to Ad DataFrame...")
            ad_df['law_firm'] = ad_df['law_firm'].fillna('Unknown Firm').astype(str)
            
            def normalize_firm_field(name_str):
                """Handle both single firms and semicolon-separated firms"""
                if ';' in name_str:
                    firms = [firm.strip() for firm in name_str.split(';')]
                    normalized_firms = []
                    for firm in firms:
                        if firm:
                            capitalized_firm = self.law_firm_handler.capitalize_law_firm_names(firm)
                            normalized_firm = normalize_law_firm_name(capitalized_firm)
                            normalized_firms.append(normalized_firm)
                    return ' ; '.join(normalized_firms)
                else:
                    capitalized_name = self.law_firm_handler.capitalize_law_firm_names(name_str)
                    return normalize_law_firm_name(capitalized_name)
            
            ad_df['law_firm'] = ad_df['law_firm'].apply(normalize_firm_field)
            self.logger.info("Finished law firm name normalization for Ad DataFrame.")
        else:
            self.logger.warning("Ad DataFrame missing 'law_firm' column for normalization.")

        return ad_df

    def load_docket_df(self, is_weekly: bool = False) -> pd.DataFrame:
        """
        SYNC method that matches original interface exactly.
        Loads PACER docket data for the specified period (daily or weekly).
        This is the method called by the original orchestrator via asyncio.to_thread.
        """
        cache_attr = '_cached_weekly_docket_df' if is_weekly else '_cached_docket_df'
        if getattr(self, cache_attr) is not None:
            self.logger.info(f"Using cached {'weekly' if is_weekly else 'daily'} docket data.")
            return getattr(self, cache_attr).copy()

        if not self.config.iso_date:
            self.logger.error("Cannot load dockets: iso_date is not set.")
            return pd.DataFrame()

        start_date = self.config.seven_days_ago if is_weekly else self.config.iso_date
        end_date = self.config.iso_date
        date_range_str = f"{start_date} to {end_date}" if is_weekly else self.config.iso_date

        if not start_date:
            self.logger.error(
                f"Cannot load dockets: Start date is missing for {'weekly' if is_weekly else 'daily'} range.")
            return pd.DataFrame()

        self.logger.info(f'Loading PACER dockets for {date_range_str}')
        items = []
        try:
            if is_weekly:
                # For weekly reports, query by FilingDate for each day in the range
                self.logger.info(f"Weekly report: querying by FilingDate for each day from {start_date} to {end_date}")
                from datetime import datetime, timedelta
                
                # Convert dates to datetime objects
                start_dt = datetime.strptime(start_date, '%Y%m%d')
                end_dt = datetime.strptime(end_date, '%Y%m%d')
                
                # Query each day individually by FilingDate
                current_dt = start_dt
                while current_dt <= end_dt:
                    date_str = current_dt.strftime('%Y%m%d')
                    try:
                        # SYNC method - create new async context per call
                        async def get_daily_items():
                            return await self.pacer_repo.query_by_filing_date(date_str)
                        daily_items = asyncio.run(get_daily_items())
                        items.extend(daily_items)
                    except Exception as day_error:
                        self.logger.warning(f"Error querying FilingDate {date_str}: {day_error}")
                    current_dt += timedelta(days=1)
                
                self.logger.info(f"Weekly FilingDate query returned {len(items)} total items.")
            else:
                # For daily reports, use sync wrapper
                items = self.pacer_repo.get_records_by_added_on(start_date, end_date)
                self.logger.info(f"Daily AddedOn query returned {len(items)} items.")
        except Exception as e:
            self.logger.error(f"Error querying Pacer repository for {date_range_str}: {e}", exc_info=True)
            items = []

        if not items:
            self.logger.warning(f"No docket items found for {date_range_str}.")
            # Define schema for empty DF
            cols = ['court_id', 'docket_num', 'filing_date', 'law_firm', 'title', 'versus', 'num_plaintiffs', 's3_link',
                    'mdl_num', 'allegations', 'claims', 'is_removed', 'added_on', 'pending_cto', 'transferred_in',
                    'versus_sort']
            empty_df = pd.DataFrame(columns=cols)
            # Set correct dtypes for empty df
            for col in ['is_removed', 'pending_cto', 'transferred_in']: 
                empty_df[col] = empty_df[col].astype(bool)
            empty_df['num_plaintiffs'] = empty_df['num_plaintiffs'].astype(int)
            empty_df['s3_link'] = empty_df['s3_link'].astype(str)
            empty_df[['law_firm', 'title', 'versus', 'mdl_num', 'allegations', 'claims', 'versus_sort']] = empty_df[
                ['law_firm', 'title', 'versus', 'mdl_num', 'allegations', 'claims', 'versus_sort']].astype(str)

            setattr(self, cache_attr, empty_df.copy())
            return empty_df

        # Create DataFrame and process (run sync processing in the current thread)
        try:
            df = pd.DataFrame(items)
        except Exception as df_err:
            self.logger.error(f"CRITICAL ERROR creating DataFrame from DynamoDB items: {df_err}", exc_info=True)
            self.logger.error(f"First few items that caused error (if available): {items[:3] if items else 'No items'}")
            return pd.DataFrame()

        # Process dataframe synchronously like original
        df = self._process_docket_dataframe_sync(df)
        
        # Cache the processed dataframe
        setattr(self, cache_attr, df.copy())
        self.logger.info(f"Loaded and processed {len(df)} docket entries for {date_range_str}.")
        return df

    def _get_fb_ads_cache_path(self) -> Optional[str]:
        """Get the path for FB ads cache file."""
        if not self.config.iso_date or not self.config.download_dir:
            return None
        return os.path.join(self.config.download_dir, 'reports', f'fb_ads_cache_{self.config.iso_date}.json')

    def _is_fb_ads_cache_valid(self, cache_path: str) -> bool:
        """Check if FB ads cache is valid based on TTL."""
        if not os.path.exists(cache_path):
            return False
        
        try:
            cache_stat = os.path.stat(cache_path)
            cache_age_hours = (datetime.now().timestamp() - cache_stat.st_mtime) / 3600
            return cache_age_hours < self.config.fb_ads_cache_ttl_hours
        except Exception as e:
            self.logger.error(f"Error checking cache validity: {e}")
            return False

    def _save_fb_ads_cache(self, df: pd.DataFrame) -> bool:
        """Save FB ads DataFrame to cache."""
        cache_path = self._get_fb_ads_cache_path()
        if not cache_path:
            return False
        
        try:
            # Ensure reports directory exists
            os.makedirs(os.path.dirname(cache_path), exist_ok=True)
            
            # Convert DataFrame to JSON with metadata
            cache_data = {
                'cached_at': datetime.now().isoformat(),
                'iso_date': self.config.iso_date,
                'data': df.to_dict('records')
            }
            
            with open(cache_path, 'w', encoding='utf-8') as f:
                json.dump(cache_data, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"Saved FB ads cache with {len(df)} records to {cache_path}")
            return True
        except Exception as e:
            self.logger.error(f"Error saving FB ads cache: {e}")
            return False

    def _load_fb_ads_cache(self) -> Optional[pd.DataFrame]:
        """Load FB ads DataFrame from cache if valid."""
        if not self.config.fb_ads_use_cache:
            self.logger.debug("FB ads cache disabled in config")
            return None
        
        cache_path = self._get_fb_ads_cache_path()
        if not cache_path or not self._is_fb_ads_cache_valid(cache_path):
            return None
        
        try:
            with open(cache_path, 'r', encoding='utf-8') as f:
                cache_data = json.load(f)
            
            # Validate cache data structure
            if 'data' not in cache_data or 'iso_date' not in cache_data:
                self.logger.warning("Invalid cache data structure, ignoring cache")
                return None
            
            # Validate date matches
            if cache_data['iso_date'] != self.config.iso_date:
                self.logger.warning(f"Cache date mismatch: {cache_data['iso_date']} != {self.config.iso_date}")
                return None
            
            df = pd.DataFrame(cache_data['data'])
            self.logger.info(f"Loaded FB ads cache with {len(df)} records from {cache_path}")
            return df
        except Exception as e:
            self.logger.error(f"Error loading FB ads cache: {e}")
            return None

    def load_ad_df(self) -> pd.DataFrame:
        """
        SYNC method that matches original interface exactly.
        Loads the ad data using the AdDataFrameProcessor.
        This is the method called by the original orchestrator via asyncio.to_thread.
        """
        self.logger.info("Loading Ad DataFrame...")
        
        # Check cache first if enabled and not forced to regenerate
        if self.config.fb_ads_use_cache and not self.config.fb_ads_force_regenerate:
            cached_df = self._load_fb_ads_cache()
            if cached_df is not None and not cached_df.empty:
                self.logger.info(f"Using cached FB ads data with {len(cached_df)} records")
                return cached_df
        
        # Load from database using ad processor (expensive query)
        self.logger.info("Loading FB ads from database (expensive query)...")
        ad_df = self.ad_processor.load_ad_df()  # Calls the sync method like original
        
        if ad_df is None or ad_df.empty:
            self.logger.warning("Ad DataFrame is empty or failed to load.")
            return pd.DataFrame()
        
        # Apply consistent law firm normalization here too
        if 'law_firm' in ad_df.columns:
            self.logger.info("Applying law firm name normalization to Ad DataFrame...")
            ad_df['law_firm'] = ad_df['law_firm'].fillna('Unknown Firm').astype(str)
            
            def normalize_firm_field(name_str):
                """Handle both single firms and semicolon-separated firms"""
                if ';' in name_str:
                    firms = [firm.strip() for firm in name_str.split(';')]
                    normalized_firms = []
                    for firm in firms:
                        if firm:
                            capitalized_firm = self.law_firm_handler.capitalize_law_firm_names(firm)
                            normalized_firm = normalize_law_firm_name(capitalized_firm)
                            normalized_firms.append(normalized_firm)
                    return ' ; '.join(normalized_firms)
                else:
                    capitalized_name = self.law_firm_handler.capitalize_law_firm_names(name_str)
                    return normalize_law_firm_name(capitalized_name)
            
            ad_df['law_firm'] = ad_df['law_firm'].apply(normalize_firm_field)
            self.logger.info("Finished law firm name normalization for Ad DataFrame.")
        else:
            self.logger.warning("Ad DataFrame missing 'law_firm' column for normalization.")

        # Save to cache if enabled and not empty
        if self.config.fb_ads_use_cache and not ad_df.empty:
            self._save_fb_ads_cache(ad_df)

        return ad_df