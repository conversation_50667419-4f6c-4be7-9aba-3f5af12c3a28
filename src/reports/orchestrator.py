"""
Clean async Reports Orchestrator without compatibility layers.
This is the refactored version that eliminates all migration helpers and feature flags.
"""
import logging
import os
from typing import Dict, Any, Optional
import asyncio
import pandas as pd

# Direct async repository imports - no compatibility layers
from src.infrastructure.storage.dynamodb_async import AsyncDynamoDBStorage
from src.repositories.fb_archive_repository import FBArchiveRepository
from src.repositories.pacer_repository import PacerRepository
from src.repositories.law_firms_repository import LawFirmsRepository

from src.utils.law_firm import LawFirmNameHandler
from src.utils.law_firm_normalizer import get_normalizer
from .ad_df_processor import AdDataFrameProcessor
from .ad_page_generator import AdPageGenerator
from .cache_invalidator import CacheInvalidator
from .config import ReportConfig
from .data_loader import DynamicDataLoader, StaticDataLoader
from .publisher import ReportPublisher
from .renderer import ReportRenderer
from .processor import ReportDataProcessor
from src.infrastructure.storage.s3_async import S3AsyncStorage


def load_config(date_str: str = None) -> Dict[str, Any]:
    """Load configuration - simplified without compatibility complexity"""
    return {
        'aws_region': os.environ.get('AWS_REGION', 'us-west-2'),
        'bucket_name': os.environ.get('S3_BUCKET_NAME', 'lexgenius-dockets'),
        'aws_access_key': os.environ.get('AWS_ACCESS_KEY'),
        'aws_secret_key': os.environ.get('AWS_SECRET_KEY'),
        'cloudfront_distribution_id': os.environ.get('CLOUDFRONT_DISTRIBUTION_ID'),
        'dynamodb': {
            'fb_ad_archive_table_name': 'FBAdArchive',
            'pacer_table_name': 'Pacer'
        }
    }


class ReportsOrchestrator:
    """
    Clean async Reports Orchestrator with direct repository usage.
    No compatibility layers, feature flags, or migration helpers.
    """

    def __init__(self, config_dict: Dict[str, Any]):
        self.logger = logging.getLogger(f"{__name__}.ReportsOrchestrator")
        self.config = ReportConfig(config_dict)
        
        # Create S3 manager in constructor like original
        aws_access_key = config_dict.get('aws_access_key')
        aws_secret_key = config_dict.get('aws_secret_key')
        
        if not self.config.bucket_name or not aws_access_key or not aws_secret_key:
            self.logger.warning(f"S3 credentials not configured. bucket_name={self.config.bucket_name}, aws_access_key={'SET' if aws_access_key else 'MISSING'}, aws_secret_key={'SET' if aws_secret_key else 'MISSING'}. S3 operations will be skipped.")
            self._s3_manager = None
        else:
            self.logger.info(f"Creating S3AsyncStorage with bucket={self.config.bucket_name}, region={config_dict.get('aws_region', 'us-west-2')}")
            self._s3_manager = S3AsyncStorage(
                bucket_name=self.config.bucket_name,
                aws_access_key=aws_access_key,
                aws_secret_key=aws_secret_key,
                aws_region=config_dict.get('aws_region', 'us-west-2')
            )
        
        # Create repositories directly like original manager pattern
        # Create storage for repositories
        self._storage = AsyncDynamoDBStorage({
            'aws_region': config_dict.get('aws_region', 'us-west-2'),
            'dynamodb_endpoint': None
        })
        
        # Create repositories - these will be used for queries
        self._pacer_repo = PacerRepository(self._storage)
        self._fb_archive_repo = FBArchiveRepository(self._storage)
        self._law_firms_repo = LawFirmsRepository(self._storage)
        
        # Keep backward compatibility aliases
        self.async_storage = self._storage
        self.pacer_manager = self._pacer_repo
        self.fb_ad_db_manager = self._fb_archive_repo
        self.s3_manager = self._s3_manager
        
        # Initialize other components
        self.law_firm_handler = LawFirmNameHandler()
        self.law_firm_normalizer = get_normalizer()
        
        # Initialize Core Components - use sync loader with original interface
        self.static_data_loader = StaticDataLoader()
        self.ad_processor = AdDataFrameProcessor(self.fb_ad_db_manager, self.logger, self.config.iso_date)
        
        # Create data loader with sync interface like original
        self.dynamic_data_loader = DynamicDataLoader(self.config, self._pacer_repo, self.ad_processor, self.law_firm_handler)
        
        self.data_processor = ReportDataProcessor(self.config, self._pacer_repo)
        self.renderer = ReportRenderer(self.config, self.static_data_loader, self.data_processor)
        self.ad_page_generator = AdPageGenerator(self.config, self.renderer, self._s3_manager)
        self.publisher = ReportPublisher(self.config, self._s3_manager)
        self.cache_invalidator = CacheInvalidator(self.config)

    async def __aenter__(self):
        """Async context manager entry."""
        if hasattr(self._storage, '__aenter__'):
            await self._storage.__aenter__()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if hasattr(self._storage, '__aexit__'):
            await self._storage.__aexit__(exc_type, exc_val, exc_tb)

    async def _load_and_process_data(self, is_weekly: bool) -> Dict[str, Any]:
        """Load and process all data needed for a report type asynchronously."""
        self.logger.warning(f"🔥 _LOAD_AND_PROCESS_DATA: Called for {'Weekly' if is_weekly else 'Daily'} Report")
        report_data = {}

        # Load Dynamic Data - Use async methods directly in new architecture
        try:
            self.logger.debug(f"Running dynamic_data_loader.load_docket_df_async(is_weekly={is_weekly})...")
            report_data['docket_df'] = await self.dynamic_data_loader.load_docket_df_async(is_weekly=is_weekly)
            self.logger.debug(f"Completed load_docket_df_async. Running dynamic_data_loader.load_ad_df_async()...")
            report_data['ad_df'] = await self.dynamic_data_loader.load_ad_df_async()
            self.logger.debug(f"Completed load_ad_df_async.")

        except Exception as load_err:
            self.logger.error(f"Error during threaded dynamic data loading: {load_err}", exc_info=True)
            if 'docket_df' not in report_data: report_data['docket_df'] = pd.DataFrame()
            if 'ad_df' not in report_data: report_data['ad_df'] = pd.DataFrame()

        # Calculate AFFF Stats BEFORE getting MDL Summary
        if self.config.show_afff_stats:
            report_data['afff_stats'] = await self.data_processor.calculate_afff_stats_async(report_data['docket_df'])
        else:
            report_data['afff_stats'] = None # Ensure it's None if not calculated

        # Static data loading remains synchronous
        if self.config.show_upcoming_hearings: 
            report_data['upcoming_hearings'] = self.static_data_loader.load_upcoming_hearings()
        if self.config.show_special_reports: 
            report_data['special_reports'] = self.static_data_loader.load_special_reports()
        if self.config.show_news: 
            report_data['news_items'] = self.static_data_loader.load_news()
        if self.config.show_announcements: 
            report_data['announcements'] = self.static_data_loader.load_announcements()
        
        report_data['general_sponsorships'] = self.static_data_loader.load_general_sponsorships()

        # Processing is fully async
        
        # Log docket_df sample before processing
        self.logger.warning(f"🔥 DOCKET_DF CHECK: docket_df in report_data: {'docket_df' in report_data}")
        if 'docket_df' in report_data:
            self.logger.warning(f"🔥 DOCKET_DF CHECK: docket_df type: {type(report_data['docket_df'])}")
            if hasattr(report_data['docket_df'], 'empty'):
                self.logger.warning(f"🔥 DOCKET_DF CHECK: docket_df.empty: {report_data['docket_df'].empty}")
            
        if 'docket_df' in report_data and not report_data['docket_df'].empty:
            self.logger.warning(f"🔥 DOCKET_DF SAMPLE - Shape: {report_data['docket_df'].shape}")
            self.logger.warning(f"🔥 DOCKET_DF SAMPLE - Columns: {list(report_data['docket_df'].columns)}")
            self.logger.warning(f"🔥 DOCKET_DF SAMPLE - First 2 rows:\n{report_data['docket_df'].head(2).to_string()}")
        else:
            self.logger.warning("🔥 DOCKET_DF is empty or missing!")
            
        if self.config.show_litigation:
            self.logger.warning("🔥 PROCESSING: Starting group_filings_by_litigation_async...")
            report_data['grouped_litigation'], report_data['title_totals'] = await self.data_processor.group_filings_by_litigation_async(report_data['docket_df'])
            self.logger.warning(f"🔥 GROUPED_LITIGATION RESULT - Keys: {list(report_data['grouped_litigation'].keys())}")
            self.logger.warning(f"🔥 TITLE_TOTALS RESULT - Shape: {report_data['title_totals'].shape if hasattr(report_data['title_totals'], 'shape') else 'Not DataFrame'}")
            if report_data['grouped_litigation']:
                sample_key = list(report_data['grouped_litigation'].keys())[0]
                self.logger.warning(f"🔥 GROUPED_LITIGATION SAMPLE - First key '{sample_key}': {report_data['grouped_litigation'][sample_key]}")
        else:
            report_data['grouped_litigation'], report_data['title_totals'] = {}, pd.DataFrame()
            self.logger.warning("🔥 LITIGATION: Disabled in config, using empty data")

        if self.config.show_filings:
            self.logger.info("PROCESSING: Starting group_filings_by_firm_async...")
            report_data['detailed_filings'] = await self.data_processor.group_filings_by_firm_async(report_data['docket_df'])
            self.logger.info(f"DETAILED_FILINGS RESULT - Keys: {list(report_data['detailed_filings'].keys())}")
            if report_data['detailed_filings']:
                sample_firm = list(report_data['detailed_filings'].keys())[0]
                self.logger.info(f"DETAILED_FILINGS SAMPLE - First firm '{sample_firm}': {list(report_data['detailed_filings'][sample_firm].keys())}")
        else:
            report_data['detailed_filings'] = {}
            self.logger.info("DETAILED_FILINGS: Disabled in config, using empty data")

        if self.config.show_adspy:
            report_data['grouped_ads'] = await self.data_processor.filter_ad_summaries_async(report_data['ad_df'])
        else:
            report_data['grouped_ads'] = {}

        # Get MDL Summary Data AFTER AFFF stats are calculated and PASS stats
        if self.config.show_summary:
            # Pass the calculated afff_stats directly
            chart_labels, chart_data = await self.data_processor.get_mdl_summary_data_async(
                afff_stats=report_data.get('afff_stats') # Pass the potentially None stats dict
            )
            report_data['chart_labels'], report_data['chart_data'] = chart_labels, chart_data
        else:
            report_data['chart_labels'], report_data['chart_data'] = [], []

        self.logger.info(f"--- Finished Loading and Processing Data ---")
        # Log the labels generated by the processor to confirm they are correct here
        self.logger.debug(f"Labels generated by processor: {report_data.get('chart_labels')}")
        return report_data

    async def generate_single_report(self, report_data: Dict[str, Any], is_web: bool, is_weekly: bool) -> bool:
        """Generate and publish a single report version."""
        report_type = "weekly" if is_weekly else "daily"
        target_format = "web" if is_web else "email"
        self.logger.info(f"--- Generating {report_type} {target_format} report ---")
        generation_successful = False
        try:
            # Rendering is synchronous
            if is_web:
                html_content = self.renderer.render_report(report_data, is_web=True, is_weekly=is_weekly)
            else:
                html_content = self.renderer.render_email_report(report_data, is_weekly=is_weekly)

            if "<h1>Error Generating Report</h1>" in html_content:
                self.logger.error(f"Detected rendering error in {report_type} {target_format} report output.")
                return False

            # Publishing involves IO, await the async version
            generation_successful = await self.publisher.publish_report_async(html_content, is_web, is_weekly) # Use async publisher method
            if not generation_successful:
                self.logger.error(f"Failed to publish {report_type} {target_format} report.")

        except Exception as e:
            self.logger.critical(f"FATAL Error during {report_type} {target_format} report generation/publication: {e}",
                                 exc_info=True)
            generation_successful = False

        return generation_successful

    async def run(self, skip_ads: bool = False, skip_invalidate: bool = False):
        """Execute the full report generation workflow asynchronously."""
        self.logger.warning(f"🔥 ORCHESTRATOR: run() method called!")
        self.logger.warning(f"🔥 ORCHESTRATOR: Starting report orchestration for date: {self.config.iso_date}")
        self.logger.warning(f"🔥 ORCHESTRATOR: Received run parameters: skip_ads={skip_ads}, skip_invalidate={skip_invalidate}")
        overall_success = True

        # Initialize S3 client if available like original
        if self._s3_manager:
            try:
                await self._s3_manager._initialize()
                self.logger.info("S3 client initialized successfully")
            except Exception as e:
                self.logger.error(f"Failed to initialize S3 client: {e}")
                self._s3_manager = None
                self.s3_manager = None

        # Initialize async storage like original  
        try:
            await self._storage.__aenter__()
            self.logger.info("Async storage initialized successfully")
        except Exception as e:
            self.logger.error(f"Failed to initialize async storage: {e}")
            return False # Cannot proceed

        try:
            # Await async data loading
            daily_report_data = await self._load_and_process_data(is_weekly=False)
        except Exception as data_err:
            self.logger.critical(f"Failed to load/process initial daily data. Aborting. Error: {data_err}",
                                 exc_info=True)
            return False # Cannot proceed

        # Await async report generation
        daily_web_ok = await self.generate_single_report(daily_report_data, is_web=True, is_weekly=False)
        daily_email_ok = await self.generate_single_report(daily_report_data, is_web=False, is_weekly=False)
        overall_success &= daily_web_ok and daily_email_ok

        weekly_web_ok, weekly_email_ok = True, True
        if self.config.is_sunday:
            self.logger.info("--- Processing Weekly Report ---")
            try:
                # Await async data loading
                weekly_report_data = await self._load_and_process_data(is_weekly=True)
                # Await async report generation
                weekly_web_ok = await self.generate_single_report(weekly_report_data, is_web=True, is_weekly=True)
                weekly_email_ok = await self.generate_single_report(weekly_report_data, is_web=False, is_weekly=True)
                overall_success &= weekly_web_ok and weekly_email_ok
            except Exception as weekly_err:
                self.logger.critical(f"Failed during weekly report processing/generation: {weekly_err}", exc_info=True)
                overall_success = False
        else:
            self.logger.info("--- Skipping Weekly Reports (Not Sunday) ---")

        # Ad page generation - check config flags and skip_ads parameter
        if self.config.show_adspy and not skip_ads and self.config.fb_ads_generate_pages:
            self.logger.info("--- Generating Ad HTML Pages ---")
            try:
                ad_df_to_use = daily_report_data.get('ad_df')
                if ad_df_to_use is not None and not ad_df_to_use.empty:
                    # Generate ad pages (and optionally upload based on config)
                    if self.config.fb_ads_upload_pages:
                        self.logger.info("Generating and uploading ad pages to S3")
                        await self.ad_page_generator.generate_and_upload_ad_pages(ad_df_to_use)
                    else:
                        self.logger.info("Generating ad pages locally only (upload disabled)")
                        await self.ad_page_generator.generate_ad_pages_only(ad_df_to_use)
                    
                    # Validate that ad pages were successfully uploaded (only if uploading)
                    if self.config.fb_ads_upload_pages:
                        self.logger.info("--- Validating Ad Page Uploads ---")
                        ad_ids = ad_df_to_use['ad_archive_id'].tolist() if 'ad_archive_id' in ad_df_to_use.columns else []
                        if ad_ids:
                            validation_results = await self.ad_page_generator.validate_ad_pages(ad_ids)
                            
                            # Filter out ads with missing pages from the report data
                            missing_ads = [ad_id for ad_id, exists in validation_results.items() if not exists]
                            if missing_ads:
                                self.logger.warning(f"Filtering {len(missing_ads)} ads with missing pages from report")
                                # Update the ad dataframe in daily_report_data to exclude missing ads
                                daily_report_data['ad_df'] = ad_df_to_use[~ad_df_to_use['ad_archive_id'].isin(missing_ads)]
                                
                                # Also update the filtered ads if they exist
                                if 'grouped_ads' in daily_report_data:
                                    for law_firm, firm_df in daily_report_data['grouped_ads'].items():
                                        filtered_firm_df = firm_df[~firm_df['ad_archive_id'].isin(missing_ads)]
                                        if not filtered_firm_df.empty:
                                            daily_report_data['grouped_ads'][law_firm] = filtered_firm_df
                                        else:
                                            # Remove law firm if all ads are missing
                                            del daily_report_data['grouped_ads'][law_firm]
                    else:
                        self.logger.info("Skipping ad page validation (upload disabled)")
                else:
                    self.logger.warning("Skipping ad page generation as Ad DataFrame is empty.")
            except Exception as e:
                self.logger.error(f"ERROR during ad page generation: {e}", exc_info=True)
        elif skip_ads:
            self.logger.info("--- Skipping Ad Page Generation (skip_ads flag passed to run method) ---")
        elif not self.config.fb_ads_generate_pages:
            self.logger.info("--- Skipping Ad Page Generation (fb_ads.generate_ad_pages disabled in config) ---")
        else:
            self.logger.info("--- Skipping Ad Page Generation (AdSpy section disabled in config) ---")

        # Cache invalidation - check if invalidate_cache method is async
        if overall_success and not skip_invalidate:
            self.logger.info("--- Invalidating CloudFront Cache ---")
            try:
                # Assume invalidate_cache becomes async if CloudFrontInvalidator uses async calls
                 await self.cache_invalidator.invalidate_cache()
            except Exception as e:
                self.logger.error(f"ERROR during CloudFront invalidation: {e}", exc_info=True)
        elif skip_invalidate:
            self.logger.info("--- Skipping CloudFront Invalidation (skip_invalidate flag passed to run method) ---")
        else:
            self.logger.warning("--- Skipping CloudFront Invalidation due to prior report generation errors ---")

        # Clean up S3 client if initialized like original
        if self._s3_manager and self._s3_manager._client:
            try:
                await self._s3_manager.close()
                self.logger.info("S3 client closed successfully")
            except Exception as e:
                self.logger.error(f"Error closing S3 client: {e}")

        # Clean up async storage
        try:
            await self._storage.__aexit__(None, None, None)
            self.logger.info("Async storage closed successfully")
        except Exception as e:
            self.logger.error(f"Error closing async storage: {e}")

        if overall_success:
            self.logger.info(f"Report orchestration finished successfully for {self.config.iso_date}.")
        else:
            self.logger.error(f"Report orchestration completed with errors for {self.config.iso_date}.")
        
        return overall_success


# Main execution block like original
if __name__ == "__main__":
    log_level = os.environ.get('LOG_LEVEL', 'INFO').upper()
    logging.basicConfig(level=log_level,
                        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                        datefmt='%Y-%m-%d %H:%M:%S')

    main_logger = logging.getLogger(__name__)
    main_logger.info("=" * 50)
    main_logger.info("Report Generator Script Starting")
    main_logger.info("=" * 50)

    try:
        report_date_arg = None  # Let load_config handle default or env var
        main_logger.info("Loading configuration...")
        # Use the external config loader
        config_data = load_config(date_str=report_date_arg)

        # Apply environment variable overrides for flags
        skip_ads_flag = os.environ.get('SKIP_ADS', 'false').lower() == 'true'
        skip_invalidate_flag = os.environ.get('SKIP_INVALIDATE', 'false').lower() == 'true'
        config_data['cloudfront_distribution_id'] = os.environ.get('CLOUDFRONT_DISTRIBUTION_ID',
                                                                   config_data.get('cloudfront_distribution_id'))

        main_logger.info(f"Skip Ads: {skip_ads_flag}, Skip Invalidate: {skip_invalidate_flag}")
        main_logger.info(f"Report Date from Config: {config_data.get('date')}")

        # Initialize and run the orchestrator
        orchestrator = ReportsOrchestrator(config_data)
        main_logger.info(f"Report Orchestrator initialized for date: {orchestrator.config.iso_date}")

        # Log effective section visibility
        active_ids = [s['id'] for s in orchestrator.renderer._define_section_config() if
                      getattr(orchestrator.config, s['show_flag_attr'], False)]
        main_logger.info(f"Effective Sections configured to show: {active_ids}")

        asyncio.run(orchestrator.run(skip_ads=skip_ads_flag, skip_invalidate=skip_invalidate_flag))

        main_logger.info("Report generation script finished.")

    except FileNotFoundError as e:
        main_logger.critical(f"ABORTED: Configuration or essential file not found: {e}", exc_info=True)
    except ValueError as e:
        main_logger.critical(f"ABORTED: Configuration or date value error: {e}", exc_info=True)
    except ImportError as e:
        main_logger.critical(f"ABORTED: Missing required library: {e}. Please install dependencies.", exc_info=True)
    except Exception as e:
        main_logger.critical(f"ABORTED: An unexpected error occurred during setup or execution: {e}", exc_info=True)