"""
Clean Report Data Processor without migration helpers or compatibility layers.
"""
import json
import logging
import os
import re
from datetime import datetime
from typing import Any, Dict, List, Tuple, Optional
import asyncio

import pandas as pd

# Direct repository imports - no compatibility layers
from src.repositories.pacer_repository import PacerRepository
from .config import ReportConfig


class ReportDataProcessor:
    """
    Clean async Report Data Processor with direct repository usage.
    No migration helpers, feature flags, or compatibility layers.
    """

    def __init__(self, report_config: ReportConfig, pacer_repository: PacerRepository):
        self.config = report_config
        self.pacer_repo = pacer_repository  # Direct repository, no manager wrapper
        self.logger = logging.getLogger(f"{__name__}.ReportDataProcessor")
        
        # Use the correct path to MDL lookup file - now in src/config/mdl/
        self.mdl_lookup_path = os.path.abspath(os.path.join(
            os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'src', 'config', 'mdl', 'mdl_lookup.json'
        ))
        if not os.path.exists(self.mdl_lookup_path):
            self.logger.warning(f"MDL lookup file not found at {self.mdl_lookup_path}. Summaries may be limited.")
            self.mdl_lookup_df = pd.DataFrame()
        else:
            try:
                self.mdl_lookup_df = pd.read_json(self.mdl_lookup_path)
                if 'mdl_num' in self.mdl_lookup_df.columns:
                    self.mdl_lookup_df['mdl_num'] = self.mdl_lookup_df['mdl_num'].astype(
                        str).str.strip().str.removesuffix('.0')
                else:
                    self.logger.warning("MDL lookup file missing 'mdl_num' column.")
            except Exception as e:
                self.logger.error(f"Failed to load MDL lookup file: {e}", exc_info=True)
                self.mdl_lookup_df = pd.DataFrame()

        # Path to AFFF stats file - now in src/config/reports/
        self.afff_stats_path = os.path.abspath(os.path.join(
            os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'src', 'config', 'reports', 'afff_stats.json'
        ))

    @staticmethod
    def _format_text(value: Any) -> str:
        """Helper to clean text fields - matches original implementation."""
        if not isinstance(value, str):
            return ''
        value = value.strip()
        value = re.sub(r'\r\n', '\n', value)
        value = re.sub(r'\n{3,}', '\n\n', value)
        return value

    async def calculate_afff_stats_async(self, docket_df: pd.DataFrame) -> Optional[Dict[str, Any]]:
        """Calculate AFFF statistics asynchronously - async wrapper of original logic."""
        try:
            return await asyncio.to_thread(self._calculate_afff_stats_sync, docket_df)
        except Exception as e:
            self.logger.error(f"Error calculating AFFF stats: {e}", exc_info=True)
            return None

    def _calculate_afff_stats_sync(self, docket_df: pd.DataFrame) -> Dict[str, Any]:
        """Calculates AFFF specific statistics - EXACT COPY of original logic."""
        self.logger.info("Calculating AFFF stats...")
        
        # Load stats from JSON file first
        if os.path.exists(self.afff_stats_path):
            try:
                with open(self.afff_stats_path) as f:
                    return json.load(f)
            except Exception as e:
                self.logger.error(f"Failed to load AFFF stats file: {e}", exc_info=True)

        # Fallback to default values if file doesn't exist or fails to load
        default_stats = {'pacer_cases_period': 0, 'pacer_plaintiffs_period': 0, 'transferred_in_period': 0,
                         'transferred_in_plaintiffs_period': 0, 'direct_filings_placeholder': 28,
                         'direct_filings_30_days_placeholder': 610, 'total_plaintiffs_30_days_placeholder': 840,
                         'filings_ytd_placeholder': 2559}

        # Check if docket_df is empty after loading defaults
        if docket_df is None or docket_df.empty:
            self.logger.warning("Cannot calculate AFFF stats: Docket DF is empty.")
            return default_stats

        required_cols = ['mdl_num', 'pending_cto', 'transferred_in', 'num_plaintiffs']
        if not all(col in docket_df.columns for col in required_cols):
            missing = [col for col in required_cols if col not in docket_df.columns]
            self.logger.warning(f"Cannot calculate AFFF stats: Docket DF missing columns {missing}.")
            return default_stats

        try:
            afff_df = docket_df[docket_df['mdl_num'] == '2873'].copy()
            self.logger.info(f"Found {len(afff_df)} AFFF (MDL 2873) records.")

            # Ensure correct types (original logic)
            afff_df['pending_cto'] = afff_df['pending_cto'].astype(bool)
            afff_df['transferred_in'] = afff_df['transferred_in'].astype(bool)
            afff_df['num_plaintiffs'] = pd.to_numeric(afff_df['num_plaintiffs'], errors='coerce').fillna(0).astype(int)

            pending_mask = afff_df['pending_cto'] & ~afff_df['transferred_in']
            transferred_mask = afff_df['transferred_in']

            calculated_pending_cases = afff_df[pending_mask].shape[0]
            calculated_pending_plaintiffs = int(afff_df.loc[pending_mask, 'num_plaintiffs'].sum())
            calculated_transferred_cases = afff_df[transferred_mask].shape[0]
            calculated_transferred_plaintiffs = int(afff_df.loc[transferred_mask, 'num_plaintiffs'].sum())

            stats = {
                'pacer_cases_period': calculated_pending_cases,
                'pacer_plaintiffs_period': calculated_pending_plaintiffs,
                'transferred_in_period': calculated_transferred_cases,
                'transferred_in_plaintiffs_period': calculated_transferred_plaintiffs,
                # Pass through the placeholders calculated earlier or use defaults
                'direct_filings_placeholder': default_stats['direct_filings_placeholder'],
                'direct_filings_30_days_placeholder': default_stats['direct_filings_30_days_placeholder'],
                'total_plaintiffs_30_days_placeholder': default_stats['total_plaintiffs_30_days_placeholder'],
                'filings_ytd_placeholder': default_stats['filings_ytd_placeholder']
            }
            self.logger.info(f"Calculated AFFF Stats: {stats}")
            return stats
        except Exception as e:
            self.logger.error(f"Error during AFFF stats calculation: {e}", exc_info=True)
            return default_stats  # Return defaults on error

    async def group_filings_by_litigation_async(self, docket_df: pd.DataFrame) -> Tuple[Dict[str, Dict[str, Any]], pd.DataFrame]:
        """Groups docket data by normalized title for the litigation report - async wrapper of original logic."""
        try:
            return await asyncio.to_thread(self._group_filings_by_litigation_sync, docket_df)
        except Exception as e:
            self.logger.error(f"Error grouping filings by litigation: {e}", exc_info=True)
            return {}, pd.DataFrame()

    def _group_filings_by_litigation_sync(self, docket_df: pd.DataFrame) -> Tuple[Dict[str, Dict[str, Any]], pd.DataFrame]:
        """Groups docket data by normalized title for the litigation report - EXACT COPY of original logic."""
        if docket_df is None or docket_df.empty:
            self.logger.warning("Cannot group titles: Input Docket DF is empty or None.")
            return {}, pd.DataFrame(columns=['title', 'Total Filings', 'mdl_num'])

        required_cols = ['title', 'law_firm', 'num_plaintiffs', 'pending_cto', 'transferred_in', 's3_link', 'versus',
                         'mdl_num']
        if not all(col in docket_df.columns for col in required_cols):
            missing = [col for col in required_cols if col not in docket_df.columns]
            self.logger.error(f"Input DataFrame to group_filings_by_litigation is missing columns: {missing}.")
            return {}, pd.DataFrame(columns=['title', 'Total Filings', 'mdl_num'])

        self.logger.info(f'Grouping {len(docket_df)} docket entries by title and law firm.')
        df_copy = docket_df.copy()

        # --- Ensure types and calculate filing weight (original logic) ---
        df_copy['mdl_num'] = df_copy['mdl_num'].fillna('').astype(str).str.strip().replace('nan', '',
                                                                                           regex=False).replace('None',
                                                                                                                '',
                                                                                                                regex=False).replace(
            'NA', '', regex=False)
        df_copy['title'] = df_copy['title'].astype(str)
        df_copy['law_firm'] = df_copy['law_firm'].fillna('Unknown Firm').astype(str)
        df_copy['num_plaintiffs'] = pd.to_numeric(df_copy['num_plaintiffs'], errors='coerce').fillna(0).astype(int)
        df_copy['pending_cto'] = df_copy['pending_cto'].astype(bool)
        df_copy['transferred_in'] = df_copy['transferred_in'].astype(bool)
        df_copy['s3_link'] = df_copy['s3_link'].fillna('').astype(str)
        if 'versus_sort' not in df_copy.columns:  # Should exist from loader
            df_copy['versus_sort'] = df_copy['versus'].fillna('Unknown Defendant').astype(str).str.lower()

        df_copy['filing_weight'] = 1
        condition = (df_copy['mdl_num'].isin(['2873', '3092']) & (df_copy['num_plaintiffs'] > 1))
        df_copy.loc[condition, 'filing_weight'] = df_copy.loc[condition, 'num_plaintiffs']

        # --- End Weight Calc ---

        # --- Aggregation Logic (original) ---
        def get_first_valid_mdl(series):
            first_valid = series.astype(str).str.strip().replace('', pd.NA).dropna().iloc[0] if not series.astype(
                str).str.strip().replace('', pd.NA).dropna().empty else ''
            return first_valid.removesuffix('.0')

        def aggregate_links(series):
            return list(series.astype(str).replace('', pd.NA).dropna())

        try:
            grouped_by_firm = df_copy.groupby(['title', 'law_firm'], as_index=False).agg(
                mdl_num=('mdl_num', get_first_valid_mdl),
                Filings=('filing_weight', 'sum')
            )
        except Exception as e:
            self.logger.error(f"Error during initial groupby aggregation: {e}", exc_info=True)
            return {}, pd.DataFrame(columns=['title', 'Total Filings', 'mdl_num'])

        title_totals_agg = grouped_by_firm.groupby('title', as_index=False)['Filings'].sum().rename(
            columns={'Filings': 'Total Filings'})
        title_mdl_map = grouped_by_firm[['title', 'mdl_num']].drop_duplicates('title').set_index('title')
        title_totals = title_totals_agg.set_index('title').join(title_mdl_map).reset_index()
        title_totals['mdl_num'] = title_totals['mdl_num'].fillna('').astype(str)
        title_totals = title_totals.sort_values(by=['Total Filings', 'title'], ascending=[False, True])
        # --- End Aggregation ---

        # --- Pre-process for Jinja (original logic) ---
        grouped_litigation_processed = {}
        for title in title_totals['title']:
            firm_data_df = grouped_by_firm[grouped_by_firm['title'] == title].sort_values(by='Filings',
                                                                                          ascending=False).reset_index(
                drop=True)
            title_mdl_num = firm_data_df['mdl_num'].iloc[0] if not firm_data_df.empty else ''
            firm_records = firm_data_df[['law_firm', 'Filings']].to_dict('records')
            grouped_litigation_processed[title] = {
                'firm_records': firm_records,
                'mdl_num': title_mdl_num,
                'total_filings': int(firm_data_df['Filings'].sum())
            }
        # --- End Pre-processing ---

        self.logger.info(f"Finished grouping litigation data. Found {len(title_totals)} unique titles.")
        return grouped_litigation_processed, title_totals

    async def group_filings_by_firm_async(self, docket_df: pd.DataFrame) -> Dict[str, Dict[str, List[Dict[str, Any]]]]:
        """Group filings by law firm asynchronously."""
        try:
            return await self._group_filings_by_firm_pure_async(docket_df)
        except Exception as e:
            self.logger.error(f"Error grouping filings by firm: {e}", exc_info=True)
            return {}

    async def _group_filings_by_firm_pure_async(self, docket_df: pd.DataFrame) -> Dict[str, Dict[str, List[Dict[str, Any]]]]:
        """Pure async firm grouping - matches original nested structure."""
        detailed_filings_grouped = {}
        
        if docket_df is None or docket_df.empty:
            self.logger.warning("Cannot group detailed filings: Input Docket DF is empty.")
            return detailed_filings_grouped
        
        if 'law_firm' not in docket_df.columns or 'title' not in docket_df.columns:
            self.logger.warning("Docket DF missing 'law_firm' or 'title', cannot pre-process detailed filings.")
            return detailed_filings_grouped

        self.logger.info("Pre-processing detailed filings data...")
        try:
            # Ensure required columns for output exist, add if missing (copied from original)
            required_output_cols = ['versus', 's3_link', 'mdl_num', 'num_plaintiffs', 'transferred_in', 'pending_cto']
            for col in required_output_cols:
                if col not in docket_df.columns:
                    docket_df[col] = '' if col in ['versus', 's3_link', 'mdl_num'] else (
                        0 if col == 'num_plaintiffs' else False)

            # Sort and group (original logic)
            sort_cols = ['law_firm', 'title', 'versus_sort']
            if 'versus_sort' not in docket_df.columns:
                docket_df['versus_sort'] = docket_df['versus'].fillna('').astype(str).str.lower()

            sorted_df = docket_df.sort_values(by=sort_cols)
            grouped_by_firm = sorted_df.groupby('law_firm')

            for law_firm, firm_group in grouped_by_firm:
                firm_data = {}
                grouped_by_title = firm_group.groupby('title')
                for title, title_group in grouped_by_title:
                    # Select only needed columns and convert to dict
                    case_records = title_group[required_output_cols].to_dict('records')
                    firm_data[title] = case_records
                detailed_filings_grouped[law_firm] = firm_data
            self.logger.info(f"Successfully pre-processed detailed filings for {len(detailed_filings_grouped)} firms.")
        except Exception as group_err:
            self.logger.error(f"Error pre-processing detailed filings data: {group_err}", exc_info=True)
            detailed_filings_grouped = {}  # Return empty on error

        return detailed_filings_grouped

    async def filter_ad_summaries_async(self, ad_df: pd.DataFrame) -> Dict[str, pd.DataFrame]:
        """Filter and group ad summaries asynchronously - async wrapper of original logic."""
        try:
            return await asyncio.to_thread(self._filter_ad_summaries_sync, ad_df)
        except Exception as e:
            self.logger.error(f"Error filtering ad summaries: {e}", exc_info=True)
            return {}

    def _filter_ad_summaries_sync(self, ad_df: pd.DataFrame) -> Dict[str, pd.DataFrame]:
        """Filters ad summaries within each law firm group to reduce redundancy - EXACT COPY of original logic."""
        grouped_ads_filtered_dict = {}
        if ad_df is None or ad_df.empty or 'law_firm' not in ad_df.columns:
            self.logger.warning("Cannot filter ad summaries: Ad DF is empty or missing 'law_firm'.")
            return grouped_ads_filtered_dict

        grouped_ad_data = ad_df.groupby('law_firm')
        self.logger.info(f"Filtering ad summaries for {len(grouped_ad_data.groups)} law firm groups.")

        for law_firm, group_df in grouped_ad_data:
            self.logger.debug(f"Filtering ads for: {law_firm} ({len(group_df)} ads initially)")
            if group_df.empty or 'summary' not in group_df.columns:
                self.logger.debug(f"Skipping empty or invalid group for {law_firm}")
                continue

            # --- Filtering Logic ---
            group_df_copy = group_df.copy()
            group_df_copy['summary'] = group_df_copy['summary'].fillna('').astype(str)
            group_df_copy = group_df_copy[group_df_copy['summary'].str.strip() != '']
            if group_df_copy.empty:
                continue

            ride_share_keywords = {'uber', 'lyft'}
            try:
                group_df_copy['first_word_lower'] = group_df_copy['summary'].str.split(n=1).str[0].str.lower()
                is_ride_share = group_df_copy['first_word_lower'].isin(ride_share_keywords)
            except Exception as e:
                self.logger.warning(f"Error extracting first word for filtering in group {law_firm}: {e}.")
                is_ride_share = pd.Series([False] * len(group_df_copy), index=group_df_copy.index)

            ride_share_rows = group_df_copy[is_ride_share]
            non_ride_share_rows = group_df_copy[~is_ride_share]

            unique_ride_share = ride_share_rows.drop_duplicates(subset=['summary'], keep='first')

            if not non_ride_share_rows.empty:
                unique_non_ride_share = non_ride_share_rows.drop_duplicates(subset=['first_word_lower'], keep='first')
            else:
                # Ensure unique_non_ride_share has the same columns as group_df_copy if it's empty
                unique_non_ride_share = pd.DataFrame(columns=group_df_copy.columns)

            dfs_to_concat = []
            if not unique_ride_share.empty:
                dfs_to_concat.append(unique_ride_share)
            if not unique_non_ride_share.empty:
                dfs_to_concat.append(unique_non_ride_share)

            if dfs_to_concat:
                filtered_group = pd.concat(dfs_to_concat, ignore_index=True)
            else:
                # If both unique_ride_share and unique_non_ride_share are empty (0 rows),
                # create an empty DataFrame with the columns from group_df_copy (which includes 'first_word_lower').
                filtered_group = pd.DataFrame(columns=group_df_copy.columns)

            if 'first_word_lower' in filtered_group.columns:
                filtered_group = filtered_group.drop(columns=['first_word_lower'])
            # --- End Filtering Logic ---

            if not filtered_group.empty:
                grouped_ads_filtered_dict[law_firm] = filtered_group
                self.logger.debug(f"Kept {len(filtered_group)} ads for {law_firm} after filtering.")

        num_filtered_ads = sum(len(df) for df in grouped_ads_filtered_dict.values())
        self.logger.info(
            f"Finished filtering ads. {num_filtered_ads} ads remaining across {len(grouped_ads_filtered_dict)} firms.")
        return grouped_ads_filtered_dict

    def get_allegations_and_causes(self, title: str, mdl_num: str, docket_df: pd.DataFrame) -> str:
        """
        Retrieves a summary/description for a litigation, prioritizing MDL lookup, then docket.
        This is called by the Jinja template for litigation report content.
        """
        if not isinstance(title, str) or not title:
            return ""
        
        default_summary = "No specific allegation summary available."
        mdl_num_str = str(mdl_num).strip().removesuffix('.0')

        # 1. Try MDL Lookup File (same logic as original)
        if (mdl_num_str and mdl_num_str not in ['NA', 'None', 'nan', ''] and 
            not self.mdl_lookup_df.empty and 'mdl_num' in self.mdl_lookup_df.columns):
            try:
                match = self.mdl_lookup_df[self.mdl_lookup_df['mdl_num'].str.fullmatch(mdl_num_str, case=False)]
                if not match.empty:
                    # Priority: short_summary → description
                    summary_col = 'short_summary' if 'short_summary' in match.columns else (
                        'description' if 'description' in match.columns else None)
                    if summary_col and pd.notna(match[summary_col].iloc[0]):
                        summary = str(match[summary_col].iloc[0]).strip()
                        if summary:
                            self.logger.debug(f"Found summary in MDL lookup for MDL {mdl_num_str}.")
                            return self._format_text(summary)
                    self.logger.debug(f"MDL {mdl_num_str} found in lookup but lacks summary.")
            except Exception as e:
                self.logger.error(f"Error searching MDL lookup data for {mdl_num_str}: {e}", exc_info=True)

        # 2. Fallback to provided Docket DataFrame
        self.logger.debug(
            f"MDL lookup failed/no summary for '{title}' (MDL: {mdl_num_str}). Falling back to docket data.")
        if docket_df is None or docket_df.empty:
            self.logger.warning(f"Cannot fallback to docket data for '{title}': docket_df is None or empty.")
            return default_summary
        
        try:
            # Ensure required columns exist in the passed df
            if 'title' not in docket_df.columns:
                self.logger.warning(f"Fallback docket_df missing 'title' column for '{title}'.")
                return default_summary

            match_df = docket_df[docket_df['title'] == title]
            if not match_df.empty:
                # Find the first row with non-empty combined allegations/claims
                for _, row in match_df.iterrows():
                    allegation = str(row.get('allegations', '')).strip()
                    claim = str(row.get('claims', '')).strip()
                    combined = f"{allegation} {claim}".strip()
                    if combined:
                        self.logger.debug(f"Found summary in docket data for '{title}'.")
                        return self._format_text(combined)
                self.logger.debug(f"No non-empty allegations/claims found in docket data for '{title}'.")
                return default_summary
            else:
                self.logger.warning(f"Could not find title '{title}' in provided docket_df for fallback summary.")
                return default_summary
        except Exception as e:
            self.logger.error(f"Error querying docket data for allegations for '{title}': {e}", exc_info=True)
            return default_summary

    async def get_mdl_summary_data_async(self, afff_stats: Optional[Dict[str, Any]] = None) -> Tuple[List[str], List[int]]:
        """
        Retrieves Top N MDLs by filing count for the chart, using cache.
        Applies AFFF *combined* placeholder value (sum of specific stats) BEFORE sorting and slicing if stats are provided.
        Maps MDL numbers to short names using a comprehensive lookup - EXACT COPY of original logic.
        """
        self.logger.info("Retrieving MDL summary for chart...")
        if not self.config.iso_date or not self.config.download_dir:
            self.logger.error("Cannot get MDL summary: iso_date or download_dir not set.")
            return [], []

        cache_file_path = os.path.join(self.config.download_dir, 'reports', f"mdl_summary_{self.config.iso_date}.json")

        # --- Cache Check ---
        if os.path.exists(cache_file_path):
            try:
                with open(cache_file_path, 'r', encoding='utf-8') as file:
                    data = json.load(file)
                if isinstance(data, dict) and 'chart_labels' in data and 'chart_data' in data:
                    self.logger.info(f"Loaded MDL summary from cache: {cache_file_path}")
                    return data['chart_labels'], data['chart_data']
                else:
                    self.logger.warning(f"MDL summary cache file '{cache_file_path}' invalid. Recalculating.")
            except Exception as e:
                self.logger.warning(f"Error reading MDL summary cache '{cache_file_path}': {e}. Recalculating.")

        # --- Live PACER Database Query (matches original) ---
        self.logger.info("Calculating MDL summary from live PACER data (cache miss or invalid).")
        try:
            # Get live MDL summary from PACER database - matches original implementation
            mdl_summary_df = await self.pacer_repo.get_mdl_summary(self.config.iso_date)
            if mdl_summary_df is None or mdl_summary_df.empty:
                self.logger.warning(f"No MDL summary data returned from PACER for {self.config.iso_date}.")
                return [], []

            # --- Processing Logic (copied from original) ---
            required_db_cols = ['MdlNum', 'Total']
            if not all(col in mdl_summary_df.columns for col in required_db_cols):
                missing_cols = [col for col in required_db_cols if col not in mdl_summary_df.columns]
                self.logger.error(f"MDL summary DataFrame from PACER missing required columns: {missing_cols}.")
                return [], []

            df_proc = mdl_summary_df.copy().dropna(subset=required_db_cols)
            df_proc['MdlNum'] = df_proc['MdlNum'].astype(str).str.strip().replace('nan', '', regex=False).replace(
                'None', '', regex=False).replace('NA', '', regex=False)
            df_proc = df_proc[~df_proc['MdlNum'].str.upper().isin(['', 'NA'])]
            df_proc['Total'] = pd.to_numeric(df_proc['Total'], errors='coerce').fillna(0).astype(int)

            # Define COMPREHENSIVE MDL Lookup dictionary (from original)
            mdl_lookup = {
                "3060": "Hair Relaxer",
                "2873": "AFFF",
                "3094": "Ozempic/GLP-1",
                "2738": "Talcum Powder",
                "CLJ": "Camp Lejeune",  # Key for lookup
                "25": "Camp Lejeune",  # Map '25' to CLJ key
                "3092": "Suboxone Film",
                "2666": "Bair Hugger",
                "3004": "Paraquat",
                "2974": "Paragard IUD",
                "3081": "Bard Port Catheter",
                "3029": "Covidien Mesh",
                "3140": "Depo-Provera",
                "2741": "Roundup",
                "2921": "Allergan Biocell",
                "3125": "AngioDynamics Port",
                "2846": "Bard Hernia Mesh",
                "3084": "Rideshare Assault",
                "3047": "Social Media Addiction"
                # Add any other necessary mappings
            }
            self.logger.debug(f"Using MDL Lookup: {list(mdl_lookup.keys())}")  # Log keys being used

            # *** START: Apply AFFF Placeholder Logic (SUM of two values) BEFORE Sorting ***
            afff_combined_placeholder_value = None
            if afff_stats and isinstance(afff_stats, dict):
                placeholder_key_plaintiffs = 'total_plaintiffs_30_days_placeholder'
                placeholder_key_filings = 'direct_filings_30_days_placeholder'
                plaintiffs_val = afff_stats.get(placeholder_key_plaintiffs)
                filings_val = afff_stats.get(placeholder_key_filings)

                if plaintiffs_val is not None and filings_val is not None:
                    try:
                        # Calculate the SUM here
                        afff_combined_placeholder_value = int(plaintiffs_val) + int(filings_val)
                        self.logger.info(
                            f"Calculated combined AFFF placeholder value: {plaintiffs_val} + {filings_val} = {afff_combined_placeholder_value}")
                    except (ValueError, TypeError) as conv_err:
                        self.logger.error(
                            f"Could not convert one or both AFFF placeholder values ('{plaintiffs_val}', '{filings_val}') to int. Placeholder not applied. Error: {conv_err}")
                        afff_combined_placeholder_value = None  # Ensure it's None if conversion fails
                else:
                    missing_keys = []
                    if plaintiffs_val is None: missing_keys.append(placeholder_key_plaintiffs)
                    if filings_val is None: missing_keys.append(placeholder_key_filings)
                    self.logger.warning(
                        f"Key(s) {missing_keys} not found in provided afff_stats dict. Cannot calculate combined placeholder.")

            if afff_combined_placeholder_value is not None:
                afff_mdl_num = "2873"  # AFFF MDL number as string key
                afff_mask = df_proc['MdlNum'] == afff_mdl_num
                if afff_mask.any():
                    # Safely get original value(s) for logging before overwrite
                    original_values = df_proc.loc[afff_mask, 'Total'].tolist()
                    # Apply the CALCULATED SUM to the DataFrame Total column
                    df_proc.loc[afff_mask, 'Total'] = afff_combined_placeholder_value
                    self.logger.info(
                        f"Applied COMBINED AFFF placeholder: Updated MDL {afff_mdl_num} total from {original_values} to {afff_combined_placeholder_value} BEFORE sorting.")
                else:
                    self.logger.info(
                        f"MDL {afff_mdl_num} (AFFF) not found in summary data before sorting. Combined placeholder not applied.")
            else:
                self.logger.warning(
                    "Could not apply combined AFFF placeholder value: Values missing from afff_stats or calculation failed.")
            # *** END: Apply AFFF Placeholder Logic ***

            # --- Sort AFTER potentially modifying AFFF value ---
            # Sort by the (potentially adjusted) 'Total', then by 'MdlNum' for stable sort
            df_proc = df_proc.sort_values(by=['Total', 'MdlNum'], ascending=[False, True])

            # --- Select Top N ---
            # Assumes self.config.mdl_chart_top_n is set
            top_n = df_proc.head(self.config.mdl_chart_top_n)
            self.logger.debug(
                f"Top {self.config.mdl_chart_top_n} MDL Summary DF (after potential AFFF adjustment & sorting):\n{top_n.to_string()}")

            # --- Generate Labels/Data from FINAL Top N ---
            chart_labels = []
            chart_data = []
            for index, row in top_n.iterrows():  # Use index for logging if needed
                # Clean the MDL number from the potentially modified DataFrame row
                mdl_num_from_row = str(row['MdlNum']).strip().removesuffix('.0')
                # Determine the key for lookup (handle Camp Lejeune case)
                mdl_key = 'CLJ' if mdl_num_from_row == '25' else mdl_num_from_row
                # Perform the lookup using the derived key and the COMPREHENSIVE dictionary
                mdl_name = mdl_lookup.get(mdl_key, f"MDL {mdl_key}")  # Default if key not in lookup

                # Debugging Log: Check the mapping result for each row
                self.logger.debug(
                    f"Row Index: {index}, MdlNum: '{row['MdlNum']}', Cleaned Num: '{mdl_num_from_row}', Lookup Key: '{mdl_key}', Mapped Name: '{mdl_name}', Value: {row['Total']}")

                chart_labels.append(mdl_name)
                # Use the 'Total' value from the row, which will be the placeholder if applied
                chart_data.append(int(row['Total']))

            # --- Save to Cache ---
            if chart_labels:
                # Save the final calculated labels and data
                from datetime import datetime
                data_to_save = {
                    'chart_labels': chart_labels,
                    'chart_data': chart_data,
                    'timestamp': datetime.now().isoformat()
                }
                try:
                    os.makedirs(os.path.dirname(cache_file_path), exist_ok=True)
                    with open(cache_file_path, 'w', encoding='utf-8') as file:
                        json.dump(data_to_save, file, indent=2)
                    self.logger.info(f"Saved calculated MDL summary to cache: {cache_file_path}")
                except Exception as e:
                    self.logger.error(f"Failed to save MDL summary cache to {cache_file_path}: {e}", exc_info=True)

            return chart_labels, chart_data

        except Exception as e:
            self.logger.error(f"Error retrieving MDL summary from PACER: {e}", exc_info=True)
            return [], []

    def _load_json_file(self, file_path: str) -> Dict[str, Any]:
        """Helper method to load JSON files synchronously for use with asyncio.to_thread."""
        with open(file_path, 'r') as f:
            return json.load(f)

    # Backward compatibility sync wrapper methods
    def calculate_afff_stats(self, docket_df: pd.DataFrame) -> Optional[Dict[str, Any]]:
        """Sync wrapper for calculate_afff_stats_async."""
        return asyncio.run(self.calculate_afff_stats_async(docket_df))
    
    def group_filings_by_litigation(self, docket_df: pd.DataFrame) -> Tuple[Dict[str, pd.DataFrame], pd.DataFrame]:
        """Sync wrapper for group_filings_by_litigation_async."""
        return asyncio.run(self.group_filings_by_litigation_async(docket_df))
    
    def group_filings_by_firm(self, docket_df: pd.DataFrame) -> Dict[str, pd.DataFrame]:
        """Sync wrapper for group_filings_by_firm_async."""
        return asyncio.run(self.group_filings_by_firm_async(docket_df))
    
    def filter_ad_summaries(self, ad_df: pd.DataFrame) -> Dict[str, pd.DataFrame]:
        """Sync wrapper for filter_ad_summaries_async."""
        return asyncio.run(self.filter_ad_summaries_async(ad_df))
    
    def get_mdl_summary_data(self, afff_stats: Optional[Dict[str, Any]] = None) -> Tuple[List[str], List[int]]:
        """Sync wrapper for get_mdl_summary_data_async."""
        return asyncio.run(self.get_mdl_summary_data_async(afff_stats))