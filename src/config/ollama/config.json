{"ollama_base_url": "http://localhost:11434", "default_model": "llama32-vision-ocr", "models": {"llama32-vision-ocr": {"description": "Optimized Llama 3.2 Vision for OCR tasks", "timeout": 3600, "temperature": 0.1, "num_gpu_layers": -1, "num_ctx": 2048, "num_predict": 512, "recommended": true}, "llama3.2-vision:11b": {"description": "Llama 3.2 Vision 11B - Best accuracy for OCR", "timeout": 1200, "temperature": 0.1, "num_gpu_layers": -1, "recommended": true}, "llama3.2-vision:11b-instruct-q4_K_M": {"description": "Llama 3.2 Vision 11B Instruct Q4_K_M - Balanced performance", "timeout": 3600, "temperature": 0.1, "num_gpu_layers": -1, "num_ctx": 2048, "num_predict": 512}, "llava:7b": {"description": "LLaVA 7B - Legacy model", "timeout": 1200, "temperature": 0.3, "num_gpu_layers": -1}, "llava:7b-v1.6-mistral-q4_0": {"description": "LLaVA 7B v1.6 Mistral Q4 - Efficient variant", "timeout": 1200, "temperature": 0.3, "num_gpu_layers": -1}}, "semaphore": {"enabled": true, "count": 6}, "performance": {"batch_size": 25, "db_write_concurrency": 16, "ollama_num_parallel": 4, "recommendations": {"gpu_vram_8gb": {"ollama_num_parallel": 2, "client_semaphore_count": 4}, "gpu_vram_16gb": {"ollama_num_parallel": 3, "client_semaphore_count": 6}, "gpu_vram_24gb": {"ollama_num_parallel": 4, "client_semaphore_count": 8}, "gpu_vram_48gb": {"ollama_num_parallel": 6, "client_semaphore_count": 12}}}, "ocr_prompt": "Describe this image by listing all visible text exactly as it appears. Do not interpret, summarize, or add details about style, graphics, or context. Only provide the text present in the image, in order from top to bottom."}