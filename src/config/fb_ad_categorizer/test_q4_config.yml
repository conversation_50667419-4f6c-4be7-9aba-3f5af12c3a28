# Q4 Model Test Configuration
# Usage: python src/scripts/hybrid_classifier.py --config src/config/fb_ad_categorizer/test_q4_config.yml

# Data Source
input:
  source: dynamodb
  dynamodb_table_name: "FBAdArchive"
  local_dynamodb: true
  dynamodb_endpoint_url: "http://localhost:8000"
  start_date: null
  limit: 1000                         # Test with 1000 ads only
  csv_file: null

# Campaign Configuration
campaigns:
  config_file: "src/config/fb_ad_categorizer/campaign_config.json"
  skip_terms_file: "src/config/fb_ad_categorizer/campaign_skip_terms.json"

# Processing Options
processing:
  batch_size: 256
  stages:
    rules_only: false
    embeddings_only: false
    full_pipeline: true               # Test full pipeline with Q4
  text_fields:
    - "Title"
    - "Body" 
    - "Summary"
    - "LinkDescription"
    - "PageName"
  deduplication_fields:
    - "Title"
    - "Body"
    - "Summary"

# AI Models
models:
  # Embedding Model
  embedder:
    model_name: "all-roberta-large-v1"
    cache_file: "embedding_roberta.pkl"
  
  # NER Model  
  ner:
    model_name: "en_core_web_trf"
    cache_file: "ner_cache_new.pkl"
  
  # LLM Configuration - Q4 Test
  llm:
    enabled: true
    backend: "ollama"
    model: "deepseek-r1:8b-q4_0"      # Q4 quantized model
    api_base_url: "https://api.deepseek.com"
    succinct_names: true              # Test succinct names
    enhanced_llm: true
    timeout: 60                       # Shorter timeout for Q4 (faster)
    max_retries: 3
    retry_delay: 5
    cache_file: "llm_response_cache_q4_test.pkl"

# Output
output:
  csv_file: "classified_ads_q4_test.csv"
  deduplicated_csv_file: "classified_ads_q4_dedup_test.csv"
  
# Rule Improvement
rules:
  improve_rules: false               # Skip for quick test
  output_suggestions: null

# Logging
logging:
  level: "INFO"

# AWS Settings
aws:
  region: "us-west-2"