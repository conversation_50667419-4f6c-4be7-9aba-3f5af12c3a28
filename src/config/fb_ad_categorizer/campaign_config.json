[{"Company": "PowerSchool", "LitigationName": "Power School Data Breach Litigation", "triggers": ["powerschool", "power school", "powerschool holdings", "power school holdings"]}, {"Company": "<PERSON><PERSON><PERSON>", "LitigationName": "Roblox Children's Online Safety Litigation", "triggers": ["roblo<PERSON>"], "include": ["predator", "predators", "exploited", "grooming", "groomed", "sexually", "abuse"]}, {"Company": null, "LitigationName": "Juvenile Detention Abuse", "triggers": ["youth center", "troubled teen", "juvenile detainees", "juvenile detention", "youth facility", "juvenile facilities", "child abuse", "juvenile detention center", "juvenile detention centers", "detention center", "detention centers", "detained youth", "youth abused", "detained youth facility", "detained youth facilities", "foster care"], "include": ["sexual assault", "sexual abuse", "abuse", "inappropriate touching"]}, {"Company": null, "LitigationName": "Ultra-Processed Foods", "MdlNum": ["9002"], "triggers": ["type 2 diabetes", "type-2 diabetes", "type 2 diabetes", "ultra processed foods", "ultra-processed foods", "ultra processed food", "ultra-processed food", "ultra processed food"], "include": ["childhood diabetes", "diabetes", "type 2 diabetes", "type-2 diabetes", "type 2 diabetes", "ultra processed foods", "ultra-processed foods"], "exclude": ["splenda", "glp-1", "stomach paralysis", "intestinal blockage", "eye stroke", "vomitting", "water contamination", "military", "firefighter", "aqueous-film-forming-foam", "afff", "(afff)", "ozempic", "wegovy"]}, {"Company": "Monsanto", "LitigationName": "Glyhphosate Type 2 Diabetes", "triggers": ["glyphosate", "roundup"], "include": ["type 2 diabetes", "diabetes"]}, {"Company": "Monsanto", "LitigationName": "RoundUp", "MdlNum": ["2741"], "triggers": ["weedkiller", "roundup", "glyphosate", "bayer roundup", "roundup weed killer", "exposed to roundup", "bayer/monsanto", "non-hodgkin", "non-hodgkins", "lymphoma", "non hodgkins"], "exclude": ["oxb<PERSON>ta", "afff", "firefighting", "fire fighting", "firefighting", "gard<PERSON>l", "suboxone", "merick", "indivior", "biozorb", "pfizer-biontech", "igora royal", "false labeling", "playboy", "cigna", "columbus bd", "jessup elite spice", "north haven", "river rouge", "baxter healthcare", "virginia beach", "sterilization", "river rouge", "cancer contamination", "zantac", "johnson & johnson", "firefighting foam", "firefighter", "coventry", "boston scientific", "talcum powder", "medtronic", "xomed", "camp lejeune", "girl scout", "girl scouts", "groveland", "sterigenics", "cosmed", "ethylene oxide", "toxic air", "lakewood", "hair dye", "pfas", "<PERSON><PERSON><PERSON>'s disease", "parkinsons", "ethicon", "hair-dye", "vals<PERSON>an", "losartan", "irb<PERSON><PERSON>an"]}, {"Company": "United States", "LitigationName": "Camp <PERSON>", "triggers": ["camp lejeune"]}, {"Company": "Huhai, Arbudino, and Hetero Drugs", "LitigationName": "Valsartan", "MdlNum": ["2875"], "triggers": ["vals<PERSON>an", "losartan", "irb<PERSON><PERSON>an"]}, {"Company": "<PERSON><PERSON>", "LitigationName": "Paragard IUD Products Liability", "MdlNum": ["2974"], "triggers": ["para<PERSON>", "<PERSON><PERSON> ud", "<PERSON><PERSON> iud", "IUD"]}, {"Company": "Syngenta", "LitigationName": "Paraquat", "MdlNum": ["3004"], "triggers": ["paraquat", "herbicide", "<PERSON><PERSON><PERSON>'s disease", "\"paraquat\""], "exclude": ["pregnant", "asd", "adhd", "add/adhd", "birth defect", "birth defects", "dcpa", "dact<PERSON>"]}, {"Company": "Pfizer", "LitigationName": "Depo-Provera", "triggers": ["depo-provera", "depoprovera", "birth control", "injectable birth control", "brain tumor lawsuit", "meningioma", "depo shot", "depo-shot", "depo provera", "brain tumor", "brain tumors", "brain-tumor"], "exclude": ["prednisone", "dudewipes", "glucosamine", "filshie®", "oxb<PERSON>ta", "afff", "hpv", "gardasil vaccine", "firefighting", "fire fighting", "firefighting", "gardisal", "suboxone", "merick", "indivior", "biozorb", "pfizer-biontech", "pfas", "pfas chemicals", "filshie", "filshie clip", "biozorb marker", "biozorb® implant", "biozorb® implants", "biozorb implant", "biozorb implants", "merck", "filshie clips"]}, {"Company": null, "LitigationName": "In-App <PERSON>", "triggers": ["in-game purchases", "in game purchases", "in-game purchase", "in game purchase", "dark patterns", "dark pattern", "in-game purchases", "in game purchases", "in-app purchase", "in app purchase", "in-app-purchase", "in-app-purchases", "in-app-purchase", "in-app-purchases", "in-app-purchase", "dark patterns", "dark pattern", "dark-patterns", "dark-pattern"], "exclude": ["taking over your child’s life", "your child's well being", "your child's well-being", "child's gaming habit", "video game addiction", "gaming addiction", "video games addiction", "video gaming addiction"]}, {"Company": "Chiquita Canyon/Waste Connections", "LitigationName": "Chiquita Canyon Landfill Environmental Contamination", "description": "Cases involving Chiquita Canyon Landfill, Sunshine Canyon Landfill, toxic exposure, chemical fires, gas leaks, or environmental contamination. If the case mentions specific locations like Val Verde, Castaic, Santa Clarita, or other nearby communities but involves the Chiquita Canyon or Sunshine Canyon landfills, classify under this campaign.", "triggers": ["chiquita canyon", "sunshine canyon", "landfill", "toxic exposure", "chemical fires", "val verde", "castaic", "santa clarita"], "include": ["landfil", "canyon"], "exclude": ["banana", "bananas"]}, {"Company": "Pfizer", "LitigationName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "triggers": ["oxb<PERSON>ta", "sickle cell", "global blood therapeutics", "voxelotor"]}, {"Company": "Abbott Laboratories", "LitigationName": "NEC", "MdlNum": ["3026"], "triggers": ["necrotizing enterocolitis", "fed enfamil", "necrotizing", "nec", "cow's milk", "cow's milk-based", "similac", "enfamil", "nec baby formula", "enfamil/similac"], "exclude": ["toddler beverages", "false advertising", "fascitis", "DCPA", "fire"]}, {"Company": "Johnson & Johnson", "LitigationName": "<PERSON><PERSON><PERSON>", "MdlNum": ["2738"], "triggers": ["talcum powder", "baby powder", "johnson & johnson", "<PERSON><PERSON><PERSON>", "johnson", "fallopian", "peritoneal", "ovarian cancer"], "include": ["talcum", "talcum powder", "baby powder", "talc"]}, {"Company": "Bard/AngioDynamics", "LitigationName": "Bard/AngioDynamics PowerPort", "MdlNum": ["3081", "3125"], "triggers": ["port catheter", "bard powerport lawsuit", "angio dynamics", "angiodynamics", "catheter", "powerport", "angiodynamics", "angiodynamics'", "smartport", "xcela", "ventralex", "smart-port", "chemo port", "chemo ports", "chemo port", "port-a-cath", "port-a-caths", "Bard® PowerPorts®", "powerports®", "powerports", "bard powerport", "bard powerport", "smart port ct", "BardPort®", "bard access systems"], "exclude": ["atrium health", "her2", "breast cancer", "abiomed", "hernia mesh"]}, {"Company": "Makena Pharmaceuticals", "LitigationName": "<PERSON>n", "triggers": ["makena"]}, {"Company": null, "LitigationName": "Spinal Cord Stimulator", "triggers": ["spinal cord stimulator", "spinal stimulator", "SCS implants"]}, {"Company": null, "LitigationName": "<PERSON><PERSON> coli Outbreak Liability", "triggers": ["e. coli", "e coli", "e-coli"]}, {"Company": "Sterigenics", "LitigationName": "Ethylene Oxide", "triggers": ["coon rapids", "columbus bd", "arvada, co", "steris", "arvada", "northborough steris", "new tazewell", "columbus bd", "jessup elite spice", "north haven", "river rouge", "baxter healthcare", "virginia beach", "sterilization", "river rouge", "cancer contamination", "cvr", "xomed", "lifenet", "midwest sterilization", "lemco ardmore", "groveland", "trigen laboratories", "trigen", "jorgenson labs", "erie medical center", "san engelo", "sterigenics", "medtronic", "toxic air", "sterilization", "arthrex"], "include": ["chemicals", "industrial chemicals", "air pollution", "toxic air", "cancer", "cancers", "toxic air", "unexplained illness"]}, {"Company": "Indivior", "LitigationName": "Suboxone", "MdlNum": ["3092"], "triggers": ["suboxone", "indivior", "suboxone film", "Suboxone®"]}, {"Company": "Uber Technologies", "LitigationName": "Uber Sexual Assault", "MdlNum": ["3084"], "triggers": ["uber", "lyft", "uber/lyft", "ride-sharing", "ridesharing", "rideshare"], "include": ["dating app", "dating", "harm", "assault", "sexual assault", "inappropriate", "forced kissing", "genital exposure", "sexually assaulted", "misconduct", "sexual abuse", "physical and sexual"], "exclude": ["music industry"]}, {"Company": null, "LitigationName": "Hair Relaxer", "MdlNum": ["3060"], "triggers": ["hair relaxer lawsuit", "ovarian cancer", "uterine cancer", "endometrial cancer", "diagnosed with cancer"], "include": ["hair relaxers", "hair relaxer", "hair straightener", "hair straighteners", "chemical hair relaxers", "chemical hair straightener", "dangerous chemicals"], "exclude": ["pfas", "AFFF", "firefighting", "fire fighting foam", "firefighting foam", "firefighters", "firefighting gear", "oxb<PERSON>ta", "afff", "firefighting", "fire fighting", "gard<PERSON>l", "suboxone", "merick", "indivior", "biozorb", "pfizer-biontech", "pfas"]}, {"Company": null, "LitigationName": "Ozempic", "description": "Cases involving weight loss injections, injectable weight loss medications, weight loss drugs, diabetes injections, or any weight loss and diabetes medications including but not limited to: Ozempic, Wegovy, Rybelsus, Trulicity, semaglutide, GLP-1. If ANY content mentions 'weight loss injection', 'injectable weight loss', 'diabetes injection', 'weight loss medication', 'weight loss drug', 'weight loss and diabetes' or similar variations, classify as this campaign.", "MdlNum": ["3094"], "triggers": ["ozempic", "wegovy", "ry<PERSON><PERSON>", "trulicity", "semaglutide", "mou<PERSON><PERSON>", "glp-1", "naion", "weight loss injection", "size reduction medicines", "diabetes injection", "injectable weight loss", "weight loss drug and have stomach issues", "gastrointenstinal issues", "non-arteritic anterior ischemic optic neuropathy", "vision loss while taking Weight Loss Drugs"], "exclude": ["metformin"]}, {"Company": null, "LitigationName": "PFAS General", "triggers": ["afff/pfas", "afff pfas", "pfas/afff", "pfas afff"], "include": ["toxic water", "water contamination", "forever chemicals", "groundwater", "contamination"]}, {"Company": "3M", "LitigationName": "AFFF", "MdlNum": ["2873"], "triggers": ["afff", "firefighting foam", "fire fighting foam", "aqueous fire fighting foam", "aqueous film forming foam", "afff foam", "afff exposure", "firefighters", "firefighting gear", "forever chemicals"], "include": ["afff", "firefighting foam", "firefighters", "firefighting"], "exclude": ["los angeles", "insurance payout", "total loss", "wildfire survivors", "wildfire damage", "fire lawsuit", "explosion", "recent wildfires", "eaton fire", "wildfire", "wildfires", "propane explostions", "propane explosion", "toxic water contamination", "toxic water", "hair relaxer", "dog bites", "water-resistant fabrics", "cookware", "auguste", "fout"]}, {"Company": "3M", "LitigationName": "<PERSON><PERSON>", "MdlNum": ["2666"], "triggers": ["bair hugger"], "include": ["warming device", "infections", "warming blanket"]}, {"Company": "Me<PERSON><PERSON>", "LitigationName": "<PERSON><PERSON><PERSON><PERSON>", "MdlNum": ["3036"], "triggers": ["gard<PERSON>l", "gard<PERSON>l", "merck"], "include": ["hpv vaccine", "hpv"]}, {"Company": null, "LitigationName": "Toxic Baby Food", "MdlNum": ["3101"], "triggers": ["toxic baby food", "heavy metal contamination", "gerber", "baby food autism", "best organic", "beech nut", "beech-nut", "happy baby", "baby food heavy metals", "plum organic", "earth's best organic baby food"], "exclude": ["data breach"]}, {"Company": "Exactech", "LitigationName": "Exactech", "MdlNum": ["3044"], "triggers": ["exactech"], "include": ["recall", "knee", "ankle", "hip", "joint replacement"]}, {"Company": null, "LitigationName": "Online Gambling", "triggers": ["online gambling", "online casino", "casino slots", "unauthorized gambling"]}, {"Company": null, "LitigationName": "Video Game Addiction", "triggers": ["justice for gaming", "is gaming taking over your child’s life", "video game addiction", "gaming disorder", "video gaming addiction", "gaming addiction", "fortnite", "call of duty", "roblo<PERSON>", "gaming", "gaming habits", "child stealing", "epic games", "minecraft", "grand theft auto", "gta", "overwatch", "world of warcraft", "rainbox six", "battlefield", "apex legends", "nba 2k", "candy crush"], "include": ["addicting", "addiction", "hooked", "gaming", "mental", "physical", "health", "sleep", "disturbances", "emotional", "distress", "exhaustion", "treatment", "recovery", "oppositional defiant disorder", "ADHD", "suicide", "harm", "habit", "habits", "addicted", "issues", "highly addictive"]}, {"Company": "Fisher-Price", "LitigationName": "Fisher-Price Snuga Infant Swing", "triggers": ["fisher-price"], "include": ["swing", "recall", "snuga swing", "snuga swing"]}, {"Company": null, "LitigationName": "Social Media Addiction", "MdlNum": ["3047"], "triggers": ["social media addiction", "social media harm", "instagram", "tiktok", "youtube", "facebook", "instagram", "snapchat", "meta", "social media use,", "excessive social media use", "addictive social media", "social media usage."], "include": ["social media addiction", "harming your child", "child harm", "mental health"], "exclude": ["video games", "te<PERSON><PERSON>", "<PERSON><PERSON>", "c-8", "oxy", "asbestos", "oxycontin", "oxy kingpins", "mass arbitration", "privacy violations", "arbitration", "extortion"]}, {"Company": "Hologic", "LitigationName": "Hologic Biozorb Marker", "triggers": ["hologic", "biozorb", "biozorb® implant lawsuit", "biozorb implant", "biozorb implants", "breast cancer implant lawsuit", "biozorb® implant", "biozorb® implants", "biozorb marker", "BioZorb® Marker"]}, {"Company": "<PERSON>immer <PERSON>", "LitigationName": "Zimmer Biomet Hip Implant", "triggers": ["zimmer biomet"], "include": ["hip implant", "complications", "hip"]}, {"Company": "<PERSON><PERSON><PERSON>", "LitigationName": "<PERSON><PERSON><PERSON><PERSON>", "triggers": ["<PERSON><PERSON><PERSON><PERSON>"], "include": ["liver"]}, {"Company": "Etsy", "LitigationName": "Etsy Privacy ", "triggers": ["etsy", "etsy's", "etsy.com"]}, {"Company": "Ticket <PERSON>", "LitigationName": "Ticket Junk Fee Investigation", "triggers": ["event ticket center", "event ticket centers", "purchased tickets", "purchased ticket"]}, {"Company": "Expedia", "LitigationName": "Expedia Deceptive Fees", "triggers": ["expedia"], "include": ["new york"]}, {"Company": "Experian", "LitigationName": "Experian Consumer Protection", "triggers": ["experian", "identityworks", "creditworks"], "include": ["illegally restrict", "illegally restricting", "illegal cause"]}, {"Company": "AMVAC Chemical Corporation", "LitigationName": "<PERSON><PERSON><PERSON>", "triggers": ["dact<PERSON>", "dcpa", "pesticide"], "include": ["fetal", "birth defects", "child", "birth defect"], "exclude": ["paraqut"]}, {"Company": "Basuch + Lomb", "LitigationName": "Bausch + Lomb TASS Cataract Surgery", "triggers": ["TASS", "b<PERSON>ch", "lomb", "envista"], "include": ["cataract", "cataracts", "cataract surgery"]}, {"Company": null, "LitigationName": "Asbestos Products Liability", "triggers": ["asbestos", "mesothelioma"], "exclude": ["pfas", "talcum", "talc", "toxic chemicals"]}, {"Company": null, "LitigationName": "Mohawk & Shaw PFAS Carpet & Rug Contamination", "triggers": ["mohawk and shaw", "mohawk", "shaw"], "include": ["groundwater", "contamination", "fort", "exposed"], "exclude": ["water", "firefighting", "fire fighting", "firefighting", "firefighting foam", "fire fighting foam", "firefighters", "firefighting gear", "afff", "(afff)", "afff firefighting foam"]}, {"Company": null, "LitigationName": "PFAS Drinking Water Contamination", "triggers": ["pfas", "water contamination", "toxic water contamination", "contaminated drinking water", "toxic water", "'forever chemicals", "‘Forever Chemicals’"], "include": ["groundwater", "contamination", "exposure", "exposed"], "exclude": ["free tickets", "camp lejeune", "fertilizer", "firefighting", "fire fighting", "firefighting", "firefighting foam", "fire fighting foam", "firefighters", "firefighting gear", "afff", "(afff)", "afff firefighting foam", "mohawk", "shaw", "stain resistant"]}, {"Company": null, "LitigationName": "PFAS Fertilizer", "triggers": ["pfas", "toxic"], "include": ["fertilizer"]}, {"Company": null, "LitigationName": "Pressure Cooker Liability", "triggers": ["pressure cooker", "pressure cookers", "instapot", "insta-pot"]}, {"Company": "<PERSON>", "LitigationName": "<PERSON><PERSON> Sex Abuse Litigation", "triggers": ["sean combs", "p. diddy", "diddy", "puff daddy"]}, {"Company": "Playboy", "LitigationName": "Playboy Sexual Assault Litigation", "triggers": ["playboy", "hugh hefner"], "include": ["sexual assault"], "exclude": []}, {"Company": "Bard/Covidien", "LitigationName": "Bard/<PERSON><PERSON>ien <PERSON>", "MdlNum": ["2846", "3029"], "triggers": ["hernia mesh", "hernia surgery", "hernia surgeries"]}, {"Company": "Te<PERSON>zza", "LitigationName": "Tepezza Products Liability", "MdlNum": ["3079"], "triggers": ["te<PERSON><PERSON>"], "include": ["hearing loss", "hearing", "tinnitus"]}, {"Company": "<PERSON><PERSON><PERSON><PERSON>", "LitigationName": "Filshie Clips Product Liability", "triggers": ["filshie"]}, {"Company": "Cooper Surgical", "LitigationName": "Cooper Surgical IVF Culture Media", "triggers": ["cooper surgical", "coopersurgical"], "include": ["ivf", "culture medium"], "exclude": ["pgt-a", "pgt - a", "ptg-a", "genetic", "aneuploidy"]}, {"Company": "Cooper Surgical", "LitigationName": "Cooper Surgical Defective Aneuploidy Testing", "triggers": ["ptg-a", "pgt-a", "genetic", "aneuploidy", "testing"], "include": ["cooper surgical", "coopersurgical"], "exclude": ["culture medium"]}, {"Company": "<PERSON><PERSON>med", "LitigationName": "Abiomed Impella Heart Pump", "triggers": ["abiomed heart pump", "abiomet impella", "impella heart pump"]}, {"Company": null, "LitigationName": "Unpaid Wage Claims", "triggers": ["unpaid wages", "overtime", "worker classification"]}, {"Company": null, "LitigationName": "TCPA Violations", "triggers": ["tcpa", "spam texts"]}, {"Company": "<PERSON><PERSON><PERSON>", "LitigationName": "Zantac", "MdlNum": ["2924"], "triggers": ["zantac", "ranitidine"]}, {"Company": null, "LitigationName": "Hair Dye/Color Bladder Cancer", "triggers": ["hair color", "hair dye", "color exposure", "bladder cancer"], "include": ["hair"], "exclude": ["hair relaxer", "hair relaxers", "advance magazine", "allure's", "pfas", "AFFF", "firefighting", "fire fighting foam", "firefighting foam", "firefighters", "firefighting gear", "oxb<PERSON>ta", "afff", "firefighting", "fire fighting", "gard<PERSON>l", "suboxone", "merck", "false labeling", "indivior", "biozorb", "pfizer-biontech", "pfas", "playboy", "sexual assault"]}, {"Company": "Southern ConEd", "LitigationName": "LA County Wildfire", "triggers": ["eaton fire", "palisades fire", "altadena fire", "hurst fire", "mountain fire", "la wildfire", "los angeles wildfire", "california wildfire", "southern california edison", "sce wildfire", "wildfire damage", "wildfire lawsuit", "wildfire claim"], "include": ["wildfire", "fire damage", "property loss", "evacuation", "burned", "fire victim", "fire lawsuit"], "exclude": ["maui", "moss landing", "hawaii", "biolab", "conyers fire lawsuit", "biolab explosion", "airport", "bio-lab", "firefighting foam", "afff", "fire extinguisher", "fire alarm", "fire department hiring", "fire sale", "fired from job"]}, {"Company": "Airport", "LitigationName": "Airport County Wildfire", "triggers": ["airport"], "include": ["wildfire"], "exclude": ["biolab", "bio-lab"]}, {"Company": "Johnson & Johnson/Janssen Healthcare", "LitigationName": "Risperdal (Invega)", "triggers": ["risperdal", "invega", "antipychotic", "zyprexa"]}, {"Company": "Honey/PayPal", "LitigationName": "Honey (PayPal) Commission Theft", "triggers": ["honey", "paypal"], "include": ["commission", "commissions", "commission theft", "browser extension"]}, {"Company": "The Church of Jesus Christ of Latter-day Saints", "LitigationName": "The Church of Jesus Christ of Latter-day Saints Sex Abuse Investigation", "triggers": ["mormon church", "mormon", "lds", "latter-day", "church of jesus christ"], "include": ["sexual abuse", "church", "sexual abuse", "abuse", "clergy abuse", "bishop", "stake president"]}, {"Company": "Biolab", "LitigationName": "Bio-Lab. Chemical Plant Explosion Liability", "triggers": ["biolab", "bio-lab"]}, {"Company": "CrowdStrike", "LitigationName": "Crowdstrike Outage", "triggers": ["crowdstrike", "crowd strike"], "include": ["software glitch", "outage"]}, {"Company": "<PERSON><PERSON>'s Head", "LitigationName": "Listeria", "description": "Cases involving Listeria", "triggers": ["boar's head", "boars head", "listeria"], "include": ["outbreak", "sick", "ill", "hospitalized", "diagnosed", "recall", "contamination", "monocytogenes"]}, {"Company": "Dr. <PERSON>", "LitigationName": "Dr. <PERSON> Sex Abuse Litigation", "description": "Cases involving sexual abuse allegations against Dr. <PERSON>", "triggers": ["dr. barry brock", "barry j brock", "barry j. brock", "barry brock"], "include": ["abuse", "assault", "victim", "survivor"]}, {"Company": "Dr. <PERSON>", "LitigationName": "Dr. <PERSON> Sex Abuse Litigation", "description": "Cases involving sexual abuse allegations against Dr. <PERSON>", "triggers": ["dr. derek todd", "derek todd"], "include": ["abuse", "assault", "victim", "survivor"]}, {"Company": "Dr. <PERSON>", "LitigationName": "Dr. <PERSON> Sex Abuse Litigation", "description": "Cases involving sexual abuse allegations against Dr. <PERSON>", "triggers": ["dr. david broadbent", "david broadbent"], "include": ["abuse", "assault", "victim", "survivor"]}, {"Company": "Dr. <PERSON><PERSON><PERSON>", "LitigationName": "Dr. <PERSON><PERSON><PERSON> Sex Abuse Litigation", "description": "Cases involving sexual abuse allegations against Dr. <PERSON>", "triggers": ["dr. <PERSON>n<PERSON><PERSON> kumar", "<PERSON>n<PERSON><PERSON> kumar"], "include": ["abuse", "assault", "victim", "survivor"]}, {"Company": null, "LitigationName": "Nexium/Prilosec", "description": "Cases involving prolonged use of heartburn medications Nexium or Prilosec linked to development of stomach, gastric, or esophageal cancers.", "triggers": ["nexium", "prilosec"], "include": ["cancer", "gastric", "stomach", "esophageal"]}, {"Company": "Atrium Health Wake Forest Baptist", "LitigationName": "Atrium Health Wake Forest HER2+ Breast Cancer Diagnosis Products Liability", "description": "Cases involving patients at Atrium Health Wake Forest Baptist who were misdiagnosed with HER2 positive breast cancer and underwent unnecessary treatments. This includes patients who received incorrect diagnoses and underwent treatments such as chemotherapy, radiation, or surgery before being informed of the diagnostic error.", "triggers": ["atrium health wake forest", "her2", "her2+", "her2 positive"], "include": ["breast cancer", "misdiagnosis", "wrong type", "diagnostic error", "incorrectly diagnosed", "unnecessary treatment"]}, {"Company": "Barnes & Noble", "LitigationName": "Barnes & Noble Digital Textbook Fees Mass Arbitration", "description": "Cases involving Barnes & Noble campus bookstores' alleged practice of adding undisclosed digital delivery fees for textbooks during checkout. Particularly focuses on university bookstores including Howard and Georgetown, where students may be owed up to $1,500 for potentially illegal fee practices.", "triggers": ["barnes & noble", "digital textbook", "digital delivery fee"], "include": ["howard", "georgetown", "AU's", "American University", "mass arbitration", "bookstore", "textbooks", "digital delivery", "checkout process"], "exclude": ["store closing", "job opening", "book signing", "new release"]}, {"LitigationName": "Data Breach", "triggers": ["data breach", "data breach?", "security breach", "hacked", "personal information exposed", "cyberattack", "unauthorized access", "data leak", "information compromised", "stolen data"]}, {"LitigationName": "Data Privacy", "triggers": ["online registration", "watch videos on", "privacy alert", "personal info was shared", "schedule appointment online", "schedule consultation online", "data illegally shared", "appointment online", "illegal sharing", "schedule a consultation online", "privacy investigation", "privacy violation", "illegal tracking", "tracking software", "vppa", "biometrics", "uploaded photo id to verify", "video privacy protection act", "biometric information", "bipa", "illinois biometric information privacy act", "facial recognition", "cipa", "cookie tracking", "california invasion of privacy act", "wiretapping", "illegal recording", "unlawful surveillance", "session replay", "pixel tracking", "data scraping", "unauthorized data collection", "sharing users' information", "unauthorized use of data", "violated consumers' privacy", "sharing users' personal information", "data sharing", "data shared", "secret tracking software", "data collection", "data privacy"]}, {"LitigationName": "TILA/Usury Lending", "triggers": ["usury", "predatory lending", "high interest loan", "excessive interest", "payday loan trap", "illegal loan terms", "loan shark"], "exclude": ["etsy"]}, {"LitigationName": "Deceptive Marketing", "triggers": ["limited-time", "discount", "deceptive advertising", "discounts", "false advertising", "false marketing", "misleading claims", "deceptive marketing", "bait and switch", "false promises", "product misrepresentation"], "exclude": ["insurance payout", "storm damage", "wildfire"]}, {"LitigationName": "Overdraft & NSF Fees", "triggers": ["overdraft fee", "nsf fee", "non-sufficient funds fee", "excessive bank fees", "unlawful bank charges", "surprise bank fee", "bounced check", "overdraft", "non-sufficient funds", "bank charge"]}, {"LitigationName": "Wildfire/Fire Claims", "triggers": ["wildfire damage", "fire damage", "burn injury", "property loss fire", "utility caused fire", "smoke inhalation", "airport fire", "eaton fire", "palisades fire", "maui wildfire"], "exclude": ["switchback", "uomeod", "mini steamers"]}, {"LitigationName": "Antitrust", "triggers": ["price fixing", "antitrust", "collusion", "anti-competitive practices", "price gouging", "monopoly pricing", "bid rigging"]}, {"Company": "Multiplan", "LitigationName": "Multiplan Antitrust Investigation", "triggers": ["insurance payment reimbursements", "out-of-network", "multiplan", "data isight", "viant"], "include": ["price-fixing", "price fixing"]}, {"Company": "Syngenta", "LitigationName": "Syngenta Antitrust Investigation", "triggers": ["syngenta", "corte<PERSON>", "basf"], "include": ["price-fixing", "price fixing", "antitrust"]}, {"Company": "PBMs", "LitigationName": "Insulin Pricing Litigation", "triggers": ["self-insured health plans", "eli lilly"], "include": ["insulin", "glp-1 medications"]}, {"Company": "PBM", "LitigationName": "Independent Pharamcy Antitrust Investigation", "triggers": ["independent pharmacy owners"], "include": ["price-fixing", "price fixing"]}, {"LitigationName": "Online Gambling Addiction", "triggers": ["underog fantasy", "draftkings", "fanduel", "sports bettting apps", "online sports gambling", "online sports betting"], "include": ["gambling addiction", "gambling disorder", "gambling harm", "gambling addiction investigation"], "exclude": ["video game addiction"]}, {"LitigationName": "Allergan BIOCELL Breast Implant Products Liability", "triggers": ["allergan biocell"]}, {"LitigationName": "Accolade Pacemaker Products Liability", "triggers": ["accolade pacemaker"]}, {"LitigationName": "Leqembi/Kisunla Products Liability", "triggers": ["leq<PERSON><PERSON>", "kisunla"], "include": ["brain", "brain injury"]}, {"LitigationName": "Kratom Products Liability", "triggers": ["kratom"]}, {"LitigationName": "Tabletop Fire Pits Products Liability", "triggers": ["table top fire pit", "tabletop fire pit", "tabletop fire pits", "tabletop fire pit recall"]}, {"LitigationName": "Low Interest Rate Cash Sweep", "triggers": ["low interest rates", "cash sweep"]}, {"LitigationName": "Boy Scouts of America Sexual Abuse Litigation", "triggers": ["boy scouts of america", "bsa", "boy scouts"], "include": ["sexual abuse", "sexual assault", "abuse", "assault"], "exclude": ["catholic church", "catholic clergy", "catholic priests", "catholic priest"]}, {"LitigationName": "Catholic Church Sexual Abuse", "triggers": ["catholic church", "catholic church sexual abuse survivors", "catholic clergy", "catholic priests", "catholic priest", "church leaders", "clergy"], "include": ["sexual abuse", "sexual assault", "abuse", "assault"]}, {"LitigationName": "Real Page Pricing Fixing", "triggers": ["realpage", "rent"], "exclude": ["ecampus", "digital textbooks", "digital", "hertz", "runway", "job", "knetbooks.com", "knetbooks", "capital one", "credit card"]}, {"Company": "3M", "LitigationName": "3M Earplugs", "triggers": ["earplugs", "ear plugs", "3m earplugs", "3m ear plugs", "3m earplug", "3m ear plug", "hearing loss", "tinnitus"], "exclude": ["<PERSON><PERSON><PERSON><PERSON>", "accuvue", "nasal strips", "oxb<PERSON>ta", "te<PERSON><PERSON>", "va disability attorneys", "va disability attorney"]}, {"Company": "Galaxy Gas", "LitigationName": "Galaxy Nitrous Oxide Litigation Products Liability", "triggers": ["nitrous oxide", "galaxy gas", "galaxy nitrous oxide"], "exclude": ["pricing", "price fixing", "price-fixing", "price fix", "pricefix", "price-fix", "price-fixing"]}, {"Company": null, "LitigationName": "Silicosis Products Liability", "triggers": ["silicosis", "silica dust"]}]