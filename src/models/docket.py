"""Pydantic models for docket-related data structures."""
from typing import Optional, Any, Dict, Union
from pydantic import BaseModel, Field, validator, root_validator


class DocketBase(BaseModel):
    """Base model for docket data with boolean field validation."""
    
    # These fields will be automatically converted to boolean
    is_transferred: bool = Field(default=False, description="Whether the docket has been transferred")
    is_removal: bool = Field(default=False, description="Whether the case was removed from state court")
    pending_cto: bool = Field(default=False, description="Whether a CTO (Conditional Transfer Order) is pending")
    
    # Validators to ensure consistent boolean values
    @validator('is_transferred', 'is_removal', 'pending_cto', pre=True)
    def ensure_boolean(cls, v: Any) -> bool:
        """Convert various truthy/falsy values to proper boolean."""
        if isinstance(v, bool):
            return v
        if isinstance(v, str):
            v = v.lower().strip()
            if v in ('true', 't', '1', 'yes', 'y'):
                return True
            if v in ('false', 'f', '0', 'no', 'n', ''):
                return False
        if isinstance(v, (int, float)):
            return bool(v)
        return False
    
    @root_validator(pre=True)
    def extract_from_legacy_fields(cls, values: Dict[str, Any]) -> Dict[str, Any]:
        """Extract values from legacy field names if present."""
        # Map of legacy field names to our standard field names
        legacy_field_map = {
            'IsTransferred': 'is_transferred',
            'IsRemoval': 'is_removal',
            'PendingCto': 'pending_cto',
            # Add any other legacy field mappings here
        }
        
        # Create a copy to avoid modifying the input
        result = values.copy()
        
        # Check for legacy fields and map them to our standard field names
        for legacy_field, standard_field in legacy_field_map.items():
            if legacy_field in values and standard_field not in values:
                result[standard_field] = values[legacy_field]
        
        return result
    
    def dict(self, **kwargs) -> Dict[str, Any]:
        """Override dict() to ensure consistent output format."""
        # Get the default dict representation
        d = super().dict(**kwargs)
        
        # Ensure all boolean fields are present in the output
        for field in ['is_transferred', 'is_removal', 'pending_cto']:
            if field not in d:
                d[field] = getattr(self, field, False)
        
        return d
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'DocketBase':
        """Create a DocketBase instance from a dictionary."""
        return cls(**data)
    
    def to_dynamodb(self) -> Dict[str, Any]:
        """Convert to DynamoDB-compatible format."""
        # Get the model as a dictionary
        data = self.dict()
        
        # Convert any special types to DynamoDB-compatible types
        result = {}
        for key, value in data.items():
            if value is None:
                continue
            if isinstance(value, bool):
                result[key] = value
            elif isinstance(value, (str, int, float)):
                result[key] = value
            # Add other type conversions as needed
        
        return result
    
    @classmethod
    def from_dynamodb(cls, data: Dict[str, Any]) -> 'DocketBase':
        """Create a DocketBase instance from DynamoDB data."""
        # Convert DynamoDB types to Python types if needed
        processed_data = {}
        for key, value in data.items():
            # Handle any DynamoDB-specific type conversions here
            processed_data[key] = value
        
        return cls(**processed_data)
