#!/usr/bin/env python3
"""
Copy files that were downloaded to the wrong staging area.
"""

import os
import shutil
from pathlib import Path

# Check the old staging area that was actually used
old_staging_base = Path("/Users/<USER>/PycharmProjects/lexgenius/data/download_staging_area/20250612")
final_dockets_dir = Path("/Users/<USER>/PycharmProjects/lexgenius/data/20250612/dockets")

print(f"Checking old staging area: {old_staging_base}")

if old_staging_base.exists():
    print(f"Old staging area exists! Looking for files...")
    
    # Find all ZIP files in the old staging area
    for zip_file in old_staging_base.rglob("*.zip"):
        print(f"Found stuck file: {zip_file}")
        
        # Extract case info from path to determine proper filename
        filename = zip_file.name
        
        # Try to determine the proper base filename from the staging directory name
        staging_dir = zip_file.parent.name
        print(f"Staging dir: {staging_dir}")
        
        if "dl_stage_cand" in staging_dir:
            # Extract docket info from staging dir name like: dl_stage_cand_3_25-cv-04970_d594e0eb
            parts = staging_dir.split("_")
            if len(parts) >= 5:
                court = "cand"
                case_parts = parts[3:6]  # Should be ['3', '25-cv-04970']
                docket_num = "_".join(case_parts)
                
                # Read corresponding JSON to get proper base filename
                json_pattern = f"{court}_25_{case_parts[1].replace('-cv-', '_').replace('-sf-', '_')}_*.json"
                print(f"Looking for JSON with pattern: {json_pattern}")
                
                # Find matching JSON file
                for json_file in final_dockets_dir.glob(f"{court}_25_*.json"):
                    if case_parts[1].replace('-cv-', '').replace('-sf-', '') in json_file.name:
                        base_name = json_file.stem
                        final_path = final_dockets_dir / f"{base_name}.zip"
                        
                        print(f"Copying {zip_file} -> {final_path}")
                        shutil.copy2(zip_file, final_path)
                        print(f"Successfully copied to: {final_path}")
                        break
                else:
                    # Fallback: use a generic name
                    fallback_name = f"{court}_25_{case_parts[1].replace('-cv-', '_').replace('-sf-', '_')}_recovered.zip"
                    final_path = final_dockets_dir / fallback_name
                    print(f"No matching JSON found, using fallback: {final_path}")
                    shutil.copy2(zip_file, final_path)
        else:
            # Generic copy
            recovery_path = final_dockets_dir / f"recovered_{filename}"
            print(f"Generic copy: {zip_file} -> {recovery_path}")
            shutil.copy2(zip_file, recovery_path)
            
else:
    print("Old staging area does not exist")

print("Done!")