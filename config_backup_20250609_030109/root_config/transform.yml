# YAML configuration for transform_data step
step_name: "transform_data"
input_source: "data/" # Example, adjust if needed
output_location: "data/" # Example, adjust if needed

# Date Configuration
date: '06/02/25' # MM/DD/YY format, matches main.py params
start_date: null # MM/DD/YY format or null
end_date: null   # MM/DD/YY format or null

# LLM Configuration
llm_provider: 'deepseek'

# Transform Data Parameters
post_process: False # Should be True even when reprocessing only specific files.
reprocess_files: [ ] # True to reprocess all, or list of specific files
start_from_incomplete: False
skip_files: [ ]
reprocess_md: [ ]
force_openrouter_paid: True
normalize_law_firm_names: False
reprocess_mdl_num: False
stop_after_extraction: False # If True, stops after PDF extraction and OCR, before LLM processing

# Upload Configuration
upload: False
upload_types:
  - 's3'
  - 'dynamodb'
force_upload: True

# Worker Configurat./ion
num_workers: 16