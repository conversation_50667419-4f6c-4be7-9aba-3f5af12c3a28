from moviepy.editor import VideoFileClip, TextClip, CompositeVideoClip, ImageClip, ColorClip

# Input details
video_path = "input_video.mp4"
output_path = "output_newscast_video.mp4"
logo_path = "logo.png"  # Path to the logo image
speaker_name = "<PERSON>"
speaker_title = "Senior Attorney"
website_name = "www.lexgenius.ai"

# Load the video
video = VideoFileClip(video_path)

# Create a background banner for the "lower third" (speaker details)
banner_height = 80
banner = (
    ColorClip(size=(video.w, banner_height), color=(0, 0, 128))  # Dark blue banner
    .set_duration(video.duration)
    .set_position(("bottom"))  # Positioned at the bottom of the video
)

# Add the speaker's name on the lower third banner
name_clip = (
    TextClip(speaker_name, fontsize=36, color="white", font="Arial-Bold")
    .set_duration(video.duration)
    .set_position((50, video.h - banner_height + 10))  # Slightly inside the banner
)

# Add the speaker's title below the name on the banner
title_clip = (
    TextClip(speaker_title, fontsize=24, color="white", font="Arial")
    .set_duration(video.duration)
    .set_position((50, video.h - banner_height + 40))  # Positioned under the name
)

# Add the website name at the very bottom of the video
website_clip = (
    TextClip(website_name, fontsize=24, color="lightgray", font="Arial-Italic")
    .set_duration(video.duration)
    .set_position(("center", video.h - 30))  # Centered at the bottom
)

# Add the logo to the top-left corner
logo = (
    ImageClip(logo_path)
    .set_duration(video.duration)
    .resize(height=80)  # Resize logo to fit nicely
    .set_position(("left", "top"))  # Positioned in the top-left corner
)

# Composite all clips together
final_video = CompositeVideoClip([video, banner, name_clip, title_clip, website_clip, logo])

# Export the final video
final_video.write_videofile(output_path, codec="libx264", audio_codec="aac")
