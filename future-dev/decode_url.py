import urllib.parse
import re

def get_port_from_google_oauth_error_url(url_string):
    """
    Extracts the port number from a Google OAuth error URL.

    Args:
        url_string: The full Google OAuth error URL.

    Returns:
        The port number as an integer, or None if not found or invalid.
    """
    try:
        # 1.  Completely decode the entire URL
        decoded_url = urllib.parse.unquote(url_string)
        # 2. Use a regular expression to find "http://localhost:PORT/"
        match = re.search(r"http://localhost:(\d+)/", decoded_url)  # Corrected regex

        if match:
            port_str = match.group(1)  # Get the captured group (the digits)
            return int(port_str)     # Convert to integer
        else:
            return None  # Port not found

    except Exception as e:
        print(f"Error extracting port: {e}")  # Keep error handling
        return None

test_url = "https://accounts.google.com/signin/oauth/error/v2?authError=ChVyZWRpcmVjdF91cmlfbWlzbWF0Y2gSsAEKWW91IGNhbid0IHNpZ24gaW4gdG8gdGhpcyBhcHAgYmVjYXVzZSBpdCBkb2Vzbid0IGNvbXBseSB3aXRoIEdvb2dsZSdzIE9BdXRoIDIuMCBwb2xpY3kuCgpJZiB5b3UncmUgdGhlIGFwcCBkZXZlbG9wZXIsIHJlZ2lzdGVyIHRoZSByZWRpcmVjdCBVUkkgaW4gdGhlIEdvb2dsZSBDbG91ZCBDb25zb2xlLgogIBptaHR0cHM6Ly9kZXZlbG9wZXJzLmdvb2dsZS5jb20vaWRlbnRpdHkvcHJvdG9jb2xzL29hdXRoMi93ZWItc2VydmVyI2F1dGhvcml6YXRpb24tZXJyb3JzLXJlZGlyZWN0LXVyaS1taXNtYXRjaCCQAyonCgxyZWRpcmVjdF91cmkSF2h0dHA6Ly9sb2NhbGhvc3Q6NjE0OTcvMqQCCAESsAEKWW91IGNhbid0IHNpZ24gaW4gdG8gdGhpcyBhcHAgYmVjYXVzZSBpdCBkb2Vzbid0IGNvbXBseSB3aXRoIEdvb2dsZSdzIE9BdXRoIDIuMCBwb2xpY3kuCgpJZiB5b3UncmUgdGhlIGFwcCBkZXZlbG9wZXIsIHJlZ2lzdGVyIHRoZSByZWRpcmVjdCBVUkkgaW4gdGhlIEdvb2dsZSBDbG91ZCBDb25zb2xlLgogIBptaHR0cHM6Ly9kZXZlbG9wZXJzLmdvb2dsZS5jb20vaWRlbnRpdHkvcHJvdG9jb2xzL29hdXRoMi93ZWItc2VydmVyI2F1dGhvcml6YXRpb24tZXJyb3JzLXJlZGlyZWN0LXVyaS1taXNtYXRjaA%3D%3D&client_id=************-2545953rln5tia649ukshnkmo1oj24mq.apps.googleusercontent.com&flowName=GeneralOAuthFlow"
port = get_port_from_google_oauth_error_url(test_url)
print(port)
