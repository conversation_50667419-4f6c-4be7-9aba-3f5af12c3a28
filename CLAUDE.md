# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

LexGenius is a legal intelligence platform that monitors Facebook ads from law firms and court dockets through PACER. It processes this data to generate weekly reports combining ad campaign insights with litigation updates for mass tort cases.

## Architecture

The codebase follows a modular architecture:

- **`src/lib/fb_ads/`** - Facebook ad scraping, image processing, and classification
- **`src/lib/pacer/`** - Court docket monitoring and PACER integration  
- **`src/lib/reports/`** - Report generation and email distribution
- **`src/lib/data_transformer/`** - Data processing and enrichment
- **`src/lib/`** - AI integrations (gpt4_interface.py, deepseek_interface.py, llava_vision.py)
- **`src/lib/storage/`** - AWS S3 and DynamoDB management

Key architectural patterns:
- Asynchronous I/O operations throughout
- SQLite-based queue for deferred image processing
- PHash-based image deduplication
- Hybrid rule-based and ML classification for ads
- Multi-model AI support (GPT-4, DeepSeek, LLaMA)

## Common Commands

### Environment Setup
```bash
conda env create -f environment.yml
conda activate lexgenius
```

### Main Workflows

Run the main pipeline with configuration:
```bash
python src/main.py --params config/scrape.yml     # Scrape PACER data
python src/main.py --params config/transform.yml   # Process and upload data
python src/main.py --params config/report.yml      # Generate daily report
python src/main.py --params config/weekly_report.yml # Generate weekly report
```

Run full pipeline:
```bash
./run_pipeline.sh --config scraper,transform,report
```

### Facebook Ad Processing
```bash
./run_update_fb_campaigns.sh  # Update campaign classifications
python src/scripts/process_image_queue.py --process  # Process queued images
python classify_all_ads.py    # Classify all ads
```

### Development Tools
```bash
./clean_pycaches.sh          # Clean Python cache files
./find_import_errors.sh      # Check for import errors
./search_json_files.sh <term> # Search JSON files
```

## Configuration System

The project uses YAML configuration files with a class-based `ScraperConfig` for validation. Key parameters:

- `date`: Target date (MM/DD/YY format)
- `scraper`: Enable PACER scraping
- `post_process`: Enable data transformation
- `upload`: Enable AWS upload
- `report_generator`: Enable report generation
- `fb_ads`: Enable Facebook ad processing
- `headless`: Run browser in headless mode
- `run_parallel`: Enable parallel processing
- `process_single_court`: List of specific court IDs
- `num_workers`: Number of parallel workers

## Data Flow

1. **Scrape**: Collect data from PACER and Facebook
2. **Process**: Transform, classify, and enrich data
3. **Store**: Save to DynamoDB and S3
4. **Report**: Generate HTML reports and send emails

Deferred processing is used for resource-intensive operations like OCR and image analysis.

## Key Features

- **PHash Deduplication**: Avoids reprocessing duplicate images
- **Campaign Classification**: Combines rules and embeddings to categorize ads
- **Parallel Processing**: Configurable workers for court and file processing
- **Multi-Model Support**: Switches between GPT-4, DeepSeek, and local models
- **Automatic Retries**: Handles failed downloads and processing

## Testing

Run individual test files from the root directory:
```bash
python test_classifier_simple.py
python test_hybrid_classifier.py
python test_dynamodb_upload.py
```

No unified test runner is configured.

## Important Notes

- Data directory is configurable via `LEXGENIUS_DATA_DIR` environment variable
- Default data structure: `data/YYYY/MM/DD/court_id/`
- AWS credentials required for S3 and DynamoDB operations
- PACER credentials required for court document access
- API keys needed for GPT-4 and DeepSeek in `.env` file

## Code Development Guidelines

### Reuse Existing Code

- **ALWAYS search for existing code before creating new functionality**
- **For DynamoDB**: Use existing managers in `src/lib/storage/`. If needed functionality doesn't exist, extend the manager rather than creating new code
- **For S3**: Use `S3Manager` or `S3ManagerAsync` classes. Add methods to these classes for new functionality
- **For all AWS services**: Find and use existing integration code. Never duplicate AWS service interactions
- **For common operations**: Check `src/lib/utils/` for utility functions before writing new ones

## Important Operational Guidelines

- **DO NOT RUN PIPELINE COMMANDS WITHOUT USER APPROVAL**

## Critical Path Validation - DO NOT MODIFY

**CRITICAL WARNING: The following code paths are essential for failed download reprocessing and parallel browser operations. DO NOT MODIFY without understanding the full impact:**

### Download Path Validation
- **`src/pacer/pacer_document_downloader.py`**: `context_download_path` validation logic
  - Ensures downloaded files are placed in correct directories
  - Prevents files from being lost in temp directories during parallel processing
  - Required for browser service download coordination

### Artifact Verification for Reprocessing
- **`src/pacer/file_manager.py`**: Artifact verification methods
  - `check_if_artifact_exists_by_pattern()`
  - `check_if_artifact_exists_last_7_days_by_pattern()`
  - `check_if_artifact_exists_across_dates_by_pattern()`
  - These methods ONLY check PDF/ZIP files and ignore JSON content
  - Essential for allowing reprocessing of failed downloads

### Case Verification Logic
- **`src/pacer/docket_processor.py`**: `verify_case()` method
  - `_is_explicitly_requested` logic uses artifact-only verification
  - Allows failed downloads to be retried by ignoring JSON skip reasons
  - Critical for `reprocess_failed` and `process_review_cases` functionality

**These paths were specifically designed to handle the complex interaction between:**
- Parallel browser contexts with separate download directories
- Failed download detection and reprocessing
- Artifact verification vs JSON content analysis
- Explicitly requested vs report-scraped case handling

## Database Schemas

The system uses DynamoDB tables with the following schemas:

### FBAdArchive
**Primary Key:** `AdArchiveID` (String), `StartDate` (String)
**GSIs:** StartDate-index, PageID-index, PageID-StartDate-index, PageID-LastUpdated-index, LastUpdated-index, AdArchiveID-index

Key fields: AdArchiveID, StartDate, PageID, PageName, LawFirm, LastUpdated, IsActive, EndDate, PublisherPlatform, LinkUrl, VideoHdUrl, VideoSdUrl, VideoPreviewImageUrl, OriginalImageUrl, ResizedImageUrl, LinkDescription, Body, Caption, CtaText, Title, S3ImageKey, ImageText, Summary, LLM, IsForbidden403, Category, Company, Product, Injuries, LitigationType, LitigationName, MdlName, AdCreativeId

### Pacer
**Primary Key:** `FilingDate` (String, YYYYMMDD), `DocketNum` (String)
**GSIs:** AddedOn-index, CourtId-DocketNum-index, MdlNum-FilingDate-index, TransfereeCourtId-TransfereeDocketNum-index, TransferorCourtId-TransferorDocketNum-index, LawFirm-FilingDate-index, CourtId-FilingDate-index

Key fields: FilingDate, DocketNum, CourtId, DateFiled, AddedDate, AddedDateIso, AddedOn, Versus, Cause, CauseFromReport, Nos, NosFromReport, SourcePage, Jurisdiction, JuryDemand, Office, AssignedTo, ReferredTo, Plaintiff, Plaintiffs, Defendant, Defendants, Attorney, Attorneys, PlaintiffsGpt, AttorneysGpt, LawFirm, LawFirms, BaseFilename, NewFilename, OriginalFilename, IsDownloaded, S3Html, S3Link, IsRemoval, IsTransferred, TransferredIn, PendingCto, TransfereeCourtId, TransfereeDocketNum, TransferorCourtId, TransferorDocketNum, TransferorDocketLawFirm, MdlNum, MdlCat, LeadCase, Title, Summary, Allegations, Flags, ReasonRelevant, HtmlOnly, ProcessingNotes

### LawFirms
**Primary Key:** `ID` (String), `Name` (String)

Fields: ID, Name, PageAlias, Category, ImageURI, AdArchiveLastUpdated, NumAds, LastUpdated

### FBImageHash
**Primary Key:** `PHash` (String), `AdArchiveID` (String)
**GSIs:** AdArchiveID-index

Fields: PHash, AdArchiveID, ImageUrl, S3ImageKey, CreatedDate, LastSeen

### DistrictCourts
**Primary Key:** `CourtId` (String), `MdlNum` (String)
**GSIs:** TransfereeCourtId-TransfereeDocketNum-index

Fields: CourtId, MdlNum, CourtName, District, State, TransfereeCourtId, TransfereeDocketNum, IsActive, LastUpdated

**Schema Notes:**
- Date format: YYYYMMDD for date fields
- Boolean fields properly converted to DynamoDB boolean type
- Field naming: PascalCase (e.g., AdArchiveID, StartDate)
- PHash-based image deduplication via FBImageHash table
- Extensive GSI usage for efficient querying