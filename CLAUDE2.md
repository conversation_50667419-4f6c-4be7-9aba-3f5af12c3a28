md = """
# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

LexGenius is a legal intelligence platform that monitors Facebook ads from law firms and court dockets through PACER. It processes this data to generate weekly reports combining ad campaign insights with litigation updates for mass tort cases.

## Architecture

The codebase follows a modular architecture:

- **`src/lib/fb_ads/`** - Facebook ad scraping, image processing, and classification
- **`src/lib/pacer/`** - Court docket monitoring and PACER integration  
- **`src/lib/reports/`** - Report generation and email distribution
- **`src/lib/data_transformer/`** - Data processing and enrichment
- **`src/lib/`** - AI integrations (gpt4_interface.py, deepseek_interface.py, llava_vision.py)
- **`src/lib/storage/`** - AWS S3 and DynamoDB management

Key architectural patterns:
- Asynchronous I/O operations throughout
- SQLite-based queue for deferred image processing
- PHash-based image deduplication
- Hybrid rule-based and ML classification for ads
- Multi-model AI support (GPT-4, DeepSeek, LLaMA)

# Comprehensive Software Engineering Best Practices

## 1. Fundamental Principles of Software Architecture

### 1.1 Core Architectural Principles

Software architecture serves as the foundation upon which all development work is built. Effective architecture adheres to these essential principles:

**Separation of Concerns**: Divide your system into distinct sections, each addressing a specific aspect of the functionality. This creates cleaner abstractions, simplifies maintenance, and enables parallel development.

**Single Responsibility Principle**: Each component should have one and only one reason to change. When a module has a single focus, it becomes more stable, understandable, and testable.

**Don't Repeat Yourself (DRY)**: Eliminate duplication by abstracting common functionality. Each piece of knowledge should have a single, unambiguous representation within a system.

**KISS (Keep It Simple, Stupid)**: Simplicity should be a key goal in design. Choose straightforward solutions over complex ones whenever possible. Simple solutions are easier to understand, maintain, and debug.

**YAGNI (You Aren't Gonna Need It)**: Avoid building functionality on speculation. Implement features only when they are needed, not when you anticipate they might be useful in the future.

**Open/Closed Principle**: Software entities should be open for extension but closed for modification. Design your systems so that new functionality can be added with minimal changes to existing code.

**Dependency Inversion**: High-level modules should not depend on low-level modules. Both should depend on abstractions. This principle enables flexibility and testability.

### 1.2 Architectural Patterns

Select architectural patterns based on the specific requirements and constraints of your project:

**Microservices Architecture**: Decompose applications into small, loosely-coupled services that can be developed, deployed, and scaled independently. Beneficial for large systems that require independent scaling of components.

**Layered Architecture**: Organize code into horizontal layers (e.g., presentation, business logic, data access) with strict dependencies flowing in one direction. Creates clear separation but can lead to unnecessary coupling if not carefully managed.

**Event-Driven Architecture**: Components communicate through events, enabling loose coupling and high scalability. Well-suited for systems with asynchronous processes and complex workflows.

**Domain-Driven Design (DDD)**: Align software design with the business domain through a shared language and focused domain models. Particularly valuable for complex business domains.

**Hexagonal/Ports and Adapters**: Isolate application core from external services through well-defined interfaces (ports) and implementations (adapters). Enhances testability and flexibility in integrating with external systems.

**Serverless Architecture**: Build applications using managed services without managing server infrastructure. Reduces operational complexity and can improve cost efficiency for appropriate workloads.

### 1.3 Design for Quality Attributes

Architecture must intentionally address these critical quality attributes:

**Performance**: Design for efficiency in response time, throughput, and resource utilization. Consider caching strategies, asynchronous processing, and data access optimization.

**Scalability**: Enable the system to handle increased load by adding resources. Design with horizontal scaling in mind, minimize shared state, and identify potential bottlenecks.

**Reliability**: Ensure the system functions correctly even under adverse conditions. Implement fault tolerance through redundancy, graceful degradation, and comprehensive error handling.

**Security**: Protect against unauthorized access and potential vulnerabilities. Apply security by design, implement proper authentication and authorization, and validate all inputs.

**Maintainability**: Create systems that can be easily modified and extended. Use clean code practices, comprehensive documentation, and automated testing.

**Testability**: Design components to be easily testable in isolation. Use dependency injection, interfaces, and clean separation of concerns to enable effective unit testing.

## 2. Systematic Problem-Solving and Debugging

### 2.1 Methodical Debugging Process

Effective debugging requires a disciplined, systematic approach:

**1. Reproduce the Issue**: Create a reliable, minimal test case that consistently demonstrates the problem. The ability to reproduce an issue is the foundation of effective debugging.

**2. Gather Information**: Collect relevant logs, error messages, stack traces, and system state information. More data leads to more accurate hypotheses.

**3. Analyze the Data**: Review the collected information to understand the context and behavior of the system when the issue occurs. Look for patterns and anomalies.

**4. Form Hypotheses**: Based on the available information, develop theories about what might be causing the issue. Prioritize hypotheses based on likelihood and impact.

**5. Test Hypotheses**: Design and execute tests to confirm or eliminate each hypothesis. Make one change at a time to maintain clarity about cause and effect.

**6. Implement and Verify**: Once the root cause is identified, implement a fix and verify that it resolves the issue without introducing new problems. Test in multiple scenarios to ensure the solution is robust.

**7. Document Findings**: Record the issue, its root cause, and the solution for future reference. This builds institutional knowledge and helps prevent similar issues.

### 2.2 Advanced Debugging Techniques

When facing complex issues, employ these powerful debugging approaches:

**Binary Search Debugging**: Systematically eliminate half of the potential problem space with each test. Particularly useful for finding issues in large codebases or data sets.

**Instrumentation**: Add logging, tracing, or metrics to code to gain visibility into its behavior during execution. Strategic instrumentation can reveal patterns not otherwise observable.

**Differential Debugging**: Compare working and non-working states to identify differences. This can involve comparing code versions, configurations, or environments.

**Rubber Duck Debugging**: Explain the problem aloud, line by line, to force a methodical review of the logic. This often reveals overlooked assumptions or logical errors.

**Root Cause Analysis**: Look beyond the immediate symptoms to understand the underlying cause. Use techniques like the "5 Whys" to drill down to fundamental issues.

**State Snapshot Analysis**: Capture the state of the system at critical points for later analysis. This is particularly valuable for intermittent issues that are difficult to reproduce.

### 2.3 Proactive Problem Prevention

The best debugging is the debugging you don't need to do:

**Code Reviews**: Implement thorough peer review processes to catch issues before they enter the codebase. Establish clear review standards focused on both functionality and quality.

**Static Analysis**: Use automated tools to identify potential issues, including security vulnerabilities, performance problems, and code quality concerns.

**Comprehensive Testing**: Build a test pyramid with unit tests, integration tests, and end-to-end tests to validate different aspects of the system. Aim for high test coverage of critical paths.

**Continuous Integration**: Automatically build and test code changes to detect integration issues early. Configure CI pipelines to fail fast on quality gates.

**Observability**: Implement logging, monitoring, and alerting to provide visibility into system behavior and quickly identify anomalies.

**Error Budgets**: Define acceptable reliability thresholds and track against them, balancing the need for rapid innovation with system stability.

## Common Commands

### Environment Setup
```bash
conda env create -f environment.yml
conda activate lexgenius
```

