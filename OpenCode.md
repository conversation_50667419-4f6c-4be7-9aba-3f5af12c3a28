## Project Overview
LexGenius monitors Facebook ads and PACER dockets for legal intelligence.

## Common Commands
### Environment
`conda env create -f environment.yml && conda activate lexgenius`

### Main Workflows
`python src/main.py --params config/<config_name>.yml` (e.g., scraper.yml, transform.yml, report.yml, weekly_report.yml)
`./run_pipeline.sh --config scraper,transform,report`

### Facebook Ad Processing
`./run_update_fb_campaigns.sh`
`python src/scripts/process_image_queue.py --process`
`python classify_all_ads.py`

### Testing
Run individual tests from root: `python test_classifier_simple.py`, `python test_hybrid_classifier.py`, `python test_dynamodb_upload.py`
No unified test runner. Place new test files in `test_files/`.

### Development Tools
`./clean_pycaches.sh`
`./find_import_errors.sh`
`./search_json_files.sh <term>`

## Code Style & Guidelines
- **Focus**: Only change code related to the task. Do not change unrelated behavior without asking first.
- **Modularity**: Adhere to existing structure (e.g., `src/lib/fb_ads/`, `src/lib/pacer/`, `src/lib/reports/`, `src/lib/data_transformer/`, AI integrations in `src/lib/`).
- **Reuse**:
    - DynamoDB: Use/extend managers in `src/lib/storage/`.
    - S3: Use/extend `S3Manager` or `S3ManagerAsync`.
    - AWS Services: Utilize existing integrations.
    - Utilities: Check `src/lib/utils/` before adding new functions.
- **Key Architectural Patterns**: Asynchronous I/O throughout. SQLite-based queue for deferred image processing. PHash-based image deduplication. Hybrid rule-based and ML classification for ads. Multi-model AI support (GPT-4, DeepSeek, LLaMA).
- **Configuration**: Use YAML with class-based `ScraperConfig` for validation. Key date parameter format MM/DD/YY.
- **Types & Naming**: Use Python type hints. Follow Python conventions (snake_case for functions/variables, PascalCase for classes).
- **Imports**: Group imports (standard library, third-party, local modules).
- **Error Handling**: Implement robust error handling and automatic retries where appropriate.
- **Important Notes**: Requires AWS/PACER credentials and API keys in `.env` file. Data directory configurable via `LEXGENIUS_DATA_DIR` (default structure: `data/YYYY/MM/DD/court_id/`).
- **Documentation**: Store all project documentation in the `docs/` directory.
