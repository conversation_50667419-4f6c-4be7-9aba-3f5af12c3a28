#!/bin/bash

# Nuclear option script to suppress macOS keychain prompts
# This is an aggressive approach to stop all keychain access prompts

echo "🔒 Suppressing macOS keychain prompts..."

# Kill any existing keychain-related processes
killall -KILL securityd 2>/dev/null || true
killall -KILL SecurityAgent 2>/dev/null || true

# Set environment variables system-wide
launchctl setenv WEBKIT_DISABLE_TCC_PROMPT 1
launchctl setenv WEBKIT_DISABLE_KEYCHAIN 1
launchctl setenv CHROME_DISABLE_KEYCHAIN 1
launchctl setenv CHROMIUM_NO_KEYCHAIN 1

# Temporarily disable keychain access for the current session
security unlock-keychain -p "" ~/Library/Keychains/login.keychain-db 2>/dev/null || true

# Create a temporary script to auto-deny keychain prompts
cat > /tmp/auto_deny_keychain.applescript << 'EOF'
on run
    repeat
        try
            tell application "System Events"
                if exists (processes where name is "SecurityAgent") then
                    tell process "SecurityAgent"
                        if exists button "Deny" of window 1 then
                            click button "Deny" of window 1
                        end if
                    end tell
                end if
            end tell
        end try
        delay 0.5
    end repeat
end run
EOF

# Start the auto-deny script in background
osascript /tmp/auto_deny_keychain.applescript &
AUTO_DENY_PID=$!

echo "🚫 Keychain suppression active (PID: $AUTO_DENY_PID)"
echo "📝 To stop suppression later, run: kill $AUTO_DENY_PID"
echo "💾 PID saved to /tmp/keychain_suppression_pid"
echo $AUTO_DENY_PID > /tmp/keychain_suppression_pid

# Optional: Return the PID so it can be killed later
echo $AUTO_DENY_PID