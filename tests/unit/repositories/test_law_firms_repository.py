"""
Unit tests for Law Firms Repository
"""
import pytest
from unittest.mock import Mock, AsyncMock
from botocore.exceptions import Client<PERSON>rror

from src.repositories.law_firms_repository import LawFirmsRepository


class TestLawFirmsRepository:
    """Test Law Firms repository operations"""
    
    @pytest.fixture
    def mock_storage(self):
        """Create mock storage"""
        storage = Mock()
        storage.put_item = AsyncMock()
        storage.get_item = AsyncMock()
        storage.query = AsyncMock()
        storage.scan = AsyncMock()
        storage.update_item = AsyncMock()
        storage.get_table = AsyncMock()
        return storage
    
    @pytest.fixture
    def repository(self, mock_storage):
        """Create repository with mock storage"""
        return LawFirmsRepository(mock_storage)
    
    @pytest.mark.asyncio
    async def test_add_or_update_record(self, repository, mock_storage):
        """Test adding/updating a law firm record"""
        record = {
            'ID': '123456',
            'Name': 'Test Law Firm',
            'PageAlias': 'testlawfirm'
        }
        
        mock_storage.put_item.return_value = None
        
        result = await repository.add_or_update_record(record)
        
        assert result is True
        mock_storage.put_item.assert_called_once_with('LawFirms', record)
    
    @pytest.mark.asyncio
    async def test_get_by_key(self, repository, mock_storage):
        """Test getting record by composite key"""
        firm_id = '123456'
        name = 'Test Law Firm'
        
        expected_record = {
            'ID': firm_id,
            'Name': name,
            'PageAlias': 'testlawfirm'
        }
        mock_storage.get_item.return_value = expected_record
        
        result = await repository.get_by_key(firm_id, name)
        
        assert result == expected_record
        mock_storage.get_item.assert_called_once_with(
            'LawFirms',
            {'ID': firm_id, 'Name': name}
        )
    
    @pytest.mark.asyncio
    async def test_get_by_id(self, repository, mock_storage):
        """Test getting all records for a firm ID"""
        firm_id = '123456'
        
        expected_records = [
            {'ID': firm_id, 'Name': 'Test Law Firm'},
            {'ID': firm_id, 'Name': 'Test Law Firm LLC'}
        ]
        mock_storage.query.return_value = expected_records
        
        result = await repository.get_by_id(firm_id)
        
        assert result == expected_records
    
    @pytest.mark.asyncio
    async def test_search_by_name(self, repository, mock_storage):
        """Test searching firms by name"""
        search_term = 'morgan'
        
        all_records = [
            {'ID': '1', 'Name': 'Morgan & Morgan'},
            {'ID': '2', 'Name': 'Smith Law Firm'},
            {'ID': '3', 'Name': 'Morgan Legal Group'}
        ]
        mock_storage.scan.return_value = all_records
        
        result = await repository.search_by_name(search_term)
        
        assert len(result) == 2
        assert all('morgan' in r['Name'].lower() for r in result)
    
    @pytest.mark.asyncio
    async def test_search_by_page_alias(self, repository, mock_storage):
        """Test searching firms by page alias"""
        search_term = 'legal'
        
        all_records = [
            {'ID': '1', 'Name': 'Test Firm', 'PageAlias': 'testlegal'},
            {'ID': '2', 'Name': 'Another Firm', 'PageAlias': 'anotherfirm'},
            {'ID': '3', 'Name': 'Legal Eagles', 'PageAlias': 'legaleagles'}
        ]
        mock_storage.scan.return_value = all_records
        
        result = await repository.search_by_page_alias(search_term)
        
        assert len(result) == 2
        assert all('legal' in r['PageAlias'].lower() for r in result)
    
    @pytest.mark.asyncio
    async def test_update_attributes(self, repository, mock_storage):
        """Test updating firm attributes"""
        firm_id = '123456'
        name = 'Test Law Firm'
        updates = {'Website': 'https://testfirm.com', 'State': 'TX'}
        
        mock_storage.update_item.return_value = None
        
        result = await repository.update_attributes(firm_id, name, updates)
        
        assert result is True
        mock_storage.update_item.assert_called_once_with(
            'LawFirms',
            {'ID': firm_id, 'Name': name},
            updates
        )
    
    @pytest.mark.asyncio
    async def test_scan_all_with_projection(self, repository, mock_storage):
        """Test scanning with projection expression"""
        projection = 'ID,Name,State'
        
        mock_table = Mock()
        mock_table.scan = AsyncMock(return_value={
            'Items': [
                {'ID': '1', 'Name': 'Firm 1', 'State': 'TX'},
                {'ID': '2', 'Name': 'Firm 2', 'State': 'FL'}
            ]
        })
        mock_storage.get_table.return_value = mock_table
        
        result = await repository.scan_all(projection_expression=projection)
        
        assert len(result) == 2
        mock_table.scan.assert_called_once_with(ProjectionExpression=projection)
    
    @pytest.mark.asyncio
    async def test_batch_update_last_updated(self, repository, mock_storage):
        """Test batch updating LastUpdated field"""
        new_date = '2024-12-10'
        
        all_records = [
            {'ID': '1', 'Name': 'Firm 1'},
            {'ID': '2', 'Name': 'Firm 2'}
        ]
        mock_storage.scan.return_value = all_records
        mock_storage.update_item.return_value = None
        
        result = await repository.batch_update_last_updated(new_date)
        
        assert result == 2
        assert mock_storage.update_item.call_count == 2
    
    @pytest.mark.asyncio
    async def test_delete_record(self, repository, mock_storage):
        """Test deleting a record"""
        firm_id = '123456'
        name = 'Test Law Firm'
        
        mock_table = Mock()
        mock_table.delete_item = AsyncMock()
        mock_storage.get_table.return_value = mock_table
        
        result = await repository.delete_record(firm_id, name)
        
        assert result is True
        mock_table.delete_item.assert_called_once_with(
            Key={'ID': firm_id, 'Name': name}
        )