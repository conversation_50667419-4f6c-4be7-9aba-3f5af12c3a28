"""
Unit tests for Facebook Ad Archive Repository
"""
import pytest
from unittest.mock import <PERSON><PERSON>, AsyncMock, MagicMock
from botocore.exceptions import ClientError

from src.repositories.fb_archive_repository import FBArchiveRepository


class TestFBArchiveRepository:
    """Test Facebook Ad Archive repository operations"""
    
    @pytest.fixture
    def mock_storage(self):
        """Create mock storage"""
        storage = Mock()
        storage.get_item = AsyncMock()
        storage.put_item = AsyncMock()
        storage.query = AsyncMock()
        storage.scan = AsyncMock()
        storage.update_item = AsyncMock()
        storage.get_table = AsyncMock()
        storage.dynamodb = Mock()
        return storage
    
    @pytest.fixture
    def repository(self, mock_storage):
        """Create repository with mock storage"""
        return FBArchiveRepository(mock_storage)
    
    @pytest.mark.asyncio
    async def test_add_or_update_record(self, repository, mock_storage):
        """Test adding or updating a record"""
        record = {
            'AdArchiveID': '123456789',
            'StartDate': '20241201',
            'PageID': '987654321',
            'LawFirm': 'Test Law Firm'
        }
        
        mock_storage.put_item.return_value = True
        
        result = await repository.add_or_update_record(record)
        
        assert result is True
        mock_storage.put_item.assert_called_once_with('FBAdArchive', record)
    
    @pytest.mark.asyncio
    async def test_get_by_key(self, repository, mock_storage):
        """Test getting record by primary key"""
        ad_id = '123456789'
        start_date = '20241201'
        
        expected_record = {
            'AdArchiveID': ad_id,
            'StartDate': start_date,
            'LawFirm': 'Test Law Firm'
        }
        
        mock_storage.get_item.return_value = expected_record
        
        result = await repository.get_by_key(ad_id, start_date)
        
        assert result == expected_record
        mock_storage.get_item.assert_called_once_with(
            'FBAdArchive',
            {'AdArchiveID': ad_id, 'StartDate': start_date}
        )
    
    @pytest.mark.asyncio
    async def test_query_by_start_date(self, repository, mock_storage):
        """Test querying by start date using GSI"""
        start_date = '20241201'
        
        # Mock storage.query response
        expected_records = [
            {'AdArchiveID': '123', 'StartDate': start_date},
            {'AdArchiveID': '456', 'StartDate': start_date}
        ]
        mock_storage.query.return_value = expected_records
        
        result = await repository.query_by_start_date(start_date)
        
        assert len(result) == 2
        assert result[0]['StartDate'] == start_date
        
        # Verify query was called with correct parameters
        from boto3.dynamodb.conditions import Key
        mock_storage.query.assert_called_once_with(
            table_name='FBAdArchive',
            key_condition=Key('StartDate').eq(start_date),
            index_name='StartDate-index'
        )
    
    @pytest.mark.asyncio
    async def test_query_by_page_id(self, repository, mock_storage):
        """Test querying by page ID"""
        page_id = '987654321'
        
        expected_records = [
            {'PageID': page_id, 'AdArchiveID': '123'},
            {'PageID': page_id, 'AdArchiveID': '456'}
        ]
        
        mock_storage.query.return_value = expected_records
        
        result = await repository.query_by_page_id(page_id)
        
        assert result == expected_records
        mock_storage.query.assert_called_once()
        
        # Verify correct index was used
        call_args = mock_storage.query.call_args
        assert call_args[1]['index_name'] == 'PageID-StartDate-index'
    
    @pytest.mark.asyncio
    async def test_batch_get_records(self, repository, mock_storage):
        """Test batch get multiple records"""
        keys = [
            {'AdArchiveID': '123', 'StartDate': '20241201'},
            {'AdArchiveID': '456', 'StartDate': '20241202'}
        ]
        
        # Mock table and batch get response
        mock_table = Mock()
        mock_storage.get_table.return_value = mock_table
        
        mock_storage.dynamodb.batch_get_item = AsyncMock(return_value={
            'Responses': {
                'FBAdArchive': [
                    {'AdArchiveID': '123', 'StartDate': '20241201', 'LawFirm': 'Firm A'},
                    {'AdArchiveID': '456', 'StartDate': '20241202', 'LawFirm': 'Firm B'}
                ]
            }
        })
        
        result = await repository.batch_get_records(keys)
        
        assert len(result) == 2
        assert result[0]['AdArchiveID'] == '123'
        assert result[1]['AdArchiveID'] == '456'
    
    @pytest.mark.asyncio
    async def test_batch_write_records(self, repository, mock_storage):
        """Test batch write multiple records"""
        records = [
            {'AdArchiveID': '123', 'StartDate': '20241201'},
            {'AdArchiveID': '456', 'StartDate': '20241202'}
        ]
        
        # Mock table with batch writer
        mock_table = Mock()
        mock_batch_writer = MagicMock()
        mock_batch_writer.__aenter__ = AsyncMock(return_value=mock_batch_writer)
        mock_batch_writer.__aexit__ = AsyncMock(return_value=None)
        mock_batch_writer.put_item = AsyncMock()
        
        mock_table.batch_writer.return_value = mock_batch_writer
        mock_storage.get_table.return_value = mock_table
        
        result = await repository.batch_write_records(records)
        
        assert result == 2  # All records written successfully
    
    @pytest.mark.asyncio
    async def test_update_attributes(self, repository, mock_storage):
        """Test updating specific attributes"""
        ad_id = '123456789'
        start_date = '20241201'
        updates = {
            'LawFirm': 'Updated Law Firm',
            'IsActive': False
        }
        
        mock_storage.update_item.return_value = True
        
        result = await repository.update_attributes(ad_id, start_date, updates)
        
        assert result is True
        mock_storage.update_item.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_remove_attributes(self, repository, mock_storage):
        """Test removing attributes from record"""
        ad_id = '123456789'
        start_date = '20241201'
        attributes = ['ImageText', 'CDNUrl']
        
        # Mock table
        mock_table = Mock()
        mock_table.update_item = AsyncMock()
        mock_storage.get_table.return_value = mock_table
        
        result = await repository.remove_attributes(ad_id, start_date, attributes)
        
        assert result is True
        mock_table.update_item.assert_called_once()
        
        # Verify REMOVE expression was used
        call_args = mock_table.update_item.call_args
        assert 'REMOVE' in call_args[1]['UpdateExpression']
    
    @pytest.mark.asyncio
    async def test_delete_record(self, repository, mock_storage):
        """Test deleting a single record"""
        ad_id = '123456789'
        start_date = '20241201'
        
        # Mock table
        mock_table = Mock()
        mock_table.delete_item = AsyncMock()
        mock_storage.get_table.return_value = mock_table
        
        result = await repository.delete_record(ad_id, start_date)
        
        assert result is True
        mock_table.delete_item.assert_called_once_with(
            Key={'AdArchiveID': ad_id, 'StartDate': start_date}
        )
    
    @pytest.mark.asyncio
    async def test_get_unique_values(self, repository, mock_storage):
        """Test getting unique values for an attribute"""
        attribute = 'LawFirm'
        
        mock_storage.scan.return_value = [
            {'LawFirm': 'Firm A', 'AdArchiveID': '123'},
            {'LawFirm': 'Firm B', 'AdArchiveID': '456'},
            {'LawFirm': 'Firm A', 'AdArchiveID': '789'}  # Duplicate
        ]
        
        result = await repository.get_unique_values(attribute)
        
        assert result == {'Firm A', 'Firm B'}
        assert len(result) == 2
    
    @pytest.mark.asyncio
    async def test_error_handling(self, repository, mock_storage):
        """Test error handling for ClientError"""
        mock_storage.put_item.side_effect = ClientError(
            {'Error': {'Code': 'ValidationException', 'Message': 'Invalid key'}},
            'PutItem'
        )
        
        result = await repository.add_or_update_record({'invalid': 'record'})
        
        assert result is False