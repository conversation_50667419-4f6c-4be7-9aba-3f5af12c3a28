"""
Tests for PacerRepository field mapping functionality
"""
import pytest
from unittest.mock import Mock, AsyncMock
from src.repositories.pacer_repository import PacerRepository


class TestPacerRepositoryFieldMapping:
    """Test field mapping in PacerRepository"""

    @pytest.fixture
    def mock_storage(self):
        """Create mock storage"""
        storage = Mock()
        storage.put_item = AsyncMock()
        return storage

    @pytest.fixture
    def repository(self, mock_storage):
        """Create repository with mock storage"""
        return PacerRepository(mock_storage)

    def test_map_fields_to_dynamodb_core_fields(self, repository):
        """Test mapping of core identification fields"""
        record = {
            'court_id': 'njd',
            'docket_num': '3:25-cv-10287',
            'filing_date': '20250612',
            'date_filed': '20250612'
        }
        
        mapped = repository._map_fields_to_dynamodb(record)
        
        assert mapped['CourtId'] == 'njd'
        assert mapped['DocketNum'] == '3:25-cv-10287'
        assert mapped['FilingDate'] == '20250612'
        assert mapped['DateFiled'] == '20250612'

    def test_map_fields_to_dynamodb_date_fields(self, repository):
        """Test mapping of date-related fields"""
        record = {
            'added_date': '2025-06-12',
            'added_date_iso': '20250612',
            'added_on': '20250612'
        }
        
        mapped = repository._map_fields_to_dynamodb(record)
        
        assert mapped['AddedDate'] == '2025-06-12'
        assert mapped['AddedDateIso'] == '20250612'
        assert mapped['AddedOn'] == '20250612'

    def test_map_fields_to_dynamodb_case_info_fields(self, repository):
        """Test mapping of case information fields"""
        record = {
            'versus': 'Pranger v. Johnson & Johnson et Al',
            'cause': '28:1332 Diversity-Product Liability',
            'cause_from_report': '28:1332 Diversity-Product Liability',
            'nos': '367 Personal Injury',
            'nos_from_report': '367 Personal Injury: Health Care/Pharmaceutical',
            'source_page': 'Saved Report',
            'jurisdiction': 'Diversity',
            'jury_demand': 'Plaintiff',
            'office': 'Trenton',
            'assigned_to': 'Judge Michael A. Shipp',
            'referred_to': 'Magistrate Judge Rukhsanah L. Singh'
        }
        
        mapped = repository._map_fields_to_dynamodb(record)
        
        assert mapped['Versus'] == 'Pranger v. Johnson & Johnson et Al'
        assert mapped['Cause'] == '28:1332 Diversity-Product Liability'
        assert mapped['CauseFromReport'] == '28:1332 Diversity-Product Liability'
        assert mapped['Nos'] == '367 Personal Injury'
        assert mapped['NosFromReport'] == '367 Personal Injury: Health Care/Pharmaceutical'
        assert mapped['SourcePage'] == 'Saved Report'
        assert mapped['Jurisdiction'] == 'Diversity'
        assert mapped['JuryDemand'] == 'Plaintiff'
        assert mapped['Office'] == 'Trenton'
        assert mapped['AssignedTo'] == 'Judge Michael A. Shipp'
        assert mapped['ReferredTo'] == 'Magistrate Judge Rukhsanah L. Singh'

    def test_map_fields_preserves_unmapped_fields(self, repository):
        """Test that unmapped fields are preserved as-is"""
        record = {
            'court_id': 'njd',
            'docket_num': '3:25-cv-10287',
            'filing_date': '20250612',
            'custom_field': 'custom_value',
            'another_field': 123,
            'nested_field': {'key': 'value'}
        }
        
        mapped = repository._map_fields_to_dynamodb(record)
        
        # Mapped fields should be transformed
        assert mapped['CourtId'] == 'njd'
        assert mapped['DocketNum'] == '3:25-cv-10287'
        assert mapped['FilingDate'] == '20250612'
        
        # Unmapped fields should be preserved
        assert mapped['custom_field'] == 'custom_value'
        assert mapped['another_field'] == 123
        assert mapped['nested_field'] == {'key': 'value'}

    @pytest.mark.asyncio
    async def test_add_or_update_record_with_field_mapping(self, repository, mock_storage):
        """Test that add_or_update_record properly maps fields before storage"""
        record = {
            'court_id': 'njd',
            'docket_num': '3:25-cv-10287',
            'filing_date': '20250612',
            'versus': 'Test Case',
            'added_on': '20250612'
        }
        
        result = await repository.add_or_update_record(record)
        
        assert result is True
        
        # Verify that put_item was called with mapped fields
        mock_storage.put_item.assert_called_once()
        call_args = mock_storage.put_item.call_args
        
        assert call_args[0][0] == 'Pacer'  # table name
        mapped_record = call_args[0][1]  # mapped record
        
        # Verify fields were mapped correctly
        assert mapped_record['CourtId'] == 'njd'
        assert mapped_record['DocketNum'] == '3:25-cv-10287'
        assert mapped_record['FilingDate'] == '20250612'
        assert mapped_record['Versus'] == 'Test Case'
        assert mapped_record['AddedOn'] == '20250612'

    @pytest.mark.asyncio
    async def test_add_or_update_record_missing_required_fields(self, repository, mock_storage):
        """Test that missing required fields cause validation failure"""
        # Missing FilingDate
        record = {
            'court_id': 'njd',
            'docket_num': '3:25-cv-10287',
            'versus': 'Test Case'
        }
        
        result = await repository.add_or_update_record(record)
        
        assert result is False
        mock_storage.put_item.assert_not_called()

    @pytest.mark.asyncio
    async def test_add_or_update_record_missing_docket_num(self, repository, mock_storage):
        """Test that missing DocketNum causes validation failure"""
        # Missing DocketNum
        record = {
            'court_id': 'njd',
            'filing_date': '20250612',
            'versus': 'Test Case'
        }
        
        result = await repository.add_or_update_record(record)
        
        assert result is False
        mock_storage.put_item.assert_not_called()

    @pytest.mark.asyncio
    async def test_add_or_update_record_with_cleaned_docket_num(self, repository, mock_storage):
        """Test that cleaned docket numbers work correctly"""
        # Simulate record with cleaned docket number (13 characters)
        record = {
            'court_id': 'njd',
            'docket_num': '3:25-cv-10287',  # Already cleaned to 13 characters
            'filing_date': '20250612',
            'versus': 'Test Case',
            'added_on': '20250612'
        }
        
        result = await repository.add_or_update_record(record)
        
        assert result is True
        
        # Verify the cleaned docket number is properly stored
        call_args = mock_storage.put_item.call_args
        mapped_record = call_args[0][1]
        
        assert mapped_record['DocketNum'] == '3:25-cv-10287'
        assert len(mapped_record['DocketNum']) == 13

    def test_field_mapping_comprehensive_real_world_record(self, repository):
        """Test field mapping with a comprehensive real-world record"""
        # Real record structure from the logs
        record = {
            'court_id': 'njd',
            'docket_num': '3:25-cv-10287',
            'versus': 'Pranger v. Johnson & Johnson et Al',
            'filing_date': '20250612',
            'added_date': '2025-06-12',
            'added_date_iso': '20250612',
            'source_page': 'Saved Report',
            'cause_from_report': '28:1332 Diversity-Product Liability',
            'nos_from_report': '367 Personal Injury: Health Care/Pharmaceutical Personal Injury Product Liability',
            'flags': ['MDL2738'],
            'office': 'Trenton',
            'assigned_to': 'Judge Michael A. Shipp',
            'referred_to': 'Magistrate Judge Rukhsanah L. Singh',
            'cause': '28:1332 Diversity-Product Liability',
            'jury_demand': 'Plaintiff',
            'nos': '367 Personal Injury',
            'jurisdiction': 'Diversity',
            'plaintiff': ['Gaylon Pranger'],
            'attorney': [{'attorney_name': 'RYAN M. MCINTOSH', 'law_firm': 'ANDREWS & THORNTON'}],
            'defendant': ['Johnson & Johnson', 'Johnson & Johnson Consumer INC'],
            'mdl_num': '2738',
            'is_removal': False,
            'law_firms': ['Andrews & Thornton'],
            'law_firm': 'Andrews & Thornton',
            'added_on': '20250612'
        }
        
        mapped = repository._map_fields_to_dynamodb(record)
        
        # Verify key mappings
        assert mapped['CourtId'] == 'njd'
        assert mapped['DocketNum'] == '3:25-cv-10287'
        assert mapped['FilingDate'] == '20250612'
        assert mapped['AddedOn'] == '20250612'
        assert mapped['Versus'] == 'Pranger v. Johnson & Johnson et Al'
        assert mapped['AssignedTo'] == 'Judge Michael A. Shipp'
        
        # Verify unmapped fields are preserved
        assert mapped['flags'] == ['MDL2738']
        assert mapped['plaintiff'] == ['Gaylon Pranger']
        assert mapped['attorney'] == [{'attorney_name': 'RYAN M. MCINTOSH', 'law_firm': 'ANDREWS & THORNTON'}]