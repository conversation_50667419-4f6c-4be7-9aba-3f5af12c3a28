"""
Unit tests for District Courts Repository
"""
import pytest
from unittest.mock import Mock, AsyncMock
from botocore.exceptions import ClientError

from src.repositories.district_courts_repository import DistrictCourtsRepository


class TestDistrictCourtsRepository:
    """Test District Courts repository operations"""
    
    @pytest.fixture
    def mock_storage(self):
        """Create mock storage"""
        storage = Mock()
        storage.put_item = AsyncMock()
        storage.get_item = AsyncMock()
        storage.query = AsyncMock()
        storage.scan = AsyncMock()
        storage.update_item = AsyncMock()
        storage.get_table = AsyncMock()
        return storage
    
    @pytest.fixture
    def repository(self, mock_storage):
        """Create repository with mock storage"""
        return DistrictCourtsRepository(mock_storage)
    
    @pytest.mark.asyncio
    async def test_add_or_update_record_success(self, repository, mock_storage):
        """Test successful record addition"""
        record = {
            'CourtId': 'TXND',
            'MdlNum': '2873',
            'Title': 'AFFF MDL'
        }
        
        mock_storage.put_item.return_value = None
        
        result = await repository.add_or_update_record(record)
        
        assert result is True
        mock_storage.put_item.assert_called_once_with('DistrictCourts', record)
    
    @pytest.mark.asyncio
    async def test_add_or_update_record_failure(self, repository, mock_storage):
        """Test failed record addition"""
        record = {'CourtId': 'TXND', 'MdlNum': '2873'}
        
        mock_storage.put_item.side_effect = ClientError(
            {'Error': {'Code': 'ValidationException'}}, 'PutItem'
        )
        
        result = await repository.add_or_update_record(record)
        
        assert result is False
    
    @pytest.mark.asyncio
    async def test_get_by_key(self, repository, mock_storage):
        """Test getting record by key"""
        court_id = 'TXND'
        mdl_num = '2873'
        
        expected_record = {
            'CourtId': court_id,
            'MdlNum': mdl_num,
            'Title': 'AFFF MDL'
        }
        mock_storage.get_item.return_value = expected_record
        
        result = await repository.get_by_key(court_id, mdl_num)
        
        assert result == expected_record
        mock_storage.get_item.assert_called_once_with(
            'DistrictCourts',
            {'CourtId': court_id, 'MdlNum': mdl_num}
        )
    
    @pytest.mark.asyncio
    async def test_query_by_court_id(self, repository, mock_storage):
        """Test querying by court ID"""
        court_id = 'TXND'
        
        expected_records = [
            {'CourtId': court_id, 'MdlNum': '2873'},
            {'CourtId': court_id, 'MdlNum': '2874'}
        ]
        mock_storage.query.return_value = expected_records
        
        result = await repository.query_by_court_id(court_id)
        
        assert result == expected_records
        
        # Verify query was called with Key condition
        from boto3.dynamodb.conditions import Key
        mock_storage.query.assert_called_once()
        args = mock_storage.query.call_args[0]
        assert args[0] == 'DistrictCourts'
    
    @pytest.mark.asyncio
    async def test_query_by_transferee_court_and_docket(self, repository, mock_storage):
        """Test querying by transferee court and docket"""
        court_id = 'SCED'
        docket_num = '4:21-md-02873'
        
        expected_records = [
            {
                'CourtId': 'TXND',
                'MdlNum': '2873',
                'TransfereeCourtId': court_id,
                'TransfereeDocketNum': docket_num
            }
        ]
        mock_storage.query.return_value = expected_records
        
        result = await repository.query_by_transferee_court_and_docket(
            court_id, docket_num
        )
        
        assert result == expected_records
        
        # Verify GSI was used
        kwargs = mock_storage.query.call_args[1]
        assert kwargs.get('index_name') == 'TransfereeCourtId-TransfereeDocketNum-index'
    
    @pytest.mark.asyncio
    async def test_update_attributes(self, repository, mock_storage):
        """Test updating record attributes"""
        court_id = 'TXND'
        mdl_num = '2873'
        updates = {'Title': 'Updated Title', 'Status': 'Active'}
        
        mock_storage.update_item.return_value = None
        
        result = await repository.update_attributes(court_id, mdl_num, updates)
        
        assert result is True
        mock_storage.update_item.assert_called_once_with(
            'DistrictCourts',
            {'CourtId': court_id, 'MdlNum': mdl_num},
            updates
        )
    
    @pytest.mark.asyncio
    async def test_scan_all(self, repository, mock_storage):
        """Test scanning all records"""
        expected_records = [
            {'CourtId': 'TXND', 'MdlNum': '2873'},
            {'CourtId': 'FLSD', 'MdlNum': '2874'}
        ]
        mock_storage.scan.return_value = expected_records
        
        result = await repository.scan_all()
        
        assert result == expected_records
        mock_storage.scan.assert_called_once_with('DistrictCourts')
    
    @pytest.mark.asyncio
    async def test_delete_record(self, repository, mock_storage):
        """Test deleting a record"""
        court_id = 'TXND'
        mdl_num = '2873'
        
        mock_table = Mock()
        mock_table.delete_item = AsyncMock()
        mock_storage.get_table.return_value = mock_table
        
        result = await repository.delete_record(court_id, mdl_num)
        
        assert result is True
        mock_table.delete_item.assert_called_once_with(
            Key={'CourtId': court_id, 'MdlNum': mdl_num}
        )