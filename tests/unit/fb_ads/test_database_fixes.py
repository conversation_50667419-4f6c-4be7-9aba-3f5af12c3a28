"""
Unit tests for Facebook Ads DynamoDB update fixes.

This test suite covers the specific fixes implemented to resolve
the issue where FB ads were failing to update existing DynamoDB items.

Test Categories:
1. PascalCase conversion fixes
2. AI enhancement feature flag
3. Sample firm development mode
4. Database operation fixes
"""
import pytest
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from typing import Dict, Any, List

from src.fb_ads.services.ad_processing_service import AdProcessingService
from src.fb_ads.services.data_validation_service import DataValidationService
from src.fb_ads.processor import AdProcessor


class TestPascalCaseConversionFixes:
    """Test the PascalCase conversion fixes for DynamoDB primary keys."""
    
    @pytest.fixture
    def mock_config(self):
        return {
            'iso_date': '20250612',
            'default_date_range_days': 14,
            'verbose': False
        }
    
    @pytest.fixture
    def mock_logger(self):
        return Mock()
    
    @pytest.fixture
    def ad_processing_service(self, mock_config, mock_logger):
        service = AdProcessingService(mock_config, mock_logger)
        service.law_firms_repo = AsyncMock()
        service.fb_archive_repo = AsyncMock()
        return service
    
    def test_convert_to_pascal_case_special_mappings(self, ad_processing_service):
        """Test that special field mappings work correctly for DynamoDB schema."""
        # Test data with snake_case keys that need special handling
        test_data = {
            'ad_archive_id': '123456789',
            'start_date': '20250612',
            'end_date': '20250613',
            'law_firm': 'Test Law Firm',
            'page_id': '987654321',
            'page_name': 'Test Page',
            'is_active': True,
            'publisher_platform': 'Facebook',
            'link_url': 'https://example.com',
            'link_description': 'Test link',
            'last_updated': '20250612',
            's3_image_key': 'images/test.jpg',
            'ad_creative_id': '111222333'
        }
        
        result = ad_processing_service._convert_to_pascal_case(test_data)
        
        # Verify special mappings for critical DynamoDB fields
        assert result['AdArchiveID'] == '123456789'  # NOT AdArchiveId
        assert result['StartDate'] == '20250612'      # NOT StartDate
        assert result['EndDate'] == '20250613'
        assert result['LawFirm'] == 'Test Law Firm'
        assert result['PageID'] == '987654321'        # NOT PageId
        assert result['PageName'] == 'Test Page'
        assert result['IsActive'] is True
        assert result['PublisherPlatform'] == 'Facebook'
        assert result['LinkUrl'] == 'https://example.com'
        assert result['LinkDescription'] == 'Test link'
        assert result['LastUpdated'] == '20250612'
        assert result['S3ImageKey'] == 'images/test.jpg'
        assert result['AdCreativeID'] == '111222333'  # NOT AdCreativeId
    
    def test_convert_to_pascal_case_standard_conversion(self, ad_processing_service):
        """Test standard snake_case to PascalCase conversion for non-special fields."""
        test_data = {
            'title': 'Test Title',
            'body': 'Test Body',
            'video_hd_url': 'https://video.com/hd.mp4',
            'some_other_field': 'test value',
            'field_with_multiple_underscores': 'test'
        }
        
        result = ad_processing_service._convert_to_pascal_case(test_data)
        
        assert result['Title'] == 'Test Title'
        assert result['Body'] == 'Test Body'
        assert result['VideoHdUrl'] == 'https://video.com/hd.mp4'
        assert result['SomeOtherField'] == 'test value'
        assert result['FieldWithMultipleUnderscores'] == 'test'
    
    def test_convert_to_pascal_case_already_pascal(self, ad_processing_service):
        """Test conversion of fields that are already in PascalCase."""
        test_data = {
            'Title': 'Test Title',
            'Summary': 'Test Summary',
            'LLM': 'deepseek',
            'Category': 'Mass Tort'
        }
        
        result = ad_processing_service._convert_to_pascal_case(test_data)
        
        assert result['Title'] == 'Test Title'
        assert result['Summary'] == 'Test Summary'
        assert result['Llm'] == 'deepseek'  # Should capitalize first letter
        assert result['Category'] == 'Mass Tort'
    
    def test_convert_to_pascal_case_empty_and_none_values(self, ad_processing_service):
        """Test conversion with empty and None values."""
        test_data = {
            'ad_archive_id': '123456789',
            'empty_field': '',
            'none_field': None,
            'zero_field': 0,
            'false_field': False
        }
        
        result = ad_processing_service._convert_to_pascal_case(test_data)
        
        assert result['AdArchiveID'] == '123456789'
        assert result['EmptyField'] == ''
        assert result['NoneField'] is None
        assert result['ZeroField'] == 0
        assert result['FalseField'] is False
    
    @pytest.mark.asyncio
    async def test_database_operations_with_correct_keys(self, ad_processing_service):
        """Test that database operations use correct PascalCase keys."""
        # Mock processed ads with snake_case keys
        processed_ads = [
            {
                'ad_archive_id': '123456789',
                'start_date': '20250612',
                'law_firm': 'Test Firm',
                'title': 'Test Ad',
                'is_active': True
            }
        ]
        
        # Mock repository responses
        ad_processing_service.fb_archive_repo.get_item.return_value = None  # New item
        ad_processing_service.fb_archive_repo.put_item.return_value = True
        ad_processing_service.law_firms_repo.add_or_update_record.return_value = True
        
        result = await ad_processing_service._process_database_operations(
            processed_ads, 'Test Firm', 'firm123', True
        )
        
        # Verify the method was called with properly converted keys
        put_call_args = ad_processing_service.fb_archive_repo.put_item.call_args[0][0]
        
        # Critical: These must be exact matches for DynamoDB primary key
        assert 'AdArchiveID' in put_call_args
        assert 'StartDate' in put_call_args
        assert put_call_args['AdArchiveID'] == '123456789'
        assert put_call_args['StartDate'] == '20250612'
        assert put_call_args['LawFirm'] == 'Test Firm'
        assert put_call_args['Title'] == 'Test Ad'
        assert put_call_args['IsActive'] is True
        
        # Verify results
        items_processed, items_updated, items_added = result
        assert items_processed == 1
        assert items_updated == 0
        assert items_added == 1
    
    def test_law_firms_repository_method_fix(self, ad_processing_service):
        """Test that law firm updates use the correct repository method."""
        # This test verifies that we use add_or_update_record instead of put_item
        # for the LawFirms repository (which was causing the error)
        
        # The method name should be accessible for verification
        law_firms_repo_methods = dir(ad_processing_service.law_firms_repo)
        
        # This isn't a direct test but documents the expected interface
        # In actual usage, we verify through integration tests
        assert hasattr(ad_processing_service.law_firms_repo, 'add_or_update_record')


class TestAIEnhancementFeatureFlag:
    """Test AI enhancement feature flag implementation."""
    
    @pytest.fixture
    def mock_config_ai_enabled(self):
        return {
            'feature_flags': {
                'enable_ai_enhancement': True
            },
            'current_process_date': '20250612',
            'use_tqdm': False
        }
    
    @pytest.fixture
    def mock_config_ai_disabled(self):
        return {
            'feature_flags': {
                'enable_ai_enhancement': False
            },
            'current_process_date': '20250612',
            'use_tqdm': False
        }
    
    @pytest.fixture
    def mock_dependencies(self):
        return {
            'image_handler': Mock(),
            'ai_integrator': AsyncMock(),
            'vector_clusterer': Mock(),
            'fb_ad_db': Mock(),
            's3_manager': Mock()
        }
    
    def test_ai_enhancement_enabled_flag_check(self, mock_config_ai_enabled, mock_dependencies):
        """Test that AI enhancement flag is properly checked when enabled."""
        processor = AdProcessor(
            config=mock_config_ai_enabled,
            current_process_date='20250612',
            **mock_dependencies
        )
        
        # Verify flag reading
        ai_enabled = processor.config.get('feature_flags', {}).get('enable_ai_enhancement', True)
        assert ai_enabled is True
    
    def test_ai_enhancement_disabled_flag_check(self, mock_config_ai_disabled, mock_dependencies):
        """Test that AI enhancement flag is properly checked when disabled."""
        processor = AdProcessor(
            config=mock_config_ai_disabled,
            current_process_date='20250612',
            **mock_dependencies
        )
        
        # Verify flag reading
        ai_enabled = processor.config.get('feature_flags', {}).get('enable_ai_enhancement', True)
        assert ai_enabled is False
    
    @pytest.mark.asyncio
    async def test_process_raw_ads_with_ai_enabled(self, mock_config_ai_enabled, mock_dependencies):
        """Test ad processing when AI enhancement is enabled."""
        # Mock AI integrator to return enhanced ads
        mock_dependencies['ai_integrator'].enhance_ad_data.return_value = {
            'ad_archive_id': '123',
            'title': 'Test Ad',
            'summary': 'AI generated summary',
            'campaign': 'Test Campaign'
        }
        
        # Mock vector clusterer
        mock_dependencies['vector_clusterer'].get_category_for_ad_data = Mock()
        
        processor = AdProcessor(
            config=mock_config_ai_enabled,
            current_process_date='20250612',
            **mock_dependencies
        )
        
        # Mock raw ad groups
        raw_ad_groups = [[{
            'adArchiveID': '123',
            'pageName': 'Test Firm',
            'startDate': 1640995200,  # Unix timestamp
            'isActive': True,
            'snapshot': {
                'cards': [{'title': 'Test Ad', 'body': 'Test content'}]
            }
        }]]
        
        result = await processor.process_raw_ads(raw_ad_groups, 'Test Firm')
        
        # Verify AI integrator was called
        mock_dependencies['ai_integrator'].enhance_ad_data.assert_called_once()
        
        # Verify vector clusterer was called for rule-based categorization
        mock_dependencies['vector_clusterer'].get_category_for_ad_data.assert_called_once()
        
        # Verify result
        assert len(result) == 1
        assert result[0]['ad_archive_id'] == '123'
    
    @pytest.mark.asyncio
    async def test_process_raw_ads_with_ai_disabled(self, mock_config_ai_disabled, mock_dependencies):
        """Test ad processing when AI enhancement is disabled."""
        # Mock vector clusterer
        mock_dependencies['vector_clusterer'].get_category_for_ad_data = Mock()
        
        processor = AdProcessor(
            config=mock_config_ai_disabled,
            current_process_date='20250612',
            **mock_dependencies
        )
        
        # Mock raw ad groups
        raw_ad_groups = [[{
            'adArchiveID': '123',
            'pageName': 'Test Firm',
            'startDate': 1640995200,  # Unix timestamp
            'isActive': True,
            'snapshot': {
                'cards': [{'title': 'Test Ad', 'body': 'Test content'}]
            }
        }]]
        
        result = await processor.process_raw_ads(raw_ad_groups, 'Test Firm')
        
        # Verify AI integrator was NOT called
        mock_dependencies['ai_integrator'].enhance_ad_data.assert_not_called()
        
        # Verify vector clusterer was still called for rule-based categorization
        mock_dependencies['vector_clusterer'].get_category_for_ad_data.assert_called_once()
        
        # Verify result has basic summary
        assert len(result) == 1
        assert result[0]['ad_archive_id'] == '123'
        assert 'summary' in result[0]  # Should have basic summary when AI disabled
    
    @pytest.mark.asyncio
    async def test_basic_summary_generation_when_ai_disabled(self, mock_config_ai_disabled, mock_dependencies):
        """Test that basic summaries are generated when AI is disabled."""
        mock_dependencies['vector_clusterer'].get_category_for_ad_data = Mock()
        
        processor = AdProcessor(
            config=mock_config_ai_disabled,
            current_process_date='20250612',
            **mock_dependencies
        )
        
        # Test with title
        raw_ad_groups = [[{
            'adArchiveID': '123',
            'pageName': 'Test Firm',
            'startDate': 1640995200,
            'isActive': True,
            'snapshot': {
                'cards': [{'title': 'This is a very long title that should be truncated for summary', 'body': 'Test content'}]
            }
        }]]
        
        result = await processor.process_raw_ads(raw_ad_groups, 'Test Firm')
        
        # Verify basic summary from title (truncated to 50 chars)
        assert len(result) == 1
        assert result[0]['summary'] == 'This is a very long title that should be truncated...'
    
    @pytest.mark.asyncio
    async def test_fallback_summary_from_body_when_ai_disabled(self, mock_config_ai_disabled, mock_dependencies):
        """Test that basic summaries are generated when AI is disabled (simplified test)."""
        mock_dependencies['vector_clusterer'].get_category_for_ad_data = Mock()
        
        processor = AdProcessor(
            config=mock_config_ai_disabled,
            current_process_date='20250612',
            **mock_dependencies
        )
        
        # Test with short title and body content
        raw_ad_groups = [[{
            'adArchiveID': '123',
            'pageName': 'Test Firm',
            'startDate': 1640995200,
            'isActive': True,
            'snapshot': {
                'cards': [{'title': 'Short', 'body': 'This is the body content that should be used for summary'}]
            }
        }]]
        
        result = await processor.process_raw_ads(raw_ad_groups, 'Test Firm')
        
        # Verify basic summary was generated
        assert len(result) == 1
        assert 'summary' in result[0]
        assert result[0]['summary'] == 'Short'  # Short title used as summary


class TestSampleFirmDevelopmentMode:
    """Test sample firm development mode functionality."""
    
    @pytest.fixture
    def mock_config_dev_mode(self):
        return {
            'iso_date': '20250612',
            'skip_firms_updated_today': True,
            'shuffle_firm_order': True,
            'development': {
                'sample_firms': ['301100319750499', '123456789'],
                'max_test_firms': 1
            }
        }
    
    @pytest.fixture
    def mock_config_production_mode(self):
        return {
            'iso_date': '20250612',
            'skip_firms_updated_today': True,
            'shuffle_firm_order': True,
            'development': {
                'sample_firms': [],  # Empty means production mode
                'max_test_firms': None
            }
        }
    
    @pytest.fixture
    def mock_logger(self):
        return Mock()
    
    @pytest.fixture
    def mock_processing_tracker(self):
        tracker = Mock()
        tracker.get_skip_list.return_value = set()
        return tracker
    
    def test_development_mode_sample_firm_filtering(self, mock_config_dev_mode, mock_logger, mock_processing_tracker):
        """Test that sample firms are properly filtered in development mode."""
        with patch.object(DataValidationService, '_load_ignore_firms', return_value={}):
            service = DataValidationService(mock_config_dev_mode, mock_logger, mock_processing_tracker)
        
        # Mock all firms data
        all_firms = [
            {'ID': '301100319750499', 'Name': 'Levy Konigsberg LLP', 'AdArchiveLastUpdated': '20250611'},
            {'ID': '123456789', 'Name': 'Test Firm', 'AdArchiveLastUpdated': '20250611'},
            {'ID': '987654321', 'Name': 'Another Firm', 'AdArchiveLastUpdated': '20250611'},
            {'ID': '555666777', 'Name': 'Fourth Firm', 'AdArchiveLastUpdated': '20250611'}
        ]
        
        result = service.filter_law_firms(all_firms)
        
        # Should only return sample firms, limited by max_test_firms
        assert len(result) == 1  # Limited by max_test_firms: 1
        assert result[0]['ID'] in ['301100319750499', '123456789']  # One of the sample firms
        
        # Verify development mode logging
        mock_logger.info.assert_any_call('Development mode: Testing with sample firms: [\'301100319750499\', \'123456789\']')
        mock_logger.info.assert_any_call('Limited to 1 test firms')
        mock_logger.info.assert_any_call('Development mode: Processing 1 sample firms')
    
    def test_development_mode_all_sample_firms_when_no_limit(self, mock_logger, mock_processing_tracker):
        """Test that all sample firms are returned when no max_test_firms limit."""
        config_no_limit = {
            'iso_date': '20250612',
            'skip_firms_updated_today': True,
            'shuffle_firm_order': True,
            'development': {
                'sample_firms': ['301100319750499', '123456789'],
                'max_test_firms': None  # No limit
            }
        }
        
        with patch.object(DataValidationService, '_load_ignore_firms', return_value={}):
            service = DataValidationService(config_no_limit, mock_logger, mock_processing_tracker)
        
        all_firms = [
            {'ID': '301100319750499', 'Name': 'Levy Konigsberg LLP', 'AdArchiveLastUpdated': '20250611'},
            {'ID': '123456789', 'Name': 'Test Firm', 'AdArchiveLastUpdated': '20250611'},
            {'ID': '987654321', 'Name': 'Another Firm', 'AdArchiveLastUpdated': '20250611'}
        ]
        
        result = service.filter_law_firms(all_firms)
        
        # Should return both sample firms
        assert len(result) == 2
        result_ids = {firm['ID'] for firm in result}
        assert result_ids == {'301100319750499', '123456789'}
    
    def test_development_mode_sample_firms_not_found(self, mock_config_dev_mode, mock_logger, mock_processing_tracker):
        """Test behavior when sample firms are not found in database."""
        with patch.object(DataValidationService, '_load_ignore_firms', return_value={}):
            service = DataValidationService(mock_config_dev_mode, mock_logger, mock_processing_tracker)
        
        # Mock all firms data without the sample firms
        all_firms = [
            {'ID': '987654321', 'Name': 'Another Firm', 'AdArchiveLastUpdated': '20250611'},
            {'ID': '555666777', 'Name': 'Fourth Firm', 'AdArchiveLastUpdated': '20250611'}
        ]
        
        result = service.filter_law_firms(all_firms)
        
        # Should return empty list and log warning
        assert result == []
        mock_logger.warning.assert_called_with('No sample firms found in database. Requested: [\'301100319750499\', \'123456789\']')
    
    def test_production_mode_ignores_sample_firms(self, mock_config_production_mode, mock_logger, mock_processing_tracker):
        """Test that production mode ignores sample_firms configuration."""
        with patch.object(DataValidationService, '_load_ignore_firms', return_value={}):
            service = DataValidationService(mock_config_production_mode, mock_logger, mock_processing_tracker)
        
        all_firms = [
            {'ID': '301100319750499', 'Name': 'Levy Konigsberg LLP', 'AdArchiveLastUpdated': '20250611'},
            {'ID': '123456789', 'Name': 'Test Firm', 'AdArchiveLastUpdated': '20250611'},
            {'ID': '987654321', 'Name': 'Another Firm', 'AdArchiveLastUpdated': '20250611'}
        ]
        
        with patch('random.shuffle'):  # Prevent random shuffling for predictable test
            result = service.filter_law_firms(all_firms)
        
        # Should return all firms (normal production filtering)
        assert len(result) == 3
        result_ids = {firm['ID'] for firm in result}
        assert result_ids == {'301100319750499', '123456789', '987654321'}
        
        # Should not have development mode logging
        development_calls = [call for call in mock_logger.info.call_args_list 
                           if 'Development mode' in str(call)]
        assert len(development_calls) == 0


class TestDatabaseOperationFixes:
    """Test database operation fixes and error handling."""
    
    @pytest.fixture
    def mock_config(self):
        return {
            'iso_date': '20250612',
            'default_date_range_days': 14,
            'verbose': False
        }
    
    @pytest.fixture
    def mock_logger(self):
        return Mock()
    
    @pytest.fixture
    def ad_processing_service(self, mock_config, mock_logger):
        service = AdProcessingService(mock_config, mock_logger)
        service.law_firms_repo = AsyncMock()
        service.fb_archive_repo = AsyncMock()
        return service
    
    @pytest.mark.asyncio
    async def test_database_operations_success_logging(self, ad_processing_service):
        """Test that database operations log success correctly."""
        processed_ads = [
            {
                'ad_archive_id': '123456789',
                'start_date': '20250612',
                'law_firm': 'Test Firm',
                'title': 'Test Ad'
            }
        ]
        
        # Mock successful operations
        ad_processing_service.fb_archive_repo.get_item.return_value = None
        ad_processing_service.fb_archive_repo.put_item.return_value = True
        ad_processing_service.law_firms_repo.add_or_update_record.return_value = True
        
        result = await ad_processing_service._process_database_operations(
            processed_ads, 'Test Firm', 'firm123', True
        )
        
        # Verify success logging
        ad_processing_service._logger.info.assert_any_call(
            'FBAdArchive database save completed: 1 success, 0 failures'
        )
        ad_processing_service._logger.info.assert_any_call(
            'Success rate: 100.0% (1/1)'
        )
        ad_processing_service._logger.info.assert_any_call(
            'All items saved successfully to FBAdArchive'
        )
        ad_processing_service._logger.info.assert_any_call(
            'Updated law firm Test Firm record'
        )
    
    @pytest.mark.asyncio
    async def test_database_operations_detailed_logging(self, ad_processing_service):
        """Test detailed logging during database operations."""
        processed_ads = [
            {
                'ad_archive_id': '123456789',
                'start_date': '20250612',
                'law_firm': 'Test Firm',
                'title': 'Test Ad'
            }
        ]
        
        # Mock successful operations
        ad_processing_service.fb_archive_repo.get_item.return_value = None
        ad_processing_service.fb_archive_repo.put_item.return_value = True
        ad_processing_service.law_firms_repo.add_or_update_record.return_value = True
        
        await ad_processing_service._process_database_operations(
            processed_ads, 'Test Firm', 'firm123', True
        )
        
        # Verify detailed logging for debugging
        ad_processing_service._logger.info.assert_any_call(
            'Sample item structure: AdArchiveID=123456789, StartDate=20250612, PageID=None, LawFirm=Test Firm'
        )
        ad_processing_service._logger.info.assert_any_call(
            'Saving item 1/1: AdArchiveID=123456789, StartDate=20250612'
        )
        ad_processing_service._logger.info.assert_any_call(
            'Operation type for 123456789: INSERT'
        )
    
    @pytest.mark.asyncio
    async def test_database_operations_update_vs_insert_detection(self, ad_processing_service):
        """Test detection of UPDATE vs INSERT operations."""
        processed_ads = [
            {
                'ad_archive_id': '123456789',
                'start_date': '20250612',
                'law_firm': 'Test Firm',
                'title': 'Updated Ad'
            }
        ]
        
        # Mock existing item (should be UPDATE)
        existing_item = {
            'AdArchiveID': '123456789',
            'StartDate': '20250612',
            'LawFirm': 'Test Firm',
            'Title': 'Old Ad',
            'LastUpdated': '20250611'
        }
        ad_processing_service.fb_archive_repo.get_item.return_value = existing_item
        ad_processing_service.fb_archive_repo.put_item.return_value = True
        ad_processing_service.law_firms_repo.add_or_update_record.return_value = True
        
        await ad_processing_service._process_database_operations(
            processed_ads, 'Test Firm', 'firm123', True
        )
        
        # Verify UPDATE operation detected
        ad_processing_service._logger.info.assert_any_call(
            'Operation type for 123456789: UPDATE'
        )
    
    @pytest.mark.asyncio
    async def test_database_operations_field_change_detection(self, ad_processing_service):
        """Test that field changes are properly detected and logged."""
        processed_ads = [
            {
                'ad_archive_id': '123456789',
                'start_date': '20250612',
                'law_firm': 'Test Firm',
                'title': 'Updated Ad Title',
                'body': 'Updated content'
            }
        ]
        
        # Mock existing item with different values
        existing_item = {
            'AdArchiveID': '123456789',
            'StartDate': '20250612',
            'LawFirm': 'Test Firm',
            'Title': 'Old Ad Title',
            'Body': 'Old content',
            'LastUpdated': '20250611'
        }
        
        # Mock the database comparison setup
        ad_processing_service.fb_archive_repo.get_item.return_value = existing_item
        ad_processing_service.fb_archive_repo.put_item.return_value = True
        ad_processing_service.law_firms_repo.add_or_update_record.return_value = True
        
        await ad_processing_service._process_database_operations(
            processed_ads, 'Test Firm', 'firm123', True
        )
        
        # Verify detailed comparison logging shows field changes
        # Note: This tests that the comparison logic works, actual logging may vary
        # based on implementation details
        log_calls = [str(call) for call in ad_processing_service._logger.debug.call_args_list]
        title_change_logged = any('Title' in call and 'changed' in call for call in log_calls)
        body_change_logged = any('Body' in call and 'changed' in call for call in log_calls)
        
        # At least some change detection should be logged
        assert title_change_logged or body_change_logged or any('fields changed' in call for call in log_calls)


class TestIntegrationScenarios:
    """Test integration scenarios combining multiple fixes."""
    
    @pytest.mark.asyncio
    async def test_end_to_end_successful_processing(self):
        """Test end-to-end successful processing with all fixes applied."""
        # This test simulates the complete flow that was failing before fixes
        
        # Mock configuration with AI disabled and development mode
        config = {
            'iso_date': '20250612',
            'default_date_range_days': 14,
            'feature_flags': {
                'enable_ai_enhancement': False
            },
            'development': {
                'sample_firms': ['301100319750499'],
                'max_test_firms': 1
            },
            'current_process_date': '20250612',
            'use_tqdm': False,
            'verbose': False
        }
        
        logger = Mock()
        
        # Test AdProcessingService with proper PascalCase conversion
        ad_service = AdProcessingService(config, logger)
        ad_service.law_firms_repo = AsyncMock()
        ad_service.fb_archive_repo = AsyncMock()
        
        # Mock successful database operations
        ad_service.fb_archive_repo.get_item.return_value = None  # New item
        ad_service.fb_archive_repo.put_item.return_value = True
        ad_service.law_firms_repo.add_or_update_record.return_value = True
        
        # Test processed ad with snake_case keys
        processed_ads = [
            {
                'ad_archive_id': '123456789',
                'start_date': '20250612',
                'law_firm': 'Levy Konigsberg LLP',
                'title': 'Test Ad',
                'is_active': True,
                'summary': 'Test summary',
                'campaign': 'Mass Tort'
            }
        ]
        
        # Execute database operations
        result = await ad_service._process_database_operations(
            processed_ads, 'Levy Konigsberg LLP', '301100319750499', True
        )
        
        # Verify successful processing
        items_processed, items_updated, items_added = result
        assert items_processed == 1
        assert items_updated == 0
        assert items_added == 1
        
        # Verify correct DynamoDB keys were used
        put_call_args = ad_service.fb_archive_repo.put_item.call_args[0][0]
        assert put_call_args['AdArchiveID'] == '123456789'  # Correct PascalCase
        assert put_call_args['StartDate'] == '20250612'     # Correct PascalCase
        assert put_call_args['LawFirm'] == 'Levy Konigsberg LLP'
        
        # Verify law firm update used correct method
        ad_service.law_firms_repo.add_or_update_record.assert_called_once()
    
    def test_all_fixes_configuration_validation(self):
        """Test that all implemented fixes work together with proper configuration."""
        # Configuration with all fixes enabled
        config = {
            'iso_date': '20250612',
            'feature_flags': {
                'enable_ai_enhancement': False,  # AI disabled for stability
                'enable_rule_based_classification': True
            },
            'development': {
                'sample_firms': ['301100319750499'],  # Sample firm testing
                'max_test_firms': 1,
                'enable_debug_output': True
            },
            'skip_firms_updated_today': False,
            'shuffle_firm_order': True
        }
        
        # Validate configuration contains all necessary fix settings
        assert 'feature_flags' in config
        assert 'enable_ai_enhancement' in config['feature_flags']
        assert 'development' in config
        assert 'sample_firms' in config['development']
        assert 'max_test_firms' in config['development']
        
        # Test DataValidationService with sample firms
        logger = Mock()
        processing_tracker = Mock()
        processing_tracker.get_skip_list.return_value = set()
        
        with patch.object(DataValidationService, '_load_ignore_firms', return_value={}):
            validation_service = DataValidationService(config, logger, processing_tracker)
        
        # Mock firm data
        all_firms = [
            {'ID': '301100319750499', 'Name': 'Levy Konigsberg LLP', 'AdArchiveLastUpdated': '20250611'},
            {'ID': '123456789', 'Name': 'Other Firm', 'AdArchiveLastUpdated': '20250611'}
        ]
        
        # Test filtering works with sample firms
        result = validation_service.filter_law_firms(all_firms)
        
        # Should return only sample firm
        assert len(result) == 1
        assert result[0]['ID'] == '301100319750499'
        
        # Verify development mode logging
        logger.info.assert_any_call('Development mode: Testing with sample firms: [\'301100319750499\']')