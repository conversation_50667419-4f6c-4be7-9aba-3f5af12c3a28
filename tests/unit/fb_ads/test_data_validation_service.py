"""
Unit tests for DataValidationService.

Tests data validation, filtering, and transformation logic:
- Law firm filtering and validation
- Skip list management
- Data sanitization and transformation
- Configuration validation
"""
import pytest
from unittest.mock import Mock, patch, mock_open
import unittest.mock
import json
from datetime import datetime

from src.fb_ads.services.data_validation_service import DataValidationService


@pytest.fixture
def mock_config():
    """Mock configuration for testing."""
    return {
        'iso_date': '20250115',
        'skip_firms_updated_today': True,
        'shuffle_firm_order': True,
        'single_firm_date_range_days': 30,
        'ignore_list_date_range_days': 30,
        'default_date_range_days': 14,
        'bucket_name': 'test-bucket'
    }


@pytest.fixture
def mock_logger():
    """Mock logger for testing."""
    return Mock()


@pytest.fixture
def mock_processing_tracker():
    """Mock processing tracker for testing."""
    tracker = Mock()
    tracker.get_skip_list.return_value = set(['skip123', 'skip456'])
    return tracker


@pytest.fixture
def data_validation_service(mock_config, mock_logger, mock_processing_tracker):
    """Create DataValidationService instance for testing."""
    with patch.object(DataValidationService, '_load_ignore_firms', return_value={'ignore123': 'Ignored Firm'}):
        service = DataValidationService(mock_config, mock_logger, mock_processing_tracker)
    return service


class TestDataValidationServiceInitialization:
    """Test DataValidationService initialization."""
    
    @patch.object(DataValidationService, '_load_ignore_firms')
    def test_init_with_valid_config(self, mock_load_ignore, mock_config, mock_logger, mock_processing_tracker):
        """Test service initializes correctly with valid config."""
        mock_load_ignore.return_value = {'firm123': 'Test Firm'}
        
        service = DataValidationService(mock_config, mock_logger, mock_processing_tracker)
        
        assert service.config == mock_config
        assert service._logger == mock_logger
        assert service.processing_tracker == mock_processing_tracker
        assert service.end_date_iso == '20250115'
        assert service.ignore_firm_data == {'firm123': 'Test Firm'}
        mock_load_ignore.assert_called_once()


class TestIgnoreFirmsLoading:
    """Test ignore firms data loading."""
    
    @patch('builtins.open', new_callable=mock_open, read_data='{"firm123": "Test Firm", "firm456": "Another Firm"}')
    @patch('pathlib.Path.exists', return_value=True)
    def test_load_ignore_firms_success(self, mock_exists, mock_file, mock_config, mock_logger, mock_processing_tracker):
        """Test successful loading of ignore firms."""
        service = DataValidationService(mock_config, mock_logger, mock_processing_tracker)
        
        expected_data = {"firm123": "Test Firm", "firm456": "Another Firm"}
        assert service.ignore_firm_data == expected_data
    
    @patch('builtins.open', side_effect=FileNotFoundError())
    def test_load_ignore_firms_file_not_found(self, mock_file, mock_config, mock_logger, mock_processing_tracker):
        """Test loading ignore firms when file not found."""
        service = DataValidationService(mock_config, mock_logger, mock_processing_tracker)
        
        assert service.ignore_firm_data == {}
        mock_logger.error.assert_called()
    
    @patch('builtins.open', new_callable=mock_open, read_data='invalid json')
    @patch('pathlib.Path.exists', return_value=True)
    def test_load_ignore_firms_invalid_json(self, mock_exists, mock_file, mock_config, mock_logger, mock_processing_tracker):
        """Test loading ignore firms with invalid JSON."""
        service = DataValidationService(mock_config, mock_logger, mock_processing_tracker)
        
        assert service.ignore_firm_data == {}
        mock_logger.error.assert_called()
    
    @patch('builtins.open', new_callable=mock_open, read_data='["not", "a", "dict"]')
    @patch('pathlib.Path.exists', return_value=True)
    def test_load_ignore_firms_wrong_format(self, mock_exists, mock_file, mock_config, mock_logger, mock_processing_tracker):
        """Test loading ignore firms with wrong format (not dict)."""
        service = DataValidationService(mock_config, mock_logger, mock_processing_tracker)
        
        assert service.ignore_firm_data == {}
        mock_logger.error.assert_called_with(unittest.mock.ANY)


class TestLawFirmFiltering:
    """Test law firm filtering functionality."""
    
    def test_filter_law_firms_empty_input(self, data_validation_service):
        """Test filtering with empty input."""
        result = data_validation_service.filter_law_firms([])
        assert result == []
    
    def test_filter_law_firms_basic_filtering(self, data_validation_service):
        """Test basic law firm filtering."""
        all_firms = [
            {'ID': 'firm123', 'Name': 'Firm 1', 'AdArchiveLastUpdated': '20250114'},
            {'ID': 'firm456', 'Name': 'Firm 2', 'AdArchiveLastUpdated': '20250113'},
            {'ID': 'skip123', 'Name': 'Firm 3', 'AdArchiveLastUpdated': '20250113'},  # In skip list
            {'ID': 'ignore123', 'Name': 'Firm 4', 'AdArchiveLastUpdated': '20250113'},  # In ignore list
            {'ID': 'firm789', 'Name': 'Firm 5', 'AdArchiveLastUpdated': '20250115'},  # Updated today
        ]
        
        with patch('random.shuffle') as mock_shuffle:
            result = data_validation_service.filter_law_firms(all_firms)
        
        # Should exclude: skip123 (skip list), ignore123 (ignore list), firm789 (updated today)
        expected_ids = {'firm123', 'firm456'}
        result_ids = {firm['ID'] for firm in result}
        assert result_ids == expected_ids
        
        # Should call shuffle due to config setting
        mock_shuffle.assert_called_once()
    
    def test_filter_law_firms_no_shuffle(self, data_validation_service):
        """Test filtering without shuffling."""
        data_validation_service.config['shuffle_firm_order'] = False
        
        all_firms = [
            {'ID': 'firm123', 'Name': 'Firm 1', 'AdArchiveLastUpdated': '20250114'},
        ]
        
        with patch('random.shuffle') as mock_shuffle:
            result = data_validation_service.filter_law_firms(all_firms)
        
        assert len(result) == 1
        mock_shuffle.assert_not_called()
    
    def test_filter_law_firms_skip_updated_today_disabled(self, data_validation_service):
        """Test filtering when skip_firms_updated_today is disabled."""
        data_validation_service.config['skip_firms_updated_today'] = False
        
        all_firms = [
            {'ID': 'firm123', 'Name': 'Firm 1', 'AdArchiveLastUpdated': '20250115'},  # Updated today
        ]
        
        result = data_validation_service.filter_law_firms(all_firms)
        
        # Should not skip firms updated today
        assert len(result) == 1
        assert result[0]['ID'] == 'firm123'
    
    def test_filter_law_firms_missing_id(self, data_validation_service):
        """Test filtering firms with missing ID."""
        all_firms = [
            {'Name': 'Firm without ID'},
            {'ID': 'firm123', 'Name': 'Valid Firm'}
        ]
        
        result = data_validation_service.filter_law_firms(all_firms)
        
        # Should skip firm without ID
        assert len(result) == 1
        assert result[0]['ID'] == 'firm123'


class TestFirmDataValidation:
    """Test firm data validation functionality."""
    
    def test_validate_firm_data_valid(self, data_validation_service):
        """Test validation with valid firm data."""
        firm_data = {
            'ID': '123456789',
            'Name': 'Test Law Firm',
            'AdArchiveLastUpdated': '20250115'
        }
        
        result = data_validation_service.validate_firm_data(firm_data)
        assert result is True
    
    def test_validate_firm_data_missing_id(self, data_validation_service):
        """Test validation with missing ID."""
        firm_data = {
            'Name': 'Test Law Firm'
        }
        
        result = data_validation_service.validate_firm_data(firm_data)
        assert result is False
    
    def test_validate_firm_data_missing_name(self, data_validation_service):
        """Test validation with missing Name."""
        firm_data = {
            'ID': '123456789'
        }
        
        result = data_validation_service.validate_firm_data(firm_data)
        assert result is False
    
    def test_validate_firm_data_invalid_id_format(self, data_validation_service):
        """Test validation with invalid ID format."""
        firm_data = {
            'ID': '',  # Empty string
            'Name': 'Test Law Firm'
        }
        
        result = data_validation_service.validate_firm_data(firm_data)
        assert result is False
        
        firm_data['ID'] = 123  # Not a string
        result = data_validation_service.validate_firm_data(firm_data)
        assert result is False


class TestDataSanitization:
    """Test data sanitization functionality."""
    
    def test_sanitize_firm_data_complete(self, data_validation_service):
        """Test sanitization with complete firm data."""
        firm_data = {
            'ID': '123456789',
            'Name': '  Test Law Firm  ',  # With whitespace
            'pageAlias': 'test-firm',
            'category': 'Legal Services',
            'imageURI': 'https://example.com/image.jpg',
            'extra_field': 'should be ignored'
        }
        
        result = data_validation_service.sanitize_firm_data(firm_data)
        
        expected = {
            'ID': '123456789',
            'Name': 'Test Law Firm',  # Whitespace trimmed
            'pageAlias': 'test-firm',
            'category': 'Legal Services',
            'imageURI': 'https://example.com/image.jpg',
            'AdArchiveLastUpdated': None,
            'NumAds': 0
        }
        
        assert result == expected
    
    def test_sanitize_firm_data_minimal(self, data_validation_service):
        """Test sanitization with minimal firm data."""
        firm_data = {
            'ID': '123456789',
            'Name': 'Test Law Firm'
        }
        
        result = data_validation_service.sanitize_firm_data(firm_data)
        
        expected = {
            'ID': '123456789',
            'Name': 'Test Law Firm',
            'AdArchiveLastUpdated': None,
            'NumAds': 0
        }
        
        assert result == expected
    
    def test_sanitize_firm_data_none_values(self, data_validation_service):
        """Test sanitization with None values."""
        firm_data = {
            'ID': '123456789',
            'Name': 'Test Law Firm',
            'pageAlias': None,
            'category': None
        }
        
        result = data_validation_service.sanitize_firm_data(firm_data)
        
        # None values should not be included
        assert 'pageAlias' not in result
        assert 'category' not in result
        assert result['ID'] == '123456789'
        assert result['Name'] == 'Test Law Firm'


class TestAdDataValidation:
    """Test ad data validation functionality."""
    
    def test_validate_ad_data_valid(self, data_validation_service):
        """Test validation with valid ad data."""
        ad_data = {
            'AdArchiveID': '123456789',
            'StartDate': '2025-01-15',
            'Title': 'Test Ad',
            'Body': 'Ad content'
        }
        
        result = data_validation_service.validate_ad_data(ad_data)
        assert result is True
    
    def test_validate_ad_data_missing_required_fields(self, data_validation_service):
        """Test validation with missing required fields."""
        # Missing AdArchiveID
        ad_data = {
            'StartDate': '2025-01-15',
            'Title': 'Test Ad'
        }
        result = data_validation_service.validate_ad_data(ad_data)
        assert result is False
        
        # Missing StartDate
        ad_data = {
            'AdArchiveID': '123456789',
            'Title': 'Test Ad'
        }
        result = data_validation_service.validate_ad_data(ad_data)
        assert result is False
    
    def test_validate_ad_data_none_values(self, data_validation_service):
        """Test validation with None values in required fields."""
        ad_data = {
            'AdArchiveID': None,
            'StartDate': '2025-01-15'
        }
        
        result = data_validation_service.validate_ad_data(ad_data)
        assert result is False
    
    def test_validate_ad_data_not_dict(self, data_validation_service):
        """Test validation with non-dict input."""
        result = data_validation_service.validate_ad_data("not a dict")
        assert result is False
        
        result = data_validation_service.validate_ad_data(None)
        assert result is False


class TestDateRangeProcessing:
    """Test date range processing functionality."""
    
    @patch('src.utils.date.DateUtils')
    def test_get_processing_date_range_default(self, mock_date_utils, data_validation_service):
        """Test getting default processing date range."""
        mock_date_utils.get_date_before_n_days.return_value = '20250101'
        
        start_date, end_date = data_validation_service.get_processing_date_range('default')
        
        assert end_date == '20250115'
        mock_date_utils.get_date_before_n_days.assert_called_once_with(14, '20250115')
    
    @patch('src.utils.date.DateUtils')
    def test_get_processing_date_range_single_firm(self, mock_date_utils, data_validation_service):
        """Test getting single firm processing date range."""
        mock_date_utils.get_date_before_n_days.return_value = '20241216'
        
        start_date, end_date = data_validation_service.get_processing_date_range('single_firm')
        
        assert end_date == '20250115'
        mock_date_utils.get_date_before_n_days.assert_called_once_with(30, '20250115')
    
    @patch('src.utils.date.DateUtils')
    def test_get_processing_date_range_ignore_list(self, mock_date_utils, data_validation_service):
        """Test getting ignore list processing date range."""
        mock_date_utils.get_date_before_n_days.return_value = '20241216'
        
        start_date, end_date = data_validation_service.get_processing_date_range('ignore_list')
        
        assert end_date == '20250115'
        mock_date_utils.get_date_before_n_days.assert_called_once_with(30, '20250115')


class TestConfigurationValidation:
    """Test configuration validation functionality."""
    
    def test_validate_config_valid(self, data_validation_service):
        """Test validation with valid configuration."""
        result = data_validation_service.validate_config()
        assert result is True
    
    def test_validate_config_missing_required(self, data_validation_service):
        """Test validation with missing required fields."""
        data_validation_service.config.pop('iso_date')
        result = data_validation_service.validate_config()
        assert result is False
        
        data_validation_service.config['iso_date'] = '20250115'
        data_validation_service.config.pop('bucket_name')
        result = data_validation_service.validate_config()
        assert result is False
    
    def test_validate_config_invalid_date_format(self, data_validation_service):
        """Test validation with invalid date format."""
        data_validation_service.config['iso_date'] = 'invalid-date'
        result = data_validation_service.validate_config()
        assert result is False


class TestDataCleaning:
    """Test data cleaning functionality."""
    
    def test_clean_ad_data_for_storage(self, data_validation_service):
        """Test cleaning ad data for storage."""
        ad_data = {
            'AdArchiveID': '123456789',
            'Title': 'Test Ad',
            'original_image_url': 'https://example.com/image.jpg',  # Should be removed
            'video_hd_url': 'https://example.com/video.mp4',  # Should be removed
            'ImageText': 'Extracted text',  # Should be removed
            '_temp_source_image_url': 'temp://url',  # Should be removed
            'Body': 'Ad content'  # Should be kept
        }
        
        result = data_validation_service.clean_ad_data_for_storage(ad_data)
        
        # Should keep these fields
        assert result['AdArchiveID'] == '123456789'
        assert result['Title'] == 'Test Ad'
        assert result['Body'] == 'Ad content'
        
        # Should remove these fields
        assert 'original_image_url' not in result
        assert 'video_hd_url' not in result
        assert 'ImageText' not in result
        assert '_temp_source_image_url' not in result


class TestUtilityMethods:
    """Test utility methods."""
    
    def test_get_ignore_list_firms(self, data_validation_service):
        """Test getting ignore list firm IDs."""
        result = data_validation_service.get_ignore_list_firms()
        assert result == ['ignore123']  # From fixture