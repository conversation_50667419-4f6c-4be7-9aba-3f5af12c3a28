"""Tests for FB ads image utilities."""
import pytest
from unittest.mock import Mock, patch, AsyncMock
from botocore.exceptions import ClientError
from src.fb_ads.image_utils import FBImageHashManager, calculate_image_hash, extract_ids_from_key


class TestFBImageHashManager:
    """Test suite for FBImageHashManager."""

    @pytest.fixture
    def config(self):
        """Test configuration."""
        return {
            'region_name': 'us-west-2',
            'dynamodb': {
                'fb_image_hash_table_name': 'test-FBImageHash'
            },
            'use_local_dynamodb': False
        }

    @pytest.fixture
    def hash_manager(self, config):
        """Create test hash manager."""
        with patch('src.fb_ads.image_utils.DynamoDbBaseManager.__init__', return_value=None):
            manager = FBImageHashManager('test-FBImageHash', config)
            manager.table = Mock()
            return manager

    def test_process_records_empty_list(self, hash_manager):
        """Test process_records with empty list."""
        result = hash_manager.process_records([])
        assert result == []

    def test_process_records_with_data(self, hash_manager):
        """Test process_records with actual data."""
        test_records = [
            {'PHash': 'abc123', 'AdArchiveID': '12345', 'S3Key': 'test/key.jpg'},
            {'PHash': 'def456', 'AdArchiveID': '67890', 'S3Key': 'test/key2.jpg'}
        ]
        
        result = hash_manager.process_records(test_records)
        
        assert len(result) == 2
        assert result[0]['PHash'] == 'abc123'
        assert result[1]['PHash'] == 'def456'
        # Should return records as-is without any filtering

    def test_process_records_returns_all_records(self, hash_manager):
        """Test that process_records returns all records without filtering."""
        test_records = [
            {'PHash': 'abc123', 'AdArchiveID': '12345'},
            {'PHash': 'empty', 'AdArchiveID': '67890'},  # This should NOT be filtered out
            {'PHash': 'def456', 'AdArchiveID': '54321'}
        ]
        
        result = hash_manager.process_records(test_records)
        
        # Should return all records as-is
        assert len(result) == 3
        assert result[0]['PHash'] == 'abc123'
        assert result[1]['PHash'] == 'empty'
        assert result[2]['PHash'] == 'def456'

    def test_get_record_by_phash_success(self, hash_manager):
        """Test successful retrieval by PHash using get_item."""
        # Mock table.get_item response
        mock_response = {
            'Item': {'PHash': 'abc123', 'AdArchiveID': '12345', 'S3Key': 'test/key.jpg'}
        }
        hash_manager.table.get_item.return_value = mock_response
        hash_manager.process_records = Mock(return_value=[mock_response['Item']])
        
        result = hash_manager.get_record_by_phash('abc123')
        
        # Verify get_item was called with correct key
        hash_manager.table.get_item.assert_called_once_with(
            Key={'PHash': 'abc123'}
        )
        
        # Verify process_records was called
        hash_manager.process_records.assert_called_once_with([mock_response['Item']])
        
        assert result is not None
        assert result['PHash'] == 'abc123'

    def test_get_record_by_phash_not_found(self, hash_manager):
        """Test handling when PHash is not found."""
        # Mock empty response
        hash_manager.table.get_item.return_value = {}
        
        result = hash_manager.get_record_by_phash('nonexistent')
        
        assert result is None

    def test_get_record_by_phash_client_error(self, hash_manager):
        """Test handling of ClientError during get_item."""
        # Mock ClientError
        error = ClientError(
            {'Error': {'Code': 'ValidationException', 'Message': 'Test error'}},
            'GetItem'
        )
        hash_manager.table.get_item.side_effect = error
        
        result = hash_manager.get_record_by_phash('abc123')
        
        assert result is None

    def test_put_item_success(self, hash_manager):
        """Test successful item insertion."""
        test_item = {
            'PHash': 'abc123',
            'AdArchiveID': '12345',
            'S3Key': 'test/key.jpg',
            'ImageText': 'test text'
        }
        
        # Mock successful put_item
        hash_manager.table.put_item.return_value = {}
        
        result = hash_manager.put_item(test_item)
        
        assert result is True
        hash_manager.table.put_item.assert_called_once_with(Item=test_item)

    def test_put_item_missing_keys(self, hash_manager):
        """Test put_item with missing required keys."""
        # Missing AdArchiveID
        test_item = {
            'PHash': 'abc123',
            'S3Key': 'test/key.jpg'
        }
        
        result = hash_manager.put_item(test_item)
        
        assert result is False
        hash_manager.table.put_item.assert_not_called()

    def test_put_item_client_error(self, hash_manager):
        """Test put_item with ClientError."""
        test_item = {
            'PHash': 'abc123',
            'AdArchiveID': '12345',
            'S3Key': 'test/key.jpg'
        }
        
        # Mock ClientError
        error = ClientError(
            {'Error': {'Code': 'ValidationException', 'Message': 'Test error'}},
            'PutItem'
        )
        hash_manager.table.put_item.side_effect = error
        
        result = hash_manager.put_item(test_item)
        
        assert result is False


class TestUtilityFunctions:
    """Test utility functions."""

    def test_extract_ids_from_key_original_format(self):
        """Test extracting IDs from original format S3 key."""
        s3_key = "adarchive/fb/123456789/creative_12345.jpg"
        
        archive_id, creative_id = extract_ids_from_key(s3_key, 'original')
        
        assert archive_id == '123456789'
        assert creative_id == '12345'

    def test_extract_ids_from_key_no_creative_pattern(self):
        """Test extracting IDs when creative pattern doesn't match."""
        s3_key = "adarchive/fb/123456789/someotherfile.jpg"
        
        archive_id, creative_id = extract_ids_from_key(s3_key, 'original')
        
        assert archive_id == '123456789'
        # Should generate a default creative ID
        assert creative_id.startswith('default_')

    def test_extract_ids_from_key_empty_key(self):
        """Test extracting IDs from empty key."""
        archive_id, creative_id = extract_ids_from_key('', 'original')
        
        assert archive_id is None
        assert creative_id is None

    def test_extract_ids_from_key_archive_id_creative_id_format(self):
        """Test extracting IDs from archive_id_creative_id format."""
        s3_key = "adarchive/fb/archive_123456789_creative_12345.jpg"
        
        archive_id, creative_id = extract_ids_from_key(s3_key, 'archive_id_creative_id')
        
        assert archive_id == '123456789'
        assert creative_id == '12345'

    @patch('src.fb_ads.image_utils.Image')
    @patch('src.fb_ads.image_utils.imagehash')
    def test_calculate_image_hash_success(self, mock_imagehash, mock_image):
        """Test successful image hash calculation."""
        # Mock PIL Image and imagehash
        mock_img = Mock()
        mock_image.open.return_value = mock_img
        mock_hash = Mock()
        mock_imagehash.phash.return_value = mock_hash
        
        test_content = b"fake image content"
        
        result = calculate_image_hash(test_content)
        
        assert result == mock_hash
        mock_image.open.assert_called_once()
        mock_imagehash.phash.assert_called_once_with(mock_img)

    @patch('src.fb_ads.image_utils.Image')
    def test_calculate_image_hash_unidentified_image(self, mock_image):
        """Test image hash calculation with unidentified image error."""
        from PIL import UnidentifiedImageError
        
        mock_image.open.side_effect = UnidentifiedImageError("Cannot identify image file")
        
        test_content = b"invalid image content"
        
        result = calculate_image_hash(test_content)
        
        assert result is None

    @patch('src.fb_ads.image_utils.Image')
    def test_calculate_image_hash_generic_error(self, mock_image):
        """Test image hash calculation with generic error."""
        mock_image.open.side_effect = Exception("Generic error")
        
        test_content = b"image content"
        
        result = calculate_image_hash(test_content)
        
        assert result is None


if __name__ == '__main__':
    pytest.main([__file__])