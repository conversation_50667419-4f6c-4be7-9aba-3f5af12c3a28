"""Tests for FB Ads Processor image queue configuration and initialization."""
import pytest
from unittest.mock import Mock, patch, MagicMock
import os
from pathlib import Path

from src.fb_ads.processor import AdProcessor


class TestAdProcessorImageQueue:
    """Test AdProcessor image queue configuration and initialization."""
    
    @pytest.fixture
    def base_config(self):
        """Base configuration without defer processing."""
        return {
            'iso_date': '20241212',
            'bucket_name': 'test-bucket',
            'use_tqdm': True,
            'processing_results_dir': './test_results'
        }
    
    @pytest.fixture
    def config_with_defer(self):
        """Configuration with defer_image_processing enabled."""
        return {
            'iso_date': '20241212',
            'bucket_name': 'test-bucket',
            'use_tqdm': True,
            'processing_results_dir': './test_results',
            'defer_image_processing': True,
            'image_queue_dir': './test_image_queue'
        }
    
    @pytest.fixture
    def config_with_nested_queue(self):
        """Configuration with nested image_queue config."""
        return {
            'iso_date': '20241212',
            'bucket_name': 'test-bucket',
            'use_tqdm': True,
            'processing_results_dir': './test_results',
            'defer_image_processing': True,
            'image_queue': {
                'directory': './test_nested_queue',
                'batch_size': 100,
                'cleanup_days': 30
            }
        }
    
    @pytest.fixture
    def config_with_processing_nested(self):
        """Configuration with defer processing in nested 'processing' config."""
        return {
            'iso_date': '20241212',
            'bucket_name': 'test-bucket',
            'use_tqdm': True,
            'processing_results_dir': './test_results',
            'processing': {
                'defer_image_processing': True,
                'image_queue': {
                    'directory': './test_processing_queue'
                }
            }
        }
    
    @pytest.fixture
    def mock_s3_manager(self):
        """Mock S3 manager."""
        return Mock()
    
    @pytest.fixture
    def mock_image_handler(self):
        """Mock image handler."""
        return Mock()
    
    @pytest.fixture
    def mock_ai_integrator(self):
        """Mock AI integrator."""
        return Mock()
    
    @pytest.fixture
    def mock_vector_clusterer(self):
        """Mock vector clusterer."""
        return Mock()
    
    @pytest.fixture
    def current_process_date(self):
        """Current process date."""
        return '20241212'
    
    def test_defer_processing_disabled_by_default(self, base_config, mock_image_handler, mock_ai_integrator, mock_vector_clusterer, current_process_date, mock_s3_manager):
        """Test that defer_image_processing is disabled by default."""
        
        with patch('src.fb_ads.processor.LocalImageQueue') as mock_queue_class:
            processor = AdProcessor(base_config, mock_image_handler, mock_ai_integrator, mock_vector_clusterer, current_process_date, s3_manager=mock_s3_manager)
            
            # Verify defer processing is False
            assert processor.defer_image_processing is False
            
            # Verify local queue is None
            assert processor.local_queue is None
            
            # Verify LocalImageQueue was not instantiated
            mock_queue_class.assert_not_called()
    
    def test_defer_processing_enabled_with_image_queue_dir(self, config_with_defer, mock_s3_manager):
        """Test defer processing enabled with image_queue_dir configuration."""
        
        with patch('src.fb_ads.processor.LocalImageQueue') as mock_queue_class:
            mock_queue = Mock()
            mock_queue_class.return_value = mock_queue
            
            processor = AdProcessor(config_with_defer, Mock(), Mock(), Mock(), '20241212', s3_manager=mock_s3_manager)
            
            # Verify defer processing is True
            assert processor.defer_image_processing is True
            
            # Verify local queue is set
            assert processor.local_queue == mock_queue
            
            # Verify LocalImageQueue was instantiated with correct directory
            mock_queue_class.assert_called_once_with('./test_image_queue')
    
    def test_defer_processing_with_nested_image_queue_config(self, config_with_nested_queue, mock_s3_manager):
        """Test defer processing with nested image_queue configuration."""
        
        with patch('src.fb_ads.processor.LocalImageQueue') as mock_queue_class:
            mock_queue = Mock()
            mock_queue_class.return_value = mock_queue
            
            processor = AdProcessor(config_with_nested_queue, Mock(), Mock(), Mock(), '20241212', s3_manager=mock_s3_manager)
            
            # Verify defer processing is True
            assert processor.defer_image_processing is True
            
            # Verify local queue is set
            assert processor.local_queue == mock_queue
            
            # Verify LocalImageQueue was instantiated with nested directory
            mock_queue_class.assert_called_once_with('./test_nested_queue')
    
    def test_defer_processing_with_processing_nested_config(self, config_with_processing_nested, mock_s3_manager):
        """Test defer processing with processing.defer_image_processing configuration."""
        
        with patch('src.fb_ads.processor.LocalImageQueue') as mock_queue_class:
            mock_queue = Mock()
            mock_queue_class.return_value = mock_queue
            
            processor = AdProcessor(config_with_processing_nested, Mock(), Mock(), Mock(), '20241212', s3_manager=mock_s3_manager)
            
            # Verify defer processing is True
            assert processor.defer_image_processing is True
            
            # Verify local queue is set
            assert processor.local_queue == mock_queue
            
            # Verify LocalImageQueue was instantiated with processing nested directory
            mock_queue_class.assert_called_once_with('./test_processing_queue')
    
    def test_defer_processing_with_environment_variable(self, base_config, mock_s3_manager):
        """Test defer processing enabled via environment variable."""
        
        with patch.dict(os.environ, {'DEFER_IMAGE_PROCESSING': 'true'}), \
             patch('src.fb_ads.processor.LocalImageQueue') as mock_queue_class:
            
            mock_queue = Mock()
            mock_queue_class.return_value = mock_queue
            
            processor = AdProcessor(base_config, Mock(), Mock(), Mock(), '20241212', s3_manager=mock_s3_manager)
            
            # Verify defer processing is True from environment
            assert processor.defer_image_processing is True
            
            # Verify local queue is set
            assert processor.local_queue == mock_queue
            
            # Verify LocalImageQueue was instantiated with default directory
            mock_queue_class.assert_called_once_with('./data/image_queue')
    
    def test_queue_directory_hierarchy_precedence(self, mock_s3_manager):
        """Test the precedence of queue directory configuration sources."""
        
        config = {
            'iso_date': '20241212',
            'bucket_name': 'test-bucket',
            'use_tqdm': True,
            'processing_results_dir': './test_results',
            'defer_image_processing': True,
            'image_queue_dir': './top_level_queue',  # Highest precedence
            'processing': {
                'image_queue': {
                    'directory': './processing_queue'  # Lower precedence
                }
            },
            'image_queue': {
                'directory': './mid_level_queue'  # Middle precedence
            }
        }
        
        with patch('src.fb_ads.processor.LocalImageQueue') as mock_queue_class:
            mock_queue = Mock()
            mock_queue_class.return_value = mock_queue
            
            processor = AdProcessor(config, Mock(), Mock(), Mock(), '20241212', s3_manager=mock_s3_manager)
            
            # Should use the highest precedence directory
            mock_queue_class.assert_called_once_with('./top_level_queue')
    
    def test_defer_processing_queue_initialization_failure(self, config_with_defer, mock_s3_manager):
        """Test handling of queue initialization failure."""
        
        with patch('src.fb_ads.processor.LocalImageQueue') as mock_queue_class:
            # Make LocalImageQueue raise an exception
            mock_queue_class.side_effect = Exception("Queue initialization failed")
            
            # Should raise the exception during processor initialization
            with pytest.raises(Exception, match="Queue initialization failed"):
                AdProcessor(config_with_defer, Mock(), Mock(), Mock(), '20241212', s3_manager=mock_s3_manager)
    
    def test_defer_processing_without_queue_directory_uses_default(self, mock_s3_manager):
        """Test that defer processing uses default directory when none specified."""
        
        config = {
            'iso_date': '20241212',
            'bucket_name': 'test-bucket',
            'use_tqdm': True,
            'processing_results_dir': './test_results',
            'defer_image_processing': True
            # No image_queue_dir specified
        }
        
        with patch('src.fb_ads.processor.LocalImageQueue') as mock_queue_class:
            mock_queue = Mock()
            mock_queue_class.return_value = mock_queue
            
            processor = AdProcessor(config, Mock(), Mock(), Mock(), '20241212', s3_manager=mock_s3_manager)
            
            # Should use default directory
            mock_queue_class.assert_called_once_with('./data/image_queue')
    
    def test_multiple_config_sources_for_defer_processing(self, mock_s3_manager):
        """Test that defer_image_processing can be enabled from multiple config sources."""
        
        # Test with config.defer_image_processing
        config1 = {
            'iso_date': '20241212',
            'bucket_name': 'test-bucket',
            'defer_image_processing': True,
            'image_queue_dir': './test1'
        }
        
        # Test with config.processing.defer_image_processing
        config2 = {
            'iso_date': '20241212',
            'bucket_name': 'test-bucket',
            'processing': {
                'defer_image_processing': True,
                'image_queue': {'directory': './test2'}
            }
        }
        
        with patch('src.fb_ads.processor.LocalImageQueue') as mock_queue_class:
            mock_queue = Mock()
            mock_queue_class.return_value = mock_queue
            
            # Test config1
            processor1 = AdProcessor(config1, Mock(), Mock(), Mock(), '20241212', s3_manager=mock_s3_manager)
            assert processor1.defer_image_processing is True
            
            # Reset mock for second test
            mock_queue_class.reset_mock()
            
            # Test config2
            processor2 = AdProcessor(config2, Mock(), Mock(), Mock(), '20241212', s3_manager=mock_s3_manager)
            assert processor2.defer_image_processing is True
            
            # Both should have called LocalImageQueue
            assert mock_queue_class.call_count == 2


class TestAdProcessorImageQueueIntegration:
    """Integration tests for AdProcessor with actual LocalImageQueue behavior."""
    
    @pytest.fixture
    def temp_queue_dir(self, tmp_path):
        """Create a temporary directory for queue testing."""
        queue_dir = tmp_path / "test_queue"
        queue_dir.mkdir()
        return str(queue_dir)
    
    @pytest.fixture
    def config_with_temp_queue(self, temp_queue_dir):
        """Configuration using temporary queue directory."""
        return {
            'iso_date': '20241212',
            'bucket_name': 'test-bucket',
            'use_tqdm': True,
            'processing_results_dir': './test_results',
            'defer_image_processing': True,
            'image_queue_dir': temp_queue_dir
        }
    
    @pytest.fixture
    def mock_s3_manager(self):
        """Mock S3 manager."""
        return Mock()
    
    def test_processor_creates_queue_directory_if_not_exists(self, mock_s3_manager, tmp_path):
        """Test that processor creates queue directory if it doesn't exist."""
        
        queue_dir = tmp_path / "nonexistent_queue"
        config = {
            'iso_date': '20241212',
            'bucket_name': 'test-bucket',
            'defer_image_processing': True,
            'image_queue_dir': str(queue_dir)
        }
        
        # Directory should not exist initially
        assert not queue_dir.exists()
        
        # Should not raise exception even if directory doesn't exist
        # (LocalImageQueue should handle directory creation)
        processor = AdProcessor(config, Mock(), Mock(), Mock(), '20241212', s3_manager=mock_s3_manager)
        
        # Verify defer processing is enabled
        assert processor.defer_image_processing is True
        assert processor.local_queue is not None
    
    def test_config_merge_scenario_from_main_processor(self, mock_s3_manager, temp_queue_dir):
        """Test the specific scenario where config is merged from MainProcessor."""
        
        # Simulate the scenario from the bug fix - config merged from params
        original_config = {
            'iso_date': '20241212',
            'bucket_name': 'test-bucket'
        }
        
        # Simulate params being merged (this is what the fix does)
        merged_config = original_config.copy()
        merged_config.update({
            'defer_image_processing': True,
            'image_queue_dir': temp_queue_dir,
            'headless': True,
            'use_proxy': True
        })
        
        processor = AdProcessor(merged_config, Mock(), Mock(), Mock(), '20241212', s3_manager=mock_s3_manager)
        
        # Verify the merged config enables defer processing
        assert processor.defer_image_processing is True
        assert processor.local_queue is not None
        
        # Verify other merged flags are accessible
        assert merged_config['headless'] is True
        assert merged_config['use_proxy'] is True