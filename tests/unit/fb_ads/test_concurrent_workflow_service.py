"""
Unit tests for ConcurrentWorkflowService.

Tests concurrent processing functionality:
- Concurrent firm processing
- Session pool management
- Batch processing optimization
- Performance monitoring
- Graceful degradation
"""
import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime, timedelta

from src.fb_ads.services.concurrent_workflow_service import ConcurrentWorkflowService, ProcessingBatch, ProcessingResult


@pytest.fixture
def mock_config():
    """Mock configuration for testing."""
    return {
        'iso_date': '20250115',
        'enable_concurrent_processing': True,
        'max_concurrent_firms': 3,
        'concurrent_batch_size': 5,
        'session_pool_size': 2,
        'session_refresh_interval': 5,
        'shuffle_firm_order': True,
        'rotate_proxy_between_firms': True
    }


@pytest.fixture
def mock_logger():
    """Mock logger for testing."""
    return Mock()


@pytest.fixture
def mock_services():
    """Mock service dependencies."""
    services = {
        'ad_processing_service': <PERSON>ck(),
        'data_validation_service': Mock(),
        'error_handling_service': Mock(),
        'session_manager': AsyncMock()
    }
    
    # Setup default return values
    services['ad_processing_service'].process_single_firm_core = AsyncMock(
        return_value=(True, '', 5, 3, 1, 2)  # success, error, fetched, processed, updated, added
    )
    
    services['data_validation_service'].filter_law_firms = Mock(return_value=[
        {'ID': 'firm1', 'Name': 'Firm 1'},
        {'ID': 'firm2', 'Name': 'Firm 2'},
        {'ID': 'firm3', 'Name': 'Firm 3'}
    ])
    services['data_validation_service'].validate_firm_data = Mock(return_value=True)
    services['data_validation_service'].get_ignore_list_firms = Mock(return_value=['firm4', 'firm5'])
    
    services['error_handling_service'].clear_errors = Mock()
    services['error_handling_service'].summarize_errors = Mock()
    services['error_handling_service'].add_firm_error = Mock()
    
    services['session_manager'].create_new_session = AsyncMock(return_value=True)
    services['session_manager'].random_sleep = AsyncMock()
    services['session_manager'].use_proxy = False
    
    return services


@pytest.fixture
def mock_progress():
    """Mock progress bar for testing."""
    progress = Mock()
    progress.add_task = Mock(return_value='task_id')
    progress.update = Mock()
    progress.__enter__ = Mock(return_value=progress)
    progress.__exit__ = Mock(return_value=None)
    return progress


@pytest.fixture
def concurrent_workflow_service(mock_config, mock_logger, mock_services, mock_progress):
    """Create ConcurrentWorkflowService instance for testing."""
    service = ConcurrentWorkflowService(mock_config, mock_logger)
    service.set_dependencies(
        mock_services['ad_processing_service'],
        mock_services['data_validation_service'], 
        mock_services['error_handling_service'],
        mock_services['session_manager'],
        mock_progress
    )
    return service


class TestConcurrentWorkflowServiceInitialization:
    """Test ConcurrentWorkflowService initialization."""
    
    def test_init_with_concurrent_config(self, mock_config, mock_logger):
        """Test service initializes correctly with concurrent config."""
        service = ConcurrentWorkflowService(mock_config, mock_logger)
        
        assert service.max_concurrent_firms == 3
        assert service.batch_size == 5
        assert service.session_pool_size == 2
        assert service.enable_concurrent_processing is True
        assert service._session_pool == []
        assert service._session_pool_initialized is False
    
    def test_init_with_concurrent_disabled(self, mock_logger):
        """Test service initialization with concurrent processing disabled."""
        config = {
            'iso_date': '20250115',
            'enable_concurrent_processing': False
        }
        service = ConcurrentWorkflowService(config, mock_logger)
        
        assert service.enable_concurrent_processing is False
        assert service.max_concurrent_firms == 3  # Default value
        assert service.batch_size == 5  # Default value
    
    def test_processing_stats_initialization(self, concurrent_workflow_service):
        """Test processing statistics are properly initialized."""
        stats = concurrent_workflow_service.get_processing_stats()
        
        expected_keys = [
            'total_firms', 'successful_firms', 'failed_firms',
            'total_processing_time', 'concurrent_batches', 'avg_batch_time'
        ]
        
        for key in expected_keys:
            assert key in stats
            assert stats[key] == 0 or stats[key] == 0.0


class TestSessionPoolManagement:
    """Test session pool management functionality."""
    
    @pytest.mark.asyncio
    @patch('src.fb_ads.session_manager.FacebookSessionManager')
    async def test_initialize_session_pool_success(self, mock_session_manager_class, concurrent_workflow_service):
        """Test successful session pool initialization."""
        # Mock session manager instances
        mock_session1 = AsyncMock()
        mock_session1.create_new_session = AsyncMock(return_value=True)
        mock_session2 = AsyncMock()
        mock_session2.create_new_session = AsyncMock(return_value=True)
        
        mock_session_manager_class.side_effect = [mock_session1, mock_session2]
        
        await concurrent_workflow_service._initialize_session_pool()
        
        assert concurrent_workflow_service._session_pool_initialized is True
        assert len(concurrent_workflow_service._session_pool) == 2
        mock_session1.create_new_session.assert_called_once()
        mock_session2.create_new_session.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_available_session_from_pool(self, concurrent_workflow_service):
        """Test getting available session from pool."""
        # Setup mock session pool
        mock_session1 = Mock()
        mock_session2 = Mock()
        concurrent_workflow_service._session_pool = [mock_session1, mock_session2]
        
        session = await concurrent_workflow_service._get_available_session()
        
        assert session in [mock_session1, mock_session2]
    
    @pytest.mark.asyncio
    async def test_get_available_session_fallback(self, concurrent_workflow_service, mock_services):
        """Test fallback to main session when pool is empty."""
        concurrent_workflow_service._session_pool = []
        
        session = await concurrent_workflow_service._get_available_session()
        
        assert session == mock_services['session_manager']
    
    @pytest.mark.asyncio
    async def test_refresh_session_pool(self, concurrent_workflow_service):
        """Test session pool refresh functionality."""
        # Setup mock session pool
        mock_session1 = AsyncMock()
        mock_session1.create_new_session = AsyncMock(return_value=True)
        mock_session2 = AsyncMock()
        mock_session2.create_new_session = AsyncMock(return_value=True)
        
        concurrent_workflow_service._session_pool = [mock_session1, mock_session2]
        
        await concurrent_workflow_service._refresh_session_pool()
        
        mock_session1.create_new_session.assert_called_once()
        mock_session2.create_new_session.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_cleanup_session_pool(self, concurrent_workflow_service):
        """Test session pool cleanup."""
        # Setup mock session pool with cleanup methods
        mock_session1 = Mock()
        mock_session1.cleanup = AsyncMock()
        mock_session2 = Mock()
        mock_session2.cleanup = AsyncMock()
        
        concurrent_workflow_service._session_pool = [mock_session1, mock_session2]
        concurrent_workflow_service._session_pool_initialized = True
        
        await concurrent_workflow_service._cleanup_session_pool()
        
        assert concurrent_workflow_service._session_pool == []
        assert concurrent_workflow_service._session_pool_initialized is False
        mock_session1.cleanup.assert_called_once()
        mock_session2.cleanup.assert_called_once()


class TestBatchProcessing:
    """Test batch processing functionality."""
    
    def test_create_processing_batches(self, concurrent_workflow_service):
        """Test creation of processing batches."""
        firms = [
            {'ID': f'firm{i}', 'Name': f'Firm {i}'}
            for i in range(1, 13)  # 12 firms
        ]
        
        batches = concurrent_workflow_service._create_processing_batches(firms)
        
        # With batch_size=5, should create 3 batches: [5, 5, 2]
        assert len(batches) == 3
        assert len(batches[0].firms) == 5
        assert len(batches[1].firms) == 5
        assert len(batches[2].firms) == 2
        
        # Check batch metadata
        for i, batch in enumerate(batches):
            assert batch.batch_id == i + 1
            assert batch.total_batches == 3
    
    def test_create_processing_batches_small_list(self, concurrent_workflow_service):
        """Test batch creation with fewer firms than batch size."""
        firms = [
            {'ID': 'firm1', 'Name': 'Firm 1'},
            {'ID': 'firm2', 'Name': 'Firm 2'}
        ]
        
        batches = concurrent_workflow_service._create_processing_batches(firms)
        
        assert len(batches) == 1
        assert len(batches[0].firms) == 2
        assert batches[0].batch_id == 1
        assert batches[0].total_batches == 1
    
    @pytest.mark.asyncio
    async def test_process_single_batch_success(self, concurrent_workflow_service, mock_services):
        """Test successful processing of a single batch."""
        firms = [
            {'ID': 'firm1', 'Name': 'Firm 1'},
            {'ID': 'firm2', 'Name': 'Firm 2'}
        ]
        batch = ProcessingBatch(firms=firms, batch_id=1, total_batches=1)
        
        # Mock the concurrent processing method
        concurrent_workflow_service._process_single_firm_concurrent = AsyncMock(
            side_effect=[
                ProcessingResult(firm_id='firm1', firm_name='Firm 1', success=True),
                ProcessingResult(firm_id='firm2', firm_name='Firm 2', success=True)
            ]
        )
        
        result = await concurrent_workflow_service._process_single_batch(batch, 'main_task_id')
        
        assert result is True
        assert concurrent_workflow_service.processing_stats['successful_firms'] == 2
        assert concurrent_workflow_service.processing_stats['failed_firms'] == 0
    
    @pytest.mark.asyncio
    async def test_process_single_batch_with_failures(self, concurrent_workflow_service, mock_services):
        """Test batch processing with some failures."""
        firms = [
            {'ID': 'firm1', 'Name': 'Firm 1'},
            {'ID': 'firm2', 'Name': 'Firm 2'}
        ]
        batch = ProcessingBatch(firms=firms, batch_id=1, total_batches=1)
        
        # Mock mixed success/failure results
        concurrent_workflow_service._process_single_firm_concurrent = AsyncMock(
            side_effect=[
                ProcessingResult(firm_id='firm1', firm_name='Firm 1', success=True),
                ProcessingResult(firm_id='firm2', firm_name='Firm 2', success=False, error_msg='Network error')
            ]
        )
        
        result = await concurrent_workflow_service._process_single_batch(batch, 'main_task_id')
        
        assert result is False  # Batch fails if any firm fails
        assert concurrent_workflow_service.processing_stats['successful_firms'] == 1
        assert concurrent_workflow_service.processing_stats['failed_firms'] == 1


class TestConcurrentFirmProcessing:
    """Test concurrent firm processing functionality."""
    
    @pytest.mark.asyncio
    async def test_process_single_firm_concurrent_success(self, concurrent_workflow_service, mock_services):
        """Test successful concurrent processing of a single firm."""
        firm_data = {'ID': 'firm123', 'Name': 'Test Firm'}
        
        # Mock get_available_session to return main session
        concurrent_workflow_service._get_available_session = AsyncMock(
            return_value=mock_services['session_manager']
        )
        
        result = await concurrent_workflow_service._process_single_firm_concurrent(firm_data)
        
        assert isinstance(result, ProcessingResult)
        assert result.success is True
        assert result.firm_id == 'firm123'
        assert result.firm_name == 'Test Firm'
        assert result.ads_fetched == 5
        assert result.items_processed == 3
        mock_services['data_validation_service'].validate_firm_data.assert_called_once_with(firm_data)
        mock_services['ad_processing_service'].process_single_firm_core.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_process_single_firm_concurrent_validation_failure(self, concurrent_workflow_service, mock_services):
        """Test concurrent processing with validation failure."""
        firm_data = {'ID': 'firm123'}  # Missing Name
        mock_services['data_validation_service'].validate_firm_data.return_value = False
        
        result = await concurrent_workflow_service._process_single_firm_concurrent(firm_data)
        
        assert isinstance(result, ProcessingResult)
        assert result.success is False
        assert result.error_msg == "Invalid firm data"
        mock_services['ad_processing_service'].process_single_firm_core.assert_not_called()
    
    @pytest.mark.asyncio
    async def test_process_single_firm_concurrent_processing_failure(self, concurrent_workflow_service, mock_services):
        """Test concurrent processing with core processing failure."""
        firm_data = {'ID': 'firm123', 'Name': 'Test Firm'}
        mock_services['ad_processing_service'].process_single_firm_core = AsyncMock(
            return_value=(False, 'Network timeout', 0, 0, 0, 0)
        )
        
        concurrent_workflow_service._get_available_session = AsyncMock(
            return_value=mock_services['session_manager']
        )
        
        result = await concurrent_workflow_service._process_single_firm_concurrent(firm_data)
        
        assert isinstance(result, ProcessingResult)
        assert result.success is False
        assert result.error_msg == 'Network timeout'
        mock_services['error_handling_service'].add_firm_error.assert_called_once_with(
            'firm123', 'Test Firm', 'Network timeout'
        )
    
    @pytest.mark.asyncio
    async def test_process_single_firm_concurrent_exception(self, concurrent_workflow_service, mock_services):
        """Test concurrent processing with unexpected exception."""
        firm_data = {'ID': 'firm123', 'Name': 'Test Firm'}
        mock_services['ad_processing_service'].process_single_firm_core = AsyncMock(
            side_effect=Exception('Unexpected error')
        )
        
        concurrent_workflow_service._get_available_session = AsyncMock(
            return_value=mock_services['session_manager']
        )
        
        result = await concurrent_workflow_service._process_single_firm_concurrent(firm_data)
        
        assert isinstance(result, ProcessingResult)
        assert result.success is False
        assert 'Concurrent processing exception' in result.error_msg
        mock_services['error_handling_service'].add_firm_error.assert_called_once()


class TestConcurrentWorkflows:
    """Test high-level concurrent workflow functionality."""
    
    @pytest.mark.asyncio
    async def test_run_full_scrape_workflow_concurrent_success(self, concurrent_workflow_service, mock_services):
        """Test successful concurrent full scrape workflow."""
        # Mock internal methods
        concurrent_workflow_service._initialize_session_pool = AsyncMock()
        concurrent_workflow_service._get_filtered_law_firms = AsyncMock(return_value=[
            {'ID': 'firm1', 'Name': 'Firm 1'},
            {'ID': 'firm2', 'Name': 'Firm 2'}
        ])
        concurrent_workflow_service._process_firms_concurrent_batches = AsyncMock(return_value=True)
        concurrent_workflow_service._cleanup_session_pool = AsyncMock()
        
        result = await concurrent_workflow_service.run_full_scrape_workflow_concurrent()
        
        assert result is True
        mock_services['error_handling_service'].clear_errors.assert_called_once()
        mock_services['session_manager'].create_new_session.assert_called_once()
        concurrent_workflow_service._initialize_session_pool.assert_called_once()
        concurrent_workflow_service._process_firms_concurrent_batches.assert_called_once()
        mock_services['error_handling_service'].summarize_errors.assert_called_once()
        concurrent_workflow_service._cleanup_session_pool.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_run_full_scrape_workflow_concurrent_disabled(self, mock_config, mock_logger, mock_services, mock_progress):
        """Test workflow when concurrent processing is disabled."""
        mock_config['enable_concurrent_processing'] = False
        service = ConcurrentWorkflowService(mock_config, mock_logger)
        service.set_dependencies(
            mock_services['ad_processing_service'],
            mock_services['data_validation_service'], 
            mock_services['error_handling_service'],
            mock_services['session_manager'],
            mock_progress
        )
        
        # Mock the parent's run_full_scrape_workflow method
        with patch.object(service.__class__.__bases__[0], 'run_full_scrape_workflow') as mock_parent:
            mock_parent.return_value = True
            
            result = await service.run_full_scrape_workflow()
            
            assert result is True
            mock_parent.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_run_full_scrape_workflow_session_failure(self, concurrent_workflow_service, mock_services):
        """Test concurrent workflow when initial session creation fails."""
        mock_services['session_manager'].create_new_session = AsyncMock(return_value=False)
        
        result = await concurrent_workflow_service.run_full_scrape_workflow_concurrent()
        
        assert result is False
        mock_services['error_handling_service'].clear_errors.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_run_ignore_list_workflow_concurrent(self, concurrent_workflow_service, mock_services):
        """Test concurrent ignore list workflow."""
        # Mock internal methods
        concurrent_workflow_service._initialize_session_pool = AsyncMock()
        concurrent_workflow_service._fetch_ignore_list_firms = AsyncMock(return_value=[
            {'ID': 'firm4', 'Name': 'Ignored Firm 1'},
            {'ID': 'firm5', 'Name': 'Ignored Firm 2'}
        ])
        concurrent_workflow_service._adjust_date_range_for_ignore_list = Mock(return_value=('old_start', 'old_end'))
        concurrent_workflow_service._process_firms_concurrent_batches = AsyncMock(return_value=True)
        concurrent_workflow_service._restore_date_range = Mock()
        concurrent_workflow_service._cleanup_session_pool = AsyncMock()
        
        result = await concurrent_workflow_service.run_ignore_list_workflow()
        
        assert result is True
        mock_services['error_handling_service'].clear_errors.assert_called_once()
        concurrent_workflow_service._initialize_session_pool.assert_called_once()
        concurrent_workflow_service._process_firms_concurrent_batches.assert_called_once()
        concurrent_workflow_service._cleanup_session_pool.assert_called_once()


class TestPerformanceMonitoring:
    """Test performance monitoring functionality."""
    
    def test_get_processing_stats(self, concurrent_workflow_service):
        """Test getting processing statistics."""
        # Modify some stats
        concurrent_workflow_service.processing_stats['total_firms'] = 10
        concurrent_workflow_service.processing_stats['successful_firms'] = 8
        concurrent_workflow_service.processing_stats['failed_firms'] = 2
        
        stats = concurrent_workflow_service.get_processing_stats()
        
        assert stats['total_firms'] == 10
        assert stats['successful_firms'] == 8
        assert stats['failed_firms'] == 2
        
        # Ensure it returns a copy
        stats['total_firms'] = 999
        assert concurrent_workflow_service.processing_stats['total_firms'] == 10
    
    def test_reset_processing_stats(self, concurrent_workflow_service):
        """Test resetting processing statistics."""
        # Set some initial stats
        concurrent_workflow_service.processing_stats['total_firms'] = 10
        concurrent_workflow_service.processing_stats['successful_firms'] = 8
        
        concurrent_workflow_service.reset_processing_stats()
        
        stats = concurrent_workflow_service.get_processing_stats()
        assert all(value == 0 or value == 0.0 for value in stats.values())
    
    @patch('src.fb_ads.services.concurrent_workflow_service.datetime')
    def test_log_processing_stats(self, mock_datetime, concurrent_workflow_service):
        """Test logging of processing statistics."""
        # Setup mock datetime
        mock_datetime.now.return_value = datetime(2025, 1, 15, 10, 30, 0)
        
        # Set some stats
        concurrent_workflow_service.processing_stats.update({
            'total_firms': 10,
            'successful_firms': 8,
            'failed_firms': 2,
            'total_processing_time': 100.0,
            'concurrent_batches': 2
        })
        
        total_time = timedelta(seconds=120)
        
        # Should not raise exception
        concurrent_workflow_service._log_processing_stats(total_time)
        
        # Verify avg_batch_time was calculated
        assert concurrent_workflow_service.processing_stats['avg_batch_time'] == 50.0


class TestProcessingResult:
    """Test ProcessingResult dataclass."""
    
    def test_processing_result_creation(self):
        """Test ProcessingResult creation with all fields."""
        result = ProcessingResult(
            firm_id='firm123',
            firm_name='Test Firm',
            success=True,
            error_msg=None,
            ads_fetched=10,
            items_processed=8,
            items_updated=3,
            items_added=5,
            processing_time=2.5
        )
        
        assert result.firm_id == 'firm123'
        assert result.firm_name == 'Test Firm'
        assert result.success is True
        assert result.error_msg is None
        assert result.ads_fetched == 10
        assert result.items_processed == 8
        assert result.items_updated == 3
        assert result.items_added == 5
        assert result.processing_time == 2.5
    
    def test_processing_result_defaults(self):
        """Test ProcessingResult creation with default values."""
        result = ProcessingResult(
            firm_id='firm123',
            firm_name='Test Firm',
            success=False
        )
        
        assert result.firm_id == 'firm123'
        assert result.firm_name == 'Test Firm'
        assert result.success is False
        assert result.error_msg is None
        assert result.ads_fetched == 0
        assert result.items_processed == 0
        assert result.items_updated == 0
        assert result.items_added == 0
        assert result.processing_time == 0.0


class TestProcessingBatch:
    """Test ProcessingBatch dataclass."""
    
    def test_processing_batch_creation(self):
        """Test ProcessingBatch creation."""
        firms = [
            {'ID': 'firm1', 'Name': 'Firm 1'},
            {'ID': 'firm2', 'Name': 'Firm 2'}
        ]
        
        batch = ProcessingBatch(
            firms=firms,
            batch_id=1,
            total_batches=3
        )
        
        assert batch.firms == firms
        assert batch.batch_id == 1
        assert batch.total_batches == 3