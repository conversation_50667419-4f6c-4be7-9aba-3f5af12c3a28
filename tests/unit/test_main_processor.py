"""Tests for MainProcessor upload workflow and session management."""
import pytest
from unittest.mock import AsyncMock, MagicMock, patch
import asyncio

from src.main import MainProcessor


@pytest.fixture
def mock_config():
    """Mock configuration for MainProcessor."""
    config = MagicMock()
    config.upload_enabled = True
    config.upload_types = {'s3', 'dynamodb'}
    config.get = MagicMock(side_effect=lambda key, default=None: {
        'upload_enabled': True,
        'bucket_name': 'test-bucket',
        'uploader_workers': 5
    }.get(key, default))
    return config


@pytest.fixture
def mock_file_handler():
    """Mock file handler."""
    handler = MagicMock()
    handler.find_json_files = MagicMock(return_value=['test1.json', 'test2.json'])
    return handler


@pytest.fixture  
def mock_s3_manager():
    """Mock S3AsyncStorage manager."""
    mock_s3 = AsyncMock()
    mock_s3.__aenter__ = AsyncMock(return_value=mock_s3)
    mock_s3.__aexit__ = AsyncMock()
    return mock_s3


@pytest.fixture
def mock_pacer_db():
    """Mock PacerRepository with storage."""
    mock_pacer = MagicMock()
    mock_storage = AsyncMock()
    mock_storage.__aenter__ = AsyncMock(return_value=mock_storage)
    mock_storage.__aexit__ = AsyncMock()
    mock_pacer.storage = mock_storage
    return mock_pacer


@pytest.fixture
def mock_uploader():
    """Mock uploader."""
    uploader = AsyncMock()
    uploader.upload_batch_to_aws_async = AsyncMock(return_value={
        'uploaded': ['test1.json'],
        'skipped': [],
        'failed': ['test2.json']
    })
    return uploader


class TestMainProcessorUploadWorkflow:
    """Test MainProcessor upload workflow and context management."""
    
    @pytest.mark.asyncio
    async def test_s3_only_upload_context_management(self, mock_config, mock_file_handler):
        """Test upload workflow with S3 only using proper context management."""
        processor = MainProcessor(mock_config, mock_file_handler)
        
        # Mock S3 manager creation
        mock_s3_manager = AsyncMock()
        mock_s3_manager.__aenter__ = AsyncMock(return_value=mock_s3_manager)
        mock_s3_manager.__aexit__ = AsyncMock()
        
        # Mock uploader creation
        mock_uploader = AsyncMock()
        mock_uploader.upload_batch_to_aws_async = AsyncMock(return_value={
            'uploaded': ['test1.json'],
            'skipped': [],
            'failed': []
        })
        
        with patch('src.main.S3AsyncStorage', return_value=mock_s3_manager), \
             patch('src.main.Uploader', return_value=mock_uploader):
            
            # Set upload types to S3 only
            upload_types = {'s3'}
            
            result = await processor.run_upload_workflow(
                processed_summary=[],
                upload_types=upload_types,
                force_s3_upload=False
            )
            
            # Verify S3 context manager was used
            mock_s3_manager.__aenter__.assert_called_once()
            mock_s3_manager.__aexit__.assert_called_once()
            
            # Verify upload was called
            mock_uploader.upload_batch_to_aws_async.assert_called_once()
            
            # Verify result
            assert 'uploaded' in result
            assert result['uploaded'] == ['test1.json']
    
    @pytest.mark.asyncio
    async def test_dynamodb_only_upload_context_management(self, mock_config, mock_file_handler):
        """Test upload workflow with DynamoDB only using proper context management."""
        processor = MainProcessor(mock_config, mock_file_handler)
        
        # Mock PacerRepository with storage
        mock_pacer_db = MagicMock()
        mock_storage = AsyncMock()
        mock_storage.__aenter__ = AsyncMock(return_value=mock_storage)
        mock_storage.__aexit__ = AsyncMock()
        mock_pacer_db.storage = mock_storage
        
        # Mock uploader creation
        mock_uploader = AsyncMock()
        mock_uploader.upload_batch_to_aws_async = AsyncMock(return_value={
            'uploaded': ['test1.json'],
            'skipped': [],
            'failed': []
        })
        
        with patch('src.main.PacerRepository', return_value=mock_pacer_db), \
             patch('src.main.Uploader', return_value=mock_uploader):
            
            # Set upload types to DynamoDB only
            upload_types = {'dynamodb'}
            
            result = await processor.run_upload_workflow(
                processed_summary=[],
                upload_types=upload_types,
                force_s3_upload=False
            )
            
            # Verify DynamoDB storage context manager was used
            mock_storage.__aenter__.assert_called_once()
            mock_storage.__aexit__.assert_called_once()
            
            # Verify upload was called
            mock_uploader.upload_batch_to_aws_async.assert_called_once()
            
            # Verify result
            assert 'uploaded' in result
            assert result['uploaded'] == ['test1.json']
    
    @pytest.mark.asyncio
    async def test_both_s3_and_dynamodb_upload_context_management(self, mock_config, mock_file_handler):
        """Test upload workflow with both S3 and DynamoDB using proper context management."""
        processor = MainProcessor(mock_config, mock_file_handler)
        
        # Mock S3 manager
        mock_s3_manager = AsyncMock()
        mock_s3_manager.__aenter__ = AsyncMock(return_value=mock_s3_manager)
        mock_s3_manager.__aexit__ = AsyncMock()
        
        # Mock PacerRepository with storage
        mock_pacer_db = MagicMock()
        mock_storage = AsyncMock()
        mock_storage.__aenter__ = AsyncMock(return_value=mock_storage)
        mock_storage.__aexit__ = AsyncMock()
        mock_pacer_db.storage = mock_storage
        
        # Mock uploader creation
        mock_uploader = AsyncMock()
        mock_uploader.upload_batch_to_aws_async = AsyncMock(return_value={
            'uploaded': ['test1.json'],
            'skipped': [],
            'failed': []
        })
        
        with patch('src.main.S3AsyncStorage', return_value=mock_s3_manager), \
             patch('src.main.PacerRepository', return_value=mock_pacer_db), \
             patch('src.main.Uploader', return_value=mock_uploader):
            
            # Set upload types to both
            upload_types = {'s3', 'dynamodb'}
            
            result = await processor.run_upload_workflow(
                processed_summary=[],
                upload_types=upload_types,
                force_s3_upload=False
            )
            
            # Verify both context managers were used
            mock_s3_manager.__aenter__.assert_called_once()
            mock_s3_manager.__aexit__.assert_called_once()
            mock_storage.__aenter__.assert_called_once()
            mock_storage.__aexit__.assert_called_once()
            
            # Verify upload was called
            mock_uploader.upload_batch_to_aws_async.assert_called_once()
            
            # Verify result
            assert 'uploaded' in result
            assert result['uploaded'] == ['test1.json']
    
    @pytest.mark.asyncio
    async def test_upload_workflow_exception_handling(self, mock_config, mock_file_handler):
        """Test that context managers properly clean up even with exceptions."""
        processor = MainProcessor(mock_config, mock_file_handler)
        
        # Mock S3 manager
        mock_s3_manager = AsyncMock()
        mock_s3_manager.__aenter__ = AsyncMock(return_value=mock_s3_manager)
        mock_s3_manager.__aexit__ = AsyncMock()
        
        # Mock uploader that raises exception
        mock_uploader = AsyncMock()
        mock_uploader.upload_batch_to_aws_async = AsyncMock(side_effect=Exception("Upload failed"))
        
        with patch('src.main.S3AsyncStorage', return_value=mock_s3_manager), \
             patch('src.main.Uploader', return_value=mock_uploader):
            
            upload_types = {'s3'}
            
            # Should handle exception gracefully
            result = await processor.run_upload_workflow(
                processed_summary=[],
                upload_types=upload_types,
                force_s3_upload=False
            )
            
            # Verify context manager cleanup was called despite exception
            mock_s3_manager.__aenter__.assert_called_once()
            mock_s3_manager.__aexit__.assert_called_once()
            
            # Should return error result
            assert 'failed' in result
    
    @pytest.mark.asyncio
    async def test_no_valid_storage_instances(self, mock_config, mock_file_handler):
        """Test handling when no valid storage instances are available."""
        processor = MainProcessor(mock_config, mock_file_handler)
        
        # Don't mock any storage instances
        upload_types = {'s3', 'dynamodb'}
        
        result = await processor.run_upload_workflow(
            processed_summary=[],
            upload_types=upload_types,
            force_s3_upload=False
        )
        
        # Should return appropriate error result
        assert 'failed' in result
        assert 'No valid storage instances' in result['failed']
    
    @pytest.mark.asyncio
    async def test_pacer_db_without_storage_attribute(self, mock_config, mock_file_handler):
        """Test handling when PacerRepository doesn't have storage attribute."""
        processor = MainProcessor(mock_config, mock_file_handler)
        
        # Mock PacerRepository without storage attribute
        mock_pacer_db = MagicMock()
        # Don't set storage attribute
        
        with patch('src.main.PacerRepository', return_value=mock_pacer_db):
            
            upload_types = {'dynamodb'}
            
            result = await processor.run_upload_workflow(
                processed_summary=[],
                upload_types=upload_types,
                force_s3_upload=False
            )
            
            # Should handle gracefully
            assert 'failed' in result


class TestMainProcessorAsyncResourceCleanup:
    """Test async resource cleanup functionality."""
    
    @pytest.mark.asyncio
    async def test_cleanup_async_resources_with_open_sessions(self):
        """Test cleanup_async_resources finds and closes open aiohttp sessions."""
        from src.main import cleanup_async_resources
        
        # Mock aiohttp.ClientSession
        mock_open_session = MagicMock()
        mock_open_session.closed = False
        mock_open_session.close = AsyncMock()
        
        mock_closed_session = MagicMock()
        mock_closed_session.closed = True
        
        # Mock gc.get_objects to return both open and closed sessions
        with patch('gc.get_objects', return_value=[mock_open_session, mock_closed_session, "not_a_session"]), \
             patch('aiohttp.ClientSession') as mock_session_class:
            
            # Configure isinstance to return True for our mock sessions
            original_isinstance = isinstance
            def mock_isinstance(obj, cls):
                if cls == mock_session_class:
                    return obj in [mock_open_session, mock_closed_session]
                return original_isinstance(obj, cls)
            
            with patch('builtins.isinstance', side_effect=mock_isinstance):
                await cleanup_async_resources()
            
            # Verify only open session was closed
            mock_open_session.close.assert_called_once()
            assert not hasattr(mock_closed_session, 'close') or mock_closed_session.close.call_count == 0
    
    @pytest.mark.asyncio
    async def test_cleanup_async_resources_no_open_sessions(self):
        """Test cleanup_async_resources when no open sessions exist."""
        from src.main import cleanup_async_resources
        
        # Mock gc.get_objects to return no aiohttp sessions
        with patch('gc.get_objects', return_value=["not_a_session", 123, {}]):
            # Should not raise any exceptions
            await cleanup_async_resources()
    
    @pytest.mark.asyncio
    async def test_cleanup_async_resources_exception_handling(self):
        """Test cleanup_async_resources handles exceptions during session close."""
        from src.main import cleanup_async_resources
        
        # Mock session that raises exception on close
        mock_session = MagicMock()
        mock_session.closed = False
        mock_session.close = AsyncMock(side_effect=Exception("Close failed"))
        
        with patch('gc.get_objects', return_value=[mock_session]), \
             patch('aiohttp.ClientSession') as mock_session_class:
            
            # Configure isinstance
            original_isinstance = isinstance
            def mock_isinstance(obj, cls):
                if cls == mock_session_class:
                    return obj == mock_session
                return original_isinstance(obj, cls)
            
            with patch('builtins.isinstance', side_effect=mock_isinstance):
                # Should not raise exception despite session.close() error
                await cleanup_async_resources()
            
            # Verify close was attempted
            mock_session.close.assert_called_once()


class TestMainProcessorGranularUploadFlags:
    """Test granular upload flag handling in MainProcessor."""
    
    @pytest.fixture
    def mock_processor(self):
        """Mock MainProcessor with granular upload flags."""
        processor = MagicMock()
        processor.logger = MagicMock()
        processor.file_handler = MagicMock()
        processor.run_upload_workflow = AsyncMock()
        return processor
    
    def test_granular_upload_flags_parsing(self):
        """Test that granular upload flags are properly parsed."""
        from src.main import MainProcessor
        
        # Test config with granular flags
        params = {
            'upload_json_to_dynamodb': True,
            'upload_pdfs_to_s3': False,
            'upload_reports_to_s3': True,
            'date': '12/13/25'
        }
        
        # Mock dependencies
        with patch('src.main.load_config'), \
             patch('src.main.FileHandler'):
            
            processor = MainProcessor(params)
            
            # Verify flags are accessible
            assert processor.params.get('upload_json_to_dynamodb') is True
            assert processor.params.get('upload_pdfs_to_s3') is False
            assert processor.params.get('upload_reports_to_s3') is True
    
    @pytest.mark.asyncio
    async def test_json_only_upload_workflow(self):
        """Test upload workflow when only JSON to DynamoDB is enabled."""
        from src.main import MainProcessor
        
        params = {
            'upload_json_to_dynamodb': True,
            'upload_pdfs_to_s3': False,
            'upload_reports_to_s3': False,
            'date': '12/13/25'
        }
        
        with patch('src.main.load_config'), \
             patch('src.main.FileHandler') as mock_file_handler:
            
            processor = MainProcessor(params)
            processor._handle_post_processing_and_upload = AsyncMock()
            
            # Simulate the logic from _handle_post_processing_and_upload
            upload_json = processor.params.get('upload_json_to_dynamodb', False)
            upload_pdfs = processor.params.get('upload_pdfs_to_s3', False)
            
            assert upload_json is True
            assert upload_pdfs is False
            
            # Verify upload targets would be set correctly
            upload_targets = set()
            if upload_json:
                upload_targets.add('dynamodb')
            if upload_pdfs:
                upload_targets.add('s3')
                
            assert upload_targets == {'dynamodb'}
    
    @pytest.mark.asyncio
    async def test_pdfs_only_upload_workflow(self):
        """Test upload workflow when only PDFs to S3 is enabled."""
        from src.main import MainProcessor
        
        params = {
            'upload_json_to_dynamodb': False,
            'upload_pdfs_to_s3': True,
            'upload_reports_to_s3': False,
            'date': '12/13/25'
        }
        
        with patch('src.main.load_config'), \
             patch('src.main.FileHandler'):
            
            processor = MainProcessor(params)
            
            # Simulate the logic from _handle_post_processing_and_upload
            upload_json = processor.params.get('upload_json_to_dynamodb', False)
            upload_pdfs = processor.params.get('upload_pdfs_to_s3', False)
            
            assert upload_json is False
            assert upload_pdfs is True
            
            # Verify upload targets would be set correctly
            upload_targets = set()
            if upload_json:
                upload_targets.add('dynamodb')
            if upload_pdfs:
                upload_targets.add('s3')
                
            assert upload_targets == {'s3'}
            
            # Verify force_s3_upload would be True when PDFs requested
            force_s3_upload = upload_pdfs
            assert force_s3_upload is True
    
    @pytest.mark.asyncio
    async def test_both_json_and_pdfs_upload_workflow(self):
        """Test upload workflow when both JSON and PDFs are enabled."""
        from src.main import MainProcessor
        
        params = {
            'upload_json_to_dynamodb': True,
            'upload_pdfs_to_s3': True,
            'upload_reports_to_s3': False,
            'date': '12/13/25'
        }
        
        with patch('src.main.load_config'), \
             patch('src.main.FileHandler'):
            
            processor = MainProcessor(params)
            
            # Simulate the logic from _handle_post_processing_and_upload
            upload_json = processor.params.get('upload_json_to_dynamodb', False)
            upload_pdfs = processor.params.get('upload_pdfs_to_s3', False)
            
            assert upload_json is True
            assert upload_pdfs is True
            
            # Verify upload targets would be set correctly
            upload_targets = set()
            if upload_json:
                upload_targets.add('dynamodb')
            if upload_pdfs:
                upload_targets.add('s3')
                
            assert upload_targets == {'s3', 'dynamodb'}
    
    @pytest.mark.asyncio
    async def test_no_uploads_enabled_skips_workflow(self):
        """Test that no upload workflow runs when all flags are False."""
        from src.main import MainProcessor
        
        params = {
            'upload_json_to_dynamodb': False,
            'upload_pdfs_to_s3': False,
            'upload_reports_to_s3': True,  # Reports handled separately
            'date': '12/13/25'
        }
        
        with patch('src.main.load_config'), \
             patch('src.main.FileHandler'):
            
            processor = MainProcessor(params)
            
            # Simulate the logic from _handle_post_processing_and_upload
            upload_json = processor.params.get('upload_json_to_dynamodb', False)
            upload_pdfs = processor.params.get('upload_pdfs_to_s3', False)
            
            assert upload_json is False
            assert upload_pdfs is False
            
            # Verify no upload workflow would run
            should_run_upload = upload_json or upload_pdfs
            assert should_run_upload is False


@pytest.mark.asyncio
async def test_main_function_cleanup_integration():
    """Test that main function calls cleanup_async_resources."""
    from src.main import main
    
    # Mock MainProcessor and cleanup function
    mock_processor = AsyncMock()
    mock_processor.run = AsyncMock()
    
    with patch('src.main.MainProcessor', return_value=mock_processor), \
         patch('src.main.cleanup_async_resources') as mock_cleanup, \
         patch('src.main.ScraperConfig'), \
         patch('src.main.FileHandler'):
        
        await main()
        
        # Verify cleanup was called
        mock_cleanup.assert_called_once()


class TestMainProcessorFBAdConfigMerging:
    """Test FB ads configuration merging behavior in MainProcessor."""
    
    @pytest.fixture
    def base_config(self):
        """Base config without FB ads processing flags."""
        return {
            'iso_date': '20241212',
            'bucket_name': 'test-bucket',
            'aws_access_key': 'test-key',
            'aws_secret_key': 'test-secret',
            'aws_region': 'us-west-2',
            'project_root': '/tmp/test',
            'DATA_DIR': '/tmp/test/data',
            'directories': {
                'base_dir': '/tmp/test'
            }
        }
    
    @pytest.fixture
    def fb_params(self):
        """FB ads parameters that should be merged."""
        return {
            'defer_image_processing': True,
            'image_queue_dir': './test_image_queue',
            'image_queue': {
                'directory': './test_image_queue',
                'batch_size': 100,
                'cleanup_days': 30
            },
            'headless': True,
            'use_proxy': True,
            'mobile_proxy': False,
            'render_html': False,
            'airplane_mode': False,
            'max_ad_pages': 50,
            'api_retries': 5,
            'api_backoff_base': 1.7,
            'payload_retries': 3,
            'oxylabs_num_proxies': 2000,
            'proxy_ban_duration': 600,
            'max_proxy_failures': 3,
            'rotate_proxy_between_firms': True,
            'rotate_proxy_per_page': False,
            'facebook_ads': {
                'enabled': True,
                'app_id': 'test_app_id',
                'app_secret': 'test_app_secret',
                'access_token': 'test_token',
                'ad_account_id': 'test_account'
            },
            'fb_ads': True
        }
    
    @pytest.mark.asyncio
    async def test_fb_ads_config_merging_with_defer_processing(self, base_config, fb_params):
        """Test that FB ads processing flags are properly merged into config."""
        
        # Create a MainProcessor instance with params containing FB flags
        params = fb_params.copy()
        params.update({'date': '12/12/24'})
        
        with patch('src.main.load_config', return_value=base_config), \
             patch('src.main.FacebookAdsOrchestrator') as mock_orchestrator_class, \
             patch('aiohttp.ClientSession') as mock_session_class:
            
            mock_session = AsyncMock()
            mock_session_class.return_value.__aenter__.return_value = mock_session
            mock_session_class.return_value.__aexit__.return_value = None
            
            mock_orchestrator = AsyncMock()
            mock_orchestrator.run = AsyncMock()
            mock_orchestrator_class.return_value = mock_orchestrator
            
            processor = MainProcessor(params, config=base_config)
            
            # Run FB ads processing
            await processor.run_fb_ads()
            
            # Verify orchestrator was called with merged config
            mock_orchestrator_class.assert_called_once()
            call_args = mock_orchestrator_class.call_args
            merged_config = call_args[0][0]  # First positional arg is the config
            
            # Verify critical FB processing flags were merged
            assert merged_config['defer_image_processing'] is True
            assert merged_config['image_queue_dir'] == './test_image_queue'
            assert merged_config['headless'] is True
            assert merged_config['use_proxy'] is True
            assert merged_config['max_ad_pages'] == 50
            assert merged_config['api_retries'] == 5
            
            # Verify original config values are preserved
            assert merged_config['iso_date'] == '20241212'
            assert merged_config['bucket_name'] == 'test-bucket'
            
            # Verify orchestrator.run was called
            mock_orchestrator.run.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_fb_ads_disabled_skips_processing(self, base_config):
        """Test that FB ads processing is skipped when disabled."""
        
        params = {
            'date': '12/12/24',
            'facebook_ads': {'enabled': False},
            'fb_ads': False
        }
        
        with patch('src.main.load_config', return_value=base_config), \
             patch('src.main.FacebookAdsOrchestrator') as mock_orchestrator_class:
            
            processor = MainProcessor(params, config=base_config)
            
            # Run FB ads processing
            await processor.run_fb_ads()
            
            # Verify orchestrator was never instantiated
            mock_orchestrator_class.assert_not_called()
    
    @pytest.mark.asyncio
    async def test_fb_ads_missing_credentials_skips_processing(self, base_config, fb_params):
        """Test that FB ads processing is skipped when credentials are missing."""
        
        # Remove required credentials
        params = fb_params.copy()
        params['facebook_ads'] = {'enabled': True}  # Missing app_id, app_secret, etc.
        params.update({'date': '12/12/24'})
        
        with patch('src.main.load_config', return_value=base_config), \
             patch('src.main.FacebookAdsOrchestrator') as mock_orchestrator_class:
            
            processor = MainProcessor(params, config=base_config)
            
            # Run FB ads processing
            await processor.run_fb_ads()
            
            # Verify orchestrator was never instantiated
            mock_orchestrator_class.assert_not_called()
    
    @pytest.mark.asyncio
    async def test_fb_ads_config_merging_preserves_base_config(self, base_config, fb_params):
        """Test that config merging doesn't modify the original base config."""
        
        original_config = base_config.copy()
        params = fb_params.copy()
        params.update({'date': '12/12/24'})
        
        with patch('src.main.load_config', return_value=base_config), \
             patch('src.main.FacebookAdsOrchestrator') as mock_orchestrator_class, \
             patch('aiohttp.ClientSession') as mock_session_class:
            
            mock_session = AsyncMock()
            mock_session_class.return_value.__aenter__.return_value = mock_session
            mock_session_class.return_value.__aexit__.return_value = None
            
            mock_orchestrator = AsyncMock()
            mock_orchestrator.run = AsyncMock()
            mock_orchestrator_class.return_value = mock_orchestrator
            
            processor = MainProcessor(params, config=base_config)
            
            # Store processor's config before FB ads processing
            processor_config_before = processor.config.copy()
            
            # Run FB ads processing
            await processor.run_fb_ads()
            
            # Verify original base_config wasn't modified beyond expected changes
            # MainProcessor constructor adds llm_provider to config, so account for that
            expected_config_changes = {'llm_provider': 'deepseek'}
            expected_config = original_config.copy()
            expected_config.update(expected_config_changes)
            assert base_config == expected_config
            
            # Verify processor's config wasn't modified
            assert processor.config == processor_config_before
    
    def test_fb_processing_flags_list_completeness(self):
        """Test that all necessary FB processing flags are included in the merge list."""
        
        # Read the actual flags from main.py
        from src.main import MainProcessor
        import inspect
        
        # Get the source code of run_fb_ads method
        source = inspect.getsource(MainProcessor.run_fb_ads)
        
        # Verify that the expected flags are present in the code
        expected_flags = [
            'defer_image_processing',
            'image_queue_dir', 
            'image_queue',
            'headless',
            'use_proxy',
            'mobile_proxy',
            'render_html',
            'airplane_mode',
            'max_ad_pages',
            'api_retries',
            'api_backoff_base',
            'payload_retries',
            'oxylabs_num_proxies',
            'proxy_ban_duration',
            'max_proxy_failures',
            'rotate_proxy_between_firms',
            'rotate_proxy_per_page'
        ]
        
        # Check that critical flags are mentioned in the source
        critical_flags = ['defer_image_processing', 'image_queue_dir', 'headless', 'use_proxy']
        for flag in critical_flags:
            assert f"'{flag}'" in source, f"Critical flag '{flag}' not found in fb_processing_flags list"