"""
Tests for AWS DynamoDB configuration handling
"""
import pytest
import os
from unittest.mock import patch, MagicMock
from src.infrastructure.storage.dynamodb_async import AsyncDynamoDBStorage


class TestAWSDynamoDBConfig:
    """Test AWS DynamoDB configuration scenarios"""

    def test_default_aws_region_configuration(self):
        """Test that default AWS region is us-west-2 when not specified"""
        config = MagicMock()
        # No aws_region attribute on config
        del config.aws_region
        
        with patch.dict(os.environ, {}, clear=True):
            storage = AsyncDynamoDBStorage(config)
            # Should use default region from the __aenter__ method
            assert hasattr(storage, 'config')

    @patch.dict(os.environ, {'AWS_REGION': 'us-east-1'})
    def test_aws_region_from_environment(self):
        """Test AWS region configuration from environment variable"""
        config = MagicMock()
        storage = AsyncDynamoDBStorage(config)
        # Environment should take precedence
        assert os.getenv('AWS_REGION') == 'us-east-1'

    @patch.dict(os.environ, {'LEXGENIUS_AWS_REGION': 'eu-west-1'})
    def test_lexgenius_aws_region_from_environment(self):
        """Test LexGenius-specific AWS region environment variable"""
        config = MagicMock()
        storage = AsyncDynamoDBStorage(config)
        assert os.getenv('LEXGENIUS_AWS_REGION') == 'eu-west-1'

    def test_local_dynamodb_endpoint_configuration(self):
        """Test local DynamoDB endpoint configuration"""
        config = MagicMock()
        config.dynamodb_endpoint = 'http://localhost:8000'
        config.aws_region = 'us-west-2'
        
        storage = AsyncDynamoDBStorage(config)
        assert storage.config.dynamodb_endpoint == 'http://localhost:8000'

    def test_aws_cloud_dynamodb_configuration(self):
        """Test AWS cloud DynamoDB configuration (no local endpoint)"""
        config = MagicMock()
        config.dynamodb_endpoint = None
        config.aws_region = 'us-west-2'
        
        storage = AsyncDynamoDBStorage(config)
        assert storage.config.dynamodb_endpoint is None

    @patch.dict(os.environ, {}, clear=True)
    def test_region_priority_order(self):
        """Test that environment variables take priority over config"""
        config = MagicMock()
        config.aws_region = 'config-region'
        
        # Test priority: AWS_REGION > LEXGENIUS_AWS_REGION > REGION_NAME > config
        with patch.dict(os.environ, {'AWS_REGION': 'env-aws-region'}):
            storage = AsyncDynamoDBStorage(config)
            # AWS_REGION should have highest priority
            assert os.getenv('AWS_REGION') == 'env-aws-region'
            
        with patch.dict(os.environ, {'LEXGENIUS_AWS_REGION': 'env-lexgenius-region'}):
            storage = AsyncDynamoDBStorage(config)
            # LEXGENIUS_AWS_REGION should be second priority
            assert os.getenv('LEXGENIUS_AWS_REGION') == 'env-lexgenius-region'

    def test_dynamodb_retry_configuration(self):
        """Test DynamoDB retry configuration defaults"""
        config = MagicMock()
        # Remove retry attributes to test defaults
        del config.dynamodb_max_retries
        del config.dynamodb_base_delay
        del config.dynamodb_max_delay
        
        storage = AsyncDynamoDBStorage(config)
        
        # Should use default values
        assert storage.default_max_retries == 15
        assert storage.default_base_delay == 1.0
        assert storage.default_max_delay == 300.0

    def test_dynamodb_retry_configuration_from_config(self):
        """Test DynamoDB retry configuration from config object"""
        config = MagicMock()
        config.dynamodb_max_retries = 10
        config.dynamodb_base_delay = 2.0
        config.dynamodb_max_delay = 600.0
        
        storage = AsyncDynamoDBStorage(config)
        
        # Should use config values
        assert storage.default_max_retries == 10
        assert storage.default_base_delay == 2.0
        assert storage.default_max_delay == 600.0

    @pytest.mark.asyncio
    async def test_aws_context_manager_region_resolution(self):
        """Test that AWS region is properly resolved in context manager"""
        config = MagicMock()
        config.aws_region = 'us-west-2'
        config.dynamodb_endpoint = None
        
        storage = AsyncDynamoDBStorage(config)
        
        # Mock the aioboto3 session and resource
        with patch('aioboto3.Session') as mock_session:
            mock_resource = MagicMock()
            mock_session.return_value.resource.return_value.__aenter__ = MagicMock(return_value=mock_resource)
            
            # Test context manager entry
            async with storage as s:
                # Verify that session.resource was called with correct parameters
                mock_session.return_value.resource.assert_called_with(
                    'dynamodb',
                    region_name='us-west-2',
                    endpoint_url=None
                )

    @pytest.mark.asyncio 
    async def test_local_dynamodb_context_manager(self):
        """Test context manager with local DynamoDB endpoint"""
        config = MagicMock()
        config.aws_region = 'us-west-2'
        config.dynamodb_endpoint = 'http://localhost:8000'
        
        storage = AsyncDynamoDBStorage(config)
        
        with patch('aioboto3.Session') as mock_session:
            mock_resource = MagicMock()
            mock_session.return_value.resource.return_value.__aenter__ = MagicMock(return_value=mock_resource)
            
            async with storage as s:
                # Verify local endpoint is used
                mock_session.return_value.resource.assert_called_with(
                    'dynamodb',
                    region_name='us-west-2',
                    endpoint_url='http://localhost:8000'
                )