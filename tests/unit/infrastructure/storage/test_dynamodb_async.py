"""Tests for AsyncDynamoDBStorage implementation."""
import pytest
from unittest.mock import Async<PERSON>ock, MagicMock, patch
from botocore.exceptions import ClientError

from src.infrastructure.storage.dynamodb_async import AsyncDynamoDBStorage


@pytest.fixture
def dynamodb_config():
    """DynamoDB configuration fixture."""
    return {
        'aws_access_key': 'test-key',
        'aws_secret_key': 'test-secret',
        'aws_region': 'us-west-2'
    }


@pytest.fixture
async def dynamodb_storage(dynamodb_config):
    """DynamoDB storage fixture with mocked client."""
    storage = AsyncDynamoDBStorage(
        aws_access_key=dynamodb_config['aws_access_key'],
        aws_secret_key=dynamodb_config['aws_secret_key'],
        aws_region=dynamodb_config['aws_region']
    )
    
    # Mock the session and dynamodb
    storage.session = AsyncMock()
    storage.dynamodb = AsyncMock()
    
    yield storage
    
    # Cleanup
    storage.session = None
    storage.dynamodb = None


class TestAsyncDynamoDBStorage:
    """Test AsyncDynamoDBStorage operations."""
    
    @pytest.mark.asyncio
    async def test_initialization(self, dynamodb_config):
        """Test storage initialization."""
        storage = AsyncDynamoDBStorage(**dynamodb_config)
        assert storage.aws_access_key == dynamodb_config['aws_access_key']
        assert storage.aws_secret_key == dynamodb_config['aws_secret_key']
        assert storage.aws_region == dynamodb_config['aws_region']
        assert storage.session is None
        assert storage.dynamodb is None
    
    @pytest.mark.asyncio
    async def test_context_manager_enter(self, dynamodb_config):
        """Test async context manager __aenter__."""
        with patch('aioboto3.Session') as mock_session_class:
            mock_session = AsyncMock()
            mock_dynamodb_cm = AsyncMock()
            mock_dynamodb_cm.__aenter__ = AsyncMock(return_value=MagicMock())
            mock_dynamodb_cm.__aexit__ = AsyncMock()
            mock_session.resource = MagicMock(return_value=mock_dynamodb_cm)
            mock_session_class.return_value = mock_session
            
            storage = AsyncDynamoDBStorage(**dynamodb_config)
            
            # Enter context manager
            result = await storage.__aenter__()
            
            # Verify initialization
            assert result == storage
            assert storage.session is not None
            assert storage.dynamodb is not None
            mock_session_class.assert_called_once()
            mock_session.resource.assert_called_once_with('dynamodb')
    
    @pytest.mark.asyncio
    async def test_context_manager_exit_with_session_cleanup(self, dynamodb_storage):
        """Test async context manager __aexit__ properly closes session."""
        # Mock session close method
        dynamodb_storage.session.close = AsyncMock()
        
        # Call __aexit__
        await dynamodb_storage.__aexit__(None, None, None)
        
        # Verify session cleanup
        dynamodb_storage.session.close.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_context_manager_exit_without_session(self, dynamodb_config):
        """Test __aexit__ handles case where session is None gracefully."""
        storage = AsyncDynamoDBStorage(**dynamodb_config)
        # Don't initialize session
        
        # Should not raise exception
        await storage.__aexit__(None, None, None)
        
        # Verify state
        assert storage.session is None
        assert storage.dynamodb is None
    
    @pytest.mark.asyncio
    async def test_context_manager_exit_with_dynamodb_cleanup(self, dynamodb_storage):
        """Test __aexit__ properly closes dynamodb resource."""
        # Mock dynamodb __aexit__ method
        dynamodb_storage.dynamodb.__aexit__ = AsyncMock()
        dynamodb_storage.session.close = AsyncMock()
        
        # Call __aexit__
        await dynamodb_storage.__aexit__(None, None, None)
        
        # Verify both dynamodb and session cleanup
        dynamodb_storage.dynamodb.__aexit__.assert_called_once_with(None, None, None)
        dynamodb_storage.session.close.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_table(self, dynamodb_storage):
        """Test get_table method."""
        # Mock table object
        mock_table = MagicMock()
        dynamodb_storage.dynamodb.Table = MagicMock(return_value=mock_table)
        
        result = await dynamodb_storage.get_table('test-table')
        
        assert result == mock_table
        dynamodb_storage.dynamodb.Table.assert_called_once_with('test-table')
    
    @pytest.mark.asyncio
    async def test_full_context_manager_lifecycle(self, dynamodb_config):
        """Test complete context manager lifecycle with proper cleanup."""
        with patch('aioboto3.Session') as mock_session_class:
            mock_session = AsyncMock()
            mock_session.close = AsyncMock()
            mock_dynamodb_cm = AsyncMock()
            mock_dynamodb_cm.__aenter__ = AsyncMock(return_value=MagicMock())
            mock_dynamodb_cm.__aexit__ = AsyncMock()
            mock_session.resource = MagicMock(return_value=mock_dynamodb_cm)
            mock_session_class.return_value = mock_session
            
            # Use as context manager
            async with AsyncDynamoDBStorage(**dynamodb_config) as storage:
                assert storage.session is not None
                assert storage.dynamodb is not None
                
            # Verify cleanup was called
            mock_session.close.assert_called_once()
            mock_dynamodb_cm.__aexit__.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_context_manager_with_exception(self, dynamodb_config):
        """Test context manager cleanup happens even with exceptions."""
        with patch('aioboto3.Session') as mock_session_class:
            mock_session = AsyncMock()
            mock_session.close = AsyncMock()
            mock_dynamodb_cm = AsyncMock()
            mock_dynamodb_cm.__aenter__ = AsyncMock(return_value=MagicMock())
            mock_dynamodb_cm.__aexit__ = AsyncMock()
            mock_session.resource = MagicMock(return_value=mock_dynamodb_cm)
            mock_session_class.return_value = mock_session
            
            # Use as context manager with exception
            with pytest.raises(ValueError):
                async with AsyncDynamoDBStorage(**dynamodb_config) as storage:
                    assert storage.session is not None
                    assert storage.dynamodb is not None
                    raise ValueError("Test exception")
                
            # Verify cleanup was still called
            mock_session.close.assert_called_once()
            mock_dynamodb_cm.__aexit__.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_session_cleanup_error_handling(self, dynamodb_storage):
        """Test that session cleanup errors don't raise exceptions."""
        # Mock session close to raise an exception
        dynamodb_storage.session.close = AsyncMock(side_effect=Exception("Close error"))
        dynamodb_storage.dynamodb.__aexit__ = AsyncMock()
        
        # Should not raise exception despite session.close() error
        await dynamodb_storage.__aexit__(None, None, None)
        
        # Verify attempts were made
        dynamodb_storage.session.close.assert_called_once()
        dynamodb_storage.dynamodb.__aexit__.assert_called_once()