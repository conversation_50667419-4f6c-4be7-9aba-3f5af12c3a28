"""Tests for S3 async storage implementation."""
import pytest
from unittest.mock import Async<PERSON>ock, MagicMock, patch
from botocore.exceptions import ClientError

from src.infrastructure.storage.s3_async import S3AsyncStorage


@pytest.fixture
def s3_config():
    """S3 configuration fixture."""
    return {
        'bucket_name': 'test-bucket',
        'aws_access_key': 'test-key',
        'aws_secret_key': 'test-secret',
        'aws_region': 'us-west-2'
    }


@pytest.fixture
async def s3_storage(s3_config):
    """S3 storage fixture with mocked client."""
    storage = S3AsyncStorage(
        bucket_name=s3_config['bucket_name'],
        aws_access_key=s3_config['aws_access_key'],
        aws_secret_key=s3_config['aws_secret_key'],
        aws_region=s3_config['aws_region']
    )
    
    # Mock the client
    mock_client = AsyncMock()
    storage._client = mock_client
    storage._session = MagicMock()
    
    yield storage
    
    # Cleanup
    storage._client = None
    storage._session = None


class TestS3AsyncStorage:
    """Test S3 async storage operations."""
    
    @pytest.mark.asyncio
    async def test_file_exists_true(self, s3_storage):
        """Test file_exists returns True when object exists."""
        s3_storage._client.head_object = AsyncMock()
        
        result = await s3_storage.file_exists('test-key')
        
        assert result is True
        s3_storage._client.head_object.assert_called_once_with(
            Bucket='test-bucket',
            Key='test-key'
        )
        
    @pytest.mark.asyncio
    async def test_file_exists_false(self, s3_storage):
        """Test file_exists returns False when object doesn't exist."""
        error = ClientError(
            {'Error': {'Code': '404'}},
            'HeadObject'
        )
        s3_storage._client.head_object = AsyncMock(side_effect=error)
        
        result = await s3_storage.file_exists('test-key')
        
        assert result is False
        
    @pytest.mark.asyncio
    async def test_upload_content(self, s3_storage):
        """Test uploading content to S3."""
        s3_storage._client.put_object = AsyncMock()
        
        result = await s3_storage.upload_content(
            content='test content',
            object_key='test-key',
            content_type='text/plain'
        )
        
        assert result is True
        s3_storage._client.put_object.assert_called_once()
        
        # Check the call arguments
        call_args = s3_storage._client.put_object.call_args
        assert call_args.kwargs['Bucket'] == 'test-bucket'
        assert call_args.kwargs['Key'] == 'test-key'
        assert call_args.kwargs['Body'] == b'test content'
        assert call_args.kwargs['ContentType'] == 'text/plain'
        
    @pytest.mark.asyncio
    async def test_download_content(self, s3_storage):
        """Test downloading content from S3."""
        mock_response = {
            'Body': AsyncMock(read=AsyncMock(return_value=b'test content'))
        }
        s3_storage._client.get_object = AsyncMock(return_value=mock_response)
        
        result = await s3_storage.download_content('test-key')
        
        assert result == b'test content'
        s3_storage._client.get_object.assert_called_once_with(
            Bucket='test-bucket',
            Key='test-key'
        )
        
    @pytest.mark.asyncio
    async def test_download_content_not_found(self, s3_storage):
        """Test downloading non-existent content returns None."""
        error = ClientError(
            {'Error': {'Code': 'NoSuchKey'}},
            'GetObject'
        )
        s3_storage._client.get_object = AsyncMock(side_effect=error)
        
        result = await s3_storage.download_content('test-key')
        
        assert result is None
        
    @pytest.mark.asyncio
    async def test_list_objects(self, s3_storage):
        """Test listing objects with prefix."""
        # Mock paginator
        mock_paginator = MagicMock()
        mock_page = {
            'Contents': [
                {'Key': 'prefix/file1.txt'},
                {'Key': 'prefix/file2.txt'}
            ]
        }
        
        # Make paginator async iterable
        async def async_paginate(**kwargs):
            yield mock_page
            
        mock_paginator.paginate = MagicMock(return_value=async_paginate())
        s3_storage._client.get_paginator = MagicMock(return_value=mock_paginator)
        
        result = await s3_storage.list_objects('prefix/')
        
        assert result == ['prefix/file1.txt', 'prefix/file2.txt']
        s3_storage._client.get_paginator.assert_called_once_with('list_objects_v2')
        
    @pytest.mark.asyncio
    async def test_delete_object(self, s3_storage):
        """Test deleting an object."""
        s3_storage._client.delete_object = AsyncMock()
        
        result = await s3_storage.delete_object('test-key')
        
        assert result is True
        s3_storage._client.delete_object.assert_called_once_with(
            Bucket='test-bucket',
            Key='test-key'
        )
        
    @pytest.mark.asyncio
    async def test_copy_object(self, s3_storage):
        """Test copying an object."""
        s3_storage._client.copy_object = AsyncMock()
        
        result = await s3_storage.copy_object('source-key', 'dest-key')
        
        assert result is True
        s3_storage._client.copy_object.assert_called_once_with(
            CopySource={'Bucket': 'test-bucket', 'Key': 'source-key'},
            Bucket='test-bucket',
            Key='dest-key'
        )
        
    @pytest.mark.asyncio
    async def test_context_manager(self, s3_config):
        """Test async context manager initialization."""
        with patch('aioboto3.Session') as mock_session_class:
            mock_session = MagicMock()
            mock_client_cm = AsyncMock()
            mock_client_cm.__aenter__ = AsyncMock(return_value=MagicMock())
            mock_client_cm.__aexit__ = AsyncMock()
            mock_session.client = MagicMock(return_value=mock_client_cm)
            mock_session_class.return_value = mock_session
            
            storage = S3AsyncStorage(**s3_config)
            await storage._initialize()
            assert storage._client is not None
            assert storage._session is not None
            
            # Cleanup
            await storage.close()
    
    @pytest.mark.asyncio
    async def test_session_cleanup_on_close(self, s3_config):
        """Test that session is properly closed when close() is called."""
        with patch('aioboto3.Session') as mock_session_class:
            mock_session = AsyncMock()
            mock_session.close = AsyncMock()
            mock_client_cm = AsyncMock()
            mock_client_cm.__aenter__ = AsyncMock(return_value=MagicMock())
            mock_client_cm.__aexit__ = AsyncMock()
            mock_session.client = MagicMock(return_value=mock_client_cm)
            mock_session_class.return_value = mock_session
            
            storage = S3AsyncStorage(**s3_config)
            await storage._initialize()
            
            # Verify session is set
            assert storage._session is not None
            
            # Call close and verify session.close() is called
            await storage.close()
            
            # Verify session cleanup
            mock_session.close.assert_called_once()
            assert storage._session is None
            assert storage._client is None
    
    @pytest.mark.asyncio 
    async def test_context_manager_proper_cleanup(self, s3_config):
        """Test async context manager properly cleans up session."""
        with patch('aioboto3.Session') as mock_session_class:
            mock_session = AsyncMock()
            mock_session.close = AsyncMock()
            mock_client_cm = AsyncMock()
            mock_client_cm.__aenter__ = AsyncMock(return_value=MagicMock())
            mock_client_cm.__aexit__ = AsyncMock()
            mock_session.client = MagicMock(return_value=mock_client_cm)
            mock_session_class.return_value = mock_session
            
            # Use as context manager
            async with S3AsyncStorage(**s3_config) as storage:
                assert storage._session is not None
                assert storage._client is not None
                
            # Verify cleanup was called
            mock_session.close.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_close_without_session(self, s3_config):
        """Test close() handles case where session is None gracefully."""
        storage = S3AsyncStorage(**s3_config)
        # Don't initialize, so session remains None
        
        # Should not raise exception
        await storage.close()
        
        # Verify state
        assert storage._session is None
        assert storage._client is None