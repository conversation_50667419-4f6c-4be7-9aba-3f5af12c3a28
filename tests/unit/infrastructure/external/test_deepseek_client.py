"""Tests for DeepSeek client implementation."""
import json
import pytest
from unittest.mock import Async<PERSON>ock, MagicMock, patch
import aiohttp

from src.infrastructure.external.deepseek_client import DeepSeekClient


@pytest.fixture
def deepseek_client():
    """DeepSeek client fixture."""
    config = {
        'deepseek_api_key': 'test-api-key',
        'openrouter_api_key': 'test-openrouter-key',
        'timeout': 30,
        'max_retries': 2
    }
    return DeepSeekClient(api_key="test-api-key", config=config)


@pytest.fixture
async def deepseek_with_session(deepseek_client):
    """DeepSeek client with mocked session."""
    mock_session = AsyncMock()
    deepseek_client._session = mock_session
    yield deepseek_client
    deepseek_client._session = None


class TestDeepSeekClient:
    """Test DeepSeek client functionality."""
    
    @pytest.mark.asyncio
    async def test_context_manager(self):
        """Test async context manager."""
        client = DeepSeekClient(api_key="test-key", config={'openrouter_api_key': 'test-openrouter-key'})
        
        with patch('aiohttp.ClientSession') as mock_session_class:
            mock_session = AsyncMock()
            mock_session.close = AsyncMock()
            mock_session_class.return_value = mock_session
            
            async with client as c:
                assert c._session is not None
                mock_session_class.assert_called_once()
                
            mock_session.close.assert_called_once()
            
    @pytest.mark.asyncio
    async def test_generate_text_success(self, deepseek_with_session):
        """Test successful text generation."""
        mock_response = AsyncMock()
        mock_response.raise_for_status = MagicMock()
        mock_response.status = 200  # Add status code
        mock_response.json = AsyncMock(return_value={
            'choices': [{
                'message': {'content': 'Generated text response'}
            }]
        })
        
        mock_post = AsyncMock(return_value=mock_response)
        mock_post.__aenter__ = AsyncMock(return_value=mock_response)
        mock_post.__aexit__ = AsyncMock()
        
        deepseek_with_session._session.post = MagicMock(return_value=mock_post)
        
        result = await deepseek_with_session.generate_text(
            "Test prompt",
            model="deepseek-chat",
            temperature=0.5
        )
        
        assert result == "Generated text response"
        
        # Verify the API call
        deepseek_with_session._session.post.assert_called_once()
        call_args = deepseek_with_session._session.post.call_args
        # Check if either DeepSeek Direct or OpenRouter URL was used
        assert call_args[0][0] in [
            "https://api.deepseek.com/v1/chat/completions",
            "https://openrouter.ai/api/v1/chat/completions"
        ]
        
        # Check payload
        payload = call_args[1]['json']
        # Model can be either deepseek-chat or an OpenRouter model
        assert payload['model'] in ['deepseek-chat', 'deepseek/deepseek-chat-v3-0324:free', 'deepseek/deepseek-chat-v3-0324']
        assert payload['temperature'] == 0.5
        assert payload['messages'][1]['content'] == "Test prompt"
        
    @pytest.mark.asyncio
    async def test_generate_text_retry(self, deepseek_with_session):
        """Test retry logic on API errors."""
        # Mock time window check to allow request
        with patch.object(deepseek_with_session, '_is_within_deepseek_window', return_value=True):
            # First call fails with server error, second succeeds
            error_response = AsyncMock()
            error_response.status = 500  # Server error to trigger retry
            error_response.text = AsyncMock(return_value="Internal Server Error")
            
            success_response = AsyncMock()
            success_response.status = 200
            success_response.json = AsyncMock(return_value={
                'choices': [{
                    'message': {'content': 'Success after retry'}
                }]
            })
            
            responses = [error_response, success_response]
            
            async def mock_post_context(*args, **kwargs):
                return responses.pop(0)
                
            mock_post = AsyncMock()
            mock_post.__aenter__ = mock_post_context
            mock_post.__aexit__ = AsyncMock()
            
            deepseek_with_session._session.post = MagicMock(return_value=mock_post)
            
            # Since the DeepSeekClient uses backoff, we need to mock it
            from src.infrastructure.external.deepseek_client import RetryableError
            
            # Mock the _attempt_chat_completion to raise RetryableError first, then succeed
            call_count = 0
            original_attempt = deepseek_with_session._attempt_chat_completion
            
            async def mock_attempt(*args, **kwargs):
                nonlocal call_count
                call_count += 1
                if call_count == 1:
                    raise RetryableError("Server error")
                # On second call, use the success response directly
                return 'Success after retry'
            
            deepseek_with_session._attempt_chat_completion = mock_attempt
            
            result = await deepseek_with_session.generate_text("Test prompt")
                
            assert result == "Success after retry"
            assert call_count == 2
        
    @pytest.mark.asyncio
    async def test_generate_text_max_retries_exceeded(self, deepseek_with_session):
        """Test that max retries raises exception."""
        # Mock the _chat_completion to always fail
        from src.infrastructure.external.deepseek_client import ServiceUnavailableError
        
        async def mock_chat_completion(*args, **kwargs):
            raise ServiceUnavailableError("All configured services failed")
            
        deepseek_with_session._chat_completion = mock_chat_completion
        
        with pytest.raises(ServiceUnavailableError, match="All configured services failed"):
            await deepseek_with_session.generate_text("Test prompt")
        
    @pytest.mark.asyncio
    async def test_process_batch(self, deepseek_with_session):
        """Test batch processing."""
        # Mock generate_text to return different responses
        deepseek_with_session.generate_text = AsyncMock(
            side_effect=["Response 1", "Response 2", Exception("Error")]
        )
        
        results = await deepseek_with_session.process_batch(
            ["Prompt 1", "Prompt 2", "Prompt 3"]
        )
        
        assert results == ["Response 1", "Response 2", None]
        assert deepseek_with_session.generate_text.call_count == 3
        
    @pytest.mark.asyncio
    async def test_extract_structured_data(self, deepseek_with_session):
        """Test structured data extraction."""
        schema = {
            "name": "string",
            "age": "number",
            "items": ["string"]
        }
        
        # Mock _chat_completion to return parsed JSON directly
        expected_result = {
            "name": "John",
            "age": 30,
            "items": ["item1", "item2"]
        }
        
        deepseek_with_session._chat_completion = AsyncMock(
            return_value=expected_result
        )
        
        result = await deepseek_with_session.extract_structured_data(
            "Extract data from this text",
            schema
        )
        
        assert result == expected_result
        
        # Verify _chat_completion was called with json_mode=True
        deepseek_with_session._chat_completion.assert_called_once()
        call_args = deepseek_with_session._chat_completion.call_args
        # Check kwargs
        assert call_args.kwargs['json_mode'] is True
        
        # Check that the messages include the schema
        messages = call_args.kwargs['messages']
        assert len(messages) == 2
        user_message = messages[1]['content']
        assert "Schema:" in user_message
        assert json.dumps(schema, indent=2) in user_message
        
    @pytest.mark.asyncio
    async def test_extract_structured_data_invalid_json(self, deepseek_with_session):
        """Test handling of invalid JSON response."""
        # Mock _chat_completion to raise an exception (simulating JSON parse error)
        deepseek_with_session._chat_completion = AsyncMock(
            side_effect=Exception("Invalid JSON")
        )
        
        result = await deepseek_with_session.extract_structured_data(
            "Test text",
            {"field": "type"}
        )
        
        assert result == {}
        
    @pytest.mark.asyncio
    async def test_no_session_error(self, deepseek_client):
        """Test automatic session initialization when needed."""
        # The client now automatically initializes session when needed
        deepseek_client._session = None
        
        # Track if _ensure_session initializes the session
        original_ensure = deepseek_client._ensure_session
        init_called = False
        
        async def mock_ensure():
            nonlocal init_called
            # Check if session is None before calling original
            if deepseek_client._session is None:
                init_called = True
            await original_ensure()
            
        # Mock aiohttp to avoid actual network calls
        with patch('aiohttp.ClientSession') as mock_session_class:
            mock_session = AsyncMock()
            mock_session_class.return_value = mock_session
            
            # Patch _ensure_session to track initialization
            with patch.object(deepseek_client, '_ensure_session', mock_ensure):
                # Mock _attempt_chat_completion to avoid actual API calls
                with patch.object(deepseek_client, '_attempt_chat_completion', new_callable=AsyncMock) as mock_attempt:
                    mock_attempt.return_value = "Test response"
                    
                    result = await deepseek_client.generate_text("Test")
                    assert result == "Test response"
                    # Verify that session initialization was needed
                    assert init_called
                    assert deepseek_client._session is not None

    @pytest.mark.asyncio
    async def test_extract_case_information_mdl_case(self, deepseek_with_session):
        """Test case information extraction for MDL case."""
        # Mock prompt loading
        with patch.object(deepseek_with_session, '_load_prompt') as mock_load_prompt:
            mock_load_prompt.side_effect = [
                "System prompt for case extraction",
                "User prompt template: {filing}"
            ]
            
            # Mock successful MDL extraction
            mdl_response = {
                "mdl_num": "2741",
                "mdl_cat": "Individual", 
                "title": None,
                "allegations": None
            }
            
            deepseek_with_session._chat_completion = AsyncMock(return_value=mdl_response)
            
            data = {}
            pdf_text = "Sample PDF text about Roundup litigation MDL 2741"
            
            await deepseek_with_session.extract_case_information(data, pdf_text)
            
            # Verify MDL case handling
            assert data['mdl_num'] == "2741"
            assert data['mdl_cat'] == "Individual"
            assert data['title'] is None
            assert data['allegations'] is None
            
            # Verify chat completion was called with correct parameters
            deepseek_with_session._chat_completion.assert_called_once()
            call_args = deepseek_with_session._chat_completion.call_args
            assert call_args.kwargs['json_mode'] is True
            assert call_args.kwargs['temperature'] == 0.1
            assert call_args.kwargs['max_tokens'] == 1000

    @pytest.mark.asyncio
    async def test_extract_case_information_non_mdl_case(self, deepseek_with_session):
        """Test case information extraction for non-MDL case."""
        with patch.object(deepseek_with_session, '_load_prompt') as mock_load_prompt:
            mock_load_prompt.side_effect = [
                "System prompt for case extraction",
                "User prompt template: {filing}"
            ]
            
            # Mock successful non-MDL extraction
            non_mdl_response = {
                "mdl_num": "NA",
                "mdl_cat": "NA",
                "title": "LastPass Data Breach Litigation",
                "allegations": "Plaintiff sues for data breach causing financial harm"
            }
            
            deepseek_with_session._chat_completion = AsyncMock(return_value=non_mdl_response)
            
            data = {}
            pdf_text = "Sample PDF text about LastPass data breach case"
            
            await deepseek_with_session.extract_case_information(data, pdf_text)
            
            # Verify non-MDL case handling
            assert data['mdl_num'] == "NA"
            assert data['mdl_cat'] == "NA"
            assert data['title'] == "LastPass Data Breach Litigation"
            assert data['allegations'] == "Plaintiff sues for data breach causing financial harm"

    @pytest.mark.asyncio
    async def test_extract_case_information_prompt_loading_error(self, deepseek_with_session):
        """Test handling when prompt files cannot be loaded."""
        with patch.object(deepseek_with_session, '_load_prompt') as mock_load_prompt:
            mock_load_prompt.side_effect = FileNotFoundError("Prompt file not found")
            
            data = {}
            pdf_text = "Sample PDF text"
            
            # Should not raise exception, just log error
            await deepseek_with_session.extract_case_information(data, pdf_text)
            
            # Data should remain unchanged
            assert len(data) == 0

    @pytest.mark.asyncio  
    async def test_extract_case_information_json_parse_error(self, deepseek_with_session):
        """Test handling of invalid JSON response."""
        with patch.object(deepseek_with_session, '_load_prompt') as mock_load_prompt:
            mock_load_prompt.side_effect = [
                "System prompt",
                "User prompt: {filing}"
            ]
            
            # Mock response that returns invalid JSON string
            deepseek_with_session._chat_completion = AsyncMock(return_value="invalid json response")
            
            data = {}
            pdf_text = "Sample PDF text"
            
            await deepseek_with_session.extract_case_information(data, pdf_text)
            
            # Data should remain unchanged when JSON parsing fails
            assert len(data) == 0

    @pytest.mark.asyncio
    async def test_extract_case_information_no_response(self, deepseek_with_session):
        """Test handling when no response is received."""
        with patch.object(deepseek_with_session, '_load_prompt') as mock_load_prompt:
            mock_load_prompt.side_effect = [
                "System prompt",
                "User prompt: {filing}"
            ]
            
            # Mock no response
            deepseek_with_session._chat_completion = AsyncMock(return_value=None)
            
            data = {}
            pdf_text = "Sample PDF text"
            
            await deepseek_with_session.extract_case_information(data, pdf_text)
            
            # Data should remain unchanged
            assert len(data) == 0

    @pytest.mark.asyncio
    async def test_extract_case_information_text_truncation(self, deepseek_with_session):
        """Test that long PDF text is properly truncated."""
        with patch.object(deepseek_with_session, '_load_prompt') as mock_load_prompt:
            mock_load_prompt.side_effect = [
                "System prompt",
                "User prompt: {filing}"
            ]
            
            deepseek_with_session._chat_completion = AsyncMock(return_value={
                "mdl_num": "NA",
                "mdl_cat": "NA", 
                "title": "Test Case",
                "allegations": "Test allegations"
            })
            
            data = {}
            # Create text longer than 10000 characters
            long_text = "A" * 15000
            
            await deepseek_with_session.extract_case_information(data, long_text)
            
            # Verify chat completion was called
            call_args = deepseek_with_session._chat_completion.call_args
            messages = call_args.kwargs['messages']
            user_message = messages[1]['content']
            
            # Should contain truncated text (first 10000 chars)
            assert len(user_message) < len(long_text)
            assert "A" * 10000 in user_message