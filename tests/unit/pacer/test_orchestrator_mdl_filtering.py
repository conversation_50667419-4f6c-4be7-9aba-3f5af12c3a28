"""
Tests for MDL flags filtering functionality in PacerOrchestrator.

This module tests the new mdl_flags configuration that allows filtering out
cases with specific MDL flags during saved report processing.
"""

import pytest
import asyncio
import tempfile
import json
from pathlib import Path
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import date

import sys
sys.path.insert(0, str(Path(__file__).parent.parent.parent.parent / "src"))

from pacer.orchestrator import PacerOrchestrator


@pytest.fixture
def temp_dir():
    """Create a temporary directory for test files."""
    with tempfile.TemporaryDirectory() as tmpdir:
        yield tmpdir


@pytest.fixture
def mock_config_with_mdl_flags():
    """Mock configuration with MDL flags enabled for filtering."""
    return {
        'mdl_flags': ['MDL2738', 'MDL3010'],
        'use_docket_report_log': True,
        'data_path': None,  # Will be set in test
        'bucket_name': 'test-bucket',
        'username_prod': 'test_user',
        'password_prod': 'test_pass',
        'openai_api_key': 'test_key'
    }


@pytest.fixture
def mock_config_no_mdl_flags():
    """Mock configuration with MDL flags disabled."""
    return {
        'mdl_flags': False,
        'use_docket_report_log': True,
        'data_path': None,  # Will be set in test
        'bucket_name': 'test-bucket',
        'username_prod': 'test_user',
        'password_prod': 'test_pass',
        'openai_api_key': 'test_key'
    }


@pytest.fixture
def sample_saved_report_with_mdl_cases():
    """Sample saved report JSON with MDL cases."""
    return {
        "cases": [
            {
                "court_id": "njd",
                "docket_num": "3:25-cv-10310-MAS-RLS",
                "versus": "KENDRICK-LARAMIE v. JOHNSON & JOHNSON et al",
                "filing_date": "2025-06-12",
                "cause": "28:1332 Diversity-Product Liability",
                "nos": "365 Personal Inj. Prod. Liability",
                "case_flags": "MDL2738"
            },
            {
                "court_id": "njd",
                "docket_num": "3:25-cv-10311-MAS-RLS",
                "versus": "SMITH v. ACME CORP et al",
                "filing_date": "2025-06-12",
                "cause": "28:1332 Diversity-Product Liability",
                "nos": "365 Personal Inj. Prod. Liability",
                "case_flags": "MDL3010"
            },
            {
                "court_id": "njd",
                "docket_num": "3:25-cv-10312-MAS-RLS",
                "versus": "JONES v. NORMAL CORP et al",
                "filing_date": "2025-06-12",
                "cause": "28:1332 Diversity-Product Liability",
                "nos": "365 Personal Inj. Prod. Liability",
                "case_flags": "NORMAL_CASE"
            },
            {
                "court_id": "njd",
                "docket_num": "3:25-cv-10313-MAS-RLS",
                "versus": "BROWN v. ANOTHER CORP et al",
                "filing_date": "2025-06-12",
                "cause": "28:1332 Diversity-Product Liability",
                "nos": "365 Personal Inj. Prod. Liability",
                "case_flags": ""
            }
        ]
    }


@pytest.fixture
def orchestrator_with_mdl_flags(temp_dir, mock_config_with_mdl_flags):
    """Create a PacerOrchestrator instance with MDL flags enabled."""
    mock_config_with_mdl_flags['data_path'] = temp_dir
    
    orchestrator = PacerOrchestrator(config=mock_config_with_mdl_flags)
    orchestrator.historically_review_dockets = set()
    
    # Mock required components
    orchestrator._get_pacer_repo = AsyncMock()
    orchestrator.file_manager = MagicMock()
    orchestrator.file_manager.check_if_downloaded_last_7_days_by_pattern = AsyncMock(return_value=False)
    orchestrator.file_manager.load_json_if_exists = AsyncMock(return_value=None)
    orchestrator._navigate_to_query_page = AsyncMock(return_value=True)
    orchestrator._query_docket_and_get_page = AsyncMock(return_value=MagicMock())
    orchestrator._process_single_row_docket = AsyncMock(return_value=({}, True))
    
    yield orchestrator


@pytest.fixture
def orchestrator_no_mdl_flags(temp_dir, mock_config_no_mdl_flags):
    """Create a PacerOrchestrator instance with MDL flags disabled."""
    mock_config_no_mdl_flags['data_path'] = temp_dir
    
    orchestrator = PacerOrchestrator(config=mock_config_no_mdl_flags)
    orchestrator.historically_review_dockets = set()
    
    # Mock required components
    orchestrator._get_pacer_repo = AsyncMock()
    orchestrator.file_manager = MagicMock()
    orchestrator.file_manager.check_if_downloaded_last_7_days_by_pattern = AsyncMock(return_value=False)
    orchestrator.file_manager.load_json_if_exists = AsyncMock(return_value=None)
    orchestrator._navigate_to_query_page = AsyncMock(return_value=True)
    orchestrator._query_docket_and_get_page = AsyncMock(return_value=MagicMock())
    orchestrator._process_single_row_docket = AsyncMock(return_value=({}, True))
    
    yield orchestrator


class TestMDLFlagsFiltering:
    """Test MDL flags filtering in saved report processing."""

    @pytest.mark.asyncio
    async def test_mdl_flags_filter_matching_cases(self, orchestrator_with_mdl_flags, temp_dir, sample_saved_report_with_mdl_cases):
        """Test that cases with matching MDL flags are filtered out."""
        # Setup saved report file
        report_file_path = Path(temp_dir) / "20250612" / "logs" / "docket_report_list_njd.json"
        report_file_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(report_file_path, 'w') as f:
            json.dump(sample_saved_report_with_mdl_cases, f)
        
        # Mock navigator and context
        mock_navigator = MagicMock()
        mock_context = MagicMock()
        mock_logger = MagicMock()
        
        # Mock _check_docket_existence_and_relevance to not skip cases
        from pacer.orchestrator import ExistenceCheckResult
        orchestrator_with_mdl_flags._check_docket_existence_and_relevance = AsyncMock(
            return_value=ExistenceCheckResult(should_skip=False, reason=None, base_filename="test")
        )
        
        # Call the method
        has_cases, row_counts = await orchestrator_with_mdl_flags._load_and_process_saved_report(
            court_id='njd',
            iso_date='20250612',
            navigator=mock_navigator,
            context=mock_context,
            processor_config={},
            start_date_obj=date(2025, 6, 12),
            end_date_obj=date(2025, 6, 12),
            court_logger=mock_logger
        )
        
        # Verify that only non-MDL cases were processed
        # Cases with MDL2738 and MDL3010 should be skipped (2 cases)
        # Cases with NORMAL_CASE and empty flags should be processed (2 cases)
        assert row_counts["skipped_other_relevance"] == 2  # MDL cases skipped
        assert row_counts["attempted"] == 2  # Non-MDL cases attempted
        
        # Verify logging of skipped cases
        skip_log_calls = [call for call in mock_logger.info.call_args_list if "SKIPPING: Case flag" in str(call)]
        assert len(skip_log_calls) == 2

    @pytest.mark.asyncio
    async def test_mdl_flags_disabled_processes_all_cases(self, orchestrator_no_mdl_flags, temp_dir, sample_saved_report_with_mdl_cases):
        """Test that when MDL flags are disabled, all cases are processed."""
        # Setup saved report file
        report_file_path = Path(temp_dir) / "20250612" / "logs" / "docket_report_list_njd.json"
        report_file_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(report_file_path, 'w') as f:
            json.dump(sample_saved_report_with_mdl_cases, f)
        
        # Mock navigator and context
        mock_navigator = MagicMock()
        mock_context = MagicMock()
        mock_logger = MagicMock()
        
        # Mock _check_docket_existence_and_relevance to not skip cases
        from pacer.orchestrator import ExistenceCheckResult
        orchestrator_no_mdl_flags._check_docket_existence_and_relevance = AsyncMock(
            return_value=ExistenceCheckResult(should_skip=False, reason=None, base_filename="test")
        )
        
        # Call the method
        has_cases, row_counts = await orchestrator_no_mdl_flags._load_and_process_saved_report(
            court_id='njd',
            iso_date='20250612',
            navigator=mock_navigator,
            context=mock_context,
            processor_config={},
            start_date_obj=date(2025, 6, 12),
            end_date_obj=date(2025, 6, 12),
            court_logger=mock_logger
        )
        
        # Verify that all cases were attempted to be processed
        assert row_counts["skipped_other_relevance"] == 0  # No MDL filtering
        assert row_counts["attempted"] == 4  # All 4 cases attempted
        
        # Verify no MDL skipping logs
        skip_log_calls = [call for call in mock_logger.info.call_args_list if "SKIPPING: Case flag" in str(call)]
        assert len(skip_log_calls) == 0

    @pytest.mark.asyncio
    async def test_mdl_flags_multiple_flags_per_case(self, orchestrator_with_mdl_flags, temp_dir):
        """Test MDL filtering with multiple flags per case."""
        # Create report with case having multiple flags, one matching
        report_data = {
            "cases": [
                {
                    "court_id": "njd",
                    "docket_num": "3:25-cv-10310-MAS-RLS",
                    "versus": "TEST v. CORP",
                    "filing_date": "2025-06-12",
                    "cause": "Test Cause",
                    "nos": "Test NOS",
                    "case_flags": "OTHER_FLAG,MDL2738,ANOTHER_FLAG"
                }
            ]
        }
        
        # Setup saved report file
        report_file_path = Path(temp_dir) / "20250612" / "logs" / "docket_report_list_njd.json"
        report_file_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(report_file_path, 'w') as f:
            json.dump(report_data, f)
        
        # Mock components
        mock_navigator = MagicMock()
        mock_context = MagicMock()
        mock_logger = MagicMock()
        
        from pacer.orchestrator import ExistenceCheckResult
        orchestrator_with_mdl_flags._check_docket_existence_and_relevance = AsyncMock(
            return_value=ExistenceCheckResult(should_skip=False, reason=None, base_filename="test")
        )
        
        # Call the method
        has_cases, row_counts = await orchestrator_with_mdl_flags._load_and_process_saved_report(
            court_id='njd',
            iso_date='20250612',
            navigator=mock_navigator,
            context=mock_context,
            processor_config={},
            start_date_obj=date(2025, 6, 12),
            end_date_obj=date(2025, 6, 12),
            court_logger=mock_logger
        )
        
        # Verify the case was skipped due to MDL2738 match
        assert row_counts["skipped_other_relevance"] == 1
        assert row_counts["attempted"] == 0
        
        # Verify logging mentions the correct flag
        skip_log_calls = [call for call in mock_logger.info.call_args_list if "MDL2738" in str(call)]
        assert len(skip_log_calls) >= 1

    @pytest.mark.asyncio
    async def test_mdl_flags_empty_case_flags(self, orchestrator_with_mdl_flags, temp_dir):
        """Test MDL filtering with empty or missing case_flags."""
        # Create report with cases having empty or missing flags
        report_data = {
            "cases": [
                {
                    "court_id": "njd",
                    "docket_num": "3:25-cv-10310-MAS-RLS",
                    "versus": "TEST v. CORP",
                    "filing_date": "2025-06-12",
                    "cause": "Test Cause",
                    "nos": "Test NOS",
                    "case_flags": ""
                },
                {
                    "court_id": "njd",
                    "docket_num": "3:25-cv-10311-MAS-RLS",
                    "versus": "TEST2 v. CORP",
                    "filing_date": "2025-06-12",
                    "cause": "Test Cause",
                    "nos": "Test NOS"
                    # Missing case_flags field
                }
            ]
        }
        
        # Setup saved report file
        report_file_path = Path(temp_dir) / "20250612" / "logs" / "docket_report_list_njd.json"
        report_file_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(report_file_path, 'w') as f:
            json.dump(report_data, f)
        
        # Mock components
        mock_navigator = MagicMock()
        mock_context = MagicMock()
        mock_logger = MagicMock()
        
        from pacer.orchestrator import ExistenceCheckResult
        orchestrator_with_mdl_flags._check_docket_existence_and_relevance = AsyncMock(
            return_value=ExistenceCheckResult(should_skip=False, reason=None, base_filename="test")
        )
        
        # Call the method
        has_cases, row_counts = await orchestrator_with_mdl_flags._load_and_process_saved_report(
            court_id='njd',
            iso_date='20250612',
            navigator=mock_navigator,
            context=mock_context,
            processor_config={},
            start_date_obj=date(2025, 6, 12),
            end_date_obj=date(2025, 6, 12),
            court_logger=mock_logger
        )
        
        # Both cases should be processed (no MDL flags to match)
        assert row_counts["skipped_other_relevance"] == 0
        assert row_counts["attempted"] == 2

    def test_mdl_flags_config_validation(self, temp_dir):
        """Test that MDL flags configuration is properly validated."""
        # Test with various invalid configurations
        base_config = {
            'data_path': temp_dir,
            'bucket_name': 'test-bucket',
            'username_prod': 'test_user',
            'password_prod': 'test_pass',
            'openai_api_key': 'test_key'
        }
        
        invalid_configs = [
            {**base_config, 'mdl_flags': 'MDL2738'},  # String instead of list
            {**base_config, 'mdl_flags': 123},        # Number instead of list
            {**base_config, 'mdl_flags': None},       # None instead of list
        ]
        
        for config in invalid_configs:
            orchestrator = PacerOrchestrator(config=config)
            
            # MDL flags check should handle invalid config gracefully
            # The logic checks: if mdl_flags_config and isinstance(mdl_flags_config, list)
            # So invalid configs should be ignored (returns falsy value)
            mdl_flags_config = orchestrator.config.get('mdl_flags', False)
            should_check = mdl_flags_config and isinstance(mdl_flags_config, list)
            assert not should_check  # Should be falsy (False, None, etc.)


if __name__ == '__main__':
    pytest.main([__file__, '-v'])