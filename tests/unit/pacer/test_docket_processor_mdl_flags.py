"""
Tests for MDL flags functionality in DocketProcessor.

This module tests the new mdl_flags configuration that allows forcing
download/upload of HTML and saving JSON for specific MDL numbers.
"""

import pytest
import asyncio
import tempfile
from pathlib import Path
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import date

import sys
sys.path.insert(0, str(Path(__file__).parent.parent.parent.parent / "src"))

from pacer.docket_processor import DocketProcessor
from pacer.file_manager import PacerFileManager


@pytest.fixture
def temp_dir():
    """Create a temporary directory for test files."""
    with tempfile.TemporaryDirectory() as tmpdir:
        yield tmpdir


@pytest.fixture
def mock_config_with_mdl_flags():
    """Mock configuration with MDL flags enabled."""
    return {
        'mdl_flags': ['MDL2738', 'MDL3010'],
        'use_docket_report_log': True,
        'cdn_base_url': 'https://cdn.lexgenius.ai'
    }


@pytest.fixture
def mock_config_no_mdl_flags():
    """Mock configuration with MDL flags disabled."""
    return {
        'mdl_flags': False,
        'use_docket_report_log': True
    }


@pytest.fixture
def docket_processor_with_mdl_flags(temp_dir, mock_config_with_mdl_flags):
    """Create a DocketProcessor instance with MDL flags enabled."""
    from datetime import date
    file_manager = PacerFileManager(temp_dir)
    
    processor = DocketProcessor(
        court_id='njd',
        iso_date='20250612',
        start_date_obj=date(2025, 6, 11),
        end_date_obj=date(2025, 6, 12),
        navigator=None,
        file_manager=file_manager,
        pacer_repository=None,
        district_courts_repository=None,
        s3_storage=None,
        gpt_interface=None,
        config=mock_config_with_mdl_flags
    )
    processor.logger = MagicMock()
    yield processor


@pytest.fixture
def docket_processor_no_mdl_flags(temp_dir, mock_config_no_mdl_flags):
    """Create a DocketProcessor instance with MDL flags disabled."""
    from datetime import date
    file_manager = PacerFileManager(temp_dir)
    
    processor = DocketProcessor(
        court_id='njd',
        iso_date='20250612',
        start_date_obj=date(2025, 6, 11),
        end_date_obj=date(2025, 6, 12),
        navigator=None,
        file_manager=file_manager,
        pacer_repository=None,
        district_courts_repository=None,
        s3_storage=None,
        gpt_interface=None,
        config=mock_config_no_mdl_flags
    )
    processor.logger = MagicMock()
    yield processor


class TestMDLFlagsChecking:
    """Test the _check_mdl_flags method."""

    def test_check_mdl_flags_disabled(self, docket_processor_no_mdl_flags):
        """Test that MDL flags check returns None when disabled."""
        case_details = {'mdl_num': '2738', 'flags': ['MDL2738']}
        result = docket_processor_no_mdl_flags._check_mdl_flags(case_details)
        assert result is None

    def test_check_mdl_flags_empty_list(self, docket_processor_with_mdl_flags):
        """Test that MDL flags check returns None for empty config."""
        docket_processor_with_mdl_flags.config['mdl_flags'] = []
        case_details = {'mdl_num': '2738', 'flags': ['MDL2738']}
        result = docket_processor_with_mdl_flags._check_mdl_flags(case_details)
        assert result is None

    def test_check_mdl_flags_match_by_mdl_num(self, docket_processor_with_mdl_flags):
        """Test MDL flags match by mdl_num field."""
        case_details = {'mdl_num': '2738', 'flags': []}
        result = docket_processor_with_mdl_flags._check_mdl_flags(case_details)
        
        assert result is not None
        assert result['mdl_num'] == '2738'
        assert result['force_processing'] is True

    def test_check_mdl_flags_match_by_flags(self, docket_processor_with_mdl_flags):
        """Test MDL flags match by flags field."""
        case_details = {'flags': ['MDL3010', 'OTHER_FLAG']}
        result = docket_processor_with_mdl_flags._check_mdl_flags(case_details)
        
        assert result is not None
        assert result['mdl_flag'] == 'MDL3010'
        assert result['force_processing'] is True

    def test_check_mdl_flags_no_match(self, docket_processor_with_mdl_flags):
        """Test MDL flags with no matching MDL numbers."""
        case_details = {'mdl_num': '9999', 'flags': ['MDL9999', 'OTHER_FLAG']}
        result = docket_processor_with_mdl_flags._check_mdl_flags(case_details)
        assert result is None

    def test_check_mdl_flags_missing_fields(self, docket_processor_with_mdl_flags):
        """Test MDL flags with missing mdl_num and flags fields."""
        case_details = {'court_id': 'njd'}
        result = docket_processor_with_mdl_flags._check_mdl_flags(case_details)
        assert result is None


class TestMDLFlagsProcessingLogic:
    """Test the integration of MDL flags in the processing workflow."""

    @pytest.mark.asyncio
    async def test_mdl_flags_force_html_only(self, docket_processor_with_mdl_flags):
        """Test that MDL flags force HTML-only processing."""
        # Mock verify_case to return True
        docket_processor_with_mdl_flags.verify_case = AsyncMock(return_value=True)
        
        # Mock file manager
        docket_processor_with_mdl_flags.file_manager.save_json = AsyncMock()
        
        case_details = {
            'mdl_num': '2738',  # This should match MDL flags
            'court_id': 'njd',
            'base_filename': 'test_case'
        }
        
        # Mock the _check_mdl_flags method to return a match
        mdl_match = {'mdl_num': '2738', 'force_processing': True}
        docket_processor_with_mdl_flags._check_mdl_flags = MagicMock(return_value=mdl_match)
        
        # Test that MDL flags are detected
        assert docket_processor_with_mdl_flags._check_mdl_flags(case_details) == mdl_match

    @pytest.mark.asyncio
    async def test_mdl_flags_html_only_mode(self, docket_processor_with_mdl_flags):
        """Test that MDL flags force HTML-only mode regardless of downloader availability."""
        # Even with document downloader available
        docket_processor_with_mdl_flags.html_only = False
        docket_processor_with_mdl_flags.document_downloader = MagicMock()
        
        case_details = {'mdl_num': '2738'}
        mdl_match = {'mdl_num': '2738', 'force_processing': True}
        
        # MDL flags should force HTML-only processing
        # The new logic forces HTML-only when MDL flags match
        if mdl_match:
            case_details['html_only'] = True
        
        assert case_details.get('html_only') is True

    def test_mdl_flags_processing_notes(self, docket_processor_with_mdl_flags):
        """Test that MDL flags add appropriate processing notes."""
        case_details = {'mdl_num': '2738'}
        mdl_match = {'mdl_num': '2738', 'force_processing': True}
        
        # Simulate adding processing notes for HTML-only processing
        case_details['_processing_notes'] = (
            case_details.get('_processing_notes', '') + 
            " Matched mdl_flags config - HTML only."
        ).strip()
        
        assert "Matched mdl_flags config - HTML only" in case_details['_processing_notes']


class TestMDLFlagsConfiguration:
    """Test various MDL flags configuration scenarios."""

    def test_mdl_flags_string_config(self, temp_dir):
        """Test that string config for mdl_flags is handled properly."""
        from datetime import date
        config = {'mdl_flags': 'MDL2738'}  # Invalid: should be list or False
        
        file_manager = PacerFileManager(temp_dir)
        processor = DocketProcessor(
            court_id='njd',
            iso_date='20250612',
            start_date_obj=date(2025, 6, 11),
            end_date_obj=date(2025, 6, 12),
            navigator=None,
            file_manager=file_manager,
            pacer_repository=None,
            district_courts_repository=None,
            s3_storage=None,
            gpt_interface=None,
            config=config
        )
        processor.logger = MagicMock()
        
        case_details = {'mdl_num': '2738'}
        result = processor._check_mdl_flags(case_details)
        assert result is None  # Should return None for non-list config

    def test_mdl_flags_multiple_entries(self, docket_processor_with_mdl_flags):
        """Test MDL flags with multiple configured entries."""
        case_details_1 = {'mdl_num': '2738'}
        case_details_2 = {'mdl_num': '3010'}
        case_details_3 = {'mdl_num': '9999'}
        
        result_1 = docket_processor_with_mdl_flags._check_mdl_flags(case_details_1)
        result_2 = docket_processor_with_mdl_flags._check_mdl_flags(case_details_2)
        result_3 = docket_processor_with_mdl_flags._check_mdl_flags(case_details_3)
        
        assert result_1 is not None  # MDL2738 should match
        assert result_2 is not None  # MDL3010 should match
        assert result_3 is None      # MDL9999 should not match


class TestMDLFlagsIntegration:
    """Integration tests for MDL flags functionality."""

    @pytest.mark.asyncio
    async def test_mdl_flags_file_save_integration(self, docket_processor_with_mdl_flags, temp_dir):
        """Test that MDL flags properly trigger file saves."""
        # Setup test directory structure
        test_date_dir = Path(temp_dir) / '20250612' / 'dockets'
        test_date_dir.mkdir(parents=True, exist_ok=True)
        
        case_details = {
            'mdl_num': '2738',
            'court_id': 'njd',
            'base_filename': 'njd_25_00001_test_case'
        }
        
        # Mock file manager save operation
        docket_processor_with_mdl_flags.file_manager.save_json = AsyncMock()
        
        # Test that MDL flags would trigger save
        mdl_match = docket_processor_with_mdl_flags._check_mdl_flags(case_details)
        assert mdl_match is not None
        
        # Verify the match triggers the expected behavior
        assert mdl_match['force_processing'] is True

    def test_mdl_flags_logging(self, docket_processor_with_mdl_flags):
        """Test that MDL flags generate appropriate log messages."""
        case_details = {'mdl_num': '2738'}
        
        # Call the method to trigger logging
        result = docket_processor_with_mdl_flags._check_mdl_flags(case_details)
        
        # Verify debug logging would be called
        assert result is not None
        # Note: In actual processing, this would call:
        # self.logger.debug(f"Case matches MDL flags: MDL2738")


class TestHtmlOnlyModeIntegration:
    """Test global html_only mode functionality."""

    @pytest.fixture
    def docket_processor_html_only(self, temp_dir):
        """Create a DocketProcessor instance with global html_only mode."""
        from datetime import date
        config = {'html_only': True, 'use_docket_report_log': True}
        file_manager = PacerFileManager(temp_dir)
        
        processor = DocketProcessor(
            court_id='njd',
            iso_date='20250612',
            start_date_obj=date(2025, 6, 11),
            end_date_obj=date(2025, 6, 12),
            navigator=None,
            file_manager=file_manager,
            pacer_repository=None,
            district_courts_repository=None,
            s3_storage=None,
            gpt_interface=None,
            config=config
        )
        processor.logger = MagicMock()
        processor.html_only = True  # Explicitly set html_only mode
        yield processor

    @pytest.mark.asyncio
    async def test_html_only_prevents_pdf_download(self, docket_processor_html_only):
        """Test that global html_only mode prevents PDF downloads."""
        case_details = {
            'court_id': 'njd',
            'base_filename': 'test_case',
            's3_html': '/20250612/html/test.html'
        }
        
        # Mock required methods
        docket_processor_html_only.verify_case = AsyncMock(return_value=True)
        docket_processor_html_only.file_manager.save_json = AsyncMock()
        
        # Simulate the html_only check logic
        if docket_processor_html_only.html_only:
            case_details['html_only'] = True
            case_details['_processing_notes'] = "Global html_only mode - HTML only."
            # Should return early without PDF download
            should_skip_pdf = True
        else:
            should_skip_pdf = False
        
        assert should_skip_pdf is True
        assert case_details.get('html_only') is True
        assert "Global html_only mode" in case_details.get('_processing_notes', '')

    def test_html_only_overrides_other_settings(self, docket_processor_html_only):
        """Test that html_only mode overrides other settings like MDL flags."""
        # Even with MDL flags that would normally force HTML-only,
        # global html_only should take precedence and be checked first
        case_details = {'mdl_num': '2738'}  # This would match MDL flags
        
        # html_only should be checked before MDL flags
        assert docket_processor_html_only.html_only is True
        
        # The processor should skip PDF download due to html_only, not MDL flags
        # This ensures html_only is the primary mechanism


class TestIgnoreDownloadIntegration:
    """Test ignore_download configuration functionality."""

    @pytest.fixture
    def docket_processor_with_ignore_download(self, temp_dir):
        """Create a DocketProcessor with ignore_download configuration."""
        from datetime import date
        
        ignore_download_config = [
            {
                "court_id": "njd",
                "attorney_name": "JOHN DOE",
                "law_firm": "TEST LAW FIRM",
                "flags": ["MDL2738"],
                "report_law_firm": "Test Law Firm LLP"
            }
        ]
        
        config = {'mdl_flags': False, 'use_docket_report_log': False}
        file_manager = PacerFileManager(temp_dir)
        
        processor = DocketProcessor(
            court_id='njd',
            iso_date='20250612',
            start_date_obj=date(2025, 6, 11),
            end_date_obj=date(2025, 6, 12),
            navigator=None,
            file_manager=file_manager,
            pacer_repository=None,
            district_courts_repository=None,
            s3_storage=None,
            gpt_interface=None,
            config=config
        )
        processor.ignore_download_config = ignore_download_config
        processor.logger = MagicMock()
        yield processor

    def test_ignore_download_matching(self, docket_processor_with_ignore_download):
        """Test that ignore_download matches correct cases."""
        case_details = {
            'court_id': 'njd',
            'attorney': [{'attorney_name': 'JOHN DOE', 'law_firm': 'TEST LAW FIRM'}],
            'flags': ['MDL2738']
        }
        
        result = docket_processor_with_ignore_download._check_ignore_download(case_details)
        assert result is not None
        assert result['report_law_firm'] == 'Test Law Firm LLP'

    def test_ignore_download_no_match(self, docket_processor_with_ignore_download):
        """Test that ignore_download doesn't match incorrect cases."""
        case_details = {
            'court_id': 'njd',
            'attorney': [{'attorney_name': 'JANE SMITH', 'law_firm': 'OTHER LAW FIRM'}],
            'flags': ['MDL2738']
        }
        
        result = docket_processor_with_ignore_download._check_ignore_download(case_details)
        assert result is None


class TestThreeMechanismsPriority:
    """Test that the three mechanisms work in the correct priority order."""

    @pytest.fixture
    def docket_processor_all_mechanisms(self, temp_dir):
        """Create a processor with all three mechanisms configured."""
        from datetime import date
        
        ignore_download_config = [
            {
                "court_id": "njd",
                "attorney_name": "JOHN DOE",
                "law_firm": "TEST LAW FIRM",
                "flags": ["MDL2738"],
                "report_law_firm": "Test Law Firm LLP"
            }
        ]
        
        config = {
            'html_only': False,  # Initially disabled
            'mdl_flags': ['MDL2738'],
            'use_docket_report_log': False
        }
        file_manager = PacerFileManager(temp_dir)
        
        processor = DocketProcessor(
            court_id='njd',
            iso_date='20250612',
            start_date_obj=date(2025, 6, 11),
            end_date_obj=date(2025, 6, 12),
            navigator=None,
            file_manager=file_manager,
            pacer_repository=None,
            district_courts_repository=None,
            s3_storage=None,
            gpt_interface=None,
            config=config
        )
        processor.ignore_download_config = ignore_download_config
        processor.logger = MagicMock()
        yield processor

    def test_html_only_takes_priority(self, docket_processor_all_mechanisms):
        """Test that html_only takes priority over MDL flags and ignore_download."""
        # Enable html_only
        docket_processor_all_mechanisms.html_only = True
        
        case_details = {
            'court_id': 'njd',
            'mdl_num': '2738',  # Would match MDL flags
            'attorney': [{'attorney_name': 'JOHN DOE', 'law_firm': 'TEST LAW FIRM'}],  # Would match ignore_download
            'flags': ['MDL2738']
        }
        
        # html_only should be checked first and take priority
        assert docket_processor_all_mechanisms.html_only is True
        
        # Other mechanisms should not be needed if html_only is True
        # This test ensures the order of checks is correct

    def test_mdl_flags_before_ignore_download(self, docket_processor_all_mechanisms):
        """Test that MDL flags are checked before ignore_download."""
        case_details = {
            'court_id': 'njd',
            'mdl_num': '2738',  # Would match MDL flags
            'attorney': [{'attorney_name': 'JOHN DOE', 'law_firm': 'TEST LAW FIRM'}],  # Would also match ignore_download
            'flags': ['MDL2738']
        }
        
        # MDL flags should match first
        mdl_result = docket_processor_all_mechanisms._check_mdl_flags(case_details)
        ignore_result = docket_processor_all_mechanisms._check_ignore_download(case_details)
        
        assert mdl_result is not None  # MDL flags match
        assert ignore_result is not None  # ignore_download also matches
        
        # But MDL flags should be processed first in the actual flow


class TestIgnoreDownloadExactFlagMatching:
    """Test exact flag matching in ignore_download logic."""
    
    def test_ignore_download_exact_flag_match(self, temp_dir):
        """Test that ignore_download only matches exact flags, not substrings."""
        from datetime import date
        
        # Mock ignore_download config with MDL2738 flag
        ignore_download_config = [
            {
                "court_id": "njd",
                "attorney_name": "JOHN F. FOLEY", 
                "law_firm": "SIMMONS HANLY CONROY",
                "flags": ["MDL2738"],
                "report_law_firm": "Simmons Hanly Conroy LLP"
            }
        ]
        
        config = {'mdl_flags': False, 'use_docket_report_log': False}
        file_manager = PacerFileManager(temp_dir)
        
        processor = DocketProcessor(
            court_id='njd',
            iso_date='20250612', 
            start_date_obj=date(2025, 6, 11),
            end_date_obj=date(2025, 6, 12),
            navigator=None,
            file_manager=file_manager,
            pacer_repository=None,
            district_courts_repository=None,
            s3_storage=None,
            gpt_interface=None,
            config=config
        )
        processor.ignore_download_config = ignore_download_config
        
        # Exact match should work
        case_exact_match = {
            'court_id': 'njd',
            'attorney': [{'attorney_name': 'JOHN F. FOLEY', 'law_firm': 'SIMMONS HANLY CONROY'}],
            'flags': ['MDL2738']
        }
        result_exact = processor._check_ignore_download(case_exact_match)
        assert result_exact is not None
        
        # Different MDL should NOT match
        case_different_mdl = {
            'court_id': 'njd', 
            'attorney': [{'attorney_name': 'JOHN F. FOLEY', 'law_firm': 'SIMMONS HANLY CONROY'}],
            'flags': ['MDL3060']
        }
        result_different = processor._check_ignore_download(case_different_mdl)
        assert result_different is None
        
        # Multiple flags with one exact match should work
        case_multi_flags = {
            'court_id': 'njd',
            'attorney': [{'attorney_name': 'JOHN F. FOLEY', 'law_firm': 'SIMMONS HANLY CONROY'}],
            'flags': ['MDL3060', 'MDL2738', 'OTHER_FLAG']
        }
        result_multi = processor._check_ignore_download(case_multi_flags)
        assert result_multi is not None


if __name__ == '__main__':
    pytest.main([__file__, '-v'])