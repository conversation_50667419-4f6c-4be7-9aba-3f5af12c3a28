"""
Tests for document downloader file path fix.

This module tests the fix that ensures downloaded files use the browser's 
download directory instead of creating separate staging directories, which
was causing files to be downloaded but not moved to the proper final location.
"""

import pytest
import asyncio
import tempfile
import json
import os
from pathlib import Path
from unittest.mock import AsyncMock, MagicMock, patch, mock_open
from datetime import date, timedelta

import sys
sys.path.insert(0, str(Path(__file__).parent.parent.parent.parent / "src"))

from pacer.pacer_document_downloader import PacerDocumentDownloader
from pacer.navigator import PacerNavigator
from pacer.file_manager import PacerFileManager


@pytest.fixture
def mock_navigator():
    """Mock PacerNavigator."""
    navigator = MagicMock(spec=PacerNavigator)
    navigator.page = AsyncMock()
    return navigator


@pytest.fixture
def mock_file_manager():
    """Mock PacerFileManager."""
    file_manager = MagicMock(spec=PacerFileManager)
    file_manager.base_data_dir = Path("/test/data")
    file_manager.finalize_download = AsyncMock(return_value="/test/final/path.zip")
    return file_manager


@pytest.fixture
def mock_s3_manager():
    """Mock S3 manager."""
    return AsyncMock()


@pytest.fixture
def base_config():
    """Base configuration for tests."""
    return {
        'zip_pdf_downloads': True,
        'stability_config': {
            'file_stabilization_wait_ms': 1000,
            'file_stabilization_max_attempts': 3
        }
    }


@pytest.fixture
def case_details():
    """Sample case details for testing."""
    return {
        'base_filename': 'cand_25_04962_S_v_Uber_Technologies_Inc_et_al',
        'docket_num': '3:25-cv-04962',
        'is_removal': False,
        'original_filename': 'test_document'
    }


class TestDocumentDownloaderPathFix:
    """Test the file path fix for document downloader."""

    def test_init_with_context_download_path(self, mock_navigator, mock_file_manager, mock_s3_manager, base_config):
        """Test initialization with context_download_path in config."""
        config_with_path = base_config.copy()
        config_with_path['context_download_path'] = '/test/browser/download/path'
        
        downloader = PacerDocumentDownloader(
            navigator=mock_navigator,
            file_manager=mock_file_manager,
            s3_manager=mock_s3_manager,
            stability_config=config_with_path['stability_config'],
            config=config_with_path,
            iso_date='20250612',
            current_court_id='cand'
        )
        
        assert downloader.config['context_download_path'] == '/test/browser/download/path'
        assert downloader.court_id == 'cand'
        assert downloader.iso_date == '20250612'

    def test_init_without_context_download_path(self, mock_navigator, mock_file_manager, mock_s3_manager, base_config):
        """Test initialization without context_download_path in config."""
        downloader = PacerDocumentDownloader(
            navigator=mock_navigator,
            file_manager=mock_file_manager,
            s3_manager=mock_s3_manager,
            stability_config=base_config['stability_config'],
            config=base_config,
            iso_date='20250612',
            current_court_id='cand'
        )
        
        assert 'context_download_path' not in downloader.config
        assert downloader.court_id == 'cand'
        assert downloader.iso_date == '20250612'

    @pytest.mark.asyncio
    async def test_staging_directory_with_browser_path(self, mock_navigator, mock_file_manager, mock_s3_manager, base_config, case_details):
        """Test that staging directory uses browser download path when available."""
        browser_download_path = '/test/browser/download/path'
        config_with_path = base_config.copy()
        config_with_path['context_download_path'] = browser_download_path
        
        downloader = PacerDocumentDownloader(
            navigator=mock_navigator,
            file_manager=mock_file_manager,
            s3_manager=mock_s3_manager,
            stability_config=config_with_path['stability_config'],
            config=config_with_path,
            iso_date='20250612',
            current_court_id='cand'
        )
        
        # Mock the download process to avoid full execution
        with patch.object(downloader, '_wait_for_file_stabilization', return_value=True):
            with patch('asyncio.to_thread') as mock_thread:
                with patch('pathlib.Path.exists', return_value=True):
                    with patch('pathlib.Path.mkdir'):
                        # Mock the download event and file operations
                        mock_thread.side_effect = lambda func, *args, **kwargs: True
                        
                        # We'll test that the staging root is derived from browser path
                        # by checking the log messages (mocked)
                        with patch.object(downloader.logger, 'info') as mock_log:
                            try:
                                await downloader.trigger_and_finalize_download(case_details)
                            except Exception:
                                pass  # We expect this to fail in testing, we're just checking the path logic
                            
                            # Check that the browser download path was used as staging root
                            log_calls = [call.args[0] for call in mock_log.call_args_list]
                            browser_path_used = any(browser_download_path in log_msg for log_msg in log_calls)
                            assert browser_path_used, f"Browser download path not found in logs: {log_calls}"

    @pytest.mark.asyncio
    async def test_staging_directory_fallback_to_default(self, mock_navigator, mock_file_manager, mock_s3_manager, base_config, case_details):
        """Test that staging directory falls back to default when no browser path."""
        downloader = PacerDocumentDownloader(
            navigator=mock_navigator,
            file_manager=mock_file_manager,
            s3_manager=mock_s3_manager,
            stability_config=base_config['stability_config'],
            config=base_config,  # No context_download_path
            iso_date='20250612',
            current_court_id='cand'
        )
        
        # Mock the download process to avoid full execution
        with patch.object(downloader, '_wait_for_file_stabilization', return_value=True):
            with patch('asyncio.to_thread') as mock_thread:
                with patch('pathlib.Path.exists', return_value=True):
                    with patch('pathlib.Path.mkdir'):
                        mock_thread.side_effect = lambda func, *args, **kwargs: True
                        
                        with patch.object(downloader.logger, 'info') as mock_log:
                            try:
                                await downloader.trigger_and_finalize_download(case_details)
                            except Exception:
                                pass  # We expect this to fail in testing
                            
                            # Check that the default staging area was used
                            log_calls = [call.args[0] for call in mock_log.call_args_list]
                            default_path_used = any('download_staging_area' in log_msg for log_msg in log_calls)
                            # Should either use default path OR we won't see the browser path log
                            browser_path_not_used = not any('Using browser download path' in log_msg for log_msg in log_calls)
                            assert browser_path_not_used, "Should not use browser download path when not configured"

    @pytest.mark.asyncio
    async def test_staging_directory_creates_case_specific_subdir(self, mock_navigator, mock_file_manager, mock_s3_manager, base_config, case_details):
        """Test that case-specific subdirectory is created within browser download path."""
        browser_download_path = '/test/browser/download/path'
        config_with_path = base_config.copy()
        config_with_path['context_download_path'] = browser_download_path
        
        downloader = PacerDocumentDownloader(
            navigator=mock_navigator,
            file_manager=mock_file_manager,
            s3_manager=mock_s3_manager,
            stability_config=config_with_path['stability_config'],
            config=config_with_path,
            iso_date='20250612',
            current_court_id='cand'
        )
        
        # Mock the page close check and download actions
        downloader.navigator.page.is_closed.return_value = False
        
        captured_debug_msgs = []
        
        def capture_debug_msg(msg):
            captured_debug_msgs.append(msg)
        
        with patch.object(downloader, '_wait_for_file_stabilization', return_value=True):
            with patch('asyncio.to_thread') as mock_thread:
                with patch('pathlib.Path.exists', return_value=True):
                    with patch.object(downloader.logger, 'debug', side_effect=capture_debug_msg):
                        mock_thread.side_effect = lambda func, *args, **kwargs: True
                        
                        try:
                            await downloader.trigger_and_finalize_download(case_details)
                        except Exception:
                            pass  # Expected to fail in testing
                        
                        # Check debug messages for the staging directory creation
                        staging_dir_msgs = [msg for msg in captured_debug_msgs if 'Created temp download staging dir:' in msg]
                        if staging_dir_msgs:
                            staging_msg = staging_dir_msgs[0]
                            assert browser_download_path in staging_msg
                            assert 'dl_stage_cand_3_25-cv-04962' in staging_msg

    def test_config_access(self, mock_navigator, mock_file_manager, mock_s3_manager, base_config):
        """Test that config values are accessible within the downloader."""
        test_config = base_config.copy()
        test_config['context_download_path'] = '/custom/path'
        test_config['custom_setting'] = 'test_value'
        
        downloader = PacerDocumentDownloader(
            navigator=mock_navigator,
            file_manager=mock_file_manager,
            s3_manager=mock_s3_manager,
            stability_config=test_config['stability_config'],
            config=test_config,
            iso_date='20250612',
            current_court_id='cand'
        )
        
        assert downloader.config.get('context_download_path') == '/custom/path'
        assert downloader.config.get('custom_setting') == 'test_value'
        assert downloader.config.get('nonexistent_setting') is None

    @pytest.mark.asyncio
    async def test_path_validation_and_creation(self, mock_navigator, mock_file_manager, mock_s3_manager, base_config, case_details):
        """Test that paths are properly validated and directories are created."""
        browser_download_path = '/test/browser/download/path'
        config_with_path = base_config.copy()
        config_with_path['context_download_path'] = browser_download_path
        
        downloader = PacerDocumentDownloader(
            navigator=mock_navigator,
            file_manager=mock_file_manager,
            s3_manager=mock_s3_manager,
            stability_config=config_with_path['stability_config'],
            config=config_with_path,
            iso_date='20250612',
            current_court_id='cand'
        )
        
        mkdir_called = False
        
        def mock_mkdir(*args, **kwargs):
            nonlocal mkdir_called
            mkdir_called = True
            return True
        
        with patch.object(downloader, '_wait_for_file_stabilization', return_value=True):
            with patch('asyncio.to_thread', side_effect=mock_mkdir):
                with patch('pathlib.Path.exists', return_value=True):
                    with patch.object(downloader.logger, 'debug'):
                        try:
                            await downloader.trigger_and_finalize_download(case_details)
                        except Exception:
                            pass
                        
                        assert mkdir_called, "Directory creation should have been attempted"

    @pytest.mark.asyncio
    async def test_error_handling_with_invalid_browser_path(self, mock_navigator, mock_file_manager, mock_s3_manager, base_config, case_details):
        """Test error handling when browser download path is invalid."""
        config_with_invalid_path = base_config.copy()
        config_with_invalid_path['context_download_path'] = '/invalid/nonexistent/path'
        
        downloader = PacerDocumentDownloader(
            navigator=mock_navigator,
            file_manager=mock_file_manager,
            s3_manager=mock_s3_manager,
            stability_config=config_with_invalid_path['stability_config'],
            config=config_with_invalid_path,
            iso_date='20250612',
            current_court_id='cand'
        )
        
        def mock_mkdir_fail(*args, **kwargs):
            raise OSError("Permission denied")
        
        with patch('asyncio.to_thread', side_effect=mock_mkdir_fail):
            with patch.object(downloader.logger, 'error') as mock_error_log:
                result = await downloader.trigger_and_finalize_download(case_details)
                
                assert result is False  # Should return False on failure
                mock_error_log.assert_called()  # Should log the error


class TestDownloadPathIntegration:
    """Integration tests for the download path fix."""

    @pytest.mark.asyncio
    async def test_orchestrator_to_downloader_path_flow(self, mock_navigator, mock_file_manager, mock_s3_manager, base_config):
        """Test the complete flow from orchestrator setting path to downloader using it."""
        # Simulate what orchestrator does
        orchestrator_download_path = '/data/20250612/dockets/temp/cand_ctx_dl_report/attempt_1_abc12345'
        
        # Create config as orchestrator would
        processor_config = base_config.copy()
        processor_config['context_download_path'] = orchestrator_download_path
        
        # Create downloader as processor would
        downloader = PacerDocumentDownloader(
            navigator=mock_navigator,
            file_manager=mock_file_manager,
            s3_manager=mock_s3_manager,
            stability_config=processor_config['stability_config'],
            config=processor_config,
            iso_date='20250612',
            current_court_id='cand'
        )
        
        # Verify the path is available in downloader
        assert downloader.config.get('context_download_path') == orchestrator_download_path
        
        # Test that the path would be used in staging directory creation
        case_details = {
            'base_filename': 'cand_25_04962_S_v_Uber_Technologies_Inc_et_al',
            'docket_num': '3:25-cv-04962',
            'is_removal': False
        }
        
        expected_staging_pattern = f"{orchestrator_download_path}/dl_stage_cand_3_25-cv-04962"
        
        with patch.object(downloader, '_wait_for_file_stabilization', return_value=True):
            with patch('asyncio.to_thread') as mock_thread:
                with patch('pathlib.Path.exists', return_value=True):
                    with patch.object(downloader.logger, 'debug') as mock_debug_log:
                        mock_thread.side_effect = lambda func, *args, **kwargs: True
                        
                        try:
                            await downloader.trigger_and_finalize_download(case_details)
                        except Exception:
                            pass
                        
                        # Check that the staging directory was created in the right location
                        debug_calls = [call.args[0] for call in mock_debug_log.call_args_list if call.args]
                        staging_dir_logged = any('Created temp download staging dir:' in msg and orchestrator_download_path in msg 
                                                for msg in debug_calls)
                        assert staging_dir_logged, f"Staging directory not created in browser path. Debug logs: {debug_calls}"


if __name__ == '__main__':
    pytest.main([__file__, '-v'])