"""
Tests for browser service functionality.

This module tests the BrowserService class, particularly the new temporary
directory functionality that enables parallel browser contexts while
maintaining keychain suppression on macOS.
"""

import pytest
import asyncio
import tempfile
import json
import os
from pathlib import Path
from unittest.mock import AsyncMock, MagicMock, patch, mock_open
from datetime import date, timedelta

import sys
sys.path.insert(0, str(Path(__file__).parent.parent.parent.parent / "src"))

from pacer.browser_service import BrowserService


@pytest.fixture
def browser_service():
    """Create a BrowserService instance for testing."""
    return BrowserService(headless=True, timeout_ms=30000)


@pytest.fixture
def mock_playwright():
    """Mock Playwright instance."""
    mock_playwright = AsyncMock()
    mock_chromium = AsyncMock()
    mock_context = AsyncMock()
    
    mock_playwright.chromium = mock_chromium
    mock_chromium.launch_persistent_context = AsyncMock(return_value=mock_context)
    mock_context.set_default_timeout = MagicMock()
    
    return mock_playwright


class TestBrowserServiceInitialization:
    """Test browser service initialization and configuration."""

    def test_init_default_params(self):
        """Test initialization with default parameters."""
        service = BrowserService()
        
        assert service.headless is True
        assert service.timeout_ms == 30000
        assert service.playwright is None
        assert service._managed_temp_dirs == []

    def test_init_custom_params(self):
        """Test initialization with custom parameters."""
        service = BrowserService(headless=False, timeout_ms=60000)
        
        assert service.headless is False
        assert service.timeout_ms == 60000
        assert service.playwright is None
        assert service._managed_temp_dirs == []

    def test_logger_configuration(self, browser_service):
        """Test that logger is properly configured."""
        logger_name = browser_service.logger.name
        assert "BrowserService" in logger_name


class TestPlaywrightConnection:
    """Test Playwright connection management."""

    @pytest.mark.asyncio
    async def test_connect_success(self, browser_service):
        """Test successful Playwright connection."""
        mock_playwright = AsyncMock()
        
        with patch('pacer.browser_service.async_playwright') as mock_async_playwright:
            mock_async_playwright.return_value.start = AsyncMock(return_value=mock_playwright)
            
            await browser_service.connect()
            
            assert browser_service.playwright == mock_playwright
            mock_async_playwright.return_value.start.assert_called_once()

    @pytest.mark.asyncio
    async def test_connect_already_active(self, browser_service):
        """Test connection when Playwright is already active."""
        # Set playwright to simulate already connected state
        browser_service.playwright = AsyncMock()
        
        with patch('pacer.browser_service.async_playwright') as mock_async_playwright:
            await browser_service.connect()
            
            # Should not call start again
            mock_async_playwright.assert_not_called()

    @pytest.mark.asyncio
    async def test_connect_failure(self, browser_service):
        """Test handling of connection failures."""
        with patch('pacer.browser_service.async_playwright') as mock_async_playwright:
            mock_async_playwright.return_value.start = AsyncMock(side_effect=Exception("Connection failed"))
            
            with pytest.raises(Exception, match="Connection failed"):
                await browser_service.connect()

    @pytest.mark.asyncio
    async def test_close_with_playwright(self, browser_service):
        """Test closing service with active Playwright instance."""
        mock_playwright = AsyncMock()
        browser_service.playwright = mock_playwright
        
        await browser_service.close()
        
        mock_playwright.stop.assert_called_once()
        assert browser_service.playwright is None

    @pytest.mark.asyncio
    async def test_close_without_playwright(self, browser_service):
        """Test closing service without active Playwright instance."""
        # Should not raise any errors
        await browser_service.close()
        assert browser_service.playwright is None


class TestTemporaryDirectoryManagement:
    """Test temporary directory creation and management for parallel contexts."""

    @pytest.mark.asyncio
    async def test_new_context_creates_temp_directory(self, browser_service):
        """Test that new_context creates unique temporary directories."""
        mock_playwright = AsyncMock()
        mock_context = AsyncMock()
        browser_service.playwright = mock_playwright
        
        mock_playwright.chromium.launch_persistent_context = AsyncMock(return_value=mock_context)
        
        with patch('tempfile.TemporaryDirectory') as mock_temp_dir:
            mock_temp_obj = MagicMock()
            mock_temp_obj.name = '/tmp/test_temp_dir'
            mock_temp_dir.return_value = mock_temp_obj
            
            with patch('builtins.open', mock_open()):
                with patch('json.dump'):
                    with patch('os.makedirs'):
                        context = await browser_service.new_context()
            
            # Verify temporary directory was created and managed
            mock_temp_dir.assert_called_once()
            assert mock_temp_obj in browser_service._managed_temp_dirs
            assert context == mock_context

    @pytest.mark.asyncio
    async def test_multiple_contexts_use_different_directories(self, browser_service):
        """Test that multiple contexts get unique temporary directories."""
        mock_playwright = AsyncMock()
        mock_context1 = AsyncMock()
        mock_context2 = AsyncMock()
        browser_service.playwright = mock_playwright
        
        mock_playwright.chromium.launch_persistent_context = AsyncMock(
            side_effect=[mock_context1, mock_context2]
        )
        
        temp_dirs = []
        
        def mock_temp_dir():
            mock_obj = MagicMock()
            mock_obj.name = f'/tmp/test_temp_dir_{len(temp_dirs)}'
            temp_dirs.append(mock_obj)
            return mock_obj
        
        with patch('tempfile.TemporaryDirectory', side_effect=mock_temp_dir):
            with patch('builtins.open', mock_open()):
                with patch('json.dump'):
                    with patch('os.makedirs'):
                        context1 = await browser_service.new_context()
                        context2 = await browser_service.new_context()
        
        # Verify two different temporary directories were created
        assert len(browser_service._managed_temp_dirs) == 2
        assert browser_service._managed_temp_dirs[0] != browser_service._managed_temp_dirs[1]
        assert browser_service._managed_temp_dirs[0].name != browser_service._managed_temp_dirs[1].name

    @pytest.mark.asyncio
    async def test_cleanup_managed_temp_dirs(self, browser_service):
        """Test cleanup of managed temporary directories."""
        # Add mock temp directories
        mock_temp1 = MagicMock()
        mock_temp2 = MagicMock()
        browser_service._managed_temp_dirs = [mock_temp1, mock_temp2]
        
        await browser_service.close()
        
        # Verify all temp directories were cleaned up
        mock_temp1.cleanup.assert_called_once()
        mock_temp2.cleanup.assert_called_once()
        assert browser_service._managed_temp_dirs == []

    @pytest.mark.asyncio
    async def test_cleanup_handles_temp_dir_errors(self, browser_service):
        """Test that cleanup handles errors from temp directory cleanup."""
        mock_temp = MagicMock()
        mock_temp.cleanup.side_effect = Exception("Cleanup failed")
        browser_service._managed_temp_dirs = [mock_temp]
        
        # Should not raise exception despite cleanup error
        await browser_service.close()
        
        mock_temp.cleanup.assert_called_once()
        assert browser_service._managed_temp_dirs == []


class TestKeychainSuppression:
    """Test keychain suppression configuration."""

    @pytest.mark.asyncio
    async def test_environment_variables_set(self, browser_service):
        """Test that keychain suppression environment variables are set."""
        mock_playwright = AsyncMock()
        mock_context = AsyncMock()
        browser_service.playwright = mock_playwright
        
        mock_playwright.chromium.launch_persistent_context = AsyncMock(return_value=mock_context)
        
        with patch('tempfile.TemporaryDirectory') as mock_temp_dir:
            mock_temp_obj = MagicMock()
            mock_temp_obj.name = '/tmp/test_temp_dir'
            mock_temp_dir.return_value = mock_temp_obj
            
            with patch('builtins.open', mock_open()):
                with patch('json.dump'):
                    with patch('os.makedirs'):
                        await browser_service.new_context()
        
        # Verify key environment variables are set
        expected_env_vars = [
            'ELECTRON_DISABLE_SECURITY_WARNINGS',
            'DISABLE_KEYCHAIN_ACCESS',
            'CHROME_DISABLE_KEYCHAIN',
            'CHROMIUM_NO_KEYCHAIN'
        ]
        
        for env_var in expected_env_vars:
            assert env_var in os.environ

    @pytest.mark.asyncio
    async def test_chrome_flags_include_keychain_suppression(self, browser_service):
        """Test that Chrome launch arguments include keychain suppression flags."""
        mock_playwright = AsyncMock()
        mock_context = AsyncMock()
        browser_service.playwright = mock_playwright
        
        captured_args = None
        
        def capture_launch_args(**kwargs):
            nonlocal captured_args
            captured_args = kwargs.get('args', [])
            return mock_context
        
        mock_playwright.chromium.launch_persistent_context = AsyncMock(side_effect=capture_launch_args)
        
        with patch('tempfile.TemporaryDirectory') as mock_temp_dir:
            mock_temp_obj = MagicMock()
            mock_temp_obj.name = '/tmp/test_temp_dir'
            mock_temp_dir.return_value = mock_temp_obj
            
            with patch('builtins.open', mock_open()):
                with patch('json.dump'):
                    with patch('os.makedirs'):
                        await browser_service.new_context()
        
        # Verify key keychain suppression flags are present
        expected_flags = [
            '--use-mock-keychain',
            '--password-store=basic',
            '--disable-keychain-access',
            '--disable-mac-keychain',
            '--disable-safe-storage'
        ]
        
        for flag in expected_flags:
            assert flag in captured_args

    @pytest.mark.asyncio
    async def test_preferences_file_creation(self, browser_service):
        """Test that Chrome preferences file is created with proper settings."""
        mock_playwright = AsyncMock()
        mock_context = AsyncMock()
        browser_service.playwright = mock_playwright
        
        mock_playwright.chromium.launch_persistent_context = AsyncMock(return_value=mock_context)
        
        captured_prefs = None
        
        def capture_json_dump(data, file):
            nonlocal captured_prefs
            if 'plugins' in data:  # This is the preferences file
                captured_prefs = data
        
        with patch('tempfile.TemporaryDirectory') as mock_temp_dir:
            mock_temp_obj = MagicMock()
            mock_temp_obj.name = '/tmp/test_temp_dir'
            mock_temp_dir.return_value = mock_temp_obj
            
            with patch('builtins.open', mock_open()):
                with patch('json.dump', side_effect=capture_json_dump):
                    with patch('os.makedirs'):
                        await browser_service.new_context()
        
        # Verify preferences include credential suppression
        assert captured_prefs is not None
        assert captured_prefs['credentials_enable_service'] is False
        assert captured_prefs['password_manager_enabled'] is False
        assert captured_prefs['profile']['password_manager_enabled'] is False

    @pytest.mark.asyncio
    async def test_local_state_file_creation(self, browser_service):
        """Test that Local State file is created with security settings."""
        mock_playwright = AsyncMock()
        mock_context = AsyncMock()
        browser_service.playwright = mock_playwright
        
        mock_playwright.chromium.launch_persistent_context = AsyncMock(return_value=mock_context)
        
        captured_local_state = None
        
        def capture_json_dump(data, file):
            nonlocal captured_local_state
            if 'credential_provider' in data:  # This is the Local State file
                captured_local_state = data
        
        with patch('tempfile.TemporaryDirectory') as mock_temp_dir:
            mock_temp_obj = MagicMock()
            mock_temp_obj.name = '/tmp/test_temp_dir'
            mock_temp_dir.return_value = mock_temp_obj
            
            with patch('builtins.open', mock_open()):
                with patch('json.dump', side_effect=capture_json_dump):
                    with patch('os.makedirs'):
                        await browser_service.new_context()
        
        # Verify Local State includes security settings
        assert captured_local_state is not None
        assert captured_local_state['credential_provider']['enabled'] is False
        assert captured_local_state['signin']['allowed'] is False


class TestDownloadConfiguration:
    """Test download path configuration."""

    @pytest.mark.asyncio
    async def test_download_path_configuration(self, browser_service):
        """Test that download path is properly configured."""
        mock_playwright = AsyncMock()
        mock_context = AsyncMock()
        browser_service.playwright = mock_playwright
        
        download_path = "/test/download/path"
        captured_options = None
        
        def capture_launch_options(**kwargs):
            nonlocal captured_options
            captured_options = kwargs
            return mock_context
        
        mock_playwright.chromium.launch_persistent_context = AsyncMock(side_effect=capture_launch_options)
        
        with patch('tempfile.TemporaryDirectory') as mock_temp_dir:
            mock_temp_obj = MagicMock()
            mock_temp_obj.name = '/tmp/test_temp_dir'
            mock_temp_dir.return_value = mock_temp_obj
            
            with patch('builtins.open', mock_open()):
                with patch('json.dump'):
                    with patch('os.makedirs'):
                        with patch('pathlib.Path.mkdir'):
                            await browser_service.new_context(download_path=download_path)
        
        # Verify download path is set in context options
        assert captured_options is not None
        assert captured_options['downloads_path'] == download_path

    @pytest.mark.asyncio
    async def test_no_download_path_configuration(self, browser_service):
        """Test behavior when no download path is provided."""
        mock_playwright = AsyncMock()
        mock_context = AsyncMock()
        browser_service.playwright = mock_playwright
        
        captured_options = None
        
        def capture_launch_options(**kwargs):
            nonlocal captured_options
            captured_options = kwargs
            return mock_context
        
        mock_playwright.chromium.launch_persistent_context = AsyncMock(side_effect=capture_launch_options)
        
        with patch('tempfile.TemporaryDirectory') as mock_temp_dir:
            mock_temp_obj = MagicMock()
            mock_temp_obj.name = '/tmp/test_temp_dir'
            mock_temp_dir.return_value = mock_temp_obj
            
            with patch('builtins.open', mock_open()):
                with patch('json.dump'):
                    with patch('os.makedirs'):
                        await browser_service.new_context()
        
        # Verify no downloads_path is set when not provided
        assert captured_options is not None
        assert 'downloads_path' not in captured_options


class TestErrorHandling:
    """Test error handling in browser service."""

    @pytest.mark.asyncio
    async def test_new_context_without_playwright(self, browser_service):
        """Test new_context automatically connects if Playwright not initialized."""
        mock_playwright = AsyncMock()
        mock_context = AsyncMock()
        
        with patch('pacer.browser_service.async_playwright') as mock_async_playwright:
            mock_async_playwright.return_value.start = AsyncMock(return_value=mock_playwright)
            mock_playwright.chromium.launch_persistent_context = AsyncMock(return_value=mock_context)
            
            with patch('tempfile.TemporaryDirectory') as mock_temp_dir:
                mock_temp_obj = MagicMock()
                mock_temp_obj.name = '/tmp/test_temp_dir'
                mock_temp_dir.return_value = mock_temp_obj
                
                with patch('builtins.open', mock_open()):
                    with patch('json.dump'):
                        with patch('os.makedirs'):
                            context = await browser_service.new_context()
            
            # Verify connect was called automatically
            mock_async_playwright.return_value.start.assert_called_once()
            assert browser_service.playwright == mock_playwright
            assert context == mock_context

    @pytest.mark.asyncio
    async def test_context_launch_failure(self, browser_service):
        """Test handling of context launch failures."""
        mock_playwright = AsyncMock()
        browser_service.playwright = mock_playwright
        
        mock_playwright.chromium.launch_persistent_context = AsyncMock(
            side_effect=Exception("Launch failed")
        )
        
        with patch('tempfile.TemporaryDirectory') as mock_temp_dir:
            mock_temp_obj = MagicMock()
            mock_temp_obj.name = '/tmp/test_temp_dir'
            mock_temp_dir.return_value = mock_temp_obj
            
            with patch('builtins.open', mock_open()):
                with patch('json.dump'):
                    with patch('os.makedirs'):
                        with pytest.raises(ConnectionError, match="Generic error creating persistent browser context"):
                            await browser_service.new_context()
        
        # Verify temp directory was cleaned up on failure
        mock_temp_obj.cleanup.assert_called_once()

    @pytest.mark.asyncio
    async def test_preferences_setup_failure(self, browser_service):
        """Test handling of preferences setup failures."""
        mock_playwright = AsyncMock()
        browser_service.playwright = mock_playwright
        
        with patch('tempfile.TemporaryDirectory') as mock_temp_dir:
            mock_temp_obj = MagicMock()
            mock_temp_obj.name = '/tmp/test_temp_dir'
            mock_temp_dir.return_value = mock_temp_obj
            
            with patch('os.makedirs', side_effect=OSError("Permission denied")):
                with pytest.raises(IOError, match="Failed to set up user data directory preferences"):
                    await browser_service.new_context()


class TestAsyncContextManager:
    """Test async context manager functionality."""

    @pytest.mark.asyncio
    async def test_async_context_manager(self):
        """Test using BrowserService as async context manager."""
        mock_playwright = AsyncMock()
        
        with patch('pacer.browser_service.async_playwright') as mock_async_playwright:
            mock_async_playwright.return_value.start = AsyncMock(return_value=mock_playwright)
            
            async with BrowserService() as service:
                assert service.playwright == mock_playwright
            
            # Verify stop was called on exit
            mock_playwright.stop.assert_called_once()


if __name__ == '__main__':
    pytest.main([__file__, '-v'])