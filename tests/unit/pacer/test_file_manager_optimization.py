"""
Tests for file manager optimization functionality.

This module tests the new caching and optimization features added to
PacerFileManager for improved performance when checking existing files.
"""

import pytest
import asyncio
import tempfile
import json
from pathlib import Path
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import date, timedelta

import sys
sys.path.insert(0, str(Path(__file__).parent.parent.parent.parent / "src"))

from pacer.file_manager import PacerFileManager


@pytest.fixture
def temp_dir():
    """Create a temporary directory for test files."""
    with tempfile.TemporaryDirectory() as tmpdir:
        yield tmpdir


@pytest.fixture
def file_manager(temp_dir):
    """Create a PacerFileManager instance for testing."""
    return PacerFileManager(temp_dir)


@pytest.fixture
def sample_docket_files(temp_dir):
    """Create sample docket files for testing."""
    # Create directory structure
    date_dir = Path(temp_dir) / '20250612' / 'dockets'
    date_dir.mkdir(parents=True, exist_ok=True)
    
    # Create sample JSON files with various patterns
    files = [
        'njd_25_00001_SMITH_v_ACME.json',
        'njd_25_00002_JONES_v_CORP.json',
        'flnd_25_00770_CLARK_v_PFIZER.json',
        'cand_25_12345_DOE_v_TECH.json',
        'njd_24_99999_OLD_CASE.json'
    ]
    
    for filename in files:
        json_path = date_dir / filename
        json_path.write_text(json.dumps({
            'court_id': filename.split('_')[0],
            'case_name': filename.split('_', 3)[3].replace('.json', ''),
            'processing_notes': 'test case'
        }))
        
        # Create corresponding PDF for some files
        if 'SMITH' in filename or 'JONES' in filename:
            pdf_path = date_dir / filename.replace('.json', '.pdf')
            pdf_path.write_text('dummy pdf content')
    
    return files


@pytest.fixture
def sample_multi_date_files(temp_dir):
    """Create sample files across multiple dates for 7-day testing."""
    base_date = date(2025, 6, 12)
    
    for i in range(7):
        test_date = base_date - timedelta(days=i)
        date_dir = Path(temp_dir) / test_date.strftime('%Y%m%d') / 'dockets'
        date_dir.mkdir(parents=True, exist_ok=True)
        
        # Create a test file for each date
        filename = f'njd_25_{1000+i:05d}_TEST_CASE_DAY_{i}.json'
        json_path = date_dir / filename
        json_path.write_text(json.dumps({
            'court_id': 'njd',
            'case_name': f'TEST_CASE_DAY_{i}',
            'date': test_date.strftime('%Y-%m-%d')
        }))
        
        # Add PDF for some dates
        if i % 2 == 0:
            pdf_path = date_dir / filename.replace('.json', '.pdf')
            pdf_path.write_text('dummy pdf content')


class TestFilenameCaching:
    """Test the filename caching functionality."""

    @pytest.mark.asyncio
    async def test_get_cached_filenames_first_call(self, file_manager, sample_docket_files):
        """Test that first call to get_cached_filenames loads from filesystem."""
        iso_date = '20250612'
        
        # First call should load from filesystem
        filenames = await file_manager._get_cached_filenames(iso_date)
        
        # Should return lowercased stems
        expected_stems = [f.replace('.json', '').lower() for f in sample_docket_files]
        assert len(filenames) == len(expected_stems)
        assert all(stem in filenames for stem in expected_stems)

    @pytest.mark.asyncio
    async def test_get_cached_filenames_cached_call(self, file_manager, sample_docket_files):
        """Test that subsequent calls use cached data."""
        iso_date = '20250612'
        
        # First call
        filenames_1 = await file_manager._get_cached_filenames(iso_date)
        
        # Second call should return same data without filesystem access
        filenames_2 = await file_manager._get_cached_filenames(iso_date)
        
        assert filenames_1 == filenames_2
        assert iso_date in file_manager._filename_cache

    @pytest.mark.asyncio
    async def test_get_cached_filenames_nonexistent_date(self, file_manager):
        """Test caching behavior for non-existent date directory."""
        iso_date = '20990101'  # Far future date
        
        filenames = await file_manager._get_cached_filenames(iso_date)
        
        assert filenames == []
        assert iso_date in file_manager._filename_cache
        assert file_manager._filename_cache[iso_date] == []

    @pytest.mark.asyncio
    async def test_filename_cache_multiple_dates(self, file_manager, sample_multi_date_files):
        """Test caching across multiple dates."""
        base_date = date(2025, 6, 12)
        
        # Load cache for multiple dates
        for i in range(3):
            test_date = base_date - timedelta(days=i)
            iso_date = test_date.strftime('%Y%m%d')
            filenames = await file_manager._get_cached_filenames(iso_date)
            assert len(filenames) == 1  # One file per date
            assert iso_date in file_manager._filename_cache


class TestOptimizedPatternMatching:
    """Test the optimized check_if_downloaded_by_pattern method."""

    @pytest.mark.asyncio
    async def test_pattern_matching_with_cache(self, file_manager, sample_docket_files):
        """Test that pattern matching uses cached filenames."""
        iso_date = '20250612'
        
        # Test matching pattern
        result = await file_manager.check_if_downloaded_by_pattern(iso_date, 'njd', '25_00001')
        assert result is True  # Should find njd_25_00001_SMITH_v_ACME with PDF
        
        # Verify cache was populated
        assert iso_date in file_manager._filename_cache

    @pytest.mark.asyncio
    async def test_pattern_matching_no_match(self, file_manager, sample_docket_files):
        """Test pattern matching with no matches."""
        iso_date = '20250612'
        
        result = await file_manager.check_if_downloaded_by_pattern(iso_date, 'txed', '25_99999')
        assert result is False

    @pytest.mark.asyncio
    async def test_pattern_matching_json_only(self, file_manager, sample_docket_files):
        """Test pattern matching for files with JSON but no PDF/ZIP."""
        iso_date = '20250612'
        
        # flnd_25_00770 has JSON but no PDF
        result = await file_manager.check_if_downloaded_by_pattern(iso_date, 'flnd', '25_00770')
        assert result is False  # Should return False as no PDF/ZIP exists

    @pytest.mark.asyncio
    async def test_pattern_matching_multiple_matches(self, file_manager, sample_docket_files):
        """Test pattern matching when multiple files match pattern."""
        iso_date = '20250612'
        
        # Pattern that should match multiple njd files
        result = await file_manager.check_if_downloaded_by_pattern(iso_date, 'njd', '25_0000')
        assert result is True  # Should find at least one with PDF

    @pytest.mark.asyncio
    async def test_pattern_matching_case_insensitive(self, file_manager, sample_docket_files):
        """Test that pattern matching is case-insensitive."""
        iso_date = '20250612'
        
        # Test with uppercase
        result_upper = await file_manager.check_if_downloaded_by_pattern(iso_date, 'NJD', '25_00001')
        result_lower = await file_manager.check_if_downloaded_by_pattern(iso_date, 'njd', '25_00001')
        
        assert result_upper == result_lower


class TestSevenDayPreloading:
    """Test the 7-day filename cache preloading functionality."""

    @pytest.mark.asyncio
    async def test_preload_filename_cache_for_7_days(self, file_manager, sample_multi_date_files):
        """Test preloading cache for 7 days."""
        end_date = date(2025, 6, 12)
        
        # Preload cache
        await file_manager.preload_filename_cache_for_7_days(end_date)
        
        # Verify all 7 days are cached
        for i in range(7):
            check_date = end_date - timedelta(days=i)
            iso_date = check_date.strftime('%Y%m%d')
            assert iso_date in file_manager._filename_cache

    @pytest.mark.asyncio
    async def test_preload_cache_performance(self, file_manager, sample_multi_date_files):
        """Test that preloading is more efficient than individual calls."""
        end_date = date(2025, 6, 12)
        
        # Time the preload operation
        import time
        start_time = time.time()
        await file_manager.preload_filename_cache_for_7_days(end_date)
        preload_time = time.time() - start_time
        
        # Clear cache and time individual calls
        file_manager._filename_cache.clear()
        start_time = time.time()
        for i in range(7):
            check_date = end_date - timedelta(days=i)
            iso_date = check_date.strftime('%Y%m%d')
            await file_manager._get_cached_filenames(iso_date)
        individual_time = time.time() - start_time
        
        # Preload should be comparable or faster (mainly testing it doesn't error)
        assert preload_time >= 0
        assert individual_time >= 0

    @pytest.mark.asyncio
    async def test_check_if_downloaded_last_7_days_with_preload(self, file_manager, sample_multi_date_files):
        """Test optimized 7-day check with preloading."""
        end_date = date(2025, 6, 12)
        
        # This should trigger preloading internally
        result = await file_manager.check_if_downloaded_last_7_days_by_pattern(
            end_date, 'njd', '25_01000'
        )
        
        # Should find the file from day 0
        assert result is True
        
        # Verify cache was populated for all 7 days
        for i in range(7):
            check_date = end_date - timedelta(days=i)
            iso_date = check_date.strftime('%Y%m%d')
            assert iso_date in file_manager._filename_cache


class TestOptimizationEdgeCases:
    """Test edge cases and error handling in optimizations."""

    @pytest.mark.asyncio
    async def test_cache_with_filesystem_errors(self, file_manager, temp_dir):
        """Test caching behavior when filesystem operations fail."""
        iso_date = '20250612'
        
        # Mock glob to raise an exception
        with patch('pathlib.Path.glob', side_effect=OSError("Permission denied")):
            filenames = await file_manager._get_cached_filenames(iso_date)
            
            # Should return empty list and cache it
            assert filenames == []
            assert iso_date in file_manager._filename_cache

    @pytest.mark.asyncio
    async def test_pattern_matching_with_corrupted_json(self, file_manager, temp_dir):
        """Test pattern matching with corrupted JSON files."""
        # Create directory and corrupted JSON file
        date_dir = Path(temp_dir) / '20250612' / 'dockets'
        date_dir.mkdir(parents=True, exist_ok=True)
        
        json_path = date_dir / 'njd_25_00001_corrupted.json'
        json_path.write_text('{ invalid json content')
        
        iso_date = '20250612'
        result = await file_manager.check_if_downloaded_by_pattern(iso_date, 'njd', '25_00001')
        
        # Should handle the error gracefully
        assert result is False

    @pytest.mark.asyncio
    async def test_empty_directory_optimization(self, file_manager, temp_dir):
        """Test optimizations with empty directories."""
        # Create empty directory
        date_dir = Path(temp_dir) / '20250612' / 'dockets'
        date_dir.mkdir(parents=True, exist_ok=True)
        
        iso_date = '20250612'
        filenames = await file_manager._get_cached_filenames(iso_date)
        
        assert filenames == []
        assert iso_date in file_manager._filename_cache


class TestPerformanceImprovements:
    """Test that optimizations provide performance improvements."""

    @pytest.mark.asyncio
    async def test_list_comprehension_performance(self, file_manager, temp_dir):
        """Test that list comprehension pattern matching is efficient."""
        # Create many test files
        date_dir = Path(temp_dir) / '20250612' / 'dockets'
        date_dir.mkdir(parents=True, exist_ok=True)
        
        # Create 100 test files
        for i in range(100):
            filename = f'test_{i:03d}_case.json'
            json_path = date_dir / filename
            json_path.write_text(json.dumps({'test': f'case_{i}'}))
        
        # Add one matching file
        target_file = date_dir / 'njd_25_00001_target.json'
        target_file.write_text(json.dumps({'test': 'target'}))
        
        # Test pattern matching
        iso_date = '20250612'
        result = await file_manager.check_if_downloaded_by_pattern(iso_date, 'njd', '25_00001')
        
        # Should find the target file efficiently
        assert result is False  # No PDF, so returns False
        
        # But should have populated cache efficiently
        assert iso_date in file_manager._filename_cache
        assert len(file_manager._filename_cache[iso_date]) == 101  # 100 + 1 target

    @pytest.mark.asyncio
    async def test_cache_reuse_efficiency(self, file_manager, sample_docket_files):
        """Test that cache reuse avoids redundant filesystem operations."""
        iso_date = '20250612'
        
        # First call populates cache
        await file_manager.check_if_downloaded_by_pattern(iso_date, 'njd', '25_00001')
        
        # Mock filesystem operations to ensure they're not called again
        with patch('pathlib.Path.glob') as mock_glob:
            # Second call should use cache
            await file_manager.check_if_downloaded_by_pattern(iso_date, 'njd', '25_00002')
            
            # glob should not have been called
            mock_glob.assert_not_called()


if __name__ == '__main__':
    pytest.main([__file__, '-v'])