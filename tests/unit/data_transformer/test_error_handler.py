"""
Unit tests for <PERSON>rror<PERSON>andler.

Tests the error handling functionality extracted from transformer.py
as part of Phase 3 refactoring.
"""
import pytest
from unittest.mock import AsyncMock, MagicMock

from src.data_transformer.error_handler import ErrorHandler


class TestErrorHandler:
    """Test ErrorHandler functionality."""
    
    @pytest.fixture
    def mock_file_handler(self):
        """Mock file handler for ErrorHandler."""
        file_handler = MagicMock()
        file_handler.save_json_async = AsyncMock()
        return file_handler
    
    @pytest.fixture
    def mock_logger(self):
        """Mock logger for ErrorHandler."""
        return MagicMock()
    
    @pytest.fixture
    def error_handler(self, mock_file_handler, mock_logger):
        """Create ErrorHandler instance."""
        return <PERSON>rror<PERSON>andler(mock_file_handler, mock_logger)
    
    @pytest.mark.asyncio
    async def test_update_error_status_and_save_success(self, error_handler, mock_file_handler):
        """Test successful error status update and save."""
        data = {'existing_field': 'value'}
        json_path = '/test/path.json'
        error_message = 'Test error message'
        
        await error_handler.update_error_status_and_save(data, json_path, error_message)
        
        # Verify data was updated
        assert 'processing_errors' in data
        assert error_message in data['processing_errors']
        assert data['processing_status'] == 'error'
        
        # Verify save was called
        mock_file_handler.save_json_async.assert_called_once_with(json_path, data)
    
    @pytest.mark.asyncio
    async def test_update_error_status_and_save_none_data(self, error_handler, mock_file_handler):
        """Test error status update with None data."""
        await error_handler.update_error_status_and_save(None, '/test/path.json', 'Test error')
        
        # Should not attempt to save
        mock_file_handler.save_json_async.assert_not_called()
    
    @pytest.mark.asyncio
    async def test_update_error_status_and_save_file_error(self, error_handler, mock_file_handler, mock_logger):
        """Test error status update when file save fails."""
        mock_file_handler.save_json_async.side_effect = Exception("Save failed")
        
        data = {'existing_field': 'value'}
        await error_handler.update_error_status_and_save(data, '/test/path.json', 'Test error')
        
        # Verify error was logged
        mock_logger.error.assert_called()
    
    def test_update_error_status_new_errors(self, error_handler):
        """Test updating error status with new errors."""
        data = {'existing_field': 'value'}
        error_message = 'Test error message'
        
        error_handler.update_error_status(data, '/test/path.json', error_message)
        
        assert 'processing_errors' in data
        assert isinstance(data['processing_errors'], list)
        assert error_message in data['processing_errors']
        assert data['processing_status'] == 'error'
    
    def test_update_error_status_existing_errors_list(self, error_handler):
        """Test updating error status with existing errors list."""
        data = {
            'processing_errors': ['Existing error'],
            'processing_status': 'warning'
        }
        error_message = 'New error message'
        
        error_handler.update_error_status(data, '/test/path.json', error_message)
        
        assert len(data['processing_errors']) == 2
        assert 'Existing error' in data['processing_errors']
        assert error_message in data['processing_errors']
        assert data['processing_status'] == 'error'
    
    def test_update_error_status_existing_errors_string(self, error_handler):
        """Test updating error status with existing errors as string."""
        data = {
            'processing_errors': 'Existing error string',
            'processing_status': 'warning'
        }
        error_message = 'New error message'
        
        error_handler.update_error_status(data, '/test/path.json', error_message)
        
        assert isinstance(data['processing_errors'], list)
        assert len(data['processing_errors']) == 2
        assert 'Existing error string' in data['processing_errors']
        assert error_message in data['processing_errors']
        assert data['processing_status'] == 'error'
    
    def test_update_error_status_none_data(self, error_handler, mock_logger):
        """Test updating error status with None data."""
        error_handler.update_error_status(None, '/test/path.json', 'Test error')
        
        # Should log error about None data
        mock_logger.error.assert_called()
    
    def test_clear_processing_errors(self, error_handler):
        """Test clearing processing errors."""
        data = {
            'processing_errors': ['Error 1', 'Error 2'],
            'processing_status': 'error',
            'other_field': 'value'
        }
        
        error_handler.clear_processing_errors(data)
        
        assert 'processing_errors' not in data
        assert 'processing_status' not in data
        assert 'other_field' in data  # Other fields should remain
    
    def test_clear_processing_errors_no_errors(self, error_handler):
        """Test clearing processing errors when none exist."""
        data = {'other_field': 'value'}
        
        error_handler.clear_processing_errors(data)
        
        # Should not raise error
        assert 'other_field' in data
    
    def test_has_processing_errors_with_errors_list(self, error_handler):
        """Test checking for processing errors with errors list."""
        data = {'processing_errors': ['Error 1', 'Error 2']}
        
        assert error_handler.has_processing_errors(data) is True
    
    def test_has_processing_errors_with_error_status(self, error_handler):
        """Test checking for processing errors with error status."""
        data = {'processing_status': 'error'}
        
        assert error_handler.has_processing_errors(data) is True
    
    def test_has_processing_errors_empty_list(self, error_handler):
        """Test checking for processing errors with empty list."""
        data = {'processing_errors': []}
        
        assert error_handler.has_processing_errors(data) is False
    
    def test_has_processing_errors_no_errors(self, error_handler):
        """Test checking for processing errors with no errors."""
        data = {'other_field': 'value'}
        
        assert error_handler.has_processing_errors(data) is False
    
    def test_get_error_summary_with_errors_list(self, error_handler):
        """Test getting error summary with errors list."""
        data = {
            'processing_errors': ['Error 1', 'Error 2', 'Error 3', 'Error 4', 'Error 5'],
            'processing_status': 'error'
        }
        
        summary = error_handler.get_error_summary(data)
        
        assert 'Status: error' in summary
        assert 'Errors: 5' in summary
        # Should include last 3 errors
        assert 'Error 3' in summary
        assert 'Error 4' in summary
        assert 'Error 5' in summary
    
    def test_get_error_summary_with_error_string(self, error_handler):
        """Test getting error summary with error string."""
        data = {
            'processing_errors': 'Single error message',
            'processing_status': 'error'
        }
        
        summary = error_handler.get_error_summary(data)
        
        assert 'Status: error' in summary
        assert 'Error: Single error message' in summary
    
    def test_get_error_summary_no_errors(self, error_handler):
        """Test getting error summary with no errors."""
        data = {'processing_status': 'completed'}
        
        summary = error_handler.get_error_summary(data)
        
        assert summary == 'Status: completed'
    
    def test_get_error_summary_unknown_status(self, error_handler):
        """Test getting error summary with unknown status."""
        data = {}
        
        summary = error_handler.get_error_summary(data)
        
        assert summary == 'Status: unknown'


if __name__ == "__main__":
    pytest.main([__file__, "-v"])