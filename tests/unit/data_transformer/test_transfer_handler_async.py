"""
Unit tests for TransferHandler async migration.

Tests the elimination of compatibility layer and direct async repository usage.
"""
import asyncio
import pytest
from unittest.mock import AsyncMock, MagicMock, patch

# Import the transfer_handler we're testing
from src.data_transformer.transfer_handler import TransferHandler


class TestTransferHandlerAsyncMigration:
    """Test the async migration of TransferHandler class."""
    
    @pytest.fixture
    def mock_config(self):
        """Mock configuration for TransferHandler."""
        return {'test_config': True}
    
    @pytest.fixture
    def mock_pacer_db(self):
        """Mock PacerRepository."""
        mock_pacer = AsyncMock()
        mock_pacer.update_item = AsyncMock(return_value=True)
        mock_pacer.get_by_docket_number = AsyncMock(return_value={'test': 'case'})
        return mock_pacer
    
    @pytest.fixture
    def mock_district_court_db(self):
        """Mock DistrictCourtsRepository."""
        mock_courts = AsyncMock()
        mock_courts.scan_all = AsyncMock(return_value=[
            {'CourtName': 'Test District Court', 'CourtId': 'testd'},
            {'CourtName': 'Another Court', 'CourtId': 'anothd'}
        ])
        return mock_courts
    
    @pytest.fixture
    def mock_mdl_processor(self):
        """Mock MDLProcessor."""
        mock_mdl = MagicMock()
        mock_mdl._create_mdl_lookup = MagicMock(return_value={
            '123': 'testd',
            '456': 'anothd'
        })
        mock_mdl._create_mdl_lookup_async = AsyncMock(return_value={
            '2741': 'cand',  # Roundup MDL case
            '123': 'testd',
            '456': 'anothd'
        })
        return mock_mdl
    
    @pytest.fixture
    def transfer_handler(self, mock_config, mock_pacer_db, mock_district_court_db, mock_mdl_processor):
        """Create TransferHandler instance with mocked dependencies."""
        return TransferHandler(mock_config, mock_pacer_db, mock_district_court_db, mock_mdl_processor)
    
    def test_no_compatibility_layer_imports(self, transfer_handler):
        """Test that no compatibility layer code remains."""
        # Verify no compatibility layer attributes exist
        assert not hasattr(transfer_handler, '_using_new_architecture')
        assert not hasattr(transfer_handler, 'create_manager_replacement')
        
        # Verify the class doesn't reference compatibility layer methods
        import inspect
        source = inspect.getsource(TransferHandler)
        
        compatibility_patterns = [
            'create_manager_replacement',
            'src.migration',
            '_using_new_architecture',
            'TYPE_CHECKING'
        ]
        
        for pattern in compatibility_patterns:
            assert pattern not in source, f"Found compatibility layer reference: {pattern}"
    
    def test_direct_repository_types(self, transfer_handler):
        """Test that repositories are directly typed without string quotes."""
        from src.repositories.pacer_repository import PacerRepository
        from src.repositories.district_courts_repository import DistrictCourtsRepository
        
        # Verify direct type usage (not string references)
        assert isinstance(transfer_handler.pacer_db, (PacerRepository, AsyncMock))
        assert isinstance(transfer_handler.district_court_db, (DistrictCourtsRepository, AsyncMock))
    
    def test_sync_bridge_method_exists(self, transfer_handler):
        """Test that the sync bridge method exists for Phase 1."""
        # The bridge method should exist to handle sync-to-async conversion
        assert hasattr(transfer_handler, '_sync_scan_courts')
        assert callable(getattr(transfer_handler, '_sync_scan_courts'))
    
    def test_district_court_lookup_creation(self, transfer_handler, mock_district_court_db):
        """Test district court lookup creation with new repository."""
        # Mock the bridge method to return test data
        with patch.object(transfer_handler, '_sync_scan_courts') as mock_sync_scan:
            mock_sync_scan.return_value = [
                {'CourtName': 'Test District Court', 'CourtId': 'testd'},
                {'CourtName': 'Another Court', 'CourtId': 'anothd'}
            ]
            
            lookup = transfer_handler._create_district_court_lookup()
            
            # Should create proper lookup dictionary
            # The implementation creates lookups for both full names and partial names
            expected_keys = {'Test District Court', 'Another Court'}
            actual_keys = set(lookup.keys())
            assert expected_keys.issubset(actual_keys), f"Missing expected keys. Expected: {expected_keys}, Got: {actual_keys}"
            
            # Should cache the result
            assert transfer_handler._district_court_lookup_loaded is True
            assert transfer_handler._district_court_lookup_cache == lookup
    
    @pytest.mark.asyncio
    async def test_process_transfers_async_functionality(self, transfer_handler, mock_mdl_processor):
        """Test async transfer processing functionality."""
        test_data = {
            'court_id': 'testd',
            'docket_num': '1:23-cv-00001',
            'mdl_num': '123'
        }
        
        # Should process without errors using async method
        await transfer_handler.process_transfers(test_data)
        
        # Should have added transfer flags
        assert 'transferred_in' in test_data
        assert 'is_transferred' in test_data
        assert 'pending_cto' in test_data
        assert 'is_removal' in test_data
    
    @pytest.mark.asyncio 
    async def test_pending_cto_rule_application(self, transfer_handler, mock_mdl_processor):
        """Test that pending CTO rule is correctly applied for Roundup case."""
        # Test case: New Mexico court with Roundup MDL should be pending CTO
        test_data = {
            'court_id': 'nmd',  # New Mexico District
            'docket_num': '1:25-cv-00547',
            'mdl_num': '2741',  # Roundup MDL
            'new_filename': 'nmd_25_00547_test_case',
            'is_removal': False
        }
        
        # Process transfers
        await transfer_handler.process_transfers(test_data)
        
        # Should apply Rule 2: Pending CTO
        assert test_data['pending_cto'] is True
        assert test_data['is_transferred'] is False
        assert test_data['transferred_in'] is False
        assert test_data['is_removal'] is False
    
    @pytest.mark.asyncio
    async def test_mdl_lookup_uses_async_method(self, transfer_handler, mock_mdl_processor):
        """Test that transfer processing uses async MDL lookup method."""
        test_data = {
            'court_id': 'nmd',
            'docket_num': '1:25-cv-00547', 
            'mdl_num': '2741',
            'new_filename': 'test_case'
        }
        
        # Process transfers
        await transfer_handler.process_transfers(test_data)
        
        # Verify async MDL lookup was called
        mock_mdl_processor._create_mdl_lookup_async.assert_called_once()
        # Verify sync method was NOT called
        mock_mdl_processor._create_mdl_lookup.assert_not_called()
    
    @pytest.mark.asyncio
    async def test_in_mdl_court_rule(self, transfer_handler, mock_mdl_processor):
        """Test Rule 3: Case already in correct MDL court."""
        test_data = {
            'court_id': 'cand',  # Northern District of California (Roundup MDL court)
            'docket_num': '3:25-cv-00001',
            'mdl_num': '2741',  # Roundup MDL
            'new_filename': 'cand_25_00001_test_case'
        }
        
        await transfer_handler.process_transfers(test_data)
        
        # Should apply Rule 3: In MDL Court (no transferor)
        assert test_data['pending_cto'] is False
        assert test_data['is_transferred'] is False
        assert test_data['transferred_in'] is False
    
    @pytest.mark.asyncio
    async def test_transferred_in_rule(self, transfer_handler, mock_mdl_processor):
        """Test Rule 4: Case transferred into MDL court."""
        test_data = {
            'court_id': 'cand',  # Northern District of California (Roundup MDL court)
            'docket_num': '3:25-cv-00001',
            'mdl_num': '2741',  # Roundup MDL
            'transferor_court_id': 'nmd',  # Transferred from New Mexico
            'new_filename': 'cand_25_00001_transferred_case'
        }
        
        await transfer_handler.process_transfers(test_data)
        
        # Should apply Rule 4: Transferred IN
        assert test_data['pending_cto'] is False
        assert test_data['is_transferred'] is True
        assert test_data['transferred_in'] is True
    
    @pytest.mark.asyncio
    async def test_district_court_lookup_async(self, transfer_handler, mock_district_court_db):
        """Test async district court lookup functionality."""
        # Test the async district court lookup method
        lookup = await transfer_handler._create_district_court_lookup_async()
        
        # Should call the async scan method
        mock_district_court_db.scan_all.assert_called_once()
        
        # Should create proper lookup dictionary
        expected_keys = {'Test District Court', 'Another Court'}
        actual_keys = set(lookup.keys())
        assert expected_keys.issubset(actual_keys)
        
        # Should cache the result
        assert transfer_handler._district_court_lookup_loaded is True
        assert transfer_handler._district_court_lookup_cache == lookup


if __name__ == "__main__":
    pytest.main([__file__, "-v"])