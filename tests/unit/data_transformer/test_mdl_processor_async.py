"""
Unit tests for MDLProcessor async migration.

Tests the async MDL lookup functionality and bridge patterns.
"""
import asyncio
import pytest
from unittest.mock import AsyncMock, MagicMock, patch
import pandas as pd

# Import the mdl_processor we're testing
from src.data_transformer.mdl_processor import MDLProcessor


class TestMDLProcessorAsyncMigration:
    """Test the async migration of MDLProcessor class."""
    
    @pytest.fixture
    def mock_config(self):
        """Mock configuration for MDLProcessor."""
        return {
            'directories': {'base_dir': '/tmp/test'},
            'test_config': True
        }
    
    @pytest.fixture
    def mock_district_court_db(self):
        """Mock DistrictCourtsRepository."""
        mock_courts = AsyncMock()
        mock_courts.scan_all = AsyncMock(return_value=[
            {'MdlNum': '2873', 'CourtId': 'scd'},
            {'MdlNum': '3092', 'CourtId': 'njd'},
            {'MdlNum': 'NA', 'CourtId': 'testd'},  # Should be filtered out
            {'MdlNum': '2775', 'CourtId': 'cand'}
        ])
        return mock_courts
    
    @pytest.fixture
    def mock_mdl_dataframe(self):
        """Mock MDL litigation DataFrame."""
        return pd.DataFrame([
            {'mdl_num': '2873', 'litigation': 'AFFF', 'description': 'Test AFFF case'},
            {'mdl_num': '3092', 'litigation': 'Depo-Provera', 'description': 'Test Depo case'}
        ])
    
    @pytest.fixture
    def mdl_processor(self, mock_config, mock_district_court_db, mock_mdl_dataframe):
        """Create MDLProcessor instance with mocked dependencies."""
        mock_file_handler = MagicMock()
        mock_gpt = MagicMock()
        
        return MDLProcessor(
            mdl_litigations=mock_mdl_dataframe,
            mdl_path='/tmp/test/mdl_lookup.json',
            file_handler=mock_file_handler,
            gpt=mock_gpt,
            config=mock_config,
            district_court_db=mock_district_court_db
        )
    
    @pytest.mark.asyncio
    async def test_create_mdl_lookup_async_success(self, mdl_processor, mock_district_court_db):
        """Test successful async MDL lookup creation."""
        # Reset cache to ensure fresh test
        mdl_processor._mdl_lookup_loaded = False
        mdl_processor._mdl_lookup_cache = None
        
        result = await mdl_processor._create_mdl_lookup_async()
        
        # Verify repository was called
        mock_district_court_db.scan_all.assert_called_once()
        
        # Verify lookup dictionary was created correctly
        expected_lookup = {
            '2873': 'scd',
            '3092': 'njd',
            '2775': 'cand'
            # 'NA' should be filtered out
        }
        assert result == expected_lookup
        
        # Verify caching
        assert mdl_processor._mdl_lookup_loaded is True
        assert mdl_processor._mdl_lookup_cache == expected_lookup
    
    @pytest.mark.asyncio
    async def test_create_mdl_lookup_async_cached(self, mdl_processor, mock_district_court_db):
        """Test cached MDL lookup returns without repository call."""
        # Pre-populate cache
        cached_lookup = {'1234': 'testd'}
        mdl_processor._mdl_lookup_cache = cached_lookup
        mdl_processor._mdl_lookup_loaded = True
        
        result = await mdl_processor._create_mdl_lookup_async()
        
        # Should return cached version without calling repository
        mock_district_court_db.scan_all.assert_not_called()
        assert result == cached_lookup
    
    @pytest.mark.asyncio
    async def test_create_mdl_lookup_async_no_repository(self, mdl_processor):
        """Test handling when district_court_db is None."""
        mdl_processor.district_court_db = None
        mdl_processor._mdl_lookup_loaded = False
        
        result = await mdl_processor._create_mdl_lookup_async()
        
        # Should return empty dict and mark as loaded
        assert result == {}
        assert mdl_processor._mdl_lookup_loaded is True
        assert mdl_processor._mdl_lookup_cache == {}
    
    @pytest.mark.asyncio
    async def test_create_mdl_lookup_async_repository_error(self, mdl_processor, mock_district_court_db):
        """Test handling of repository errors."""
        mock_district_court_db.scan_all.side_effect = Exception("Repository error")
        mdl_processor._mdl_lookup_loaded = False
        
        result = await mdl_processor._create_mdl_lookup_async()
        
        # Should return empty dict on error
        assert result == {}
        assert mdl_processor._mdl_lookup_loaded is True
        assert mdl_processor._mdl_lookup_cache == {}
    
    @pytest.mark.asyncio
    async def test_create_mdl_lookup_async_empty_data(self, mdl_processor, mock_district_court_db):
        """Test handling when repository returns no data."""
        mock_district_court_db.scan_all.return_value = []
        mdl_processor._mdl_lookup_loaded = False
        
        result = await mdl_processor._create_mdl_lookup_async()
        
        assert result == {}
        assert mdl_processor._mdl_lookup_loaded is True
    
    @pytest.mark.asyncio
    async def test_create_mdl_lookup_async_invalid_data(self, mdl_processor, mock_district_court_db):
        """Test handling of invalid data structure."""
        # Missing required columns
        mock_district_court_db.scan_all.return_value = [
            {'InvalidCol': 'value1'},
            {'AnotherCol': 'value2'}
        ]
        mdl_processor._mdl_lookup_loaded = False
        
        result = await mdl_processor._create_mdl_lookup_async()
        
        assert result == {}
        assert mdl_processor._mdl_lookup_loaded is True
    
    def test_deprecation_warnings(self, mdl_processor):
        """Test that deprecated sync methods show warnings."""
        with patch.object(mdl_processor.logger, 'warning') as mock_warning:
            # The legacy sync method should show deprecation warning
            try:
                mdl_processor._create_mdl_lookup_legacy_sync()
            except Exception:
                # May fail due to sync repository call, but warning should be logged
                pass
            
            # Should have logged deprecation warning
            mock_warning.assert_called_with(
                "Using deprecated sync MDL lookup method. Please migrate to async version."
            )
    
    def test_async_vs_legacy_sync_architecture(self, mdl_processor, mock_district_court_db):
        """Test async architecture vs legacy sync architecture."""
        # Reset cache for fair comparison
        def reset_cache():
            mdl_processor._mdl_lookup_loaded = False
            mdl_processor._mdl_lookup_cache = None
        
        # Test direct async performance (run outside async context)
        reset_cache()
        async_result = asyncio.run(mdl_processor._create_mdl_lookup_async())
        
        # Test legacy sync method - should fail gracefully with async repository
        reset_cache()
        with patch.object(mdl_processor.logger, 'error'):
            legacy_result = mdl_processor._create_mdl_lookup_legacy_sync()
        
        # Async should succeed
        assert len(async_result) > 0
        
        # Legacy sync should fail gracefully when async repository is used
        assert legacy_result == {}  # Should return empty dict when it can't use async repo
        
        print(f"\nAsync result: {len(async_result)} entries")
        print(f"Legacy sync result: {len(legacy_result)} entries (expected 0 with async repo)")
    
    def test_data_filtering_logic(self, mdl_processor, mock_district_court_db):
        """Test that data filtering works correctly."""
        # Test with various invalid data scenarios
        mock_district_court_db.scan_all = AsyncMock(return_value=[
            {'MdlNum': '2873', 'CourtId': 'scd'},       # Valid
            {'MdlNum': 'NA', 'CourtId': 'testd'},       # Invalid MDL num
            {'MdlNum': '', 'CourtId': 'testd'},         # Empty MDL num
            {'MdlNum': '3092', 'CourtId': ''},          # Empty CourtId
            {'MdlNum': '2775', 'CourtId': 'cand'},      # Valid
            {'MdlNum': None, 'CourtId': 'testd'},       # Null MDL num
        ])
        
        mdl_processor._mdl_lookup_loaded = False
        
        # Use asyncio.run for sync test method
        result = asyncio.run(mdl_processor._create_mdl_lookup_async())
        
        # Only valid entries should remain (None should be filtered out)
        expected = {
            '2873': 'scd',
            '2775': 'cand'
        }
        assert result == expected
        
        # Verify None was filtered out
        assert 'None' not in result
        assert 'NA' not in result
        assert '' not in result
    
    def test_update_district_court_db_method_exists(self, mdl_processor):
        """Test that update_district_court_db method exists and is callable."""
        assert hasattr(mdl_processor, 'update_district_court_db')
        assert callable(getattr(mdl_processor, 'update_district_court_db'))
    
    def test_update_district_court_db_reinitializes_lookup_manager(self, mdl_processor):
        """Test that update_district_court_db properly re-initializes lookup manager."""
        # Store original lookup manager reference
        original_lookup_manager = mdl_processor.lookup_manager
        
        # Create new mock district court DB
        new_district_court_db = AsyncMock()
        new_district_court_db.scan_all = AsyncMock(return_value=[
            {'MdlNum': '2741', 'CourtId': 'cand'},  # Roundup case
            {'MdlNum': '2873', 'CourtId': 'scd'}
        ])
        
        # Update the district court database
        mdl_processor.update_district_court_db(new_district_court_db)
        
        # Verify district_court_db was updated
        assert mdl_processor.district_court_db is new_district_court_db
        
        # Verify lookup manager was re-initialized (should be a new instance)
        assert mdl_processor.lookup_manager is not original_lookup_manager
        
        # Verify the new lookup manager has the correct district court DB
        assert mdl_processor.lookup_manager.district_court_db is new_district_court_db
    
    @pytest.mark.asyncio
    async def test_update_district_court_db_fixes_mdl_lookup_issue(self, mdl_processor):
        """Test that update_district_court_db fixes the empty MDL lookup issue."""
        # Start with None district court DB (simulating initial state)
        mdl_processor.district_court_db = None
        # Force re-initialization of lookup manager with None DB
        mdl_processor.update_district_court_db(None)
        mdl_processor._mdl_lookup_loaded = False
        
        # Verify initial lookup returns empty (the bug we fixed)
        initial_lookup = await mdl_processor._create_mdl_lookup_async()
        assert initial_lookup == {}
        
        # Now update with proper district court DB (simulating async_init)
        new_district_court_db = AsyncMock()
        new_district_court_db.scan_all = AsyncMock(return_value=[
            {'MdlNum': '2741', 'CourtId': 'cand'},  # Roundup case
            {'MdlNum': '2873', 'CourtId': 'scd'}
        ])
        
        mdl_processor.update_district_court_db(new_district_court_db)
        
        # Reset the lookup cache to force re-creation
        mdl_processor._mdl_lookup_loaded = False
        mdl_processor._mdl_lookup_cache = None
        
        # Now lookup should work properly
        fixed_lookup = await mdl_processor._create_mdl_lookup_async()
        
        # Should contain the MDL mappings
        assert fixed_lookup == {
            '2741': 'cand',
            '2873': 'scd'
        }
        
        # Verify the district court database was called
        new_district_court_db.scan_all.assert_called_once()


if __name__ == "__main__":
    pytest.main([__file__, "-v"])