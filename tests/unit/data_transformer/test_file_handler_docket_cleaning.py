"""
Tests for FileHandler docket number cleaning functionality
"""
import pytest
from src.data_transformer.file_handler import FileHandler


class TestFileHandlerDocketCleaning:
    """Test docket number cleaning in FileHandler"""

    def test_clean_docket_num_removes_suffix(self):
        """Test that docket number cleaning removes judge suffixes"""
        # Test cases from the user requirements
        assert FileHandler.clean_docket_num("1:25-sf-65985-JPC") == "1:25-sf-65985"
        assert FileHandler.clean_docket_num("1:25-sf-65985-JPC-MRS") == "1:25-sf-65985"
        
    def test_clean_docket_num_various_formats(self):
        """Test cleaning with various docket number formats"""
        # Standard formats
        assert FileHandler.clean_docket_num("3:25-cv-10287-MAS-RLS") == "3:25-cv-10287"
        assert FileHandler.clean_docket_num("2:25-cv-05339-RMG") == "2:25-cv-05339"
        assert FileHandler.clean_docket_num("1:24-cv-12345-ABC-DEF-GHI") == "1:24-cv-12345"
        
    def test_clean_docket_num_preserves_correct_format(self):
        """Test that correctly formatted docket numbers are preserved"""
        assert FileHandler.clean_docket_num("1:25-sf-65985") == "1:25-sf-65985"
        assert FileHandler.clean_docket_num("3:25-cv-10287") == "3:25-cv-10287"
        
    def test_clean_docket_num_handles_edge_cases(self):
        """Test edge cases for docket number cleaning"""
        # None/empty inputs
        assert FileHandler.clean_docket_num(None) is None
        assert FileHandler.clean_docket_num("") == ""
        assert FileHandler.clean_docket_num("   ") == "   "
        
        # Non-string inputs
        assert FileHandler.clean_docket_num(123) == 123
        assert FileHandler.clean_docket_num([]) == []
        
        # Malformed docket numbers (should return original)
        assert FileHandler.clean_docket_num("invalid-format") == "invalid-format"
        assert FileHandler.clean_docket_num("1:25") == "1:25"
        assert FileHandler.clean_docket_num("cv-12345") == "cv-12345"
        
    def test_clean_docket_num_13_character_requirement(self):
        """Test that cleaned docket numbers are exactly 13 characters"""
        test_cases = [
            "1:25-sf-65985-JPC",
            "3:25-cv-10287-MAS-RLS", 
            "2:25-cv-05339-RMG"
        ]
        
        for docket in test_cases:
            cleaned = FileHandler.clean_docket_num(docket)
            # Should be 13 characters: N:NN-XX-NNNNN format
            assert len(cleaned) == 13, f"Cleaned docket {cleaned} should be 13 characters"
            
    def test_clean_docket_num_regex_pattern(self):
        """Test that the regex pattern correctly identifies valid docket numbers"""
        # Valid patterns that should be cleaned
        valid_patterns = [
            "1:25-sf-65985-JPC",
            "12:24-cv-12345-ABC",
            "9:23-cr-98765-XYZ-DEF"
        ]
        
        for pattern in valid_patterns:
            cleaned = FileHandler.clean_docket_num(pattern)
            # Should match pattern: digits:digits-letters-digits
            parts = cleaned.split('-')
            assert len(parts) == 3
            assert ':' in parts[0]
            assert parts[1].isalpha() and len(parts[1]) == 2
            assert parts[2].isdigit() and len(parts[2]) == 5