"""
Unit tests for MDLDataProcessor component.
"""
import pytest
from unittest.mock import Mock, AsyncMock, patch
import pandas as pd
from src.data_transformer.mdl_data_processor import MDLDataProcessor


class TestMDLDataProcessor:
    """Test suite for MDLDataProcessor component."""
    
    @pytest.fixture
    def processor(self):
        return MDLDataProcessor(config={'test': True})
    
    @pytest.fixture
    def sample_mdl_df(self):
        return pd.DataFrame([
            {'mdl_num': '2873', 'litigation': 'AFFF', 'short_summary': 'AFFF litigation summary'},
            {'mdl_num': '3019', 'litigation': 'Zantac', 'short_summary': 'Zantac litigation summary'}
        ])
    
    @pytest.fixture
    def sample_docket_data(self):
        return {
            'mdl_num': '2873',
            'docket_num': 'TEST123',
            'title': '',
            'allegations': ''
        }
    
    @pytest.mark.asyncio
    async def test_add_mdl_info_to_docket_success(self, processor, sample_docket_data, sample_mdl_df):
        """Test successful addition of MDL info to docket."""
        result = await processor.add_mdl_info_to_docket(sample_docket_data, sample_mdl_df)
        
        assert result is True
        assert sample_docket_data['title'] == 'AFFF'
        assert sample_docket_data['allegations'] == 'AFFF litigation summary'
    
    @pytest.mark.asyncio
    async def test_add_mdl_info_to_docket_no_match(self, processor, sample_mdl_df):
        """Test adding MDL info when no matching MDL found."""
        docket_data = {
            'mdl_num': '9999',  # Non-existent MDL
            'docket_num': 'TEST123',
            'title': '',
            'allegations': ''
        }
        
        result = await processor.add_mdl_info_to_docket(docket_data, sample_mdl_df)
        
        assert result is False
        assert docket_data['title'] == ''
        assert docket_data['allegations'] == ''
    
    @pytest.mark.asyncio
    async def test_add_mdl_info_to_docket_invalid_mdl_num(self, processor, sample_mdl_df):
        """Test adding MDL info with invalid MDL number."""
        docket_data = {
            'mdl_num': None,
            'docket_num': 'TEST123',
            'title': '',
            'allegations': ''
        }
        
        result = await processor.add_mdl_info_to_docket(docket_data, sample_mdl_df)
        
        assert result is False
        assert docket_data['title'] == ''
        assert docket_data['allegations'] == ''
    
    @pytest.mark.asyncio
    async def test_add_mdl_info_to_docket_empty_mdl_df(self, processor, sample_docket_data):
        """Test adding MDL info with empty MDL DataFrame."""
        empty_df = pd.DataFrame()
        
        result = await processor.add_mdl_info_to_docket(sample_docket_data, empty_df)
        
        assert result is False
        assert sample_docket_data['title'] == ''
        assert sample_docket_data['allegations'] == ''
    
    @pytest.mark.asyncio
    async def test_update_title_from_mdl_success(self, processor, sample_docket_data):
        """Test successful title update from MDL."""
        mdl_info = {'litigation': 'AFFF Litigation'}
        
        result = await processor._update_title_from_mdl(sample_docket_data, mdl_info, '2873')
        
        assert result is True
        assert sample_docket_data['title'] == 'AFFF Litigation'
    
    @pytest.mark.asyncio
    async def test_update_title_from_mdl_no_change(self, processor):
        """Test title update when no change needed."""
        docket_data = {'title': 'Existing Title'}
        mdl_info = {'litigation': 'Existing Title'}
        
        result = await processor._update_title_from_mdl(docket_data, mdl_info, '2873')
        
        assert result is True  # Same title, but considered successful
        assert docket_data['title'] == 'Existing Title'
    
    @pytest.mark.asyncio
    async def test_update_title_from_mdl_invalid_litigation(self, processor, sample_docket_data):
        """Test title update with invalid litigation name."""
        mdl_info = {'litigation': ''}
        
        result = await processor._update_title_from_mdl(sample_docket_data, mdl_info, '2873')
        
        assert result is False
    
    @pytest.mark.asyncio
    async def test_update_allegations_from_mdl_short_summary(self, processor, sample_docket_data):
        """Test allegations update from short_summary."""
        mdl_info = {
            'short_summary': 'Short summary content',
            'description': 'Description content',
            'summary': 'Summary content'
        }
        
        result = await processor._update_allegations_from_mdl(sample_docket_data, mdl_info, '2873')
        
        assert result is True
        assert sample_docket_data['allegations'] == 'Short summary content'
    
    @pytest.mark.asyncio
    async def test_update_allegations_from_mdl_description_fallback(self, processor, sample_docket_data):
        """Test allegations update falling back to description."""
        mdl_info = {
            'short_summary': '',
            'description': 'Description content',
            'summary': 'Summary content'
        }
        
        result = await processor._update_allegations_from_mdl(sample_docket_data, mdl_info, '2873')
        
        assert result is True
        assert sample_docket_data['allegations'] == 'Description content'
    
    @pytest.mark.asyncio
    async def test_update_allegations_from_mdl_summary_fallback(self, processor, sample_docket_data):
        """Test allegations update falling back to summary."""
        mdl_info = {
            'short_summary': '',
            'description': '',
            'summary': 'Summary content'
        }
        
        result = await processor._update_allegations_from_mdl(sample_docket_data, mdl_info, '2873')
        
        assert result is True
        assert sample_docket_data['allegations'] == 'Summary content'
    
    @pytest.mark.asyncio
    async def test_update_allegations_from_mdl_no_content(self, processor, sample_docket_data):
        """Test allegations update with no valid content."""
        mdl_info = {
            'short_summary': '',
            'description': '',
            'summary': ''
        }
        
        result = await processor._update_allegations_from_mdl(sample_docket_data, mdl_info, '2873')
        
        assert result is False
    
    @pytest.mark.asyncio
    async def test_filter_mdl_docket_patterns_success(self, processor):
        """Test filtering MDL docket patterns."""
        json_paths = [
            '/test/normal_docket.json',
            '/test/mdl_2873_transfer.json',
            '/test/MDL3019_motion.json',
            '/test/another_normal.json'
        ]
        
        filtered = await processor.filter_mdl_docket_patterns(json_paths)
        
        # Should filter out MDL pattern files
        assert len(filtered) == 2
        assert '/test/normal_docket.json' in filtered
        assert '/test/another_normal.json' in filtered
    
    @pytest.mark.asyncio
    async def test_filter_mdl_docket_patterns_empty_list(self, processor):
        """Test filtering with empty list."""
        filtered = await processor.filter_mdl_docket_patterns([])
        
        assert filtered == []
    
    @pytest.mark.asyncio
    async def test_filter_mdl_docket_patterns_all_mdl(self, processor):
        """Test filtering when all files are MDL patterns."""
        json_paths = [
            '/test/mdl_2873_transfer.json',
            '/test/MDL3019_motion.json'
        ]
        
        filtered = await processor.filter_mdl_docket_patterns(json_paths)
        
        assert filtered == []
    
    def test_is_mdl_pattern_file_true(self, processor):
        """Test MDL pattern detection returns True."""
        test_cases = [
            'mdl_2873_transfer.json',
            'MDL3019_motion.json',
            'MDL_1234_order.json',
            'mdl-5678-transfer.json'
        ]
        
        for filename in test_cases:
            assert processor._is_mdl_pattern_file(filename) is True
    
    def test_is_mdl_pattern_file_false(self, processor):
        """Test MDL pattern detection returns False."""
        test_cases = [
            'normal_docket.json',
            'smith_v_jones.json',
            'transfer_order.json',
            'class_action.json'
        ]
        
        for filename in test_cases:
            assert processor._is_mdl_pattern_file(filename) is False
    
    def test_validate_mdl_processing_data_success(self, processor):
        """Test successful MDL processing data validation."""
        valid_data = {
            'mdl_num': '2873',
            'title': 'AFFF Litigation',
            'allegations': 'Test allegations'
        }
        
        report = processor.validate_mdl_processing_data(valid_data)
        
        assert report['status'] == 'success'
        assert report['has_mdl_num'] is True
        assert report['has_title'] is True
        assert report['has_allegations'] is True
    
    def test_validate_mdl_processing_data_missing_fields(self, processor):
        """Test validation with missing fields."""
        incomplete_data = {
            'mdl_num': '2873'
            # Missing title and allegations
        }
        
        report = processor.validate_mdl_processing_data(incomplete_data)
        
        assert report['status'] == 'warning'
        assert report['has_mdl_num'] is True
        assert report['has_title'] is False
        assert report['has_allegations'] is False
    
    def test_validate_mdl_processing_data_no_mdl_num(self, processor):
        """Test validation without MDL number."""
        data_no_mdl = {
            'title': 'Some Title',
            'allegations': 'Some allegations'
        }
        
        report = processor.validate_mdl_processing_data(data_no_mdl)
        
        assert report['status'] == 'warning'
        assert report['has_mdl_num'] is False
    
    def test_validate_mdl_processing_data_empty(self, processor):
        """Test validation with empty data."""
        report = processor.validate_mdl_processing_data({})
        
        assert report['status'] == 'warning'
        assert report['has_mdl_num'] is False
        assert report['has_title'] is False
        assert report['has_allegations'] is False
    
    @pytest.mark.asyncio
    async def test_validate_docket_mdl_data_success(self, processor):
        """Test successful docket MDL data validation."""
        valid_data = {
            'mdl_num': '2873',
            'docket_num': 'TEST123',
            'title': 'AFFF Litigation',
            'allegations': 'Test allegations'
        }
        
        report = await processor.validate_docket_mdl_data(valid_data)
        
        assert report['status'] == 'success'
        assert report['mdl_info_complete'] is True
    
    @pytest.mark.asyncio
    async def test_validate_docket_mdl_data_incomplete(self, processor):
        """Test validation with incomplete docket MDL data."""
        incomplete_data = {
            'mdl_num': '2873',
            'docket_num': 'TEST123'
            # Missing title and allegations
        }
        
        report = await processor.validate_docket_mdl_data(incomplete_data)
        
        assert report['status'] == 'warning'
        assert report['mdl_info_complete'] is False
    
    @pytest.mark.asyncio
    async def test_validate_docket_mdl_data_no_docket_num(self, processor):
        """Test validation without docket number."""
        data = {
            'mdl_num': '2873',
            'title': 'AFFF Litigation'
        }
        
        report = await processor.validate_docket_mdl_data(data)
        
        assert report['status'] == 'warning'
        assert 'No docket number' in report['warnings'][0]
    
    def test_get_mdl_processing_summary_complete(self, processor):
        """Test processing summary for complete data."""
        complete_data = {
            'mdl_num': '2873',
            'title': 'AFFF Litigation',
            'allegations': 'Test allegations',
            'docket_num': 'TEST123'
        }
        
        summary = processor.get_mdl_processing_summary(complete_data)
        
        assert 'MDL 2873' in summary
        assert 'TEST123' in summary
        assert 'complete' in summary
    
    def test_get_mdl_processing_summary_incomplete(self, processor):
        """Test processing summary for incomplete data."""
        incomplete_data = {
            'mdl_num': '2873',
            'docket_num': 'TEST123'
        }
        
        summary = processor.get_mdl_processing_summary(incomplete_data)
        
        assert 'MDL 2873' in summary
        assert 'incomplete' in summary
    
    def test_get_mdl_processing_summary_no_mdl(self, processor):
        """Test processing summary without MDL number."""
        data = {
            'docket_num': 'TEST123',
            'title': 'Some Title'
        }
        
        summary = processor.get_mdl_processing_summary(data)
        
        assert summary == "No MDL processing data available"
    
    def test_clean_mdl_number_string(self, processor):
        """Test MDL number cleaning with string input."""
        result = processor._clean_mdl_number('2873')
        assert result == '2873'
    
    def test_clean_mdl_number_float(self, processor):
        """Test MDL number cleaning with float input."""
        result = processor._clean_mdl_number('2873.0')
        assert result == '2873'
    
    def test_clean_mdl_number_with_leading_zeros(self, processor):
        """Test MDL number cleaning with leading zeros."""
        result = processor._clean_mdl_number('002873')
        assert result == '2873'
    
    def test_clean_mdl_number_empty_after_cleaning(self, processor):
        """Test MDL number that becomes empty after cleaning."""
        result = processor._clean_mdl_number('0')
        assert result == ''
        
        result = processor._clean_mdl_number('0.0')
        assert result == ''
    
    def test_is_valid_mdl_content_valid(self, processor):
        """Test validation of valid MDL content."""
        assert processor._is_valid_mdl_content('Valid content') is True
        assert processor._is_valid_mdl_content('   Valid content   ') is True
    
    def test_is_valid_mdl_content_invalid(self, processor):
        """Test validation of invalid MDL content."""
        assert processor._is_valid_mdl_content('') is False
        assert processor._is_valid_mdl_content('   ') is False
        assert processor._is_valid_mdl_content(None) is False
        assert processor._is_valid_mdl_content('NA') is False
        assert processor._is_valid_mdl_content('Pro Se') is False
    
    def test_get_allegations_source_field_priority(self, processor):
        """Test allegations source field priority."""
        mdl_info = {
            'short_summary': 'Short summary',
            'description': 'Description',
            'summary': 'Summary'
        }
        
        content, field = processor._get_allegations_source_field(mdl_info)
        
        # Should prioritize short_summary
        assert content == 'Short summary'
        assert field == 'short_summary'
    
    def test_get_allegations_source_field_fallback(self, processor):
        """Test allegations source field fallback."""
        mdl_info = {
            'short_summary': '',
            'description': 'Description content',
            'summary': 'Summary content'
        }
        
        content, field = processor._get_allegations_source_field(mdl_info)
        
        # Should fall back to description
        assert content == 'Description content'
        assert field == 'description'
    
    def test_get_allegations_source_field_none(self, processor):
        """Test allegations source field when none available."""
        mdl_info = {
            'short_summary': '',
            'description': '',
            'summary': ''
        }
        
        content, field = processor._get_allegations_source_field(mdl_info)
        
        assert content is None
        assert field == "None"