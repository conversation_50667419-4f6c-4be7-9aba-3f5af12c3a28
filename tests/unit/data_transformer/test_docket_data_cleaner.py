"""
Unit tests for DocketDataCleaner component.
"""
import pytest
from datetime import datetime
from src.data_transformer.docket_data_cleaner import DocketDataCleaner


class TestDocketDataCleaner:
    """Test suite for DocketDataCleaner component."""
    
    @pytest.fixture
    def cleaner(self):
        return DocketDataCleaner()
    
    def test_process_filing_date_valid_date(self, cleaner):
        """Test processing of valid filing date."""
        data = {'date_filed': '12/15/2023'}
        cleaner.process_filing_date(data)
        
        assert data['date_filed'] == '20231215'
        assert data['filing_date'] == '20231215'
    
    def test_process_filing_date_already_yyyymmdd(self, cleaner):
        """Test processing of date already in YYYYMMDD format."""
        data = {'date_filed': '20231215'}
        cleaner.process_filing_date(data)
        
        assert data['date_filed'] == '20231215'
        assert data['filing_date'] == '20231215'
    
    def test_process_filing_date_invalid_date(self, cleaner):
        """Test processing of invalid date."""
        data = {'date_filed': 'invalid-date'}
        cleaner.process_filing_date(data)
        
        assert data['date_filed'] == 'invalid-date'
        assert data['filing_date'] == 'invalid-date'
    
    def test_process_filing_date_missing_date(self, cleaner):
        """Test processing when date is missing."""
        data = {}
        cleaner.process_filing_date(data)
        
        assert data['date_filed'] == 'NA'
        assert data['filing_date'] == 'NA'
    
    def test_clean_and_flatten_plaintiff_list(self, cleaner):
        """Test cleaning and deduplication of plaintiff list."""
        data = {
            'plaintiff': ['John Doe', 'Jane Smith', 'John Doe', ''],
            'defendant': ['Company A', 'Company B']
        }
        cleaner.clean_and_flatten(data)
        
        assert data['plaintiff'] == ['John Doe', 'Jane Smith']
        assert data['defendant'] == ['Company A', 'Company B']
    
    def test_clean_and_flatten_with_case_info(self, cleaner):
        """Test flattening of case_info dictionary."""
        data = {
            'plaintiff': ['John Doe'],
            'case_info': {
                'nature_of_suit': 'Contract',
                'jurisdiction': 'Federal',
                'judge': 'Judge Smith'
            }
        }
        cleaner.clean_and_flatten(data)
        
        assert 'case_info' not in data
        assert data['nature_of_suit'] == 'Contract'
        assert data['jurisdiction'] == 'Federal'
        assert data['judge'] == 'Judge Smith'
    
    def test_clean_and_flatten_plaintiffs_gpt(self, cleaner):
        """Test deduplication of plaintiffs_gpt field."""
        data = {
            'plaintiffs_gpt': ['John Doe', 'Jane Smith', 'John Doe', None, '']
        }
        cleaner.clean_and_flatten(data)
        
        assert data['plaintiffs_gpt'] == ['John Doe', 'Jane Smith']
    
    def test_normalize_defendant_name(self, cleaner):
        """Test defendant name normalization."""
        normalized = cleaner._normalize_defendant_name('Company ABC Inc.')
        assert normalized == 'company abc'
        
        normalized = cleaner._normalize_defendant_name('XYZ Corporation')
        assert normalized == 'xyz'
    
    def test_validate_required_fields_valid(self, cleaner):
        """Test validation with all required fields present."""
        data = {
            'docket_num': '12345',
            'court_id': 'cacd',
            'date_filed': '20231215',
            'plaintiff': ['John Doe'],
            'defendant': ['Company A']
        }
        
        errors = cleaner.validate_required_fields(data)
        assert len(errors) == 0
    
    def test_validate_required_fields_missing(self, cleaner):
        """Test validation with missing required fields."""
        data = {
            'plaintiff': ['John Doe']
        }
        
        errors = cleaner.validate_required_fields(data)
        assert len(errors) >= 2  # Missing docket_num and court_id
        assert any('docket_num' in error for error in errors)
        assert any('court_id' in error for error in errors)
    
    def test_normalize_field_names(self, cleaner):
        """Test field name normalization."""
        data = {
            'CourtId': 'cacd',
            'DocketNum': '12345',
            'DateFiled': '12/15/2023'
        }
        
        cleaner.normalize_field_names(data)
        
        assert 'CourtId' not in data
        assert 'DocketNum' not in data
        assert 'DateFiled' not in data
        assert data['court_id'] == 'cacd'
        assert data['docket_num'] == '12345'
        assert data['date_filed'] == '12/15/2023'
    
    def test_clean_text_fields(self, cleaner):
        """Test cleaning of text fields."""
        data = {
            'case_title': '  Multiple   spaces  here  ',
            'allegations': 'Normal text',
            'summary': ''
        }
        
        cleaner.clean_text_fields(data)
        
        assert data['case_title'] == 'Multiple spaces here'
        assert data['allegations'] == 'Normal text'
        assert data['summary'] is None
    
    def test_clear_processing_errors(self, cleaner):
        """Test clearing of processing error fields."""
        data = {
            'processing_errors': 'Some error',
            'processing_status': 'failed',
            'last_error_date': '2023-12-15',
            'docket_num': '12345'
        }
        
        cleaner.clear_processing_errors(data)
        
        assert 'processing_errors' not in data
        assert 'processing_status' not in data
        assert 'last_error_date' not in data
        assert data['docket_num'] == '12345'  # Other fields preserved
    
    def test_format_date_patterns(self, cleaner):
        """Test various date format patterns."""
        test_cases = [
            ('12/15/23', '20231215'),
            ('12/15/2023', '20231215'),
            ('2023-12-15', '20231215'),
            ('December 15, 2023', '20231215'),
            ('Dec 15, 2023', '20231215'),
            ('12-15-2023', '20231215'),
            ('12-15-23', '20231215')
        ]
        
        for input_date, expected in test_cases:
            result = cleaner._format_date_to_yyyymmdd(input_date)
            assert result == expected, f"Failed for input: {input_date}"
    
    def test_clean_party_list_various_types(self, cleaner):
        """Test cleaning of party lists with various input types."""
        # Test with list input
        result = cleaner._clean_party_list(['John Doe', 'Jane Smith', ''], 'plaintiff')
        assert result == ['John Doe', 'Jane Smith']
        
        # Test with string input
        result = cleaner._clean_party_list('John Doe', 'plaintiff')
        assert result == ['John Doe']
        
        # Test with empty input
        result = cleaner._clean_party_list(None, 'plaintiff')
        assert result == []
        
        # Test with non-string/non-list input
        result = cleaner._clean_party_list(123, 'plaintiff')
        assert result == []