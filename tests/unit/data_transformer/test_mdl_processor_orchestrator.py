"""
Unit tests for the refactored MDLProcessor orchestrator.
"""
import pytest
from unittest.mock import Mock, AsyncMock, patch
import pandas as pd
from src.data_transformer.mdl_processor import MDLProcessor


class TestMDLProcessorOrchestrator:
    """Test suite for MDLProcessor orchestrator functionality."""
    
    @pytest.fixture
    def mock_dependencies(self):
        """Create mock dependencies for MDLProcessor."""
        return {
            'mdl_litigations': pd.DataFrame([
                {'mdl_num': '2873', 'litigation': 'AFFF', 'short_summary': 'AFFF summary'}
            ]),
            'mdl_path': '/test/mdl_data.json',
            'file_handler': Mock(),
            'gpt': Mock(),
            'config': {'test': True},
            'litigation_classifier': Mock(),
            'pdf_cache': <PERSON>ck(),
            'download_dir': '/test/downloads',
            'district_court_db': Mock()
        }
    
    @pytest.fixture
    def processor(self, mock_dependencies):
        """Create MDLProcessor instance with mocked components."""
        with patch('src.data_transformer.mdl_processor.MDLLookupManager'), \
             patch('src.data_transformer.mdl_processor.AfffCalculator'), \
             patch('src.data_transformer.mdl_processor.MDLDescriptionManager'), \
             patch('src.data_transformer.mdl_processor.MDLPersistenceManager'), \
             patch('src.data_transformer.mdl_processor.MDLDataProcessor'):
            
            return MDLProcessor(**mock_dependencies)
    
    def test_initialization_success(self, mock_dependencies):
        """Test successful initialization of MDLProcessor with all components."""
        with patch('src.data_transformer.mdl_processor.MDLLookupManager') as mock_lookup, \
             patch('src.data_transformer.mdl_processor.AfffCalculator') as mock_afff, \
             patch('src.data_transformer.mdl_processor.MDLDescriptionManager') as mock_desc, \
             patch('src.data_transformer.mdl_processor.MDLPersistenceManager') as mock_persist, \
             patch('src.data_transformer.mdl_processor.MDLDataProcessor') as mock_data:
            
            processor = MDLProcessor(**mock_dependencies)
            
            # Verify all components were initialized
            mock_lookup.assert_called_once()
            mock_afff.assert_called_once()
            mock_desc.assert_called_once()
            mock_persist.assert_called_once()
            mock_data.assert_called_once()
            
            # Verify component attributes exist
            assert hasattr(processor, 'lookup_manager')
            assert hasattr(processor, 'afff_calculator')
            assert hasattr(processor, 'description_manager')
            assert hasattr(processor, 'persistence_manager')
            assert hasattr(processor, 'data_processor')
    
    def test_initialization_failure(self, mock_dependencies):
        """Test initialization failure handling."""
        with patch('src.data_transformer.mdl_processor.MDLLookupManager') as mock_lookup:
            mock_lookup.side_effect = Exception("Initialization failed")
            
            with pytest.raises(Exception, match="Initialization failed"):
                MDLProcessor(**mock_dependencies)
    
    @pytest.mark.asyncio
    async def test_load_mdl_litigation_async_delegation(self, processor):
        """Test that async loading delegates to persistence manager."""
        processor.persistence_manager.load_mdl_litigation_async = AsyncMock(
            return_value=pd.DataFrame([{'mdl_num': '2873', 'litigation': 'AFFF'}])
        )
        
        result = await processor._load_mdl_litigation_async()
        
        assert len(result) == 1
        processor.persistence_manager.load_mdl_litigation_async.assert_called_once()
        # Verify internal reference was updated
        assert processor.mdl_litigations.equals(result)
    
    @pytest.mark.asyncio
    async def test_create_mdl_lookup_async_delegation(self, processor):
        """Test that async lookup creation delegates to lookup manager."""
        processor.lookup_manager.get_mdl_lookup_async = AsyncMock(
            return_value={'2873': 'scd', '3019': 'njd'}
        )
        
        result = await processor._create_mdl_lookup_async()
        
        assert result == {'2873': 'scd', '3019': 'njd'}
        processor.lookup_manager.get_mdl_lookup_async.assert_called_once()
        # Verify backward compatibility cache was updated
        assert processor._mdl_lookup_cache == result
        assert processor._mdl_lookup_loaded is True
    
    @pytest.mark.asyncio
    async def test_calculate_afff_num_plaintiffs_async_delegation(self, processor):
        """Test that AFFF calculation delegates to AFFF calculator."""
        processor.afff_calculator.calculate_afff_num_plaintiffs = AsyncMock(return_value='3')
        
        data = {'mdl_num': '2873', 'plaintiff': ['John', 'Jane', 'Bob']}
        result = await processor.calculate_afff_num_plaintiffs_async(data)
        
        assert result == '3'
        processor.afff_calculator.calculate_afff_num_plaintiffs.assert_called_once_with(data)
    
    @pytest.mark.asyncio
    async def test_manual_match_title_to_mdl_litigation_async_delegation(self, processor):
        """Test that title matching delegates to description manager."""
        processor.data_processor.filter_mdl_docket_patterns = AsyncMock(
            return_value=['/test/file1.json']
        )
        processor.description_manager.match_titles_to_mdl_litigation = AsyncMock(
            return_value=(processor.mdl_litigations, 1)
        )
        processor.save_mdl_litigation_async = AsyncMock(return_value=True)
        
        with patch.object(processor, '_get_json_file_paths') as mock_get_paths:
            mock_get_paths.return_value = ['/test/file1.json', '/test/file2.json']
            
            await processor.manual_match_title_to_mdl_litigation_async()
            
            processor.data_processor.filter_mdl_docket_patterns.assert_called_once()
            processor.description_manager.match_titles_to_mdl_litigation.assert_called_once()
            processor.save_mdl_litigation_async.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_update_mdl_description_async_delegation(self, processor):
        """Test that description update delegates to description manager."""
        processor.description_manager.update_mdl_descriptions_from_dockets = AsyncMock(
            return_value=(processor.mdl_litigations, 2)
        )
        processor.save_mdl_litigation_async = AsyncMock(return_value=True)
        
        await processor.update_mdl_description_async()
        
        processor.description_manager.update_mdl_descriptions_from_dockets.assert_called_once()
        processor.save_mdl_litigation_async.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_summarize_description_async_delegation(self, processor):
        """Test that description summarization delegates to description manager."""
        processor.description_manager.summarize_existing_descriptions = AsyncMock(
            return_value=(processor.mdl_litigations, 1)
        )
        processor.save_mdl_litigation_async = AsyncMock(return_value=True)
        
        await processor.summarize_description_async()
        
        processor.description_manager.summarize_existing_descriptions.assert_called_once()
        processor.save_mdl_litigation_async.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_save_mdl_litigation_async_delegation(self, processor):
        """Test that async saving delegates to persistence manager."""
        processor.persistence_manager.save_mdl_litigation_async = AsyncMock(return_value=True)
        
        result = await processor.save_mdl_litigation_async()
        
        assert result is True
        processor.persistence_manager.save_mdl_litigation_async.assert_called_once_with(
            processor.mdl_litigations
        )
    
    @pytest.mark.asyncio
    async def test_update_and_save_mdl_litigations_async_delegation(self, processor):
        """Test that update and save delegates to persistence manager."""
        updated_df = pd.DataFrame([{'mdl_num': '2873', 'litigation': 'Updated AFFF'}])
        processor.persistence_manager.update_and_save_mdl_entry = AsyncMock(
            return_value=(updated_df, True)
        )
        
        result = await processor.update_and_save_mdl_litigations_async(
            '2873', 'Updated AFFF', {'description': 'New description'}
        )
        
        assert result is True
        assert processor.mdl_litigations.equals(updated_df)
        processor.persistence_manager.update_and_save_mdl_entry.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_add_mdl_info_async_delegation(self, processor):
        """Test that MDL info addition delegates to data processor."""
        processor.data_processor.add_mdl_info_to_docket = AsyncMock(return_value=True)
        
        data = {'mdl_num': '2873', 'docket_num': 'TEST123'}
        result = await processor.add_mdl_info_async(data)
        
        assert result is True
        processor.data_processor.add_mdl_info_to_docket.assert_called_once_with(
            data, processor.mdl_litigations
        )
    
    def test_validate_processing_results_comprehensive(self, processor):
        """Test comprehensive validation using all components."""
        # Mock component validation methods
        processor.data_processor.validate_mdl_processing_data = Mock(
            return_value={'status': 'success', 'message': 'Data processor validation passed'}
        )
        processor.afff_calculator.validate_afff_data = AsyncMock(
            return_value={'status': 'success', 'message': 'AFFF validation passed'}
        )
        processor.data_processor.validate_docket_mdl_data = AsyncMock(
            return_value={'status': 'success', 'message': 'Docket validation passed'}
        )
        
        data = {'mdl_num': '2873', 'title': 'AFFF', 'allegations': 'Test'}
        
        with patch.object(processor, 'get_processing_summary') as mock_summary:
            mock_summary.return_value = 'Test summary'
            
            report = processor.validate_processing_results(data)
            
            assert report['overall_status'] == 'success'
            assert len(report['component_results']) == 3
            assert report['summary'] == 'Test summary'
    
    def test_validate_processing_results_with_errors(self, processor):
        """Test validation when components report errors."""
        # Mock one component with error
        processor.data_processor.validate_mdl_processing_data = Mock(
            return_value={'status': 'error', 'message': 'Data validation failed'}
        )
        processor.afff_calculator.validate_afff_data = AsyncMock(
            return_value={'status': 'success', 'message': 'AFFF validation passed'}
        )
        processor.data_processor.validate_docket_mdl_data = AsyncMock(
            return_value={'status': 'warning', 'message': 'Docket validation warning'}
        )
        
        data = {'mdl_num': '2873'}
        
        with patch.object(processor, 'get_processing_summary') as mock_summary:
            mock_summary.return_value = 'Test summary'
            
            report = processor.validate_processing_results(data)
            
            assert report['overall_status'] == 'error'  # Error takes precedence
    
    def test_get_processing_summary_comprehensive(self, processor):
        """Test comprehensive processing summary from all components."""
        # Mock component summary methods
        processor.data_processor.get_mdl_processing_summary = Mock(
            return_value='MDL 2873 processed'
        )
        processor.afff_calculator.get_afff_summary = Mock(
            return_value='AFFF case TEST123: 3 plaintiffs'
        )
        processor.description_manager.get_description_summary = Mock(
            return_value='2 MDL entries with descriptions'
        )
        
        data = {'mdl_num': '2873', 'docket_num': 'TEST123'}
        
        summary = processor.get_processing_summary(data)
        
        expected_parts = [
            'MDL 2873 processed',
            'AFFF case TEST123: 3 plaintiffs',
            '2 MDL entries with descriptions'
        ]
        assert all(part in summary for part in expected_parts)
        assert ' | ' in summary  # Parts should be joined
    
    def test_get_component_summary(self, processor):
        """Test component summary generation."""
        summary = processor.get_component_summary()
        
        assert 'MDLProcessor with 5 components' in summary
        assert 'lookup_manager' in summary
        assert 'afff_calculator' in summary
        assert 'description_manager' in summary
        assert 'persistence_manager' in summary
        assert 'data_processor' in summary
    
    def test_clear_lookup_cache(self, processor):
        """Test lookup cache clearing."""
        # Set cache values
        processor._mdl_lookup_cache = {'test': 'data'}
        processor._mdl_lookup_loaded = True
        processor.lookup_manager.clear_cache = Mock()
        
        processor._clear_lookup_cache()
        
        assert processor._mdl_lookup_cache is None
        assert processor._mdl_lookup_loaded is False
        processor.lookup_manager.clear_cache.assert_called_once()
    
    # Test backward compatibility methods (deprecated)
    
    def test_load_mdl_litigation_sync_deprecated(self, processor):
        """Test deprecated sync loading method."""
        processor.persistence_manager.load_mdl_litigation_sync = Mock(
            return_value=pd.DataFrame([{'mdl_num': '2873'}])
        )
        
        result = processor._load_mdl_litigation()
        
        assert len(result) == 1
        processor.persistence_manager.load_mdl_litigation_sync.assert_called_once()
    
    def test_create_mdl_lookup_sync_deprecated(self, processor):
        """Test deprecated sync lookup creation."""
        processor.lookup_manager.get_mdl_lookup_sync = Mock(
            return_value={'2873': 'scd'}
        )
        
        result = processor._create_mdl_lookup()
        
        assert result == {'2873': 'scd'}
        processor.lookup_manager.get_mdl_lookup_sync.assert_called_once()
    
    def test_calculate_afff_num_plaintiffs_sync_deprecated(self, processor):
        """Test deprecated sync AFFF calculation."""
        processor.afff_calculator.calculate_afff_num_plaintiffs_sync = Mock(return_value='3')
        
        data = {'mdl_num': '2873', 'plaintiff': ['John', 'Jane', 'Bob']}
        result = processor.calculate_afff_num_plaintiffs(data)
        
        assert result == '3'
        processor.afff_calculator.calculate_afff_num_plaintiffs_sync.assert_called_once()
    
    def test_manual_match_title_sync_deprecated(self, processor):
        """Test deprecated sync title matching."""
        with patch('asyncio.run') as mock_run:
            processor.manual_match_title_to_mdl_litigation()
            
            mock_run.assert_called_once()
    
    def test_update_mdl_description_sync_deprecated(self, processor):
        """Test deprecated sync description update."""
        with patch('asyncio.run') as mock_run:
            processor.update_mdl_description()
            
            mock_run.assert_called_once()
    
    def test_summarize_description_sync_deprecated(self, processor):
        """Test deprecated sync description summarization."""
        with patch('asyncio.run') as mock_run:
            processor.summarize_description()
            
            mock_run.assert_called_once()
    
    def test_save_mdl_litigation_sync_deprecated(self, processor):
        """Test deprecated sync saving."""
        processor.persistence_manager.save_mdl_litigation_sync = Mock()
        
        processor.save_mdl_litigation()
        
        processor.persistence_manager.save_mdl_litigation_sync.assert_called_once()
    
    def test_update_and_save_sync_deprecated(self, processor):
        """Test deprecated sync update and save."""
        with patch('asyncio.run') as mock_run:
            mock_run.return_value = True
            
            result = processor.update_and_save_mdl_litigations(
                '2873', 'AFFF', {'description': 'Test'}
            )
            
            assert result is True
            mock_run.assert_called_once()
    
    def test_add_mdl_info_sync_deprecated(self, processor):
        """Test deprecated sync MDL info addition."""
        with patch('asyncio.run') as mock_run:
            data = {'mdl_num': '2873'}
            
            processor.add_mdl_info(data)
            
            mock_run.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_json_file_paths_success(self, processor):
        """Test getting JSON file paths."""
        with patch('os.path.isdir') as mock_isdir, \
             patch('os.listdir') as mock_listdir:
            
            mock_isdir.return_value = True
            mock_listdir.return_value = ['file1.json', 'file2.txt', 'file3.json']
            
            paths = await processor._get_json_file_paths()
            
            assert len(paths) == 2
            assert all(path.endswith('.json') for path in paths)
    
    @pytest.mark.asyncio
    async def test_get_json_file_paths_no_directory(self, processor):
        """Test getting JSON paths when directory doesn't exist."""
        processor.download_dir = None
        processor.file_handler.download_dir = None
        
        paths = await processor._get_json_file_paths()
        
        assert paths == []
    
    def test_legacy_match_title_to_mdl_num(self, processor):
        """Test legacy title matching method."""
        processor.description_manager.match_titles_to_mdl_litigation = AsyncMock(
            return_value=(processor.mdl_litigations, 1)
        )
        processor.save_mdl_litigation = Mock()
        
        with patch('asyncio.run') as mock_run:
            processor.match_title_to_mdl_num(['/test/file1.json'])
            
            mock_run.assert_called_once()