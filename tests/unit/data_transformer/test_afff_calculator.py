"""
Unit tests for AfffCalculator component.
"""
import pytest
from unittest.mock import Mock
from src.data_transformer.afff_calculator import AfffCalculator


class TestAfffCalculator:
    """Test suite for AfffCalculator component."""
    
    @pytest.fixture
    def calculator(self):
        return AfffCalculator(config={'test': True})
    
    @pytest.mark.asyncio
    async def test_calculate_afff_num_plaintiffs_async_success(self, calculator):
        """Test successful AFFF plaintiff calculation."""
        data = {
            'mdl_num': '2873',
            'plaintiff': ['<PERSON>', '<PERSON>', '<PERSON>']
        }
        
        result = await calculator.calculate_afff_num_plaintiffs(data)
        
        assert result == '3'
        assert data['num_plaintiffs'] == '3'
    
    @pytest.mark.asyncio
    async def test_calculate_afff_num_plaintiffs_async_string_list(self, calculator):
        """Test AFFF calculation with string representation of list."""
        data = {
            'mdl_num': '2873',
            'plaintiff': "['<PERSON>', '<PERSON>']"
        }
        
        result = await calculator.calculate_afff_num_plaintiffs(data)
        
        assert result == '2'
        assert data['num_plaintiffs'] == '2'
    
    @pytest.mark.asyncio
    async def test_calculate_afff_num_plaintiffs_async_single_string(self, calculator):
        """Test AFFF calculation with single plaintiff string."""
        data = {
            'mdl_num': '2873',
            'plaintiff': 'John Doe'
        }
        
        result = await calculator.calculate_afff_num_plaintiffs(data)
        
        assert result == '1'
        assert data['num_plaintiffs'] == '1'
    
    @pytest.mark.asyncio
    async def test_calculate_afff_num_plaintiffs_async_non_afff_case(self, calculator):
        """Test calculation for non-AFFF case."""
        data = {
            'mdl_num': '3019',
            'plaintiff': ['John Doe', 'Jane Smith']
        }
        
        result = await calculator.calculate_afff_num_plaintiffs(data)
        
        assert result == 'NA'
        assert 'num_plaintiffs' not in data
    
    @pytest.mark.asyncio
    async def test_calculate_afff_num_plaintiffs_async_with_nulls(self, calculator):
        """Test AFFF calculation filtering out null conditions."""
        data = {
            'mdl_num': '2873',
            'plaintiff': ['John Doe', '', 'NA', None, 'Jane Smith']
        }
        
        result = await calculator.calculate_afff_num_plaintiffs(data)
        
        assert result == '2'  # Only 'John Doe' and 'Jane Smith' are valid
        assert data['num_plaintiffs'] == '2'
    
    @pytest.mark.asyncio
    async def test_calculate_afff_num_plaintiffs_async_no_plaintiffs(self, calculator):
        """Test AFFF calculation with no valid plaintiffs."""
        data = {
            'mdl_num': '2873',
            'plaintiff': ['', 'NA', None]
        }
        
        result = await calculator.calculate_afff_num_plaintiffs(data)
        
        assert result == 'NA'
        assert data['num_plaintiffs'] == 'NA'
    
    @pytest.mark.asyncio
    async def test_calculate_afff_num_plaintiffs_async_none_data(self, calculator):
        """Test calculation with None data."""
        result = await calculator.calculate_afff_num_plaintiffs(None)
        
        assert result == 'NA'
    
    def test_calculate_afff_num_plaintiffs_sync_deprecated(self, calculator):
        """Test that sync method shows deprecation warning."""
        data = {
            'mdl_num': '2873',
            'plaintiff': ['John Doe', 'Jane Smith']
        }
        
        result = calculator.calculate_afff_num_plaintiffs_sync(data)
        
        assert result == '2'
        assert data['num_plaintiffs'] == '2'
    
    @pytest.mark.asyncio
    async def test_extract_plaintiffs_list_from_list(self, calculator):
        """Test extracting plaintiffs from list input."""
        plaintiffs_data = ['John Doe', 'Jane Smith']
        
        result = await calculator._extract_plaintiffs_list(plaintiffs_data)
        
        assert result == ['John Doe', 'Jane Smith']
    
    @pytest.mark.asyncio
    async def test_extract_plaintiffs_list_from_string(self, calculator):
        """Test extracting plaintiffs from string input."""
        plaintiffs_data = "['John Doe', 'Jane Smith']"
        
        result = await calculator._extract_plaintiffs_list(plaintiffs_data)
        
        assert result == ['John Doe', 'Jane Smith']
    
    @pytest.mark.asyncio
    async def test_extract_plaintiffs_list_invalid_string(self, calculator):
        """Test extracting plaintiffs from invalid string."""
        plaintiffs_data = "John Doe"  # Not a list format
        
        result = await calculator._extract_plaintiffs_list(plaintiffs_data)
        
        assert result == ['John Doe']  # Should treat as single plaintiff
    
    @pytest.mark.asyncio
    async def test_filter_valid_plaintiffs(self, calculator):
        """Test filtering valid plaintiffs."""
        plaintiffs = ['John Doe', '', 'NA', None, 'Jane Smith', 'Pro Se']
        
        result = await calculator._filter_valid_plaintiffs(plaintiffs)
        
        assert result == ['John Doe', 'Jane Smith']
    
    @pytest.mark.asyncio
    async def test_is_valid_plaintiff(self, calculator):
        """Test plaintiff validation."""
        assert await calculator._is_valid_plaintiff('John Doe') is True
        assert await calculator._is_valid_plaintiff('') is False
        assert await calculator._is_valid_plaintiff('NA') is False
        assert await calculator._is_valid_plaintiff(None) is False
        assert await calculator._is_valid_plaintiff('  ') is False
    
    @pytest.mark.asyncio
    async def test_validate_afff_data_success(self, calculator):
        """Test validation of valid AFFF data."""
        data = {
            'mdl_num': '2873',
            'plaintiff': ['John Doe', 'Jane Smith'],
            'num_plaintiffs': '2'
        }
        
        report = await calculator.validate_afff_data(data)
        
        assert report['status'] == 'success'
        assert report['is_afff_case'] is True
        assert report['plaintiff_count'] == 2
    
    @pytest.mark.asyncio
    async def test_validate_afff_data_non_afff(self, calculator):
        """Test validation of non-AFFF case."""
        data = {
            'mdl_num': '3019',
            'plaintiff': ['John Doe']
        }
        
        report = await calculator.validate_afff_data(data)
        
        assert report['is_afff_case'] is False
        assert 'Not an AFFF case' in report['validation_details']['message']
    
    @pytest.mark.asyncio
    async def test_validate_afff_data_no_plaintiffs(self, calculator):
        """Test validation of AFFF case with no plaintiffs."""
        data = {
            'mdl_num': '2873'
        }
        
        report = await calculator.validate_afff_data(data)
        
        assert report['status'] == 'success'  # Early return keeps status as success
        assert 'No plaintiff data found' in report['warnings'][0]
    
    @pytest.mark.asyncio
    async def test_validate_afff_data_inconsistent_count(self, calculator):
        """Test validation with inconsistent plaintiff count."""
        data = {
            'mdl_num': '2873',
            'plaintiff': ['John Doe', 'Jane Smith'],
            'num_plaintiffs': '3'  # Inconsistent with actual count
        }
        
        report = await calculator.validate_afff_data(data)
        
        assert report['status'] == 'warning'
        assert any('doesn\'t match calculated count' in warning for warning in report['warnings'])
    
    def test_get_afff_summary_afff_case(self, calculator):
        """Test summary for AFFF case."""
        data = {
            'mdl_num': '2873',
            'num_plaintiffs': '5',
            'docket_num': 'TEST123'
        }
        
        summary = calculator.get_afff_summary(data)
        
        assert summary == 'AFFF case TEST123: 5 plaintiffs'
    
    def test_get_afff_summary_non_afff_case(self, calculator):
        """Test summary for non-AFFF case."""
        data = {
            'mdl_num': '3019'
        }
        
        summary = calculator.get_afff_summary(data)
        
        assert summary == 'Not an AFFF case (MDL 3019)'