"""
Unit tests for Uploader async migration.

Tests the elimination of compatibility layer and direct async repository usage.
"""
import asyncio
import pytest
from unittest.mock import AsyncMock, MagicMock, patch

# Import the uploader we're testing
from src.data_transformer.uploader import Uploader


class TestUploaderAsyncMigration:
    """Test the async migration of Uploader class."""
    
    @pytest.fixture
    def mock_config(self):
        """Mock configuration for Uploader."""
        return {
            'bucket_name': 'test-bucket',
            'uploader_workers': 5,
            'aws_s3': {'bucket_name': 'fallback-bucket'}
        }
    
    @pytest.fixture
    def mock_s3_manager(self):
        """Mock S3AsyncStorage manager."""
        mock_s3 = AsyncMock()
        mock_s3.__aenter__ = AsyncMock(return_value=mock_s3)
        mock_s3.__aexit__ = AsyncMock(return_value=None)
        mock_s3.upload_file_async = AsyncMock(return_value=True)
        mock_s3.check_file_exists_async = AsyncMock(return_value=False)
        return mock_s3
    
    @pytest.fixture
    def mock_pacer_db(self):
        """Mock PacerRepository."""
        mock_pacer = AsyncMock()
        mock_pacer.save_batch = AsyncMock(return_value=True)
        return mock_pacer
    
    @pytest.fixture
    def mock_file_handler(self):
        """Mock FileHandler."""
        mock_handler = MagicMock()
        mock_handler.load_json_async = AsyncMock(return_value={'test': 'data'})
        return mock_handler
    
    @pytest.fixture
    def uploader(self, mock_config, mock_s3_manager, mock_pacer_db, mock_file_handler):
        """Create Uploader instance with mocked dependencies."""
        return Uploader(mock_config, mock_s3_manager, mock_pacer_db, mock_file_handler)
    
    def test_no_compatibility_layer_imports(self, uploader):
        """Test that no compatibility layer code remains."""
        assert not hasattr(uploader, '_using_new_architecture')
        assert not hasattr(uploader, 'create_manager_replacement')
        
        # Verify the class doesn't reference compatibility layer methods
        import inspect
        source = inspect.getsource(Uploader)
        
        compatibility_patterns = [
            'create_manager_replacement',
            'src.migration',
            '_using_new_architecture',
            'TYPE_CHECKING'
        ]
        
        for pattern in compatibility_patterns:
            assert pattern not in source, f"Found compatibility layer reference: {pattern}"
    
    def test_direct_repository_types(self, uploader):
        """Test that repositories are directly typed without string quotes."""
        from src.repositories.pacer_repository import PacerRepository
        from src.infrastructure.storage.s3_async import S3AsyncStorage
        
        # Verify direct type usage (not string references)
        assert isinstance(uploader.pacer_db, (PacerRepository, AsyncMock))
        assert isinstance(uploader.s3_manager, (S3AsyncStorage, AsyncMock))
    
    @pytest.mark.asyncio
    async def test_batch_upload_performance(self, uploader, mock_s3_manager, mock_file_handler):
        """Test concurrent batch uploads for performance gains."""
        json_paths = [f"/tmp/test_{i}.json" for i in range(10)]
        upload_types = {'s3', 'dynamodb'}
        
        mock_file_handler.load_json_async.return_value = {
            'case_data': {'id': 'test_case'},
            'metadata': {'processed': True}
        }
        
        mock_s3_manager.check_file_exists_async.return_value = False
        mock_s3_manager.upload_file_async.return_value = True
        
        start_time = asyncio.get_event_loop().time()
        
        result = await uploader.upload_batch_to_aws_async(
            json_paths, upload_types, force_s3_upload=False
        )
        
        end_time = asyncio.get_event_loop().time()
        processing_time = end_time - start_time
        
        assert 'uploaded' in result
        assert 'skipped' in result
        assert 'failed' in result
        
        # Should be fast with mocks
        assert processing_time < 1.0
        
        mock_s3_manager.__aenter__.assert_called_once()
        mock_s3_manager.__aexit__.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_pre_initialized_s3_manager_handling(self, mock_config, mock_pacer_db, mock_file_handler):
        """Test handling of pre-initialized S3 manager (with _client attribute)."""
        # Create S3 manager with _client attribute to simulate pre-initialization
        mock_s3_manager = AsyncMock()
        mock_s3_manager._client = MagicMock()  # Simulate initialized state
        mock_s3_manager.__aenter__ = AsyncMock(return_value=mock_s3_manager)
        mock_s3_manager.__aexit__ = AsyncMock()
        mock_s3_manager.check_file_exists_async = AsyncMock(return_value=False)
        mock_s3_manager.upload_file_async = AsyncMock(return_value=True)
        
        uploader = Uploader(mock_config, mock_s3_manager, mock_pacer_db, mock_file_handler)
        
        json_paths = ["/tmp/test.json"]
        upload_types = {'s3'}
        
        mock_file_handler.load_json_async.return_value = {
            'case_data': {'id': 'test_case'},
            'metadata': {'processed': True}
        }
        
        result = await uploader.upload_batch_to_aws_async(
            json_paths, upload_types, force_s3_upload=False
        )
        
        assert 'uploaded' in result
        
        # Should NOT call __aenter__ for pre-initialized manager
        mock_s3_manager.__aenter__.assert_not_called()
        mock_s3_manager.__aexit__.assert_not_called()
        
        # But should still call upload methods directly
        mock_s3_manager.upload_file_async.assert_called()
    
    @pytest.mark.asyncio
    async def test_uninitialized_s3_manager_context_management(self, mock_config, mock_pacer_db, mock_file_handler):
        """Test context manager usage for uninitialized S3 manager (no _client attribute)."""
        # Create S3 manager without _client attribute to simulate uninitialized state
        mock_s3_manager = AsyncMock()
        # Note: No _client attribute set
        mock_s3_manager.__aenter__ = AsyncMock(return_value=mock_s3_manager)
        mock_s3_manager.__aexit__ = AsyncMock()
        mock_s3_manager.check_file_exists_async = AsyncMock(return_value=False)
        mock_s3_manager.upload_file_async = AsyncMock(return_value=True)
        
        uploader = Uploader(mock_config, mock_s3_manager, mock_pacer_db, mock_file_handler)
        
        json_paths = ["/tmp/test.json"]
        upload_types = {'s3'}
        
        mock_file_handler.load_json_async.return_value = {
            'case_data': {'id': 'test_case'},
            'metadata': {'processed': True}
        }
        
        result = await uploader.upload_batch_to_aws_async(
            json_paths, upload_types, force_s3_upload=False
        )
        
        assert 'uploaded' in result
        
        # Should call context manager methods for uninitialized manager
        mock_s3_manager.__aenter__.assert_called_once()
        mock_s3_manager.__aexit__.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_s3_manager_none_client_attribute(self, mock_config, mock_pacer_db, mock_file_handler):
        """Test handling when S3 manager has _client attribute but it's None."""
        # Create S3 manager with _client = None to simulate partial initialization
        mock_s3_manager = AsyncMock()
        mock_s3_manager._client = None  # Explicit None
        mock_s3_manager.__aenter__ = AsyncMock(return_value=mock_s3_manager)
        mock_s3_manager.__aexit__ = AsyncMock()
        mock_s3_manager.check_file_exists_async = AsyncMock(return_value=False)
        mock_s3_manager.upload_file_async = AsyncMock(return_value=True)
        
        uploader = Uploader(mock_config, mock_s3_manager, mock_pacer_db, mock_file_handler)
        
        json_paths = ["/tmp/test.json"]
        upload_types = {'s3'}
        
        mock_file_handler.load_json_async.return_value = {
            'case_data': {'id': 'test_case'},
            'metadata': {'processed': True}
        }
        
        result = await uploader.upload_batch_to_aws_async(
            json_paths, upload_types, force_s3_upload=False
        )
        
        assert 'uploaded' in result
        
        # Should use context manager since _client is None
        mock_s3_manager.__aenter__.assert_called_once()
        mock_s3_manager.__aexit__.assert_called_once()


if __name__ == "__main__":
    pytest.main([__file__, "-v"])