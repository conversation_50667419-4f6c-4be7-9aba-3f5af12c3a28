"""
Tests for FB ad upload control functionality in reports.
"""
import pytest
import asyncio
import pandas as pd
import tempfile
import os
from unittest.mock import AsyncMock, Mock, patch, MagicMock

from src.reports.config import ReportConfig
from src.reports.orchestrator import ReportsOrchestrator
from src.reports.ad_page_generator import AdPageGenerator


class TestFBAdsUploadControl:
    """Test suite for FB ads upload control functionality."""

    @pytest.fixture
    def temp_dir(self):
        """Create temporary directory for tests."""
        temp_dir = tempfile.mkdtemp()
        yield temp_dir

    @pytest.fixture
    def base_config_dict(self, temp_dir):
        """Base configuration dictionary."""
        return {
            'date': '01/15/24',
            'project_root': temp_dir,
            'bucket_name': 'test-bucket',
            'show_adspy': True
        }

    @pytest.fixture
    def config_generate_and_upload(self, base_config_dict):
        """Configuration with both generation and upload enabled."""
        base_config_dict['fb_ads'] = {
            'generate_ad_pages': True,
            'upload_ad_pages': True,
            'use_cache': True
        }
        return ReportConfig(base_config_dict)

    @pytest.fixture
    def config_generate_only(self, base_config_dict):
        """Configuration with generation enabled but upload disabled."""
        base_config_dict['fb_ads'] = {
            'generate_ad_pages': True,
            'upload_ad_pages': False,
            'use_cache': True
        }
        return ReportConfig(base_config_dict)

    @pytest.fixture
    def config_no_generation(self, base_config_dict):
        """Configuration with generation disabled."""
        base_config_dict['fb_ads'] = {
            'generate_ad_pages': False,
            'upload_ad_pages': True,
            'use_cache': True
        }
        return ReportConfig(base_config_dict)

    @pytest.fixture
    def sample_ad_dataframe(self):
        """Sample FB ad DataFrame for testing."""
        return pd.DataFrame([
            {
                'ad_archive_id': 'test-ad-001',
                'law_firm': 'Test Law Firm',
                'title': 'Test Ad Title',
                'body': 'Test ad content',
                'start_date': '2024-01-15',
                'end_date': '2024-01-20',
                'page_id': 'test-page-001',
                'ad_creative_id': 'test-creative-001',
                'publisher_platform': 'Facebook',
                'link_url': 'https://example.com'
            }
        ])

    @pytest.fixture
    def mock_storage(self):
        """Mock async storage."""
        storage = AsyncMock()
        storage.__aenter__ = AsyncMock(return_value=storage)
        storage.__aexit__ = AsyncMock()
        return storage

    @pytest.fixture
    def mock_s3_manager(self):
        """Mock S3 manager."""
        s3_manager = AsyncMock()
        s3_manager._initialize = AsyncMock()
        s3_manager._client = Mock()  # Simulate initialized client
        s3_manager.file_exists = AsyncMock(return_value=True)
        s3_manager.upload_file = AsyncMock(return_value=True)
        s3_manager.create_folder = AsyncMock(return_value=True)
        return s3_manager

    @pytest.fixture
    def mock_renderer(self):
        """Mock renderer."""
        renderer = Mock()
        template_mock = Mock()
        template_mock.render.return_value = '<html>Test Ad Page</html>'
        renderer.env.get_template.return_value = template_mock
        return renderer

    def test_report_config_upload_control_properties(self, config_generate_and_upload, config_generate_only, config_no_generation):
        """Test that ReportConfig properly loads upload control configuration."""
        # Test generate and upload enabled
        assert config_generate_and_upload.fb_ads_generate_pages is True
        assert config_generate_and_upload.fb_ads_upload_pages is True
        
        # Test generate only
        assert config_generate_only.fb_ads_generate_pages is True
        assert config_generate_only.fb_ads_upload_pages is False
        
        # Test no generation
        assert config_no_generation.fb_ads_generate_pages is False
        assert config_no_generation.fb_ads_upload_pages is True  # Upload flag can be true but won't matter

    async def test_ad_page_generator_generate_and_upload(self, config_generate_and_upload, mock_s3_manager, mock_renderer, sample_ad_dataframe):
        """Test AdPageGenerator.generate_and_upload_ad_pages method."""
        generator = AdPageGenerator(config_generate_and_upload, mock_renderer, mock_s3_manager)
        
        # Mock the internal methods
        with patch.object(generator, '_create_s3_folders', return_value=True):
            with patch.object(generator, '_upload_ad_task') as mock_upload:
                mock_upload.return_value = ('test-ad-001', True, None)
                
                await generator.generate_and_upload_ad_pages(sample_ad_dataframe)
                
                # Verify S3 initialization was called
                mock_s3_manager._initialize.assert_called_once()
                
                # Verify upload task was called
                mock_upload.assert_called_once()

    async def test_ad_page_generator_generate_only(self, config_generate_only, mock_s3_manager, mock_renderer, sample_ad_dataframe):
        """Test AdPageGenerator.generate_ad_pages_only method."""
        generator = AdPageGenerator(config_generate_only, mock_renderer, mock_s3_manager)
        
        await generator.generate_ad_pages_only(sample_ad_dataframe)
        
        # Verify local directory was created
        local_ads_dir = os.path.join(config_generate_only.download_dir, 'ads')
        assert os.path.exists(local_ads_dir)
        
        # Verify HTML file was created locally
        ad_file_path = os.path.join(local_ads_dir, 'test-ad-001.html')
        assert os.path.exists(ad_file_path)
        
        # Verify file content
        with open(ad_file_path, 'r') as f:
            content = f.read()
        assert '<html>Test Ad Page</html>' in content

    async def test_ad_page_generator_generate_only_empty_df(self, config_generate_only, mock_s3_manager, mock_renderer):
        """Test generate_ad_pages_only with empty DataFrame."""
        generator = AdPageGenerator(config_generate_only, mock_renderer, mock_s3_manager)
        empty_df = pd.DataFrame()
        
        await generator.generate_ad_pages_only(empty_df)
        
        # Should not create any files
        local_ads_dir = os.path.join(config_generate_only.download_dir, 'ads')
        if os.path.exists(local_ads_dir):
            assert len(os.listdir(local_ads_dir)) == 0

    @patch('src.reports.orchestrator.ReportsOrchestrator._load_and_process_data')
    @patch('src.reports.orchestrator.ReportsOrchestrator.generate_single_report')
    async def test_orchestrator_generate_and_upload_flow(self, mock_generate_report, mock_load_data, config_generate_and_upload, mock_storage):
        """Test orchestrator flow with generation and upload enabled."""
        # Setup mocks
        mock_load_data.return_value = {
            'ad_df': pd.DataFrame([{'ad_archive_id': 'test-001'}]),
            'docket_df': pd.DataFrame()
        }
        mock_generate_report.return_value = True
        
        # Create orchestrator with mocked dependencies
        with patch('src.reports.orchestrator.AsyncDynamoDBStorage', return_value=mock_storage):
            with patch('src.reports.orchestrator.S3AsyncStorage') as mock_s3_class:
                mock_s3_manager = AsyncMock()
                mock_s3_class.return_value = mock_s3_manager
                
                orchestrator = ReportsOrchestrator(config_generate_and_upload)
                
                # Mock ad page generator methods
                orchestrator.ad_page_generator.generate_and_upload_ad_pages = AsyncMock()
                orchestrator.ad_page_generator.validate_ad_pages = AsyncMock(return_value={'test-001': True})
                
                # Mock cache invalidator
                orchestrator.cache_invalidator.invalidate_cache = AsyncMock()
                
                await orchestrator.run(skip_ads=False, skip_invalidate=False)
                
                # Verify generate_and_upload_ad_pages was called
                orchestrator.ad_page_generator.generate_and_upload_ad_pages.assert_called_once()
                # Verify validation was called
                orchestrator.ad_page_generator.validate_ad_pages.assert_called_once()

    @patch('src.reports.orchestrator.ReportsOrchestrator._load_and_process_data')
    @patch('src.reports.orchestrator.ReportsOrchestrator.generate_single_report')
    async def test_orchestrator_generate_only_flow(self, mock_generate_report, mock_load_data, config_generate_only, mock_storage):
        """Test orchestrator flow with generation enabled but upload disabled."""
        # Setup mocks
        mock_load_data.return_value = {
            'ad_df': pd.DataFrame([{'ad_archive_id': 'test-001'}]),
            'docket_df': pd.DataFrame()
        }
        mock_generate_report.return_value = True
        
        # Create orchestrator with mocked dependencies
        with patch('src.reports.orchestrator.AsyncDynamoDBStorage', return_value=mock_storage):
            with patch('src.reports.orchestrator.S3AsyncStorage') as mock_s3_class:
                mock_s3_manager = AsyncMock()
                mock_s3_class.return_value = mock_s3_manager
                
                orchestrator = ReportsOrchestrator(config_generate_only)
                
                # Mock ad page generator methods
                orchestrator.ad_page_generator.generate_ad_pages_only = AsyncMock()
                orchestrator.ad_page_generator.validate_ad_pages = AsyncMock()
                
                # Mock cache invalidator
                orchestrator.cache_invalidator.invalidate_cache = AsyncMock()
                
                await orchestrator.run(skip_ads=False, skip_invalidate=False)
                
                # Verify generate_ad_pages_only was called (not upload version)
                orchestrator.ad_page_generator.generate_ad_pages_only.assert_called_once()
                # Verify validation was NOT called (since upload is disabled)
                orchestrator.ad_page_generator.validate_ad_pages.assert_not_called()

    @patch('src.reports.orchestrator.ReportsOrchestrator._load_and_process_data')
    @patch('src.reports.orchestrator.ReportsOrchestrator.generate_single_report')
    async def test_orchestrator_no_generation_flow(self, mock_generate_report, mock_load_data, config_no_generation, mock_storage):
        """Test orchestrator flow with generation disabled."""
        # Setup mocks
        mock_load_data.return_value = {
            'ad_df': pd.DataFrame([{'ad_archive_id': 'test-001'}]),
            'docket_df': pd.DataFrame()
        }
        mock_generate_report.return_value = True
        
        # Create orchestrator with mocked dependencies
        with patch('src.reports.orchestrator.AsyncDynamoDBStorage', return_value=mock_storage):
            with patch('src.reports.orchestrator.S3AsyncStorage') as mock_s3_class:
                mock_s3_manager = AsyncMock()
                mock_s3_class.return_value = mock_s3_manager
                
                orchestrator = ReportsOrchestrator(config_no_generation)
                
                # Mock ad page generator methods
                orchestrator.ad_page_generator.generate_and_upload_ad_pages = AsyncMock()
                orchestrator.ad_page_generator.generate_ad_pages_only = AsyncMock()
                
                # Mock cache invalidator
                orchestrator.cache_invalidator.invalidate_cache = AsyncMock()
                
                await orchestrator.run(skip_ads=False, skip_invalidate=False)
                
                # Verify neither generation method was called
                orchestrator.ad_page_generator.generate_and_upload_ad_pages.assert_not_called()
                orchestrator.ad_page_generator.generate_ad_pages_only.assert_not_called()

    @patch('src.reports.orchestrator.ReportsOrchestrator._load_and_process_data')
    @patch('src.reports.orchestrator.ReportsOrchestrator.generate_single_report')
    async def test_orchestrator_skip_ads_parameter(self, mock_generate_report, mock_load_data, config_generate_and_upload, mock_storage):
        """Test orchestrator respects skip_ads parameter."""
        # Setup mocks
        mock_load_data.return_value = {
            'ad_df': pd.DataFrame([{'ad_archive_id': 'test-001'}]),
            'docket_df': pd.DataFrame()
        }
        mock_generate_report.return_value = True
        
        # Create orchestrator with mocked dependencies
        with patch('src.reports.orchestrator.AsyncDynamoDBStorage', return_value=mock_storage):
            with patch('src.reports.orchestrator.S3AsyncStorage') as mock_s3_class:
                mock_s3_manager = AsyncMock()
                mock_s3_class.return_value = mock_s3_manager
                
                orchestrator = ReportsOrchestrator(config_generate_and_upload)
                
                # Mock ad page generator methods
                orchestrator.ad_page_generator.generate_and_upload_ad_pages = AsyncMock()
                
                # Mock cache invalidator
                orchestrator.cache_invalidator.invalidate_cache = AsyncMock()
                
                await orchestrator.run(skip_ads=True, skip_invalidate=False)
                
                # Verify no ad generation methods were called
                orchestrator.ad_page_generator.generate_and_upload_ad_pages.assert_not_called()

    def test_ad_page_generator_missing_columns(self, config_generate_only, mock_s3_manager, mock_renderer):
        """Test generate_ad_pages_only with missing required columns."""
        generator = AdPageGenerator(config_generate_only, mock_renderer, mock_s3_manager)
        
        # DataFrame missing required columns
        incomplete_df = pd.DataFrame([{'ad_archive_id': 'test-001'}])  # Missing many required columns
        
        # Should handle gracefully and not create files
        asyncio.run(generator.generate_ad_pages_only(incomplete_df))
        
        local_ads_dir = os.path.join(config_generate_only.download_dir, 'ads')
        if os.path.exists(local_ads_dir):
            assert len(os.listdir(local_ads_dir)) == 0