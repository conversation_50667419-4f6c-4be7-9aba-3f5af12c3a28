"""
Basic tests for the refactored Data Loader focusing on key functionality.
"""
import pytest
import asyncio
import pandas as pd
from unittest.mock import Async<PERSON>ock, Mock, patch
import tempfile
import os
import json


class TestDataLoaderBasic:
    """Basic tests for the clean async Data Loader."""

    @pytest.mark.asyncio
    async def test_no_migration_helpers_imported(self):
        """Test that the refactored data loader doesn't import migration helpers."""
        from src.reports.data_loader import DynamicD<PERSON><PERSON>oader, StaticDataLoader
        
        # Check DynamicDataLoader
        import sys
        module = sys.modules[DynamicDataLoader.__module__]
        module_file = module.__file__
        
        with open(module_file, 'r') as f:
            content = f.read()
        
        # Verify clean imports - no migration helpers
        assert 'create_manager_replacement' not in content
        assert 'src.migration' not in content
        assert '_using_new_architecture' not in content
        
        print("✓ Confirmed: No migration helpers imported in refactored data loader")

    @pytest.mark.asyncio
    async def test_dynamic_data_loader_direct_repository_usage(self):
        """Test that DynamicDataLoader uses repositories directly."""
        from src.reports.data_loader import DynamicDataLoader
        
        # Mock dependencies
        mock_config = Mock()
        mock_config.iso_date = '20240115'
        mock_config.seven_days_ago = '20240108'
        
        mock_pacer_repo = AsyncMock()
        mock_pacer_repo.query_by_added_on_range = AsyncMock(return_value=[])
        
        mock_ad_processor = Mock()
        mock_law_firm_handler = Mock()
        
        # Create loader
        loader = DynamicDataLoader(
            report_config=mock_config,
            pacer_repository=mock_pacer_repo,
            ad_processor=mock_ad_processor,
            law_firm_handler=mock_law_firm_handler
        )
        
        # Verify direct repository assignment (no wrapper)
        assert loader.pacer_repo is mock_pacer_repo
        
        print("✓ Confirmed: Direct repository usage without compatibility wrappers")

    @pytest.mark.asyncio
    async def test_async_methods_are_truly_async(self):
        """Test that async methods are coroutines, not sync wrappers."""
        from src.reports.data_loader import DynamicDataLoader
        import inspect
        
        # Mock dependencies
        mock_config = Mock()
        mock_pacer_repo = AsyncMock()
        mock_ad_processor = Mock()
        mock_law_firm_handler = Mock()
        
        loader = DynamicDataLoader(
            report_config=mock_config,
            pacer_repository=mock_pacer_repo,
            ad_processor=mock_ad_processor,
            law_firm_handler=mock_law_firm_handler
        )
        
        # Verify methods are coroutines (truly async)
        assert inspect.iscoroutinefunction(loader.load_docket_df_async)
        assert inspect.iscoroutinefunction(loader.load_ad_df_async)
        
        print("✓ Confirmed: Async methods are truly async, not sync wrappers")

    @pytest.mark.asyncio
    async def test_docket_data_loading_basic(self):
        """Test basic docket data loading functionality."""
        from src.reports.data_loader import DynamicDataLoader
        
        # Mock dependencies
        mock_config = Mock()
        mock_config.iso_date = '20240115'
        mock_config.seven_days_ago = '20240108'
        
        # Sample data that mimics DynamoDB format
        sample_data = [
            {
                'CourtId': 'test-court',
                'DocketNum': '1:24-cv-00001',
                'FilingDate': '20240115',
                'LawFirm': 'test firm',
                'Title': 'In Re: Test Case',
                'Versus': 'Test Defendant',
                'NumPlaintiffs': 100,
                'S3Link': 'https://test.com',
                'MdlNum': '9999',  # Use non-existent MDL to avoid lookup enhancement
                'Allegations': 'Test allegations',
                'Claims': 'Test claims',
                'IsRemoved': False,
                'AddedOn': '20240115',
                'PendingCto': False,
                'TransferredIn': False
            }
        ]
        
        mock_pacer_repo = AsyncMock()
        mock_pacer_repo.query_by_added_on_range = AsyncMock(return_value=sample_data)
        
        mock_ad_processor = Mock()
        mock_law_firm_handler = Mock()
        mock_law_firm_handler.capitalize_law_firm_names = Mock(side_effect=lambda x: x.title())
        
        with patch('src.reports.data_loader.normalize_law_firm_name', side_effect=lambda x: x.upper()):
            loader = DynamicDataLoader(
                report_config=mock_config,
                pacer_repository=mock_pacer_repo,
                ad_processor=mock_ad_processor,
                law_firm_handler=mock_law_firm_handler
            )
            
            # Test async loading
            df = await loader.load_docket_df_async(is_weekly=False)
            
            # Verify data processing
            assert isinstance(df, pd.DataFrame)
            assert len(df) == 1
            
            # Verify field transformations
            assert 'court_id' in df.columns  # Renamed from CourtId
            assert 'docket_num' in df.columns  # Renamed from DocketNum
            assert df.iloc[0]['court_id'] == 'test-court'
            assert df.iloc[0]['title'] == 'Test Case'  # "In Re:" prefix removed
            assert df.iloc[0]['law_firm'] == 'TEST FIRM'  # Normalized
            
            # Verify repository method was called
            mock_pacer_repo.query_by_added_on_range.assert_called_once_with('20240115', '20240115')
            
            print("✓ Confirmed: Docket data loading and processing works correctly")

    @pytest.mark.asyncio
    async def test_ad_data_loading_basic(self):
        """Test basic ad data loading functionality."""
        from src.reports.data_loader import DynamicDataLoader
        
        # Mock dependencies
        mock_config = Mock()
        mock_pacer_repo = AsyncMock()
        
        sample_ad_data = pd.DataFrame([{
            'ad_archive_id': 'test-ad-001',
            'law_firm': 'test firm',
            'ad_creative_body': 'Test ad content'
        }])
        
        mock_ad_processor = AsyncMock()
        mock_ad_processor.load_ad_df_async = AsyncMock(return_value=sample_ad_data)
        
        mock_law_firm_handler = Mock()
        mock_law_firm_handler.capitalize_law_firm_names = Mock(side_effect=lambda x: x.title())
        
        with patch('src.reports.data_loader.normalize_law_firm_name', side_effect=lambda x: x.upper()):
            loader = DynamicDataLoader(
                report_config=mock_config,
                pacer_repository=mock_pacer_repo,
                ad_processor=mock_ad_processor,
                law_firm_handler=mock_law_firm_handler
            )
            
            # Test async loading
            df = await loader.load_ad_df_async()
            
            # Verify data processing
            assert isinstance(df, pd.DataFrame)
            assert len(df) == 1
            assert 'law_firm' in df.columns
            assert df.iloc[0]['law_firm'] == 'TEST FIRM'  # Normalized
            
            # Verify ad processor was called
            mock_ad_processor.load_ad_df_async.assert_called_once()
            
            print("✓ Confirmed: Ad data loading and processing works correctly")

    @pytest.mark.asyncio
    async def test_empty_data_handling(self):
        """Test proper handling of empty data."""
        from src.reports.data_loader import DynamicDataLoader
        
        # Mock dependencies
        mock_config = Mock()
        mock_config.iso_date = '20240115'
        
        mock_pacer_repo = AsyncMock()
        mock_pacer_repo.query_by_added_on_range = AsyncMock(return_value=[])
        
        mock_ad_processor = Mock()
        mock_law_firm_handler = Mock()
        
        loader = DynamicDataLoader(
            report_config=mock_config,
            pacer_repository=mock_pacer_repo,
            ad_processor=mock_ad_processor,
            law_firm_handler=mock_law_firm_handler
        )
        
        # Test empty data handling
        df = await loader.load_docket_df_async(is_weekly=False)
        
        # Should return empty DataFrame with correct schema
        assert isinstance(df, pd.DataFrame)
        assert len(df) == 0
        assert 'court_id' in df.columns
        assert 'docket_num' in df.columns
        
        print("✓ Confirmed: Empty data handled correctly")

    @pytest.mark.asyncio
    async def test_no_event_loop_creation_overhead(self):
        """Test that no new event loops are created (performance improvement)."""
        from src.reports.data_loader import DynamicDataLoader
        
        with patch('asyncio.new_event_loop') as mock_new_loop:
            
            # Mock dependencies
            mock_config = Mock()
            mock_config.iso_date = '20240115'
            
            mock_pacer_repo = AsyncMock()
            mock_pacer_repo.get_records_by_added_on_date_range = AsyncMock(return_value=[])
            
            mock_ad_processor = Mock()
            mock_law_firm_handler = Mock()
            
            loader = DynamicDataLoader(
                report_config=mock_config,
                pacer_repository=mock_pacer_repo,
                ad_processor=mock_ad_processor,
                law_firm_handler=mock_law_firm_handler
            )
            
            # Perform async operations
            await loader.load_docket_df_async(is_weekly=False)
            
            # Verify no new event loops were created (big improvement over compatibility layer!)
            mock_new_loop.assert_not_called()
            
            print("✓ Confirmed: No event loops created during async operations")

    def test_static_data_loader_clean_imports(self):
        """Test that StaticDataLoader has clean imports."""
        from src.reports.data_loader import StaticDataLoader
        
        # Check the module source
        import sys
        module = sys.modules[StaticDataLoader.__module__]
        module_file = module.__file__
        
        with open(module_file, 'r') as f:
            content = f.read()
        
        # Verify no migration helpers
        assert 'create_manager_replacement' not in content
        assert 'src.migration' not in content
        
        print("✓ Confirmed: StaticDataLoader has clean imports")

    def test_backward_compatibility_preserved(self):
        """Test that backward compatibility sync methods exist."""
        from src.reports.data_loader import DynamicDataLoader
        
        # Mock dependencies
        mock_config = Mock()
        mock_pacer_repo = AsyncMock()
        mock_ad_processor = Mock()
        mock_law_firm_handler = Mock()
        
        loader = DynamicDataLoader(
            report_config=mock_config,
            pacer_repository=mock_pacer_repo,
            ad_processor=mock_ad_processor,
            law_firm_handler=mock_law_firm_handler
        )
        
        # Verify sync methods exist for backward compatibility
        assert hasattr(loader, 'load_docket_df')
        assert hasattr(loader, 'load_ad_df')
        assert callable(loader.load_docket_df)
        assert callable(loader.load_ad_df)
        
        print("✓ Confirmed: Backward compatibility methods preserved")


if __name__ == "__main__":
    pytest.main([__file__, "-v"])