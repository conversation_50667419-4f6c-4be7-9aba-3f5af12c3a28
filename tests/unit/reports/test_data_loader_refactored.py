"""
Comprehensive tests for the refactored Data Loader.
Tests the clean async implementation without migration helpers.
"""
import pytest
import asyncio
import pandas as pd
from unittest.mock import AsyncMock, Mock, patch, MagicMock
import tempfile
import os
import json
from datetime import datetime, timedelta


class TestStaticDataLoaderRefactored:
    """Test suite for the refactored StaticDataLoader."""

    def create_test_config_files(self):
        """Create temporary config files for testing."""
        temp_dir = tempfile.mkdtemp()
        
        # Create directory structure
        reports_config_dir = os.path.join(temp_dir, 'src', 'config', 'reports')
        attorneys_config_dir = os.path.join(temp_dir, 'src', 'config', 'attorneys')
        os.makedirs(reports_config_dir, exist_ok=True)
        os.makedirs(attorneys_config_dir, exist_ok=True)
        
        # Create test config files
        test_configs = {
            'litigation_sponsorships.json': {'mdl_3001': {'sponsor': 'Test Sponsor', 'link': 'http://test.com'}},
            'general_sponsorships.json': {'header': 'Test Header', 'slots': []},
            'upcoming_hearings.json': [{'date': '01/15/2024', 'title': 'Test Hearing', 'court': 'Test Court'}],
            'announcements.json': [{'content': 'Test Announcement', 'date': '2024-01-15'}],
            'news.json': [{'date': '01/15/24', 'title': 'Test News', 'link': 'http://news.test'}],
            'special_reports.json': [{'title': 'Test Report', 'link': 'http://report.test'}]
        }
        
        for filename, content in test_configs.items():
            file_path = os.path.join(reports_config_dir, filename)
            with open(file_path, 'w') as f:
                json.dump(content, f)
        
        # Create attorney lookup file
        attorney_data = [
            {'attorney_name': 'John Doe', 'law_firm': 'Test Law Firm'},
            {'attorney_name': 'Jane Smith', 'law_firm': 'Another Firm'}
        ]
        with open(os.path.join(attorneys_config_dir, 'attorney_lookup.json'), 'w') as f:
            json.dump(attorney_data, f)
        
        return temp_dir

    def test_static_data_loader_initialization(self):
        """Test that StaticDataLoader initializes without migration helpers."""
        temp_dir = self.create_test_config_files()
        
        try:
            # Mock the path detection to use our temp directory
            import os
            original_dirname = os.path.dirname
            with patch('os.path.dirname') as mock_dirname:
                mock_dirname.side_effect = lambda x: temp_dir if 'data_loader.py' in x else original_dirname(x)
                
                from src.reports.data_loader import StaticDataLoader
                
                loader = StaticDataLoader()
                
                # Verify no migration helpers are imported
                import sys
                module = sys.modules[loader.__module__]
                module_file = module.__file__
                
                with open(module_file, 'r') as f:
                    content = f.read()
                
                # Verify clean imports
                assert 'create_manager_replacement' not in content
                assert 'src.migration' not in content
                assert '_using_new_architecture' not in content
                
                print("✓ Confirmed: No migration helpers in StaticDataLoader")
                
        finally:
            import shutil
            shutil.rmtree(temp_dir)

    def test_load_attorney_df(self):
        """Test attorney data loading."""
        temp_dir = self.create_test_config_files()
        
        try:
            from src.reports.data_loader import StaticDataLoader
            
            # Mock the data_path to point to our temp directory
            import logging
            data_path = os.path.join(temp_dir, 'src', 'config')
            with patch.object(StaticDataLoader, '__init__', lambda self: setattr(self, 'data_path', data_path) or setattr(self, 'logger', logging.getLogger('test'))):
                loader = StaticDataLoader()
                df = loader.load_attorney_df()
                
                assert isinstance(df, pd.DataFrame)
                assert len(df) == 2
                assert 'Attorney' in df.columns
                assert 'Law Firm' in df.columns
                assert df.iloc[0]['Attorney'] == 'John Doe'
                assert df.iloc[0]['Law Firm'] == 'Test Law Firm'
                
        finally:
            import shutil
            shutil.rmtree(temp_dir)

    def test_load_sponsorships(self):
        """Test sponsorship data loading."""
        temp_dir = self.create_test_config_files()
        
        try:
            from src.reports.data_loader import StaticDataLoader
            
            # Mock the config_path to point to our temp directory
            import logging
            config_path = os.path.join(temp_dir, 'src', 'config', 'reports')  
            with patch.object(StaticDataLoader, '__init__', lambda self: setattr(self, 'config_path', config_path) or setattr(self, 'logger', logging.getLogger('test'))):
                loader = StaticDataLoader()
                
                # Test litigation sponsorships
                lit_sponsors = loader.load_litigation_sponsorships()
                assert 'mdl_3001' in lit_sponsors
                assert lit_sponsors['mdl_3001']['sponsor'] == 'Test Sponsor'
                
                # Test general sponsorships
                gen_sponsors = loader.load_general_sponsorships()
                assert gen_sponsors['header'] == 'Test Header'
                
        finally:
            import shutil
            shutil.rmtree(temp_dir)

    def test_load_upcoming_hearings(self):
        """Test upcoming hearings loading with date processing."""
        temp_dir = self.create_test_config_files()
        
        try:
            from src.reports.data_loader import StaticDataLoader
            
            # Mock the config_path to point to our temp directory
            import logging
            config_path = os.path.join(temp_dir, 'src', 'config', 'reports')
            with patch.object(StaticDataLoader, '__init__', lambda self: setattr(self, 'config_path', config_path) or setattr(self, 'logger', logging.getLogger('test'))):
                loader = StaticDataLoader()
                hearings = loader.load_upcoming_hearings()
                
                assert len(hearings) == 1
                assert hearings[0]['title'] == 'Test Hearing'
                assert hearings[0]['date_day'] == '15'
                assert hearings[0]['date_year'] == '2024'
                
        finally:
            import shutil
            shutil.rmtree(temp_dir)


class TestDynamicDataLoaderRefactored:
    """Test suite for the refactored DynamicDataLoader."""

    @pytest.fixture
    def mock_config(self):
        """Mock report configuration."""
        config = Mock()
        config.iso_date = '20240115'
        config.seven_days_ago = '20240108'
        return config

    @pytest.fixture
    def mock_pacer_repo(self):
        """Mock PACER repository."""
        repo = AsyncMock()
        repo.query_by_filing_date = AsyncMock()
        repo.query_by_added_on_range = AsyncMock()
        return repo

    @pytest.fixture
    def mock_ad_processor(self):
        """Mock ad processor."""
        processor = AsyncMock()
        processor.load_ad_df_async = AsyncMock()
        return processor

    @pytest.fixture
    def mock_law_firm_handler(self):
        """Mock law firm handler."""
        handler = Mock()
        handler.capitalize_law_firm_names = Mock(side_effect=lambda x: x.title())
        return handler

    @pytest.fixture
    def sample_docket_data(self):
        """Sample docket data for testing."""
        return [
            {
                'CourtId': 'test-court-1',
                'DocketNum': '1:24-cv-00001',
                'FilingDate': '20240115',
                'LawFirm': 'test law firm',
                'Title': 'In Re: Test Litigation',
                'Versus': 'Test Defendant',
                'NumPlaintiffs': 100,
                'S3Link': 'https://s3.amazonaws.com/test-link',
                'MdlNum': '9999',  # Use non-existent MDL to avoid lookup enhancement
                'Allegations': 'Test allegations',
                'Claims': 'Test claims',
                'IsRemoved': False,
                'AddedOn': '20240115',
                'PendingCto': False,
                'TransferredIn': False
            }
        ]

    @pytest.fixture
    def sample_ad_data(self):
        """Sample ad data for testing."""
        return pd.DataFrame([
            {
                'ad_archive_id': 'test-ad-001',
                'law_firm': 'test law firm',
                'ad_creative_body': 'Test ad content',
                'start_date': '2024-01-15',
                'impressions': 1000,
                'spend': 500.00
            }
        ])

    @pytest.mark.asyncio
    async def test_no_migration_helpers_imported(self):
        """Test that DynamicDataLoader doesn't import migration helpers."""
        from src.reports.data_loader import DynamicDataLoader
        
        # Verify no migration helpers are imported
        import sys
        module = sys.modules[DynamicDataLoader.__module__]
        module_file = module.__file__
        
        with open(module_file, 'r') as f:
            content = f.read()
        
        # Verify clean imports
        assert 'create_manager_replacement' not in content
        assert 'src.migration' not in content
        assert '_using_new_architecture' not in content
        
        print("✓ Confirmed: No migration helpers in DynamicDataLoader")

    @pytest.mark.asyncio
    async def test_load_docket_df_async_daily(self, mock_config, mock_pacer_repo, mock_ad_processor, 
                                               mock_law_firm_handler, sample_docket_data):
        """Test async docket data loading for daily reports."""
        # Setup mocks
        mock_pacer_repo.query_by_added_on_range.return_value = sample_docket_data
        
        with patch('src.reports.data_loader.normalize_law_firm_name', side_effect=lambda x: x):
            from src.reports.data_loader import DynamicDataLoader
            
            loader = DynamicDataLoader(
                report_config=mock_config,
                pacer_repository=mock_pacer_repo,
                ad_processor=mock_ad_processor,
                law_firm_handler=mock_law_firm_handler
            )
            
            # Test async loading
            df = await loader.load_docket_df_async(is_weekly=False)
            
            # Verify data was loaded and processed correctly
            assert isinstance(df, pd.DataFrame)
            assert len(df) == 1
            assert 'court_id' in df.columns  # Renamed from CourtId
            assert 'docket_num' in df.columns  # Renamed from DocketNum
            assert df.iloc[0]['court_id'] == 'test-court-1'
            assert df.iloc[0]['title'] == 'Test Litigation'  # "In Re:" removed
            
            # Verify repository method was called with correct parameters
            mock_pacer_repo.query_by_added_on_range.assert_called_once_with('20240115', '20240115')

    @pytest.mark.asyncio
    async def test_load_docket_df_async_weekly(self, mock_config, mock_pacer_repo, mock_ad_processor, 
                                                mock_law_firm_handler, sample_docket_data):
        """Test async docket data loading for weekly reports."""
        # Setup mocks for weekly query
        mock_pacer_repo.query_by_filing_date.return_value = sample_docket_data
        
        with patch('src.reports.data_loader.normalize_law_firm_name', side_effect=lambda x: x):
            from src.reports.data_loader import DynamicDataLoader
            
            loader = DynamicDataLoader(
                report_config=mock_config,
                pacer_repository=mock_pacer_repo,
                ad_processor=mock_ad_processor,
                law_firm_handler=mock_law_firm_handler
            )
            
            # Test async weekly loading
            df = await loader.load_docket_df_async(is_weekly=True)
            
            # Verify data was loaded
            assert isinstance(df, pd.DataFrame)
            assert len(df) >= 1  # Could be multiple calls for different dates
            
            # Verify repository method was called for filing date queries
            assert mock_pacer_repo.query_by_filing_date.call_count >= 1

    @pytest.mark.asyncio
    async def test_load_ad_df_async(self, mock_config, mock_pacer_repo, mock_ad_processor, 
                                     mock_law_firm_handler, sample_ad_data):
        """Test async ad data loading."""
        # Setup mock
        mock_ad_processor.load_ad_df_async.return_value = sample_ad_data
        
        with patch('src.reports.data_loader.normalize_law_firm_name', side_effect=lambda x: x):
            from src.reports.data_loader import DynamicDataLoader
            
            loader = DynamicDataLoader(
                report_config=mock_config,
                pacer_repository=mock_pacer_repo,
                ad_processor=mock_ad_processor,
                law_firm_handler=mock_law_firm_handler
            )
            
            # Test async loading
            df = await loader.load_ad_df_async()
            
            # Verify data was loaded and processed
            assert isinstance(df, pd.DataFrame)
            assert len(df) == 1
            assert 'law_firm' in df.columns
            assert 'ad_archive_id' in df.columns
            
            # Verify ad processor was called
            mock_ad_processor.load_ad_df_async.assert_called_once()

    @pytest.mark.asyncio
    async def test_data_processing_and_normalization(self, mock_config, mock_pacer_repo, mock_ad_processor, 
                                                      mock_law_firm_handler, sample_docket_data):
        """Test data processing and law firm normalization."""
        # Setup mocks
        mock_pacer_repo.query_by_added_on_range.return_value = sample_docket_data
        
        with patch('src.reports.data_loader.normalize_law_firm_name', side_effect=lambda x: x.upper()):
            from src.reports.data_loader import DynamicDataLoader
            
            loader = DynamicDataLoader(
                report_config=mock_config,
                pacer_repository=mock_pacer_repo,
                ad_processor=mock_ad_processor,
                law_firm_handler=mock_law_firm_handler
            )
            
            df = await loader.load_docket_df_async(is_weekly=False)
            
            # Verify data transformations
            assert df.iloc[0]['law_firm'] == 'TEST LAW FIRM'  # Law firm normalization applied
            assert df.iloc[0]['title'] == 'Test Litigation'  # "In Re:" prefix removed
            assert df.iloc[0]['versus_sort'] == 'test defendant'  # Lowercase version created
            assert df.iloc[0]['is_removed'] == False  # Boolean conversion
            assert df.iloc[0]['num_plaintiffs'] == 100  # Numeric conversion

    @pytest.mark.asyncio
    async def test_empty_data_handling(self, mock_config, mock_pacer_repo, mock_ad_processor, mock_law_firm_handler):
        """Test handling of empty data results."""
        # Setup mocks to return empty data
        mock_pacer_repo.query_by_added_on_range.return_value = []
        
        from src.reports.data_loader import DynamicDataLoader
        
        loader = DynamicDataLoader(
            report_config=mock_config,
            pacer_repository=mock_pacer_repo,
            ad_processor=mock_ad_processor,
            law_firm_handler=mock_law_firm_handler
        )
        
        df = await loader.load_docket_df_async(is_weekly=False)
        
        # Verify empty DataFrame with correct schema
        assert isinstance(df, pd.DataFrame)
        assert len(df) == 0
        assert 'court_id' in df.columns
        assert 'docket_num' in df.columns
        assert 'law_firm' in df.columns

    @pytest.mark.asyncio
    async def test_error_handling(self, mock_config, mock_pacer_repo, mock_ad_processor, mock_law_firm_handler):
        """Test error handling during data loading."""
        # Setup mock to raise exception
        mock_pacer_repo.query_by_added_on_range.side_effect = Exception("Database error")
        
        from src.reports.data_loader import DynamicDataLoader
        
        loader = DynamicDataLoader(
            report_config=mock_config,
            pacer_repository=mock_pacer_repo,
            ad_processor=mock_ad_processor,
            law_firm_handler=mock_law_firm_handler
        )
        
        df = await loader.load_docket_df_async(is_weekly=False)
        
        # Should return empty DataFrame on error
        assert isinstance(df, pd.DataFrame)
        assert len(df) == 0

    @pytest.mark.asyncio
    async def test_caching_functionality(self, mock_config, mock_pacer_repo, mock_ad_processor, 
                                          mock_law_firm_handler, sample_docket_data):
        """Test that data is cached properly."""
        # Setup mock
        mock_pacer_repo.query_by_added_on_range.return_value = sample_docket_data
        
        with patch('src.reports.data_loader.normalize_law_firm_name', side_effect=lambda x: x):
            from src.reports.data_loader import DynamicDataLoader
            
            loader = DynamicDataLoader(
                report_config=mock_config,
                pacer_repository=mock_pacer_repo,
                ad_processor=mock_ad_processor,
                law_firm_handler=mock_law_firm_handler
            )
            
            # Load data twice
            df1 = await loader.load_docket_df_async(is_weekly=False)
            df2 = await loader.load_docket_df_async(is_weekly=False)
            
            # Verify repository was only called once (cached on second call)
            mock_pacer_repo.query_by_added_on_range.assert_called_once()
            
            # Verify data is the same
            assert len(df1) == len(df2)
            assert df1.equals(df2)

    def test_backward_compatibility_sync_methods(self, mock_config, mock_pacer_repo, mock_ad_processor, 
                                                   mock_law_firm_handler):
        """Test that sync wrapper methods work for backward compatibility."""
        from src.reports.data_loader import DynamicDataLoader
        
        loader = DynamicDataLoader(
            report_config=mock_config,
            pacer_repository=mock_pacer_repo,
            ad_processor=mock_ad_processor,
            law_firm_handler=mock_law_firm_handler
        )
        
        # Verify sync methods exist and are callable
        assert hasattr(loader, 'load_docket_df')
        assert hasattr(loader, 'load_ad_df')
        assert callable(loader.load_docket_df)
        assert callable(loader.load_ad_df)


class TestPerformanceImprovements:
    """Test performance improvements from the refactored data loader."""

    @pytest.fixture
    def mock_config(self):
        """Mock report configuration."""
        config = Mock()
        config.iso_date = '20240115'
        config.seven_days_ago = '20240108'
        return config

    @pytest.fixture
    def mock_pacer_repo(self):
        """Mock PACER repository."""
        repo = AsyncMock()
        repo.query_by_added_on_range = AsyncMock()
        repo.get_records_by_added_on_date_range = AsyncMock()
        return repo

    @pytest.fixture
    def mock_ad_processor(self):
        """Mock ad processor."""
        processor = AsyncMock()
        processor.load_ad_df_async = AsyncMock()
        return processor

    @pytest.fixture
    def mock_law_firm_handler(self):
        """Mock law firm handler."""
        handler = Mock()
        handler.capitalize_law_firm_names = Mock(side_effect=lambda x: x.title())
        return handler

    @pytest.mark.asyncio
    async def test_no_event_loop_creation(self, mock_config, mock_pacer_repo, mock_ad_processor, mock_law_firm_handler):
        """Test that no new event loops are created during async operations."""
        with patch('asyncio.new_event_loop') as mock_new_loop:
            
            mock_pacer_repo.query_by_added_on_range.return_value = []
            
            from src.reports.data_loader import DynamicDataLoader
            
            loader = DynamicDataLoader(
                report_config=mock_config,
                pacer_repository=mock_pacer_repo,
                ad_processor=mock_ad_processor,
                law_firm_handler=mock_law_firm_handler
            )
            
            # Perform async operations
            await loader.load_docket_df_async(is_weekly=False)
            
            # Verify no new event loops were created
            mock_new_loop.assert_not_called()
            print("✓ Confirmed: No event loops created during async data loading")

    @pytest.mark.asyncio
    async def test_direct_repository_usage(self, mock_config, mock_pacer_repo, mock_ad_processor, mock_law_firm_handler):
        """Test that repository is used directly without compatibility wrappers."""
        mock_pacer_repo.query_by_added_on_range.return_value = []
        
        from src.reports.data_loader import DynamicDataLoader
        
        loader = DynamicDataLoader(
            report_config=mock_config,
            pacer_repository=mock_pacer_repo,
            ad_processor=mock_ad_processor,
            law_firm_handler=mock_law_firm_handler
        )
        
        await loader.load_docket_df_async(is_weekly=False)
        
        # Verify direct repository method was called (no wrapper)
        assert loader.pacer_repo is mock_pacer_repo
        mock_pacer_repo.query_by_added_on_range.assert_called_once()
        print("✓ Confirmed: Direct repository usage without compatibility wrappers")


if __name__ == "__main__":
    pytest.main([__file__, "-v"])