"""
Tests for FB ad caching functionality in reports.
"""
import pytest
import asyncio
import pandas as pd
import json
import os
import tempfile
from unittest.mock import AsyncMock, Mock, patch, MagicMock
from datetime import datetime, timedelta

from src.reports.config import ReportConfig
from src.reports.data_loader import DynamicDataLoader


class TestFBAdsCache:
    """Test suite for FB ads caching functionality."""

    @pytest.fixture
    def temp_dir(self):
        """Create temporary directory for tests."""
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        # Cleanup handled by tempfile

    @pytest.fixture
    def sample_config_dict(self, temp_dir):
        """Sample configuration dictionary for testing."""
        return {
            'date': '01/15/24',
            'project_root': temp_dir,
            'bucket_name': 'test-bucket',
            'fb_ads': {
                'use_cache': True,
                'force_regenerate': False,
                'cache_ttl_hours': 24,
                'generate_ad_pages': True,
                'upload_ad_pages': True
            }
        }

    @pytest.fixture
    def config_with_cache_disabled(self, sample_config_dict):
        """Configuration with cache disabled."""
        sample_config_dict['fb_ads']['use_cache'] = False
        return ReportConfig(sample_config_dict)

    @pytest.fixture
    def config_with_cache_enabled(self, sample_config_dict):
        """Configuration with cache enabled."""
        return ReportConfig(sample_config_dict)

    @pytest.fixture
    def config_with_force_regenerate(self, sample_config_dict):
        """Configuration with force regenerate enabled."""
        sample_config_dict['fb_ads']['force_regenerate'] = True
        return ReportConfig(sample_config_dict)

    @pytest.fixture
    def sample_ad_dataframe(self):
        """Sample FB ad DataFrame for testing."""
        return pd.DataFrame([
            {
                'ad_archive_id': 'test-ad-001',
                'law_firm': 'Test Law Firm',
                'title': 'Test Ad Title',
                'body': 'Test ad content',
                'start_date': '2024-01-15',
                'end_date': '2024-01-20',
                'page_id': 'test-page-001',
                'ad_creative_id': 'test-creative-001',
                'publisher_platform': 'Facebook',
                'link_url': 'https://example.com',
                'campaign': 'Test Campaign'
            },
            {
                'ad_archive_id': 'test-ad-002',
                'law_firm': 'Another Law Firm',
                'title': 'Another Test Ad',
                'body': 'Another test ad content',
                'start_date': '2024-01-14',
                'end_date': '2024-01-19',
                'page_id': 'test-page-002',
                'ad_creative_id': 'test-creative-002',
                'publisher_platform': 'Instagram',
                'link_url': 'https://example2.com',
                'campaign': 'Another Campaign'
            }
        ])

    @pytest.fixture
    def mock_repositories(self):
        """Mock repositories for data loader."""
        return {
            'pacer': AsyncMock(),
            'fb_archive': AsyncMock(),
            'law_firms': AsyncMock()
        }

    @pytest.fixture
    def mock_ad_processor(self, sample_ad_dataframe):
        """Mock ad processor."""
        processor = Mock()
        processor.load_ad_df.return_value = sample_ad_dataframe
        return processor

    @pytest.fixture
    def data_loader(self, config_with_cache_enabled, mock_repositories, mock_ad_processor):
        """Data loader with mocked dependencies."""
        loader = DynamicDataLoader(config_with_cache_enabled, mock_repositories)
        loader.ad_processor = mock_ad_processor
        return loader

    def test_report_config_fb_ads_properties(self, config_with_cache_enabled):
        """Test that ReportConfig properly loads FB ads configuration."""
        assert config_with_cache_enabled.fb_ads_use_cache is True
        assert config_with_cache_enabled.fb_ads_force_regenerate is False
        assert config_with_cache_enabled.fb_ads_cache_ttl_hours == 24
        assert config_with_cache_enabled.fb_ads_generate_pages is True
        assert config_with_cache_enabled.fb_ads_upload_pages is True

    def test_report_config_fb_ads_defaults(self, temp_dir):
        """Test FB ads config defaults when not specified."""
        config_dict = {
            'date': '01/15/24',
            'project_root': temp_dir,
            'bucket_name': 'test-bucket'
        }
        config = ReportConfig(config_dict)
        
        # Should use defaults
        assert config.fb_ads_use_cache is True
        assert config.fb_ads_force_regenerate is False
        assert config.fb_ads_cache_ttl_hours == 24
        assert config.fb_ads_generate_pages is True
        assert config.fb_ads_upload_pages is True

    def test_get_fb_ads_cache_path(self, data_loader):
        """Test cache path generation."""
        cache_path = data_loader._get_fb_ads_cache_path()
        expected_path = os.path.join(
            data_loader.config.download_dir, 
            'reports', 
            'fb_ads_cache_20240115.json'
        )
        assert cache_path == expected_path

    def test_get_fb_ads_cache_path_missing_config(self, mock_repositories):
        """Test cache path when config is missing required data."""
        config_dict = {'date': '01/15/24', 'bucket_name': 'test'}
        config = ReportConfig(config_dict)
        config.iso_date = None  # Simulate missing date
        
        loader = DynamicDataLoader(config, mock_repositories)
        cache_path = loader._get_fb_ads_cache_path()
        assert cache_path is None

    def test_save_fb_ads_cache(self, data_loader, sample_ad_dataframe):
        """Test saving FB ads data to cache."""
        # Ensure cache directory exists
        cache_path = data_loader._get_fb_ads_cache_path()
        os.makedirs(os.path.dirname(cache_path), exist_ok=True)
        
        # Save cache
        result = data_loader._save_fb_ads_cache(sample_ad_dataframe)
        assert result is True
        assert os.path.exists(cache_path)
        
        # Verify cache content
        with open(cache_path, 'r') as f:
            cache_data = json.load(f)
        
        assert 'cached_at' in cache_data
        assert cache_data['iso_date'] == '20240115'
        assert 'data' in cache_data
        assert len(cache_data['data']) == 2
        assert cache_data['data'][0]['ad_archive_id'] == 'test-ad-001'

    def test_save_fb_ads_cache_empty_dataframe(self, data_loader):
        """Test saving empty DataFrame to cache."""
        empty_df = pd.DataFrame()
        result = data_loader._save_fb_ads_cache(empty_df)
        assert result is True  # Should still succeed
        
        cache_path = data_loader._get_fb_ads_cache_path()
        with open(cache_path, 'r') as f:
            cache_data = json.load(f)
        assert len(cache_data['data']) == 0

    def test_is_fb_ads_cache_valid_fresh_cache(self, data_loader, sample_ad_dataframe):
        """Test cache validation with fresh cache."""
        # Create fresh cache
        cache_path = data_loader._get_fb_ads_cache_path()
        os.makedirs(os.path.dirname(cache_path), exist_ok=True)
        data_loader._save_fb_ads_cache(sample_ad_dataframe)
        
        # Should be valid
        assert data_loader._is_fb_ads_cache_valid(cache_path) is True

    def test_is_fb_ads_cache_valid_expired_cache(self, data_loader, sample_ad_dataframe):
        """Test cache validation with expired cache."""
        # Create cache
        cache_path = data_loader._get_fb_ads_cache_path()
        os.makedirs(os.path.dirname(cache_path), exist_ok=True)
        data_loader._save_fb_ads_cache(sample_ad_dataframe)
        
        # Modify config to have very short TTL
        data_loader.config.fb_ads_cache_ttl_hours = 0.001  # Very short TTL
        
        # Should be expired
        assert data_loader._is_fb_ads_cache_valid(cache_path) is False

    def test_is_fb_ads_cache_valid_missing_file(self, data_loader):
        """Test cache validation with missing cache file."""
        non_existent_path = '/tmp/non_existent_cache.json'
        assert data_loader._is_fb_ads_cache_valid(non_existent_path) is False

    def test_load_fb_ads_cache_success(self, data_loader, sample_ad_dataframe):
        """Test successful cache loading."""
        # Create cache
        cache_path = data_loader._get_fb_ads_cache_path()
        os.makedirs(os.path.dirname(cache_path), exist_ok=True)
        data_loader._save_fb_ads_cache(sample_ad_dataframe)
        
        # Load cache
        loaded_df = data_loader._load_fb_ads_cache()
        assert loaded_df is not None
        assert len(loaded_df) == 2
        assert loaded_df.iloc[0]['ad_archive_id'] == 'test-ad-001'
        assert loaded_df.iloc[1]['ad_archive_id'] == 'test-ad-002'

    def test_load_fb_ads_cache_disabled(self, config_with_cache_disabled, mock_repositories):
        """Test cache loading when cache is disabled."""
        loader = DynamicDataLoader(config_with_cache_disabled, mock_repositories)
        loaded_df = loader._load_fb_ads_cache()
        assert loaded_df is None

    def test_load_fb_ads_cache_invalid_data(self, data_loader):
        """Test cache loading with invalid cache data."""
        # Create invalid cache file
        cache_path = data_loader._get_fb_ads_cache_path()
        os.makedirs(os.path.dirname(cache_path), exist_ok=True)
        
        invalid_data = {'invalid': 'structure'}
        with open(cache_path, 'w') as f:
            json.dump(invalid_data, f)
        
        loaded_df = data_loader._load_fb_ads_cache()
        assert loaded_df is None

    def test_load_fb_ads_cache_date_mismatch(self, data_loader, sample_ad_dataframe):
        """Test cache loading with date mismatch."""
        # Create cache with different date
        cache_path = data_loader._get_fb_ads_cache_path()
        os.makedirs(os.path.dirname(cache_path), exist_ok=True)
        
        cache_data = {
            'cached_at': datetime.now().isoformat(),
            'iso_date': '20240120',  # Different date
            'data': sample_ad_dataframe.to_dict('records')
        }
        
        with open(cache_path, 'w') as f:
            json.dump(cache_data, f)
        
        loaded_df = data_loader._load_fb_ads_cache()
        assert loaded_df is None

    def test_load_ad_df_uses_cache(self, data_loader, sample_ad_dataframe):
        """Test that load_ad_df uses cache when available."""
        # Create cache
        cache_path = data_loader._get_fb_ads_cache_path()
        os.makedirs(os.path.dirname(cache_path), exist_ok=True)
        data_loader._save_fb_ads_cache(sample_ad_dataframe)
        
        # Load ads - should use cache
        result_df = data_loader.load_ad_df()
        
        # Should return cached data
        assert len(result_df) == 2
        assert result_df.iloc[0]['ad_archive_id'] == 'test-ad-001'
        
        # Ad processor should not have been called
        data_loader.ad_processor.load_ad_df.assert_not_called()

    def test_load_ad_df_force_regenerate(self, config_with_force_regenerate, mock_repositories, mock_ad_processor, sample_ad_dataframe):
        """Test that load_ad_df bypasses cache when force_regenerate is True."""
        loader = DynamicDataLoader(config_with_force_regenerate, mock_repositories)
        loader.ad_processor = mock_ad_processor
        
        # Create cache
        cache_path = loader._get_fb_ads_cache_path()
        os.makedirs(os.path.dirname(cache_path), exist_ok=True)
        loader._save_fb_ads_cache(sample_ad_dataframe)
        
        # Load ads - should bypass cache
        result_df = loader.load_ad_df()
        
        # Ad processor should have been called
        mock_ad_processor.load_ad_df.assert_called_once()

    def test_load_ad_df_no_cache_fallback(self, data_loader):
        """Test that load_ad_df falls back to database when no cache."""
        # No cache exists, should call ad processor
        result_df = data_loader.load_ad_df()
        
        # Ad processor should have been called
        data_loader.ad_processor.load_ad_df.assert_called_once()
        
        # Should save to cache after loading
        cache_path = data_loader._get_fb_ads_cache_path()
        assert os.path.exists(cache_path)

    def test_load_ad_df_saves_to_cache(self, data_loader):
        """Test that load_ad_df saves data to cache after loading from database."""
        # Load ads (no cache exists)
        result_df = data_loader.load_ad_df()
        
        # Cache should now exist
        cache_path = data_loader._get_fb_ads_cache_path()
        assert os.path.exists(cache_path)
        
        # Verify cache content
        with open(cache_path, 'r') as f:
            cache_data = json.load(f)
        assert len(cache_data['data']) == 2