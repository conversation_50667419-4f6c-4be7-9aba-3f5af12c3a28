"""
Unit tests for the date utility functions.

This tests the DateUtils class which provides core date parsing,
formatting, and manipulation functionality used throughout the application.
"""
import pytest
from datetime import datetime, date, timedelta
from freezegun import freeze_time

from src.utils.date import DateUtils, FORMAT_ISO, FORMAT_US_SHORT, FORMAT_US_LONG, FORMAT_ISO_DASH


class TestDateUtils:
    """Test suite for DateUtils class."""
    
    # Test parse_date method
    
    def test_parse_date_with_datetime_object(self):
        """Test parsing when input is already a datetime object."""
        dt = datetime(2025, 1, 15, 10, 30, 0)
        result = DateUtils.parse_date(dt)
        assert result == dt
    
    def test_parse_date_with_date_object(self):
        """Test parsing when input is a date object."""
        d = date(2025, 1, 15)
        result = DateUtils.parse_date(d)
        assert result == datetime(2025, 1, 15, 0, 0, 0)
    
    def test_parse_date_with_timestamp(self):
        """Test parsing Unix timestamp."""
        # Timestamp for 2025-01-15 10:30:00 UTC
        timestamp = 1736939400
        result = DateUtils.parse_date(timestamp)
        assert result.year == 2025
        assert result.month == 1
        assert result.day == 15
    
    def test_parse_date_with_iso_format(self):
        """Test parsing ISO format (YYYYMMDD)."""
        result = DateUtils.parse_date("20250115")
        assert result == datetime(2025, 1, 15)
    
    def test_parse_date_with_us_short_format(self):
        """Test parsing US short format (MM/DD/YY)."""
        result = DateUtils.parse_date("01/15/25")
        assert result == datetime(2025, 1, 15)
    
    def test_parse_date_with_us_long_format(self):
        """Test parsing US long format (MM/DD/YYYY)."""
        result = DateUtils.parse_date("01/15/2025")
        assert result == datetime(2025, 1, 15)
    
    def test_parse_date_with_iso_dash_format(self):
        """Test parsing ISO dash format (YYYY-MM-DD)."""
        result = DateUtils.parse_date("2025-01-15")
        assert result == datetime(2025, 1, 15)
    
    def test_parse_date_with_month_name(self):
        """Test parsing dates with month names."""
        test_cases = [
            ("May 16, 2025", datetime(2025, 5, 16)),
            ("16 May 2025", datetime(2025, 5, 16)),
            ("January 1, 2025", datetime(2025, 1, 1)),
            ("Dec 31, 2025", datetime(2025, 12, 31))
        ]
        
        for date_str, expected in test_cases:
            result = DateUtils.parse_date(date_str)
            assert result == expected, f"Failed to parse {date_str}"
    
    def test_parse_date_with_default_format(self):
        """Test parsing with a specified default format."""
        result = DateUtils.parse_date("2025/01/15", default_format="%Y/%m/%d")
        assert result == datetime(2025, 1, 15)
    
    def test_parse_date_with_invalid_input(self):
        """Test parsing with invalid inputs."""
        assert DateUtils.parse_date(None) is None
        assert DateUtils.parse_date("") is None
        assert DateUtils.parse_date("invalid date") is None
        assert DateUtils.parse_date("99/99/99") is None
    
    # Test format_date method
    
    def test_format_date_to_iso(self):
        """Test formatting to ISO format."""
        dt = datetime(2025, 1, 15)
        result = DateUtils.format_date(dt, FORMAT_ISO)
        assert result == "20250115"
    
    def test_format_date_to_us_short(self):
        """Test formatting to US short format."""
        dt = datetime(2025, 1, 15)
        result = DateUtils.format_date(dt, FORMAT_US_SHORT)
        assert result == "01/15/25"
    
    def test_format_date_to_us_long(self):
        """Test formatting to US long format."""
        dt = datetime(2025, 1, 15)
        result = DateUtils.format_date(dt, FORMAT_US_LONG)
        assert result == "01/15/2025"
    
    def test_format_date_from_string(self):
        """Test formatting when input is a string."""
        result = DateUtils.format_date("2025-01-15", FORMAT_ISO)
        assert result == "20250115"
    
    def test_format_date_with_invalid_input(self):
        """Test formatting with invalid input."""
        assert DateUtils.format_date("invalid") is None
        assert DateUtils.format_date(None) is None
    
    # Test date_to_iso method
    
    def test_date_to_iso(self):
        """Test conversion to ISO format."""
        test_cases = [
            ("01/15/25", "20250115"),
            ("2025-01-15", "20250115"),
            (datetime(2025, 1, 15), "20250115"),
            (date(2025, 1, 15), "20250115")
        ]
        
        for input_val, expected in test_cases:
            result = DateUtils.date_to_iso(input_val)
            assert result == expected, f"Failed to convert {input_val} to ISO"
    
    # Test date_to_us_format method
    
    def test_date_to_us_format_short(self):
        """Test conversion to US short format."""
        result = DateUtils.date_to_us_format("20250115", long_year=False)
        assert result == "01/15/25"
    
    def test_date_to_us_format_long(self):
        """Test conversion to US long format."""
        result = DateUtils.date_to_us_format("20250115", long_year=True)
        assert result == "01/15/2025"
    
    # Test get_date_before method
    
    def test_get_date_before(self):
        """Test getting date N days before."""
        result = DateUtils.get_date_before("20250115", 5)
        assert result == "20250110"
        
        result = DateUtils.get_date_before("2025-01-15", 10)
        assert result == "20250105"
    
    @freeze_time("2025-01-15")
    def test_get_date_before_with_none(self):
        """Test get_date_before with None uses current date."""
        result = DateUtils.get_date_before(None, 5)
        assert result == "20250110"
    
    # Test get_date_before_n_days method
    
    def test_get_date_before_n_days(self):
        """Test the legacy get_date_before_n_days method."""
        result = DateUtils.get_date_before_n_days(5, "20250115")
        assert result == "20250110"
    
    def test_get_date_before_n_days_invalid_format(self):
        """Test get_date_before_n_days with invalid format."""
        with pytest.raises(ValueError):
            DateUtils.get_date_before_n_days(5, "2025-01-15")
        
        with pytest.raises(ValueError):
            DateUtils.get_date_before_n_days(5, "01/15/25")
    
    # Test get_date_after method
    
    def test_get_date_after(self):
        """Test getting date N days after."""
        result = DateUtils.get_date_after("20250115", 5)
        assert result == "20250120"
        
        result = DateUtils.get_date_after("2025-01-15", 10)
        assert result == "20250125"
    
    # Test convert_unix_to_date_str method
    
    def test_convert_unix_to_date_str(self):
        """Test converting Unix timestamp to date string."""
        # Timestamp for 2025-01-15 10:30:00 UTC
        timestamp = 1736939400
        
        result = DateUtils.convert_unix_to_date_str(timestamp, FORMAT_ISO)
        assert result == "20250115"
        
        result = DateUtils.convert_unix_to_date_str(timestamp, FORMAT_US_SHORT)
        assert result == "01/15/25"
    
    def test_convert_unix_to_date_str_invalid(self):
        """Test converting invalid Unix timestamps."""
        assert DateUtils.convert_unix_to_date_str(None) is None
        assert DateUtils.convert_unix_to_date_str(0) is None
        assert DateUtils.convert_unix_to_date_str(-1) is None
        assert DateUtils.convert_unix_to_date_str("not a number") is None
    
    # Test generate_date_range method
    
    def test_generate_date_range(self):
        """Test generating date ranges."""
        result = DateUtils.generate_date_range("20250110", "20250115")
        expected = ["01/10/25", "01/11/25", "01/12/25", "01/13/25", "01/14/25", "01/15/25"]
        assert result == expected
    
    def test_generate_date_range_reverse_order(self):
        """Test generate_date_range with dates in reverse order."""
        result = DateUtils.generate_date_range("20250115", "20250110")
        expected = ["01/10/25", "01/11/25", "01/12/25", "01/13/25", "01/14/25", "01/15/25"]
        assert result == expected
    
    def test_generate_date_range_custom_format(self):
        """Test generate_date_range with custom format."""
        result = DateUtils.generate_date_range("20250110", "20250112", FORMAT_ISO)
        expected = ["20250110", "20250111", "20250112"]
        assert result == expected
    
    def test_generate_date_range_invalid(self):
        """Test generate_date_range with invalid dates."""
        result = DateUtils.generate_date_range("invalid", "20250115")
        assert result == []
        
        result = DateUtils.generate_date_range("20250110", "invalid")
        assert result == []
    
    # Test is_valid_date method
    
    def test_is_valid_date(self):
        """Test date validation."""
        valid_dates = [
            "20250115",
            "01/15/25",
            "2025-01-15",
            datetime(2025, 1, 15),
            date(2025, 1, 15),
            1736939400  # Unix timestamp
        ]
        
        for date_val in valid_dates:
            assert DateUtils.is_valid_date(date_val) is True, f"{date_val} should be valid"
        
        invalid_dates = [
            None,
            "",
            "invalid",
            "99/99/99",
            "2025-13-40",  # Invalid month/day
            []
        ]
        
        for date_val in invalid_dates:
            assert DateUtils.is_valid_date(date_val) is False, f"{date_val} should be invalid"
    
    # Test ensure_iso_format method
    
    def test_ensure_iso_format(self):
        """Test ensuring ISO format."""
        # Already in ISO format
        assert DateUtils.ensure_iso_format("20250115") == "20250115"
        
        # Needs conversion
        assert DateUtils.ensure_iso_format("01/15/25") == "20250115"
        assert DateUtils.ensure_iso_format("2025-01-15") == "20250115"
        
        # Invalid dates return original
        assert DateUtils.ensure_iso_format("invalid") == "invalid"
        assert DateUtils.ensure_iso_format(None) is None
    
    # Test current_date method
    
    @freeze_time("2025-01-15 10:30:00")
    def test_current_date(self):
        """Test getting current date."""
        assert DateUtils.current_date() == "20250115"
        assert DateUtils.current_date(FORMAT_US_SHORT) == "01/15/25"
        assert DateUtils.current_date(FORMAT_ISO_DASH) == "2025-01-15"
    
    # Test parse_date_with_fallback method
    
    def test_parse_date_with_fallback(self):
        """Test parsing with multiple format attempts."""
        formats = [FORMAT_ISO, FORMAT_US_SHORT, FORMAT_ISO_DASH]
        
        # Test ISO format
        dt, fmt = DateUtils.parse_date_with_fallback("20250115", formats)
        assert dt == datetime(2025, 1, 15)
        assert fmt == FORMAT_ISO
        
        # Test US short format
        dt, fmt = DateUtils.parse_date_with_fallback("01/15/25", formats)
        assert dt == datetime(2025, 1, 15)
        assert fmt == FORMAT_US_SHORT
        
        # Test ISO dash format
        dt, fmt = DateUtils.parse_date_with_fallback("2025-01-15", formats)
        assert dt == datetime(2025, 1, 15)
        assert fmt == FORMAT_ISO_DASH
        
        # Test invalid format
        dt, fmt = DateUtils.parse_date_with_fallback("invalid", formats)
        assert dt is None
        assert fmt is None
        
        # Test None input
        dt, fmt = DateUtils.parse_date_with_fallback(None, formats)
        assert dt is None
        assert fmt is None