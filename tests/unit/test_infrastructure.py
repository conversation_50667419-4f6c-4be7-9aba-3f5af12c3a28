"""
Test to verify that the test infrastructure is set up correctly.
"""
import pytest


class TestInfrastructure:
    """Verify test infrastructure is working."""
    
    def test_fixtures_available(self, mock_dynamodb, mock_s3, test_data_dir):
        """Test that basic fixtures are available and working."""
        # Test mock DynamoDB
        assert mock_dynamodb is not None
        # Use one of the default tables
        mock_dynamodb.put_item('pacer-dockets', {'docket_id': '1:24-cv-12345', 'name': 'test'})
        result = mock_dynamodb.get_item('pacer-dockets', {'docket_id': '1:24-cv-12345'})
        assert result['Item']['name'] == 'test'
        
        # Test mock S3
        assert mock_s3 is not None
        # Use one of the default buckets
        mock_s3.put_object('lexgenius-data', 'test-key', b'test content')
        obj = mock_s3.get_object('lexgenius-data', 'test-key')
        assert obj['Body'].read() == b'test content'
        
        # Test data directory
        assert test_data_dir.exists()
        test_file = test_data_dir / 'test.txt'
        test_file.write_text('test content')
        assert test_file.read_text() == 'test content'
    
    def test_sample_data_fixtures(self, sample_docket_data, sample_fb_ad_data):
        """Test that sample data fixtures provide expected structure."""
        # Test docket data
        assert 'docket_id' in sample_docket_data
        assert 'court' in sample_docket_data
        assert 'entries' in sample_docket_data
        assert isinstance(sample_docket_data['entries'], list)
        
        # Test FB ad data
        assert 'ad_id' in sample_fb_ad_data
        assert 'page_name' in sample_fb_ad_data
        assert 'ad_creative_body' in sample_fb_ad_data
    
    def test_markers_applied(self):
        """Test that pytest markers are applied correctly."""
        # This test should automatically get the 'unit' marker
        # based on its location in the unit/ directory
        pass
    
    @pytest.mark.slow
    def test_slow_marker(self):
        """Test that slow marker can be applied."""
        # This test would be skipped when running quick tests
        import time
        time.sleep(0.1)  # Simulate slow operation
        assert True