"""
Unit tests for DeepSeek Service
"""
import pytest
from unittest.mock import Mock, AsyncMock, patch
import json

from src.services.ai.deepseek_service import DeepSeekService


class TestDeepSeekService:
    """Test DeepSeek AI service operations"""
    
    @pytest.fixture
    def mock_client(self):
        """Create mock DeepSeek client"""
        client = Mock()
        client.make_request = AsyncMock()
        client.calculate_cost = Mock(return_value=0.001)
        return client
    
    @pytest.fixture
    def mock_prompt_manager(self):
        """Create mock prompt manager"""
        manager = Mock()
        manager.get_prompt = Mock(side_effect=lambda key: f"Mock prompt for {key}")
        return manager
    
    @pytest.fixture
    def service(self, mock_client, mock_prompt_manager):
        """Create service with mocks"""
        return DeepSeekService(mock_client, mock_prompt_manager)
    
    @pytest.mark.asyncio
    async def test_classify_ad_success(self, service, mock_client):
        """Test successful ad classification"""
        # Mock API response
        mock_client.make_request.return_value = {
            'choices': [{
                'message': {
                    'content': json.dumps({
                        'is_litigation': True,
                        'litigation_type': 'mass_tort',
                        'product_or_service': 'AFFF',
                        'defendants': ['3M', 'DuPont'],
                        'law_firm': 'Test Law Firm',
                        'confidence': 0.95,
                        'reasoning': 'Clear mass tort advertisement'
                    })
                }
            }]
        }
        
        result = await service.classify_ad(
            title="AFFF Lawsuit",
            body="Were you exposed to firefighting foam?",
            link="https://example.com",
            image_text="Call now for compensation",
            summary="AFFF mass tort case"
        )
        
        assert result['is_litigation'] is True
        assert result['litigation_type'] == 'mass_tort'
        assert result['confidence'] == 0.95
        assert 'AFFF' in result['product_or_service']
    
    @pytest.mark.asyncio
    async def test_classify_ad_parse_error(self, service, mock_client):
        """Test classification with JSON parse error"""
        # Mock invalid JSON response
        mock_client.make_request.return_value = {
            'choices': [{
                'message': {
                    'content': 'Invalid JSON response'
                }
            }]
        }
        
        result = await service.classify_ad(
            title="Test Ad",
            body="Test body",
            link="https://example.com"
        )
        
        # Should return default classification
        assert result['is_litigation'] is False
        assert result['confidence'] == 0.0
        assert result['reasoning'] == 'Failed to classify'
    
    @pytest.mark.asyncio
    async def test_extract_summary(self, service, mock_client, mock_prompt_manager):
        """Test extracting summary from text"""
        mock_client.make_request.return_value = {
            'choices': [{
                'message': {
                    'content': json.dumps({
                        'plaintiffs': ['John Doe', 'Jane Smith'],
                        'defendants': ['ABC Corp'],
                        'case_type': 'Product Liability',
                        'court': 'TXND',
                        'filing_date': '2024-12-01',
                        'summary': 'Product liability case involving defective products'
                    })
                }
            }]
        }
        
        result = await service.extract_summary(
            text="Long legal document text...",
            prompt_type='full'
        )
        
        assert 'plaintiffs' in result
        assert len(result['plaintiffs']) == 2
        assert result['case_type'] == 'Product Liability'
    
    @pytest.mark.asyncio
    async def test_extract_summary_with_chunking(self, service, mock_client, mock_prompt_manager):
        """Test summary extraction with text chunking"""
        # Create long text that triggers chunking
        long_text = "word " * 100000  # Very long text
        
        # Mock responses for each chunk
        mock_client.make_request.side_effect = [
            {
                'choices': [{
                    'message': {
                        'content': json.dumps({
                            'plaintiffs': ['John Doe'],
                            'defendants': ['ABC Corp'],
                            'summary': 'Part 1'
                        })
                    }
                }]
            },
            {
                'choices': [{
                    'message': {
                        'content': json.dumps({
                            'plaintiffs': ['Jane Smith'],
                            'defendants': ['XYZ Inc'],
                            'summary': 'Part 2'
                        })
                    }
                }]
            }
        ]
        
        with patch('src.services.ai.deepseek_service.chunk_text') as mock_chunk:
            mock_chunk.return_value = ['chunk1', 'chunk2']
            
            result = await service.extract_summary(long_text)
            
            # Should combine results from both chunks
            assert len(result['plaintiffs']) == 2
            assert len(result['defendants']) == 2
    
    @pytest.mark.asyncio
    async def test_extract_attorney_info(self, service, mock_client, mock_prompt_manager):
        """Test extracting attorney information"""
        mock_client.make_request.return_value = {
            'choices': [{
                'message': {
                    'content': json.dumps({
                        'attorneys': [
                            {'name': 'John Smith', 'firm': 'Smith & Associates'},
                            {'name': 'Jane Doe', 'firm': 'Doe Law Group'}
                        ],
                        'law_firms': ['Smith & Associates', 'Doe Law Group']
                    })
                }
            }]
        }
        
        result = await service.extract_attorney_info("Document with attorney names...")
        
        assert len(result['attorneys']) == 2
        assert len(result['law_firms']) == 2
        assert result['attorneys'][0]['name'] == 'John Smith'
    
    @pytest.mark.asyncio
    async def test_determine_court_id(self, service, mock_client):
        """Test determining court ID from description"""
        mock_client.make_request.return_value = {
            'choices': [{
                'message': {
                    'content': 'The court ID is TXND for the Northern District of Texas'
                }
            }]
        }
        
        result = await service.determine_court_id("Northern District of Texas")
        
        assert result['court_id'] == 'TXND'
        assert result['confidence'] == 0.8
    
    @pytest.mark.asyncio
    async def test_format_fb_body(self, service, mock_client):
        """Test formatting content for Facebook"""
        mock_client.make_request.return_value = {
            'choices': [{
                'message': {
                    'content': '📢 Important Legal Notice!\n\nLearn more about your rights. #LegalHelp #Justice #LawFirm'
                }
            }]
        }
        
        result = await service.format_fb_body("Plain legal content")
        
        assert '📢' in result['formatted_body']
        assert len(result['hashtags']) == 3
        assert '#LegalHelp' in result['hashtags']
    
    @pytest.mark.asyncio
    async def test_parse_law_firm_signature(self, service, mock_client, mock_prompt_manager):
        """Test parsing law firm signature page"""
        mock_client.make_request.return_value = {
            'choices': [{
                'message': {
                    'content': json.dumps({
                        'law_firm': 'Smith & Associates LLP',
                        'attorneys': ['John Smith, Esq.', 'Jane Doe, Esq.'],
                        'contact_info': {
                            'phone': '************',
                            'email': '<EMAIL>',
                            'address': '123 Legal St, Dallas, TX'
                        }
                    })
                }
            }]
        }
        
        result = await service.parse_law_firm_signature("Signature page content...")
        
        assert result['law_firm'] == 'Smith & Associates LLP'
        assert len(result['attorneys']) == 2
        assert result['contact_info']['phone'] == '************'
    
    @pytest.mark.asyncio
    async def test_validate_classification(self, service):
        """Test classification validation"""
        # Test with missing fields
        classification = {
            'is_litigation': 'yes',  # Should be boolean
            'confidence': 1.5  # Should be 0-1
        }
        
        validated = service._validate_classification(classification)
        
        assert validated['is_litigation'] is False  # Converted to boolean
        assert validated['confidence'] == 1.0  # Clamped to max
        assert 'litigation_type' in validated  # Missing field added
    
    @pytest.mark.asyncio
    async def test_extract_court_id(self, service):
        """Test court ID extraction from text"""
        # Test various patterns
        assert service._extract_court_id("Filed in TXND District") == 'TXND'
        assert service._extract_court_id("Southern District of California (CASD)") == 'CASD'
        assert service._extract_court_id("No court mentioned") is None
    
    @pytest.mark.asyncio
    async def test_extract_hashtags(self, service):
        """Test hashtag extraction"""
        text = "Check out our services! #LegalHelp #Justice #LawFirm2024"
        hashtags = service._extract_hashtags(text)
        
        assert len(hashtags) == 3
        assert '#LegalHelp' in hashtags
        assert '#Justice' in hashtags
        assert '#LawFirm2024' in hashtags