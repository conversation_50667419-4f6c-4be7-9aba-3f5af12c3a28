"""
Unit tests for Facebook Ad Archive Query Service
"""
import pytest
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime
from collections import defaultdict

from src.services.fb_archive.query_service import FBArchiveQueryService


class TestFBArchiveQueryService:
    """Test FB Archive query service operations"""
    
    @pytest.fixture
    def mock_repository(self):
        """Create mock repository"""
        repository = Mock()
        repository.query_by_archive_id = AsyncMock()
        repository.query_by_start_date = AsyncMock()
        repository.query_by_page_id = AsyncMock()
        repository.query_by_last_updated = AsyncMock()
        repository.batch_get_records = AsyncMock()
        repository.scan_all = AsyncMock()
        return repository
    
    @pytest.fixture
    def service(self, mock_repository):
        """Create service with mock repository"""
        return FBArchiveQueryService(mock_repository)
    
    @pytest.mark.asyncio
    async def test_get_ads_by_archive_ids(self, service, mock_repository):
        """Test getting ads by multiple archive IDs"""
        archive_ids = ['123456789', '987654321']
        
        # Mock query results for each ID
        mock_repository.query_by_archive_id.side_effect = [
            [
                {'AdArchiveID': '123456789', 'StartDate': '20241201'},
                {'AdArchiveID': '123456789', 'StartDate': '20241202'}
            ],
            [
                {'AdArchiveID': '987654321', 'StartDate': '20241201'}
            ]
        ]
        
        # Mock batch get results
        mock_repository.batch_get_records.return_value = [
            {'AdArchiveID': '123456789', 'StartDate': '20241201', 'LawFirm': 'Firm A'},
            {'AdArchiveID': '123456789', 'StartDate': '20241202', 'LawFirm': 'Firm A'},
            {'AdArchiveID': '987654321', 'StartDate': '20241201', 'LawFirm': 'Firm B'}
        ]
        
        results = await service.get_ads_by_archive_ids(archive_ids)
        
        assert len(results) == 3
        assert mock_repository.query_by_archive_id.call_count == 2
        assert mock_repository.batch_get_records.call_count == 1
    
    @pytest.mark.asyncio
    async def test_get_ads_by_date_range(self, service, mock_repository):
        """Test getting ads within date range"""
        start_date = '20241201'
        end_date = '20241203'
        
        # Mock responses for each date
        mock_repository.query_by_start_date.side_effect = [
            [{'AdArchiveID': '123', 'StartDate': '20241201'}],
            [{'AdArchiveID': '456', 'StartDate': '20241202'}],
            [{'AdArchiveID': '789', 'StartDate': '20241203'}]
        ]
        
        results = await service.get_ads_by_date_range(start_date, end_date)
        
        assert len(results) == 3
        assert mock_repository.query_by_start_date.call_count == 3
    
    @pytest.mark.asyncio
    async def test_get_ads_by_page_id_grouped(self, service, mock_repository):
        """Test getting ads grouped by law firm"""
        page_id = '987654321'
        
        mock_repository.query_by_page_id.return_value = [
            {'PageID': page_id, 'LawFirm': 'Firm A', 'AdArchiveID': '123'},
            {'PageID': page_id, 'LawFirm': 'Firm B', 'AdArchiveID': '456'},
            {'PageID': page_id, 'LawFirm': 'Firm A', 'AdArchiveID': '789'}
        ]
        
        grouped = await service.get_ads_by_page_id_grouped(page_id)
        
        assert len(grouped) == 2
        assert len(grouped['Firm A']) == 2
        assert len(grouped['Firm B']) == 1
    
    @pytest.mark.asyncio
    async def test_find_ads_with_summary_issues(self, service, mock_repository):
        """Test finding ads with invalid summaries"""
        mock_repository.scan_all.return_value = [
            {
                'AdArchiveID': '123',
                'Body': 'Legal services for mass tort cases',
                'Summary': 'Valid summary'
            },
            {
                'AdArchiveID': '456',
                'Body': 'Car accident lawyer available',
                'Summary': 'NA'
            },
            {
                'AdArchiveID': '789',
                'Body': 'Join our AFFF lawsuit',
                'Summary': ''
            }
        ]
        
        clean_ads, ads_with_issues = await service.find_ads_with_summary_issues()
        
        assert len(clean_ads) == 1  # Ad '789' has empty summary but no unwanted terms
        assert len(ads_with_issues) == 1  # Ad '456' has 'car accident' in body
        assert ads_with_issues[0]['AdArchiveID'] == '456'
    
    @pytest.mark.asyncio
    async def test_search_ads(self, service, mock_repository):
        """Test searching ads by term"""
        search_term = 'AFFF'
        
        mock_repository.scan_all.return_value = [
            {
                'AdArchiveID': '123',
                'Title': 'AFFF Lawsuit Information',
                'Body': 'Learn about firefighting foam cases'
            },
            {
                'AdArchiveID': '456',
                'Title': 'Legal Services',
                'Body': 'We handle AFFF contamination cases'
            },
            {
                'AdArchiveID': '789',
                'Title': 'Personal Injury',
                'Body': 'Car accident representation'
            }
        ]
        
        results = await service.search_ads(search_term)
        
        assert len(results) == 2
        assert all(search_term.lower() in str(r).lower() for r in results)
    
    @pytest.mark.asyncio
    async def test_get_statistics_by_date(self, service, mock_repository):
        """Test getting statistics for a specific date"""
        date = '20241201'
        
        mock_repository.query_by_last_updated.return_value = [
            {'PageID': '111', 'LawFirm': 'Firm A', 'IsActive': True},
            {'PageID': '222', 'LawFirm': 'Firm A', 'IsActive': True},
            {'PageID': '111', 'LawFirm': 'Firm B', 'IsActive': False},
            {'PageID': '333', 'LawFirm': 'Firm C', 'IsActive': True}
        ]
        
        stats = await service.get_statistics_by_date(date)
        
        assert stats['total_ads'] == 4
        assert stats['unique_pages'] == 3
        assert stats['unique_law_firms'] == 3
        assert stats['active_ads'] == 3
        assert stats['inactive_ads'] == 1
        assert stats['ads_by_law_firm']['Firm A'] == 2
        assert stats['ads_by_page']['111'] == 2
    
    @pytest.mark.asyncio
    async def test_find_duplicate_ads(self, service, mock_repository):
        """Test finding duplicate ads based on content"""
        mock_repository.scan_all.return_value = [
            {
                'AdArchiveID': '123',
                'Title': 'Same Title',
                'Body': 'Same Body',
                'LinkDescription': 'Same Link'
            },
            {
                'AdArchiveID': '456',
                'Title': 'Same Title',
                'Body': 'Same Body',
                'LinkDescription': 'Same Link'
            },
            {
                'AdArchiveID': '789',
                'Title': 'Different Title',
                'Body': 'Different Body',
                'LinkDescription': 'Different Link'
            }
        ]
        
        duplicates = await service.find_duplicate_ads()
        
        assert len(duplicates) == 1  # One group of duplicates
        duplicate_group = list(duplicates.values())[0]
        assert len(duplicate_group) == 2
        assert any(ad['AdArchiveID'] == '123' for ad in duplicate_group)
        assert any(ad['AdArchiveID'] == '456' for ad in duplicate_group)
    
    @pytest.mark.asyncio
    async def test_get_unique_archive_ids_by_page(self, service, mock_repository):
        """Test getting unique archive IDs for a page"""
        page_id = '987654321'
        
        mock_repository.query_by_page_id.return_value = [
            {'PageID': page_id, 'AdArchiveID': '123'},
            {'PageID': page_id, 'AdArchiveID': '456'},
            {'PageID': page_id, 'AdArchiveID': '123'},  # Duplicate
            {'PageID': page_id, 'AdArchiveID': '789'}
        ]
        
        unique_ids = await service.get_unique_archive_ids_by_page(page_id)
        
        assert len(unique_ids) == 3
        assert '123' in unique_ids
        assert '456' in unique_ids
        assert '789' in unique_ids
        assert unique_ids == sorted(unique_ids)  # Should be sorted