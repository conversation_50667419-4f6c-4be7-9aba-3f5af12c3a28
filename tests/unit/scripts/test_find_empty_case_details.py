"""Tests for find_empty_case_details utility script."""
import json
import tempfile
from pathlib import Path
from unittest.mock import patch
import pytest

from scripts.utils.find_empty_case_details import find_empty_case_details


@pytest.fixture
def temp_data_dir():
    """Create temporary data directory with test JSON files."""
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # Create test date directory
        date_dir = temp_path / "data" / "20250101" / "dockets"
        date_dir.mkdir(parents=True)
        
        # Test files with different combinations of empty fields
        test_files = [
            # Should match - all criteria met
            {
                "filename": "test1_match.json",
                "data": {"mdl_num": "NA", "title": "", "allegations": ""}
            },
            # Should match - null values
            {
                "filename": "test2_match.json", 
                "data": {"mdl_num": "NA", "title": None, "allegations": None}
            },
            # Should not match - has MDL number
            {
                "filename": "test3_no_match.json",
                "data": {"mdl_num": "2741", "title": "", "allegations": ""}
            },
            # Should not match - has title
            {
                "filename": "test4_no_match.json",
                "data": {"mdl_num": "NA", "title": "Some Title", "allegations": ""}
            },
            # Should not match - has allegations
            {
                "filename": "test5_no_match.json",
                "data": {"mdl_num": "NA", "title": "", "allegations": "Some allegations"}
            },
            # Should match - empty string mdl_num
            {
                "filename": "test6_match.json",
                "data": {"mdl_num": "", "title": "", "allegations": ""}
            },
        ]
        
        for test_file in test_files:
            file_path = date_dir / test_file["filename"]
            with open(file_path, 'w') as f:
                json.dump(test_file["data"], f)
        
        # Change working directory to temp directory
        original_cwd = Path.cwd()
        import os
        os.chdir(temp_dir)
        
        yield temp_dir
        
        # Restore original working directory
        os.chdir(original_cwd)


class TestFindEmptyCaseDetails:
    """Test find_empty_case_details functionality."""
    
    def test_find_empty_case_details_success(self, temp_data_dir):
        """Test successful identification of files with empty case details."""
        with patch('builtins.print'):  # Suppress console output during tests
            find_empty_case_details("20250101")
        
        # Since the function prints to console, we'll test the logic separately
        # by checking files directly
        date_dir = Path(temp_data_dir) / "data" / "20250101" / "dockets"
        json_files = list(date_dir.glob("*.json"))
        
        assert len(json_files) == 6
        
        matching_files = []
        for json_file in json_files:
            with open(json_file) as f:
                data = json.load(f)
            
            mdl_num = data.get('mdl_num', '')
            title = data.get('title', '')
            allegations = data.get('allegations', '')
            
            # Check for empty values: "", null, or "NA"
            mdl_empty = mdl_num in ["NA", "", None]
            title_empty = title in ["", None]
            allegations_empty = allegations in ["", None]
            
            if mdl_empty and title_empty and allegations_empty:
                matching_files.append(str(json_file.absolute()))
        
        # Should find 3 matching files
        assert len(matching_files) == 3
        
        # Check that the correct files match
        matching_names = [Path(f).name for f in matching_files]
        expected_matches = {"test1_match.json", "test2_match.json", "test6_match.json"}
        assert set(matching_names) == expected_matches
    
    def test_find_empty_case_details_no_directory(self, temp_data_dir):
        """Test handling of non-existent directory."""
        with patch('sys.exit') as mock_exit:
            with patch('builtins.print'):  # Suppress console output
                find_empty_case_details("20251231")
            mock_exit.assert_called_once_with(1)
    
    def test_find_empty_case_details_no_json_files(self, temp_data_dir):
        """Test handling when no JSON files exist."""
        # Create empty directory
        empty_dir = Path(temp_data_dir) / "data" / "20250102" / "dockets"
        empty_dir.mkdir(parents=True)
        
        with patch('builtins.print'):  # Suppress console output
            find_empty_case_details("20250102")
        
        # Should complete without error
    
    def test_find_empty_case_details_malformed_json(self, temp_data_dir):
        """Test handling of malformed JSON files."""
        date_dir = Path(temp_data_dir) / "data" / "20250103" / "dockets"
        date_dir.mkdir(parents=True)
        
        # Create malformed JSON file
        bad_file = date_dir / "bad.json"
        with open(bad_file, 'w') as f:
            f.write('{"invalid": json}')
        
        # Create valid file for comparison
        good_file = date_dir / "good.json"
        with open(good_file, 'w') as f:
            json.dump({"mdl_num": "NA", "title": "", "allegations": ""}, f)
        
        with patch('builtins.print'):  # Suppress console output
            find_empty_case_details("20250103")
        
        # Should handle the error gracefully and continue processing
    
    def test_find_empty_case_details_missing_fields(self, temp_data_dir):
        """Test handling when required fields are missing from JSON."""
        date_dir = Path(temp_data_dir) / "data" / "20250104" / "dockets"
        date_dir.mkdir(parents=True)
        
        # Create file with missing fields
        incomplete_file = date_dir / "incomplete.json"
        with open(incomplete_file, 'w') as f:
            json.dump({"other_field": "value"}, f)
        
        with patch('builtins.print'):  # Suppress console output
            find_empty_case_details("20250104")
        
        # Should handle missing fields gracefully (defaults to empty strings)


if __name__ == "__main__":
    pytest.main([__file__])