# LexGenius Test Infrastructure

This directory contains the comprehensive test suite for the LexGenius project, organized according to the testing pyramid principle.

## Directory Structure

```
tests/
├── __init__.py          # Test suite initialization
├── conftest.py          # Shared pytest fixtures and configuration
├── unit/                # Unit tests for isolated components
├── integration/         # Integration tests for component interactions  
├── e2e/                 # End-to-end tests for complete workflows
└── mocks/               # Mock implementations for external services
    ├── mock_dynamodb.py # In-memory DynamoDB mock
    └── mock_s3.py       # In-memory S3 mock
```

## Test Categories

### Unit Tests (`tests/unit/`)
- Test individual functions and classes in isolation
- Use mocks for all external dependencies
- Should be fast and deterministic
- Focus on edge cases and error handling

### Integration Tests (`tests/integration/`)
- Test interactions between multiple components
- May use real services in test mode or sophisticated mocks
- Verify data flow and transformations
- Test error propagation between components

### End-to-End Tests (`tests/e2e/`)
- Test complete workflows from start to finish
- Simulate real user scenarios
- May require more setup and take longer to run
- Provide confidence that the system works as a whole

## Running Tests

### Install Test Dependencies
```bash
pip install -r requirements-test.txt
```

### Run All Tests
```bash
pytest
# or
python run_tests.py
```

### Run Specific Test Types
```bash
# Unit tests only
pytest -m unit
python run_tests.py unit

# Integration tests only  
pytest -m integration
python run_tests.py integration

# E2E tests only
pytest -m e2e
python run_tests.py e2e

# Quick tests (exclude slow tests)
pytest -m "not slow"
python run_tests.py quick
```

### Run with Coverage
```bash
pytest --cov=src --cov-report=html
python run_tests.py --coverage
```

### Run in Parallel
```bash
pytest -n auto
python run_tests.py --parallel
```

### Run Tests for Specific Module
```bash
# Test only date utilities
python run_tests.py --module date_utils

# Test only s3 manager
python run_tests.py --module s3_manager
```

## Writing Tests

### Using Fixtures

The `conftest.py` file provides numerous fixtures for common test scenarios:

```python
def test_docket_processing(sample_docket_data, mock_dynamodb, test_data_dir):
    """Example test using fixtures."""
    # sample_docket_data provides test data
    # mock_dynamodb provides an in-memory DynamoDB
    # test_data_dir provides a temporary directory
    
    processor = DocketProcessor(mock_dynamodb)
    result = processor.process(sample_docket_data)
    assert result['status'] == 'success'
```

### Available Fixtures

#### Environment and Path Fixtures
- `test_data_dir`: Temporary data directory with LEXGENIUS_DATA_DIR set
- `test_config_dir`: Temporary config directory
- `sample_date`: Consistent test date (2025-01-15)
- `sample_datetime`: Consistent test datetime

#### Mock Service Fixtures
- `mock_dynamodb`: In-memory DynamoDB manager
- `mock_s3`: In-memory S3 manager
- `mock_gpt_client`: Mocked GPT client
- `mock_pacer_session`: Mocked PACER session

#### Sample Data Fixtures
- `sample_docket_data`: Sample court docket data
- `sample_fb_ad_data`: Sample Facebook ad data
- `sample_config_data`: Sample configuration
- `sample_mdl_data`: Sample MDL data

#### File System Fixtures
- `create_test_files`: Helper to create test files
- `sample_pdf_file`: Creates a minimal PDF file
- `sample_image_file`: Creates a minimal image file

### Test Markers

Tests can be marked with these markers:

```python
@pytest.mark.unit
def test_something():
    """This is a unit test."""
    pass

@pytest.mark.slow
def test_heavy_processing():
    """This test takes more than 5 seconds."""
    pass

@pytest.mark.requires_aws
def test_s3_integration():
    """This test requires AWS credentials."""
    pass
```

## Mock Implementations

### MockDynamoDBManager

Provides an in-memory implementation of DynamoDB operations:

```python
mock_db = MockDynamoDBManager()

# Put item
mock_db.put_item('table-name', {'id': '123', 'data': 'test'})

# Get item
result = mock_db.get_item('table-name', {'id': '123'})

# Query
results = mock_db.query('table-name', {'status': 'active'})

# Scan
all_items = mock_db.scan('table-name')
```

### MockS3Manager

Provides an in-memory implementation of S3 operations:

```python
mock_s3 = MockS3Manager()

# Put object
mock_s3.put_object('bucket', 'key', b'content')

# Get object
obj = mock_s3.get_object('bucket', 'key')
content = obj['Body'].read()

# List objects
objects = mock_s3.list_objects_v2('bucket', prefix='folder/')

# Delete object
mock_s3.delete_object('bucket', 'key')
```

## Best Practices

1. **Test Isolation**: Each test should be independent and not rely on the state from other tests

2. **Use Fixtures**: Leverage pytest fixtures for common setup and teardown operations

3. **Mock External Services**: Use the provided mocks or create new ones for external dependencies

4. **Test Names**: Use descriptive test names that explain what is being tested

5. **Arrange-Act-Assert**: Structure tests with clear setup, execution, and verification phases

6. **Test Edge Cases**: Don't just test the happy path; test error conditions and edge cases

7. **Keep Tests Fast**: Unit tests should run in milliseconds, not seconds

8. **Use Markers**: Apply appropriate markers to help organize and filter tests

## Continuous Integration

Tests are automatically run on:
- Every push to a feature branch
- Every pull request
- Nightly on the main branch

Failed tests will block merges to the main branch.

## Phase 1 Test Suite

The current test suite includes comprehensive tests for Phase 1 of the refactoring plan:

### Test Files
- **test_base_config.py**: Tests for BaseConfig, ScraperConfig, and validators
- **test_config_adapter.py**: Tests for backward compatibility adapter
- **test_config_loader.py**: Tests for YAML configuration loading
- **test_config_models.py**: Tests for all configuration model classes
- **test_json_safety.py**: Tests for safe JSON operations
- **test_phase1_integration.py**: End-to-end integration tests
- **generate_coverage_report.py**: Coverage report generator with progress tracking

### Generate Coverage Report
To generate a comprehensive coverage report with Phase 1 progress:

```bash
python tests/generate_coverage_report.py
```

This will:
- Run all Phase 1 tests with coverage measurement
- Generate HTML, XML, and text coverage reports
- Display Phase 1 implementation progress
- Save reports in `tests/coverage_reports/`

## Phase 1 Progress Summary

### ✅ Completed (100%)
1. **Configuration Models**: Type-safe config classes with validation
2. **Config Adapter**: Backward compatibility layer
3. **JSON Safety**: Safe file operations with atomic writes
4. **Configuration Loading**: YAML support with validation
5. **Migration Tools**: Automated config migration script

### 🔄 In Progress (90%)
- **Integration**: Most library files updated, some scripts remaining

### 📊 Coverage Metrics
- Config models: 95%+ coverage
- Config adapter: 95%+ coverage  
- JSON safety: 90%+ coverage
- Integration tests: 85%+ coverage

## Troubleshooting

### Import Errors
Make sure the src directory is in the Python path. The conftest.py file handles this automatically, but if running tests manually:

```bash
export PYTHONPATH="${PYTHONPATH}:${PWD}/src"
```

### Missing Dependencies
Install all test requirements:

```bash
pip install -r requirements-test.txt
```

### Slow Tests
Run only fast tests during development:

```bash
pytest -m "not slow"
```

### Debugging Failed Tests
Run with verbose output and stop on first failure:

```bash
pytest -vv -x --tb=long
```