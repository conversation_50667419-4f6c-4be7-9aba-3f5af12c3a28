"""Integration tests for FB Ads Orchestrator async initialization."""
import pytest
from unittest.mock import Mock, patch, AsyncMock
import aiohttp
from src.fb_ads.orchestrator import FacebookAdsOrchestrator


class TestFacebookAdsOrchestratorAsync:
    """Test FB Ads Orchestrator async initialization and cleanup."""

    @pytest.fixture
    def test_config(self):
        """Test configuration for orchestrator."""
        return {
            'iso_date': '20241212',
            'bucket_name': 'test-bucket',
            'aws_access_key_id': 'test-key',
            'aws_secret_access_key': 'test-secret',
            'region_name': 'us-west-2',
            'dynamodb': {
                'fb_ad_archive_table_name': 'test-FBAdArchive',
                'fb_image_hash_table_name': 'test-FBImageHash',
                'law_firms_table_name': 'test-LawFirms'
            },
            'fb_ad_archive_table_name': 'test-FBAdArchive',
            'fb_image_hash_table_name': 'test-FBImageHash',
            'law_firms_table_name': 'test-LawFirms',
            'use_local_dynamodb': False,
            'dynamodb_endpoint': None,
            'defer_image_processing': True,
            'image_queue_dir': './test_image_queue',
            'default_date_range_days': 14
        }

    @pytest.fixture
    async def mock_session(self):
        """Create mock aiohttp session."""
        session = Mock(spec=aiohttp.ClientSession)
        session.close = AsyncMock()
        return session

    @pytest.mark.asyncio
    async def test_orchestrator_initialization(self, test_config, mock_session):
        """Test orchestrator initialization without async_init."""
        with patch('src.fb_ads.orchestrator.S3AsyncStorage') as mock_s3_class:
            with patch('src.fb_ads.orchestrator.FacebookSessionManager'):
                with patch('src.fb_ads.orchestrator.FacebookAPIClient'):
                    with patch('src.fb_ads.orchestrator.ImageHandler'):
                        
                        orchestrator = FacebookAdsOrchestrator(test_config, mock_session)
                        
                        # Should initialize synchronously without errors
                        assert orchestrator.config == test_config
                        assert orchestrator.aiohttp_session == mock_session
                        assert orchestrator.end_date_iso == '20241212'
                        assert orchestrator._services_initialized is False
    
    @pytest.mark.asyncio
    async def test_orchestrator_with_defer_processing_enabled(self, mock_session):
        """Test orchestrator initialization with defer_image_processing enabled."""
        config_with_defer = {
            'iso_date': '20241212',
            'bucket_name': 'test-bucket',
            'aws_access_key_id': 'test-key',
            'aws_secret_access_key': 'test-secret',
            'region_name': 'us-west-2',
            'dynamodb': {
                'fb_ad_archive_table_name': 'test-FBAdArchive',
                'fb_image_hash_table_name': 'test-FBImageHash',
                'law_firms_table_name': 'test-LawFirms'
            },
            'fb_ad_archive_table_name': 'test-FBAdArchive',
            'fb_image_hash_table_name': 'test-FBImageHash',
            'law_firms_table_name': 'test-LawFirms',
            'use_local_dynamodb': False,
            'dynamodb_endpoint': None,
            'defer_image_processing': True,
            'image_queue_dir': './test_image_queue',
            'image_queue': {
                'directory': './test_image_queue',
                'batch_size': 100,
                'cleanup_days': 30
            },
            'default_date_range_days': 14
        }
        
        with patch('src.fb_ads.orchestrator.S3AsyncStorage'), \
             patch('src.fb_ads.orchestrator.FacebookSessionManager'), \
             patch('src.fb_ads.orchestrator.FacebookAPIClient'), \
             patch('src.fb_ads.orchestrator.ImageHandler'), \
             patch('src.fb_ads.orchestrator.AdProcessor') as mock_processor_class:
            
            # Mock the AdProcessor to verify it receives the defer processing config
            mock_processor = Mock()
            mock_processor_class.return_value = mock_processor
            
            orchestrator = FacebookAdsOrchestrator(config_with_defer, mock_session)
            
            # Verify orchestrator was initialized
            assert orchestrator is not None
            
            # Verify AdProcessor was called with config containing defer_image_processing
            mock_processor_class.assert_called_once()
            call_args = mock_processor_class.call_args
            passed_config = call_args[0][0]  # First positional argument is config
            
            assert passed_config['defer_image_processing'] is True
            assert passed_config['image_queue_dir'] == './test_image_queue'
    
    @pytest.mark.asyncio
    async def test_orchestrator_with_defer_processing_disabled(self, mock_session):
        """Test orchestrator initialization with defer_image_processing disabled."""
        config_without_defer = {
            'iso_date': '20241212',
            'bucket_name': 'test-bucket',
            'aws_access_key_id': 'test-key',
            'aws_secret_access_key': 'test-secret',
            'region_name': 'us-west-2',
            'dynamodb': {
                'fb_ad_archive_table_name': 'test-FBAdArchive',
                'fb_image_hash_table_name': 'test-FBImageHash',
                'law_firms_table_name': 'test-LawFirms'
            },
            'fb_ad_archive_table_name': 'test-FBAdArchive',
            'fb_image_hash_table_name': 'test-FBImageHash',
            'law_firms_table_name': 'test-LawFirms',
            'use_local_dynamodb': False,
            'dynamodb_endpoint': None,
            'defer_image_processing': False,  # Explicitly disabled
            'default_date_range_days': 14
        }
        
        with patch('src.fb_ads.orchestrator.S3AsyncStorage'), \
             patch('src.fb_ads.orchestrator.FacebookSessionManager'), \
             patch('src.fb_ads.orchestrator.FacebookAPIClient'), \
             patch('src.fb_ads.orchestrator.ImageHandler'), \
             patch('src.fb_ads.orchestrator.AdProcessor') as mock_processor_class:
            
            # Mock the AdProcessor to verify it receives the defer processing config
            mock_processor = Mock()
            mock_processor_class.return_value = mock_processor
            
            orchestrator = FacebookAdsOrchestrator(config_without_defer, mock_session)
            
            # Verify orchestrator was initialized
            assert orchestrator is not None
            
            # Verify AdProcessor was called with config containing defer_image_processing = False
            mock_processor_class.assert_called_once()
            call_args = mock_processor_class.call_args
            passed_config = call_args[0][0]  # First positional argument is config
            
            assert passed_config['defer_image_processing'] is False

    @pytest.mark.asyncio
    async def test_async_init_success(self, test_config, mock_session):
        """Test successful async initialization."""
        with patch('src.fb_ads.orchestrator.S3AsyncStorage') as mock_s3_class:
            with patch('src.fb_ads.orchestrator.FacebookSessionManager'):
                with patch('src.fb_ads.orchestrator.FacebookAPIClient'):
                    with patch('src.fb_ads.orchestrator.ImageHandler'):
                        with patch('src.fb_ads.orchestrator.AsyncDynamoDBStorage') as mock_async_storage_class:
                            with patch('src.fb_ads.orchestrator.LawFirmsRepository') as mock_law_firms_repo_class:
                                with patch('src.fb_ads.orchestrator.FBArchiveRepository') as mock_fb_repo_class:
                                    
                                    # Setup mocks
                                    mock_s3 = Mock()
                                    mock_s3._initialize = AsyncMock()
                                    mock_s3_class.return_value = mock_s3
                                    
                                    mock_storage = Mock()
                                    mock_storage.__aenter__ = AsyncMock(return_value=mock_storage)
                                    mock_async_storage_class.return_value = mock_storage
                                    
                                    mock_law_firms_repo = Mock()
                                    mock_law_firms_repo_class.return_value = mock_law_firms_repo
                                    
                                    mock_fb_repo = Mock()
                                    mock_fb_repo_class.return_value = mock_fb_repo
                                    
                                    orchestrator = FacebookAdsOrchestrator(test_config, mock_session)
                                    
                                    # Run async initialization
                                    await orchestrator.async_init()
                                    
                                    # Verify S3 initialization was called
                                    mock_s3._initialize.assert_called_once()
                                    
                                    # Verify async storage was initialized
                                    mock_async_storage_class.assert_called_once_with(test_config)
                                    mock_storage.__aenter__.assert_called_once()
                                    
                                    # Verify repositories were created
                                    mock_law_firms_repo_class.assert_called_once_with(mock_storage)
                                    mock_fb_repo_class.assert_called_once_with(mock_storage)
                                    
                                    # Verify services are marked as initialized
                                    assert orchestrator._services_initialized is True
                                    
                                    # Verify repository references are set
                                    assert orchestrator.law_firm_repo == mock_law_firms_repo
                                    assert orchestrator.fb_archive_repo == mock_fb_repo

    @pytest.mark.asyncio
    async def test_async_init_s3_failure(self, test_config, mock_session):
        """Test async initialization with S3 failure."""
        with patch('src.fb_ads.orchestrator.S3AsyncStorage') as mock_s3_class:
            with patch('src.fb_ads.orchestrator.FacebookSessionManager'):
                with patch('src.fb_ads.orchestrator.FacebookAPIClient'):
                    with patch('src.fb_ads.orchestrator.ImageHandler'):
                        
                        # Setup S3 to fail initialization
                        mock_s3 = Mock()
                        mock_s3._initialize = AsyncMock(side_effect=Exception("S3 connection failed"))
                        mock_s3_class.return_value = mock_s3
                        
                        orchestrator = FacebookAdsOrchestrator(test_config, mock_session)
                        
                        # Should raise exception during async_init
                        with pytest.raises(Exception, match="S3 connection failed"):
                            await orchestrator.async_init()
                        
                        # Services should not be marked as initialized
                        assert orchestrator._services_initialized is False

    @pytest.mark.asyncio
    async def test_async_init_storage_failure(self, test_config, mock_session):
        """Test async initialization with storage failure."""
        with patch('src.fb_ads.orchestrator.S3AsyncStorage') as mock_s3_class:
            with patch('src.fb_ads.orchestrator.FacebookSessionManager'):
                with patch('src.fb_ads.orchestrator.FacebookAPIClient'):
                    with patch('src.fb_ads.orchestrator.ImageHandler'):
                        with patch('src.fb_ads.orchestrator.AsyncDynamoDBStorage') as mock_async_storage_class:
                            
                            # Setup S3 to succeed
                            mock_s3 = Mock()
                            mock_s3._initialize = AsyncMock()
                            mock_s3_class.return_value = mock_s3
                            
                            # Setup storage to fail
                            mock_async_storage_class.side_effect = Exception("DynamoDB connection failed")
                            
                            orchestrator = FacebookAdsOrchestrator(test_config, mock_session)
                            
                            # Should raise exception during async_init
                            with pytest.raises(Exception, match="Failed to initialize async repositories"):
                                await orchestrator.async_init()

    @pytest.mark.asyncio
    async def test_async_init_idempotent(self, test_config, mock_session):
        """Test that async_init can be called multiple times safely."""
        with patch('src.fb_ads.orchestrator.S3AsyncStorage') as mock_s3_class:
            with patch('src.fb_ads.orchestrator.FacebookSessionManager'):
                with patch('src.fb_ads.orchestrator.FacebookAPIClient'):
                    with patch('src.fb_ads.orchestrator.ImageHandler'):
                        with patch('src.fb_ads.orchestrator.AsyncDynamoDBStorage') as mock_async_storage_class:
                            with patch('src.fb_ads.orchestrator.LawFirmsRepository'):
                                with patch('src.fb_ads.orchestrator.FBArchiveRepository'):
                                    
                                    # Setup mocks
                                    mock_s3 = Mock()
                                    mock_s3._initialize = AsyncMock()
                                    mock_s3_class.return_value = mock_s3
                                    
                                    mock_storage = Mock()
                                    mock_storage.__aenter__ = AsyncMock(return_value=mock_storage)
                                    mock_async_storage_class.return_value = mock_storage
                                    
                                    orchestrator = FacebookAdsOrchestrator(test_config, mock_session)
                                    
                                    # Call async_init multiple times
                                    await orchestrator.async_init()
                                    await orchestrator.async_init()
                                    await orchestrator.async_init()
                                    
                                    # S3 should only be initialized once
                                    mock_s3._initialize.assert_called_once()
                                    
                                    # Storage should only be created once
                                    mock_async_storage_class.assert_called_once()

    @pytest.mark.asyncio
    async def test_cleanup_success(self, test_config, mock_session):
        """Test successful cleanup of resources."""
        with patch('src.fb_ads.orchestrator.S3AsyncStorage') as mock_s3_class:
            with patch('src.fb_ads.orchestrator.FacebookSessionManager'):
                with patch('src.fb_ads.orchestrator.FacebookAPIClient'):
                    with patch('src.fb_ads.orchestrator.ImageHandler'):
                        with patch('src.fb_ads.orchestrator.BandwidthLogger') as mock_bandwidth_class:
                            
                            # Setup mocks
                            mock_s3 = Mock()
                            mock_s3.close = AsyncMock()
                            mock_s3_class.return_value = mock_s3
                            
                            mock_bandwidth = Mock()
                            mock_bandwidth.save_summary = Mock()
                            mock_bandwidth_class.return_value = mock_bandwidth
                            
                            orchestrator = FacebookAdsOrchestrator(test_config, mock_session)
                            orchestrator.bandwidth_logger = mock_bandwidth
                            
                            # Add mock async storage
                            mock_storage = Mock()
                            mock_storage.__aexit__ = AsyncMock()
                            orchestrator._async_storage = mock_storage
                            
                            # Run cleanup
                            await orchestrator.cleanup()
                            
                            # Verify cleanup calls
                            mock_storage.__aexit__.assert_called_once_with(None, None, None)
                            mock_s3.close.assert_called_once()
                            mock_session.close.assert_called_once()
                            mock_bandwidth.save_summary.assert_called_once()

    @pytest.mark.asyncio
    async def test_cleanup_with_errors(self, test_config, mock_session):
        """Test cleanup handles errors gracefully."""
        with patch('src.fb_ads.orchestrator.S3AsyncStorage') as mock_s3_class:
            with patch('src.fb_ads.orchestrator.FacebookSessionManager'):
                with patch('src.fb_ads.orchestrator.FacebookAPIClient'):
                    with patch('src.fb_ads.orchestrator.ImageHandler'):
                        
                        # Setup S3 to fail during cleanup
                        mock_s3 = Mock()
                        mock_s3.close = AsyncMock(side_effect=Exception("S3 cleanup failed"))
                        mock_s3_class.return_value = mock_s3
                        
                        orchestrator = FacebookAdsOrchestrator(test_config, mock_session)
                        
                        # Add mock async storage that also fails
                        mock_storage = Mock()
                        mock_storage.__aexit__ = AsyncMock(side_effect=Exception("Storage cleanup failed"))
                        orchestrator._async_storage = mock_storage
                        
                        # Cleanup should not raise exceptions
                        await orchestrator.cleanup()
                        
                        # Verify attempts were made despite errors
                        mock_storage.__aexit__.assert_called_once()
                        mock_s3.close.assert_called_once()
                        mock_session.close.assert_called_once()

    @pytest.mark.asyncio
    async def test_run_method_calls_async_init(self, test_config, mock_session):
        """Test that run method calls async_init."""
        with patch('src.fb_ads.orchestrator.S3AsyncStorage'):
            with patch('src.fb_ads.orchestrator.FacebookSessionManager'):
                with patch('src.fb_ads.orchestrator.FacebookAPIClient'):
                    with patch('src.fb_ads.orchestrator.ImageHandler'):
                        
                        orchestrator = FacebookAdsOrchestrator(test_config, mock_session)
                        
                        # Mock async_init and run_full_scrape
                        orchestrator.async_init = AsyncMock()
                        orchestrator.run_full_scrape = AsyncMock(return_value=True)
                        
                        result = await orchestrator.run()
                        
                        # Verify async_init was called
                        orchestrator.async_init.assert_called_once()
                        
                        # Verify run_full_scrape was called
                        orchestrator.run_full_scrape.assert_called_once()
                        
                        assert result is True


if __name__ == '__main__':
    pytest.main([__file__])