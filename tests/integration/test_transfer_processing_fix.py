"""
Integration test for transfer processing fix.

Tests the complete fix for the pending_cto issue where MDL lookup 
was returning empty due to async initialization problems.
"""
import asyncio
import pytest
from unittest.mock import AsyncMock, MagicMock
import pandas as pd

from src.data_transformer.transfer_handler import TransferHandler
from src.data_transformer.mdl_processor import MDLProcessor


class TestTransferProcessingIntegration:
    """Integration tests for the complete transfer processing fix."""
    
    @pytest.fixture
    def mock_config(self):
        """Mock configuration."""
        return {
            'directories': {'base_dir': '/tmp/test'},
            'test_config': True
        }
    
    @pytest.fixture
    def mock_pacer_db(self):
        """Mock PacerRepository."""
        mock_pacer = AsyncMock()
        return mock_pacer
    
    @pytest.fixture
    def mock_district_court_db(self):
        """Mock DistrictCourtsRepository with real MDL data."""
        mock_courts = AsyncMock()
        mock_courts.scan_all = AsyncMock(return_value=[
            {'MdlNum': '2741', 'CourtId': 'cand'},  # Roundup -> Northern District of California
            {'MdlNum': '2873', 'CourtId': 'scd'},   # AFFF -> South Carolina
            {'MdlNum': '3092', 'CourtId': 'njd'},   # Depo-Provera -> New Jersey
            {'MdlNum': '2775', 'CourtId': 'cand'},  # Another case -> Northern District of California
        ])
        return mock_courts
    
    @pytest.fixture
    def mock_mdl_dataframe(self):
        """Mock MDL litigation DataFrame."""
        return pd.DataFrame([
            {'mdl_num': '2741', 'litigation': 'Roundup', 'description': 'Roundup Products Liability Litigation'},
            {'mdl_num': '2873', 'litigation': 'AFFF', 'description': 'AFFF Products Liability Litigation'},
            {'mdl_num': '3092', 'litigation': 'Depo-Provera', 'description': 'Depo-Provera Products Liability Litigation'}
        ])
    
    @pytest.fixture
    def mdl_processor_with_db(self, mock_config, mock_district_court_db, mock_mdl_dataframe):
        """Create MDLProcessor with proper district court DB."""
        mock_file_handler = MagicMock()
        mock_gpt = MagicMock()
        
        processor = MDLProcessor(
            mdl_litigations=mock_mdl_dataframe,
            mdl_path='/tmp/test_mdl.json',
            file_handler=mock_file_handler,
            gpt=mock_gpt,
            config=mock_config,
            district_court_db=mock_district_court_db
        )
        
        # Simulate the async initialization fix
        processor.update_district_court_db(mock_district_court_db)
        
        return processor
    
    @pytest.fixture 
    def transfer_handler_integrated(self, mock_config, mock_pacer_db, mock_district_court_db, mdl_processor_with_db):
        """Create TransferHandler with integrated MDL processor."""
        return TransferHandler(
            mock_config, 
            mock_pacer_db, 
            mock_district_court_db, 
            mdl_processor_with_db
        )
    
    @pytest.mark.asyncio
    async def test_roundup_case_pending_cto_fix(self, transfer_handler_integrated):
        """Test the complete fix for the Roundup pending CTO issue."""
        # This is the exact case that was failing before the fix
        test_data = {
            'court_id': 'nmd',  # New Mexico District
            'docket_num': '1:25-cv-00547',
            'versus': 'Labbe v. Monsanto Company',
            'mdl_num': '2741',  # Roundup MDL
            'new_filename': 'nmd_25_00547_Labbe_v_Monsanto_Company',
            'is_removal': False
        }
        
        # Process transfers using the fixed implementation
        await transfer_handler_integrated.process_transfers(test_data)
        
        # Should now correctly identify as pending CTO
        assert test_data['pending_cto'] is True, "Roundup case in NM should be pending CTO"
        assert test_data['is_transferred'] is False
        assert test_data['transferred_in'] is False
        assert test_data['is_removal'] is False
    
    @pytest.mark.asyncio
    async def test_multiple_mdl_scenarios(self, transfer_handler_integrated):
        """Test multiple MDL scenarios to ensure rules work correctly."""
        
        test_cases = [
            # Rule 2: Pending CTO cases
            {
                'name': 'AFFF case in wrong court',
                'data': {
                    'court_id': 'txd', 'mdl_num': '2873', 'new_filename': 'test_afff_pending'
                },
                'expected': {'pending_cto': True, 'is_transferred': False, 'transferred_in': False}
            },
            {
                'name': 'Depo-Provera case in wrong court', 
                'data': {
                    'court_id': 'cad', 'mdl_num': '3092', 'new_filename': 'test_depo_pending'
                },
                'expected': {'pending_cto': True, 'is_transferred': False, 'transferred_in': False}
            },
            
            # Rule 3: Already in correct MDL court
            {
                'name': 'Roundup case already in cand',
                'data': {
                    'court_id': 'cand', 'mdl_num': '2741', 'new_filename': 'test_roundup_in_court'
                },
                'expected': {'pending_cto': False, 'is_transferred': False, 'transferred_in': False}
            },
            {
                'name': 'AFFF case already in scd',
                'data': {
                    'court_id': 'scd', 'mdl_num': '2873', 'new_filename': 'test_afff_in_court'
                },
                'expected': {'pending_cto': False, 'is_transferred': False, 'transferred_in': False}
            },
            
            # Rule 4: Transferred into MDL court
            {
                'name': 'Roundup case transferred to cand',
                'data': {
                    'court_id': 'cand', 'mdl_num': '2741', 'transferor_court_id': 'nmd', 'new_filename': 'test_roundup_transferred'
                },
                'expected': {'pending_cto': False, 'is_transferred': True, 'transferred_in': True}
            }
        ]
        
        for test_case in test_cases:
            test_data = test_case['data'].copy()
            test_data.setdefault('docket_num', '1:25-cv-00001')
            test_data.setdefault('is_removal', False)
            
            await transfer_handler_integrated.process_transfers(test_data)
            
            for field, expected_value in test_case['expected'].items():
                actual_value = test_data[field]
                assert actual_value == expected_value, (
                    f"Test case '{test_case['name']}' failed: "
                    f"{field} expected {expected_value}, got {actual_value}"
                )
    
    @pytest.mark.asyncio
    async def test_mdl_lookup_initialization_sequence(self, mock_config, mock_pacer_db, mock_district_court_db, mock_mdl_dataframe):
        """Test the complete initialization sequence that fixes the bug."""
        mock_file_handler = MagicMock()
        mock_gpt = MagicMock()
        
        # Step 1: Create MDL processor without district court DB (initial state)
        mdl_processor = MDLProcessor(
            mdl_litigations=mock_mdl_dataframe,
            mdl_path='/tmp/test_mdl.json', 
            file_handler=mock_file_handler,
            gpt=mock_gpt,
            config=mock_config,
            district_court_db=None  # Simulates initial state
        )
        
        # Verify lookup returns empty initially
        initial_lookup = await mdl_processor._create_mdl_lookup_async()
        assert initial_lookup == {}, "Initial lookup should be empty when district_court_db is None"
        
        # Step 2: Simulate async_init() updating the district court DB
        mdl_processor.update_district_court_db(mock_district_court_db)
        
        # Reset cache to force re-creation
        mdl_processor._mdl_lookup_loaded = False
        mdl_processor._mdl_lookup_cache = None
        
        # Step 3: Verify lookup now works
        fixed_lookup = await mdl_processor._create_mdl_lookup_async()
        expected_lookup = {
            '2741': 'cand',
            '2873': 'scd', 
            '3092': 'njd',
            '2775': 'cand'
        }
        assert fixed_lookup == expected_lookup, "Lookup should work after update_district_court_db"
        
        # Step 4: Create transfer handler and verify it works
        transfer_handler = TransferHandler(mock_config, mock_pacer_db, mock_district_court_db, mdl_processor)
        
        # Test the original failing case
        test_data = {
            'court_id': 'nmd',
            'docket_num': '1:25-cv-00547',
            'mdl_num': '2741',
            'new_filename': 'integration_test_case'
        }
        
        await transfer_handler.process_transfers(test_data)
        
        # Should correctly identify as pending CTO
        assert test_data['pending_cto'] is True, "Integration test should show pending CTO after fix"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])