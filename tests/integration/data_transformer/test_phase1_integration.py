"""
Integration tests for Data Transformer Phase 1 migration.

Tests the complete elimination of compatibility layer across all critical files
and validates they work together with direct async repository usage.
"""
import asyncio
import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from pathlib import Path

# Import all the migrated components
from src.data_transformer.transformer import DataTransformer
from src.data_transformer.uploader import Uploader
from src.data_transformer.transfer_handler import TransferHandler
import pandas as pd

class TestPhase1Integration:
    """Integration tests for Phase 1 compatibility layer elimination."""
    
    @pytest.fixture
    def mock_config(self):
        """Mock configuration for all components."""
        return {
            'directories': {
                'base_dir': '/tmp/test'
            },
            'files': {
                'mdl_lookup': '/tmp/test/mdl_lookup.json'
            },
            'bucket_name': 'test-bucket',
            'aws_access_key': 'test-key',
            'aws_secret_key': 'test-secret',
            'aws_region': 'us-west-2',
            'llm_provider': 'deepseek',
            'uploader_workers': 5,
            'iso_date': '20250611',
            'project_root': '/tmp/test'
        }
    
    @pytest.fixture
    def mock_logger(self):
        """Mock logger."""
        logger = MagicMock()
        logger.info = MagicMock()
        logger.debug = MagicMock()
        logger.error = MagicMock()
        logger.warning = MagicMock()
        return logger
    
    @pytest.fixture
    def mock_async_storage(self):
        """Mock AsyncDynamoDBStorage."""
        mock_storage = AsyncMock()
        mock_storage.__aenter__ = AsyncMock(return_value=mock_storage)
        mock_storage.__aexit__ = AsyncMock(return_value=None)
        mock_storage.scan = AsyncMock(return_value=[
            {'CourtName': 'Test District Court', 'CourtId': 'testd'},
            {'CourtName': 'Another Court', 'CourtId': 'anothd'}
        ])
        return mock_storage
    
    @pytest.fixture
    def mock_s3_storage(self):
        """Mock S3AsyncStorage."""
        mock_s3 = AsyncMock()
        mock_s3.__aenter__ = AsyncMock(return_value=mock_s3)
        mock_s3.__aexit__ = AsyncMock(return_value=None)
        mock_s3.upload_file_async = AsyncMock(return_value=True)
        mock_s3.check_file_exists_async = AsyncMock(return_value=False)
        return mock_s3
    
    @pytest.fixture
    def mock_repositories(self, mock_async_storage):
        """Mock repository instances."""
        pacer_repo = AsyncMock()
        pacer_repo.save = AsyncMock(return_value=True)
        pacer_repo.update_item = AsyncMock(return_value=True)
        pacer_repo.get_by_docket_number = AsyncMock(return_value={'test': 'case'})
        
        courts_repo = AsyncMock()
        courts_repo.scan_all = AsyncMock(return_value=[
            {'CourtName': 'Test District Court', 'CourtId': 'testd'},
            {'CourtName': 'Another Court', 'CourtId': 'anothd'}
        ])
        
        return {'pacer': pacer_repo, 'courts': courts_repo}
    
    @pytest.mark.asyncio
    async def test_full_pipeline_compatibility_layer_elimination(self, mock_config, mock_logger):
        """Test that entire pipeline works without compatibility layer."""
        with patch('src.data_transformer.transformer.AsyncDynamoDBStorage') as mock_storage_class, \
             patch('src.data_transformer.transformer.S3AsyncStorage') as mock_s3_class, \
             patch('src.data_transformer.transformer.PacerRepository') as mock_pacer_repo_class, \
             patch('src.data_transformer.transformer.DistrictCourtsRepository') as mock_courts_repo_class, \
             patch.object(DataTransformer, '_load_mdl_data') as mock_load_mdl, \
             patch('src.data_transformer.transformer.get_feature_flags') as mock_get_flags, \
             patch('src.data_transformer.transformer.HTMLDataUpdater') as mock_html_updater, \
             patch('src.data_transformer.transformer.TransferHandler') as mock_transfer_handler, \
             patch('src.data_transformer.transformer.DocketProcessor') as mock_docket_processor, \
             patch('src.data_transformer.transformer.Uploader') as mock_uploader:
            
            # Setup mocks
            mock_storage = AsyncMock()
            mock_storage_class.return_value = mock_storage
            mock_s3 = AsyncMock()
            mock_s3_class.return_value = mock_s3
            mock_pacer_repo = AsyncMock()
            mock_pacer_repo_class.return_value = mock_pacer_repo
            mock_courts_repo = AsyncMock()
            mock_courts_repo_class.return_value = mock_courts_repo
            mock_load_mdl.return_value = MagicMock()
            mock_get_flags.return_value = None
            
            # Test complete pipeline initialization
            async with DataTransformer(mock_config, mock_logger) as transformer:
                # Verify all components were initialized with direct repositories
                assert transformer.pacer_db == mock_pacer_repo
                assert transformer.district_court_db == mock_courts_repo
                
                # Verify no compatibility layer references exist
                assert not hasattr(transformer, 'create_manager_replacement')
                assert not hasattr(transformer, '_using_new_architecture')
                
                # Verify all dependent components were created with actual repositories
                mock_html_updater.assert_called_once()
                mock_transfer_handler.assert_called_once()
                mock_docket_processor.assert_called_once()
                mock_uploader.assert_called_once()
                
                # Verify they were called with the actual repository instances
                transfer_args, _ = mock_transfer_handler.call_args
                assert mock_pacer_repo in transfer_args
                assert mock_courts_repo in transfer_args
    
    @pytest.mark.asyncio
    async def test_uploader_integration_with_repositories(self, mock_config, mock_repositories):
        """Test Uploader integration with direct repository usage."""
        with patch('src.data_transformer.uploader.S3AsyncStorage') as mock_s3_class:
            mock_s3 = AsyncMock()
            mock_s3_class.return_value = mock_s3
            
            # Mock file handler
            mock_file_handler = MagicMock()
            mock_file_handler.load_json_async = AsyncMock(return_value={'test': 'data'})
            
            # Create uploader with direct repository types
            uploader = Uploader(
                mock_config, 
                mock_s3, 
                mock_repositories['pacer'], 
                mock_file_handler
            )
            
            # Verify no compatibility layer imports
            assert not hasattr(uploader, '_using_new_architecture')
            assert not hasattr(uploader, 'create_manager_replacement')
            
            # Test upload functionality works with direct repositories
            result = await uploader.upload_batch_to_aws_async(
                ['/tmp/test.json'], {'s3'}, force_s3_upload=True
            )
            
            # Should complete without compatibility layer
            assert 'uploaded' in result
            assert 'skipped' in result
            assert 'failed' in result
    
    def test_transfer_handler_integration_with_repositories(self, mock_config, mock_repositories):
        """Test TransferHandler integration with direct repository usage."""
        # Mock MDL processor
        mock_mdl_processor = MagicMock()
        mock_mdl_processor._create_mdl_lookup = MagicMock(return_value={'123': 'testd'})
        
        # Create transfer handler with direct repository types
        transfer_handler = TransferHandler(
            mock_config,
            mock_repositories['pacer'],
            mock_repositories['courts'],
            mock_mdl_processor
        )
        
        # Verify no compatibility layer imports
        assert not hasattr(transfer_handler, '_using_new_architecture')
        assert not hasattr(transfer_handler, 'create_manager_replacement')
        
        # Test basic functionality works
        test_data = {
            'court_id': 'testd',
            'docket_num': '1:23-cv-00001',
            'mdl_num': '123'
        }
        
        # Should process without errors
        transfer_handler.process_transfers(test_data)
        
        # Should have added transfer flags
        assert 'transferred_in' in test_data
        assert 'is_transferred' in test_data
        assert 'pending_cto' in test_data
    
    def test_no_compatibility_layer_in_entire_pipeline(self):
        """Test that no compatibility layer code remains anywhere."""
        # Check all three critical files for compatibility layer remnants
        from src.data_transformer import transformer, uploader, transfer_handler
        
        modules_to_check = [
            transformer,
            uploader, 
            transfer_handler
        ]
        
        compatibility_patterns = [
            'create_manager_replacement',
            'src.migration',
            '_using_new_architecture',
            'TYPE_CHECKING'
        ]
        
        for module in modules_to_check:
            import inspect
            source = inspect.getsource(module)
            
            for pattern in compatibility_patterns:
                assert pattern not in source, f"Found compatibility layer reference '{pattern}' in {module.__name__}"


class TestPhase1CompletionCriteria:
    """Test that Phase 1 completion criteria are met."""
    
    def test_compatibility_layer_elimination_complete(self):
        """Test that compatibility layer is completely eliminated."""
        # Check that src.migration imports are gone from critical files
        critical_files = [
            'src/data_transformer/transformer.py',
            'src/data_transformer/uploader.py', 
            'src/data_transformer/transfer_handler.py'
        ]
        
        for file_path in critical_files:
            with open(file_path, 'r') as f:
                content = f.read()
                
            # These should not exist anywhere in the files
            forbidden_patterns = [
                'from src.migration import',
                'create_manager_replacement',
                '_using_new_architecture'
            ]
            
            for pattern in forbidden_patterns:
                assert pattern not in content, f"Found forbidden pattern '{pattern}' in {file_path}"
    
    def test_direct_repository_usage(self):
        """Test that all components use direct repository imports."""
        # Check that repositories are imported directly
        critical_files = [
            'src/data_transformer/transformer.py',
            'src/data_transformer/uploader.py',
            'src/data_transformer/transfer_handler.py'
        ]
        
        for file_path in critical_files:
            with open(file_path, 'r') as f:
                content = f.read()
            
            # Should have direct repository imports
            assert 'from src.repositories.pacer_repository import PacerRepository' in content, f"Missing direct PacerRepository import in {file_path}"
    
    def test_async_architecture_readiness(self):
        """Test that components are ready for full async conversion."""
        from src.data_transformer.transformer import DataTransformer
        
        # DataTransformer should have async context manager support
        assert hasattr(DataTransformer, '__aenter__')
        assert hasattr(DataTransformer, '__aexit__')
        assert hasattr(DataTransformer, 'async_init')
        
        # Should have repository getter methods that are async
        import inspect
        assert inspect.iscoroutinefunction(DataTransformer._get_pacer_repo)
        assert inspect.iscoroutinefunction(DataTransformer._get_district_courts_repo)
    
    def test_unit_test_coverage(self):
        """Test that comprehensive unit tests exist for migrated components."""
        import os
        
        test_files = [
            'tests/unit/data_transformer/test_transformer_async.py',
            'tests/unit/data_transformer/test_uploader_async.py',
            'tests/unit/data_transformer/test_transfer_handler_async.py'
        ]
        
        for test_file in test_files:
            assert os.path.exists(test_file), f"Missing unit test file: {test_file}"
            
            # Check that test files have substantial content
            with open(test_file, 'r') as f:
                content = f.read()
            
            # Should have multiple test methods
            test_method_count = content.count('def test_')
            assert test_method_count >= 3, f"Insufficient test coverage in {test_file}: only {test_method_count} test methods"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])