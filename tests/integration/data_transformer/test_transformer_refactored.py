"""
Integration tests for refactored DataTransformer.

Tests the modular architecture and ensures all components work together
in the refactored transformer.py created during Phase 3.
"""
import pytest
from unittest.mock import AsyncMock, MagicMock, patch
import pandas as pd

# Import the refactored transformer
from src.data_transformer.transformer_refactored import DataTransformer, Reporter
import pandas as pd

class TestRefactoredDataTransformer:
    """Integration tests for the refactored DataTransformer."""
    
    @pytest.fixture
    def mock_config(self):
        """Mock configuration for DataTransformer."""
        return {
            'directories': {
                'base_dir': '/tmp/test'
            },
            'iso_date': '20250611',
            'project_root': '/tmp/test',
            'bucket_name': 'test-bucket',
            'aws_access_key': 'test_key',
            'aws_secret_key': 'test_secret',
            'aws_region': 'us-west-2',
            'llm_provider': 'deepseek',
            'deepseek_api_key': 'test_deepseek_key',
            'files': {'mdl_lookup': '/tmp/test/mdl_lookup.json'}
        }
    
    @pytest.fixture
    def mock_logger(self):
        """Mock logger for DataTransformer."""
        return MagicMock()
    
    @pytest.fixture
    def refactored_transformer(self, mock_config, mock_logger):
        """Create refactored DataTransformer instance."""
        return DataTransformer(mock_config, mock_logger)
    
    def test_initialization(self, refactored_transformer):
        """Test basic initialization of refactored transformer."""
        # Should initialize without error
        assert refactored_transformer.config is not None
        assert refactored_transformer.logger is not None
        assert refactored_transformer.component_factory is not None
        assert not refactored_transformer._initialized
    
    @pytest.mark.asyncio
    async def test_async_context_manager(self, refactored_transformer):
        """Test async context manager functionality."""
        with patch.object(refactored_transformer, '_initialize_async_storage') as mock_storage, \
             patch.object(refactored_transformer, '_initialize_core_components') as mock_core, \
             patch.object(refactored_transformer, '_initialize_modular_components') as mock_modular:
            
            mock_storage.return_value = None
            mock_core.return_value = None
            mock_modular.return_value = None
            
            async with refactored_transformer as transformer:
                assert transformer._initialized
                mock_storage.assert_called_once()
                mock_core.assert_called_once()
                mock_modular.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_async_init_components(self, refactored_transformer):
        """Test async initialization of all components."""
        with patch('src.data_transformer.transformer.AsyncDynamoDBStorage') as mock_storage, \
             patch('src.data_transformer.transformer.PacerRepository') as mock_pacer, \
             patch('src.data_transformer.transformer.DistrictCourtsRepository') as mock_courts, \
             patch('src.data_transformer.transformer.FileHandler') as mock_file_handler_class, \
             patch('src.data_transformer.transformer.LawFirmProcessor') as mock_law_firm, \
             patch('src.data_transformer.transformer.TransferHandler') as mock_transfer, \
             patch('src.data_transformer.transformer.HTMLDataUpdater') as mock_html, \
             patch('src.data_transformer.transformer.DocketValidator') as mock_validator, \
             patch('src.data_transformer.transformer.MDLProcessor') as mock_mdl_proc, \
             patch('src.data_transformer.transformer.DocketProcessor') as mock_docket_proc, \
             patch.object(refactored_transformer.component_factory, 'load_mdl_data') as mock_mdl_load, \
             patch.object(refactored_transformer.component_factory, 'initialize_llm_client') as mock_llm:
            
            # Mock returns
            mock_mdl_load.return_value = pd.DataFrame([{'mdl_num': '2873', 'litigation': 'AFFF'}])
            mock_llm.return_value = MagicMock()
            mock_file_handler_class.return_value = MagicMock()
            mock_law_firm.return_value = MagicMock()
            mock_transfer.return_value = MagicMock()
            mock_html.return_value = MagicMock()
            mock_validator.return_value = MagicMock()
            mock_mdl_proc.return_value = MagicMock()
            mock_docket_proc.return_value = MagicMock()
            
            await refactored_transformer.async_init()
            
            # Verify initialization was called
            assert refactored_transformer._initialized
            assert refactored_transformer.data_processing_engine is not None
            assert refactored_transformer.file_operations_manager is not None
            assert refactored_transformer.specialized_workflows is not None
            assert refactored_transformer.error_handler is not None
    
    @pytest.mark.asyncio
    async def test_specialized_workflow_law_firm_normalization(self, refactored_transformer):
        """Test law firm normalization workflow."""
        # Mock the specialized workflows component
        mock_specialized = MagicMock()
        mock_specialized.run_law_firm_normalization_only = AsyncMock(return_value=[
            {'filename': 'test1.json', 'status': 'success'},
            {'filename': 'test2.json', 'status': 'success'}
        ])
        refactored_transformer.specialized_workflows = mock_specialized
        refactored_transformer._initialized = True
        
        # Test law firm normalization workflow
        results = await refactored_transformer.start(run_law_firm_normalization_only=True, num_workers=2)
        
        # Verify workflow was called
        mock_specialized.run_law_firm_normalization_only.assert_called_once_with(2)
        assert len(results) == 2
        assert all(r['status'] == 'success' for r in results)
    
    @pytest.mark.asyncio
    async def test_specialized_workflow_mdl_title_update(self, refactored_transformer):
        """Test MDL title update workflow."""
        # Mock the specialized workflows component
        mock_specialized = MagicMock()
        mock_specialized.run_mdl_title_update_only = AsyncMock(return_value=[
            {'filename': 'test1.json', 'status': 'success', 'mdl_num': '2873'},
            {'filename': 'test2.json', 'status': 'no_change', 'mdl_num': '3092'}
        ])
        refactored_transformer.specialized_workflows = mock_specialized
        refactored_transformer._initialized = True
        
        # Test MDL title update workflow
        results = await refactored_transformer.start(run_mdl_title_update_only=True, num_workers=3)
        
        # Verify workflow was called
        mock_specialized.run_mdl_title_update_only.assert_called_once_with(3)
        assert len(results) == 2
        assert results[0]['status'] == 'success'
        assert results[1]['status'] == 'no_change'
    
    @pytest.mark.asyncio
    async def test_main_processing_workflow(self, refactored_transformer):
        """Test main processing workflow."""
        # Mock all the modular components
        mock_file_ops = MagicMock()
        mock_file_ops.get_files_to_process = AsyncMock(return_value=['/test/file1.json', '/test/file2.json'])
        
        mock_data_engine = MagicMock()
        mock_data_engine.load_and_validate_initial_data = AsyncMock(return_value=({}, 'test_file', '/test'))
        mock_data_engine.enrich_data = AsyncMock(return_value=True)
        mock_data_engine.clean_duplicate_fields = MagicMock()
        
        mock_error_handler = MagicMock()
        
        refactored_transformer.file_operations_manager = mock_file_ops
        refactored_transformer.data_processing_engine = mock_data_engine
        refactored_transformer.error_handler = mock_error_handler
        refactored_transformer._initialized = True
        
        # Mock file handler
        mock_file_handler = MagicMock()
        mock_file_handler.save_json = MagicMock()
        refactored_transformer.file_handler = mock_file_handler
        
        # Test main processing
        with patch.object(refactored_transformer, '_process_single_file_async') as mock_process:
            mock_process.return_value = {'filename': 'test_file', 'status': 'success'}
            
            results = await refactored_transformer.start(
                reprocess_files=['file1.json'],
                num_workers=2
            )
            
            # Verify components were called
            mock_file_ops.get_files_to_process.assert_called_once()
            assert len(results) >= 0  # Should handle empty or populated results
    
    
    def test_format_elapsed_time(self):
        """Test elapsed time formatting utility."""
        # Test seconds
        assert DataTransformer._format_elapsed_time(45.5) == "45.5s"
        
        # Test minutes
        assert DataTransformer._format_elapsed_time(125) == "2m 5s"
        
        # Test hours
        assert DataTransformer._format_elapsed_time(3665) == "1h 1m"
    
    def test_log_docket_summary_no_data(self, refactored_transformer, mock_logger):
        """Test logging summary with no data."""
        refactored_transformer.docket_summary = None
        
        refactored_transformer.log_docket_summary()
        
        mock_logger.info.assert_called_with("No docket summary available")
    
    def test_log_docket_summary_with_data(self, refactored_transformer, mock_logger):
        """Test logging summary with data."""
        refactored_transformer.docket_summary = [
            {'status': 'success'},
            {'status': 'success'},
            {'status': 'error'}
        ]
        
        refactored_transformer.log_docket_summary()
        
        # Should log summary information
        mock_logger.info.assert_called()
        call_args = [call.args[0] for call in mock_logger.info.call_args_list]
        summary_logged = any("Processing Summary:" in arg for arg in call_args)
        assert summary_logged
    
    def test_backward_compatibility_alias(self):
        """Test that PostProcessorV2 alias works for backward compatibility."""
        from src.data_transformer.transformer import PostProcessorV2, DataTransformer as OldDataTransformer
        
        # Should be the same as the old DataTransformer
        assert PostProcessorV2 is OldDataTransformer


class TestReporter:
    """Test Reporter functionality."""
    
    @pytest.fixture
    def reporter(self):
        """Create Reporter instance."""
        return Reporter()
    
    def test_generate_summary(self, reporter):
        """Test summary generation."""
        process_summary = [
            {'status': 'success'},
            {'status': 'success'},
            {'status': 'error'},
            {'status': 'success'}
        ]
        upload_results = {
            'bucket1': ['file1.json', 'file2.json'],
            'bucket2': ['file3.json']
        }
        
        with patch.object(reporter.console, 'print') as mock_print:
            reporter.generate_summary(process_summary, upload_results, 4)
            
            # Should print summary information
            mock_print.assert_called()
            
            # Check that correct numbers are reported
            print_calls = [call.args[0] for call in mock_print.call_args_list]
            assert any("Total files processed: 4" in str(call) for call in print_calls)
            assert any("Successful: 3" in str(call) for call in print_calls)
            assert any("Errors: 1" in str(call) for call in print_calls)
            assert any("Files uploaded: 3" in str(call) for call in print_calls)


class TestModularArchitectureIntegration:
    """Test integration between modular components."""
    
    @pytest.mark.asyncio
    async def test_component_dependencies(self):
        """Test that all modular components can be instantiated with proper dependencies."""
        from src.data_transformer.component_factory import ComponentFactory
        from src.data_transformer.error_handler import ErrorHandler
        from src.data_transformer.file_operations import FileOperationsManager
        from src.data_transformer.data_processing_engine import DataProcessingEngine
        from src.data_transformer.specialized_workflows import SpecializedWorkflows
        
        # Mock dependencies
        mock_config = {'test': 'config'}
        mock_logger = MagicMock()
        mock_file_handler = MagicMock()
        mock_docket_processor = MagicMock()
        mock_mdl_processor = MagicMock()
        mock_llm_client = MagicMock()
        
        # Test component instantiation
        component_factory = ComponentFactory(mock_config, mock_logger)
        assert component_factory is not None
        
        error_handler = ErrorHandler(mock_file_handler, mock_logger)
        assert error_handler is not None
        
        file_ops_manager = FileOperationsManager(mock_file_handler, mock_logger)
        assert file_ops_manager is not None
        
        data_processing_engine = DataProcessingEngine(
            mock_docket_processor, mock_mdl_processor, mock_llm_client, mock_logger
        )
        assert data_processing_engine is not None
        
        specialized_workflows = SpecializedWorkflows(
            mock_file_handler, mock_docket_processor, mock_mdl_processor, mock_logger
        )
        assert specialized_workflows is not None
    
    def test_modular_architecture_benefits(self):
        """Test that modular architecture provides expected benefits."""
        # Each module should be independently testable
        from src.data_transformer.component_factory import ComponentFactory
        from src.data_transformer.error_handler import ErrorHandler
        from src.data_transformer.file_operations import FileOperationsManager
        from src.data_transformer.data_processing_engine import DataProcessingEngine
        from src.data_transformer.specialized_workflows import SpecializedWorkflows
        
        # Each module should have a focused responsibility
        assert 'ComponentFactory' in ComponentFactory.__name__
        assert 'ErrorHandler' in ErrorHandler.__name__
        assert 'FileOperationsManager' in FileOperationsManager.__name__
        assert 'DataProcessingEngine' in DataProcessingEngine.__name__
        assert 'SpecializedWorkflows' in SpecializedWorkflows.__name__
        
        # Each module should be in its own file (importable separately)
        assert ComponentFactory.__module__.endswith('component_factory')
        assert ErrorHandler.__module__.endswith('error_handler')
        assert FileOperationsManager.__module__.endswith('file_operations')
        assert DataProcessingEngine.__module__.endswith('data_processing_engine')
        assert SpecializedWorkflows.__module__.endswith('specialized_workflows')


if __name__ == "__main__":
    pytest.main([__file__, "-v"])