"""
Integration tests for Data Transformer Phase 2 migration.

Tests the integration of async MDL processor with other components
and validates the bridge patterns work correctly.
"""
import asyncio
import pytest
from unittest.mock import AsyncMock, MagicMock, patch
import pandas as pd

# Import the components we're testing
from src.data_transformer.mdl_processor import MDLProcessor
from src.data_transformer.transfer_handler import TransferHandler
from src.data_transformer.docket_processor import DocketProcessor
import pandas as pd

class TestPhase2Integration:
    """Integration tests for Phase 2 async MDL processor migration."""
    
    @pytest.fixture
    def mock_config(self):
        """Mock configuration for all components."""
        return {
            'directories': {'base_dir': '/tmp/test'},
            'iso_date': '20250611',
            'project_root': '/tmp/test'
        }
    
    @pytest.fixture
    def mock_district_court_db(self):
        """Mock DistrictCourtsRepository with async methods."""
        mock_courts = AsyncMock()
        mock_courts.scan_all = AsyncMock(return_value=[
            {'MdlNum': '2873', 'CourtId': 'scd', 'CourtName': 'South Carolina District'},
            {'MdlNum': '3092', 'CourtId': 'njd', 'CourtName': 'New Jersey District'},
            {'MdlNum': '2775', 'CourtId': 'cand', 'CourtName': 'Northern California District'}
        ])
        return mock_courts
    
    @pytest.fixture
    def mock_pacer_db(self):
        """Mock PacerRepository."""
        mock_pacer = AsyncMock()
        mock_pacer.update_item = AsyncMock(return_value=True)
        mock_pacer.get_by_docket_number = AsyncMock(return_value={'test': 'case'})
        return mock_pacer
    
    @pytest.fixture
    def mock_mdl_dataframe(self):
        """Mock MDL litigation DataFrame."""
        return pd.DataFrame([
            {'mdl_num': '2873', 'litigation': 'AFFF', 'description': 'Test AFFF case'},
            {'mdl_num': '3092', 'litigation': 'Depo-Provera', 'description': 'Test Depo case'},
            {'mdl_num': '2775', 'litigation': 'Roundup', 'description': 'Test Roundup case'}
        ])
    
    @pytest.fixture
    def mdl_processor(self, mock_config, mock_district_court_db, mock_mdl_dataframe):
        """Create MDLProcessor with mocked dependencies."""
        mock_file_handler = MagicMock()
        mock_gpt = MagicMock()
        
        return MDLProcessor(
            mdl_litigations=mock_mdl_dataframe,
            mdl_path='/tmp/test/mdl_lookup.json',
            file_handler=mock_file_handler,
            gpt=mock_gpt,
            config=mock_config,
            district_court_db=mock_district_court_db
        )
    
    @pytest.fixture
    def transfer_handler(self, mock_config, mock_pacer_db, mock_district_court_db, mdl_processor):
        """Create TransferHandler with MDLProcessor."""
        return TransferHandler(
            mock_config,
            mock_pacer_db,
            mock_district_court_db, 
            mdl_processor
        )
    
    @pytest.fixture
    def docket_processor(self, mock_config):
        """Create DocketProcessor with mocked dependencies."""
        mock_llm_client = MagicMock()
        mock_law_firm_processor = MagicMock()
        mock_transfer_handler = MagicMock()
        mock_html_data_updater = MagicMock()
        mock_validator = MagicMock()
        mock_file_handler = MagicMock()
        
        return DocketProcessor(
            config=mock_config,
            llm_client=mock_llm_client,
            law_firm_processor=mock_law_firm_processor,
            transfer_handler=mock_transfer_handler,
            html_data_updater=mock_html_data_updater,
            validator=mock_validator,
            file_handler=mock_file_handler
        )
    
    @pytest.mark.asyncio
    async def test_mdl_processor_async_lookup_integration(self, mdl_processor, mock_district_court_db):
        """Test MDL processor async lookup works in integration."""
        # Reset cache for fresh test
        mdl_processor._mdl_lookup_loaded = False
        mdl_processor._mdl_lookup_cache = None
        
        # Test async lookup
        result = await mdl_processor._create_mdl_lookup_async()
        
        # Verify lookup was created correctly
        expected_lookup = {
            '2873': 'scd',
            '3092': 'njd', 
            '2775': 'cand'
        }
        assert result == expected_lookup
        
        # Verify repository was called
        mock_district_court_db.scan_all.assert_called_once()
        
        # Verify caching works
        cached_result = await mdl_processor._create_mdl_lookup_async()
        assert cached_result == expected_lookup
        # Should not call repository again
        assert mock_district_court_db.scan_all.call_count == 1
    
    @pytest.mark.asyncio
    async def test_transfer_handler_async_mdl_integration(self, transfer_handler, mdl_processor, mock_district_court_db):
        """Test TransferHandler integration with async MDL processor directly."""
        # Reset MDL cache
        mdl_processor._mdl_lookup_loaded = False
        mdl_processor._mdl_lookup_cache = None
        
        # Test async MDL lookup through transfer handler
        result = await transfer_handler._create_mdl_lookup_async()
        
        # Should get the lookup via async method
        expected_lookup = {
            '2873': 'scd',
            '3092': 'njd',
            '2775': 'cand'
        }
        assert result == expected_lookup
        
        # Verify the lookup is cached for subsequent calls
        cached_result = await transfer_handler._create_mdl_lookup_async()
        assert cached_result == expected_lookup
    
    def test_transfer_handler_process_with_mdl(self, transfer_handler, mdl_processor):
        """Test transfer processing with MDL lookup."""
        # Reset cache
        mdl_processor._mdl_lookup_loaded = False
        
        test_data = {
            'court_id': 'scd',
            'docket_num': '1:23-cv-00001', 
            'mdl_num': '2873'  # AFFF case
        }
        
        # Process transfers should work with MDL lookup
        transfer_handler.process_transfers(test_data)
        
        # Should have added transfer flags
        assert 'transferred_in' in test_data
        assert 'is_transferred' in test_data
        assert 'pending_cto' in test_data
        assert 'is_removal' in test_data
    
    def test_docket_processor_basic_functionality(self, docket_processor):
        """Test DocketProcessor basic functionality is not affected by Phase 2."""
        test_data = {
            'date_filed': '01/15/2024',
            'docket_num': '1:24-cv-00123'
        }
        
        # Basic date processing should work
        docket_processor.process_filing_date(test_data)
        
        # Should format date correctly
        assert test_data['date_filed'] == '20240115'
        assert test_data['filing_date'] == '20240115'
    
    @pytest.mark.asyncio
    async def test_full_pipeline_phase2_integration(self, mdl_processor, transfer_handler, docket_processor, mock_district_court_db):
        """Test complete Phase 2 pipeline integration."""
        # Reset MDL cache
        mdl_processor._mdl_lookup_loaded = False
        mdl_processor._mdl_lookup_cache = None
        
        # Step 1: MDL processor async lookup works
        mdl_lookup = await mdl_processor._create_mdl_lookup_async()
        assert len(mdl_lookup) == 3
        assert '2873' in mdl_lookup
        
        # Step 2: Transfer handler can use the bridge method
        transfer_lookup = transfer_handler._create_mdl_lookup()
        assert transfer_lookup == mdl_lookup  # Should be same (cached)
        
        # Step 3: Process transfer data
        transfer_data = {
            'court_id': 'scd',
            'docket_num': '1:23-cv-00001',
            'mdl_num': '2873'
        }
        transfer_handler.process_transfers(transfer_data)
        assert 'transferred_in' in transfer_data
        
        # Step 4: Docket processor can process the same data
        docket_data = {
            'date_filed': '03/10/2024',
            'docket_num': '1:24-cv-00456',
            'mdl_num': '3092'
        }
        docket_processor.process_filing_date(docket_data)
        assert docket_data['date_filed'] == '20240310'
    
    def test_legacy_sync_error_handling(self, mdl_processor, transfer_handler, mock_district_court_db):
        """Test legacy sync method handles async repository gracefully."""
        with patch.object(mdl_processor.logger, 'error') as mock_error:
            # Reset cache to force lookup
            mdl_processor._mdl_lookup_loaded = False
            
            # Legacy sync method should fail gracefully with async repository
            result = transfer_handler._create_mdl_lookup()
            
            # Should return empty dict when async repository is detected
            assert result == {}
            
            # Should have logged an error about incompatible repository
            mock_error.assert_called_with(
                "Cannot use async repository in sync context. Please use _create_mdl_lookup_async()."
            )
    
    @pytest.mark.asyncio 
    async def test_async_performance_characteristics(self, mdl_processor, mock_district_court_db):
        """Test async performance characteristics of Phase 2."""
        # Mock multiple async operations
        mock_district_court_db.scan_all = AsyncMock(return_value=[
            {'MdlNum': str(i), 'CourtId': f'court{i}'} for i in range(100)
        ])
        
        # Reset cache
        mdl_processor._mdl_lookup_loaded = False
        
        # Test concurrent async operations
        start_time = asyncio.get_event_loop().time()
        
        # Run multiple async lookups concurrently
        tasks = [mdl_processor._create_mdl_lookup_async() for _ in range(5)]
        results = await asyncio.gather(*tasks)
        
        end_time = asyncio.get_event_loop().time()
        
        # All results should be identical (due to caching after first)
        for result in results[1:]:
            assert result == results[0]
        
        # Should complete quickly due to caching
        total_time = end_time - start_time
        assert total_time < 1.0  # Should be much faster than 1 second
        
        print(f"\nAsync concurrent lookup time: {total_time:.4f}s")
        print(f"Number of concurrent operations: {len(tasks)}")
    
    def test_phase2_backward_compatibility(self, mdl_processor, transfer_handler):
        """Test that Phase 2 maintains backward compatibility."""
        # Legacy sync method should still work (but return empty with async repo)
        mdl_processor._clear_lookup_cache()
        with patch.object(mdl_processor.logger, 'error'):
            legacy_result = mdl_processor._create_mdl_lookup_legacy_sync()
        
        # Async method should work properly
        mdl_processor._clear_lookup_cache()  # Reset cache properly for fresh test
        async_result = asyncio.run(mdl_processor._create_mdl_lookup_async())
        
        # Transfer handler sync (deprecated) should work
        mdl_processor._clear_lookup_cache()  # Reset cache properly for fresh test
        with patch.object(transfer_handler.logger, 'warning'):
            transfer_result = transfer_handler._create_mdl_lookup()
        
        # All methods should return dict (legacy returns empty due to async repo)
        assert isinstance(legacy_result, dict)
        assert isinstance(async_result, dict)
        assert isinstance(transfer_result, dict)
        
        # Async should have data, legacy should be empty
        assert len(async_result) > 0
        assert len(legacy_result) == 0  # Can't use async repo in sync context
    
    def test_no_regression_in_existing_functionality(self, docket_processor, transfer_handler):
        """Test that existing functionality is not broken by Phase 2 changes."""
        # Test docket processor date handling
        test_data = {
            'date_filed': '12/25/2023'
        }
        docket_processor.process_filing_date(test_data)
        assert test_data['date_filed'] == '20231225'
        
        # Test transfer handler basic processing
        transfer_data = {
            'court_id': 'testd',
            'docket_num': '1:23-cv-00999'
        }
        transfer_handler.process_transfers(transfer_data)
        
        # Should add transfer flags even without MDL
        assert 'transferred_in' in transfer_data
        assert 'is_transferred' in transfer_data


class TestPhase2CompletionCriteria:
    """Test that Phase 2 completion criteria are met."""
    
    def test_async_mdl_lookup_implemented(self):
        """Test that async MDL lookup method exists and is properly async."""
        from src.data_transformer.mdl_processor import MDLProcessor
        
        # Should have async method
        assert hasattr(MDLProcessor, '_create_mdl_lookup_async')
        
        # Method should be a coroutine function
        import inspect
        assert inspect.iscoroutinefunction(MDLProcessor._create_mdl_lookup_async)
    
    def test_async_first_architecture_implemented(self):
        """Test that async-first architecture is properly implemented."""
        from src.data_transformer.mdl_processor import MDLProcessor
        
        # Should have async method as primary
        assert hasattr(MDLProcessor, '_create_mdl_lookup_async')
        
        # Async method should be a coroutine function
        import inspect
        assert inspect.iscoroutinefunction(MDLProcessor._create_mdl_lookup_async)
        
        # Should have legacy sync method for backward compatibility
        assert hasattr(MDLProcessor, '_create_mdl_lookup_legacy_sync')
        assert not inspect.iscoroutinefunction(MDLProcessor._create_mdl_lookup_legacy_sync)
    
    def test_transfer_handler_async_methods(self):
        """Test that transfer handler has async methods."""
        import inspect
        from src.data_transformer.transfer_handler import TransferHandler
        
        # Should have async MDL lookup method
        assert hasattr(TransferHandler, '_create_mdl_lookup_async')
        assert inspect.iscoroutinefunction(TransferHandler._create_mdl_lookup_async)
        
        # Should have async district court lookup method
        assert hasattr(TransferHandler, '_create_district_court_lookup_async')
        assert inspect.iscoroutinefunction(TransferHandler._create_district_court_lookup_async)
        
        # Legacy sync methods should have deprecation warnings
        sync_source = inspect.getsource(TransferHandler._create_mdl_lookup)
        assert 'deprecated' in sync_source.lower()
    
    def test_performance_improvement_path_exists(self):
        """Test that the path for performance improvement is clear."""
        from src.data_transformer.mdl_processor import MDLProcessor
        import inspect
        
        # Async method should exist for direct usage
        assert hasattr(MDLProcessor, '_create_mdl_lookup_async')
        
        # Legacy sync method should have clear deprecation warnings
        legacy_source = inspect.getsource(MDLProcessor._create_mdl_lookup_legacy_sync)
        assert 'deprecated' in legacy_source.lower()
        assert 'Phase 3' in legacy_source  # Should mention when it will be removed


if __name__ == "__main__":
    pytest.main([__file__, "-v"])