"""
Mock S3 implementation for testing.

This module provides a mock implementation of S3 operations
that stores files in memory instead of making actual AWS calls.
"""
from typing import Dict, List, Any, Optional, Union
from datetime import datetime
import copy
import io
import json
import base64
from pathlib import Path


class MockS3Manager:
    """Mock implementation of S3 manager for testing."""
    
    def __init__(self):
        """Initialize the mock S3 with empty buckets."""
        self.buckets = {}
        self._create_default_buckets()
    
    def _create_default_buckets(self):
        """Create default buckets used in LexGenius."""
        default_buckets = [
            'lexgenius-data',
            'lexgenius-reports',
            'lexgenius-images',
            'lexgenius-backups'
        ]
        
        for bucket_name in default_buckets:
            self.buckets[bucket_name] = {}
    
    def put_object(self, bucket: str, key: str, body: Union[str, bytes, io.IOBase],
                   content_type: Optional[str] = None, metadata: Optional[Dict[str, str]] = None) -> Dict[str, Any]:
        """Put an object into the mock S3 bucket."""
        if bucket not in self.buckets:
            raise ValueError(f"Bucket {bucket} does not exist")
        
        # Convert body to bytes if needed
        if isinstance(body, str):
            data = body.encode('utf-8')
        elif isinstance(body, io.IOBase):
            data = body.read()
            if isinstance(data, str):
                data = data.encode('utf-8')
        else:
            data = body
        
        # Store the object with metadata
        self.buckets[bucket][key] = {
            'body': data,
            'content_type': content_type or 'application/octet-stream',
            'metadata': metadata or {},
            'last_modified': datetime.now().isoformat(),
            'size': len(data),
            'etag': f'"{hash(data)}"'
        }
        
        return {
            'ETag': f'"{hash(data)}"',
            'ResponseMetadata': {'HTTPStatusCode': 200}
        }
    
    def get_object(self, bucket: str, key: str) -> Dict[str, Any]:
        """Get an object from the mock S3 bucket."""
        if bucket not in self.buckets:
            raise ValueError(f"Bucket {bucket} does not exist")
        
        if key not in self.buckets[bucket]:
            raise ValueError(f"Key {key} not found in bucket {bucket}")
        
        obj = self.buckets[bucket][key]
        
        return {
            'Body': io.BytesIO(obj['body']),
            'ContentType': obj['content_type'],
            'ContentLength': obj['size'],
            'LastModified': obj['last_modified'],
            'ETag': obj['etag'],
            'Metadata': obj['metadata']
        }
    
    def delete_object(self, bucket: str, key: str) -> Dict[str, Any]:
        """Delete an object from the mock S3 bucket."""
        if bucket not in self.buckets:
            raise ValueError(f"Bucket {bucket} does not exist")
        
        if key in self.buckets[bucket]:
            del self.buckets[bucket][key]
        
        return {'ResponseMetadata': {'HTTPStatusCode': 204}}
    
    def list_objects_v2(self, bucket: str, prefix: Optional[str] = None,
                        delimiter: Optional[str] = None, max_keys: int = 1000) -> Dict[str, Any]:
        """List objects in the mock S3 bucket."""
        if bucket not in self.buckets:
            raise ValueError(f"Bucket {bucket} does not exist")
        
        contents = []
        common_prefixes = set()
        
        for key, obj in self.buckets[bucket].items():
            # Filter by prefix
            if prefix and not key.startswith(prefix):
                continue
            
            # Handle delimiter (for "folder" simulation)
            if delimiter and prefix:
                relative_key = key[len(prefix):]
                delimiter_pos = relative_key.find(delimiter)
                if delimiter_pos != -1:
                    # This is a "folder"
                    folder_prefix = prefix + relative_key[:delimiter_pos + 1]
                    common_prefixes.add(folder_prefix)
                    continue
            
            contents.append({
                'Key': key,
                'LastModified': obj['last_modified'],
                'ETag': obj['etag'],
                'Size': obj['size']
            })
            
            if len(contents) >= max_keys:
                break
        
        response = {
            'Contents': contents,
            'KeyCount': len(contents),
            'MaxKeys': max_keys,
            'Name': bucket,
            'Prefix': prefix or ''
        }
        
        if common_prefixes:
            response['CommonPrefixes'] = [{'Prefix': p} for p in sorted(common_prefixes)]
        
        return response
    
    def head_object(self, bucket: str, key: str) -> Dict[str, Any]:
        """Get object metadata without retrieving the object."""
        if bucket not in self.buckets:
            raise ValueError(f"Bucket {bucket} does not exist")
        
        if key not in self.buckets[bucket]:
            raise ValueError(f"Key {key} not found in bucket {bucket}")
        
        obj = self.buckets[bucket][key]
        
        return {
            'ContentType': obj['content_type'],
            'ContentLength': obj['size'],
            'LastModified': obj['last_modified'],
            'ETag': obj['etag'],
            'Metadata': obj['metadata']
        }
    
    def copy_object(self, source_bucket: str, source_key: str,
                    dest_bucket: str, dest_key: str) -> Dict[str, Any]:
        """Copy an object within or between buckets."""
        if source_bucket not in self.buckets:
            raise ValueError(f"Source bucket {source_bucket} does not exist")
        
        if dest_bucket not in self.buckets:
            raise ValueError(f"Destination bucket {dest_bucket} does not exist")
        
        if source_key not in self.buckets[source_bucket]:
            raise ValueError(f"Source key {source_key} not found")
        
        # Copy the object
        source_obj = self.buckets[source_bucket][source_key]
        self.buckets[dest_bucket][dest_key] = copy.deepcopy(source_obj)
        self.buckets[dest_bucket][dest_key]['last_modified'] = datetime.now().isoformat()
        
        return {
            'CopyObjectResult': {
                'ETag': source_obj['etag'],
                'LastModified': datetime.now().isoformat()
            }
        }
    
    def upload_file(self, filename: str, bucket: str, key: str,
                    extra_args: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Upload a file to the mock S3 bucket."""
        # Read the file content
        with open(filename, 'rb') as f:
            content = f.read()
        
        # Determine content type from filename
        content_type = 'application/octet-stream'
        if filename.endswith('.json'):
            content_type = 'application/json'
        elif filename.endswith('.html'):
            content_type = 'text/html'
        elif filename.endswith('.pdf'):
            content_type = 'application/pdf'
        elif filename.endswith('.jpg') or filename.endswith('.jpeg'):
            content_type = 'image/jpeg'
        elif filename.endswith('.png'):
            content_type = 'image/png'
        
        # Override with extra args if provided
        if extra_args:
            content_type = extra_args.get('ContentType', content_type)
            metadata = extra_args.get('Metadata', {})
        else:
            metadata = {}
        
        return self.put_object(bucket, key, content, content_type, metadata)
    
    def download_file(self, bucket: str, key: str, filename: str) -> None:
        """Download a file from the mock S3 bucket."""
        obj = self.get_object(bucket, key)
        
        # Write to file
        with open(filename, 'wb') as f:
            f.write(obj['Body'].read())
    
    def generate_presigned_url(self, operation: str, params: Dict[str, Any],
                              expiration: int = 3600) -> str:
        """Generate a mock presigned URL."""
        bucket = params.get('Bucket', '')
        key = params.get('Key', '')
        
        # Return a mock URL
        return f"https://{bucket}.s3.amazonaws.com/{key}?X-Amz-Expires={expiration}"
    
    def delete_objects(self, bucket: str, delete: Dict[str, List[Dict[str, str]]]) -> Dict[str, Any]:
        """Delete multiple objects from the mock S3 bucket."""
        if bucket not in self.buckets:
            raise ValueError(f"Bucket {bucket} does not exist")
        
        deleted = []
        errors = []
        
        for obj in delete.get('Objects', []):
            key = obj.get('Key')
            if key and key in self.buckets[bucket]:
                del self.buckets[bucket][key]
                deleted.append({'Key': key})
            else:
                errors.append({
                    'Key': key,
                    'Code': 'NoSuchKey',
                    'Message': 'The specified key does not exist.'
                })
        
        response = {'Deleted': deleted}
        if errors:
            response['Errors'] = errors
        
        return response
    
    # Test helper methods
    
    def create_bucket(self, bucket_name: str):
        """Create a new bucket (test helper)."""
        if bucket_name not in self.buckets:
            self.buckets[bucket_name] = {}
    
    def delete_bucket(self, bucket_name: str):
        """Delete a bucket (test helper)."""
        if bucket_name in self.buckets:
            del self.buckets[bucket_name]
    
    def clear_bucket(self, bucket_name: str):
        """Clear all objects from a bucket (test helper)."""
        if bucket_name in self.buckets:
            self.buckets[bucket_name] = {}
    
    def get_all_objects(self, bucket_name: str) -> Dict[str, Dict[str, Any]]:
        """Get all objects from a bucket (test helper)."""
        if bucket_name not in self.buckets:
            return {}
        return copy.deepcopy(self.buckets[bucket_name])
    
    def get_object_count(self, bucket_name: str) -> int:
        """Get the number of objects in a bucket (test helper)."""
        if bucket_name not in self.buckets:
            return 0
        return len(self.buckets[bucket_name])
    
    def object_exists(self, bucket: str, key: str) -> bool:
        """Check if an object exists (test helper)."""
        return bucket in self.buckets and key in self.buckets[bucket]