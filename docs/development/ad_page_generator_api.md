# AdPageGenerator API Documentation

## Class: AdPageGenerator

Located in: `src/lib/reports/ad_page_generator.py`

### Overview

The AdPageGenerator class is responsible for generating individual HTML pages for Facebook ads and uploading them to S3. It includes retry logic, error tracking, and validation capabilities.

### Constructor

```python
def __init__(self, report_config: ReportConfig, renderer: ReportRenderer, s3_manager: S3Manager)
```

**Parameters:**
- `report_config`: ReportConfig instance containing date and path configurations
- `renderer`: ReportRenderer instance with Jinja2 environment for template rendering
- `s3_manager`: S3Manager instance for file uploads

### Public Methods

#### generate_and_upload_ad_pages

```python
async def generate_and_upload_ad_pages(self, ad_df: pd.DataFrame) -> None
```

Generates HTML pages for all ads in the dataframe and uploads them to S3.

**Parameters:**
- `ad_df`: Pandas DataFrame containing ad data with required columns

**Required DataFrame Columns:**
- `ad_archive_id`: Unique identifier for the ad
- `ad_creative_id`: Creative ID for the ad
- `page_id`: Facebook page ID
- `law_firm`: Law firm name
- `body`: Ad body text
- `link_url`: Ad destination URL
- `publisher_platform`: List of platforms
- Additional optional columns for title, caption, description, etc.

**Behavior:**
1. Creates S3 folders for the report date
2. Generates HTML for each ad using the template
3. Uploads HTML files concurrently with retry logic
4. Tracks and saves failed uploads
5. Logs detailed statistics

**Example:**
```python
await ad_page_generator.generate_and_upload_ad_pages(ad_dataframe)
```

#### validate_ad_pages

```python
async def validate_ad_pages(self, ad_ids: List[str]) -> Dict[str, bool]
```

Validates that ad HTML pages exist on S3.

**Parameters:**
- `ad_ids`: List of ad archive IDs to check

**Returns:**
- Dictionary mapping ad_id to existence status (True/False)

**Example:**
```python
validation_results = await ad_page_generator.validate_ad_pages(['1788443798685155', '2394857629485729'])
# Returns: {'1788443798685155': True, '2394857629485729': False}
```

### Private Methods

#### _upload_ad_task

```python
async def _upload_ad_task(self, ad_html: str, ad_archive_id: str, retry_count: int = 3) -> Tuple[str, bool, Optional[str]]
```

Uploads a single ad HTML to S3 with retry logic.

**Parameters:**
- `ad_html`: HTML content to upload
- `ad_archive_id`: Ad identifier for the S3 path
- `retry_count`: Number of retry attempts (default: 3)

**Returns:**
- Tuple of (ad_archive_id, success, error_message)

**Retry Behavior:**
- Exponential backoff: 2^attempt seconds (2s, 4s, 8s)
- Catches TimeoutError separately for better error reporting
- Returns detailed error message on final failure

#### _save_failed_uploads

```python
def _save_failed_uploads(self, failed_ads: List[Dict[str, str]]) -> None
```

Saves failed ad upload information to a JSON file for recovery.

**Parameters:**
- `failed_ads`: List of dictionaries with 'ad_id' and 'error' keys

**Output Location:**
- `{download_dir}/failed_ad_uploads/failed_ads_{date}_{timestamp}.json`

**Output Format:**
```json
{
  "report_date": "20250527",
  "timestamp": "20250529_123456",
  "total_failures": 3,
  "failed_ads": [
    {
      "ad_id": "1788443798685155",
      "error": "TimeoutError: Upload timed out"
    }
  ]
}
```

#### _check_ad_exists

```python
async def _check_ad_exists(self, ad_id: str, s3_path: str) -> Tuple[str, bool]
```

Checks if a single ad page exists on S3.

**Parameters:**
- `ad_id`: Ad archive ID
- `s3_path`: Full S3 object path

**Returns:**
- Tuple of (ad_id, exists)

### Static Methods

#### _filter_curly_braces

```python
@staticmethod
def _filter_curly_braces(text: str) -> str
```

Removes template variable markers ({{...}}) from text.

#### _check_and_format

```python
@staticmethod
def _check_and_format(value: Any) -> str
```

Safely converts values to strings, handling NaN and None cases.

#### _get_image_uri

```python
@staticmethod
def _get_image_uri(row_dict: Dict) -> str
```

Determines the image URL for an ad, checking multiple possible fields.

### Error Handling

The class implements comprehensive error handling:

1. **Upload Errors**: Caught and retried with exponential backoff
2. **Template Errors**: Logged and ad skipped
3. **Missing Data**: Handled gracefully with fallback values
4. **S3 Errors**: Detailed logging with specific error types

### Logging

Uses structured logging with levels:
- `INFO`: Normal operations and statistics
- `WARNING`: Non-critical issues (missing data, failed uploads)
- `ERROR`: Critical errors with stack traces
- `DEBUG`: Detailed operation information

### Performance Considerations

1. **Concurrent Uploads**: Uses asyncio for parallel S3 uploads
2. **Batch Validation**: Validates ads in batches of 50
3. **Worker Pool**: Configurable number of upload workers (default: 10)
4. **Memory Efficiency**: Processes ads in streaming fashion

### Integration Example

```python
# In ReportOrchestrator
async def generate_report():
    # Load ad data
    ad_df = await load_ad_data()
    
    # Generate and upload ad pages
    await ad_page_generator.generate_and_upload_ad_pages(ad_df)
    
    # Validate uploads
    ad_ids = ad_df['ad_archive_id'].tolist()
    validation_results = await ad_page_generator.validate_ad_pages(ad_ids)
    
    # Filter out missing ads
    missing_ads = [ad_id for ad_id, exists in validation_results.items() if not exists]
    if missing_ads:
        ad_df = ad_df[~ad_df['ad_archive_id'].isin(missing_ads)]
    
    # Continue with report generation...
```

### Configuration

The class uses settings from ReportConfig:
- `iso_date`: Report date for S3 paths
- `download_dir`: Local directory for failed upload logs
- `s3_upload_workers`: Number of concurrent upload workers
- `s3_prod_url`: CDN URL prefix for image links

### Future Enhancements

1. **Batch HTML Generation**: Generate multiple HTML files in parallel
2. **Progressive Upload**: Start uploading while still generating
3. **Compression**: Gzip HTML before upload
4. **Caching**: Skip regeneration of unchanged ads