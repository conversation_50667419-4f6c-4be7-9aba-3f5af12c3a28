# PACER Module Migration to Async Architecture

## Overview

This document outlines the complete migration of the `src/pacer/` module from the problematic compatibility layer to a clean async architecture. The migration eliminates all usage of `src/migration/` and `compat` classes while maintaining full functionality.

## Current State Analysis

### Existing Files in src/pacer/
```
src/pacer/
├── __init__.py
├── authenticator.py          # Browser authentication (keep)
├── browser_service.py        # Browser automation (keep) 
├── case_relevance_engine.py  # Business logic (refactor)
├── case_transfer_handler.py  # Business logic (refactor)
├── config.py                 # Configuration (eliminate)
├── docket_processor.py       # Business logic (refactor)
├── file_manager.py           # File operations (refactor)
├── navigator.py              # Browser navigation (keep)
├── orchestrator.py           # Main orchestrator (major refactor)
├── pacer_document_downloader.py # Document handling (refactor)
├── pacer_free_order_service.py  # External service (keep)
├── pacer_utils.py            # Utilities (keep/enhance)
├── receipt_logger.py         # Logging (keep)
└── report_handler.py         # Report processing (refactor)
```

### Key Issues to Address
1. **Orchestrator.py dependencies** on `create_manager_replacement()` and compatibility classes
2. **Sync database operations** that cause event loop conflicts
3. **Mixed async/sync patterns** throughout the module
4. **Large file sizes** (orchestrator.py is likely 2000+ lines)
5. **Tight coupling** between components

## Migration Strategy

### Phase 1: Direct Repository Integration (Week 1)

#### 1.1 Eliminate Compatibility Layer Dependencies
**Current Problematic Pattern:**
```python
# In orchestrator.py
from src.migration import create_manager_replacement
self.pacer_db = create_manager_replacement('PacerManager', config)
```

**Target Clean Pattern:**
```python
# Direct async repository usage
from src.infrastructure.storage.dynamodb_async import AsyncDynamoDBStorage
from src.repositories.pacer_repository import PacerRepository

class PacerOrchestrator:
    def __init__(self, config: PacerConfig):
        self.config = config
        self._storage = None
        self._pacer_repo = None
    
    async def __aenter__(self):
        self._storage = AsyncDynamoDBStorage(self.config.storage)
        await self._storage.__aenter__()
        self._pacer_repo = PacerRepository(self._storage)
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self._storage:
            await self._storage.__aexit__(exc_type, exc_val, exc_tb)
```

#### 1.2 Method Mapping for Direct Repository Calls
**Replace compatibility layer calls:**
```python
# OLD: Using compatibility layer
docket_exists = self.pacer_db.check_docket_exists(court_id, docket_num)
records = self.pacer_db.get_docket_items(court_id, docket_num)

# NEW: Direct async repository
docket_exists = await self._pacer_repo.check_docket_exists(court_id, docket_num)
records = await self._pacer_repo.get_docket_items(court_id, docket_num)
```

### Phase 2: Service Layer Extraction (Week 2)

#### 2.1 Break Down Large Files
**Target File Structure:**
```
src/pacer/
├── __init__.py
├── orchestrator.py           # Coordination only (300 lines max)
├── services/
│   ├── __init__.py
│   ├── case_analysis_service.py     # From case_relevance_engine.py
│   ├── transfer_service.py          # From case_transfer_handler.py
│   ├── document_service.py          # From pacer_document_downloader.py
│   ├── docket_service.py            # From docket_processor.py
│   └── report_service.py            # From report_handler.py
├── infrastructure/
│   ├── __init__.py
│   ├── browser_automation.py       # From browser_service.py + navigator.py
│   ├── authentication.py           # From authenticator.py
│   └── file_operations.py          # From file_manager.py
├── domain/
│   ├── __init__.py
│   ├── docket.py                   # Domain models
│   ├── case.py
│   └── court.py
└── utils/
    ├── __init__.py
    ├── pacer_utils.py              # Enhanced utilities
    └── receipt_logger.py           # Logging utilities
```

#### 2.2 Service Interface Definitions
```python
# src/pacer/services/case_analysis_service.py
from src.protocols.storage import IRepository
from src.pacer.domain.case import Case
from typing import List, Optional

class CaseAnalysisService:
    def __init__(self, pacer_repo: IRepository):
        self.pacer_repo = pacer_repo
    
    async def analyze_case_relevance(self, case_data: dict) -> bool:
        """Determine if a case is relevant for processing"""
        # Business logic from case_relevance_engine.py
        pass
    
    async def check_transfer_status(self, court_id: str, docket_num: str) -> dict:
        """Check if case has been transferred"""
        # Logic from case_transfer_handler.py
        pass
```

### Phase 3: Async Context Management (Week 3)

#### 3.1 Proper Async Context Managers
**Main Orchestrator Pattern:**
```python
# src/pacer/orchestrator.py
class PacerOrchestrator:
    def __init__(self, config: PacerConfig):
        self.config = config
        self._storage = None
        self._repositories = {}
        self._services = {}
    
    async def __aenter__(self):
        # Single storage connection with pooling
        self._storage = AsyncDynamoDBStorage(self.config.storage)
        await self._storage.__aenter__()
        
        # Initialize repositories
        self._repositories['pacer'] = PacerRepository(self._storage)
        self._repositories['courts'] = DistrictCourtsRepository(self._storage)
        
        # Initialize services with repositories
        self._services['case_analysis'] = CaseAnalysisService(
            self._repositories['pacer']
        )
        self._services['document'] = DocumentService(
            self._repositories['pacer']
        )
        
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self._storage:
            await self._storage.__aexit__(exc_type, exc_val, exc_tb)
    
    async def process_courts(self, court_ids: List[str], 
                           start_date_str: Optional[str], 
                           end_date_str: Optional[str]) -> Tuple[List[str], List[Dict[str, Any]]]:
        """Main processing method with proper async patterns"""
        
        # Use services for business logic
        failed_courts = []
        processed_stats = []
        
        # Concurrent processing with proper async patterns
        async with asyncio.TaskGroup() as tg:
            tasks = [
                tg.create_task(self._process_single_court(court_id, start_date_str, end_date_str))
                for court_id in court_ids
            ]
        
        # Collect results
        for i, task in enumerate(tasks):
            try:
                result = task.result()
                processed_stats.append(result)
            except Exception as e:
                failed_courts.append(court_ids[i])
                self.logger.error(f"Court {court_ids[i]} failed: {e}")
        
        return failed_courts, processed_stats
```

#### 3.2 Service Integration
```python
async def _process_single_court(self, court_id: str, start_date_str: str, end_date_str: str) -> Dict[str, Any]:
    """Process a single court with service layer"""
    
    # Use case analysis service
    relevant_cases = await self._services['case_analysis'].find_relevant_cases(
        court_id, start_date_str, end_date_str
    )
    
    # Use document service for downloads
    download_results = await self._services['document'].download_dockets(
        relevant_cases
    )
    
    return {
        'court_id': court_id,
        'cases_processed': len(relevant_cases),
        'downloads_successful': download_results['successful'],
        'downloads_failed': download_results['failed']
    }
```

### Phase 4: Configuration Migration (Week 4)

#### 4.1 Eliminate config.py
**Remove:**
- `src/pacer/config.py` (replaced by Pydantic models)

**Replace with:**
```python
# src/config_models/pacer.py
from pydantic import BaseModel, Field
from typing import List, Optional
from datetime import datetime

class PacerConfig(BaseModel):
    """PACER scraping configuration"""
    date: datetime
    start_date: Optional[datetime] = None
    scraper: bool = True
    headless: bool = True
    run_parallel: bool = False
    process_single_court: Optional[List[str]] = None
    num_workers: int = Field(4, ge=1, le=16)
    
    # PACER-specific settings
    use_docket_report_log: bool = True
    html_only: bool = False
    process_review_cases: bool = False
    reprocess_failed: bool = False
    
    # Authentication
    username_prod: str = Field(..., env='PACER_USERNAME')
    password_prod: str = Field(..., env='PACER_PASSWORD')
    
    class Config:
        env_prefix = "PACER_"
        extra = "forbid"

class PacerStorageConfig(BaseModel):
    """PACER storage configuration"""
    data_dir: str = Field(default="data")
    log_dir: str = Field(default="logs")
    paths_config: dict = Field(default_factory=dict)
    relevance_config: dict = Field(default_factory=dict)
    stability_config: dict = Field(default_factory=dict)
```

#### 4.2 Update Import Patterns
**Throughout src/pacer/:**
```python
# OLD
from .config import load_config
config = load_config(date_str)

# NEW
from src.config_models.pacer import PacerConfig
config = PacerConfig(date=date_str, **yaml_config)
```

### Phase 5: Testing Infrastructure (Week 5)

#### 5.1 Comprehensive Test Structure
```
tests/
├── unit/
│   ├── pacer/
│   │   ├── services/
│   │   │   ├── test_case_analysis_service.py
│   │   │   ├── test_document_service.py
│   │   │   ├── test_transfer_service.py
│   │   │   └── test_docket_service.py
│   │   ├── infrastructure/
│   │   │   ├── test_browser_automation.py
│   │   │   └── test_authentication.py
│   │   ├── domain/
│   │   │   ├── test_docket.py
│   │   │   └── test_case.py
│   │   └── test_orchestrator.py
│   └── config_models/
│       └── test_pacer_config.py
├── integration/
│   └── pacer/
│       ├── test_pacer_workflow.py
│       └── test_database_integration.py
└── e2e/
    └── test_pacer_full_pipeline.py
```

#### 5.2 Mock Infrastructure
```python
# tests/mocks/mock_pacer_repository.py
from src.protocols.storage import IRepository
from typing import List, Dict, Any, Optional

class MockPacerRepository(IRepository):
    def __init__(self):
        self._data = {}
        self._call_log = []
    
    async def check_docket_exists(self, court_id: str, docket_num: str) -> bool:
        self._call_log.append(('check_docket_exists', court_id, docket_num))
        return f"{court_id}_{docket_num}" in self._data
    
    async def get_docket_items(self, court_id: str, docket_num: str) -> List[Dict[str, Any]]:
        self._call_log.append(('get_docket_items', court_id, docket_num))
        return self._data.get(f"{court_id}_{docket_num}", [])
    
    def add_test_data(self, court_id: str, docket_num: str, data: List[Dict[str, Any]]):
        """Helper method for setting up test data"""
        self._data[f"{court_id}_{docket_num}"] = data
```

### Phase 6: Entry Point Integration (Week 6)

#### 6.1 Update main.py Integration
```python
# src/main.py updates
async def run_pacer_workflow(config: PacerConfig):
    """Main PACER workflow with proper async context management"""
    
    async with PacerOrchestrator(config) as orchestrator:
        failed_courts, processed_stats = await orchestrator.process_courts(
            court_ids=config.process_single_court or await get_all_court_ids(),
            start_date_str=config.start_date.strftime('%m/%d/%y') if config.start_date else None,
            end_date_str=config.date.strftime('%m/%d/%y')
        )
        
        return {
            'failed_courts': failed_courts,
            'processed_stats': processed_stats,
            'success': len(failed_courts) == 0
        }

# Integration with main workflow
async def main():
    config = load_config(args.params)
    
    if config.workflow.pacer.enabled:
        pacer_config = PacerConfig(**config.pacer)
        pacer_result = await run_pacer_workflow(pacer_config)
        
        if not pacer_result['success']:
            logger.error(f"PACER workflow failed for courts: {pacer_result['failed_courts']}")
        else:
            logger.info("PACER workflow completed successfully")
```

#### 6.2 Remove All Migration Dependencies
**Files to Remove After Migration:**
- All imports of `create_manager_replacement`
- All imports from `src.migration.manager_to_service`
- All compatibility wrapper usage

**Search and Replace Pattern:**
```bash
# Remove these patterns across src/pacer/
grep -r "create_manager_replacement" src/pacer/ | cut -d: -f1 | sort -u
grep -r "ManagerCompat" src/pacer/ | cut -d: -f1 | sort -u
grep -r "src.migration" src/pacer/ | cut -d: -f1 | sort -u
```

## Implementation Timeline

### Week 1: Core Infrastructure
- **Day 1-2**: Create new service classes and move business logic
- **Day 3**: Update orchestrator to use direct repositories
- **Day 4**: Test repository integration
- **Day 5**: Fix any async context issues

### Week 2: Service Layer
- **Day 1-2**: Extract services from large files
- **Day 3-4**: Implement proper service interfaces
- **Day 5**: Test service integration

### Week 3: Async Patterns
- **Day 1-2**: Implement proper async context managers
- **Day 3-4**: Update all async/await patterns
- **Day 5**: Performance testing and optimization

### Week 4: Configuration
- **Day 1-2**: Create Pydantic configuration models
- **Day 3**: Update all configuration usage
- **Day 4-5**: Test configuration validation

### Week 5: Testing
- **Day 1-3**: Create comprehensive test suite
- **Day 4-5**: Test migration against production scenarios

### Week 6: Integration
- **Day 1-2**: Update main.py integration
- **Day 3**: Remove all migration dependencies
- **Day 4-5**: Final testing and documentation

## Expected Benefits

### Performance Improvements
- **50-70% reduction** in database operation time (no event loop creation overhead)
- **30-40% reduction** in memory usage (single async context)
- **Improved concurrency** with proper async patterns

### Code Quality Improvements
- **Single responsibility** for each service
- **Clean interfaces** between components
- **Testable components** with dependency injection
- **Type safety** with Pydantic configuration

### Maintenance Benefits
- **No compatibility layer** complexity
- **Direct async patterns** throughout
- **Clear separation** of concerns
- **Comprehensive testing** coverage

## Risk Mitigation

### Backward Compatibility
- Use feature flags during transition
- Keep old code paths until migration is proven
- Comprehensive testing before deployment

### Rollback Plan
- Tag current working version before migration
- Document exact steps to revert changes
- Maintain monitoring during rollout

### Validation Strategy
- Compare performance metrics before/after
- Test with production data volumes
- Validate all existing functionality works

## Success Metrics

1. **Zero usage** of `src/migration/` or compatibility classes
2. **100% async** patterns throughout PACER module
3. **50%+ performance improvement** in database operations
4. **80%+ test coverage** for all new components
5. **No functional regressions** in existing workflows

## Files Modified/Created

### New Files
- `src/pacer/services/case_analysis_service.py`
- `src/pacer/services/transfer_service.py`
- `src/pacer/services/document_service.py`
- `src/pacer/services/docket_service.py`
- `src/pacer/services/report_service.py`
- `src/pacer/infrastructure/browser_automation.py`
- `src/pacer/infrastructure/authentication.py`
- `src/pacer/infrastructure/file_operations.py`
- `src/pacer/domain/docket.py`
- `src/pacer/domain/case.py`
- `src/pacer/domain/court.py`
- `src/config_models/pacer.py`
- Comprehensive test suite

### Modified Files
- `src/pacer/orchestrator.py` (major refactor)
- `src/pacer/case_relevance_engine.py` (extract to service)
- `src/pacer/case_transfer_handler.py` (extract to service)
- `src/pacer/docket_processor.py` (extract to service)
- `src/pacer/file_manager.py` (move to infrastructure)
- `src/pacer/report_handler.py` (extract to service)
- `src/main.py` (update integration)

### Removed Files
- `src/pacer/config.py` (replaced by Pydantic)

This migration plan ensures a complete transformation of the PACER module to use clean async architecture while maintaining all existing functionality and improving performance significantly.