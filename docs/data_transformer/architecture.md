# Data Transformer Module Architecture

## Overview

The Data Transformer module is a critical component of the LexGenius pipeline responsible for processing, enriching, and transforming legal docket data from PACER. It operates as the post-processing stage after PACER scraping, preparing data for upload to AWS services (S3 and DynamoDB) and report generation.

## Purpose

The module handles:
- Legal document text extraction (PDF/HTML)
- Attorney and law firm information processing
- MDL (Multi-District Litigation) classification
- Transfer case tracking
- Data validation and enrichment
- Upload orchestration to AWS

## Architecture Overview

```
┌─────────────────────────────────────────────────────────────────┐
│                     DataTransformer (Main)                       │
│  - Orchestrates the entire post-processing workflow              │
│  - Manages parallel processing with configurable workers         │
│  - Coordinates all sub-processors                               │
└───────────────────────┬─────────────────────────────────────────┘
                        │
    ┌───────────────────┴──────────────────────────────┐
    │                                                   │
    ▼                                                   ▼
┌──────────────────────┐                    ┌────────────────────┐
│  DocketProcessor     │                    │  DocketFileManager │
│  - Processes         │                    │  - Manages files   │
│    individual files  │                    │  - Handles zips    │
│  - Runs LLM extract  │                    │  - Recovery logic  │
└──────────┬───────────┘                    └────────────────────┘
           │
     ┌─────┴──────┬────────────┬──────────────┬───────────────┐
     ▼            ▼            ▼              ▼               ▼
┌─────────┐ ┌──────────┐ ┌──────────┐ ┌─────────────┐ ┌──────────┐
│FileHandler│LawFirm   │ │Transfer  │ │MDLProcessor │ │Validator │
│-Save/load│Processor  │ │Handler   │ │-<PERSON><PERSON> lookup  │ │-Validate │
│-S3 keys  │-Normalize │ │-Transfer │ │-Litigation  │ │ complete │
└─────────┘ └──────────┘ └──────────┘ └─────────────┘ └──────────┘
```

## Core Components

### 1. DataTransformer (`transformer.py`)
The main orchestrator that:
- Initializes all sub-components
- Manages the processing pipeline
- Handles parallel processing with configurable workers
- Provides law firm normalization mode
- Generates processing reports

Key responsibilities:
- Component initialization and dependency injection
- File discovery and filtering
- Batch processing coordination
- Error handling and recovery
- Upload orchestration

### 2. DocketProcessor (`docket_processor.py`)
Handles individual docket file processing:
- PDF text extraction via OCR (MistralOCR)
- HTML parsing for court data
- LLM-based information extraction
- Filing date processing
- Data cleaning and flattening

Key features:
- Async/sync processing modes
- Temporary file management
- S3 download support
- Error recovery

### 3. FileHandler (`file_handler.py`)
Manages all file operations:
- JSON file loading/saving with atomic writes
- Filename generation following court conventions
- S3 key generation for uploads
- Skip file management
- Temporary file cleanup

Filename format: `{court_id}_{YY}_{NNNNN}_{cleaned_versus}`

### 4. DocketFileManager (`docket_file_manager.py`)
Handles file discovery and recovery:
- ZIP file extraction
- Recovery from .bak and .tmp files
- JSON file filtering
- Incomplete file detection

### 5. LawFirmProcessor (`law_firm_processor.py`)
Processes attorney and law firm information:
- Extracts law firms from attorney data
- Normalizes law firm names
- Manages attorney lookup mappings
- Handles ignore_download overrides
- Removes deprecated keys

### 6. TransferHandler (`transfer_handler.py`)
Manages transfer case logic:
- Determines transfer status (transferred_in, is_transferred, pending_cto)
- Updates transferor cases with transferee information
- Manages S3 link inheritance from transferor cases
- District court name-to-ID lookups

### 7. MDLProcessor (`mdl_processor.py`)
Handles MDL-related operations:
- MDL number to court ID mapping
- Litigation classification via rules
- AFFF plaintiff counting (MDL 2873)
- MDL summary generation via LLM
- Title matching to MDL numbers

### 8. DocketValidator (`docket_validator.py`)
Validates data completeness:
- Checks required fields
- Validates date formats
- Marks completion with 'added_on' field
- Identifies processing errors

### 9. Uploader (`uploader.py`)
Manages AWS uploads:
- Batch uploads to S3 and DynamoDB
- S3 existence checking
- Parallel upload coordination
- Upload result tracking

### 10. Supporting Classes

#### LitigationClassifier (`litigation_classifier.py`)
- Rule-based litigation identification
- Keyword matching with AND/OR logic
- MDL number association

#### CachedPdfData (`cached_pdf_data.py`)
- Simple PDF text caching
- Litigation identification from cached text
- Signature page extraction (placeholder)

## Processing Pipeline

1. **Initialization Phase**
   - Load configuration
   - Initialize all components
   - Set up AWS connections
   - Load MDL lookup data

2. **File Discovery Phase**
   - Scan target directory for JSON files
   - Apply filters (skip files, patterns)
   - Recover from .bak/.tmp files
   - Determine processing list

3. **Processing Phase** (Parallel)
   - Load JSON data
   - Extract text from PDF/HTML
   - Run LLM extraction
   - Process law firms
   - Handle transfers
   - Add MDL information
   - Validate and save

4. **Upload Phase**
   - Batch upload to S3
   - Update DynamoDB records
   - Track upload results

5. **Reporting Phase**
   - Generate summary statistics
   - Log processing results

## Configuration

Key configuration parameters:
- `iso_date`: Processing date (YYYYMMDD)
- `num_workers`: Parallel processing threads
- `llm_provider`: LLM service (deepseek/openai)
- `normalize_law_firm_names`: Enable normalization mode
- `reprocess_md`: Force markdown reprocessing
- `bucket_name`: S3 bucket for uploads

## Error Handling

- Atomic file saves with backup/restore
- Processing error tracking in data
- Graceful degradation for missing services
- Comprehensive logging at all levels
- Recovery from interrupted processing

## Performance Optimizations

- Parallel processing with semaphores
- Lazy loading of lookup tables
- Caching for repeated lookups
- Batch operations for uploads
- Progress tracking with tqdm

## Integration Points

- **Input**: JSON files from PACER scraper
- **LLM Services**: DeepSeek/GPT for extraction
- **OCR Service**: MistralOCR for PDF text
- **AWS Services**: S3 for storage, DynamoDB for records
- **Output**: Enriched data for report generation