# GraphQL Parser Compatibility Update Summary

## Changes Made to `src/scripts/graphql_parser.py`

### 1. Import Updates
- **Added**: Import of `DeepSeek` (now required for summary generation)
- **Added**: Import of `VectorClusterer` (required for campaign field generation)

### 2. Field Initialization Updates
- **Removed**: Initialization of classification fields (`Category`, `Company`, `Product`, `Injuries`, `LitigationType`, `LitigationName`, `MdlName`)
- **Added**: `campaign` field placeholder (will be populated by VectorClusterer)
- **Kept**: `Summary` field (now generated by DeepSeek instead of GPT4)

### 3. Component Initialization Updates
- **Added**: DeepSeek instance initialization
- **Added**: VectorClusterer instance initialization with aiohttp session
- **Updated**: AIIntegrator now receives DeepSeek instance
- **Updated**: AdProcessor now receives VectorClusterer instance

### 4. Cleanup Updates
- **Added**: Cleanup for aiohttp session used by VectorClusterer

### 5. Command Line Arguments
- **Added**: `--disable-deepseek` flag to allow disabling DeepSeek if needed

## How It Works Now

1. **GraphQL Parser** extracts raw ad data from HTML files
2. **AdProcessor** structures the data and handles images
3. **AIIntegrator** generates summaries using DeepSeek (not GPT4)
4. **VectorClusterer** adds campaign field using rule-based matching
5. **FBAdArchiveManager** saves the processed ads to the database

## Fields Generated

The processed ads now contain:
- All original fields from the HTML (title, body, dates, etc.)
- `ImageText`: Extracted by LLaVA (if enabled)
- `Summary`: Generated by DeepSeek using the fb_ad_summary prompt
- `campaign`: Added by VectorClusterer based on rule matching
- `s3_image_key`: Added by ImageHandler

## Usage

```bash
# Basic usage (processes all HTML files)
python src/scripts/graphql_parser.py

# Process specific company
python src/scripts/graphql_parser.py --company-id *********

# Disable DeepSeek (no summaries will be generated)
python src/scripts/graphql_parser.py --disable-deepseek

# Verbose mode for debugging
python src/scripts/graphql_parser.py -v
```

## Notes

- The script no longer generates classification fields (Category, Company, Product, etc.)
- Summary generation now uses DeepSeek API instead of GPT4
- Campaign field is added through rule-based matching, not AI classification
- All other functionality remains the same