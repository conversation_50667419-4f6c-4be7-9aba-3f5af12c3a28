# Hybrid Classifier Architecture

## Overview

The Hybrid Classifier (`src/scripts/hybrid_classifier.py`) is a sophisticated multi-stage classification system that combines rule-based pattern matching with machine learning techniques to categorize Facebook ads into litigation campaigns. It implements a comprehensive pipeline optimized for M4 Mac processors with advanced parallel processing, intelligent caching, and multiple classification strategies.

## System Architecture

### Core Components

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Data Sources  │───▶│  Hybrid         │───▶│ Classification  │
│                 │    │  Classifier     │    │ Results         │
└─────────────────┘    └──────────────────┘    └─────────────────┘
│                      │                      │
├─ CSV Files          ├─ Rule Matcher       ├─ Campaign Labels
├─ DynamoDB           ├─ Vector Embedder    ├─ Confidence Scores  
├─ JSON Data          ├─ LLM Classifier     ├─ Company Names
└─ Ad Archive         └─ NER Processor      └─ Analytics Reports
```

### Data Flow Pipeline

```
Input Ad Data
      │
      ▼
┌─────────────────┐
│ Text Preparation│
│ - Combine fields│
│ - Normalize     │
│ - Clean data    │
└─────────────────┘
      │
      ▼
┌─────────────────┐
│ Skip Term Check │
│ - Fast rejection│
│ - Early exit    │
└─────────────────┘
      │
      ▼
┌─────────────────┐
│ Rule-Based      │
│ Classification  │
│ - Pattern match │
│ - Campaign rules│
└─────────────────┘
      │
      ▼
┌─────────────────┐
│ Vector Embedding│
│ - Similarity    │
│ - Clustering    │
└─────────────────┘
      │
      ▼
┌─────────────────┐
│ LLM Enhancement │
│ - Context aware │
│ - Final decision│
└─────────────────┘
      │
      ▼
┌─────────────────┐
│ Post-Processing │
│ - Company NER   │
│ - Name norm.    │
└─────────────────┘
```

## Core Classes and Components

### 1. HybridClassifier (Main Orchestrator)

**Purpose**: Primary classification engine that coordinates all classification stages.

**Key Responsibilities**:
- Orchestrates the entire classification pipeline
- Manages configuration and component initialization
- Handles batch processing with incremental saving
- Implements fallback strategies when components fail

**Data Flow**:
```
Ad Data → Text Preparation → Skip Check → Rule Match → Vector Embedding → LLM → Result
```

**Configuration Dependencies**:
- `campaign_config.json`: Campaign rules and patterns
- `embedding_cache.pkl`: Cached embeddings for performance
- `hybrid_classifier_config.yml`: System configuration

### 2. AdCampaignMatcher (Rule Engine)

**Purpose**: Fast rule-based pattern matching for known campaigns.

**Architecture**:
```python
Class Structure:
├── known_campaigns: List[Dict] - Pre-processed campaign rules
├── priority_rules: List[Dict] - High-priority patterns
├── standard_rules: List[Dict] - Standard matching rules
└── fallback_rules: List[Dict] - Catch-all patterns
```

**Matching Strategy**:
1. **Priority Rules**: Exact matches for high-confidence campaigns
2. **Standard Rules**: Pattern-based matching with triggers
3. **Fallback Rules**: Broad category matching

**Pattern Types**:
- **Triggers**: Required terms that must be present
- **Include**: Optional terms that boost confidence
- **Exclude**: Terms that disqualify a match

### 3. M4OptimizedEmbedder (Vector Processing)

**Purpose**: Generates and caches semantic embeddings for similarity-based classification with M4 Mac optimizations.

**Technical Implementation**:
- **Model**: SentenceTransformers with MPS/CUDA optimization
- **Caching**: Dual-mode caching system (in-memory or disk-based)
- **Performance**: Optimized for Apple Silicon (MPS) and NVIDIA GPUs
- **Memory Management**: Adaptive caching based on available system memory

**Data Flow**:
```
Text Input → Normalization → Cache Check → Model Inference → Vector Output → Cache Storage
                                 ↓                                              ↓
                            Cache Hit → Return Cached                    Disk/Memory Cache
```

**Cache Management**:
- **Disk Cache**: SQLite-based for low memory situations (<10GB available)
- **Memory Cache**: Pickle-based for high performance (>20GB available)
- **Lazy Loading**: Cache loaded on first use to reduce startup time
- **Batch Saving**: Incremental saves to prevent data loss
- **Hash-based Keys**: MD5 hashing for efficient lookup

### 4. LocalLLMClassifier (Language Model Integration)

**Purpose**: Provides context-aware classification using local language models.

**Supported Backends**:
- **Ollama**: Local model hosting (primary)
- **Transformers**: HuggingFace pipeline
- **MLX**: Apple Silicon optimization
- **Llama.cpp**: CPU-optimized inference

**Classification Strategy**:
```python
Enhanced Prompting:
├── Context-aware prompts
├── Few-shot examples
├── Confidence scoring
└── "Other" category handling
```

### 5. CompanyNameNormalizer (Entity Processing)

**Purpose**: Standardizes company names and extracts entities from ad text.

**Normalization Pipeline**:
1. **Text Cleaning**: Remove common legal suffixes
2. **Fuzzy Matching**: Handle variations and typos (85% similarity threshold)
3. **Canonical Mapping**: Map to standardized names
4. **Alias Management**: Track known variations
5. **Law Firm Filtering**: Exclude law firms from defendant company extraction

**NER Integration**:
- SpaCy-based entity extraction with transformer models
- Lazy-loaded NER cache for performance
- Batch processing with spaCy's nlp.pipe()
- Entity type filtering (ORG, PERSON, GPE)
- Law firm database integration for accurate filtering

## Classification Stages

### Stage 1: Text Preparation

**Input**: Raw ad data dictionary
**Output**: Cleaned, normalized text for processing

**Process**:
```python
def _prepare_ad_text_for_processing(ad_data, fields, invalid_strings):
    # Combine configured fields (Summary, Body, etc.)
    # Remove invalid/placeholder text
    # Normalize whitespace and encoding
    # Return raw and processed versions
```

**Field Priority**:
1. `summary` - Primary ad text
2. `body` - Extended description
3. `funding_entity` - Advertiser information
4. Custom fields from configuration

### Stage 2: Skip Term Filtering

**Purpose**: Fast rejection of obviously irrelevant ads.

**Implementation**:
- Pre-compiled regex patterns
- Early exit to avoid expensive processing
- Configurable skip terms list

**Performance Impact**: 70-80% reduction in processing time for irrelevant ads.

### Stage 3: Rule-Based Classification

**Input**: Normalized ad text
**Output**: Campaign match with confidence score

**Algorithm**:
```python
def match_campaign(text):
    for priority_rule in priority_rules:
        if exact_match(text, priority_rule.triggers):
            return high_confidence_result
    
    for standard_rule in standard_rules:
        if pattern_match(text, standard_rule):
            confidence = calculate_confidence(text, standard_rule)
            return standard_result(confidence)
    
    return no_match_result
```

**Confidence Calculation**:
- Trigger match: Base confidence
- Include terms: Boost confidence
- Exclude terms: Penalty/disqualification
- Text length and quality factors

### Stage 4: Vector Similarity

**Purpose**: Handle fuzzy matches and semantic similarity.

**Process**:
1. Generate embedding for input text
2. Compare against cached campaign embeddings
3. Calculate cosine similarity scores
4. Apply threshold-based classification

**Similarity Thresholds**:
- High confidence: > 0.85
- Medium confidence: 0.7 - 0.85
- Low confidence: 0.5 - 0.7
- No match: < 0.5

### Stage 5: LLM Enhancement

**Purpose**: Handle edge cases and provide context-aware decisions.

**Prompt Engineering**:
```
Context: {ad_context}
Text: {normalized_text}
Candidates: {campaign_labels}

Instructions:
- Analyze the text for litigation-related content
- Consider context and intent
- Assign to most appropriate campaign or "Other"
- Provide confidence score (0-100)
```

**Fallback Strategy**:
- Primary: Ollama local models
- Secondary: Transformers pipeline
- Tertiary: Rule-based only

## Configuration System

### Campaign Configuration (`campaign_config.json`)

**Structure**:
```json
{
  "Company": "Company Name",
  "LitigationName": "Legal Case Name", 
  "triggers": ["required", "terms"],
  "include": ["optional", "boost", "terms"],
  "exclude": ["disqualifying", "terms"],
  "priority": "high|standard|low"
}
```

**Rule Processing**:
- Triggers compiled to regex patterns
- Include/exclude terms normalized
- Priority determines matching order

### System Configuration (`hybrid_classifier_config.yml`)

**Complete Configuration Structure**:
```yaml
input:
  csv_file: null  # Optional CSV input
  dynamodb_table_name: "FBAdArchive"  # DynamoDB table
  local_dynamodb: true
  dynamodb_endpoint_url: "http://localhost:8000"
  start_date: null  # YYYYMMDD format
  limit: null  # Processing limit

campaigns:
  config_file: "src/config/fb_ad_categorizer/campaign_config.json"
  skip_terms_file: "src/config/fb_ad_categorizer/campaign_skip_terms.json"

processing:
  batch_size: 512  # Optimized for M4 Mac
  text_fields: ["Title", "Body", "Summary", "LinkDescription"]
  deduplication_fields: ["Title", "Body"]
  max_workers: null  # Auto-detect CPU cores
  parallel_chunk_size: 500

models:
  embedder:
    model_name: "all-MiniLM-L6-v2"
    cache_file: "embedding_cache.pkl"
  
  ner:
    model_name: "en_core_web_trf"
    cache_file: "ner_results_cache.pkl"
  
  llm:
    enabled: false
    backend: "ollama"  # ollama|mlx|llama_cpp|transformers
    model: "llama3:8b"
    cache_file: "llm_response_cache.pkl"
    enhanced_llm: true
    timeout: 120
    max_retries: 3
    retry_delay: 5

output:
  csv_file: "classified_ads_output.csv"
  deduplicated_csv_file: null

rules:
  improve_rules: false
  output_suggestions: "rule_suggestions.json"

logging:
  level: "INFO"
```

## Performance Optimization

### Advanced Caching System

**Three-Tier Cache Architecture**:

1. **NER Cache** (`ner_results_cache.pkl`):
   - Text-keyed entity extraction results
   - Lazy loading on first access
   - Batch warming with spaCy pipeline
   - Composite key support for backward compatibility

2. **Embedding Cache** (adaptive mode):
   - **High Memory Mode** (>20GB): In-memory pickle cache
   - **Low Memory Mode** (<10GB): Disk-based SQLite cache
   - Model-specific cache files (e.g., `embedding_cache_all-MiniLM-L6-v2.pkl`)
   - Automatic migration from pickle to disk cache

3. **LLM Response Cache** (`llm_response_cache.pkl`):
   - Hashed request/response pairs
   - Backend-specific caching (Ollama, MLX, etc.)
   - Configurable cache file paths

**Cache Management Commands**:
```bash
# Delete specific caches
python src/scripts/hybrid_classifier.py --rebuild-ner
python src/scripts/hybrid_classifier.py --rebuild-embeddings
python src/scripts/hybrid_classifier.py --rebuild-llm

# Delete all caches
python src/scripts/hybrid_classifier.py --rebuild-all-caches
```

### M4 Mac Optimized Parallel Processing

**Intelligent Worker Management**:
- Auto-detection of CPU cores (up to 24 workers)
- Adaptive chunk sizing based on dataset size
- Sequential fallback for large datasets (>10K items)

**Processing Strategies**:
```python
Dataset Size → Processing Mode:
├── < 100 ads → Sequential (avoid overhead)
├── 100-10K ads → Parallel (optimal performance)
└── > 10K ads → Sequential (memory efficient)
```

**Parallel Pipeline**:
1. **Pre-processing Stage**: 
   - Parallel NER cache warming
   - Batch embedding generation
   - Progress tracking with Rich UI

2. **Classification Stage**:
   - Multiprocessing with ProcessPoolExecutor
   - Worker process pooling
   - Chunk-based task distribution
   - Error recovery per chunk

**Memory Optimization**:
- psutil-based memory monitoring
- Adaptive batch sizes (256-2048)
- Forced garbage collection between batches
- Incremental cache saving

## Data Sources and Integration

### Input Formats

**CSV Files**:
- Standard ad export format
- Configurable column mapping
- Automatic type inference

**DynamoDB**:
- Direct table scanning
- Parallel segment processing
- Optimized query patterns

**JSON Data**:
- Flexible schema handling
- Nested field extraction
- Error-tolerant parsing

### Output Formats

**Classification Results**:
```python
@dataclass
class ClassificationResult:
    ad_id: str
    campaign: str
    company: str
    confidence: float
    method: str  # rule|vector|llm|hybrid
    processing_time: float
    metadata: Dict[str, Any]
```

**Analytics Reports**:
- Campaign distribution analysis
- Confidence score histograms
- Processing performance metrics
- Error and edge case reports

## Error Handling and Resilience

### Graceful Degradation

**Component Failures**:
1. LLM unavailable → Fall back to vector similarity
2. Vector model loading fails → Use rule-based only
3. NER pipeline errors → Skip entity extraction
4. Cache corruption → Rebuild incrementally

### Data Quality Issues

**Invalid Text Handling**:
- Empty or null text detection
- Encoding error recovery
- Malformed JSON handling
- Missing field accommodation

**Recovery Strategies**:
- Incremental processing checkpoints
- Failed item reprocessing
- Partial result preservation
- Detailed error logging

## Monitoring and Analytics

### Performance Metrics

**Classification Quality**:
- Confidence score distributions
- Method usage statistics
- Processing time analysis
- Error rate tracking

**System Performance**:
- Memory usage monitoring
- GPU utilization tracking
- Cache hit rates
- Throughput measurement

### Debugging Tools

**Classification Analysis**:
- Rule matching trace
- Vector similarity scores
- LLM response analysis
- Decision path visualization

**Interactive Configuration**:
- Rule testing interface
- Threshold tuning tools
- Campaign management UI
- Performance profiling

## Command-Line Interface

### Basic Usage

```bash
# Basic classification with CSV input
python src/scripts/hybrid_classifier.py --input-csv ads.csv --output-csv results.csv

# Use DynamoDB as input source
python src/scripts/hybrid_classifier.py --input-dynamodb-table-name FBAdArchive

# Interactive mode
python src/scripts/hybrid_classifier.py --interactive

# YAML configuration
python src/scripts/hybrid_classifier.py --config config/hybrid_classifier.yml
```

### Cache Management

```bash
# Rebuild specific caches
python src/scripts/hybrid_classifier.py --rebuild-ner
python src/scripts/hybrid_classifier.py --rebuild-embeddings  
python src/scripts/hybrid_classifier.py --rebuild-llm

# Rebuild all caches
python src/scripts/hybrid_classifier.py --rebuild-all-caches

# Combine with processing
python src/scripts/hybrid_classifier.py --rebuild-embeddings --config config.yml
```

### Performance Tuning

```bash
# Control parallel processing
python src/scripts/hybrid_classifier.py --max-workers 8 --parallel-chunk-size 500

# Force parallel preprocessing (even with low memory)
python src/scripts/hybrid_classifier.py --force-parallel-preprocessing

# Limit processing for testing
python src/scripts/hybrid_classifier.py --limit 1000 --batch-size 128
```

### Model Configuration

```bash
# Specify embedding model
python src/scripts/hybrid_classifier.py --embedder-model all-mpnet-base-v2

# Configure LLM backend
python src/scripts/hybrid_classifier.py --use-llm --llm-backend ollama --llm-model llama3:8b

# Disable LLM enhancement
python src/scripts/hybrid_classifier.py --no-enhanced-llm
```

### Advanced Options

```bash
# Rule improvement analysis
python src/scripts/hybrid_classifier.py --improve-rules --output-rule-suggestions suggestions.json

# Deduplication
python src/scripts/hybrid_classifier.py --output-deduplicated-csv dedup_results.csv \
    --deduplication-fields Title Body

# Date filtering (DynamoDB)
python src/scripts/hybrid_classifier.py --input-dynamodb-table-name FBAdArchive \
    --start-date 20240101

# Local DynamoDB
python src/scripts/hybrid_classifier.py --local-dynamodb \
    --dynamodb-endpoint-url http://localhost:8000
```

## Extension Points

### Adding New Classification Methods

**Plugin Architecture**:
```python
class CustomClassifier:
    def classify(self, text: str) -> ClassificationResult:
        # Custom logic implementation
        pass
    
    def get_confidence(self, text: str, result: str) -> float:
        # Confidence calculation
        pass
```

### Custom Rule Types

**Rule Engine Extensions**:
- Regex pattern enhancements
- Semantic rule definitions
- Context-aware matching
- Multi-field coordination

### Model Integration

**New Backend Support**:
- Additional LLM providers
- Custom embedding models
- Specialized NER systems
- Domain-specific classifiers

## Dependencies and Requirements

### Core Dependencies
- `torch`: Neural network computations
- `sentence-transformers`: Embedding generation
- `transformers`: LLM integration
- `spacy`: NLP and NER
- `pandas/numpy`: Data processing
- `rich`: UI and progress tracking

### Optional Dependencies
- `ollama`: Local LLM hosting
- `mlx`: Apple Silicon optimization
- `llama-cpp-python`: CPU inference
- `fuzzywuzzy`: String similarity
- `boto3`: AWS integration

### Hardware Requirements
- **Minimum**: 8GB RAM, 4 CPU cores
- **Recommended**: 16GB RAM, 8 cores, GPU
- **Optimal**: 32GB RAM, 16 cores, Apple Silicon/NVIDIA GPU

## Enhanced Features

### DBSCAN Clustering for "Other" Campaigns

**Purpose**: Group unclassified ads into meaningful clusters for analysis and rule generation.

**Implementation**:
- Automatic eps estimation using k-nearest neighbors
- Adaptive min_samples based on dataset size
- Fallback to KMeans for sparse data
- Cluster-based LLM campaign suggestions

### Interactive Mode

**Features**:
- Rich UI with menu-driven configuration
- Real-time configuration validation
- Progress visualization during processing
- Result analytics and reporting

**Menu Options**:
1. Configure Input/Output
2. Configure Processing Options
3. Configure AI Models
4. View Current Configuration
5. Start Processing

### Law Firm Integration

**Database Integration**:
- Pre-loaded law firm cache from DynamoDB
- Fuzzy matching for law firm identification
- Automatic filtering from company extraction
- PageAlias and Name field matching

### Enhanced Company Extraction

**Multi-Source Extraction**:
1. Campaign configuration Company field (highest priority)
2. NER extraction from ORG entities
3. GPE entities for misclassified companies
4. PERSON entities for eponymous companies
5. Campaign name parsing (fallback)

**Validation Pipeline**:
- Legal suffix removal
- Law firm exclusion
- Geographic location filtering
- Fuzzy deduplication

## Future Enhancements

### Planned Features
1. **Active Learning**: Incorporate user feedback
2. **Multi-modal**: Image and text classification
3. **Real-time**: Streaming classification pipeline
4. **Explainability**: Decision reasoning output
5. **Auto-tuning**: Automated threshold optimization

### Scalability Improvements
1. **Distributed Processing**: Multi-node classification
2. **Stream Processing**: Kafka/Kinesis integration
3. **Model Serving**: REST API deployment
4. **Caching**: Redis/Memcached integration
5. **Monitoring**: Prometheus/Grafana metrics