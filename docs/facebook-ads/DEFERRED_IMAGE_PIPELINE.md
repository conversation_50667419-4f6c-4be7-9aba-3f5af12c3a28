# Deferred Image Processing Pipeline

## Overview

The deferred image processing pipeline allows the Facebook ads scraper to operate in two distinct phases:

1. **Fast Scraping Phase**: Download and store images without text extraction
2. **Processing Phase**: Extract text from queued images using OCR

This separation significantly improves scraping performance while maintaining complete data processing capability.

## Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Ad Scraping   │    │   Image Queue    │    │ Image Processor │
│                 │───►│                  │───►│                 │
│ - Download imgs │    │ - SQLite storage │    │ - OCR via LLaVA │
│ - Calculate hash│    │ - Status tracking│    │ - Text storage  │
│ - Queue for OCR │    │ - Retry logic    │    │ - Progress track│
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│      S3         │    │   Local SQLite   │    │   DynamoDB      │
│                 │    │                  │    │                 │
│ - Image storage │    │ - Queue metadata │    │ - Text results  │
│ - Fast upload   │    │ - Processing log │    │ - Ad updates    │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## Phase 1: Fast Scraping with Queue Population

### Configuration

Enable deferred processing via configuration:

```python
# In config
config['defer_image_processing'] = True

# Or via environment variable
export DEFER_IMAGE_PROCESSING=true

# Or via CLI flag
python orchestrator.py --defer-image-processing
```

### Processing Flow

When deferred processing is enabled, the `AdProcessor` modifies its behavior:

#### 1. Image Download and Storage

```python
# processor.py - process_raw_ads()
if self.defer_image_processing and s3_key_result and self.local_queue:
    logger.debug(f"Adding image to processing queue for later text extraction")
    
    # Check if PHash already exists
    actual_phash = None
    should_queue_for_text_extraction = True
    
    # Query existing PHash records
    existing_records = self.image_handler.hash_manager.get_hash_by_archive_id(ad_archive_id)
    for record in existing_records:
        if record.get('AdCreativeID') == ad_creative_id:
            actual_phash = record.get('PHash')
            existing_image_text = record.get('ImageText', '')
            
            # Skip if text already exists
            if existing_image_text and existing_image_text.strip():
                should_queue_for_text_extraction = False
                break
```

#### 2. Queue Entry Creation

```python
# Only queue if text extraction is needed
if should_queue_for_text_extraction:
    queue_hash = actual_phash if actual_phash else f"{ad_archive_id}_{ad_creative_id}"
    
    self.local_queue.add_to_queue(
        image_hash=queue_hash,
        ad_archive_id=ad_archive_id,
        start_date=start_date,
        s3_path=s3_key_result,
        scrape_date=self.current_process_date,
        creative_id=ad_creative_id,
        law_firm_name=law_firm_name
    )
    
    # Set ImageText to None for deferred processing
    ad_dict_structured['ImageText'] = None
```

### Performance Benefits

**Speed Improvements**:
- 60-80% faster scraping (no OCR wait time)
- Parallel image downloads without AI bottlenecks
- Reduced memory usage during scraping

**Resource Optimization**:
- AI models not loaded during scraping phase
- Lower GPU memory requirements
- Better CPU utilization separation

## Local Image Queue (SQLite)

### Database Schema

```sql
CREATE TABLE image_queue (
    image_hash TEXT PRIMARY KEY,        -- PHash or fallback ID
    ad_archive_id TEXT NOT NULL,       -- Facebook ad ID
    start_date TEXT NOT NULL,          -- Ad start date (YYYYMMDD)
    s3_path TEXT NOT NULL,             -- S3 key for image
    creative_id TEXT,                  -- Creative ID
    law_firm_name TEXT,                -- Law firm name
    scrape_date TEXT NOT NULL,         -- Date queued (YYYYMMDD)
    status TEXT DEFAULT 'pending',     -- pending|processing|processed|failed|skipped
    queued_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP,
    image_text TEXT,                   -- Extracted text result
    processing_time_ms INTEGER,        -- Processing duration
    error TEXT,                        -- Error message if failed
    retry_count INTEGER DEFAULT 0,     -- Number of retry attempts
    last_retry_at TIMESTAMP,           -- Last retry timestamp
    phash TEXT                         -- Actual perceptual hash
);

CREATE TABLE queue_summary (
    scrape_date TEXT PRIMARY KEY,      -- Date summary (YYYYMMDD)
    total_queued INTEGER DEFAULT 0,    -- Items in queue
    total_processed INTEGER DEFAULT 0, -- Successfully processed
    total_failed INTEGER DEFAULT 0,    -- Permanently failed
    total_skipped INTEGER DEFAULT 0,   -- Skipped (403, etc.)
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Key Operations

#### Adding Items to Queue

```python
# local_image_queue.py
def add_to_queue(self, image_hash: str, ad_archive_id: str, 
                 start_date: str, s3_path: str, scrape_date: str,
                 creative_id: str = None, law_firm_name: str = None) -> bool:
    """Add image to processing queue with duplicate detection."""
    
    with self._get_connection() as conn:
        # Insert with IGNORE to prevent duplicates
        conn.execute("""
            INSERT OR IGNORE INTO image_queue 
            (image_hash, ad_archive_id, start_date, s3_path, 
             creative_id, law_firm_name, scrape_date) 
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """, (image_hash, ad_archive_id, start_date, s3_path, 
              creative_id, law_firm_name, scrape_date))
        
        # Update summary statistics
        conn.execute("""
            INSERT INTO queue_summary (scrape_date, total_queued)
            VALUES (?, 1)
            ON CONFLICT(scrape_date) DO UPDATE SET
                total_queued = total_queued + 1,
                last_updated = CURRENT_TIMESTAMP
        """, (scrape_date,))
        
        return conn.total_changes > 0
```

#### Queue Status Tracking

```python
def get_pending(self, limit: int = 1000, scrape_date: str = None) -> List[Dict]:
    """Get pending items for processing."""
    
    query = """
        SELECT image_hash, ad_archive_id, start_date, s3_path, 
               creative_id, law_firm_name, scrape_date, queued_at, phash
        FROM image_queue 
        WHERE status = 'pending'
    """
    
    if scrape_date:
        query += " AND scrape_date = ?"
        params = [scrape_date, limit]
    else:
        params = [limit]
    
    query += " ORDER BY queued_at ASC LIMIT ?"
    
    with self._get_connection() as conn:
        cursor = conn.execute(query, params)
        return [dict(row) for row in cursor]
```

## Phase 2: Image Processing

### Processing Script

The image processing is handled by a separate script that can be run independently:

```bash
# Process all pending images
python src/scripts/process_image_queue.py --process

# Process specific date
python src/scripts/process_image_queue.py --process --date 20241201

# Process with custom batch size
python src/scripts/process_image_queue.py --process --batch-size 50

# Show queue statistics
python src/scripts/process_image_queue.py --status
```

### Processing Workflow

#### 1. Queue Scanning

```python
# Get pending items from queue
pending_items = local_queue.get_pending(limit=batch_size, scrape_date=target_date)

if not pending_items:
    logger.info("No pending images to process")
    return

logger.info(f"Found {len(pending_items)} pending images to process")
```

#### 2. Batch Processing

```python
# Mark items as processing to prevent concurrent access
image_hashes = [item['image_hash'] for item in pending_items]
marked_count = local_queue.mark_processing(image_hashes)
logger.info(f"Marked {marked_count} items as processing")

# Process each item
for item in pending_items:
    try:
        start_time = time.time()
        
        # Extract text using LLaVA
        image_text = await llava_extractor.extract_text_from_s3_image(
            item['s3_path'], 
            prompt="Extract all text visible in this image"
        )
        
        processing_time_ms = int((time.time() - start_time) * 1000)
        
        # Update queue with results
        local_queue.mark_processed(
            item['image_hash'], 
            image_text, 
            processing_time_ms,
            item.get('phash')
        )
        
        # Update DynamoDB FBImageHash table
        await update_dynamodb_hash_record(item, image_text)
        
    except Exception as e:
        logger.error(f"Failed to process {item['image_hash']}: {e}")
        local_queue.mark_failed(item['image_hash'], str(e))
```

#### 3. DynamoDB Integration

```python
async def update_dynamodb_hash_record(item: Dict, image_text: str):
    """Update FBImageHash table with extracted text."""
    
    # Find existing record
    existing_records = hash_manager.get_hash_by_archive_id(item['ad_archive_id'])
    
    for record in existing_records:
        if record.get('AdCreativeID') == item['creative_id']:
            # Update existing record
            record['ImageText'] = image_text
            record['LastUpdated'] = datetime.utcnow().isoformat()
            
            success = hash_manager.put_item(record)
            if success:
                logger.info(f"Updated ImageText for PHash {record.get('PHash')}")
            else:
                logger.error(f"Failed to update ImageText for {item['image_hash']}")
            break
```

### Error Handling and Retries

#### Retry Logic

```python
def mark_failed(self, image_hash: str, error: str) -> bool:
    """Mark item as failed with retry logic."""
    
    with self._get_connection() as conn:
        # Get current retry count
        cursor = conn.execute(
            "SELECT scrape_date, retry_count FROM image_queue WHERE image_hash = ?",
            (image_hash,)
        )
        row = cursor.fetchone()
        
        if not row:
            return False
        
        retry_count = row['retry_count']
        
        # Determine if should retry or permanently fail
        new_status = 'failed' if retry_count >= 2 else 'pending'
        
        conn.execute("""
            UPDATE image_queue 
            SET status = ?, error = ?, retry_count = retry_count + 1,
                last_retry_at = CURRENT_TIMESTAMP
            WHERE image_hash = ?
        """, (new_status, error, image_hash))
        
        return True
```

#### Error Categories

**Retryable Errors**:
- Temporary network issues
- LLaVA service unavailable
- Rate limiting

**Non-Retryable Errors**:
- Image file corrupted
- S3 access denied (403)
- Invalid image format

**Skipped Items**:
- Images requiring login
- Deleted/inaccessible content
- Copyright restricted images

## Queue Management

### Status Monitoring

```python
def get_detailed_stats(self, scrape_date: str) -> Dict[str, Any]:
    """Get comprehensive queue statistics."""
    
    with self._get_connection() as conn:
        # Status counts
        cursor = conn.execute("""
            SELECT status, COUNT(*) as count 
            FROM image_queue 
            WHERE scrape_date = ?
            GROUP BY status
        """, (scrape_date,))
        
        status_counts = {row['status']: row['count'] for row in cursor}
        
        # Processing time statistics
        cursor = conn.execute("""
            SELECT 
                COUNT(*) as processed_count,
                AVG(processing_time_ms) as avg_time_ms,
                MIN(processing_time_ms) as min_time_ms,
                MAX(processing_time_ms) as max_time_ms
            FROM image_queue 
            WHERE scrape_date = ? AND status = 'processed'
        """, (scrape_date,))
        
        time_stats = dict(cursor.fetchone())
        
        return {
            'scrape_date': scrape_date,
            'status_counts': status_counts,
            'processing_time_stats': time_stats,
            'total_items': sum(status_counts.values())
        }
```

### Cleanup Operations

```python
# Reset stuck processing items
def reset_processing_items(self, scrape_date: str = None) -> int:
    """Reset items stuck in 'processing' status."""
    
    with self._get_connection() as conn:
        query = "UPDATE image_queue SET status = 'pending' WHERE status = 'processing'"
        params = []
        
        if scrape_date:
            query += " AND scrape_date = ?"
            params.append(scrape_date)
        
        conn.execute(query, params)
        return conn.total_changes

# Clean old processed items
def cleanup_old_processed(self, days_to_keep: int = 7) -> int:
    """Remove old processed items to save space."""
    
    with self._get_connection() as conn:
        conn.execute("""
            DELETE FROM image_queue 
            WHERE status = 'processed'
            AND processed_at < datetime('now', '-' || ? || ' days')
        """, (days_to_keep,))
        
        return conn.total_changes
```

## Integration with Main Pipeline

### Configuration Detection

The system automatically detects deferred processing configuration:

```python
# processor.py - __init__
self.defer_image_processing = (
    self.config.get('defer_image_processing', False) or
    self.config.get('processing', {}).get('defer_image_processing', False) or
    os.environ.get('DEFER_IMAGE_PROCESSING', '').lower() == 'true'
)

if self.defer_image_processing:
    queue_dir = (
        self.config.get('image_queue_dir') or
        self.config.get('processing', {}).get('image_queue', {}).get('directory') or
        './data/image_queue'
    )
    self.local_queue = LocalImageQueue(queue_dir)
```

### AI Integrator Coordination

The AI integrator checks for existing text before processing:

```python
# ai_integrator.py
async def enhance_ad_data(self, ad_dict: Dict[str, Any]) -> Dict[str, Any]:
    """Enhance ad with AI-generated content."""
    
    # Check if we have deferred image text available
    if ad_dict.get('ImageText') is None and self.defer_image_processing:
        # Look up processed text from queue
        processed_text = self.get_processed_image_text(
            ad_dict.get('ad_archive_id'),
            ad_dict.get('start_date')
        )
        if processed_text:
            ad_dict['ImageText'] = processed_text
    
    # Continue with normal AI enhancement
    return await self._process_with_ai(ad_dict)
```

## Performance Monitoring

### Key Metrics

**Throughput**:
- Images queued per hour
- Images processed per hour
- Average processing time per image

**Success Rates**:
- Queue success rate (added vs failed)
- Processing success rate (completed vs failed)
- Retry effectiveness

**Resource Usage**:
- Queue database size
- Processing memory usage
- GPU utilization during OCR

### Logging and Alerting

```python
# Example monitoring setup
def log_processing_metrics(stats: Dict[str, Any]):
    """Log processing metrics for monitoring."""
    
    logger.info(f"Processing Summary for {stats['scrape_date']}:")
    logger.info(f"  Total Items: {stats['total_items']}")
    logger.info(f"  Processed: {stats['status_counts'].get('processed', 0)}")
    logger.info(f"  Failed: {stats['status_counts'].get('failed', 0)}")
    logger.info(f"  Pending: {stats['status_counts'].get('pending', 0)}")
    logger.info(f"  Avg Processing Time: {stats['processing_time_stats']['avg_time_ms']:.0f}ms")
```

## Best Practices

### 1. Queue Management

- Monitor queue depth to prevent overflow
- Regular cleanup of old processed items
- Reset stuck processing items before new runs

### 2. Error Handling

- Implement exponential backoff for retries
- Categorize errors for appropriate handling
- Log sufficient detail for debugging

### 3. Performance Optimization

- Batch process images for efficiency
- Use appropriate worker counts for your hardware
- Monitor resource usage and adjust accordingly

### 4. Data Consistency

- Ensure PHash accuracy for deduplication
- Validate S3 paths before processing
- Maintain referential integrity between queue and main data

The deferred image processing pipeline provides significant performance improvements while maintaining data completeness, making it ideal for large-scale Facebook ad data collection operations.