# Ad Page Generation Guide

## Overview

This guide documents the ad page generation system for LexGenius reports, including recent improvements for reliability and error recovery.

## Architecture

### Components

1. **AdPageGenerator** (`src/lib/reports/ad_page_generator.py`)
   - Generates individual HTML pages for each ad
   - Uploads pages to S3 with retry logic
   - Validates successful uploads
   - Tracks and logs failed uploads

2. **ReportOrchestrator** (`src/lib/reports/orchestrator.py`)
   - Coordinates the entire report generation process
   - Calls ad page generation after data processing
   - Validates ad pages before including them in reports

3. **Ad Page Template** (`src/assets/templates/view_ad_v2.html`)
   - Jinja2 template for individual ad pages
   - Responsive design with Facebook-style ad rendering
   - Includes ad metadata and interactive elements

## Key Features

### 1. Retry Mechanism with Exponential Backoff

The system now implements automatic retry for failed S3 uploads:

```python
async def _upload_ad_task(self, ad_html: str, ad_archive_id: str, retry_count: int = 3) -> Tuple[str, bool, Optional[str]]:
    """Task for uploading a single ad HTML to S3 with retry logic."""
    for attempt in range(retry_count):
        try:
            if attempt > 0:
                await asyncio.sleep(2 ** attempt)  # Exponential backoff
            
            success = await asyncio.to_thread(
                self.s3_manager.upload_html_string,
                html_string=ad_html,
                object_name=s3_path,
                content_type='text/html',
                overwrite=True
            )
            
            if success:
                return ad_archive_id, True, None
        except Exception as upload_err:
            last_error = f"{type(upload_err).__name__}: {str(upload_err)}"
    
    return ad_archive_id, False, last_error
```

### 2. Failed Upload Tracking

Failed uploads are automatically saved to JSON files for debugging and recovery:

```json
{
  "report_date": "20250527",
  "timestamp": "20250529_123456",
  "total_failures": 3,
  "failed_ads": [
    {
      "ad_id": "1788443798685155",
      "error": "TimeoutError: Upload timed out"
    }
  ]
}
```

Location: `data/{report_date}/failed_ad_uploads/failed_ads_{report_date}_{timestamp}.json`

### 3. Ad Page Validation

Before publishing reports, the system validates that all referenced ad pages exist:

```python
async def validate_ad_pages(self, ad_ids: List[str]) -> Dict[str, bool]:
    """Validate that ad HTML pages exist on S3."""
    validation_results = {}
    
    for ad_id in ad_ids:
        s3_path = f"{self.config.iso_date}/ads/{ad_id}.html"
        exists = await self._check_ad_exists(ad_id, s3_path)
        validation_results[ad_id] = exists
    
    return validation_results
```

### 4. Automatic Filtering

Ads with missing pages are automatically filtered from reports to prevent broken links:

```python
# In orchestrator.py
validation_results = await self.ad_page_generator.validate_ad_pages(ad_ids)
missing_ads = [ad_id for ad_id, exists in validation_results.items() if not exists]

if missing_ads:
    # Filter out missing ads from report data
    daily_report_data['ad_df'] = ad_df[~ad_df['ad_archive_id'].isin(missing_ads)]
```

## Ad Page Regeneration Utility

### Overview

The `regenerate_missing_ads.py` script provides tools for recovering missing ad pages.

### Usage

#### 1. Find Missing Ads

```bash
python regenerate_missing_ads.py 20250527 --find-missing
```

Output:
```
Missing ad IDs:
  1788443798685155
  2394857629485729
  ...
```

#### 2. Regenerate Specific Ads

```bash
python regenerate_missing_ads.py 20250527 --ad-ids 1788443798685155 2394857629485729
```

#### 3. Regenerate All Missing Ads

```bash
python regenerate_missing_ads.py 20250527 --regenerate-missing
```

#### 4. Regenerate from Failed Log

```bash
python regenerate_missing_ads.py 20250527 --failed-log data/20250527/failed_ad_uploads/failed_ads_20250527_123456.json
```

### How It Works

1. **Data Retrieval**: Fetches ad data from FBAdArchive DynamoDB table
2. **Data Transformation**: Converts DynamoDB format to report format
3. **HTML Generation**: Uses the same template and rendering as regular reports
4. **S3 Upload**: Uploads to the correct S3 bucket with proper paths
5. **Validation**: Verifies successful upload

## Troubleshooting

### Common Issues

#### 1. Empty Ad Pages

**Symptom**: Ad link returns 404 or empty page

**Causes**:
- Ad HTML never uploaded due to S3 error
- Network timeout during upload
- Concurrent upload limit reached

**Solution**:
```bash
# Check if ad exists in database
python check_specific_ad.py

# Regenerate the missing page
python regenerate_missing_ads.py 20250527 --ad-ids 1788443798685155
```

#### 2. Bulk Upload Failures

**Symptom**: Many ads missing from a report

**Causes**:
- S3 service issues
- Network connectivity problems
- Rate limiting

**Solution**:
```bash
# Find and regenerate all missing ads
python regenerate_missing_ads.py 20250527 --regenerate-missing
```

#### 3. Persistent Upload Failures

**Symptom**: Same ads fail repeatedly

**Check**:
- S3 bucket permissions
- Ad data integrity in database
- Template rendering errors

**Debug**:
```python
# Check failed upload logs
cat data/20250527/failed_ad_uploads/failed_ads_*.json
```

### Monitoring

#### Log Files

Ad page generation logs are included in the main report generation logs:

```
INFO - Generating 2957 ad HTML pages.
INFO - Ensuring S3 folders exist for date: 20250527
INFO - Finished generating 2957 ad HTML contents.
INFO - Uploading 2957 ad HTMLs to S3 using up to 10 workers.
WARNING - S3 upload task failed for ad 1788443798685155 (attempt 1/3)
INFO - Retry attempt 2/3 for ad 1788443798685155
ERROR - Failed to upload ad 1788443798685155 after 3 attempts. Last error: TimeoutError
INFO - Finished uploading ad HTMLs. Successful: 2956, Failed: 1.
ERROR - Failed to upload 1 ad pages:
ERROR -   - Ad 1788443798685155: TimeoutError: Upload timed out
INFO - Saved 1 failed ad uploads to: data/20250527/failed_ad_uploads/failed_ads_20250527_123456.json
```

#### Validation Output

```
INFO - Ad page validation: 2956/2957 exist, 1 missing
WARNING - Missing ad pages: ['1788443798685155']
WARNING - Filtering 1 ads with missing pages from report
```

## Configuration

### S3 Configuration

Ad pages are stored in the main reports S3 bucket:

```python
# Bucket: lexgenius-dockets (or as configured)
# Path: {date}/ads/{ad_archive_id}.html
# URL: https://cdn.lexgenius.ai/{date}/ads/{ad_archive_id}.html
```

### Upload Settings

Configure in `ReportConfig`:

```python
self.s3_upload_workers: int = 10  # Concurrent upload workers
```

### Retry Settings

Configure in `AdPageGenerator._upload_ad_task()`:

```python
retry_count: int = 3  # Number of retry attempts
# Backoff: 2^attempt seconds (2s, 4s, 8s)
```

## Best Practices

1. **Monitor Failed Uploads**: Check failed upload logs after each report generation
2. **Regenerate Promptly**: Run regeneration for missing ads before reports are widely accessed
3. **Validate Critical Reports**: For important reports, manually verify key ad pages
4. **Archive Failed Logs**: Keep failed upload logs for debugging patterns

## Future Improvements

1. **Real-time Regeneration**: API endpoint to regenerate missing ads on-demand
2. **CloudFront Integration**: Automatic cache invalidation after regeneration
3. **Monitoring Dashboard**: Visual dashboard for ad page health
4. **Automated Recovery**: Scheduled job to find and fix missing ads

## Related Documentation

- [Report Generation Guide](./report_generation_guide.md)
- [S3 Upload Guide](../infrastructure/s3_upload_guide.md)
- [Facebook Ads Processing](../facebook-ads/README.md)