# Report Section Configuration Guide

## Overview

The report sections in the `ReportRenderer` are now configured via a JSON file located at `src/config/reports/section_config.json`. This change makes it easier to manage and modify report sections without changing the code.

## Configuration File Location

```
src/config/reports/section_config.json
```

## Configuration Structure

The configuration file contains a single `sections` array with objects defining each section:

```json
{
  "sections": [
    {
      "id": "summary",
      "title": "Report Summary",
      "show_flag_attr": "show_summary",
      "icon_class": "fas fa-chart-line",
      "web_template_wrapper": "includes/sections/_section_simple_wrapper.html",
      "web_content_template": "includes/_summary_web_content.html",
      "email_template_type": "special_summary",
      "web_default_expanded": true
    },
    // ... more sections
  ]
}
```

## Section Properties

Each section object can have the following properties:

- **`id`** (required): Unique identifier for the section
- **`title`** (required): Display title for the section
- **`show_flag_attr`** (required): Config attribute name that controls section visibility
- **`icon_class`**: Font Awesome icon class for the section
- **`data_attr`**: Report data attribute containing section content
- **`web_template_wrapper`**: Template file for web wrapper
- **`web_content_template`**: Template file for web content
- **`email_template_type`**: Type of email rendering (full, collapsed, special_summary)
- **`email_content_template`**: Template file for email content
- **`web_default_expanded`**: Whether section is expanded by default on web
- **`email_always_expanded`**: Force expansion in email
- **`email_simple_wrapper`**: Use simple wrapper for email
- **`sponsorship_slot`**: Sponsorship slot identifier
- **`extra_*`**: Additional styling properties (extra_icon_classes, extra_wrapper_style, etc.)

## Adding a New Section

1. Edit `src/config/reports/section_config.json`
2. Add a new object to the `sections` array:

```json
{
  "id": "new-section",
  "title": "New Section Title",
  "show_flag_attr": "show_new_section",
  "icon_class": "fas fa-star",
  "data_attr": "new_section_data",
  "web_template_wrapper": "includes/sections/_section_collapsable_web.html",
  "web_content_template": "includes/_new_section_content.html",
  "email_template_type": "full",
  "email_content_template": "includes/_new_section_content.html",
  "web_default_expanded": false
}
```

3. Add the corresponding flag to `src/config/reports/core_config.json`:

```json
{
  "show_new_section": true,
  // ... other flags
}
```

4. Create the template files referenced in the configuration
5. Ensure the data attribute is populated in the report processor

## Modifying Section Order

The order of sections in the rendered report is determined by their order in the `sections` array. Simply reorder the objects in the array to change the display order.

## Caching

The section configuration is cached after the first load to improve performance. To force a reload during runtime:

```python
renderer.reload_section_config()
```

## Testing

A test script is available at `test_files/test_section_config_loading.py` to verify the configuration loads correctly:

```bash
python test_files/test_section_config_loading.py
```

## Benefits

1. **Easier Management**: No code changes needed to modify section properties
2. **Clear Structure**: All section configurations in one place
3. **Version Control**: Track changes to section configuration separately
4. **Runtime Flexibility**: Potential for dynamic section loading (with cache reload)
5. **Reduced Code Complexity**: Cleaner renderer code without hardcoded configuration