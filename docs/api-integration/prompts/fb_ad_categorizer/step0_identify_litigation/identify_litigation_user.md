# User Prompt: Identify Specific Litigation

Analyze the following input text based *only* on the list of known litigations and rules provided in the system prompt.

**Input Text:**

*   **Title:** `{ad_title}`
*   **Body:** `{ad_body}`
*   **Image Text:** `{ad_image_text}`
*   **Link Description:** `{ad_link_description}`
*   **Video Text (if available):** `{ad_video_text}`

**Task:**

Identify which specific litigation from the system prompt's list the input text pertains to. Return a JSON object containing the exact `LitigationName` if a clear match is found according to the trigger, include, exclude, and context rules. If no specific litigation from the list is clearly identified, return `"No Specific Litigation Identified"` as the value.

**Output:**

```json
{
  "identified_litigation_name": "string"
}
```