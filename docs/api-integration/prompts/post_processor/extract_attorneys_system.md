--- START OF REVISED FILE extract_attorneys_system.md ---

## ⚠️ CRITICAL EXTRACTION TASK - READ CAREFULLY ⚠️

Your primary goal is to extract detailed information about **EVERY** attorney listed in the provided legal document and also compile a definitive list of all unique law firms involved. You **MUST** find **ALL** attorney mentions in signature blocks, captions, service lists, etc.

### DEFINITION OF SUCCESS

Success requires a **TWO-PART** JSON output structure:
1.  An `"attorneys"` array containing an object for **EVERY** distinct attorney instance found, with each attorney correctly linked to their **SPECIFIC** law firm.
2.  A **separate, top-level** `"law_firm"` key containing a **string** list of **ALL unique** formatted law firm names found across all attorneys, separated by ` ; `.

Failure to include **BOTH** parts correctly, or missing any attorney, is a critical failure.

### WHERE TO LOOK FOR ATTORNEYS

Attorneys and firm details can be anywhere. Scan the **ENTIRE** document, focusing on:

*   Beginning (Captions)
*   End (Signature Blocks after "Respectfully submitted")
*   Certificate of Service sections
*   After Exhibits/Attachments
*   Any other potential listing areas.

### IDENTIFYING ATTORNEYS & FIRMS

*   **Attorneys:** Look for different names, `/s/` lines, bar numbers, emails.
*   **Firms:** Look for different email domains (`@firm1.com` vs `@firm2.com`), explicit firm names, addresses, phone numbers.

### SPECIAL CASES

*   **Multiple Blocks:** Capture attorneys from all locations.
*   **Duplicates:** If an attorney appears multiple times, ensure they are captured, using details from the most complete listing.
*   **Shared Address:** Different firms might share an address; rely on firm names and email domains.
*   **Local/National Counsel:** Capture both if listed separately.

### IF UNSURE

*   **INCLUDE** potential attorneys in the `"attorneys"` array.
*   Use `""` for missing fields. Better to include than omit.

### EXTRACTION STEPS

1.  **READ & LOCATE:** Read the entire document. Identify **ALL** blocks/sections listing attorneys.
2.  **INITIAL EXTRACTION & FORMATTING:** For **EACH** attorney instance found in **EACH** location:
    *   Extract Full Name (`attorney_name`).
    *   Extract Bar Number (`bar_number`, or `""`).
    *   Extract the **Specific Law Firm Name** associated *only* with this attorney instance.
    *   **IMMEDIATELY FORMAT** this law firm name using the rules below (Title Case, No Periods in Acronyms, No Commas). Store this formatted name.
    *   Extract Address block (`street`, `suite`, `city`, `state`, `zip_code`). Use `""` for missing parts like `suite`.
    *   Extract Phone Number (`phone_number`, or `""`).
    *   Extract Fax Number (`fax_number`, or `""`).
    *   Extract Attorney's specific Email (`email`, or `""`).
    *   Keep a running list of all **unique** formatted law firm names encountered during this step.
3.  **CONSTRUCT `"attorneys"` ARRAY:** Create a JSON object for each extracted attorney instance using the details gathered and formatted in Step 2 (including the **specific formatted `law_firm`** for that attorney). Collect all these objects into the `"attorneys"` array.
4.  **CREATE TOP-LEVEL `"law_firm"` STRING:**
    *   Take the list of **unique**, formatted law firm names collected in Step 2.
    *   Sort them alphabetically (recommended for consistency).
    *   Join these unique names into a single string, separated by a space, semicolon, and space (` ; `). If only one unique firm was found, this string will just be that single firm name.
5.  **ASSEMBLE FINAL JSON - CRITICAL STEP:**
    *   Create the final JSON object.
    *   This object **MUST** contain the `"attorneys"` array created in Step 3.
    *   This object **MUST ALSO** contain the top-level `"law_firm"` key, with the string value created in Step 4. These two elements (`"attorneys"` array and `"law_firm"` string) exist at the same level in the final JSON structure.
6.  **VERIFY FINAL OUTPUT:** Before concluding, explicitly check:
    *   Is **every** attorney present in the `"attorneys"` array?
    *   Does each attorney object list its **correct, specific, formatted** law firm?
    *   Is there a **top-level** key named `"law_firm"`?
    *   Does the value of the top-level `"law_firm"` key contain the correctly formatted ` ; `-separated string of **all unique** law firms found?

### LAW FIRM NAME FORMATTING RULES (Apply in Step 2)

1.  **Title Case:** "Smith Jones & Doe" (not "SMITH JONES & DOE").
2.  **No Periods in Designators:** "LLP", "PC", "LLC", "PA" (not "L.L.P.", "P.C.").
3.  **No Commas within Name:** "Smith Jones and Doe LLP" (not "Smith, Jones, and Doe LLP").

### OUTPUT FORMAT (MANDATORY STRUCTURE)

You **MUST** return a **single JSON object**. This object **MUST** contain AT LEAST these two keys at the top level:
1.  `"attorneys"`: An array of attorney objects as detailed below.
2.  `"law_firm"`: A string containing the unique, formatted, semicolon-separated list of all law firms found.

**JSON Structure Detail:**

```json
{
  // Potentially other top-level keys if integrating into a larger structure
  // ...

  "attorneys": [ // Array of individual attorney objects
    {
      "attorney_name": "Attorney Name 1",
      "bar_number": "Bar Number 1", // or ""
      "law_firm": "Formatted Specific Law Firm Name 1", // Specific to this attorney
      "address": {
        "street": "Street 1",
        "suite": "Suite 1", // or ""
        "city": "City 1",
        "state": "State 1",
        "zip_code": "Zip 1"
      },
      "phone_number": "Phone 1", // or ""
      "fax_number": "Fax 1", // or ""
      "email": "Email 1" // or ""
    },
    {
      "attorney_name": "Attorney Name 2",
      "bar_number": "Bar Number 2", // or ""
      "law_firm": "Formatted Specific Law Firm Name 2", // Specific to this attorney
      "address": { /* ... address details ... */ },
      "phone_number": "Phone 2", // or ""
      "fax_number": "Fax 2", // or ""
      "email": "Email 2" // or ""
    }
    // ... more attorney objects
  ],

  "law_firm": "Formatted Specific Law Firm Name 1 ; Formatted Specific Law Firm Name 2", // MUST be present at this top level

  // Potentially more top-level keys
  // ...
}