# Prompt for Generating Proper Versus Title and Plaintiff List in JSON Format

**Objective:** Generate a JSON object containing a properly formatted case title string (Plaintiff v. Defendant) and the original list of plaintiffs. The title generation includes special handling for anonymous plaintiffs ("<PERSON>", etc.), plaintiffs identified only by initials ("<PERSON><PERSON><PERSON><PERSON>", etc.), and ensures preservation of name suffixes (Jr., Sr., II, III, etc.) for all parties.

**Inputs:**

1.  `original_filename`: (String) The original filename, potentially containing case name parts (e.g., "25_42342_{plaintiff}_v_{defendant}"). Assume names within the filename might be separated by underscores or other delimiters and may require cleaning.
2.  `plaintiffs`: (List of Strings) A list containing the full names or identifiers of all plaintiffs. This exact list should be returned in the output.
3.  `defendants`: (List of Strings) A list containing the full names or identifiers of all defendants.

**Processing Rules (for `versus` Title Generation):**

**I. Plaintiff Selection:**

*   **Helper Step: Identify Name Type:** For plaintiff names (especially the first plaintiff and those considered for filename matching), determine if they represent:
    *   a) An Anonymous Plaintiff (starts case-insensitively with "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>").
    *   b) An Initials-Only Plaintiff (consists only of capital letters, periods, and spaces, e.g., "B. D.", "A.C.", "F. T. B."). A pattern like `^[A-Z]\.?(\s*[A-Z]\.?)*$` can identify these.
    *   c) A Standard Name (anything else, typically including a first and last name, potentially with suffixes).
*   **Helper Step: Suffix Handling:** When extracting last names for Standard Names (Type c) for matching or selection (Rules I.3, I.4), identify and temporarily separate common suffixes (Jr., Sr., II, III, IV, etc.). The core last name is used for matching. If a standard name with a suffix is selected, the suffix must be re-appended later (See Rule III.4).

1.  **Check for Anonymous Plaintiff Exception (Type a):**
    *   Examine the **first** plaintiff name in the `plaintiffs` list.
    *   If it is Type a (Anonymous):
        *   The Plaintiff part is the **full name** of this first plaintiff.
        *   **Apply Specific Casing:**
            *   Ensure "Jane Doe", "John Doe", "Jane Roe", or "John Roe" is Title Cased.
            *   Ensure subsequent text *not* in parentheses (codes, identifiers) is UPPERCASE ("LS 580").
            *   Ensure text *within* parentheses is UPPERCASE ("(AB)").
            *   Preserve standard name suffixes with standard casing ("John Doe Jr.").
            *   Example: "JANE DOE NLG (AB)" -> "Jane Doe NLG (AB)". "Jane Doe ls 580" -> "Jane Doe LS 580". "JOHN DOE JR" -> "John Doe Jr.".
        *   Append " et al." if `len(plaintiffs) > 1`.
        *   **Skip rules I.2, I.3, I.4.** Proceed to Defendant Selection (II).

2.  **Check for Initials-Only Plaintiff Exception (Type b):**
    *   Examine the **first** plaintiff name in the `plaintiffs` list.
    *   If Rule I.1 did not apply AND the first plaintiff is Type b (Initials-Only):
        *   The Plaintiff part is the first plaintiff's name, **formatted by removing all spaces and keeping existing periods.**
        *   Example: "B. D." -> "B.D.", "A C" -> "AC", "F.T. B." -> "F.T.B.".
        *   Append " et al." if `len(plaintiffs) > 1`.
        *   **Skip rules I.3, I.4.** Proceed to Defendant Selection (II).

3.  **Attempt Filename Matching (Standard or Initials):**
    *   If Rules I.1 and I.2 did not apply:
    *   Create a list of "search terms" and corresponding "display forms" for *each* plaintiff in the `plaintiffs` list:
        *   If plaintiff is Type c (Standard): Search term is the core last name. Display form is `LastName Suffix` (if suffix exists).
        *   If plaintiff is Type b (Initials-Only): Search term is the formatted initials string (spaces removed, periods kept, e.g., "B.D."). Display form is the same formatted initials string.
        *   (Type a plaintiffs are ignored here as they are handled by Rule I.1).
    *   Attempt to find matches for the **search terms** within the `original_filename`. Matching should be reasonably robust against case variations and surrounding non-alphanumeric characters.
    *   Identify all plaintiffs whose search terms were found in the `original_filename`.
    *   **If Matches Found:**
        *   Determine which match appears *earliest* in the `original_filename`.
        *   The Plaintiff part of the title is the **display form** corresponding to that earliest match.
        *   Append " et al." if EITHER:
            *   More than one *unique plaintiff* had their search term match in the filename.
            *   OR the total number of plaintiffs in the `plaintiffs` list is greater than 1.
        *   **Skip rule I.4.** Proceed to Defendant Selection (II).

4.  **Default Plaintiff Selection:**
    *   If Rules I.1, I.2 did not apply and no matches were found in Rule I.3:
    *   Select the **first** plaintiff from the `plaintiffs` list.
    *   Determine its type (a, b, or c). It cannot be Type a here.
    *   If Type b (Initials-Only): The Plaintiff part is the formatted initials string (spaces removed, periods kept, e.g., "B.D.").
    *   If Type c (Standard): The Plaintiff part is the `LastName Suffix` (if suffix exists).
    *   Append " et al." if `len(plaintiffs) > 1`.

**II. Defendant Selection:** (Logic remains focused on Last Names for simplicity, unless specified otherwise)

*   **Helper Step: Suffix Handling:** Identify and separate suffixes when extracting defendant last names for matching or selection.

1.  **Attempt Filename Matching:**
    *   Extract the **core last name** from each defendant in the `defendants` list (excluding suffixes temporarily).
    *   Attempt to find matches for these last names within the `original_filename` (robust against case/delimiters).
    *   Identify all defendant last names from the list that are present in the `original_filename`.
    *   **If Matches Found:**
        *   The Defendant part of the title is the last name that appears *earliest* in the `original_filename`.
        *   **Important:** If the full name corresponding to this selected last name includes a suffix, **append the suffix**.
        *   **Skip rule II.2.**

2.  **Default Defendant Selection (No Filename Match):**
    *   If no matches were found in Rule II.1:
    *   The Defendant part of the title is the **last name** of the *first* defendant listed in the `defendants` list.
    *   **Important:** If this first defendant's full name includes a suffix, **append the suffix**.

3.  **Append "et al." for Multiple Defendants:**
    *   After determining the base Defendant name (including suffix if applicable), **always** append " et al." if `len(defendants) > 1`.

**III. Final Formatting (for `versus` Title):**

1.  **Combine:** Construct the title string as: `{Plaintiff} v. {Defendant}`.
2.  **Clean:** Remove extraneous characters (e.g., `{`, `}`) from filename extractions *before* use. Ensure single spacing overall.
3.  **Case Finalization:**
    *   Apply Title Case to Standard Plaintiff last names (Rule I.3, I.4) and Defendant last names (Rule II.1, II.2).
    *   Use specific casing for Anonymous Plaintiffs (Rule I.1).
    *   Use uppercase for Initials-Only Plaintiffs (Rule I.2, I.3, I.4), ensuring periods are kept and spaces removed (e.g., "B.D.", "F.T.B.").
    *   Keep "v." lowercase.
    *   Keep "et al." lowercase.
    *   Ensure suffixes (Jr., Sr., II, III, etc.) retain standard casing.
4.  **Spacing:** Ensure exactly one space before/after "v.". Ensure one space before " et al." if appended. Ensure one space between a standard last name and its suffix ("Smith Jr."). Ensure no spaces within formatted initials ("B.D.").

**Output Requirements:**

*   Return a JSON object containing:
    *   The key `versus` with the final formatted title string (generated according to rules I-III) as its value.
    *   The key `plaintiff_gpt` with the *original, unmodified* list of plaintiff names (as provided in the `plaintiffs` input) as its value.

```json
{
  "versus": "Properly Formatted Case Title",
  "plaintiffs_gpt": ["Plaintiff Name 1 From Input", "Plaintiff Name 2 From Input", ...]
}