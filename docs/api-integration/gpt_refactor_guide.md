# Guide for Refactoring to Use GPTService

This guide provides steps for updating modules that use GPT or similar text generation services to use the new `GPTService` implementation.

## Step 1: Identify Files to Update

Look for files that:
- Import or use `gpt4_interface` or similar
- Make direct calls to OpenAI's API
- Use text generation, classification, or summarization

## Step 2: Update Imports

Replace existing imports:

```python
# BEFORE
from src.lib.gpt4_interface import generate_text, summarize
# OR
import src.lib.gpt4_interface as gpt

# AFTER
from src.lib.ai.services.text.gpt_service import GPTService
from src.lib.ai.orchestration.ai_coordinator import AICoordinator
```

## Step 3: Update Initialization Code

Replace direct initialization with the new service pattern:

```python
# BEFORE
gpt_client = gpt4_interface.Client(api_key="your_api_key")

# AFTER
# Option 1: Using AICoordinator (recommended)
config = {
    "openai_api_key": "your_api_key",
    "text_service": "openai",
    "openai_model": "gpt-4o"  # or your preferred model
}
ai_coordinator = AICoordinator(config)
# Use the coordinator for text tasks

# Option 2: Direct service initialization
config = {
    "openai_api_key": "your_api_key",
    "openai_model": "gpt-4o"
}
gpt_service = GPTService(config)
await gpt_service.initialize()
```

## Step 4: Update Function Calls

Replace direct function calls with service methods:

```python
# BEFORE
text = gpt4_interface.generate_text(prompt, max_tokens=1000)
summary = gpt4_interface.summarize(content)

# AFTER
# Option 1: Using AICoordinator (recommended)
await ai_coordinator.initialize()
text = await ai_coordinator.generate_text(prompt, max_tokens=1000)
summary = await ai_coordinator.summarize(content)

# Option 2: Direct service usage
text = await gpt_service.generate_text(prompt, max_tokens=1000)
summary = await gpt_service.summarize(content)
```

## Step 5: Update Classification Code

Replace custom classification code with the built-in functionality:

```python
# BEFORE
prompt = f"Classify this text: {text}\nCategories: {', '.join(categories)}\nOutput only the category."
result = gpt4_interface.generate_text(prompt, temperature=0)
category = result.strip()

# AFTER
result = await gpt_service.classify(text, categories=categories)
category = result.get("category")
```

## Step 6: Error Handling

Update error handling to match the new service:

```python
# BEFORE
try:
    text = gpt4_interface.generate_text(prompt)
except Exception as e:
    logger.error(f"Text generation failed: {e}")

# AFTER
try:
    text = await gpt_service.generate_text(prompt)
except AIServiceError as e:
    logger.error(f"GPT service error: {e}")
except Exception as e:
    logger.error(f"Unexpected error during text generation: {e}")
```

## Step 7: Cleanup

Ensure proper cleanup for async resources:

```python
# AFTER
try:
    # Use GPT service...
finally:
    # Cleanup
    await gpt_service.close()
    # OR if using coordinator
    await ai_coordinator.close()
```

## Step 8: Testing

Update any tests to match the new service pattern:

```python
# Mock the new service in tests
@patch("src.lib.ai.services.text.gpt_service.GPTService")
async def test_text_generation(mock_gpt_service):
    # Set up mock
    mock_instance = AsyncMock()
    mock_instance.generate_text.return_value = "Mocked response"
    mock_gpt_service.return_value = mock_instance
    
    # Test the function that uses GPT
    result = await your_function_that_uses_gpt()
    
    # Verify
    assert mock_instance.generate_text.called
    assert "Mocked response" in result
```

## Example: Complete Refactoring

Here's a complete before and after example:

### Before:

```python
import logging
from src.lib.gpt4_interface import Client as GPTClient

logger = logging.getLogger(__name__)

class TextProcessor:
    def __init__(self, api_key):
        self.gpt_client = GPTClient(api_key=api_key)
    
    def analyze_text(self, text, categories=None):
        if not categories:
            categories = ["Positive", "Negative", "Neutral"]
            
        try:
            # Classification
            prompt = f"Classify this text into one of these categories: {', '.join(categories)}.\nText: {text}\nCategory:"
            category = self.gpt_client.generate_text(prompt, temperature=0.1).strip()
            
            # Summarization
            summary = self.gpt_client.summarize(text)
            
            return {
                "success": True,
                "category": category,
                "summary": summary
            }
        except Exception as e:
            logger.error(f"Text analysis failed: {e}")
            return {
                "success": False,
                "error": str(e)
            }
```

### After:

```python
import logging
import asyncio
from src.lib.ai.services.text.gpt_service import GPTService
from src.lib.ai.exceptions import AIServiceError

logger = logging.getLogger(__name__)

class TextProcessor:
    def __init__(self, api_key):
        self.config = {
            "openai_api_key": api_key,
            "openai_model": "gpt-4o"
        }
        self.gpt_service = None
    
    async def initialize(self):
        if not self.gpt_service:
            self.gpt_service = GPTService(self.config)
            await self.gpt_service.initialize()
    
    async def analyze_text(self, text, categories=None):
        if not categories:
            categories = ["Positive", "Negative", "Neutral"]
            
        try:
            await self.initialize()
            
            # Classification - now uses built-in method
            classification = await self.gpt_service.classify(text, categories=categories)
            
            # Summarization - now uses built-in method
            summary = await self.gpt_service.summarize(text)
            
            return {
                "success": True,
                "category": classification.get("category"),
                "summary": summary
            }
        except AIServiceError as e:
            logger.error(f"GPT service error: {e}")
            return {
                "success": False,
                "error": str(e)
            }
        except Exception as e:
            logger.error(f"Unexpected error during text analysis: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def close(self):
        if self.gpt_service:
            await self.gpt_service.close()
            self.gpt_service = None
    
    # Synchronous wrapper for backwards compatibility
    def analyze_text_sync(self, text, categories=None):
        loop = asyncio.get_event_loop()
        try:
            return loop.run_until_complete(self.analyze_text(text, categories))
        finally:
            loop.run_until_complete(self.close())
```

This guide provides a comprehensive approach for refactoring modules to use the new GPTService.