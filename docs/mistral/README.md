# Mistral OCR Documentation

This directory contains documentation for the Mistral OCR system, which provides automated PDF text extraction using Mistral's direct OCR API with batch job management capabilities.

## 📁 Documentation Structure

- **[batch_ocr_guide.md](batch_ocr_guide.md)** - Complete user guide and usage instructions
- **[api_reference.md](api_reference.md)** - Technical API reference and code documentation
- **[troubleshooting.md](troubleshooting.md)** - Common issues and solutions
- **[examples.md](examples.md)** - Usage examples and workflows

## 🚀 Quick Start

1. **Setup Environment**
   ```bash
   export MISTRAL_API_KEY="your_api_key_here"
   ```

2. **Process PDFs for a Date**
   ```bash
   python src/scripts/mistral_batch_ocr.py --date 20250115
   ```

3. **Monitor Legacy Batch Jobs**
   ```bash
   python src/scripts/mistral_batch_ocr.py --list-jobs
   ```

4. **Cancel Pending Jobs and Download Results**
   ```bash
   python src/scripts/mistral_batch_ocr.py --cancel-pending
   ```

## 📋 Overview

The Mistral OCR system automatically:
- Discovers PDF files in `data/YYYYMMDD/dockets/` directories
- Processes them using Mistral's direct OCR API for high-quality results
- Provides real-time progress tracking with rich UI
- Falls back to PyMuPDF extraction if OCR API fails
- Manages legacy batch jobs (cancel, download results)
- Provides comprehensive logging and error handling

## 🆕 Recent Updates

**v2.0 - Direct OCR API Integration:**
- Switched from batch processing to direct OCR API calls for reliability
- Added job cancellation and result download functionality
- Improved error handling with PyMuPDF fallback
- Enhanced progress tracking and status display

## 🔗 Related Documentation

- [Mistral Batch API Documentation](https://docs.mistral.ai/capabilities/batch/)
- [Project Configuration Guide](../configuration/CONFIGURATION_GUIDE.md)
- [Operations Workflows](../operations/WORKFLOW_SUMMARY.md)

## 📞 Support

For issues or questions:
1. Check the [troubleshooting guide](troubleshooting.md)
2. Review the [API reference](api_reference.md)
3. Examine log files in `data/YYYYMMDD/logs/mistral_ocr.log`