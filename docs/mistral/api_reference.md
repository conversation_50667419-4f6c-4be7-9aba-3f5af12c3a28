# Mistral OCR API Reference

## 📋 Overview

This document provides technical reference for the `MistralBatchOCR` class and related functionality in the Mistral OCR script. The script now uses Mistral's direct OCR API for processing with legacy batch job management capabilities.

## 🏗️ Class Architecture

### MistralBatchOCR

Main class for handling Mistral OCR processing.

```python
class MistralBatchOCR:
    def __init__(self, data_dir: str = "data", max_workers: int = 4)
```

#### Constructor Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `data_dir` | `str` | `"data"` | Base directory for data files |
| `max_workers` | `int` | `4` | Maximum workers (legacy parameter) |

#### Attributes

| Attribute | Type | Description |
|-----------|------|-------------|
| `console` | `Console` | Rich console instance for formatted output |
| `data_dir` | `Path` | Path object for data directory |
| `client` | `Mistral` | Mistral API client instance |
| `jobs` | `Dict[str, Dict]` | Legacy job tracking dictionary |
| `stats` | `Dict[str, int]` | Processing statistics |
| `logger` | `Logger` | Configured logger instance |

## 📊 Statistics Tracking

### Stats Dictionary Structure

```python
stats = {
    "total_pdfs": 0,           # Total PDFs discovered
    "skipped_existing": 0,     # PDFs with existing MD files
    "queued_for_processing": 0, # PDFs queued for OCR
    "in_progress": 0,          # Currently processing
    "completed": 0,            # Successfully completed
    "failed": 0,               # Failed processing
    "total_pages": 0,          # Total pages (if available)
    "processed_pages": 0,      # Successfully processed pages
    "failed_pages": 0          # Failed pages
}
```

## 🔧 Core Methods

### File Discovery

#### `get_date_range(start_date: str, end_date: str) -> List[str]`

Generate list of date strings between start and end dates.

**Parameters:**
- `start_date` (str): Start date in YYYYMMDD format
- `end_date` (str): End date in YYYYMMDD format

**Returns:**
- `List[str]`: List of date strings in YYYYMMDD format

**Example:**
```python
processor = MistralBatchOCR()
dates = processor.get_date_range("20250101", "20250103")
# Returns: ["20250101", "20250102", "20250103"]
```

#### `discover_pdfs(date_dirs: List[str]) -> List[Tuple[Path, Path]]`

Discover PDF files and their potential MD counterparts.

**Parameters:**
- `date_dirs` (List[str]): List of date directory names

**Returns:**
- `List[Tuple[Path, Path]]`: List of (pdf_path, md_path) tuples for processing

**Side Effects:**
- Updates `self.stats` with discovery results
- Logs discovery progress and skipped files

**Directory Structure Support:**
- Flat structure: `data/YYYYMMDD/dockets/*.pdf`
- Nested structure: `data/YYYYMMDD/dockets/*/.*pdf`

**Example:**
```python
processor = MistralBatchOCR()
pdf_pairs = processor.discover_pdfs(["20250115"])
for pdf_path, md_path in pdf_pairs:
    print(f"Will process: {pdf_path} -> {md_path}")
```

### Direct OCR Processing

#### `process_pdf_directly(pdf_path: Path, md_path: Path) -> bool`

Process a single PDF using direct OCR API call.

**Parameters:**
- `pdf_path` (Path): Path to the PDF file
- `md_path` (Path): Path where MD file will be saved

**Returns:**
- `bool`: True if successful, False if failed

**Processing Flow:**
1. Read and base64-encode PDF
2. Call Mistral OCR API
3. Extract structured markdown from response
4. Save MD file with metadata
5. Fall back to PyMuPDF if OCR fails

**OCR API Call:**
```python
response = self.client.ocr.process(
    model="mistral-ocr-latest",
    document={
        "type": "document_url",
        "document_url": f"data:application/pdf;base64,{pdf_base64}"
    },
    include_image_base64=False
)
```

**Fallback Processing:**
```python
import fitz  # PyMuPDF
doc = fitz.open(str(pdf_path))
for page_num in range(doc.page_count):
    page = doc[page_num]
    page_text = page.get_text()
    # Process text...
```

#### `async process_with_direct_api(pdf_pairs: List[Tuple[Path, Path]])`

Main async processing method using direct OCR API.

**Parameters:**
- `pdf_pairs` (List[Tuple[Path, Path]]): PDFs to process

**Processing Flow:**
1. Process PDFs one by one
2. Show real-time progress for each file
3. Update live status display
4. Handle errors gracefully

**Example:**
```python
import asyncio

processor = MistralBatchOCR()
pdf_pairs = processor.discover_pdfs(["20250115"])
await processor.process_with_direct_api(pdf_pairs)
```

### Legacy Job Management

#### `list_jobs(status_filter: Optional[str] = None)`

List all batch jobs with optional status filter.

**Parameters:**
- `status_filter` (Optional[str]): Filter by job status

**Valid Status Values:**
- `QUEUED`
- `RUNNING`
- `SUCCESS`
- `FAILED`
- `TIMEOUT_EXCEEDED`
- `CANCELLED`

#### `cancel_pending_jobs(download_completed: bool = True)`

Cancel pending jobs and optionally download completed ones.

**Parameters:**
- `download_completed` (bool): Whether to download results from successful jobs

**Processing Flow:**
1. List all existing batch jobs
2. Download results from successful jobs (if requested)
3. Cancel jobs with QUEUED or RUNNING status
4. Skip already completed or failed jobs
5. Process downloaded results into MD files

**Example:**
```python
processor = MistralBatchOCR()
# Cancel pending jobs and download successful results
processor.cancel_pending_jobs(download_completed=True)
# Cancel pending jobs without downloading
processor.cancel_pending_jobs(download_completed=False)
```

#### `process_batch_results(results_content: str, job_id: str) -> int`

Process batch results and create MD files.

**Parameters:**
- `results_content` (str): Raw JSONL content from batch job
- `job_id` (str): Batch job ID for metadata

**Returns:**
- `int`: Number of successfully processed files

**Result Processing:**
1. Parse JSONL response lines
2. Extract custom IDs and match to original PDFs
3. Create MD files from extracted text
4. Log processing results

### User Interface

#### `create_status_table() -> Table`

Create a rich table showing current processing status.

**Returns:**
- `Table`: Rich table object for display

**Table Columns:**
- Metric name
- Count
- Percentage of total

#### `show_job_details(job_id: str, copy_url: bool = False)`

Show detailed information about a specific job.

**Parameters:**
- `job_id` (str): Job ID to display
- `copy_url` (bool): Whether to copy job URL to clipboard

**Side Effects:**
- Prints detailed job table to console
- Optionally copies Mistral console URL to clipboard

### Utility Methods

#### `setup_logging(log_dir: Optional[Path] = None)`

Setup logging configuration.

**Parameters:**
- `log_dir` (Optional[Path]): Custom log directory

**Default Log Location:**
```
{data_dir}/logs/mistral_ocr.log
```

**Log Levels:**
- File handler: INFO and above
- Console handler: WARNING and above (via Rich)

## 🔧 Configuration Constants

### API Configuration

```python
# Direct OCR API
OCR_MODEL = "mistral-ocr-latest"
OCR_ENDPOINT = "/v1/ocr/process"

# Legacy batch processing (fallback)
BATCH_MODEL = "pixtral-large-latest"
BATCH_ENDPOINT = "/v1/chat/completions"
MAX_TOKENS = 4000
```

### Processing Defaults

```python
DEFAULT_MAX_WORKERS = 4  # Legacy parameter
DEFAULT_DATA_DIR = "data"
ASYNC_SLEEP_INTERVAL = 0.5  # seconds between file processing
```

### OCR Configuration

```python
OCR_DOCUMENT_CONFIG = {
    "type": "document_url",
    "include_image_base64": False
}

FALLBACK_OCR_PROMPT = """Please extract all text from this PDF document. 
Maintain the structure and formatting as much as possible. 
If there are multiple pages, clearly separate them with page breaks. 
Return only the extracted text without any additional commentary."""
```

## 📁 File Structures

### Generated MD Files (OCR API)

```markdown
# OCR Extracted Text - {pdf_filename}

**Source PDF:** {pdf_filename}
**Processed:** {iso_timestamp}
**Method:** Direct OCR API call
**Pages:** {page_count}

---

--- Page 1 ---
{structured_markdown_content_page_1}

--- Page 2 ---
{structured_markdown_content_page_2}

...
```

### Generated MD Files (Fallback)

```markdown
# OCR Extracted Text - {pdf_filename}

**Source PDF:** {pdf_filename}
**Processed:** {iso_timestamp}
**Method:** Manual text extraction (PyMuPDF fallback)
**Pages:** {page_count}

---

--- Page 1 ---
{raw_text_content_page_1}

--- Page 2 ---
{raw_text_content_page_2}

...
```

### Legacy Batch Results

Downloaded batch results are saved as:
```
batch_results_{job_id}.jsonl
```

Each line contains:
```json
{"custom_id": "0_document_001", "response": {"body": {"choices": [{"message": {"content": "extracted text..."}}]}}}
```

## 🚨 Error Handling

### Exception Types

| Exception | Cause | Handling |
|-----------|-------|----------|
| `EnvironmentError` | Missing MISTRAL_API_KEY | Exit with error message |
| `FileNotFoundError` | Missing PDF or directory | Log warning, continue |
| `JSONDecodeError` | Malformed API response | Log error, skip item |
| `APIError` | Mistral API issues | Fall back to PyMuPDF |
| `IOError` | File read/write issues | Log error, skip file |

### Error Recovery Strategy

```python
def process_pdf_directly(self, pdf_path: Path, md_path: Path) -> bool:
    try:
        # Try OCR API first
        response = self.client.ocr.process(...)
        # Process OCR response
        return True
    except Exception as e:
        self.logger.error(f"OCR API failed: {e}")
        try:
            # Fall back to PyMuPDF
            import fitz
            # Extract text manually
            return True
        except Exception as fallback_error:
            self.logger.error(f"Fallback also failed: {fallback_error}")
            return False
```

## 🔍 API Response Structures

### OCR API Response

```python
response = OCRResponse(
    pages=[
        OCRPageObject(
            index=0,
            markdown="# Page Content\n\nExtracted text...",
            images=[],
            dimensions=OCRPageDimensions(dpi=200, height=2200, width=1700)
        ),
        # ... additional pages
    ]
)
```

### Legacy Batch Job Info

```python
job_info = {
    "id": "job_id",
    "status": "SUCCESS|FAILED|RUNNING|...",
    "created_at": "2025-01-15T10:00:00Z",
    "total_requests": 100,
    "completed_requests": 100,
    "failed_requests": 0,
    "output_file": "file_id_or_none",
    "error_file": "file_id_or_none",
    "metadata": {"job_type": "ocr_processing"}
}
```

## 🔧 Testing

### Unit Test Examples

```python
def test_pdf_discovery():
    """Test PDF discovery functionality."""
    processor = MistralBatchOCR(data_dir="test_data")
    pdf_pairs = processor.discover_pdfs(["20250115"])
    assert len(pdf_pairs) == expected_count

def test_direct_ocr_processing():
    """Test direct OCR processing."""
    processor = MistralBatchOCR()
    success = processor.process_pdf_directly(
        Path("test.pdf"), 
        Path("test.md")
    )
    assert success is True
    assert Path("test.md").exists()
```

### Mock OCR Response

```python
mock_ocr_response = OCRResponse(
    pages=[
        OCRPageObject(
            index=0,
            markdown="# Test Document\n\nThis is test content.",
            images=[],
            dimensions=OCRPageDimensions(dpi=200, height=2200, width=1700)
        )
    ]
)
```

## 📚 Dependencies

### Required Packages

```python
import argparse          # CLI argument parsing
import asyncio           # Async processing
import base64           # PDF encoding
import json             # JSON handling (legacy jobs)
import logging          # Logging system
import os               # Environment variables
import sys              # System functions
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Tuple

import pyperclip        # Clipboard operations
from mistralai import Mistral  # Mistral API client
from rich.console import Console  # Rich formatting
from rich.live import Live      # Live updates
from rich.logging import RichHandler  # Rich logging
from rich.table import Table    # Rich tables
from rich.text import Text      # Rich text

# Optional fallback dependency
import fitz             # PyMuPDF for text extraction
```

### Environment Requirements

- Python 3.11+
- MISTRAL_API_KEY environment variable
- Network connectivity to api.mistral.ai
- PyMuPDF for fallback text extraction
- Sufficient disk space for results and logs

## 🔗 External API Integration

### Mistral OCR API

| Endpoint | Method | Purpose |
|----------|--------|---------|
| `/v1/ocr/process` | POST | Direct OCR processing |

**Request Format:**
```python
{
    "model": "mistral-ocr-latest",
    "document": {
        "type": "document_url",
        "document_url": "data:application/pdf;base64,{base64_data}"
    },
    "include_image_base64": False
}
```

### Legacy Batch API Endpoints

| Endpoint | Method | Purpose |
|----------|--------|---------|
| `/v1/batch/jobs` | GET | List all jobs |
| `/v1/batch/jobs/{id}` | GET | Get job status |
| `/v1/batch/jobs/{id}/cancel` | POST | Cancel job |
| `/v1/files/{id}/content` | GET | Download results |

### Rate Limits and Performance

- **OCR API**: ~2-5 seconds per PDF
- **Direct processing**: No batch size limitations
- **Fallback processing**: ~0.5-1 second per PDF
- **Legacy job management**: Standard API rate limits apply

### Cost Considerations

- **Direct OCR API**: Per-request pricing
- **High-quality output**: Worth the cost for structured content
- **Fallback processing**: No API costs
- **Legacy batch API**: Discounted pricing (when it was available)

## 🆕 Migration Notes

### From Batch to Direct Processing

**Old Approach:**
```python
# Create batch files, submit jobs, poll status
batch_file_ids = processor.create_batch_file(pdf_pairs)
job_ids = [processor.submit_batch_job(file_id) for file_id in batch_file_ids]
await processor.process_with_polling(pdf_pairs, poll_interval=10)
```

**New Approach:**
```python
# Direct processing with real-time feedback
await processor.process_with_direct_api(pdf_pairs)
```

**Benefits of Migration:**
- Immediate results instead of waiting for batch processing
- Better error handling with automatic fallback
- Simplified codebase with fewer moving parts
- Higher quality structured markdown output