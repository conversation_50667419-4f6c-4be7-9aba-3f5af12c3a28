# Mass Tort Report Template Creation Prompt

## Overview
Create a responsive HTML template for a mass tort report system. The template should follow modern web design principles and be optimized for both web and email viewing. The design should be professional, clean, and emphasize data visualization and user interaction.

## Core Requirements

### Meta Tags and Document Setup
- Set up a proper HTML5 document structure
- Include comprehensive meta tags for:
  - Basic meta (charset, viewport)
  - Open Graph
  - Twitter Cards
  - Facebook-specific
  - Apple-specific (for iMessage)
- Set the language to English and include Open Graph prefix

### Design System

#### Color Palette
```css
--primary-color: #1a4b8c;
--secondary-color: #2c73d2;
--accent-color: #39BEFA;
--text-color: #2d3748;
--background-color: #f8fafc;
--border-color: #e2e8f0;
```

#### Typography
- Primary font stack: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif
- Enable font smoothing for better rendering
- Define hierarchical heading sizes:
  - h1: 2rem
  - h2: 1.5rem

#### Layout
- Container max-width: 1200px (60% on larger screens)
- Consistent spacing system with paddings and margins
- Responsive breakpoints focusing on mobile and desktop
- Sticky navigation

### Components

#### Navigation
1. Top Navigation Bar
- Fixed position
- Logo + Brand section
- Responsive menu with hamburger for mobile
- Smooth scroll navigation links
- Box shadow for depth

#### Header Section
- Gradient background
- Company logo
- Brand name and tagline
- Responsive sizing

#### Report Sections

##### Summary Section
- Date and key statistics
- Chart container for data visualization
- Optional Calendly integration
- Optional upcoming hearings section

##### Litigation Reports
Create three main report sections:
1. Litigation by Tort Report
2. Detailed Filings Report
3. AdSpy Report

Each section should include:
- Expandable rows
- Consistent table styling
- Click-to-expand functionality
- Smooth transitions
- Mobile-optimized views

#### Tables
Design consistent table styles with:
- Separated borders
- Rounded corners
- Hover states
- Expandable rows
- Custom cell styling for different data types
- Mobile-responsive layouts

### Interactive Features

#### Expansion System
Implement an expansion system for:
- Litigation sections
- Detail sections
- AdSpy rows
Each should include:
- Arrow indicators
- Smooth transitions
- State management
- Mobile touch support

#### Summary Toggles
Create toggleable summary sections with:
- Custom buttons
- Smooth transitions
- Clear state indicators

### JavaScript Functionality

#### Core Functions
1. Navigation
- Smooth scrolling
- Mobile menu handling
- Hash navigation
- Section expansion

2. Chart Integration
- Chart.js setup
- Responsive sizing
- Custom styling
- Data formatting

3. Interaction Handlers
- Click handlers
- Touch events
- Scroll management
- State management

#### Mobile Optimization
- Touch-friendly interactions
- Responsive font sizes
- Optimized layouts
- Performance considerations

### CSS Architecture

#### Base Styles
- Modern CSS reset
- Variable system
- Typography base
- Layout fundamentals

#### Component Styles
- Modular styling
- Consistent naming
- State management
- Transition systems

#### Utility Classes
- Shadow system
- Spacing utilities
- Color utilities
- Typography utilities

### Responsive Design
Implement a mobile-first approach with:
- Flexible grids
- Responsive typography
- Adaptive layouts
- Touch-optimized interactions

### Performance Optimization
- Efficient selectors
- Minimal dependencies
- Optimized assets
- Smooth animations

## Additional Features

### Footer
- Copyright information
- Contact details
- Unsubscribe link
- Responsive layout

### Accessibility
- ARIA labels
- Keyboard navigation
- Screen reader support
- Focus management

### Print Styling
- Optimized print layout
- Proper page breaks
- Readable typography
- Simplified colors

## Technical Requirements

### Dependencies
- Chart.js for data visualization
- jQuery for DOM manipulation
- Bootstrap bundle for utilities
- Custom plugins as needed

### Browser Support
- Modern browsers
- Graceful degradation
- Mobile browsers
- Email clients

### Development Guidelines
- Maintain semantic HTML
- Use BEM methodology
- Follow accessibility guidelines
- Implement progressive enhancement

## Documentation
Include comprehensive comments for:
- Component structure
- JavaScript functions
- CSS architecture
- Usage instructions

## Testing Requirements
- Cross-browser testing
- Mobile device testing
- Email client testing
- Accessibility testing

Remember to maintain a balance between functionality and performance, ensuring the template works efficiently across all platforms while maintaining a professional appearance.