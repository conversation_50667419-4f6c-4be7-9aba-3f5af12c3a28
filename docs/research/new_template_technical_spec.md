# Mass Tort Report Template Technical Specification

## Template Engine
The template uses Jinja2 templating engine with the following key features:
- Macro definitions for reusable components
- Conditional rendering based on web/email context
- Template inheritance patterns
- Custom filters

## Required Variables

### Core Variables
```python
{
    'is_web': bool,  # Determines if rendering for web or email
    'iso_date': str,  # ISO format date for URLs and tracking
    'report_date': str,  # Formatted date for display
    'summary_date': str,  # Formatted date for summary section
    'num_torts': int,  # Number of new tort claims
    'num_ads': int,  # Number of new legal ads
    's3_prod': str,  # Base URL for S3 assets
}
```

### Data Variables
```python
{
    'docket_df': DataFrame,  # Pandas DataFrame containing case information
    'grouped': Dict,  # Grouped litigation data
    'grouped_ads': List[Tuple],  # Grouped advertising data
    'chart_data': List[int],  # Data points for chart visualization
    'chart_labels': List[str],  # Labels for chart visualization
}
```

### Optional Feature Variables
```python
{
    'show_calendly': bool,  # Toggle Calendly section
    'calendly_link': str,  # Calendly booking URL
    'show_upcoming_hearings': bool,  # Toggle upcoming hearings section
    'upcoming_hearings': List[Dict],  # Hearing information
    'sponsorships': Dict[str, str],  # Sponsorship HTML content
}
```

## Macro Definitions

### Litigation Section Macro
```jinja
{% macro render_litigation_section(grouped, is_web, docket_df, s3_prod="", iso_date="") %}
```
Required Data Structure:
- grouped: Dictionary of grouped litigation data
- docket_df: DataFrame containing:
  - title: str
  - mdl_num: str
  - law_firm: str
  - Filings: int

### Litigation Table Macro
```jinja
{% macro render_litigation_table(title, allegations_and_causes, sub_df, is_web, s3_prod="", iso_date="") %}
```
Required Data Structure:
- title: str (litigation title)
- allegations_and_causes: str (formatted allegations text)
- sub_df: DataFrame containing:
  - law_firm: str
  - Filings: int

### Detailed Filings Macro
```jinja
{% macro render_detailed_filings(docket_df, is_web, s3_prod="", iso_date="") %}
```
Required DataFrame Structure:
- law_firm: str
- title: str
- versus: str
- transferred_in: bool
- pending_cto: bool
- s3_link: str

### Ad Report Macro
```jinja
{% macro render_ad_report(grouped_ads, is_web, s3_prod, iso_date) %}
```
Required Data Structure:
- grouped_ads: List of tuples containing:
  - name: str (law firm name)
  - group: DataFrame containing:
    - summary: str
    - ad_archive_id: str
    - start_date: str
    - end_date: str

## Custom Filters

### clean_id Filter
Purpose: Converts strings to URL/ID-safe format
```python
def clean_id(s: str) -> str:
    """
    Converts string to lowercase, replaces spaces with underscores,
    removes special characters
    """
```

### tojson Filter
Purpose: Converts Python objects to JSON format for JavaScript use
Used for: Chart data serialization

## Template Sections

### Header Section
Required Variables:
```python
{
    'is_web': bool,
    'iso_date': str
}
```

### Summary Section
Required Variables:
```python
{
    'summary_date': str,
    'num_torts': int,
    'num_ads': int,
    'chart_data': List[int],
    'chart_labels': List[str]
}
```

### Litigation Report Section
Required Variables:
```python
{
    'grouped': Dict,
    'is_web': bool,
    'docket_df': DataFrame,
    's3_prod': str,
    'iso_date': str
}
```

### Detailed Filings Section
Required Variables:
```python
{
    'docket_df': DataFrame,
    'is_web': bool
}
```

### AdSpy Report Section
Required Variables:
```python
{
    'grouped_ads': List[Tuple],
    'is_web': bool,
    's3_prod': str,
    'iso_date': str
}
```

## Conditional Rendering

### Web-specific Features
```jinja
{% if is_web %}
    <!-- Chart.js integration -->
    <!-- Interactive features -->
    <!-- Navigation scripts -->
{% endif %}
```

### Email-specific Features
```jinja
{% if not is_web %}
    <!-- Static content -->
    <!-- External links -->
    <!-- Simplified layout -->
{% endif %}
```

## Required Functions

### JavaScript Functions (Web Version)
1. Navigation Functions:
```javascript
function smoothScroll(elementId)
function handleHashNavigation()
function collapseAllSections(excludeId)
```

2. Expansion Functions:
```javascript
function expandLitigationSection(titleId)
function expandDetailSection(detailId)
function expandAdSpySection(groupId)
function toggleSummary(titleId)
```

3. Chart Functions:
```javascript
function createGradient(ctx, chartArea)
function getFontSize()
function isMobile()
```

## Data Processing Requirements

### Litigation Data Processing
The template expects:
1. Grouped litigation data by title
2. Calculated totals for each group
3. Processed allegations and causes
4. Clean versus text for display

### Ad Data Processing
The template expects:
1. Grouped ad data by law firm
2. Filtered valid summaries
3. Formatted dates
4. Valid ad archive IDs

### Chart Data Processing
The template expects:
1. Sorted data arrays
2. Matching labels array
3. Valid numerical values
4. Mobile-optimized data subset

## Error Handling

### Required Error Checks
1. Check for existence of required variables
2. Validate data structure integrity
3. Handle missing or null values
4. Provide fallback content

### Fallback Content
```jinja
{% if not grouped %}
    <p>No litigation data available for this report.</p>
{% endif %}
```

## Implementation Notes

1. Data Preparation:
   - All DataFrames should be cleaned and processed before template rendering
   - Dates should be formatted consistently
   - URLs should be validated and properly encoded

2. Performance Considerations:
   - Group and process data before template rendering
   - Minimize template logic complexity
   - Use appropriate data structures for quick access

3. Security Considerations:
   - Escape all user-generated content
   - Validate URLs and file paths
   - Sanitize input data

4. Maintenance Requirements:
   - Document all custom filters
   - Maintain consistent naming conventions
   - Comment complex logic sections
   - Keep macros modular and reusable