# Classification Analysis Tools Guide

This guide explains how to analyze your classified ads and see unique Title/Body/Summary/ImageText combinations with classification details.

## 1. Direct DynamoDB Analysis

Use `analyze_classified_ads.py` to analyze ads directly from DynamoDB:

```bash
# Basic analysis with interactive display
python src/scripts/analyze_classified_ads.py

# Analyze specific number of ads
python src/scripts/analyze_classified_ads.py --limit 1000

# Export to Excel with all details
python src/scripts/analyze_classified_ads.py \
    --output classified_ads_analysis.xlsx \
    --display-limit 20

# Export low-confidence ads for manual review
python src/scripts/analyze_classified_ads.py \
    --export-review manual_review.xlsx \
    --min-confidence 0.5

# Filter by specific classification
python src/scripts/analyze_classified_ads.py \
    --classification "AFFF Products Liability" \
    --output afff_ads.csv
```

## 2. Analyze Classification Results

If you saved classification results using `--save-results`, use `merge_classification_results.py`:

```bash
# Analyze saved classification results
python src/scripts/merge_classification_results.py \
    classification_results.csv \
    --output detailed_analysis.xlsx

# Merge with full ad data for complete picture
python src/scripts/merge_classification_results.py \
    classification_results.csv \
    --ads-data exported_ads.json \
    --output full_analysis.xlsx

# Show only high-confidence results
python src/scripts/merge_classification_results.py \
    classification_results.csv \
    --min-confidence 0.7 \
    --display-limit 10
```

## 3. Complete Workflow Example

```bash
# Step 1: Run classifier and save results
python src/scripts/hybrid_campaign_classifier_m4.py \
    --limit 1000 \
    --save-results classification_results.csv \
    --improve-rules

# Step 2: Analyze unique combinations
python src/scripts/merge_classification_results.py \
    classification_results.csv \
    --output unique_ads_analysis.xlsx \
    --display-limit 10

# Step 3: Export low-confidence for review
python src/scripts/analyze_classified_ads.py \
    --export-review needs_review.xlsx \
    --min-confidence 0.3
```

## Output Formats

### Excel Output (.xlsx)
Creates a multi-sheet workbook with:
- **Classified Ads**: All unique combinations with full details
- **Summary**: Statistics and category distribution

Columns include:
- `category`: Classification result
- `confidence`: Overall confidence score
- `method`: Classification method used (rule/embedding/hybrid)
- `rule_confidence`: Rule-based confidence
- `embedding_confidence`: Embedding-based confidence
- `duplicate_count`: Number of ads with identical content
- `title`, `body`, `summary`, `image_text`: Individual text fields
- `combined_content`: All text fields combined

### CSV Output (.csv)
Single file with all columns in tabular format.

### JSON Output (.json)
Structured JSON with all fields, useful for further processing.

## Interactive Display

The tools show rich console output with:

1. **Category Distribution Table**
   - Shows all categories found
   - Count and percentage for each

2. **Per-Category Examples**
   - Top examples from each category
   - Confidence scores and methods
   - Content preview

3. **Confidence Statistics**
   - Mean, median, min, max
   - Distribution by confidence ranges

## Filtering Options

- `--min-confidence`: Only show ads above confidence threshold
- `--classification`: Filter to specific classification
- `--limit`: Limit total ads processed
- `--display-limit`: Limit examples shown per category

## Understanding the Output

### Confidence Scores
- **0.7-1.0**: High confidence - likely correct
- **0.3-0.7**: Medium confidence - should review
- **0.0-0.3**: Low confidence - needs manual review

### Methods
- **rule**: Matched by rule-based classifier
- **embedding**: Matched by semantic similarity
- **hybrid**: Combined rule and embedding
- **none**: No good match found

### Duplicate Count
Shows how many ads have identical content. High duplicate counts indicate:
- Common ad templates
- Repeated campaigns
- Potential areas for rule improvement

## Tips for Analysis

1. **Start with low-confidence ads** - These need the most attention
2. **Look for patterns in "Other" category** - May indicate new litigation types
3. **Check high-duplicate ads** - Important to classify correctly as they appear frequently
4. **Export to Excel for team review** - Easy to share and annotate

## Example Analysis Session

```bash
# 1. Quick overview of current state
python src/scripts/analyze_classified_ads.py --limit 100

# 2. Deep dive into low-confidence ads
python src/scripts/analyze_classified_ads.py \
    --min-confidence 0.0 \
    --max-confidence 0.3 \
    --output low_confidence_analysis.xlsx

# 3. Review specific litigation type
python src/scripts/analyze_classified_ads.py \
    --classification "Depo-Provera Products Liability" \
    --output depo_provera_ads.xlsx

# 4. Export for manual review
python src/scripts/analyze_classified_ads.py \
    --export-review team_review.xlsx \
    --min-confidence 0.5
```

This will give you a complete picture of:
- Which ads are being classified correctly
- Which need better rules
- Which represent new litigation types
- How confident the system is in each classification