```mermaid
graph TD
    %% === Initialization & Entry ===
    A[Start Process Single Firm]
    A1{firm_data.ID and firm_data.Name exist?}
    A2[Update progress bar]
    A3[Log: Processing firm]
    A4[Initialize variables]
    
    A --> A1
    A1 -- No --> A1_SKIP[Log: Skip firm - missing ID/Name]
    A1 -- Yes --> A2
    A2 --> A3
    A3 --> A4

    %% === Fetch Ad Payloads ===
    B_FETCH[Fetch ad payloads]
    B1{Fetch successful?}
    B2[Count fetched ads]
    B3{Any ads found?}
    
    A4 --> B_FETCH
    B_FETCH --> B1
    B1 -- No --> B_FETCH_ERROR[Handle fetch error]
    B1 -- Yes --> B2
    B2 --> B3
    B3 -- No --> B_NO_ADS_FOUND[Set empty ads list]
    B3 -- Yes --> C_PROCESS[Process raw ads]
    
    %% === Process Ads ===
    C1{Processing successful?}
    C2{processed_ads_list is not a list?}
    C3[Filter valid ads]
    C4{Any invalid items removed?}
    C5{processed_ads_list is not empty?}
    C6[Clean ad data]
    
    C_PROCESS --> C1
    C1 -- Yes --> C_PROC_FAIL1[Error: AdProcessor returned None]
    C1 -- No --> C2
    C2 -- Yes --> C_PROC_FAIL2[Error: AdProcessor returned non-list]
    C2 -- No --> C3
    C3 --> C4
    C4 -- Yes --> C4_WARN[Log: Invalid items removed]
    C4_WARN --> C5
    C4 -- No --> C5
    C5 -- Yes --> C6
    C6 --> F_DB_PREP_START
    C5 -- No --> F_DB_PREP_START

    B_NO_ADS_FOUND --> F_DB_PREP_START
    C_PROC_FAIL1 --> F_DB_PREP_START
    C_PROC_FAIL2 --> F_DB_PREP_START

    %% === Exception Handling ===
    subgraph Exception_Handling
        EX_A[Handle Errors]
        EX_A1[Update error message]
        EX_A2{Session/Network issue?}
        EX_A3[Create new session]
        EX_A4{Recovery successful?}
        EX_A5[Log: Recovery OK]
        EX_A6[Log: Recovery Failed]
        
        EX_A --> EX_A1
        EX_A1 --> EX_A2
        EX_A2 -- Yes --> EX_A3
        EX_A3 --> EX_A4
        EX_A4 -- Yes --> EX_A5
        EX_A4 -- No --> EX_A6
    end
    
    B_FETCH_ERROR --> EX_A
    C_PROCESS -.-> EX_A
    
    EX_A5 --> F_DB_PREP_START_FROM_ERROR
    EX_A6 --> F_DB_PREP_START_FROM_ERROR
    EX_A2 -- No --> F_DB_PREP_START_FROM_ERROR

    %% Generic Exception Handling
    EX_B{Catch generic Exception}
    EX_B1[Update error message]
    
    EX_B --> EX_B1
    EX_B1 --> F_DB_PREP_START_FROM_ERROR
    
    F_DB_PREP_START_FROM_ERROR --> F_DB_PREP_START
    F_DB_PREP_START --> F1{success_flag is True AND processed_ads_list is not empty?}
    F1 -- No --> I_UPDATE_LAW_FIRM_DB[Skip DB operations]

    %% === Prepare for DB Batch Write (if successful so far) ===
    F1 -- Yes --> G_INIT_DB_WRITE_VARS[Initialize DB write variables]
    G_INIT_DB_WRITE_VARS --> G1_LOOP_PROC_ADS{Process each ad_dict}
    G1_LOOP_PROC_ADS -- Next --> G2[Convert to PascalCase]
    G2 --> G3{Has required fields?}
    G3 -- No --> G1_LOOP_PROC_ADS_END[Next ad]
    G3 -- Yes --> G4[Create key tuple and map]
    G4 --> G1_LOOP_PROC_ADS_END
    G1_LOOP_PROC_ADS -- Done --> G5{Any valid keys?}
    G5 -- No --> I_UPDATE_LAW_FIRM_DB
    G5 -- Yes --> G6[Fetch existing items]
    G6 --> G7[Create items map]
    G7 --> G8_LOOP_PROC_MAP{Process each ad}
    G8_LOOP_PROC_MAP -- Next --> G9[Initialize counters]
    G9 --> G10[Get existing ad]
    G10 --> G11[Sanitize record]
    G11 --> G12[Update last modified]
    G12 --> G13{Existing ad?}
    
    %% --- Existing Ad Logic ---
    G13 -- Yes --> G14[Handle EndDate]
    G14 --> G15_LOOP_COMPARE{Compare fields}
    G15_LOOP_COMPARE -- Next --> G16{Skip key?}
    G16 -- Yes --> G15_LOOP_COMPARE_END[Continue]
    G16 -- No --> G17{Value changed?}
    G17 -- Yes --> G18[Increment changed count]
    G18 --> G15_LOOP_COMPARE_END
    G17 -- No --> G15_LOOP_COMPARE_END
    
    G15_LOOP_COMPARE -- Done --> G19{Any changes?}
    G19 -- Yes --> G20[Update counters and add to batch]
    G19 -- No --> G21{Last updated changed?}
    G21 -- Yes --> G22[Update and add to batch]
    G21 -- No --> G8_LOOP_PROC_MAP_END[Continue to next ad in map, no changes]
    %% --- New Ad Logic ---
    G13 -- No --> G23[Add new ad to batch]
    
    G20 --> G8_LOOP_PROC_MAP_END
    G22 --> G8_LOOP_PROC_MAP_END
    G23 --> G8_LOOP_PROC_MAP_END
    G8_LOOP_PROC_MAP -- All ads in map processed --> H_DB_WRITE_CHECK
    
    %% === DB Get/Compare Exception Handling ===
    subgraph DB_Get_Compare_Exception_Handling
        G_INIT_DB_WRITE_VARS -- Can throw exceptions --> EX_C1
        EX_C{Catch Exception during Get/Compare}
        EX_C --> EX_C1[Update error message]
        EX_C1 --> I_UPDATE_LAW_FIRM_DB_FROM_ERROR
    end

    I_UPDATE_LAW_FIRM_DB_FROM_ERROR --> I_UPDATE_LAW_FIRM_DB
    H_DB_WRITE_CHECK --> H1{success_flag is True AND items_to_batch_write is not empty?}
    %% Skip DB write if previous error or no items
    H1 -- No --> I_UPDATE_LAW_FIRM_DB
    
    %% === Batch Write to DB ===
    H1 -- Yes --> H2_BATCH_WRITE[Batch insert items]
    H2_BATCH_WRITE --> H3{Any failures?}
    H3 -- Yes --> H_DB_WRITE_FAIL[Update error message]
    H_DB_WRITE_FAIL --> I_UPDATE_LAW_FIRM_DB
    H3 -- No --> I_UPDATE_LAW_FIRM_DB
    
    %% === DB Batch Write Exception Handling ===
    subgraph DB_Batch_Write_Exception_Handling
        H2_BATCH_WRITE -- Can throw exception --> EX_D1
        EX_D{Catch Exception during batch write}
        EX_D --> EX_D1[Update error message]
        EX_D1 --> I_UPDATE_LAW_FIRM_DB_FROM_ERROR2
    end

    I_UPDATE_LAW_FIRM_DB_FROM_ERROR2 --> I_UPDATE_LAW_FIRM_DB
    %% === Update LawFirm DB ===
    I_UPDATE_LAW_FIRM_DB --> I1{update_db is True?}
    I1 -- No --> K_FINAL_LOGGING
    I1 -- Yes --> I2{success_flag is True?}
    I2 -- No --> I_SKIP_LF_UPDATE[Log: Skip DB update]
    I_SKIP_LF_UPDATE --> K_FINAL_LOGGING
    I2 -- Yes --> I3[Prepare update data]
    I3 --> I4[Create key dict]
    I4 --> I5[Update law firm DB]
    I5 --> I6{Update failed?}
    I6 -- Yes --> I_LF_DB_UPDATE_FAIL[Update error message]
    I_LF_DB_UPDATE_FAIL --> K_FINAL_LOGGING
    I6 -- No --> K_FINAL_LOGGING

    %% === LawFirm DB Update Exception Handling ===
    subgraph LawFirm_DB_Update_Exception_Handling
        I5 -- Can throw exception --> EX_E1
        EX_E{Catch Exception during update}
        EX_E --> EX_E1[Update error message]
        EX_E1 --> K_FINAL_LOGGING_FROM_ERROR
    end
    K_FINAL_LOGGING_FROM_ERROR --> K_FINAL_LOGGING

    %% === Final Logging ===
    K_FINAL_LOGGING --> K1{success_flag is False?}
    K1 -- Yes --> K_LOG_FAILURE[Log: Processing failed]
    K1 -- No --> K_LOG_SUCCESS[Log: Processing succeeded]
    
    K_LOG_FAILURE --> Z_END[_process_single_firm End & Return]
    K_LOG_SUCCESS --> Z_END
```