```mermaid
graph TD
    A["Start main2.py"] --> B["Set MP Start Method spawn"];
    B --> C["Apply Patches"];
    C --> D["Setup Cleanup Handlers"];
    D --> E{"Run main async"};

    subgraph main
        E --> F["Load Config for Date"];
        F --> G["Setup Rich Logging"];
        G --> H["Instantiate MainProcessor"];
        H --> I{"Call processor run"};
    end

    subgraph "MainProcessor.run()"
        I --> J["Log Start run"];
        J --> K{"Params reset chrome?"};
        K -- "No" --> L;
        L{"Params scraper OR docket num?"};
        L -- "No" --> M;
        M{"Params upload pending OR post process OR upload?"};
        M -- "Yes" --> N{"Call handle post processing"};
        N --> O;
        O{"Params fb ads?"};
        O -- "No" --> P;
        P{"Params report generator?"};
        P -- "No" --> Q["Log Completion run"];
        Q --> R["Return from run"];
    end

    subgraph "MainProcessor._handle_post_processing()"
        N --> N1{"Params upload pending OR post process?"};
        N1 -- "Yes" --> N2{"Call run post processing"};
        N2 --> N3;
        N3{"Params upload?"};
        N3 -- "No" --> N4["Return"];
    end

    subgraph "MainProcessor.run_post_processing"
        N2 --> PP1["Log Start Post Processing"];
        PP1 --> PP2["Update config reprocess md"];
        PP2 --> PP3["Instantiate PostProcessorV2"];
        PP3 --> PP4["Log Post Processing Config"];
        PP4 --> PP5{"Call processor start"};
        PP5 --> PP6["Log Docket Summary"];
        PP6 --> N3
        %% Return to _handle_post_processing flow
    end

    subgraph "PostProcessorV2.start()"
        PP5 --> S1["Log Start Async Workflow"];
        S1 --> S2["Configure Workflow State"];
        S2 --> S3["Initialize aiohttp Session"];
        S3 --> S4["Set session in LLM Client"];

        S4 --> S5{"Phase 1 Process ZIPs?"};
        S5 -- "Yes" --> S6["Log Phase 1 Start"];
        S6 --> S7["Run process all zip files"];
        subgraph "process_all_zip_files()"
            S7 --> S7a["Find zip files"];
            S7a --> S7b["Loop zips"];
            S7b --> S7c["Extract JSON PDF"];
            S7c --> S7d["Load JSON"];
            S7d --> S7e["Add metadata"];
            S7e --> S7f["Generate initial filename"];
            S7f --> S7g["Store initial name"];
            S7g --> S7h["Save JSON orig name"];
            S7h --> S7i{"Rename PDF initial name"};
            S7i --> S7j["Delete ZIP"];
            S7j --> S7k["Delete extra files"];
            S7k --> S7l["End Zip Loop"];
            S7l --> S7m["Return JSON paths orig names"];
        end
        S7m --> S8["Store initial JSON paths"];

        S8 --> S9["Determine files Phase 3"];
        S9 --> S10{"reprocess files arg?"};
        S10 -- "No" --> S11;
        S11{"post process?"};
        S11 -- "Yes" --> S12["Set input None"];
        S12 --> S13["Set run processing True"];

        S13 --> S14{"Phase 3 Process Files?"};
        S14 -- "Yes" --> S15["Log Phase 3 Start"];
        S15 --> S16{"Call get filtered json files async"};
            subgraph "get_filtered_json_files_async()"
                 S16 --> S16a["Get target dir"];
                 S16a --> S16b["List json"];
                 S16b --> S16c["Filter skip list"];
                 S16c --> S16d["Filter added on"];
                 S16d --> S16e["Return filtered paths"];
            end
        S16e --> S17{"Files found?"};
        S17 -- "Yes" --> S18{"Call process federal filings async"};
            subgraph "process_federal_filings_async()"
                S18 --> S18a["Create Semaphore"];
                S18a --> S18b["Create tasks process file"];
                S18b --> S18c["Run tasks concurrently"];
                S18c --> S18d["Collect results"];
                S18d --> S18e["Return success list"];
            end
        S17 -- "No" --> S18e;
        S18e --> S19["Store docket summary"];
        S19 --> S20["Extract final paths"];

        S20 --> S21{"Phase 3.5 Upload?"};
        S21 -- "No" --> S22["Log Upload Skipped"];

        S22 --> S23["Log Workflow Completion"];
        S23 --> S24["Call reporter generate summary"];
        S24 --> S25["Call log docket summary"];
        S25 --> S26["Close Session"];
        S26 --> S27["Cleanup Files"];
        S27 --> PP6
        %% Return results to run_post_processing
    end

    subgraph "_process_single_file_async()"
        PSF1["Log Start file"] --> PSF2["Load JSON"];
        PSF2 --> PSF3{"Load OK?"};
        PSF3 -- "No" --> PSF_End["Return None"];
        PSF3 -- "Yes" --> PSF4["Deep Copy Data"];
        PSF4 --> PSF5["Get intermediate filename"];
        PSF5 --> PSF6{"Check complete or error or force"};
        PSF6 -- "Skip" --> PSF_End;
        PSF6 -- "Process" --> PSF7["Clear errors"];
        PSF7 --> PSF8["Process S3 HTML"];
        PSF8 --> PSF9{"Get PDF Text"};
            subgraph "_get_pdf_text()"
                PSF9 --> GPDF1["Determine PDF MD path"];
                GPDF1 --> GPDF2["Try load md"];
                GPDF2 --> GPDF3{"Text Found?"};
                GPDF3 -- "No" --> GPDF4["Try load JSON"];
                GPDF4 --> GPDF5{"Text Found?"}
                %% Corrected link - MOVED COMMENT
                GPDF5 -- "No" --> GPDF6["Check S3 CDN Local"];
                GPDF6 --> GPDF7{"Source?"};
                GPDF7 -- "No" --> GPDF11["Return None"];
                GPDF7 -- "Yes" --> GPDF8["Download HTTP"];
                GPDF8 --> GPDF9["Use MistralOCR"];
                GPDF9 --> GPDF10["Save text to md"];
                GPDF10 --> GPDF11["Return Text or None"];
            end
        GPDF11 --> PSF10{"Text Obtained?"};
        PSF10 -- "No" --> PSF11["Skip LLM"];
        PSF10 -- "Yes" --> PSF12["LLM Extraction"];
        PSF11 & PSF12 --> PSF13["Generate Final Filename"];
        PSF13 --> PSF14{"Filename OK?"};
        PSF14 -- "No" --> PSF_SaveErrorStatus["Save error to original"]-->PSF_End;
        PSF14 -- "Yes" --> PSF15{"Filename Changed?"};
        PSF15 -- "Yes" --> PSF16["Update data new filename"];
        PSF15 & PSF16 --> PSF17["Apply Other Steps"];
        PSF17 --> PSF18{"Other Steps OK?"};
        PSF18 -- "No" --> PSF_SaveErrorStatus;
        PSF18 -- "Yes" --> PSF19["Validate Keys"];
        PSF19 --> PSF20{"Keys OK?"};
        PSF20 -- "No" --> PSF_SaveErrorStatus;
        PSF20 -- "Yes" --> PSF21["Determine Final Path"];
        PSF21 --> PSF22{"Filename Changed?"};
            PSF22 -- "Yes" --> PSF23["Rename PDF"];
            PSF23 --> PSF24["Rename MD"];
            PSF24 --> PSF25["Set Save Path final"];
            PSF25 --> PSF27;
            PSF22 -- "No" --> PSF26["Set Save Path original"];
            PSF26 --> PSF27;
        PSF27 --> PSF28["Save data async"];
        PSF28 --> PSF29{"Save OK?"};
            PSF29 -- "No" --> PSF_SaveErrorStatus;
            PSF29 -- "Yes" --> PSF30{"Filename Changed?"};
            PSF30 -- "Yes" --> PSF31["Delete original file"];
            PSF30 & PSF31 --> PSF32["Log Success"];
            PSF32 --> PSF_EndSuccess["Return Success Dict"];

        PSF_SaveErrorStatus --> PSF_End;
    end

    R --> Z["End async run"];
    Z --> Z1["Cleanup Resources"];
    Z1 --> Z_End["Exit"];

```