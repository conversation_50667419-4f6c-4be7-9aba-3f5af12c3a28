# Weekly Reports

This document explains the weekly report generation feature.

## Overview

The weekly report feature allows you to generate reports that span a 7-day period instead of a single day. This provides a more comprehensive view of filing activities and ad campaigns over a weekly timeframe.

## Configuration

To generate a weekly report, set the `weekly: True` flag in your configuration. The system will:

1. Take the specified `date` as the end date
2. Automatically calculate the start date (7 days prior)
3. Aggregate all docket filings and ads from the entire 7-day period
4. Generate a comprehensive weekly report using the weekly report template

### Example Configuration

```yaml
# Weekly Report Configuration
date: '05/18/25'  # MM/DD/YY format - this will be the end date
report_generator: True
weekly: True      # This flag enables weekly report generation
skip_ads: False   # Set to True to skip ad processing
skip_invalidate: False  # Set to True to skip CloudFront cache invalidation
```

## Running Weekly Reports

You can run weekly reports in several ways:

### 1. Using the CLI

```bash
python -m src.main2 --params config/weekly_report.yml
```

### 2. Via Code

```python
# Set up parameters
params = {
    'date': '05/18/25',
    'report_generator': True,
    'weekly': True,
    # Other parameters as needed
}

# Create the processor
processor = MainProcessor(params)
await processor.run()
```

## Implementation Details

Weekly report generation:

1. Uses the weekly report template from `src/assets/templates/weekly_report_template.html`
2. Aggregates data from all 7 days of the specified period
3. Calculates metrics across the entire week
4. Uses the title "Weekly Mass Tort Report" in the output

## Output

The weekly report will be generated in both web and email formats, similar to daily reports, but will display:

1. Filing statistics aggregated across the entire week
2. Ad data for the entire week
3. Charts showing the week's most active MDLs
4. Detailed breakdowns of all filings from the full 7-day period

## Important Notes

- Weekly reports generally take longer to generate due to the larger data processing requirements
- They should typically be run during off-peak hours since they query more data
- The content of weekly reports is tailored to provide a broader overview than daily reports
- The system validates that `weekly: True` and `report_generator: True` parameters are both set