# Phase 3.1: Transformer.py Breakdown Plan

## Current State
- transformer.py: 2220 lines
- Contains DataTransformer class with 30+ methods
- Multiple functional areas mixed together

## Proposed Breakdown

### 1. Core Orchestrator (transformer.py)
- Keep main DataTransformer class
- Focus on high-level orchestration and workflow management
- Methods to keep:
  - `__init__`, `async_init`, `__aenter__`, `__aexit__`
  - `start()`, `start_sync()`
  - `process_federal_filings_async()`
  - Repository management methods

### 2. File Operations Manager (file_operations.py)
- Handle all file-related operations
- Methods to move:
  - `_get_files_to_process()`
  - `_ensure_pdf_available()`
  - `_ensure_md_available()`
  - `_finalize_and_rename_files()`
  - `_perform_cleanup()`

### 3. Data Processing Engine (data_processing_engine.py)
- Core data processing and enrichment logic
- Methods to move:
  - `_process_single_file_async()`
  - `_load_and_validate_initial_data()`
  - `_enrich_data()`
  - `_clean_duplicate_fields()`

### 4. Specialized Workflows (specialized_workflows.py)
- Handle specific processing modes
- Methods to move:
  - `_run_law_firm_normalization_only()`
  - `_run_mdl_title_update_only_async()`

### 5. Component Factory (component_factory.py)
- Initialize and manage components
- Methods to move:
  - `_initialize_llm_client()`
  - `_initialize_pdf_processor()`
  - `_load_mdl_data()`

### 6. Error Handling (error_handler.py)
- Centralize error handling logic
- Methods to move:
  - `_update_error_status_and_save()`
  - `_update_error_status()`
  - `_clear_processing_errors()`

## Benefits
1. **Maintainability**: Smaller, focused modules easier to understand
2. **Testability**: Individual components can be tested in isolation
3. **Reusability**: Components can be reused across different workflows
4. **Performance**: Easier to optimize specific functional areas
5. **Async-first**: Maintain the established async-first architecture

## Implementation Strategy
1. Create new modules with async-first design
2. Move methods while preserving public API
3. Update imports and dependencies
4. Create comprehensive tests for each module
5. Ensure no regressions in existing functionality