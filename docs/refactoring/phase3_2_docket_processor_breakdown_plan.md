# Phase 3.2: DocketProcessor.py Breakdown Plan

## Current State
- docket_processor.py: 1043 lines
- Single DocketProcessor class with 8+ major methods
- Mixed responsibilities: data processing, LLM interaction, HTML parsing, file handling

## Proposed Breakdown

### 1. Core Docket Processor (docket_processor.py)
- Keep main DocketProcessor class as orchestrator
- Focus on high-level processing coordination
- Methods to keep:
  - `__init__`
  - Main processing coordination methods

### 2. Data Cleaner & Formatter (docket_data_cleaner.py)
- Handle data cleaning, formatting, and validation
- Methods to move:
  - `process_filing_date()`
  - `clean_and_flatten()`
  - `_clear_processing_errors()`
  - Data format validation and normalization

### 3. LLM Extraction Engine (docket_llm_engine.py)
- Handle all LLM-related processing
- Methods to move:
  - `run_llm_extraction()`
  - LLM response processing and validation
  - PDF text extraction and preparation

### 4. Text & PDF Handler (docket_text_handler.py)
- Handle PDF text extraction and processing
- Methods to move:
  - `_get_pdf_text()`
  - PDF download and processing logic
  - Text extraction utilities

### 5. HTML Processor (docket_html_processor.py)
- Handle HTML parsing and data extraction
- Methods to move:
  - `process_s3_html()`
  - HTML parsing and data extraction logic
  - Plaintiff/defendant extraction from HTML

### 6. Court Data Processor (court_data_processor.py)
- Handle court-specific data processing
- New functionality for:
  - `process_nature_of_suit()`
  - `process_case_flags()`
  - `process_court_info()`
  - Court-specific data validation

### 7. Law Firm Integration (law_firm_integration.py)
- Handle law firm processing integration
- New functionality for:
  - `process_law_firms()` (async)
  - Law firm data validation and normalization

## Benefits
1. **Single Responsibility**: Each module handles one specific aspect
2. **Testability**: Components can be unit tested independently
3. **Maintainability**: Smaller, focused modules easier to modify
4. **Async-First**: All components designed with async-first patterns
5. **Reusability**: Components can be reused in other processors

## Implementation Strategy
1. Create new modules following async-first design
2. Extract methods while preserving interfaces
3. Update DocketProcessor to use new components
4. Create comprehensive tests for each module
5. Ensure no regressions in processing pipeline

## Async-First Design Principles
- All I/O operations use async patterns
- Proper error handling with async-safe logging
- Resource management with async context managers
- Concurrent processing where appropriate
- No sync wrappers or compatibility layers