# Cache Keys Documentation

## Overview
This document describes the exact cache key generation and structure for the LexGenius classification system, derived from the hybrid_classifier.bak.py implementation.

## Cache Types and Key Generation

### NER Cache Keys
NER (Named Entity Recognition) cache stores entity extraction results with composite keys.

#### Key Generation Process
```python
# 1. Extract and combine text using field processing
YAML_FIELDS = ["Title", "Body", "Summary", "LinkDescription", "PageName"]
text_processing_fields = [field.lower() for field in YAML_FIELDS]
# Result: ["title", "body", "summary", "linkdescription", "pagename"]

# 2. Process each field
raw_parts = []
ad_data_lower_keys = {k.lower(): v for k, v in ad_data.items()}

for field_name_config in text_processing_fields:
    value = ad_data_lower_keys.get(field_name_config)
    
    if value is None:
        continue
    
    value_str = str(value).strip()
    
    if not value_str:
        continue
    
    # Special validation for summary field
    if field_name_config == 'summary':
        normalized_summary_content = value_str.lower()
        if normalized_summary_content in invalid_summary_strings:
            continue
    
    raw_parts.append(value_str)

# 3. Combine text with pipe separator
raw_combined_text = " | ".join(raw_parts)

# 4. Generate hash (truncate to 10000 chars first)
max_len_for_hash = 10000
text_for_hash_key_gen = raw_combined_text[:max_len_for_hash]
text_hash = hashlib.md5(text_for_hash_key_gen.encode('utf-8')).hexdigest()

# 5. Create composite key
ad_id = ad.get('AdArchiveID', ad.get('ad_id', 'unknown'))
ner_cache_key = f"ner_{ad_id}_{text_hash}"
```

#### NER Cache Structure
- **Key**: `f"ner_{ad_id}_{text_hash}"` (string)
- **Value**: Dictionary containing extracted entities by type
  ```python
  {
    "ORG": ["Company Name 1", "Company Name 2"],
    "PERSON": ["John Doe", "Jane Smith"],
    "MONEY": ["$1000", "$5000"],
    # ... other entity types
  }
  ```

### Embedding Cache Keys
Embedding cache stores vector representations of text with hash keys.

#### Key Generation Process
```python
# Uses the same text processing as NER cache
raw_combined_text = " | ".join(raw_parts)

# Generate hash (same as NER)
max_len_for_hash = 10000
text_for_hash_key_gen = raw_combined_text[:max_len_for_hash]
embedding_cache_key = hashlib.md5(text_for_hash_key_gen.encode('utf-8')).hexdigest()
```

#### Embedding Cache Structure
- **Key**: `text_hash` (32-character MD5 hex string)
- **Value**: NumPy array or list representing the embedding vector
  ```python
  # Example: 768-dimensional vector for roberta-large
  [0.1234, -0.5678, 0.9012, ..., 0.3456]
  ```

## Text Processing Details

### Field Processing
1. **Source Fields**: `["Title", "Body", "Summary", "LinkDescription", "PageName"]`
2. **Lowercase Conversion**: Field names converted to lowercase for DynamoDB lookup
3. **Case Preservation**: Field values keep original case in final text

### Invalid Summary Handling
```python
invalid_summary_strings = {s.lower() for s in 
    ['NA', 'SKIPPED', '', 'None', 'null', "Summary generation failed"]}
```
- Summary fields containing these values (case-insensitive) are excluded
- Only applies to the `summary` field, not other fields

### Text Combination
- Fields joined with ` | ` separator (pipe with spaces)
- Example: `"Title Text | Body Text | Summary Text | Link Description | Page Name"`

### Hash Generation
- Text truncated to 10,000 characters before hashing
- Uses MD5 algorithm with UTF-8 encoding
- Results in 32-character hexadecimal string

## Cache Usage Patterns

### NER Cache Lookup
```python
# Primary lookup: composite key
ner_key = f"ner_{ad_id}_{text_hash}"
if ner_key in ner_cache:
    entities = ner_cache[ner_key]

# Fallback: raw text key (legacy support)
elif raw_combined_text in ner_cache:
    entities = ner_cache[raw_combined_text]
```

### Embedding Cache Lookup
```python
# Hash-based lookup
embedding_key = text_hash
if embedding_key in embedding_cache:
    vector = embedding_cache[embedding_key]
```

## Cache Coverage Statistics

Based on analysis of 102,951 FBAdArchive records:

### NER Cache
- **Total entries**: 102,673
- **Coverage**: 99.94%
- **Key format**: Composite (`ner_{ad_id}_{text_hash}`)

### Embedding Cache
- **Total entries**: 21,706  
- **Coverage**: 99.97%
- **Key format**: Hash (`text_hash`)

## Implementation Notes

### Key Consistency
- Both NER and embedding caches use identical text processing logic
- Same `text_hash` used for both cache types ensures consistency
- Ad ID only included in NER keys, not embedding keys

### Performance Considerations
- Hash truncation at 10,000 characters prevents memory issues with very long texts
- MD5 provides fast hashing with low collision probability for this use case
- Composite keys in NER cache enable ad-specific lookups

### Cache Compatibility
- Current implementation maintains backward compatibility with raw text keys
- New entries use composite keys for better organization
- Both key formats checked during lookup for maximum cache utilization

## Example Cache Entries

### NER Cache Entry
```python
key = "ner_614697441055080_54a5560afbfb501ab5db7d765907c962"
value = {
    "ORG": ["Roundup"],
    "MONEY": [],
    "PERSON": [],
    "PRODUCT": ["Roundup"]
}
```

### Embedding Cache Entry
```python
key = "54a5560afbfb501ab5db7d765907c962"
value = [0.1234, -0.5678, 0.9012, ..., 0.3456]  # 768-dimensional vector
```