# LexGenius Refactoring Plan

## Overview
This document outlines a step-by-step plan to refactor the LexGenius codebase to add comprehensive testing, migrate to async patterns, consolidate configuration management, and improve overall architecture while avoiding circular dependencies and unnecessary abstraction.

**Timeline Note**: With heavy AI assistance (Claude Code), this can be completed in **4-6 weeks** of focused effort, or **6-8 weeks** if done part-time while maintaining the system.

## Current State Analysis

### Architecture Overview
- **Entry Point**: `src/main.py` orchestrates workflows via YAML configs
- **Core Components**:
  - `fb_ads/` - Facebook ad processing
  - `pacer/` - Court docket monitoring
  - `reports/` - Report generation
  - `data_transformer/` - Data processing
  - **CRITICAL**: `src/lib/` contains 48 files with 28K+ lines of tightly coupled code
  - Various `*_manager.py` files handling data persistence
- **Storage**: Duplicate sync/async manager implementations across `src/lib/` and `src/storage/`
- **Dependencies**: Heavy reliance on synchronous DynamoDB and S3 operations
- **Configuration**: Multiple YAML files with inconsistent structure, various JSON configs scattered throughout

### Key Issues
1. **Massive codebase in src/lib/**: 6 files exceed 1,400+ lines (largest: 2,145 lines)
2. **Sync/Async duplication**: Nearly identical functionality in sync/async versions
3. **No test coverage**: Critical business logic untested
4. **Tight coupling**: AI integrator imports 10+ services, circular dependencies
5. **Mixed concerns**: Storage, business logic, and external services intermingled
6. **Configuration chaos**: Multiple formats and locations with no validation
7. **God objects**: Single files doing too many responsibilities

### Phase 0 & 1 Status (COMPLETED)
- ✅ **Phase 0**: Configuration consolidation ~80% complete (Pydantic models implemented)
- ✅ **Phase 1**: Testing foundation 100% complete (comprehensive test infrastructure)

## Refactoring Principles

1. **Dependency Direction**: All dependencies flow inward: Services → Infrastructure → Domain
2. **No Circular Dependencies**: Strict layering with clear boundaries
3. **Interface Segregation**: Small, focused interfaces over large, general ones
4. **Progressive Migration**: Maintain backward compatibility during transition
5. **Test-First Approach**: Add tests before refactoring to ensure behavior preservation
6. **Continuous Development**: Support ongoing bug fixes and features during refactoring
7. **Type Safety**: Use Pydantic for runtime validation and better IDE support

## Phase 0: Configuration Consolidation (Week 1)

### 0.1 Pydantic Configuration Models
Create a centralized configuration system using Pydantic for validation and type safety.

```
src/config_models/
├── __init__.py
├── base.py              # Base configuration classes
├── scraper.py           # PACER scraping configuration
├── fb_ads.py            # Facebook ads configuration
├── reports.py           # Report generation configuration
├── storage.py           # AWS/Database configuration
└── validators.py        # Custom validators
```

### 0.2 Configuration Structure
```python
# src/config_models/base.py
from pydantic import BaseSettings, Field, validator
from typing import Optional, List
from datetime import datetime

class BaseConfig(BaseSettings):
    """Base configuration with common fields"""
    date: datetime = Field(..., description="Target date for processing")
    environment: str = Field("production", regex="^(development|staging|production)$")
    log_level: str = Field("INFO", regex="^(DEBUG|INFO|WARNING|ERROR)$")
    dry_run: bool = Field(False, description="Run without making changes")
    
    class Config:
        env_file = ".env"
        env_prefix = "LEXGENIUS_"

class WorkflowConfig(BaseConfig):
    """Configuration for workflow execution"""
    name: str
    enabled: bool = True
    parallel: bool = False
    num_workers: int = Field(4, ge=1, le=16)
    timeout_seconds: int = Field(3600, ge=60)
    retry_attempts: int = Field(3, ge=0, le=10)
```

### 0.3 Configuration File Strategy

**Keep as YAML** (human-edited, workflow definitions):
- `config/workflows/*.yml` - Workflow orchestration configs
- `config/environments/*.yml` - Environment-specific settings

**Convert to JSON** (machine-readable, rarely changed):
- Court definitions, MDL lookups, attorney mappings
- Campaign classifications, company name mappings
- Any configuration that's essentially static data

**Move to Pydantic models** (type-safe, validated):
- All runtime configuration
- Feature flags
- API credentials and connection strings

### 0.4 Migration Process
1. Create Pydantic models for all configuration types
2. Build configuration loader that supports both old and new formats
3. Add deprecation warnings for old config files
4. Gradually migrate each component to use new configs
5. Remove old configuration code once migration complete

## Phase 1: Testing Foundation (Week 2 with AI assistance)

### 1.1 Test Infrastructure Setup
```
tests/
├── __init__.py
├── conftest.py                 # Shared pytest fixtures
├── unit/                       # Unit tests
│   ├── __init__.py
│   ├── lib/
│   │   ├── test_utils.py
│   │   └── test_json_safety.py
│   └── mocks/                  # Mock implementations
│       ├── __init__.py
│       ├── mock_dynamodb.py
│       └── mock_s3.py
├── integration/                # Integration tests
│   ├── __init__.py
│   └── test_data_flow.py
└── e2e/                       # End-to-end tests
    ├── __init__.py
    └── test_pipeline.py
```

### 1.2 Testing Strategy
1. **Start with leaf nodes**: Test utilities and pure functions first
   - `src/lib/utils/` - date, common, law_firm utilities
   - `src/lib/json_safety.py`
   - `src/lib/pdf_extractor.py`

2. **Mock external dependencies**:
   - Create mock implementations for DynamoDB, S3, and external APIs
   - Use dependency injection to swap implementations

3. **Test data fixtures**:
   - Create representative test data for each domain
   - Store in `tests/fixtures/` directory

### 1.3 Initial Test Implementation Order
1. Utility functions (pure, no dependencies)
2. Data models and validators
3. Business logic with mocked I/O
4. Integration tests for complete workflows

## Phase 1.5: src/lib/ Component Breakdown & Elimination (Weeks 3-5)

### CRITICAL: Complete Elimination of src/lib/

The `src/lib/` directory contains 48 files with 28K+ lines that must be completely eliminated and reorganized. Every file will be **relocated, refactored, or removed**.

## Phase 1.6: Complete Async Migration & Compatibility Layer Elimination (Weeks 3-4)

### CRITICAL: Eliminate Migration Compatibility Layer Entirely

**Current State Analysis**: Based on comprehensive codebase analysis, the project has **48 files** using problematic compatibility wrappers from `src/migration.py` (503 lines) and `src/migration/manager_to_service.py` (859 lines). These compatibility layers are causing:

- **Performance overhead**: Creating new event loops for each method call
- **Code complexity**: Dual implementation systems with inconsistent approaches
- **Anti-patterns**: Problematic sync/async bridging with `asyncio.run_until_complete()`
- **Resource inefficiency**: Multiple storage instances and improper context manager usage
- **🚨 ASYNC/SYNC SIGNATURE MISMATCHES**: Coroutine objects returned instead of actual values

### **Phase 1.6.1: Direct Repository Migration (Week 3)**

#### **Priority 1: Convert Main Orchestrators to Pure Async (Days 1-3)**

**Implementation Order:**
1. **FB Ads Orchestrator** (highest impact, most complex compatibility issues)
2. **Reports Orchestrator** (user-facing, reliability critical)  
3. **PACER Orchestrator** (core functionality)
4. **Data Transformer** (supporting pipeline)

#### **FB Ads Orchestrator Conversion**

**Current Problematic Pattern:**
```python
# Anti-pattern: Using compatibility wrappers
self.law_firm_db = create_manager_replacement('LawFirmsManager', config)
self.fb_ad_db = create_manager_replacement('FBArchiveManager', config)

# These create event loops for each call (PERFORMANCE KILLER)
all_firms = self.law_firm_db.get_all_records(projection_expression=projection)
items = self.fb_ad_db.batch_get_items(keys_to_fetch)
```

**Target Clean Pattern:**
```python
# Clean approach: Direct repository instantiation
from src.infrastructure.storage.dynamodb_async import AsyncDynamoDBStorage
from src.repositories.law_firms_repository import LawFirmsRepository
from src.repositories.fb_archive_repository import FBArchiveRepository

class FBAdOrchestrator:
    def __init__(self, config: FBAdConfig):
        self.config = config
        self._storage = None
        self._law_firms_repo = None
        self._fb_repo = None
    
    async def __aenter__(self):
        self._storage = AsyncDynamoDBStorage(self.config.storage)
        await self._storage.__aenter__()
        self._law_firms_repo = LawFirmsRepository(self._storage)
        self._fb_repo = FBArchiveRepository(self._storage)
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self._storage:
            await self._storage.__aexit__(exc_type, exc_val, exc_tb)
    
    async def process_ads(self):
        # Proper async patterns with connection pooling
        all_firms = await self._law_firms_repo.scan_all(
            projection_expression=projection
        )
        items = await self._fb_repo.get_by_keys(keys_to_fetch)
        
        # Process in batches with proper async concurrency
        async with asyncio.TaskGroup() as tg:
            tasks = [tg.create_task(self._process_single_ad(ad)) for ad in items]
        
        return [task.result() for task in tasks]
```

**Method Mapping (Complete Reference):**
- `law_firm_db.get_all_records()` → `await law_firms_repository.scan_all()`
- `law_firm_db.table.query()` → `await law_firms_repository.query()`
- `law_firm_db.update_item()` → `await law_firms_repository.update_item()`
- `fb_ad_db.batch_get_items()` → `await fb_archive_repository.get_by_keys()`
- `fb_ad_db.batch_insert_items()` → `await fb_archive_repository.batch_put_items()`
- `fb_ad_db.query_ad_archive_by_date()` → `await fb_archive_repository.query_by_start_date_range()`

#### **Reports Orchestrator Conversion**

**Files Requiring Async Updates:**
- `src/reports/ad_df_processor.py` → Accept repositories in constructor, make methods async
- `src/reports/data_loader.py` → Convert to async, use direct repository calls
- `src/reports/processor.py` → Update to use PacerRepository directly
- `src/reports/orchestrator.py` → Full async conversion with context managers

**Target Pattern:**
```python
class ReportOrchestrator:
    async def generate_report(self):
        async with AsyncDynamoDBStorage(self.config) as storage:
            fb_repo = FBArchiveRepository(storage)
            pacer_repo = PacerRepository(storage)
            law_firms_repo = LawFirmsRepository(storage)
            
            # Concurrent data loading
            fb_data, pacer_data, firms_data = await asyncio.gather(
                fb_repo.query_by_date_range(self.date_range),
                pacer_repo.get_recent_filings(self.date_range),
                law_firms_repo.scan_all()
            )
            
            # Process and generate report
            return await self._generate_html_report(fb_data, pacer_data, firms_data)
```

### **Phase 1.6.2: Eliminate Compatibility Layer (Week 3-4)**

#### **Day 4-5: Remove Compatibility Files**

**Actions:**
1. **Mark as deprecated**: Add deprecation warnings to `create_manager_replacement()`
2. **Update all imports**: Replace compatibility imports with direct repository imports across 48 files
3. **Remove dual migration systems**: Eliminate both `src/migration.py` and `src/migration/manager_to_service.py`

**Import Updates (AI-Assisted Mass Update):**
```python
# Remove these imports across entire codebase:
from src.migration import create_manager_replacement
from src.migration.manager_to_service import LawFirmsManagerCompat, FBArchiveManagerCompat

# Replace with direct imports:
from src.repositories.fb_archive_repository import FBArchiveRepository
from src.repositories.law_firms_repository import LawFirmsRepository  
from src.repositories.pacer_repository import PacerRepository
from src.infrastructure.storage.dynamodb_async import AsyncDynamoDBStorage
```

#### **Configuration Simplification**

**Remove Sync/Async Configuration Switches:**
```python
# Old configuration with sync/async options
class StorageConfig:
    use_async: bool = False  # REMOVE THIS
    sync_manager_type: str = "legacy"  # REMOVE THIS
    
# New async-only configuration
class StorageConfig:
    dynamodb_endpoint: Optional[str] = None
    aws_region: str = "us-west-2"
    connection_pool_size: int = 10
    timeout_seconds: int = 30
```

### **Phase 1.6.3: Entry Point Conversion (Week 4)**

#### **Update src/main.py to Full Async**

**Target Architecture:**
```python
async def main():
    config = load_config(args.params)
    
    async with ServiceFactory(config) as factory:
        # Concurrent workflow execution
        tasks = []
        
        if config.workflow.fb_ads.enabled:
            fb_orchestrator = await factory.create_fb_ads_orchestrator()
            tasks.append(fb_orchestrator.run())
        
        if config.workflow.reports.enabled:
            report_orchestrator = await factory.create_reports_orchestrator()
            tasks.append(report_orchestrator.generate())
        
        if config.workflow.pacer.enabled:
            pacer_orchestrator = await factory.create_pacer_orchestrator()
            tasks.append(pacer_orchestrator.scrape())
        
        # Run all workflows concurrently
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Handle results and exceptions
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"Workflow {i} failed: {result}")
            else:
                logger.info(f"Workflow {i} completed successfully")

if __name__ == "__main__":
    asyncio.run(main())
```

#### **Factory Pattern Implementation**

**Create src/factories/service_factory.py:**
```python
class ServiceFactory:
    def __init__(self, config: BaseConfig):
        self.config = config
        self._storage = None
        self._repositories = {}
    
    async def __aenter__(self):
        # Single storage instance with connection pooling
        self._storage = AsyncDynamoDBStorage(self.config.storage)
        await self._storage.__aenter__()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self._storage:
            await self._storage.__aexit__(exc_type, exc_val, exc_tb)
    
    async def get_repository(self, repo_type: str):
        if repo_type not in self._repositories:
            if repo_type == 'fb_archive':
                self._repositories[repo_type] = FBArchiveRepository(self._storage)
            elif repo_type == 'law_firms':
                self._repositories[repo_type] = LawFirmsRepository(self._storage)
            elif repo_type == 'pacer':
                self._repositories[repo_type] = PacerRepository(self._storage)
        return self._repositories[repo_type]
    
    async def create_fb_ads_orchestrator(self):
        return FBAdOrchestrator(
            fb_repository=await self.get_repository('fb_archive'),
            law_firms_repository=await self.get_repository('law_firms'),
            config=self.config.fb_ads
        )
```

### **Expected Performance Improvements**

#### **Quantified Benefits:**
- **30-50% reduction** in response time (eliminating sync/async bridging overhead)
- **60-80% reduction** in memory usage (no duplicate event loops)
- **90%+ reduction** in complexity (single async pattern vs dual systems)
- **Zero event loop creation** overhead (proper connection pooling)

#### **Before vs After Comparison:**

**Before (Compatibility Layer):**
```python
# Creates new event loop for EVERY database call
loop = asyncio.new_event_loop()
asyncio.set_event_loop(loop)
try:
    return loop.run_until_complete(async_method())  # PERFORMANCE KILLER
finally:
    loop.close()
```

**After (Direct Async):**
```python
# Proper async context management with connection pooling
async with AsyncDynamoDBStorage(config) as storage:
    repo = FBArchiveRepository(storage)  # Reuses connection
    return await repo.query_data()  # Native async performance
```

### **Implementation Timeline (Accelerated)**

#### **Week 3: Core Orchestrator Conversion**
- **Day 1**: FB Ads Orchestrator → Pure async
- **Day 2**: Reports Orchestrator → Pure async  
- **Day 3**: PACER Orchestrator → Pure async
- **Day 4**: Data Transformer → Pure async
- **Day 5**: Testing and validation

#### **Week 4: Complete Migration Layer Elimination**
- **Day 1-2**: Mass import updates (AI-assisted)
- **Day 3**: Remove compatibility files entirely
- **Day 4**: Configuration simplification
- **Day 5**: Entry point conversion and factory implementation

#### **Expected Results After Week 4:**
- ✅ **Zero compatibility layer code** remaining
- ✅ **100% async architecture** throughout
- ✅ **Single source of truth** for data access
- ✅ **Measurable performance improvements** (30-50% faster)
- ✅ **Simplified maintenance** (no dual systems)

### **Risk Mitigation Strategy**

#### **Feature Flag Approach:**
```python
# Gradual rollout with feature flags
class FeatureFlags:
    use_direct_async_repos: bool = True  # Default to new system
    fallback_to_compatibility: bool = False  # Safety net (temporary)

# During transition period only
if config.feature_flags.fallback_to_compatibility:
    # Old compatibility layer (deprecated)
    manager = create_manager_replacement('FBArchiveManager', config)
else:
    # New direct async (default)
    async with AsyncDynamoDBStorage(config) as storage:
        repo = FBArchiveRepository(storage)
```

#### **Comprehensive Testing:**
1. **Performance benchmarks** before/after each orchestrator conversion
2. **Integration tests** to verify behavior preservation
3. **Load testing** to validate async performance improvements
4. **Memory profiling** to confirm resource efficiency gains

This complete elimination of the compatibility layer will result in a dramatically cleaner, faster, and more maintainable codebase while establishing the foundation for the remaining refactoring phases.

### 1.5.1 Monster File Breakdown (Week 3)

#### **Oversized Files Requiring Immediate Breakdown:**

**1. `pacer_manager.py` (2,145 lines) → 5 Components**
```
└── repositories/pacer_repository.py (300 lines) - Data access only
└── services/pacer/query_service.py (400 lines) - Business queries  
└── services/pacer/analytics_service.py (300 lines) - Statistics
└── services/pacer/export_service.py (200 lines) - CSV generation
└── utils/pacer_utils.py (200 lines) - Utilities
```

**2. `deepseek_interface.py` (1,962 lines) → 4 Components**
```
└── infrastructure/external/deepseek_client.py (600 lines) - API client
└── services/ai/deepseek_service.py (400 lines) - Business logic
└── services/ai/batch_processor.py (400 lines) - Batch processing
└── utils/resource_monitor.py (200 lines) - Monitoring
```

**3. `fb_archive_manager.py` (1,952 lines) → 6 Components**
```
└── repositories/fb_ads_repository.py (400 lines) - Data access
└── services/fb_ads/image_service.py (400 lines) - Image processing
└── services/fb_ads/deduplication_service.py (300 lines) - Deduplication
└── services/fb_ads/campaign_service.py (400 lines) - Campaign analysis
└── services/fb_ads/categorizer_service.py (300 lines) - Categorization
└── utils/fb_ads_utils.py (150 lines) - Utilities
```

**4. `dynamodb_base_manager.py` (1,844 lines) → 2 Components**
```
└── infrastructure/storage/dynamodb_async.py (800 lines) - Async only
└── infrastructure/storage/base_repository.py (200 lines) - Common patterns
```

### 1.5.2 Manager Consolidation & Async Migration (Week 4)

#### **Sync → Async Only Migration**

**REMOVE Sync Versions (After Migration):**
- `attorneys_manager.py` → `repositories/attorneys_repository.py`
- `district_courts_manager.py` → `repositories/courts_repository.py`
- `law_firms_manager.py` → `repositories/law_firms_repository.py`
- `pacer_dockets_manager.py` → Split into 3 repository classes
- `s3_manager.py` → `infrastructure/storage/s3_async.py`

**ENHANCE Async Versions from src/storage/:**
- `storage/async_base_manager.py` → `infrastructure/storage/dynamodb_async.py`
- `storage/fb_archive_manager_async.py` → Integrate with service layer
- `storage/pacer_manager_async.py` → Repository pattern
- `storage/s3_manager_async.py` → Enhanced async implementation

### 1.5.3 AI Services Consolidation (Week 5)

#### **Break Apart ai_integrator.py God Object**
**Problem**: 807 lines importing 10+ services
**Solution**:
```
└── services/ai/ai_orchestrator.py (300 lines) - Coordination only
└── services/ai/text_generation_service.py (200 lines) - Text AI
└── services/ai/image_analysis_service.py (200 lines) - Vision AI
└── factories/ai_factory.py (100 lines) - Dependency injection
```

#### **Unify AI Clients:**
```
infrastructure/external/
├── openai_client.py (400 lines) - From gpt4_interface.py
├── deepseek_client.py (600 lines) - Unified client
├── mistral_client.py (300 lines) - From mistral_ocr.py
├── llava_client.py (200 lines) - From llava_vision.py
└── ollama_client.py (150 lines) - From ollama_client.py
```

### 1.5.4 Utility & Configuration Cleanup

#### **Utility Consolidation:**
```
utils/
├── file_utils.py (200 lines) - From utils.py
├── json_safety.py (80 lines) - Keep as-is
├── pdf_utils.py (300 lines) - From pdf_extractor.py
├── cleanup_utils.py (400 lines) - Consolidated cleanup
├── async_helpers.py (200 lines) - Async utilities
└── logging_utils.py (100 lines) - From scraper_logging.py
```

#### **Files to REMOVE:**
- `config.py`, `config_adapter.py` (replaced by Pydantic)
- `invalidate_cloudfront.py` (duplicate)
- `court_mdl_data/` → Move to `scripts/court_scrapers/`

## Phase 2: Interface Definition & Protocol Creation (Week 6)

### 2.1 Create Protocol Definitions

**Note**: Protocols created AFTER component breakdown ensures clean, focused interfaces

```
src/protocols/
├── __init__.py
├── storage.py          # Storage interfaces
├── processing.py       # Data processing interfaces
├── external.py         # External service interfaces
└── messaging.py        # Message/event interfaces
```

### 2.2 Clean Protocols Based on Actual Components

After breaking down large files, we can create focused protocols:

```python
# src/protocols/storage.py
from typing import Protocol, Dict, List, Optional, Any
from abc import abstractmethod

class IRepository(Protocol):
    """Base repository interface for data access"""
    
    @abstractmethod
    async def get_by_id(self, id: str) -> Optional[Dict[str, Any]]:
        """Retrieve a single item by ID"""
        ...
    
    @abstractmethod
    async def query(self, **filters) -> List[Dict[str, Any]]:
        """Query items based on filters"""
        ...
    
    @abstractmethod
    async def save(self, item: Dict[str, Any]) -> None:
        """Save a single item"""
        ...
    
    @abstractmethod
    async def save_batch(self, items: List[Dict[str, Any]]) -> None:
        """Save multiple items"""
        ...

class ILLMService(Protocol):
    """AI service interface based on actual usage patterns"""
    
    @abstractmethod
    async def process_text(self, text: str, prompt: str) -> str:
        """Process text with AI model"""
        ...
    
    @abstractmethod
    async def process_batch(self, items: List[str]) -> List[str]:
        """Process multiple items efficiently"""
        ...
```

### 2.3 Dependency Injection Setup
1. Create factory functions for manager instantiation
2. Use protocols instead of concrete types in function signatures
3. Configure dependencies at application startup

## Phase 3: Service Layer & Repository Pattern (Week 7-8)

### 3.1 Repository Pattern Implementation

After component breakdown, implement clean repository pattern:

```python
# src/repositories/base.py
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, TypeVar, Generic
from src.protocols.storage import IRepository

T = TypeVar('T')

class BaseRepository(Generic[T], ABC):
    """Base repository with common CRUD operations"""
    
    def __init__(self, storage: IStorage):
        self.storage = storage
    
    @abstractmethod
    def _to_entity(self, data: Dict) -> T:
        """Convert storage format to domain entity"""
        pass
    
    @abstractmethod
    def _to_storage(self, entity: T) -> Dict:
        """Convert domain entity to storage format"""
        pass
    
    async def get_by_id(self, id: str) -> Optional[T]:
        data = await self.storage.get_by_id(id)
        return self._to_entity(data) if data else None

# src/repositories/pacer_repository.py
from src.domain.litigation import Case
from .base import BaseRepository

class PacerRepository(BaseRepository[Case]):
    def _to_entity(self, data: Dict) -> Case:
        return Case.from_dict(data)
    
    def _to_storage(self, case: Case) -> Dict:
        return case.to_dict()
    
    async def find_by_court_and_date(
        self, court_id: str, date_range: DateRange
    ) -> List[Case]:
        data = await self.storage.query(
            court_id=court_id,
            filed_date__gte=date_range.start,
            filed_date__lte=date_range.end
        )
        return [self._to_entity(item) for item in data]
```

### 3.2 Service Layer Structure

Extract business logic to focused service classes:

```
src/services/
├── ai/
│   ├── ai_orchestrator.py       # Replaces ai_integrator.py
│   ├── text_generation_service.py
│   ├── image_analysis_service.py
│   └── batch_processor.py
├── pacer/
│   ├── query_service.py         # From pacer_manager.py
│   ├── analytics_service.py     # Statistics & reporting
│   └── export_service.py        # CSV generation
├── fb_ads/
│   ├── image_service.py         # Image processing
│   ├── campaign_service.py      # Campaign analysis
│   ├── categorizer_service.py   # Ad categorization
│   └── deduplication_service.py # Deduplication logic
└── document/
    └── pdf_service.py           # From pdf_extractor.py
```

## Phase 4: Infrastructure Layer & External Services (Week 9-10)

### 4.1 Infrastructure Layer Organization

Consolidate all external service implementations:

```
src/infrastructure/
├── storage/
│   ├── dynamodb_async.py      # From dynamodb_base_manager.py
│   ├── s3_async.py            # Enhanced from s3_manager.py
│   ├── base_repository.py     # Common patterns
│   └── local_dynamodb.py      # From docker_dynamodb_manager.py
├── external/
│   ├── openai_client.py       # From gpt4_interface.py
│   ├── deepseek_client.py     # Unified from deepseek_interface.py
│   ├── mistral_client.py      # From mistral_ocr.py
│   ├── llava_client.py        # From llava_vision.py
│   └── ollama_client.py       # From ollama_client.py
└── messaging/
    ├── cloudfront.py          # From cloudfront_invalidator.py
    └── email_service.py       # Email operations
```

### 4.2 Clean Async Implementation

**Key Principles:**
- **Async-only**: No sync/async duplication
- **Connection pooling**: Efficient resource usage
- **Retry logic**: Built-in resilience
- **Protocol-based**: Easy testing and swapping

```python
# src/infrastructure/storage/dynamodb_async.py
from aioboto3 import Session
from src.protocols.storage import IStorage

class AsyncDynamoDBStorage(IStorage):
    def __init__(self, config: StorageConfig):
        self.config = config
        self._session = None
        self._client = None
    
    async def __aenter__(self):
        self._session = Session()
        self._client = await self._session.client(
            'dynamodb',
            region_name=self.config.aws_region,
            endpoint_url=self.config.dynamodb_endpoint
        ).__aenter__()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self._client:
            await self._client.__aexit__(exc_type, exc_val, exc_tb)
    
    async def get_by_id(self, table: str, key: Dict) -> Optional[Dict]:
        response = await self._client.get_item(
            TableName=table,
            Key=key
        )
        return response.get('Item')
```

### 4.3 External Service Clients

Unified, clean API clients:

```python
# src/infrastructure/external/deepseek_client.py
from aiohttp import ClientSession, ClientTimeout
from src.protocols.external import ILLMClient

class DeepSeekClient(ILLMClient):
    def __init__(self, config: ExternalConfig):
        self.config = config
        self._session = None
    
    async def __aenter__(self):
        timeout = ClientTimeout(total=self.config.timeout_seconds)
        self._session = ClientSession(
            headers={'Authorization': f'Bearer {self.config.deepseek_api_key}'},
            timeout=timeout
        )
        return self
    
    async def generate_text(self, prompt: str, **kwargs) -> str:
        async with self._session.post(
            f'{self.config.deepseek_base_url}/chat/completions',
            json={
                'model': kwargs.get('model', 'deepseek-chat'),
                'messages': [{'role': 'user', 'content': prompt}],
                'max_tokens': kwargs.get('max_tokens', 4000)
            }
        ) as response:
            data = await response.json()
            return data['choices'][0]['message']['content']
```

## Phase 5: Integration & Factory Pattern (Week 11-12)

### 5.1 Factory Pattern for Dependency Injection

Replace the problematic `ai_integrator.py` god object with clean factories:

```python
# src/factories/service_factory.py
from src.config_models.loader import load_config
from src.protocols.storage import IStorage
from src.protocols.external import ILLMService
from src.infrastructure.storage.dynamodb_async import AsyncDynamoDBStorage
from src.infrastructure.external.deepseek_client import DeepSeekClient
from src.services.ai.ai_orchestrator import AIOrchestrator

class ServiceFactory:
    def __init__(self, config_path: str):
        self.config = load_config(config_path)
        self._storage = None
        self._ai_clients = {}
    
    async def get_storage(self) -> IStorage:
        if not self._storage:
            self._storage = AsyncDynamoDBStorage(self.config.storage)
            await self._storage.__aenter__()
        return self._storage
    
    async def get_ai_service(self, provider: str = 'deepseek') -> ILLMService:
        if provider not in self._ai_clients:
            if provider == 'deepseek':
                client = DeepSeekClient(self.config.external)
                await client.__aenter__()
                self._ai_clients[provider] = client
        return self._ai_clients[provider]
    
    async def create_ai_orchestrator(self) -> AIOrchestrator:
        return AIOrchestrator(
            deepseek=await self.get_ai_service('deepseek'),
            openai=await self.get_ai_service('openai'),
            config=self.config.ai
        )
```

### 5.2 Domain Models

Create clean domain entities separate from storage schemas:

```python
# src/domain/litigation.py
from dataclasses import dataclass
from datetime import datetime
from typing import List, Optional

@dataclass
class Case:
    id: str
    court_id: str
    docket_number: str
    title: str
    filed_date: datetime
    case_type: str
    attorneys: List[str]
    status: str
    
    @classmethod
    def from_dict(cls, data: dict) -> 'Case':
        return cls(
            id=data['id'],
            court_id=data['court_id'],
            docket_number=data['docket_number'],
            title=data['case_title'],
            filed_date=datetime.fromisoformat(data['filed_date']),
            case_type=data['case_type'],
            attorneys=data.get('attorneys', []),
            status=data.get('status', 'active')
        )
    
    def to_dict(self) -> dict:
        return {
            'id': self.id,
            'court_id': self.court_id,
            'docket_number': self.docket_number,
            'case_title': self.title,
            'filed_date': self.filed_date.isoformat(),
            'case_type': self.case_type,
            'attorneys': self.attorneys,
            'status': self.status
        }
```

### 5.3 Clean Service Integration

```python
# src/services/ai/ai_orchestrator.py
from src.protocols.external import ILLMService
from src.config_models.ai import AIConfig

class AIOrchestrator:
    """Replaces the 807-line ai_integrator.py god object"""
    
    def __init__(self, deepseek: ILLMService, openai: ILLMService, config: AIConfig):
        self.deepseek = deepseek
        self.openai = openai
        self.config = config
    
    async def process_text(self, text: str, task_type: str) -> str:
        """Route to appropriate AI service based on task type"""
        if task_type in self.config.deepseek_tasks:
            return await self.deepseek.generate_text(text)
        else:
            return await self.openai.generate_text(text)
    
    async def process_batch(self, items: List[str], task_type: str) -> List[str]:
        """Efficient batch processing"""
        service = self.deepseek if task_type in self.config.deepseek_tasks else self.openai
        return await service.process_batch(items)
```

## Phase 6: Testing & Documentation (Week 13-14)

### 6.1 Comprehensive Testing of New Architecture

With clean components, testing becomes straightforward:

```python
# tests/unit/services/test_ai_orchestrator.py
import pytest
from unittest.mock import AsyncMock
from src.services.ai.ai_orchestrator import AIOrchestrator

@pytest.fixture
def mock_deepseek():
    mock = AsyncMock()
    mock.generate_text.return_value = "Generated text"
    return mock

@pytest.fixture
def mock_openai():
    mock = AsyncMock()
    mock.generate_text.return_value = "GPT response"
    return mock

@pytest.mark.asyncio
async def test_ai_orchestrator_routes_correctly(mock_deepseek, mock_openai, ai_config):
    orchestrator = AIOrchestrator(mock_deepseek, mock_openai, ai_config)
    
    # Test DeepSeek routing
    result = await orchestrator.process_text("test", "classification")
    mock_deepseek.generate_text.assert_called_once_with("test")
    assert result == "Generated text"
    
    # Test OpenAI routing
    result = await orchestrator.process_text("test", "creative_writing")
    mock_openai.generate_text.assert_called_once_with("test")
    assert result == "GPT response"
```

### 6.2 Performance Optimization Built-In

**Connection Pooling**: Async context managers ensure efficient resource usage
**Batch Operations**: Services designed for batch processing from the start
**Concurrent Processing**: Natural async concurrency patterns

### 6.3 Documentation Updates

1. **Architecture Guide**: New layered architecture explanation
2. **Migration Guide**: How to use new services instead of old managers
3. **API Documentation**: All service interfaces documented
4. **Performance Benchmarks**: Before/after metrics

## Final Results: src/lib/ Complete Elimination

### Success Metrics

**Before Refactoring:**
- ❌ 48 files in `src/lib/` (28K+ lines)
- ❌ 6 files exceeding 1,400+ lines (largest: 2,145 lines)
- ❌ Sync/async duplication throughout
- ❌ God objects with 10+ dependencies
- ❌ No test coverage
- ❌ Tight coupling everywhere

**After Refactoring:**
- ✅ `src/lib/` directory completely eliminated
- ✅ No file exceeds 500 lines
- ✅ 100% async architecture
- ✅ Clean protocol-based interfaces
- ✅ Comprehensive test coverage
- ✅ Loose coupling via dependency injection

### Architecture Transformation

**From**: Monolithic `src/lib/` with tightly coupled managers
**To**: Clean layered architecture:

```
src/
├── domain/              # Business entities
├── protocols/           # Interface definitions  
├── services/            # Business logic
├── repositories/        # Data access
├── infrastructure/      # External services
├── factories/          # Dependency injection
└── utils/              # Pure utilities
```

## Complete File Migration Map from src/lib/

### Monster Files → Multiple Components

**pacer_manager.py (2,145 lines)**:
```
└── repositories/pacer_repository.py (300 lines)
└── services/pacer/query_service.py (400 lines)
└── services/pacer/analytics_service.py (300 lines)
└── services/pacer/export_service.py (200 lines)
└── utils/pacer_utils.py (200 lines)
```

**deepseek_interface.py (1,962 lines)**:
```
└── infrastructure/external/deepseek_client.py (600 lines)
└── services/ai/deepseek_service.py (400 lines)
└── services/ai/batch_processor.py (400 lines)
└── utils/resource_monitor.py (200 lines)
```

**fb_archive_manager.py (1,952 lines)**:
```
└── repositories/fb_ads_repository.py (400 lines)
└── services/fb_ads/image_service.py (400 lines)
└── services/fb_ads/deduplication_service.py (300 lines)
└── services/fb_ads/campaign_service.py (400 lines)
└── services/fb_ads/categorizer_service.py (300 lines)
```

### Manager Consolidation

**Sync Managers → Async Repositories**:
- `attorneys_manager.py` → `repositories/attorneys_repository.py`
- `district_courts_manager.py` → `repositories/courts_repository.py`
- `law_firms_manager.py` → `repositories/law_firms_repository.py`
- `s3_manager.py` → `infrastructure/storage/s3_async.py`

**AI Services → Clean Clients**:
- `ai_integrator.py` → `services/ai/ai_orchestrator.py`
- `gpt4_interface.py` → `infrastructure/external/openai_client.py`
- `llava_vision.py` → `infrastructure/external/llava_client.py`
- `mistral_ocr.py` → `infrastructure/external/mistral_client.py`

**Utilities → Organized Utils**:
- `utils.py` → `utils/file_utils.py`
- `pdf_extractor.py` → `utils/pdf_utils.py`
- `cleanup_utils.py` + `resource_cleanup_utils.py` → `utils/cleanup_utils.py`

**Configuration → Eliminated**:
- `config.py` → **REMOVED** (replaced by Pydantic)
- `config_adapter.py` → **REMOVED** (replaced by Pydantic)

**External Services → Infrastructure**:
- `cloudfront_invalidator.py` → `infrastructure/messaging/cloudfront.py`
- `docker_dynamodb_manager.py` → `infrastructure/storage/local_dynamodb.py`
- `scraper_logging.py` → `utils/logging_utils.py`

**Court Data → Scripts**:
- `court_mdl_data/` → `scripts/court_scrapers/` (archive)

### Files Completely Removed

- `invalidate_cloudfront.py` (duplicate)
- All backup and temporary files
- Sync versions of managers after async migration

## Continuing Development During Refactoring

### Parallel Development Strategy
1. **Feature Branch Strategy**:
   - Main branch continues with bug fixes and urgent features
   - Refactoring branch for architectural changes
   - Regular merges from main to refactoring branch

2. **Incremental Releases**:
   - Ship refactored components as they're completed
   - Use feature flags to toggle between old and new implementations
   - Monitor performance and rollback if needed

3. **Bug Fix Protocol**:
   - Critical bugs: Fix in both old and new code
   - Non-critical bugs: Fix only in new code if component is being refactored soon
   - Document all fixes in refactoring notes

### AI-Assisted Development Workflow
1. **Code Generation**: Use Claude for boilerplate and test generation
2. **Review and Refine**: AI reviews code for patterns and suggests improvements
3. **Documentation**: Generate comprehensive docs as you code
4. **Test Creation**: AI creates test cases based on existing code behavior

## Dependency Injection Strategy

### Why NOT use container.di (or similar DI frameworks)
1. **Unnecessary Complexity**: Python's duck typing and first-class functions provide sufficient flexibility
2. **Explicit Dependencies**: Constructor injection makes dependencies clear and testable
3. **No Magic**: Easier to understand and debug without framework magic
4. **Performance**: No runtime overhead from DI container

### Recommended Approach: Simple Factory Pattern
```python
# src/factories.py
class ServiceFactory:
    """Simple factory for creating services with dependencies"""
    
    def __init__(self, config: BaseConfig):
        self.config = config
        self._storage = None
        self._cache = None
    
    @property
    def storage(self) -> StorageProtocol:
        if not self._storage:
            if self.config.use_async:
                self._storage = AsyncDynamoDBManager(self.config)
            else:
                self._storage = DynamoDBManager(self.config)
        return self._storage
    
    def create_pacer_service(self) -> PacerService:
        return PacerService(
            storage=self.storage,
            config=self.config.pacer
        )
```

## **UPDATED: AI-Accelerated Async-First Timeline (8 Weeks Total)**

### ✅ **Weeks 1-2: COMPLETED**
- **Phase 0**: Configuration consolidation (80% complete)
- **Phase 1**: Testing foundation (100% complete)

### **🚨 PRIORITY: Week 3-4: Complete Async Migration & Compatibility Layer Elimination**
**CRITICAL**: Address the async migration and compatibility layer issues immediately as they are causing the most pain.

#### **Week 3: Core Orchestrator Conversion**
- **Day 1**: FB Ads Orchestrator → Pure async (highest impact)
- **Day 2**: Reports Orchestrator → Pure async (user-facing critical)  
- **Day 3**: PACER Orchestrator → Pure async (core functionality)
- **Day 4**: Data Transformer → Pure async (supporting pipeline)
- **Day 5**: Testing and validation of async conversions

#### **Week 4: Complete Migration Layer Elimination**
- **Day 1-2**: Mass import updates across 48 files (AI-assisted)
- **Day 3**: Remove compatibility files entirely (`src/migration.py`, `src/migration/manager_to_service.py`)
- **Day 4**: Configuration simplification (remove sync/async switches)
- **Day 5**: Entry point conversion and factory implementation

**Expected Results After Week 4:**
- ✅ **Zero compatibility layer code** remaining
- ✅ **100% async architecture** throughout
- ✅ **30-50% performance improvement**
- ✅ **Dramatically simplified codebase**

### **Week 5: Monster File Breakdown**
Now that async architecture is clean, break down large files:

**Day 1-2**: Break down `pacer_manager.py` (2,145 lines)
- Extract repository, services, and utilities
- Add tests for each component
- AI generates boilerplate and tests

**Day 3-4**: Break down `deepseek_interface.py` (1,962 lines)  
- Separate API client from business logic
- Create batch processing service
- Test all components

**Day 5**: Break down `fb_archive_manager.py` (1,952 lines)
- Extract image, campaign, and categorization services
- Create repository for data access

### **Week 6: Service Layer & Protocol Creation**
**Week**: Extract business logic to services
- Create domain models
- Implement clean service layer with protocols
- Build focused service interfaces
- Update all imports (AI-assisted)

### **Week 7: Infrastructure & External Services**
**Week**: Organize infrastructure layer
- Unified external service clients (OpenAI, DeepSeek, etc.)
- Enhanced async implementations
- Factory pattern for dependency injection

### **Week 8: Final Integration & Production Deployment**
**Week**: Complete system integration and deployment
- Comprehensive testing of new architecture
- Performance benchmarking and optimization
- Documentation updates
- **Complete elimination of src/lib/**
- Feature flags for gradual rollout
- Monitoring and rollback plans

## **Why Async-First Approach?**

The original timeline had async migration in Week 4, but the analysis shows the compatibility layer is causing:
- **Performance bottlenecks** (event loop creation for every DB call)
- **Complexity overhead** (dual migration systems)
- **Active bugs** (async/sync signature mismatches)
- **Development friction** (confusing architecture)

**By prioritizing async migration first:**
1. **Immediate performance gains** (30-50% improvement in Week 4)
2. **Simplified development** (single async pattern)
3. **Cleaner foundation** for remaining refactoring
4. **Reduced technical debt** early in the process
5. **Better team morale** (no more fighting compatibility layers)

## Why So Much Faster with AI?

1. **Code Generation**: AI writes boilerplate instantly
   - Pydantic models from existing configs: 1 hour vs 1 day
   - Test generation: 2 days vs 2 weeks
   - Async adapters: 1 day vs 1 week

2. **Refactoring**: AI handles mechanical changes
   - Import updates across entire codebase: minutes vs hours
   - Method signature changes: instant vs manual search/replace
   - Pattern application: consistent and fast

3. **Testing**: AI creates comprehensive test suites
   - Generates test cases from existing code
   - Creates mocks and fixtures automatically
   - Identifies edge cases you might miss

4. **Documentation**: Generated as you go
   - Docstrings for all functions
   - README updates
   - Architecture diagrams from code structure

## Success Metrics

1. **Test Coverage**: Achieve 80%+ code coverage
2. **Performance**: 50%+ reduction in average response time
3. **Reliability**: 99.9% uptime for critical paths
4. **Maintainability**: Reduced coupling, measured by dependency analysis
5. **Developer Experience**: Faster onboarding and feature development

## Risk Mitigation

1. **Backward Compatibility**: Maintain sync versions until async is proven
2. **Gradual Rollout**: Use feature flags for controlled deployment
3. **Monitoring**: Implement comprehensive logging before changes
4. **Rollback Plan**: Document how to revert each phase
5. **Testing**: Extensive testing at each phase before proceeding

## Next Steps

1. **Get stakeholder buy-in** on the plan and timeline
2. **Set up CI/CD pipeline** with test automation
3. **Create tracking dashboard** for progress monitoring
4. **Establish code review process** for refactored code
5. **Schedule regular check-ins** to adjust plan as needed

## Quick Start: Immediate Impact (First 3 Days)

With AI assistance, you can see significant progress immediately:

### Day 1 (4-6 hours with AI):
1. **Morning (2 hours)**:
   - Create all Pydantic config models
   - AI reads your existing configs and generates type-safe models
   - Add validation rules

2. **Afternoon (2-4 hours)**:
   - Set up pytest infrastructure
   - Create initial test structure
   - Generate tests for 10+ utility functions

### Day 2 (6 hours with AI):
1. **Morning (3 hours)**:
   - Test all date/time utilities
   - Test JSON safety functions
   - Test law firm normalizers
   - 50+ tests created by AI

2. **Afternoon (3 hours)**:
   - Create async S3 wrapper
   - Test async S3 operations
   - Benchmark performance improvement

### Day 3 (6 hours with AI):
1. **Full Day**:
   - Migrate one complete workflow to async
   - See 30-50% performance improvement
   - Deploy behind feature flag

**Result after 3 days**: You'll have:
- ✅ Type-safe configuration
- ✅ 100+ tests running in CI
- ✅ One async workflow in production
- ✅ Measurable performance improvement
- ✅ Confidence to continue

## Quick Start: What to Do First (Week 1)

### Day 1: Configuration Models
```python
# src/config_models/scraper.py
from pydantic import BaseModel, Field
from typing import List, Optional
from datetime import datetime

class ScraperConfig(BaseModel):
    """PACER scraping configuration"""
    date: datetime
    scraper: bool = True
    post_process: bool = True
    upload: bool = True
    headless: bool = True
    run_parallel: bool = False
    process_single_court: Optional[List[str]] = None
    num_workers: int = Field(4, ge=1, le=16)
    
    class Config:
        extra = "forbid"  # Fail on unknown fields
```

### Day 2-3: First Tests
Start with the simplest, most isolated code:
1. `src/lib/utils/date.py` - Pure functions, no dependencies
2. `src/lib/json_safety.py` - Critical functionality, easy to test
3. `src/lib/utils/law_firm_normalizer.py` - Business logic, no I/O

### Day 4-5: Configuration Migration
1. Create compatibility layer for existing YAML files
2. Add Pydantic validation to existing config loading
3. Gradually migrate each workflow to new config structure

## Configuration File Decisions

### JSON Files (Keep as JSON)
These are essentially data, not configuration:
- `src/config/courts/district_courts.json` - Court definitions
- `src/config/mdl/*.json` - MDL lookups and mappings
- `src/config/attorneys/attorney_lookup.json` - Attorney data
- `src/config/fb_ad_categorizer/company_name_mapping.json` - Static mappings
- `src/config/law_firms/*.json` - Law firm normalization data

### YAML Files (Consolidate and Validate)
Workflow and environment configs:
```yaml
# config/workflows/daily_pipeline.yml
name: daily_pipeline
steps:
  - name: scrape_pacer
    config:
      scraper: true
      headless: true
      parallel: true
  - name: transform_data
    config:
      post_process: true
      upload: true
  - name: generate_report
    config:
      report_type: daily
      send_email: true
```

### Environment Variables (.env)
Sensitive configuration:
```bash
LEXGENIUS_AWS_REGION=us-west-2
LEXGENIUS_DYNAMODB_ENDPOINT=http://localhost:8000
LEXGENIUS_OPENAI_API_KEY=sk-...
LEXGENIUS_DEEPSEEK_API_KEY=...
```

## Priority Refactoring Order

### High Priority (Business Critical)
1. **FB Ads Processing** - High volume, performance sensitive
   - Start with `src/lib/fb_ads/image_utils.py` (pure functions)
   - Then `src/lib/fb_ads/classifier.py` (testable business logic)
   
2. **Report Generation** - User-facing, needs reliability
   - Begin with `src/lib/reports/processor.py`
   - Add tests for HTML generation

3. **PACER Scraping** - Core functionality
   - Start with `src/lib/pacer/case_relevance_engine.py`
   - Mock browser interactions for testing

### Medium Priority (Supporting Systems)
1. Storage managers (after async migration)
2. Data transformers
3. Utility functions

### Low Priority (Stable/Rarely Changed)
1. MDL scrapers
2. Court-specific scrapers
3. One-off scripts

## Appendix: Key Design Decisions

### Why Async?
- **I/O Bound Operations**: Most operations wait on network/database
- **Scalability**: Handle more concurrent requests with same resources
- **Modern Python**: Leverage Python 3.7+ async/await features

### Why Protocol-Based Design?
- **Testability**: Easy to mock and test in isolation
- **Flexibility**: Swap implementations without changing client code
- **Type Safety**: Better IDE support and catch errors early

### Why Repository Pattern?
- **Separation of Concerns**: Business logic separate from data access
- **Testability**: Mock data layer for unit tests
- **Migration Path**: Easier to change storage backends

### Why Progressive Migration?
- **Risk Management**: Minimize disruption to production
- **Learning Opportunity**: Adjust approach based on early results
- **Team Adoption**: Give team time to learn new patterns

### Why No DI Container?
- **Simplicity**: Python doesn't need heavy DI frameworks
- **Explicitness**: Dependencies are visible in constructors
- **Performance**: No runtime overhead
- **Debugging**: Easier to trace dependencies

## Final Directory Structure

**Key Change**: Complete elimination of `src/lib/` directory. After refactoring:

```
lexgenius/
├── .env                                    # Environment variables
├── .env.example                           # Example environment file
├── environment.yml                        # Conda environment
├── pyproject.toml                         # Modern Python project config
├── setup.py                               # Legacy setup (if needed)
├── run_pipeline.sh                        # Main pipeline runner
├── README.md                              # Project documentation
│
├── config/                                # Configuration files
│   ├── workflows/                         # Workflow definitions (YAML)
│   │   ├── daily_pipeline.yml
│   │   ├── weekly_report.yml
│   │   ├── fb_ads_processing.yml
│   │   └── pacer_scraping.yml
│   │
│   ├── environments/                      # Environment-specific configs
│   │   ├── development.yml
│   │   ├── staging.yml
│   │   └── production.yml
│   │
│   └── data/                             # Static data files (JSON)
│       ├── courts/
│       │   ├── district_courts.json
│       │   └── pacer_ecm_cf_courts.json
│       ├── mdl/
│       │   ├── mdl_lookup.json
│       │   ├── mdl_classifier.json
│       │   └── mdl_short_name_lookup.json
│       ├── attorneys/
│       │   └── attorney_lookup.json
│       ├── defendants/
│       │   ├── new_defendants.json
│       │   └── relevant_defendants.json
│       ├── law_firms/
│       │   ├── capitalization_lookup.json
│       │   └── name_normalization.json
│       ├── fb_ads/
│       │   ├── company_name_mapping.json
│       │   ├── campaign_config.json
│       │   ├── campaign_skip_terms.json
│       │   └── ignore_firms.json
│       ├── pacer/
│       │   └── ignore_download.json
│       └── reports/
│           ├── afff_stats.json
│           ├── announcements.json
│           ├── attorney_lookup.json
│           ├── content_filters.json
│           ├── general_sponsorships.json
│           ├── litigation_sponsorships.json
│           ├── news.json
│           └── upcoming_hearings.json
│
├── src/
│   ├── __init__.py
│   ├── main.py                           # Main entry point
│   │
│   ├── config_models/                    # Pydantic configuration models
│   │   ├── __init__.py
│   │   ├── base.py                      # Base configuration classes
│   │   ├── scraper.py                   # PACER scraping config
│   │   ├── fb_ads.py                    # Facebook ads config
│   │   ├── reports.py                   # Report generation config
│   │   ├── storage.py                   # AWS/Database config
│   │   ├── workflows.py                 # Workflow orchestration config
│   │   └── validators.py                # Custom Pydantic validators
│   │
│   ├── domain/                          # Domain models (business entities)
│   │   ├── __init__.py
│   │   ├── litigation.py                # Litigation/case models
│   │   ├── law_firm.py                  # Law firm models
│   │   ├── advertisement.py             # Facebook ad models
│   │   ├── court.py                     # Court/docket models
│   │   └── report.py                    # Report models
│   │
│   ├── protocols/                       # Interface definitions
│   │   ├── __init__.py
│   │   ├── storage.py                   # Storage interfaces
│   │   ├── processing.py                # Data processing interfaces
│   │   ├── external.py                  # External service interfaces
│   │   └── messaging.py                 # Message/event interfaces
│   │
│   ├── services/                        # Business logic services
│   │   ├── __init__.py
│   │   ├── litigation/
│   │   │   ├── __init__.py
│   │   │   ├── classifier.py           # Litigation classification
│   │   │   ├── processor.py            # Case processing logic
│   │   │   └── relevance_engine.py     # Case relevance determination
│   │   │
│   │   ├── fb_ads/
│   │   │   ├── __init__.py
│   │   │   ├── categorizer.py          # Ad categorization
│   │   │   ├── image_processor.py      # Image processing service
│   │   │   ├── ner_extractor.py        # Named entity extraction
│   │   │   └── deduplicator.py         # Ad deduplication service
│   │   │
│   │   ├── pacer/
│   │   │   ├── __init__.py
│   │   │   ├── scraper.py              # PACER scraping service
│   │   │   ├── parser.py               # Document parsing service
│   │   │   └── transfer_handler.py     # Case transfer logic
│   │   │
│   │   └── reports/
│   │       ├── __init__.py
│   │       ├── generator.py            # Report generation service
│   │       ├── renderer.py             # HTML rendering service
│   │       └── publisher.py            # Email/distribution service
│   │
│   ├── repositories/                    # Data access layer
│   │   ├── __init__.py
│   │   ├── base.py                     # Base repository class
│   │   ├── pacer_repository.py         # PACER data repository
│   │   ├── fb_ads_repository.py        # Facebook ads repository
│   │   ├── law_firms_repository.py     # Law firms repository
│   │   └── reports_repository.py       # Reports repository
│   │
│   ├── infrastructure/                  # Infrastructure implementations
│   │   ├── __init__.py
│   │   ├── storage/
│   │   │   ├── __init__.py
│   │   │   ├── dynamodb_async.py      # Async DynamoDB client
│   │   │   ├── s3_async.py            # Async S3 client
│   │   │   ├── cache.py               # Caching implementation
│   │   │   └── queue.py               # Queue implementation
│   │   │
│   │   ├── external/
│   │   │   ├── __init__.py
│   │   │   ├── openai_client.py       # OpenAI API client
│   │   │   ├── deepseek_client.py     # DeepSeek API client
│   │   │   ├── mistral_client.py      # Mistral API client
│   │   │   └── browser_automation.py   # Selenium/browser automation
│   │   │
│   │   └── messaging/
│   │       ├── __init__.py
│   │       ├── email_service.py       # Email sending service
│   │       └── cloudfront.py          # CloudFront invalidation
│   │
│   ├── factories/                      # Factory classes for DI
│   │   ├── __init__.py
│   │   ├── service_factory.py         # Service creation factory
│   │   ├── repository_factory.py      # Repository creation factory
│   │   └── infrastructure_factory.py  # Infrastructure creation factory
│   │
│   ├── utils/                          # Utility functions
│   │   ├── __init__.py
│   │   ├── date.py                    # Date utilities
│   │   ├── text.py                    # Text processing utilities
│   │   ├── law_firm.py                # Law firm utilities
│   │   ├── json_safety.py             # JSON handling utilities
│   │   └── async_helpers.py           # Async utility functions
│   │
│   ├── assets/                         # Static assets (unchanged)
│   │   ├── templates/
│   │   ├── css/
│   │   ├── js/
│   │   └── img/
│   │
│   └── scripts/                        # Standalone scripts
│       ├── __init__.py
│       ├── migrations/                 # Database migration scripts
│       ├── analysis/                   # Data analysis scripts
│       └── maintenance/                # Maintenance scripts
│
├── tests/                              # Test directory
│   ├── __init__.py
│   ├── conftest.py                    # Pytest configuration
│   ├── fixtures/                      # Test data fixtures
│   │   ├── __init__.py
│   │   ├── pacer_data.py
│   │   ├── fb_ads_data.py
│   │   └── reports_data.py
│   │
│   ├── unit/                          # Unit tests
│   │   ├── __init__.py
│   │   ├── domain/
│   │   ├── services/
│   │   ├── repositories/
│   │   └── utils/
│   │
│   ├── integration/                   # Integration tests
│   │   ├── __init__.py
│   │   ├── test_pacer_workflow.py
│   │   ├── test_fb_ads_workflow.py
│   │   └── test_report_generation.py
│   │
│   ├── e2e/                          # End-to-end tests
│   │   ├── __init__.py
│   │   └── test_full_pipeline.py
│   │
│   └── mocks/                        # Mock implementations
│       ├── __init__.py
│       ├── mock_dynamodb.py
│       ├── mock_s3.py
│       └── mock_browser.py
│
├── docs/                             # Documentation
│   ├── architecture/
│   │   ├── overview.md
│   │   ├── data_flow.md
│   │   └── deployment.md
│   ├── api/
│   │   ├── services.md
│   │   └── repositories.md
│   ├── configuration/
│   │   └── README.md
│   ├── development/
│   │   ├── setup.md
│   │   ├── testing.md
│   │   └── contributing.md
│   └── refactoring.md               # This refactoring plan
│
├── data/                            # Local data directory (git-ignored)
│   └── YYYY/MM/DD/                 # Date-based organization
│
└── logs/                           # Application logs (git-ignored)
```

## Key Structure Changes

### 1. **Clear Layer Separation**
- `domain/` - Pure business entities, no dependencies
- `services/` - Business logic, depends only on protocols
- `repositories/` - Data access abstraction
- `infrastructure/` - External service implementations

### 2. **Protocol-Based Architecture**
- All cross-layer communication through protocols
- Services depend on protocol interfaces, not concrete implementations
- Easy to mock for testing

### 3. **Centralized Configuration**
- `config_models/` - All Pydantic models in one place
- `config/workflows/` - Human-editable workflow definitions
- `config/data/` - Static data files (JSON)

### 4. **Modern Python Project Structure**
- `pyproject.toml` for project metadata
- Proper package structure with `__init__.py` files
- Clear separation of concerns

### 5. **Comprehensive Testing**
- Parallel test structure mirroring source code
- Separate directories for unit, integration, and e2e tests
- Centralized fixtures and mocks

### 6. **Factory Pattern for DI**
- Simple factories instead of DI container
- Clear dependency creation
- Easy to understand and debug

## Migration Notes

### Files to Remove
- **Entire `src/lib/` directory** (48 files, 28K+ lines)
- All sync `*_manager.py` files (replaced by async repositories)
- Duplicate async implementations in `src/storage/`
- Legacy configuration files (`config.py`, `config_adapter.py`)
- God objects (`ai_integrator.py`, monster files 1,400+ lines)
- Duplicate functionality and backup files

### Files to Keep (with updates)
- Core business logic (move to services)
- Static assets and templates
- Utility functions (consolidate and test)
- Analysis scripts (organize in scripts/analysis)

### New Additions
- **Clean layered architecture** (domain, services, repositories, infrastructure)
- **Protocol-based interfaces** for all cross-layer communication
- **Domain models** separate from storage schemas
- **Service layer** with single responsibilities
- **Factory pattern** for dependency injection
- **Async-only architecture** with connection pooling
- **Comprehensive test coverage** for all components
- **Utility consolidation** with proper organization

## Handling Large Files (2000+ Lines)

### Current State Analysis
Some components in `fb_ads/`, `pacer/`, `data_transformer/`, and `reports/` are 2000-3000 lines. These MUST be broken down before or during refactoring.

### Recommended File Size Limits
- **Max 300-400 lines per file** for complex business logic
- **Max 500 lines** for simple CRUD operations
- **Max 200 lines** for utility functions

### How to Break Down Large Files

#### 1. Identify Cohesive Groups
Look for:
- Methods that work on the same data
- Methods with similar names (e.g., all validation methods)
- Methods that call each other frequently
- Clear stages in a pipeline

#### 2. Common Patterns to Extract

```python
# Pattern 1: Validation
# Extract all validate_* methods to validators.py

# Pattern 2: Data Transformation  
# Extract all transform_*, parse_*, format_* to transformers.py

# Pattern 3: External API Calls
# Extract all fetch_*, post_*, api_* to api_client.py

# Pattern 4: Business Rules
# Extract all check_*, calculate_*, determine_* to rules_engine.py

# Pattern 5: Error Handling
# Extract all handle_*, retry_*, recover_* to error_handler.py
```

#### 3. Example: Breaking Down a 2000+ Line File

```python
# BEFORE: fb_ads/orchestrator.py (2000+ lines)
class Orchestrator:
    def __init__(self):
        # 50+ lines of initialization
    
    def process_ads(self):
        # 300 lines
    
    def validate_ads(self):
        # 200 lines
    
    def handle_errors(self):
        # 150 lines
    
    def generate_reports(self):
        # 250 lines
    
    # ... 20 more methods
```

```python
# AFTER: Break into focused components

# fb_ads/orchestrator.py (200 lines)
from .validators import AdValidator
from .processors import AdProcessor
from .error_handler import ErrorHandler

class Orchestrator:
    def __init__(self, storage: StorageProtocol):
        self.validator = AdValidator()
        self.processor = AdProcessor(storage)
        self.error_handler = ErrorHandler()
    
    async def run(self, ads: List[Dict]):
        validated = await self.validator.validate_batch(ads)
        results = await self.processor.process_batch(validated)
        return results

# fb_ads/validators.py (300 lines)
class AdValidator:
    def validate_ad(self, ad: Dict) -> bool:
        # Focused validation logic
        
# fb_ads/processors.py (400 lines)
class AdProcessor:
    def __init__(self, storage: StorageProtocol):
        self.storage = storage
        
    async def process_ad(self, ad: Dict):
        # Focused processing logic

# fb_ads/error_handler.py (200 lines)
class ErrorHandler:
    def handle_validation_error(self, error):
        # Focused error handling
```

### Refactoring Strategy: Break Down While Testing

#### Week 1 Approach:
```python
# Day 1-2: Pick the largest file (e.g., orchestrator.py)
# 1. Identify logical groups
# 2. Extract to separate files
# 3. Add tests for each new file

# Example: Breaking down fb_ads/processor.py
# Morning: Extract validation logic → validators.py + tests
# Afternoon: Extract transformation logic → transformers.py + tests
```

### Benefits of Breaking Down Large Files:
1. **Easier to Test**: 300-line files vs 2000-line files
2. **Easier to Convert to Async**: Smaller, focused components
3. **Easier to Understand**: Single responsibility per file
4. **Easier to Maintain**: Find bugs faster

### Module-Specific Breakdowns

#### FB Ads Module Structure (if files are 2000+ lines):
```
fb_ads/
├── orchestrator.py         # Coordination only
├── validation/
│   ├── ad_validator.py
│   └── image_validator.py
├── processing/
│   ├── text_processor.py
│   ├── image_processor.py
│   └── categorizer.py
├── classification/
│   ├── classifier.py
│   └── rules_engine.py
└── storage/
    ├── queue_manager.py
    └── cache_manager.py
```

#### Data Transformer Structure (if transformer.py is huge):
```
data_transformer/
├── orchestrator.py         # Main coordination
├── processors/
│   ├── docket_processor.py
│   ├── pdf_processor.py
│   └── mdl_processor.py
├── validators/
│   └── docket_validator.py
└── handlers/
    ├── file_handler.py
    └── transfer_handler.py
```

### Implementation Priority:
1. **Break down files DURING refactoring, not after**
2. **Each extracted component gets tests immediately**
3. **Use the extraction process to identify patterns**
4. **Keep interfaces simple between components**

## Current src/config/ File Migration Map

Here's where each file from the current `src/config/` directory moves to:

### Static Data Files → `/config/data/`
These are reference data that rarely change:
- `attorneys/attorney_lookup.json` → `/config/data/attorneys/`
- `courts/*.json` → `/config/data/courts/`
- `defendants/*.json` → `/config/data/defendants/`
- `law_firms/*.json` → `/config/data/law_firms/`
- `mdl/*.json` → `/config/data/mdl/`
- `fb_ad_categorizer/company_name_mapping.json` → `/config/data/fb_ads/`
- `fb_ad_categorizer/campaign_skip_terms.json` → `/config/data/fb_ads/`
- `fb_ads/ignore_firms.json` → `/config/data/fb_ads/`
- `pacer/ignore_download/ignore_download.json` → `/config/data/pacer/`
- `reports/*.json` → `/config/data/reports/`

### Configuration Files → Pydantic Models
These become type-safe Pydantic configurations:
- `fb_ad_categorizer/*.yml` → Merged into `src/config_models/fb_ads.py`
- `fb_ads/processing_config.yaml` → `src/config_models/fb_ads.py`
- `pacer/paths_config.json` → `src/config_models/storage.py`
- `pacer/relevance_config.json` → `src/config_models/scraper.py`
- `pacer/stability_config.json` → `src/config_models/scraper.py`
- `ollama/config.json` → `src/config_models/external_services.py`
- `feature_flags.json` → `src/config_models/features.py`
- `reports/core_config.json` → `src/config_models/reports.py`

### Workflow Definitions → `/config/workflows/`
- `workflows/*.json` → Convert to YAML in `/config/workflows/`

### Prompts → Embedded in Services
Prompts move closer to where they're used:
- `prompts/fb_ads/*` → `src/services/fb_ads/prompts/`
- `prompts/post_processor/*` → `src/services/pacer/prompts/`

### Files to Eliminate
These are duplicates or temporary:
- `fb_ad_categorizer/campaign_config_bak.json` (backup file)
- `fb_ads/login_required.json` (merge into main config)
- `fb_ads/deferred_processing.json` (runtime state, use database)
- `reports/announcements2.json`, `announcements3.json` (consolidate)
- `pacer/defendants/relevant_defendants.json` (duplicate of defendants/)

### Environment-Specific Settings
Move to environment configs or .env:
- Database endpoints
- API keys
- Feature toggles per environment
- Timeouts and retry settings

This migration ensures:
1. **Type Safety**: Runtime configs use Pydantic validation
2. **Clear Separation**: Static data vs runtime configuration
3. **Locality**: Prompts and configs near their usage
4. **No Duplication**: Single source of truth for each setting