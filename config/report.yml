# YAML configuration for generate_report step
step_name: "generate_report"
input_source: "data/" # Example, adjust if needed
output_location: "data/" # Example, adjust if needed

# Date Configuration
date: '06/12/25' # MM/DD/YY format, matches main.py params
start_date: null # MM/DD/YY format or null
end_date: null   # MM/DD/YY format or null

# LLM Configuration
llm_provider: 'deepseek'

# Upload Configuration - Granular control
upload_json_to_dynamodb: false
upload_pdfs_to_s3: false
upload_reports_to_s3: true

# Number of workers
num_workers: 16

# Report Generator Configuration (from main.py params)
report_generator: True
skip_ads: True
skip_invalidate: False
weekly: False
