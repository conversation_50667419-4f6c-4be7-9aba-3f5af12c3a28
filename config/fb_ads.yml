# YAML configuration for Facebook Ads processing
step_name: "facebook_ads"
input_source: "data/law_firms" # Directory for law firm data
output_location: "data/fb_ads" # Directory where FB ads outputs raw data

# Date Configuration
date: '06/12/25' # MM/DD/YY format, set as per main params
start_date: null # MM/DD/YY format or null (will calculate based on date_range_days)
end_date: null   # MM/DD/YY format or null (will use main date)

# LLM Configuration
llm_provider: 'deepseek'

# Facebook Ads Processing Configuration
fb_ads: True
headless: True
testing: False

# Facebook Web Scraping Configuration (NOT API credentials)
# This system scrapes the public Facebook Ad Library website, not the API
facebook_ads:
  enabled: True
  app_id: "dummy"           # Required by main.py but not used by orchestrator
  app_secret: "dummy"       # Required by main.py but not used by orchestrator
  access_token: "dummy"     # Required by main.py but not used by orchestrator
  ad_account_id: "dummy"    # Required by main.py but not used by orchestrator

# Processing Modes
defer_image_processing: True  # Enable deferred image processing for better performance
use_local: False              # Use AWS DynamoDB, not local
use_proxy: True              # Enable proxy rotation
mobile_proxy: False          # Use mobile vs residential proxy
render_html: False           # Use Oxylabs HTML rendering
airplane_mode: False         # Enable SSLAdapter/TLS rotation

# Date Range Configuration
default_date_range_days: 14     # Default scraping window
single_firm_date_range_days: 30 # Date range for single firm processing
ignore_list_date_range_days: 30 # Date range for ignore list processing

# Session Management
session_refresh_interval: 10    # Proactive session refresh after N firms
skip_firms_updated_today: True # Skip firms already processed today
shuffle_firm_order: True        # Randomize firm processing order

# API Configuration
max_ad_pages: 50              # Maximum pages to fetch per firm
api_retries: 5                # HTTP request retry attempts
api_backoff_base: 1.7         # Exponential backoff multiplier
payload_retries: 3            # Payload-level retry attempts

# Proxy Configuration
oxylabs_num_proxies: 2000      # Number of proxies to use
proxy_ban_duration: 600       # Proxy ban duration in seconds
max_proxy_failures: 3         # Max failures before proxy ban
rotate_proxy_between_firms: True    # Rotate proxy between firms
rotate_proxy_per_page: False         # Rotate proxy per page request

# Image Processing Configuration
image_download_timeout: 30           # Image download timeout
temp_image_dir: "./temp_images"      # Temporary image directory
s3_ad_archive_prefix: "adarchive/fb" # S3 prefix for ad images
s3_cdn_base_url: ""                  # CDN base URL (auto-generated if empty)

# Image Queue Configuration (for deferred processing)
image_queue_dir: "./data/image_queue"  # Directory for SQLite queue
image_queue:
  directory: "./data/image_queue"      # Alternative config path
  batch_size: 100                      # Processing batch size
  cleanup_days: 30                      # Days to keep processed items

# AI Service Configuration
disable_llava: False          # Disable LLaVA image text extraction
disable_gpt: False            # Disable GPT-4 processing
disable_deepseek: False       # Disable DeepSeek processing

# LLaVA Configuration
llava_model_name: "llama3.2-vision:11b-instruct-q4_K_M"   # LLaVA model to use
llava_timeout: 1200                                       # Request timeout for LLaVA
llava_temperature: 0.3                                    # LLaVA generation temperature
llava_num_gpu_layers: -1                                  # GPU layers (-1 for auto)
llava_use_semaphore: True                                 # Use semaphore for concurrency
llava_semaphore_count: 3                                  # Max concurrent LLaVA requests

# Processing Configuration
use_tqdm: True                # Show progress bars
run_parallel: True            # Enable parallel processing
num_workers: 16               # Number of parallel workers

# Database Configuration
bucket_name: "lexgenius-dockets"     # S3 bucket name
processing_results_dir: "./processing_results"  # Processing results directory
law_firm_data_dir: "./data/law_firms"           # Law firm data directory

# DynamoDB Table Names
dynamodb:
  fb_ad_archive_table_name: "FBAdArchive"     # Main ads table
  fb_image_hash_table_name: "FBImageHash"     # Image hash table
  law_firms_table_name: "LawFirms"            # Law firms table

# Add these at top level for the services
fb_ad_archive_table_name: "FBAdArchive"
fb_image_hash_table_name: "FBImageHash"
law_firms_table_name: "LawFirms"
use_local_dynamodb: False                  # Use AWS DynamoDB
local_dynamodb_port: 8000                   # Local DynamoDB port (unused)
dynamodb_endpoint: null                     # Use AWS DynamoDB (null = AWS)

# Upload Configuration - Disabled for FB ads (uses direct DynamoDB)
upload: False
upload_types: []
force_upload: False

# Bandwidth Logging
disable_bandwidth_periodic_logging: False    # Enable bandwidth logs to show totals

# Error Handling
verbose: False                # Enable verbose error logging
max_retries: 3               # Max retries for failed operations

# Categorization Configuration
fb_ad_categorizer:
  batch_size: 50                             # Categorization batch size
  scan_workers: 8                            # Parallel scan workers (local mode)
  update_workers: 8                          # Parallel update workers (local mode)
  prompts_dir: "src/config/fb_ad_categorizer_prompts"  # Prompts directory
  campaign_config_file: "src/config/fb_ad_categorizer/campaign_config.json"
  campaign_skip_terms_file: "src/config/fb_ad_categorizer/campaign_skip_terms.json"
  embedding_stop_terms_file: "src/config/fb_ad_categorizer/embedding_stop_terms.json"
  company_name_mapping_file: "src/config/fb_ad_categorizer/company_name_mapping.json"

# Vector Clustering Configuration
vector_clusterer:
  rules_only: True                           # Use only rules (no ML embeddings)
  embed_fields: ['Title', 'Body']            # Fields to embed for similarity
  embedding_model: "text-embedding-ada-002"  # Embedding model to use

# Processing Filters
ignore_firms_file: "src/config/fb_ads/ignore_firms.json"       # Firms to ignore
login_required_file: "src/config/fb_ads/login_required.json"   # Login required pages
deferred_processing_file: "src/config/fb_ads/deferred_processing.json"  # Deferred items

# Feature Flags
feature_flags:
  enable_phash_deduplication: True           # Enable PHash-based deduplication
  enable_rule_based_classification: True     # Enable rule-based classification
  enable_ai_enhancement: False               # Enable AI-powered enhancement
  enable_bandwidth_logging: True             # Enable bandwidth usage logging
  enable_proxy_health_monitoring: True       # Enable proxy health monitoring
  enable_session_persistence: True           # Enable session cookie persistence

# Performance Tuning
performance:
  max_concurrent_firms: 1                    # Max firms processed concurrently
  max_concurrent_images: 5                   # Max images processed concurrently
  request_delay_min: 0.5                     # Min delay between requests (seconds)
  request_delay_max: 2.0                     # Max delay between requests (seconds)
  memory_limit_mb: 2048                      # Memory limit for processing
  
# Monitoring and Alerting
monitoring:
  log_level: "INFO"                          # Logging level
  enable_metrics: True                       # Enable metrics collection
  metrics_interval: 300                      # Metrics collection interval (seconds)
  enable_health_checks: True                 # Enable health monitoring
  
# Development and Testing
development:
  dry_run: False                             # Enable dry run mode (no actual processing)
  sample_firms: ["301100319750499"]          # List of firm IDs for testing (Levy Konigsberg LLP)
  max_test_firms: 1                          # Max firms for testing
  enable_debug_output: True                  # Enable debug file output
  
# Cleanup and Maintenance
cleanup:
  auto_cleanup_temp_files: True              # Auto cleanup temporary files
  cleanup_interval_hours: 24                 # Cleanup interval
  max_log_file_size_mb: 100                  # Max log file size before rotation
  log_retention_days: 30                     # Days to keep log files