#!/usr/bin/env python3
"""
Working FB Ads implementation - adapted from the original deprecated/lib/fb_ads.py
This actually processes Facebook ads using the proven working code.
"""

import sys
import os

# Add the deprecated lib path so we can import the working implementation
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'deprecated', 'lib'))

def main():
    """Run the working FB Ads implementation."""
    try:
        # Import the working implementation
        import fb_ads
        
        print("🚀 Running the WORKING Facebook Ads implementation...")
        print("📁 Location: deprecated/lib/fb_ads.py")
        print()
        
        # Run the original working implementation
        # The original has its own main() function and argument parsing
        sys.argv = ['fb_ads.py'] + sys.argv[1:]  # Pass through any command line args
        
        # Import and run the original main
        if hasattr(fb_ads, 'main'):
            fb_ads.main()
        else:
            # If no main function, run the module directly
            exec(open('deprecated/lib/fb_ads.py').read())
            
    except ImportError as e:
        print(f"❌ Could not import working FB ads implementation: {e}")
        print()
        print("📋 To run the working version directly:")
        print("   cd deprecated/lib")
        print("   python fb_ads.py")
        print()
        print("📋 Available options:")
        print("   python fb_ads.py                    # Full scrape")
        print("   python fb_ads.py --single FIRM_ID   # Single firm")
        print("   python fb_ads.py --add              # Add firm interactively")
        print("   python fb_ads.py --process-ignore   # Process ignore list")
        return 1
        
    except Exception as e:
        print(f"💥 Error running FB ads: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == '__main__':
    sys.exit(main())