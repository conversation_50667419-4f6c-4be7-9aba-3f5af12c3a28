# Environment and Virtual Environment
.env
.venv
.run
.claude
.claude/settings.local.json
.opencode
.opencode/*
.claude/*
.opencode.json
my_env.yml
environment.yml.bak
*.egg-info/
build/
dist/
*.egg
*.pyc
*.pyo
*.py.class
*~
.pyc
.pyo
.DS_Store

# DynamoDB local data
.dynamodb*
src/.dynamodb*
*.db
*.db-journal

# Data directories (project specific)
data/
Empty Dockets/
qdrant_data/
src/data/law_firms/  # Assuming you still want to ignore this
src/lib/court_mdl_data/pacer_orders/
assets/research/
archived/
archive/*
deprecated/

# Image assets and flowcharts
src/assets/img/
src/flowcharts/

# Python compiled files and cache
**/__pycache__/
*.pyc

# IDE and tools
.idea/
.vscode*
.aider*

# Certificates and other specific files
DigiCertGlobalG2TLSRSASHA2562020CA1-1.crt.pem
reporting.md

# Log files (general project logs in root)
*.log

# Cache files
cache/
**/*cache*/
*.pkl
*.cache

# Processing results
processed_batch_results/
**/results/
*.jsonl

# Files that no longer exist but are causing repo-map errors
src/config/prompts/extract_case_data_system.md
src/config/prompts/extract_case_data_user.md
src/config/prompts/extract_mdl_attorneys_system.md
src/lib/bubble_api.py
src/lib/bubble_data_processor.py
src/lib/color_logging.py
src/lib/deepseek_client.py
src/lib/facebook_account_manager.py
src/lib/fb_ads_enhanced.py
src/lib/filings_by_jurisdiction.png
src/lib/flare_pos.py
src/lib/gpt_client.py
src/lib/graphql_parser2.py
src/lib/lawfirm_data_loader.py
src/lib/litigation_data_loader.py
src/lib/litigation_map_manager.py
src/lib/local_dynamo_mixin.py
src/lib/mailgun.py
src/lib/mailgun_manager.py
src/lib/mdl_parser.py
src/lib/mdl_scraper.py
src/lib/pacer_data_loader.py
src/lib/process_mdl.py
src/lib/unicourt_api.py
src/lib/updated_dynamodb_cli.py
src/lib/updated_report_generator3.py
src/pacer_v2/data_processor.py
src/pacer_v2/download_manager.py
src/pacer_v2/error_handling.py
src/pacer_v2/interaction_manager.py
src/pacer_v2/logger.py
src/pacer_v2/main_control.py
src/pacer_v2/navigation_manager.py
src/pacer_v2/session_manager.py
src/pacer_v2/utility_manager.py
src/pacer_v2/web_driver_setup.py

# Nuclear exclusions
*.pdf
*.csv
