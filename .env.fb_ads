# FB Ads Async Migration Configuration
# Copy these to your main .env file

# Enable async migration for FB Ads
LEXGENIUS_ENABLE_FB_ADS_ASYNC=true
LEXGENIUS_USE_DIRECT_ASYNC_REPOS=true

# Safety nets (set to true for gradual rollout)
LEXGENIUS_FALLBACK_TO_COMPATIBILITY=false
LEXGENIUS_AUTO_FALLBACK_ON_ERROR=true

# Performance monitoring
LEXGENIUS_LOG_PERFORMANCE_METRICS=true
LEXGENIUS_PERFORMANCE_THRESHOLD_MS=5000

# Concurrent processing (Phase 5 features)
ENABLE_CONCURRENT_PROCESSING=true
MAX_CONCURRENT_FIRMS=3
CONCURRENT_BATCH_SIZE=5
SESSION_POOL_SIZE=2

# Debug settings (optional)
LEXGENIUS_DEBUG_ASYNC_CONVERSION=false
LEXGENIUS_DRY_RUN_ASYNC_MIGRATION=false