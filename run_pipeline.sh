#!/usr/bin/env bash

# Default to an empty string if no --config is provided or if it's empty
CONFIG_NAMES=""

# Parse arguments
while [[ "$#" -gt 0 ]]; do
    case $1 in
        --config) CONFIG_NAMES="$2"; shift ;;
        *) echo "Unknown parameter passed: $1"; exit 1 ;;
    esac
    shift
done

if [ -z "$CONFIG_NAMES" ]; then
    echo "Usage: $0 --config <config_name1>[,<config_name2>,...]"
    echo "Example: $0 --config scrape_pacer,transform_data"
    exit 1
fi

# Get the directory of the script
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
PROJECT_ROOT="$SCRIPT_DIR" # Assuming run_pipeline.sh is in the project root

# Split the comma-separated config names
IFS=',' read -ra CONFIG_ARRAY <<< "$CONFIG_NAMES"

# Loop through each config name and run main.py
for config_name in "${CONFIG_ARRAY[@]}"; do
    if [[ "$config_name" == *.yml ]]; then
        config_file_path="$PROJECT_ROOT/config/${config_name}"
    else
        config_file_path="$PROJECT_ROOT/config/${config_name}.yml"
    fi
    main_script_path="$PROJECT_ROOT/src/main.py"

    if [ ! -f "$config_file_path" ]; then
        echo "Error: Config file not found at $config_file_path"
        continue # Skip to the next config if this one is not found
    fi

    if [ ! -f "$main_script_path" ]; then
        echo "Error: main2.py not found at $main_script_path"
        exit 1 # Exit if main.py is missing, as it's crucial
    fi

    echo "Running pipeline step: $config_name with config $config_file_path"
    # Assuming main.py accepts the config file path with a --params argument
    # If main.py expects the config file path directly without --params, change the line below to:
    # python3 "$main_script_path" "$config_file_path"
    python3 "$main_script_path" --params "$config_file_path"
    
    # Check the exit code of the last command
    if [ $? -ne 0 ]; then
        echo "Error running pipeline step: $config_name"
        # To stop on the first error, uncomment the next line
        # exit 1
    fi
done

echo "All pipeline steps completed."